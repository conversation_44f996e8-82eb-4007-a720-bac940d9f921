const express = require('express');

const router = express.Router();
const mongoose = require('mongoose');
const moment = require('moment');
const { DateTime } = require('luxon');
const cmp = require('semver-compare');
const asyncHandler = require('express-async-handler');
const s3 = require('../lib/s3');
const { containsFace } = require('../lib/rekognition');
const { isValidGif } = require('../lib/gif');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const admin = require('../config/firebase-admin');
const User = require('../models/user');
const Chat = require('../models/chat');
const { findUser, findUserMetadata } = require('../middleware/user');
const { formatProfile } = require('../lib/chat');
const { pageSize } = require('../lib/constants');
const constants = require('../lib/constants');
const coinsLib = require('../lib/coins');
const { containsBannedPostKeywords } = require('../lib/report-constants');
const interestLib = require('../lib/interest');
const {
  getQuestions, createQuestion, updateNotification, getLikes, updateQuestionScore, likePost, unlikePost, reportPost, getQuestion,
  getQuestionFeedRouteHandler, getQuestionAllQuestions, formatQuestion, awardPost, addHiveFlag, getUserAttributes,
} = require('../lib/social');
const socialLib = require('../lib/social');
const {
  findDuplicateQuestion, verifyQuestionId, verifyOwnQuestion, verifyInterestAllowsImage, verifyNotQod,
} = require('../middleware/comment');
const Question = require('../models/question');
const { translate } = require('../lib/translate');
const SavedQuestion = require('../models/saved-question');
const UserMetadata = require('../models/user-metadata');
const LanguageMismatch = require('../models/language-mismatch');
const { languageCodes } = require('../lib/languages');
const coinsConstants = require('../lib/coins-constants');
const socketLib = require('../lib/socket');
const { postCandidate } = require('../lib/question-candidate');
const { onPostQuestions } = require('../lib/coins');
const { isLanguageMismatch } = require('../lib/language-moderation');
const { isVideoInappropriate } = require('../lib/video-moderation');
const { convertHls } = require('../lib/mediaconvert');
const { getFriendFcmTokens } = require('../lib/friends');
const { getRegion } = require('../lib/country-regions');
const openai = require('../lib/openai');
const premiumLib = require('../lib/premium');
const PostReport = require('../models/post-report');
const { shadowBan, unban, hasUserReportedFieldValue } = require('../lib/report');
const userMiddleware = require('../middleware/user');
const { getCurrentDayResetTime, getPreviousRewardResetTime, isAppUser, shouldRunForPercentageOfUser } = require('../lib/basic');
const { ImageModerationService } = require('../lib/image-moderation');
const { transcribeAudio } = require('../lib/deepgram');
const multer = require('multer');
const { isMatched } = require('../lib/common')
const socialLibV2 = require('../lib/social-v2');

async function deleteImages(imagesToDelete){
  for (const imageId of imagesToDelete) {
    try {
      await s3.deletePicture(imageId);
      console.log(`Image ${imageId} deleted from S3.`);
    } catch (err) {
      console.error(`Failed to delete image ${imageId}:`, err);
      return next(new Error(`Failed to delete image ${imageId}.`));
    }
  }
}

const sendPostNotification = async (req, question) => {
  // Check if notification should be sent
  if (req.user.shadowBanned || question.mediaUploadPending || question.banned) {
    return;
  }

  const notifyUsers = async (mentionedUsers, questionPart) => {
    const questionData = {
      _id: question._id,
      interestName: question.interestName,
    };
    const data = { question: JSON.stringify(questionData) };

    await socialLib.notifyMentionedUsers(mentionedUsers, question, 'question', data, req.user, questionPart);
  };

  // Notify mentioned users (title and text)
  const mentionedInTitle = question.mentionedUsersTitle && await User.find({ _id: { $in: question.mentionedUsersTitle.map(x => x._id) } }, '_id firstName fcmToken locale appVersion');
  const mentionedInText = question.mentionedUsersText && await User.find({ _id: { $in: question.mentionedUsersText.map(x => x._id) } }, '_id firstName fcmToken locale appVersion');

  if (mentionedInTitle?.length > 0) {
    await notifyUsers(mentionedInTitle, question.title);
  }

  if (mentionedInText?.length > 0) {
    await notifyUsers(mentionedInText, question.text);
  }

  if (!question.isEdited && !question.postedAnonymously) {
  // Notify friends
    const groups = await getFriendFcmTokens(req.uid);
    for (const group of groups) {
      if (group.fcmTokens && group.fcmTokens.length) {
        const { locale, fcmTokens } = group;
        const questionData = {
          _id: question._id,
          interestName: question.interestName,
        };
        const data = { question: JSON.stringify(questionData) };

        if (fcmTokens.length) {
          const notification = {
            title: translate('%s posted', locale, req.user.firstName),
            body: question.title,
            data,
            sound: 'social',
          };

          await admin.sendNotificationToFriends(fcmTokens, notification, 'friend-posted');
        }
      }

      // Update friend post events count
      if (group.userIds && group.userIds.length) {
        await User.updateMany(
          { _id: { $in: group.userIds } },
          { $inc: { 'events.friendPosted': 1 } },
        );
      }
    }
  }
};

function useSocialV2(user){
  if(process.env.NODE_ENV  === 'test-atlas'){
    return true;
  }
  if(process.env.NODE_ENV  === 'test'){
    return false; // return false to prevent other test cases fail
  }

  if(process.env.NODE_ENV === 'beta' && user._id === 'N6Ej8yvHPcQZShYIJVvrN7NWx693'){
    return false // test a user to use old version, for tracing custom feed issue
  }

  // enable for all users
  if (process.env.NODE_ENV === 'beta' || process.env.NODE_ENV === 'prod') {
    return true;
  }

  return false;
}


module.exports = function () {
  router.get('/', asyncHandler(async (req, res, next) => {
    if(useSocialV2(req.user)){
      await socialLibV2.getQuestion(req, res, next);
    }else{
      await getQuestion(req, res, next);
    }
  }));
  router.get('/feed', asyncHandler(async (req, res, next) => {
    if(useSocialV2(req.user)){
      await socialLibV2.getQuestionFeedRouteHandler(req, res, next);
    }else{
      await getQuestionFeedRouteHandler(req, res, next);
    }
  }));
  router.get('/allQuestions', asyncHandler(async (req, res, next) => {
    if(useSocialV2(req.user)){
      await socialLibV2.getQuestionAllQuestions(req, res, next);
    }else{
      await getQuestionAllQuestions(req, res, next);
    }
  }));

  router.post('/', userMiddleware.checkVerified, asyncHandler(findUserMetadata), asyncHandler(findDuplicateQuestion), asyncHandler(async (req, res, next) => {
    const { user } = req;

    // validate input
    if (!req.body.title || req.body.title.length > 300) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.text && req.body.text.length > 40000) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.gif && !isValidGif(req.body.gif)) {
      return next(invalidInputError());
    }
    if (req.body.language && !languageCodes.includes(req.body.language)) {
      return next(invalidInputError('invalid language'));
    }
    if (req.body.poll) {
      if (!Array.isArray(req.body.poll)
        || req.body.poll.length > 20
        || req.body.poll.some((x) => typeof x !== 'string' || x.length > 100)
      ) {
        return next(invalidInputError());
      }
    }

    /*
    if (req.body.postedAnonymously) {
      // if(!req.user.versionAtLeast('1.13.74')) return next(invalidInputError('Posted anonymously not allowed'));
      // if(!isAppUser(req.user)) return next(invalidInputError('Posted anonymously not allowed from web'));
      if(!req.user.anonymousProfileNickname) return next(invalidInputError('Anonymous profile not found'));
    }
    */

    // check interest tag
    let interestId;
    let interestName;
    if (req.body.interestName) {
      const interestNames = interestLib.cleanInterestNames([req.body.interestName]);
      const interests = await interestLib.validateInterestNames(interestNames);
      if (!interests) {
        return next(invalidInputError());
      }
      interestId = interests[0]._id;
      interestName = interests[0].name;
    } else if (req.body.interestId) {
      const interestNames = interestLib.getInterestNamesFromIds([req.body.interestId]);
      if (!interestNames) {
        return next(notFoundError());
      }
      interestId = req.body.interestId;
      interestName = interestNames[0];
    } else {
      return next(badRequestError());
    }

    let hashtags = [interestName];
    if (req.body.hashtags) {
      const interestNames = interestLib.cleanInterestNames(req.body.hashtags);
      const interests = await interestLib.validateInterestNames(interestNames);
      if (interests) {
        hashtags = hashtags.concat(interestNames);
      }
    }

    // check daily post limit
    if (constants.getDailyPostLimit() > 0) {
      let dailyPostLimit = constants.getDailyPostLimit();
      /*
      if (premiumLib.isPremium(user) && user.versionAtLeast('1.13.16') && user.isConfigTrue('social_premium')) {
        dailyPostLimit *= 2;
      }
      */
      const recentQuestions = await Question
        .find(
          {
            createdBy: req.uid,
            createdAt: { $gt: getPreviousRewardResetTime(user) },
            interestName: { $ne: 'questions' },
            mediaUploadPending: { $ne: true },
          },
          '-_id createdAt',
        )
        .sort('-createdAt')
        .limit(dailyPostLimit)
        .lean();

      if (recentQuestions.length == dailyPostLimit) {
        const postLimitResetAt = getCurrentDayResetTime(user.timezone);
        const diffMinutes = Math.floor((postLimitResetAt.getTime() - Date.now()) / 60000);
        const diffHours = Math.ceil(diffMinutes / 60);
        const errMsg = req.__n('You may share again in %s hour.', diffHours);
        if (user.versionAtLeast('1.13.16')) {
          return res.status(403).json({message: errMsg, postLimitResetAt});
        } else {
          return next(forbiddenError(errMsg));
        }
      }
    }

    // check for ban reasons
    let banned;
    let bannedReason;

    // check language
    const textSample = ([req.body.title, req.body.text || ''].join(' ')).trim().substring(0, 100);
    const targetLanguage = req.body.language || 'en';
    const { detectedLanguage, detectedLanguageConfidence, languageMismatch, allow } = await isLanguageMismatch(textSample, targetLanguage, user);
    if (!allow) {
      if (req.body.checkLanguage) {
        await LanguageMismatch.create({
          user: req.uid,
          type: 'post',
          title: req.body.title,
          text: req.body.text,
          targetLanguage,
          detectedLanguage,
          confidence: detectedLanguageConfidence,
        });
        return next(conflictError());
      } else {
        banned = true;
        bannedReason = 'language mismatch';
      }
    }

    let poll;
    let allPollOptions = '';
    if (req.body.poll) {
      poll = {};
      poll.options = req.body.poll.map((x) => ({ text: x }));
      allPollOptions = req.body.poll.join(' ');
    }

    if (req.foundDuplicate) {
      banned = true;
      bannedReason = 'duplicate';
    } else {
      const keyword = containsBannedPostKeywords(req.body.title, req.body.language)
                    || containsBannedPostKeywords(req.body.text, req.body.language)
                    || containsBannedPostKeywords(allPollOptions, req.body.language);
      if (keyword) {
        banned = true;
        bannedReason = `keyword: ${keyword}`;
      }
    }

    let mentionedUsersTitle, mentionedUsersText;
    if (req.body.mentionedUsersTitle) {
      mentionedUsersTitle = await socialLib.validateMentionedUsers(req.body.mentionedUsersTitle);
    }
    if (req.body.mentionedUsersText) {
      mentionedUsersText = await socialLib.validateMentionedUsers(req.body.mentionedUsersText);
    }

    let mediaUploadPending;
    if (req.body.mediaUploadPending) {
      mediaUploadPending = true;
    }

    const region = getRegion(req.user.countryCode || req.user.ipData.countryCode);

    let isLightlyBoosted;
    /*
    if (premiumLib.isPremium(user) && user.versionAtLeast('1.13.16') && user.isConfigTrue('social_premium')) {
      isLightlyBoosted = true;
    }
    */

    const newQuestion = await createQuestion({
      createdBy: req.user,
      language: req.body.language ? req.body.language : undefined,
      detectedLanguage,
      detectedLanguageConfidence,
      languageMismatch,
      parent: interestId,
      interestName,
      title: req.body.title,
      text: req.body.text,
      gif: req.body.gif,
      aspectRatio: req.body.aspectRatio || undefined,
      altText: req.body.altText || undefined,
      banned,
      bannedReason,
      poll,
      noobPost: (req.user.isConfigTrue('boost_first_three_posts') && req.user.metrics.numQuestions < 3) ? true : undefined,
      userAttributes: getUserAttributes(req.user),
      mentionedUsersTitle,
      mentionedUsersText,
      hashtags,
      region,
      mediaUploadPending,
      isLightlyBoosted,
      // ...( req.body.postedAnonymously && { postedAnonymously: true }),
    });
    if (!newQuestion) {
      return next(applicationError());
    }
    const question = newQuestion;
    await updateQuestionScore(question._id);

    if(question.postedAnonymously){
      req.user.metrics.numQuestionsPostedAnonymously >= 0 ? req.user.metrics.numQuestionsPostedAnonymously += 1 : req.user.metrics.numQuestionsPostedAnonymously = 1;
    }else{
      req.user.metrics.numQuestions += 1;
    }

    await req.user.save();
    await req.user.updateNumMinutesUntilParams('numMinutesUntilFirstPost');

    if (user.versionAtLeast('1.13.13')) {
      await user.resetCurrentDayMetricsIfNeeded();
      if (user.currentDayMetrics.karmaFromPostingQuestions < coinsConstants.getPostQuestionsDailyRewardLimit()) {
        user.currentDayMetrics.karmaFromPostingQuestions += 1;
        await user.save();
        await socialLib.incrementKarma(user._id, 1);
      }
    } else {
      socketLib.sendCoinRewards(
        req.user._id,
        await onPostQuestions(req.user),
      );
    }

    if (
      req.body.language
      && !req.userMetadata.postInTwoDimensionsRewardReceived
      && req.userMetadata.firstDimensionPosted != req.body.language
    ) {
      if (!req.userMetadata.firstDimensionPosted) {
        req.userMetadata.firstDimensionPosted = req.body.language;
        await req.userMetadata.save();
      } else {
        const coins = await coinsLib.updateCoins(
          {
            user: req.user._id,
            postInTwoDimensionsRewardReceived: { $in: [false, undefined, null] },
          },
          {
            $inc: { coins: coinsConstants.postInTwoDimensionsReward },
            $set: { postInTwoDimensionsRewardReceived: true },
          },
          'Post in 2 Dimensions Reward',
        );
        if (coins) {
          const coinReward = [{
            caption: translate('Post in 2 Dimensions Reward', user.locale),
            rewardAmount: coinsConstants.postInTwoDimensionsReward,
            newTotal: coins,
          }];
          socketLib.sendCoinRewards(req.uid, coinReward);
        }
      }
    }

    if (!req.user.shadowBanned) {
      await addHiveFlag(
        newQuestion,
        [req.body.title, allPollOptions || '', req.body.text || ''].join(' '),
      );
      await newQuestion.save();
    }

    await sendPostNotification(req, question);

    res.json(formatQuestion(newQuestion, req.user));

    const combinedText = ([req.body.title, req.body.text || ''].join(' ')).trim();
    await socialLib.moderatePostUsingOpenai(combinedText, question);
    await Question.updateSearchFields(question._id);
  }));

  router.delete('/', findUser, asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, asyncHandler(async (req, res, next) => {
    await req.question.deleteOne();
    res.json({});
  }));

  router.patch('/boost', asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    if (req.body.price != coinsConstants.boostPostCost) {
      return next(conflictError('The price of this power up has changed.'));
    }

    if (req.userMetadata.coins < coinsConstants.boostPostCost) {
      return next(forbiddenError('Insufficient coins'));
    }

    let { coins } = req.userMetadata;

    if (!req.question.isBoosted) {
      const updateData = await Question.updateOne({
        _id: req.question._id,
        createdBy: req.user._id,
        isBoosted: { $ne: true },
      }, {
        $set: {
          isBoosted: true,
        },
      });

      if (updateData.modifiedCount) {
        await User.incrementMetrics(req.user._id, ['numPostsBoosted']);
        await updateQuestionScore(req.question._id, true);

        // deduct coins
        coins = await coinsLib.updateCoins(
          { user: req.user._id },
          { $inc: { coins: -1 * coinsConstants.boostPostCost } },
          'boost post',
        );
      }
    }

    return res.json({
      coinsRemaining: coins,
    });
  }));

  router.patch('/like', findUser, asyncHandler(async (req, res, next) => {
    const notificationFn = async function (question) {
      const questionData = {
        _id: question._id,
        interest: interestLib.formatInterest(question.parent, question.createdBy.locale),
        interestName: question.interestName,
      };
      const data = { question: JSON.stringify(questionData) };
      await updateNotification(
        question.createdBy,
        question,
        'question',
        'like',
        JSON.stringify(data),
        req.user,
        question.numLikes,
      );

      let category, analyticsLabel;
      if(await isMatched(question.createdBy._id, req.user._id)){
        category = 'commentLikesMatches'
        analyticsLabel = 'new-comment-likes-matches'
      }else{
        category = 'commentLikesOtherSouls'
        analyticsLabel = 'new-comment-likes-other-souls'
      }

      admin.sendNotification(
        question.createdBy,
        category,
        translate(
          '%s loved your post',
          question.createdBy.locale,
          req.user.firstName,
        ),
        translate(
          'You: %s',
          question.createdBy.locale,
          question.title || question.text,
        ),
        data,
        question._id,
        'social',
        analyticsLabel,
      );
    };

    await likePost(
      Question,
      updateQuestionScore,
      req.user,
      req.body.questionId,
      notificationFn,
    );
    res.json({});
  }));

  router.patch('/unlike', findUser, asyncHandler(async (req, res, next) => {
    await unlikePost(
      Question,
      updateQuestionScore,
      req.user._id,
      req.body.questionId,
    );
    res.json({});
  }));

  router.patch('/edit', asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, asyncHandler(async (req, res, next) => {
    if (!req.body.title) {
      return next(badRequestError());
    }
    if (req.body.title.length > 300) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.text && req.body.text.length > 40000) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.gif && !isValidGif(req.body.gif)) {
      return next(invalidInputError());
    }

    req.question.title = req.body.title;
    req.question.text = req.body.text;
    req.question.gif = req.body.gif;

    let hashtags = [req.question.interestName]
    if(req.body.hashtags){
      if (req.body.hashtags.length > 0) {
        const interestNames = interestLib.cleanInterestNames(req.body.hashtags);
        const interests = await interestLib.validateInterestNames(interestNames);
        if (interests) {
          req.question.hashtags = [...new Set([...hashtags, ...interestNames])]
        }
      }else{
        req.question.hashtags = hashtags
      }
    }

    if (req.body.hasOwnProperty('altText')) {
      req.question.altText = req.body.altText;
    }

    if (!req.question.banned) {
      const keyword = containsBannedPostKeywords(req.question.title, req.question.language)
                    || containsBannedPostKeywords(req.question.text, req.question.language);
      if (keyword) {
        req.question.banned = true;
        req.question.bannedReason = `keyword: ${keyword}`;
      }
    }

    // process mentioned users
    const question = req.question;
    let mentionedUsersTitle, mentionedUsersText;
    if (req.body.mentionedUsersTitle) {
      mentionedUsersTitle = await socialLib.validateMentionedUsers(req.body.mentionedUsersTitle);
    }
    if (req.body.mentionedUsersText) {
      mentionedUsersText = await socialLib.validateMentionedUsers(req.body.mentionedUsersText);
    }
    question.mentionedUsersTitle = mentionedUsersTitle;
    question.mentionedUsersText = mentionedUsersText;

    if (req.body.gif && (req.question.image || req.question.images)) {
      if(req.question.image){
        console.log('/edit delete Image from S3 : ',req.question.image)
        await s3.deletePicture(req.question.image);
        req.question.image = undefined;
      }

      if(req.question.images && req.question.images.length > 0){
        const imagesToDelete = req.question.images.map(img => img.image)
        console.log('/edit delete Images from S3 : ',imagesToDelete)
        await deleteImages(imagesToDelete)
        req.question.images = undefined;
      }
    }

    req.question.isEdited = true;
    await req.question.save();

    if (mentionedUsersTitle || mentionedUsersText) {
      await sendPostNotification(req, req.question);
    }

    res.json({});
    const combinedText = ([req.question.title, req.question.text || ''].join(' ')).trim();
    await socialLib.moderatePostUsingOpenai(combinedText, question);
    await Question.updateSearchFields(req.question._id);
  }));

  router.patch('/report', findUser, asyncHandler(async (req, res, next) => {
    if (!mongoose.isObjectIdOrHexString(req.body.questionId)) {
      return next(invalidInputError());
    }
    // If any user from the same device id reported this post before, ignore this report
    const previouslyReported = await hasUserReportedFieldValue(req.user, 'postreports', 'reportedQuestion', req.body.questionId);
    if (previouslyReported) {
      return res.json({});
    }

    // max 10 post reports allowed per 24 hours
    const numPriorReports = await PostReport.countDocuments({
      reportedBy: req.uid,
      createdAt: { $gt: moment().subtract(24, 'hours').toDate() },
    });
    if (numPriorReports >= 10) {
      return res.json({});
    }

    // ignore if report success ratio over past 30 days is less than 10%
    {
      const counts = await PostReport.aggregate([
        {
          $match: {
            reportedBy: req.uid,
            createdAt: { $gt: moment().subtract(30, 'days').toDate() },
            openaiBan: { $ne: null },
          }
        },
        {
          $group: {
            _id: '$openaiBan',
            count: { $sum: 1 },
          }
        },
      ]);
      let numSuccess = 0;
      let numFail = 0;
      for (let count of counts) {
        if (count._id == false) {
          numFail += count.count;
        } else {
          numSuccess += count.count;
        }
      }
      let numTotal = numSuccess + numFail;
      if (numTotal >= 10 && numSuccess/numTotal <= 0.1) {
        return res.json({});
      }
    }

    if (!process.env.TESTING) {
      res.json({});
    }

    const question = await Question.findById(req.body.questionId).populate('createdBy');
    if (question && !question.isVideo && (!question.audio || (question.audio && question.audioTranscription))) {
      const priorReports = await PostReport.find({reportedQuestion: question._id, openaiBan: {$ne:null}});
      if (priorReports.length > 0) {
        if (process.env.TESTING) {
          res.json({});
        }
        return;
      }
      let ban = await openai.handleQuestionReport(question, req.body.reason, req.body.explanation, req.user._id);
      if (ban) {
        await socialLib.banPost(Question, question._id);
        if (question.createdBy) {
          let numBannedPosts = await PostReport.countDocuments({
            reportedUser: question.createdBy._id,
            createdAt: { $gt: DateTime.utc().minus({ days: 30 }).toJSDate() },
            openaiBan: true,
          });
          if (numBannedPosts >= 10) {
            await shadowBan(question.createdBy, null, '10 posts banned within 30 days');
          }
        }
      }
    }
    else {
      await reportPost(Question, req.body.questionId, req.user._id);
    }

    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.patch('/voteForPoll', asyncHandler(verifyQuestionId), asyncHandler(async (req, res, next) => {
    // validate input
    const { question } = req;
    const { option } = req.body;
    if (!question.poll
      || !question.poll.options
      || !question.poll.options.length
    ) {
      return next(invalidInputError());
    }
    if (Number.isInteger(option)) {
      if (option < 0 || option >= question.poll.options.length) {
        return next(invalidInputError());
      }
    }

    // check if user is reverting/changing their vote
    if (question.poll.votes) {
      const oldVote = question.poll.votes.get(req.uid);
      if (oldVote != undefined) {
        if (oldVote == option) {
          // same vote - no need to do anything
          return res.json({});
        }
        await Question.updateOne(
          {
            _id: req.body.questionId,
            [`poll.votes.${req.uid}`]: oldVote,
          },
          {
            $inc: {
              [`poll.options.${oldVote}.numVotes`]: -1,
              'poll.numVotes': -1,
            },
            $unset: {
              [`poll.votes.${req.uid}`]: 1,
            },
          },
        );
      }
    }

    if (Number.isInteger(option)) {
      await Question.updateOne(
        {
          _id: req.body.questionId,
          [`poll.votes.${req.uid}`]: { $exists: false },
        },
        {
          $inc: {
            [`poll.options.${option}.numVotes`]: 1,
            'poll.numVotes': 1,
          },
          $set: {
            [`poll.votes.${req.uid}`]: option,
          },
        },
      );
    }

    res.json({});
  }));

  router.get('/likes', asyncHandler(async (req, res, next) => {
    // API for page is 0-indexed
    let pageNumber = 0;
    if (req.query.page) {
      if (isNaN(req.query.page)) {
        return next(invalidInputError());
      }
      pageNumber = parseInt(req.query.page);
    }

    const rv = await getLikes(req.query.questionId, pageNumber, Question, req.user);
    return res.json(rv);
  }));

  router.post('/image', findUser, asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, verifyInterestAllowsImage, s3.uploadQuestionImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);

    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(req.file.key);
    if (moderationResults.hive_v2) {
      req.question.banned = true;
      req.question.bannedReason = JSON.stringify(moderationResults);
    }
    if (req.question.image) {
      await s3.deletePicture(req.question.image);
      req.question.isEdited = true;
    }
    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      req.question.aspectRatio = req.body.aspectRatio;
    }
    const shouldNotify = req.question.mediaUploadPending;
    req.question.mediaUploadPending = undefined;
    req.question.gif = undefined;
    req.question.image = req.file.key;
    if (await containsFace(req.file.key)) {
      req.question.faceDetected = true;
    }
    await req.question.save();
    if (req.question.faceDetected) {
      updateQuestionScore(req.question._id); // update score asynchronously
    }
    if (shouldNotify) {
      await sendPostNotification(req, req.question);
    }

    return res.json({
      image: req.question.image,
    });
  }));

  router.post(
    '/images',
    findUser,
    asyncHandler(verifyQuestionId),
    verifyOwnQuestion,
    verifyNotQod,
    verifyInterestAllowsImage,
    s3.uploadQuestionImage.array('images', 20),
    asyncHandler(async (req, res, next) => {
      if (req.fileValidationError) return next(req.fileValidationError);
      if (!req.body.imagesArray) {
        return next(invalidInputError('No images provided'));
      }

      let images = [];
      const imageArray = JSON.parse(req.body.imagesArray);
      const files = req.files || [];

      // Get current images from the question stored in the database
      const currentImages = req.question.images || [];
      const currentImageIds = currentImages.map(img => img.image); // Extract image IDs

      // Track which files have been processed
      let processedFileIndices = {};
      let hasOldImage = false

      // Process the new image array (incoming payload)
      for (let i = 0; i < imageArray.length; i++) {
        const imageItem = imageArray[i];

        if(imageItem.id === req.question.image){
          hasOldImage = true
        }

        if (imageItem.type === 'existing') {
          // Handle existing images
          images.push({
            image: imageItem.id,
            altText: imageItem.altText || null,
          });
        } else if (imageItem.type === 'new') {
          // Handle new images
          const fileIndex = imageItem.fileIndex;

          if (fileIndex === undefined || !files[fileIndex]) {
            return next(invalidInputError(`New image at position ${i} is missing or invalid.`));
          }

          // Avoid processing the same file multiple times
          if (processedFileIndices[fileIndex]) {
            return next(invalidInputError(`File at index ${fileIndex} is referenced multiple times.`));
          }

          const file = files[fileIndex];
          processedFileIndices[fileIndex] = true;

          // Image moderation
          const moderationService = new ImageModerationService(['Hive']);
          const moderationResults = await moderationService.moderatePicture(file.key);
          if (moderationResults.hive_v2) {
            req.question.banned = true;
            req.question.bannedReason = JSON.stringify(moderationResults);
          }

          if (i === 0 && await containsFace(file.key)) {
            req.question.faceDetected = true;
          }

          images.push({
            image: file.key,
            altText: imageItem.altText || null,
          });
        } else {
          return next(invalidInputError(`Invalid image type at position ${i}.`));
        }
      }

      if(!hasOldImage && req.question.image){
        //no old images on imagesArray then delete old image and set image to undefined
        try{
          console.log('/images delete Image from S3 : ',req.question.image)
          await s3.deletePicture(req.question.image);
          req.question.image = undefined
        }catch(error){
          console.log('DELETE old Image error', error)
        }
      } else {
        // old image was found on imagesArray then set image to undefined
        req.question.image = undefined
      }

      // Handle image deletions:
      const newImageIds = images.map(img => img.image); // Extract image IDs from the updated list

      if (currentImageIds.length > 0 && JSON.stringify(currentImageIds) !== JSON.stringify(newImageIds)) {
        req.question.isEdited = true; // Mark the post as edited if images have changed
        const imagesToDelete = currentImageIds.filter(imgId => !newImageIds.includes(imgId));

        // Delete the images from S3 and the database
        await deleteImages(imagesToDelete);


        if(images.length === 0){
          req.question.faceDetected = undefined
        }
      }

      // Update the question with the new images array
      req.question.images = images;

      // Optionally update aspect ratio
      if (req.body.aspectRatio) {
        req.question.aspectRatio = req.body.aspectRatio;
      }

      const shouldNotify = req.question.mediaUploadPending;
      // Reset media upload pending
      req.question.mediaUploadPending = undefined;
      req.question.gif = undefined;

      await req.question.save();

      if (shouldNotify) {
        await sendPostNotification(req, req.question);
      }

      return res.json({
        images: req.question.images,
      });
    })
  );

  router.post('/video', asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, verifyInterestAllowsImage, s3.uploadQuestionVideo.single('video'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Video was not provided'));
    }
    console.log(req.file);

    const status = await convertHls(req.file.key);
    if (status == 'COMPLETE') {
      req.question.convertedVideo = req.file.key;
      const result = await isVideoInappropriate(req.file.key, req.user, true);
      if (result.hive_v2) {
        console.log(`Inappropriate video thumbnail detected from user ${req.uid}`);
        req.question.banned = true;
        req.question.bannedReason = JSON.stringify(result);
      }
    }

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      req.question.aspectRatio = req.body.aspectRatio;
    }

    if (req.question.image) {
      console.log('Replacing image: ', req.question.image);
      await s3.deletePicture(req.question.image);
      req.question.isEdited = true;
    }
    const shouldNotify = req.question.mediaUploadPending;
    req.question.mediaUploadPending = undefined;
    req.question.gif = undefined;
    req.question.image = req.file.key;
    req.question.isVideo = true;
    console.log('Saving image: ', req.question.image);
    await req.question.save();
    if (shouldNotify) {
      await sendPostNotification(req, req.question);
    }

    res.json({
      image: req.question.image,
    });
  }));

  router.delete('/image', findUser, asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, asyncHandler(async (req, res, next) => {
    if (!req.question.image) {
      return next(notFoundError());
    }

    await s3.deletePicture(req.question.image);

    req.question.image = undefined;
    req.question.isVideo = undefined;
    req.question.convertedVideo = undefined;
    req.question.faceDetected = undefined;
    req.question.isEdited = true;
    await req.question.save();
    return res.json({});
  }));

  router.post('/audio', findUser, asyncHandler(verifyQuestionId), verifyNotQod, verifyOwnQuestion, s3.uploadQuestionAudio.single('audio'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError());
    }
    console.log(req.file);

    if (req.question.audio) {
      console.log('Replacing audio: ', req.question.audio);
      await s3.deletePicture(req.question.audio);
      req.question.isEdited = true;
      req.question.audioTranscription = undefined;
    }
    const shouldNotify = req.question.mediaUploadPending;
    req.question.mediaUploadPending = undefined;
    req.question.audio = req.file.key;
    const transcription = await transcribeAudio(req.file.key);
    if (transcription) {
      req.question.audioTranscription = transcription;
    }
    if (req.body.waveform && typeof req.body.waveform === 'string') {
      const waveform = JSON.parse(req.body.waveform);
      req.question.audioWaveform = waveform;
      req.question.audioDuration = req.body.duration;
    }
    console.log('Saving audio: ', req.question.audio);
    await req.question.save();
    if (shouldNotify) {
      await sendPostNotification(req, req.question);
    }
    return res.json({
      audio: req.question.audio,
    });
  }));

  router.delete('/audio', findUser, asyncHandler(verifyQuestionId), verifyOwnQuestion, verifyNotQod, asyncHandler(async (req, res, next) => {
    if (!req.question.audio) {
      return next(notFoundError());
    }

    await s3.deletePicture(req.question.audio);

    req.question.audio = undefined;
    req.question.audioTranscription = undefined;
    req.question.isEdited = true;
    await req.question.save();
    return res.json({});
  }));

  router.patch('/save', asyncHandler(verifyQuestionId), asyncHandler(async (req, res, next) => {
    await SavedQuestion.addSaved(req.user._id, req.body.questionId);
    res.json({});
  }));

  router.patch('/unsave', asyncHandler(verifyQuestionId), asyncHandler(async (req, res, next) => {
    await SavedQuestion.removeSaved(req.user._id, req.body.questionId);
    res.json({});
  }));

  router.post('/award', asyncHandler(async (req, res, next) => {
    await awardPost(req, res, next, 'question');
  }));
  router.get('/awardSenders', asyncHandler(socialLib.getAwardSenders));

  router.get('/saved', asyncHandler(async (req, res, next) => {
    const query = { user: req.user._id };
    let beforeDate;
    let questions = [];

    if (req.query.beforeId) {
      if (!mongoose.isValidObjectId(req.query.beforeId)) {
        return next(invalidInputError());
      }
      const doc = await SavedQuestion.findOne({
        user: req.user._id,
        question: req.query.beforeId,
      });
      if (doc) {
        beforeDate = doc.createdAt;
      }
    }

    while (!questions.length) {
      if (beforeDate) {
        query.createdAt = { $lt: beforeDate };
      }

      const savedQuestions = await SavedQuestion
        .find(query)
        .sort('-createdAt')
        .limit(pageSize);

      if (!savedQuestions.length) {
        break;
      }
      beforeDate = savedQuestions[savedQuestions.length - 1].createdAt;

      const ids = savedQuestions.map((x) => x.question);
      const matchBy = { _id: { $in: ids } };
      questions = await getQuestions(
        req.user,
        null,
        null,
        matchBy,
        null,
        null,
      );

      const idstr = savedQuestions.map((x) => x.question.toString());
      questions.sort((e1, e2) => idstr.indexOf(e1._id.toString()) - idstr.indexOf(e2._id.toString()));
    }

    res.json({
      questions,
    });
  }));

  router.post('/submitQod', asyncHandler(async (req, res, next) => {
    const { text } = req.body;
    const { isAnonymous } = req.body;
    const language = req.body.language || 'en';

    if (typeof text !== 'string' || typeof isAnonymous !== 'boolean') { throw invalidInputError(); }

    if (language && !languageCodes.includes(language)) {
      return next(invalidInputError('invalid language'));
    }

    if (text.length > 500) { throw invalidInputError('Character Limit exceeded'); }// because qod can't be too long.
    await postCandidate(req.user, text, isAnonymous, language);
    res.json({});
  }));

  return router;
};
