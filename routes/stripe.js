const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const premiumLib = require('../lib/premium');
const locationLib = require('../lib/location');
const stripe = require('../lib/stripe').stripe;
const { hasCountryPrice, getPricingConfig, getCountryCode, getVariant } = require('../lib/stripe-prices');
const User = require('../models/user');

const WEB_DOMAIN = process.env.WEB_DOMAIN || 'https://boo.world';

async function createStripeCustomerIfNotExists(user) {
  if (!user.stripeCustomerId) {
    const customer = await stripe.customers.create({
      name: user.firstName,
      email: user.email,
      phone: user.phoneNumber,
    });
    const res = await User.updateOne(
      { _id: user._id, stripeCustomerId: null },
      { stripeCustomerId: customer.id },
    );
    if (res.modifiedCount) {
      user.stripeCustomerId = customer.id;
    } else {
      const updatedUser = await User.findOne( { _id: user._id }, 'stripeCustomerId');
      user.stripeCustomerId = updatedUser.stripeCustomerId;
    }
  }
}

async function backfillStripeCurrency(user) {
  if (!user.stripeCustomerId || user.stripeCurrency || user.stripeCurrencyBackfilled) {
    return;
  }
  const response = await stripe.paymentIntents.list({
    customer: user.stripeCustomerId,
    limit: 100,
  });
  const successful = response.data.find(
    (pi) => pi.status === 'succeeded' && pi.currency && pi.amount > 0
  );
  if (successful) {
    user.stripeCurrency = successful.currency;
  }
  user.stripeCurrencyBackfilled = true;
  await user.save();
}

module.exports = function () {

  router.get('/products', asyncHandler(async (req, res, next) => {
    const user = req.user;
    await backfillStripeCurrency(user);
    const config = await getPricingConfig(user, req.ip);
    return res.json(config);
  }));

  router.post('/create-checkout-session', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const redirect = req.body.redirectUrl || WEB_DOMAIN;

    if (user.shadowBanned) {
      return res.json({
        redirect,
      });
    }

    const config = await getPricingConfig(user, req.ip);

    if (req.body.product && config[req.body.product].unavailable) {
      return res.json({
        redirect,
      });
    }

    const variant = getVariant(user);
    const countryCode = getCountryCode(req.ip, user);
    let countryPrefix = '';
    if (hasCountryPrice(countryCode)) {
      countryPrefix = countryCode + '_';
    }

    let lookup_key = req.body.lookup_key
                     ? req.body.lookup_key
                     : `${countryPrefix}infinity_${req.body.product}`
    if (req.body.product) {
      if (req.body.product != 'lifetime') {
        lookup_key = lookup_key + variant;
      }
      if (['coins', 'super_love', 'neurons', 'boost'].some(type => req.body.product.includes(type))) {
        lookup_key = `${countryPrefix}${req.body.product}`;
      }
    }

    let mode = 'subscription';
    if (['lifetime', 'coins', 'super_love', 'neurons', 'boost'].some(type => lookup_key.includes(type))) {
      mode = 'payment';
    }

    await createStripeCustomerIfNotExists(req.user);
    const prices = await stripe.prices.list({
      lookup_keys: [lookup_key],
      expand: ['data.product'],
    });
    const params = {
      currency: config.currency,
      customer: req.user.stripeCustomerId,
      customer_update: {
        address: 'auto',
      },
      billing_address_collection: 'auto',
      automatic_tax: {
        enabled: true,
      },
      line_items: [
        {
          price: prices.data[0].id,
          quantity: 1,
        },
      ],
      mode,
      success_url: redirect,
      cancel_url: redirect,
    };
    if (req.body.product && config[req.body.product].discount) {
      params.discounts = [{
        coupon: `flash_sale_${config[req.body.product].discount}`,
      }];
    }
    if (lookup_key.includes('lifetime') && !params.discounts) {
      params.allow_promotion_codes = true;
    }
    const session = await stripe.checkout.sessions.create(params);

    return res.json({
      redirect: session.url,
    });
  }));

  router.post('/create-portal-session', asyncHandler(async (req, res, next) => {
    await createStripeCustomerIfNotExists(req.user);
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: req.user.stripeCustomerId,
      return_url: `${WEB_DOMAIN}`,
    });

    return res.json({
      redirect: portalSession.url,
    });
  }));

  return router;
};
