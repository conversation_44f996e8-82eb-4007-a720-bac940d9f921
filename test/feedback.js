const { app, initSocket, getSocketPromise, destroySocket, waitMs } = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const User = require('../models/user');
const Feedback = require('../models/feedback');
const Chat = require('../models/chat');
const Report = require('../models/report');
const { BOO_BOT_ID, BOO_SUPPORT_ID } = require('../lib/chat');
const { topics, issues, templates, subIssues, supportOptions } = require('../lib/autoresponse/responses');
const TrackAutoreponseUsage = require('../models/track-autoresponse-usage');
const { translate } = require('../lib/translate');
const { fakeAdminMessaging, fakeCloudwatch } = require('./stub');
const { shadowBan, unban } = require('../lib/report');
const Message = require('../models/message');

async function createSupportUsers() {
  res = await request(app)
    .get('/v1/user')
    .set('authorization', BOO_BOT_ID)
    .send({ appVersion: '1.13.67' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', BOO_BOT_ID)
    .send({ firstName: 'Boo' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', BOO_SUPPORT_ID)
    .send({ appVersion: '1.13.67' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', BOO_SUPPORT_ID)
    .send({ firstName: 'Boo Support' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', BOO_SUPPORT_ID)
    .send({
      fcmToken: 'token0',
    });
  expect(res.status).to.equal(200);
}

function getObjectForExistingVersions(data) {
  return data.map((d) => ({
    title: d.title,
    next_step: d.next_step,
  }));
}

const sendFeedback = async (authorization, payload) => {
  const res = await request(app)
    .post('/v1/feedback/chat')
    .set('authorization', authorization)
    .send(payload);
  expect(res.status).to.equal(200);
  return res;
};

const sendMessage = async (chatId, authorization, text) => {
  const res = await request(app)
    .post(`/v1/message?chatId=${chatId}`)
    .set('authorization', authorization)
    .send({ text });
  expect(res.status).to.equal(200);
  return res;
};

const updateChatLastMessageTime = async (chatId, hoursAgo) => {
  const chat = await Chat.findOne({ _id: chatId });
  chat.lastMessageTime = new Date(new Date() - hoursAgo * 60 * 60 * 1000);
  await chat.save();
  return chat;
};

const validateSocketEvents = async (socketPromises, index, expectedMessage) => {
  const socketEvents = await Promise.allSettled(socketPromises);
  socketEvents.forEach((socketEvent) => { expect(socketEvent.status).to.equal('fulfilled'); });
  expect(socketEvents[index].value.message.text).to.equal(expectedMessage.text);
  expect(socketEvents[index].value.message.automatedChatOptions).to.deep.equal(expectedMessage.automatedChatOptions);
  expect(socketEvents[index].value.canWriteMessage).to.equal(expectedMessage.canWriteMessage);
};

describe('Automated chat V2', () => {
  beforeEach(async () => {
    await createSupportUsers();
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
      .send({ appVersion: '1.13.67' }); // Initiated with previous version
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({
        firstName: `name 0`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: `token`,
      });
    expect(res.status).to.equal(200);
  });
  it('Automation flow after 24h inactivity with support', async () => {
    const topicsSelection = { selectedItem: topics[0] };
    const issuesSelection = { selectedItem: issues[topics[0].next_step][0] };
    const automationStartSelection = { selectedItem: { title: 'Yes', next_step: 'support' } };

    let res = await sendFeedback(0, { message: 'sending feedback from user' });
    expect(res.body.text).to.equal('sending feedback from user');
    await sendFeedback(0, topicsSelection);
    await sendFeedback(0, issuesSelection);
    await waitMs(200);
    let chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    let messages = await Message.find({ chat: res.body.chat }).sort({ createdAt: -1 }).lean();
    expect(messages[0].automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));

    await sendFeedback(0, automationStartSelection);
    await waitMs(200);
    res = await sendFeedback(0, { message: 'message about some issue' });

    await sendMessage(res.body.chat, 0, 'test message from user');
    await sendMessage(res.body.chat, BOO_SUPPORT_ID, 'test message from support user');

    // Simulate 24 hours inactivity and test automation flow
    await updateChatLastMessageTime(res.body.chat, 25);
    // Support can send message after 24 hours and flow will continue as it is
    await sendMessage(res.body.chat, BOO_SUPPORT_ID, 'test another message from support user after 24 hours');

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(true);
    await sendMessage(res.body.chat, 0, 'test more message from user');

    // Simulate 24 hours inactivity and test redirection to automation flow
    await updateChatLastMessageTime(res.body.chat, 25);

    const s0 = await initSocket(0);
    const sp0 = getSocketPromise(s0, 'message');
    const _sp0 = getSocketPromise(s0, 'support left automatedChat');
    const __sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    await sendMessage(chat._id, 0, 'test message from user after 24 hours');
    await waitMs(200);

    await validateSocketEvents(
      [sp0, _sp0, __sp0],
      2,
      {
        text: 'Welcome back to Boo Support. What can we help you with?\nChoose from one option below:',
        automatedChatOptions: getObjectForExistingVersions(topics),
        canWriteMessage: false,
      },
    );

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    expect(chat.automatedChatState.stage).to.equal('topics');
    await destroySocket(s0);

    // Re add support and test inactivity redirection via feedback route
    await sendFeedback(0, { selectedItem: topics[1] });
    await sendFeedback(0, { selectedItem: issues[topics[1].next_step][0] });
    await waitMs(200);
    await sendFeedback(0, automationStartSelection);
    await waitMs(200);
    res = await sendFeedback(0, { message: 'message about some issue' });

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    await sendFeedback(0, { message: 'msg 0' });
    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    // Simulate 24 hours inactivity
    await updateChatLastMessageTime(res.body.chat, 25);
    await sendFeedback(0, { message: 'msg 0' });
    await waitMs(200);

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    expect(chat.automatedChatState.stage).to.equal('topics');

    // update user app version to latest
    res = await request(app).put('/v1/user/initApp').set('authorization', '0').send({ appVersion: '1.13.74' });
    expect(res.status).to.equal(200);

    // Re add support and test inactivity redirection via feedback route
    res = await sendFeedback(0, { selectedItem: topics[1] });
    messages = await Message.find({ chat: res.body.chat }).sort({ createdAt: -1 }).lean();
    expect(messages[0].automatedChatOptions).to.deep.equal(issues[topics[1].next_step]);
    res = await sendFeedback(0, { selectedItem: issues[topics[1].next_step][0] });
    await waitMs(200);
    messages = await Message.find({ chat: res.body.chat }).sort({ createdAt: -1 }).lean();
    expect(messages[0].automatedChatOptions).to.deep.equal(supportOptions);
    await sendFeedback(0, automationStartSelection);
    await waitMs(200);
    res = await sendFeedback(0, { message: 'message about some issue' });

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    // Simulate 24 hours inactivity
    await updateChatLastMessageTime(res.body.chat, 25);
    await sendFeedback(0, { message: 'msg 0' });
    await waitMs(200);

    chat = await Chat.findOne({ _id: res.body.chat });
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    expect(chat.automatedChatState.stage).to.equal('topics');
    await destroySocket(s0);
  });

  it('test markdown template for link on new app versions', async () => {
    const topicsSelection = { selectedItem: topics[0] };
    const issuesSelection = { selectedItem: issues[topics[0].next_step][0] };
    const automationStartSelection = { selectedItem: { title: 'See all topics', next_step: 'topics' } };

    let res = await sendFeedback(0, { message: 'sending feedback from user' });
    expect(res.body.text).to.equal('sending feedback from user');
    await sendFeedback(0, topicsSelection);
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await sendFeedback(0, issuesSelection);
    await waitMs(200);
    res = await sp0;
    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[issues[topics[0].next_step][0].next_step].title);

    // update user app version to latest
    res = await request(app).put('/v1/user/initApp').set('authorization', '0').send({ appVersion: '1.13.74' });
    expect(res.status).to.equal(200);

    await sendFeedback(0, automationStartSelection);
    await sendFeedback(0, topicsSelection);
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await sendFeedback(0, issuesSelection);
    await waitMs(200);
    res = await sp0;
    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[issues[topics[0].next_step][0].next_step].v2_title);
    await destroySocket(s0);
  });

  it('test tracking flows in english for new app versions', async () => {
    const automationStartSelection = { selectedItem: { title: 'See all topics', next_step: 'topics' } };
    const socket = await initSocket(0);
    let socketPromise = getSocketPromise(socket, 'automatedChatAutoResponse');
    let res = await sendFeedback(0, { message: 'sending feedback from user' });
    expect(res.body.text).to.equal('sending feedback from user');
    await waitMs(200);
    res = await socketPromise;

    socketPromise = getSocketPromise(socket, 'automatedChatAutoResponse');
    await sendFeedback(0, { selectedItem: res.message.automatedChatOptions[0] });
    await waitMs(200);
    res = await socketPromise;

    await sendFeedback(0, { selectedItem: res.message.automatedChatOptions[0] });
    await waitMs(200);

    // Template sent, flow will be tracked
    let tracking = await TrackAutoreponseUsage.find({ user: 0 }).sort({ createdAt: -1 });
    expect(tracking.length).to.equal(1);
    expect(tracking[0].topic).to.equal(topics[0].title);
    expect(tracking[0].issue).to.equal(issues[topics[0].next_step][0].title);
    expect(tracking[0].subIssue).to.equal(null);
    expect(tracking[0].outcome).to.equal('template_sent');

    // change user locale to Bengali
    res = await request(app).put('/v1/user/initApp').set('authorization', '0').send({ locale: 'bn' });
    expect(res.status).to.equal(200);

    socketPromise = getSocketPromise(socket, 'automatedChatAutoResponse');
    await sendFeedback(0, automationStartSelection);
    await waitMs(200);
    res = await socketPromise;

    socketPromise = getSocketPromise(socket, 'automatedChatAutoResponse');
    await sendFeedback(0, { selectedItem: res.message.automatedChatOptions[0] });
    await waitMs(200);
    res = await socketPromise;

    await sendFeedback(0, { selectedItem: res.message.automatedChatOptions[0] });
    await waitMs(200);

    // another flow is tracked
    tracking = await TrackAutoreponseUsage.find({ user: 0 }).sort({ createdAt: -1 });
    expect(tracking.length).to.equal(2);
    expect(tracking[0].topic).to.equal(translate(topics[0].title, 'bn'));
    expect(tracking[0].issue).to.equal(translate(issues[topics[0].next_step][0].title, 'bn'));

    // update user app version to latest
    res = await request(app).put('/v1/user/initApp').set('authorization', '0').send({ appVersion: '1.13.74' });
    expect(res.status).to.equal(200);

    await sendFeedback(0, automationStartSelection);
    await sendFeedback(0, { selectedItem: topics[0] });
    await sendFeedback(0, { selectedItem: issues[topics[0].next_step][0] });
    await waitMs(200);

    // flow is tracked in english
    tracking = await TrackAutoreponseUsage.find({ user: 0 }).sort({ createdAt: -1 });
    expect(tracking.length).to.equal(3);
    expect(tracking[0].topic).to.equal(topics[0].title);
    expect(tracking[0].issue).to.equal(issues[topics[0].next_step][0].title);
    await destroySocket(socket);
  });
});

describe('Test rate chat route', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
  });

  it('should return an error if no feedback or rating is provided', async () => {
    const res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({});

    expect(res.status).to.equal(422);
  });

  it('should return an error if rating is out of range', async () => {
    const res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({ rating: 6 });

    expect(res.status).to.equal(422);
  });

  it('should return an error if feedback exceeds character limit', async () => {
    const longFeedback = 'a'.repeat(10001);
    const res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({ feedback: longFeedback });

    expect(res.status).to.equal(422);
  });

  it('should create new feedback if none exists', async () => {
    const res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({ rating: 5, feedback: 'Excellent' });

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({});

    const feedback = await TrackAutoreponseUsage.findOne({ user: '0' });
    expect(feedback.rating).to.equal(5);
    expect(feedback.feedback).to.equal('Excellent');
  });

  it('should update existing feedback if it exists', async () => {
    let res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({ rating: 5, feedback: 'Excellent' });

    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({});

    let feedback = await TrackAutoreponseUsage.findOne({ user: '0' });
    expect(feedback.rating).to.equal(5);
    expect(feedback.feedback).to.equal('Excellent');

    res = await request(app)
      .post('/v1/feedback/rateChat')
      .set('authorization', '0')
      .send({ rating: 4 });
    expect(res.status).to.equal(200);
    expect(res.body).to.deep.equal({});

    feedback = await TrackAutoreponseUsage.findOne({ user: '0' });
    expect(feedback.rating).to.equal(4);
  });
});

describe('Automated Chat', () => {
  beforeEach(async () => {
    await createSupportUsers();
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
      .send({ appVersion: '1.13.67' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({
        firstName: `name 0`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: `token`,
      });
    expect(res.status).to.equal(200);
  });

  it('test verification sub issues flow', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[3],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[3].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[3].next_step][3],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(subIssues[issues[topics[3].next_step][3].next_step]));
    expect(res.canWriteMessage).to.equal(false);
    const templateId = res.message.automatedChatOptions[0].next_step;

    // Chat should be in sub_issues stage
    const chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('sub_issues');

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: res.message.automatedChatOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[templateId].title);
    expect(res.canWriteMessage).to.equal(false);

    // Another message will be send from the bot after 100 milliseconds
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.text).to.equal(`Would you like additional assistance with this?`);
    expect(res.canWriteMessage).to.equal(false);
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));

    await destroySocket(s0);
  });

  it('test ghost meter flow', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[1],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[1].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[1].next_step][7],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(`The Ghost Meter encourages meaningful connections and ensure every match gets the attention it deserves.\n\n\nHow it works:\n\n- You can have up to 10 open matches that haven’t been responded to yet.\n- To receive or accept new matches, simply reply to your open matches or unmatch with users you haven’t responded to.\n\n\nThis helps create a more engaging experience for everyone on the platform. Thank you for understanding!`);
    expect(res.canWriteMessage).to.equal(false);

    await destroySocket(s0);
  });

  it('test flow topics > issues > template', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    let chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('topics');
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[0].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('issues');
    expect(chat.automatedChatState.topic).to.equal(topics[0].title);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);

    let tracking = await TrackAutoreponseUsage.findOne({ user: 0 });
    expect(tracking).to.equal(null);

    // User won't be able to send message via message routes in automated stages
    res = await request(app)
      .post(`/v1/message?chatId=${res.message.chat}`)
      .set('authorization', 0)
      .send({
        text: 'test message',
      });
    expect(res.status).to.equal(422);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[0].next_step][0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[issues[topics[0].next_step][0].next_step].title);
    expect(res.canWriteMessage).to.equal(false);

    // Another message will be send from the bot after 100 milliseconds
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Would you like additional assistance with this?');
    expect(res.canWriteMessage).to.equal(false);
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));

    tracking = await TrackAutoreponseUsage.find({ user: 0 });
    expect(tracking.length).to.equal(1);
    expect(tracking[0].topic).to.equal(topics[0].title);
    expect(tracking[0].issue).to.equal(issues[topics[0].next_step][0].title);
    expect(tracking[0].subIssue).to.equal(null);
    expect(tracking[0].outcome).to.equal('template_sent');

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('support_or_topics');
    expect(chat.automatedChatState.topic).to.equal(topics[0].title);
    expect(chat.automatedChatState.issue).to.equal(issues[topics[0].next_step][0].title);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(tracking[0]._id.toString());
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal();

    // user selects no which marks the issue as resolved
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    const _sp0 = getSocketPromise(s0, 'automatedChat session end');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: { title: 'No', next_step: 'end' },
      });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));

    const [i, j] = await Promise.allSettled([_sp0, sp0]);
    expect(i.status).to.equal('fulfilled');
    expect(j.status).to.equal('fulfilled');
    await destroySocket(s0);

    chat = await Chat.findOne({ _id: chat._id });
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal(true);

    // verify translations
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '1')
      .send({ appVersion: '1.13.67', locale: 'bn' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 1)
      .send({
        firstName: `name 1`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({
        fcmToken: `token`,
      });
    expect(res.status).to.equal(200);

    const s1 = await initSocket(1);
    let sp1 = getSocketPromise(s1, 'automatedChatAutoResponse');
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 1)
      .send({ message: 'sending feedback from user 1' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user 1');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp1;

    expect(res.message.automatedChatOptions).to.deep.equal(topics.map((t) => ({ title: translate(t.title, 'bn'), next_step: t.next_step })));

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('topics');
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);

    let selectedItem = res.message.automatedChatOptions[0];
    sp1 = getSocketPromise(s1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem,
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp1;

    expect(res.message.text).to.equal(translate('Please select one of the options below:', 'bn'));
    let nextOptions = issues[topics[0].next_step].map((i) => ({ title: translate(i.title, 'bn'), next_step: i.next_step }));
    expect(res.message.automatedChatOptions).to.deep.equal(nextOptions);
    expect(res.canWriteMessage).to.equal(false);

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('issues');
    expect(chat.automatedChatState.topic).to.equal(selectedItem.title);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);

    selectedItem = res.message.automatedChatOptions[0];
    tracking = await TrackAutoreponseUsage.findOne({ user: 1 });
    expect(tracking).to.equal(null);

    // User won't be able to send message via message routes in automated stages
    res = await request(app)
      .post(`/v1/message?chatId=${res.message.chat}`)
      .set('authorization', 1)
      .send({
        text: 'test message',
      });
    expect(res.status).to.equal(422);

    sp1 = getSocketPromise(s1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem,
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp1;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(translate(templates[issues[topics[0].next_step][0].next_step].title, 'bn'));
    expect(res.canWriteMessage).to.equal(false);

    // Another message will be send from the bot after 100 milliseconds
    sp1 = getSocketPromise(s1, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp1;

    expect(res.message.text).to.equal(translate('Would you like additional assistance with this?', 'bn'));
    expect(res.canWriteMessage).to.equal(false);
    const options = supportOptions.map((o) => ({ title: translate(o.title, 'bn'), next_step: o.next_step }));
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(options));

    tracking = await TrackAutoreponseUsage.find({ user: 1 });
    expect(tracking.length).to.equal(1);
    expect(tracking[0].topic).to.equal(translate(topics[0].title, 'bn'));
    expect(tracking[0].issue).to.equal(translate(issues[topics[0].next_step][0].title, 'bn'));
    expect(tracking[0].subIssue).to.equal(null);
    expect(tracking[0].outcome).to.equal('template_sent');

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('support_or_topics');
    expect(chat.automatedChatState.topic).to.equal(translate(topics[0].title, 'bn'));
    expect(chat.automatedChatState.issue).to.equal(translate(issues[topics[0].next_step][0].title, 'bn'));
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(tracking[0]._id.toString());

    await destroySocket(s1);
  });

  it('should track multiple flow if user request template in each flow', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    let chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('topics');
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[0].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('issues');
    expect(chat.automatedChatState.topic).to.equal(topics[0].title);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);

    let tracking = await TrackAutoreponseUsage.findOne({ user: 0 });
    expect(tracking).to.equal(null);

    // User won't be able to send message via message routes in automated stages
    res = await request(app)
      .post(`/v1/message?chatId=${res.message.chat}`)
      .set('authorization', 0)
      .send({
        text: 'test message',
      });
    expect(res.status).to.equal(422);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[0].next_step][0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[issues[topics[0].next_step][0].next_step].title);
    expect(res.canWriteMessage).to.equal(false);

    // Another message will be send from the bot after 100 milliseconds
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.text).to.equal('Would you like additional assistance with this?');
    expect(res.canWriteMessage).to.equal(false);
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));

    tracking = await TrackAutoreponseUsage.find({ user: 0 });
    expect(tracking.length).to.equal(1);
    expect(tracking[0].topic).to.equal(topics[0].title);
    expect(tracking[0].issue).to.equal(issues[topics[0].next_step][0].title);
    expect(tracking[0].subIssue).to.equal(null);
    expect(tracking[0].outcome).to.equal('template_sent');

    chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('support_or_topics');
    expect(chat.automatedChatState.topic).to.equal(topics[0].title);
    expect(chat.automatedChatState.issue).to.equal(issues[topics[0].next_step][0].title);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(tracking[0]._id.toString());
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal();

    // user selects "No" which marks the issue as resolved
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    let _sp0 = getSocketPromise(s0, 'automatedChat session end');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: { title: 'No', next_step: 'end' },
      });
    expect(res.status).to.equal(200);

    const [i, j] = await Promise.allSettled([_sp0, sp0]);
    expect(i.status).to.equal('fulfilled');
    expect(j.status).to.equal('fulfilled');

    chat = await Chat.findOne({ _id: chat._id });
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal(true);

    // User starts another flow
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'Hello again' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[0],
      });
    expect(res.status).to.equal(200);

    // still only one flow traked
    tracking = await TrackAutoreponseUsage.find({ user: 0 });
    expect(tracking.length).to.equal(1);

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[0].next_step][1],
      });
    expect(res.status).to.equal(200);

    // again marking the flow as resolved
    _sp0 = getSocketPromise(s0, 'automatedChat session end');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: { title: 'No', next_step: 'end' },
      });
    expect(res.status).to.equal(200);

    // automatedChat session end socket will not be sent to this user again
    try {
      const result = await Promise.allSettled([_sp0]);
      expect(result[0].status).to.equal('rejected');
    } catch (e) {
      expect(e.message).to.equal('Failed to get reponse, connection timed out...');
    }

    // Another flow is tracked
    tracking = await TrackAutoreponseUsage.find({ user: 0 });
    expect(tracking.length).to.equal(2);
    expect(tracking[0].topic).to.equal(topics[0].title);
    expect(tracking[0].issue).to.equal(issues[topics[0].next_step][0].title);
    expect(tracking[1].topic).to.equal(topics[0].title);
    expect(tracking[1].issue).to.equal(issues[topics[0].next_step][1].title);

    await destroySocket(s0);
  });

  it('test flow topics > issues > go back to topics', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[0].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: { title: "See all topics", next_step: "topics" },
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.message.text).to.equal('Please select one of the options below:');
    expect(res.canWriteMessage).to.equal(false);

    const track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(0);

    const chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChatState.stage).to.equal('topics');
    expect(chat.automatedChatState.topic).to.equal(null);
    expect(chat.automatedChatState.issue).to.equal(null);
    expect(chat.automatedChatState.subIssue).to.equal(null);
    expect(chat.automatedChatState.trackId).to.equal(null);

    await destroySocket(s0);
  });

  it('test flow topics > other > support', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[topics.length - 1],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: supportOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal('Could you please describe your issue in more detail so we can assist you better?');
    expect(res.canWriteMessage).to.equal(true);

    let track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('support_requested');

    // A socket message will be sent to user with support added event
    sp0 = getSocketPromise(s0, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const s1 = await initSocket(BOO_SUPPORT_ID);
    const sp1 = getSocketPromise(s1, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('some random issues');

    const results = await Promise.allSettled([sp0, sp1]);
    expect(results[0].status).to.equal('fulfilled');
    expect(results[1].status).to.equal('fulfilled');

    const chat = await Chat.findOne({ _id: res.body.chat }).populate('users', 'lastMessage');
    const users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    await destroySocket(s0);
    await destroySocket(s1);

    // both user and support user can send messages via message routes now
    res = await request(app)
      .post(`/v1/message?chatId=${chat._id}`)
      .set('authorization', 0)
      .send({
        text: 'test message from user',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post(`/v1/message?chatId=${chat._id}`)
      .set('authorization', BOO_SUPPORT_ID)
      .send({
        text: 'test message from support user',
      });
    expect(res.status).to.equal(200);

    track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('forwarded');
  });

  it('test flow topics > issues > other > support', async () => {
    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[1],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[1].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: issues[topics[1].next_step][issues[topics[1].next_step].length - 2],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: supportOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal('Could you please describe your issue in more detail so we can assist you better?');
    expect(res.canWriteMessage).to.equal(true);

    // A socket message will be sent to user with support added event
    sp0 = getSocketPromise(s0, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const s1 = await initSocket(BOO_SUPPORT_ID);
    const sp1 = getSocketPromise(s1, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('some random issues');

    const results = await Promise.allSettled([sp0, sp1]);
    expect(results[0].status).to.equal('fulfilled');
    expect(results[1].status).to.equal('fulfilled');

    const chat = await Chat.findOne({ _id: res.body.chat }).populate('users', 'lastMessage');
    const users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    await destroySocket(s0);
    await destroySocket(s1);
  });

  it('test flow topics > issues > template > support', async () => {
    let s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[1],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[1].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    const options = res.message.automatedChatOptions;
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: options[2],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal(templates[options[2].next_step].title);
    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.canWriteMessage).to.equal(false);

    let track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('template_sent');

    // A socket message will be sent to user with a new message after the template has been sent
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Would you like additional assistance with this?');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: supportOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal('Could you please describe your issue in more detail so we can assist you better?');
    expect(res.canWriteMessage).to.equal(true);

    track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('support_requested');

    // A socket message will be sent to user with support added event
    sp0 = getSocketPromise(s0, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const s1 = await initSocket(BOO_SUPPORT_ID);
    const sp1 = getSocketPromise(s1, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('some random issues');

    const results = await Promise.allSettled([sp0, sp1]);
    expect(results[0].status).to.equal('fulfilled');
    expect(results[1].status).to.equal('fulfilled');

    const chat = await Chat.findOne({ _id: res.body.chat }).populate('users', 'lastMessage');
    const users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('forwarded');

    await destroySocket(s0);
    await destroySocket(s1);
  });

  it('test flow topics > issues > sub issues > template > template > support', async () => {
    let s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[2],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(topics[2].title);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[2].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    let options = res.message.automatedChatOptions;
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: options[1],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(options[1].title);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Please select one of the options below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(subIssues[options[1].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    options = res.message.automatedChatOptions;
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: options[0],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(options[0].title);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[options[0].next_step].title);
    expect(res.canWriteMessage).to.equal(false);

    // Another Socket message will be sent to user asking if they want additional information
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    let additionalOptions = [{ title: "Yes", next_step: templates[options[0].next_step].next_step }, { title: "No", next_step: "end" }];
    expect(res.message.automatedChatOptions).to.deep.equal(additionalOptions);
    expect(res.message.text).to.equal('Would you like more information on this?');
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: additionalOptions[0],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(additionalOptions[0].title);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(templates[templates[options[0].next_step].next_step].title);
    expect(res.canWriteMessage).to.equal(false);

    // Another socket message will be sent to user asking if they want additional assistance
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));
    expect(res.message.text).to.equal('Would you like additional assistance with this?');
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: res.message.automatedChatOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal('Could you please describe your issue in more detail so we can assist you better?');
    expect(res.canWriteMessage).to.equal(true);

    // A socket message will be sent to user with support added event
    sp0 = getSocketPromise(s0, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const s1 = await initSocket(BOO_SUPPORT_ID);
    const sp1 = getSocketPromise(s1, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.lastMessage.automatedChatOptions).to.equal(undefined);
    expect(res.lastMessage.text).to.equal('Boo invited Boo Support to the chat.');
    expect(res.automatedChat).to.equal(true);
    expect(res.supportAdded).to.equal(true);
    expect(res.noreply).to.equal(undefined);

    let results = await Promise.allSettled([sp1]);
    expect(results[0].status).to.equal('fulfilled');

    let chat = await Chat.findOne({ _id: res._id }).populate('users', 'lastMessage');
    let users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    let track = await TrackAutoreponseUsage.find({ user: 0 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('forwarded');

    // Validate translations
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '1')
      .send({ appVersion: '1.13.67', locale: 'hi' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 1)
      .send({
        firstName: `name 1`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({
        fcmToken: `token`,
      });
    expect(res.status).to.equal(200);

    const u1 = await initSocket(1);
    let sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 1)
      .send({ message: 'sending feedback from user 1' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user 1');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    expect(res.message.text).to.equal(translate('Welcome to Boo Support. What can we help you with?\nChoose from one option below:', 'hi'));
    expect(res.message.automatedChatOptions).to.deep.equal(topics.map((t) => ({ title: translate(t.title, 'hi'), next_step: t.next_step })));

    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem: topics[2],
      });
    expect(res.status).to.equal(200);
    options = issues[topics[2].next_step].map((t) => ({ title: translate(t.title, 'hi'), next_step: t.next_step }));

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    expect(res.message.automatedChatOptions).to.deep.equal(options);
    expect(res.canWriteMessage).to.equal(false);

    options = res.message.automatedChatOptions;
    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem: options[1],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;
    expect(res.message.text).to.equal(translate('Please select one of the options below:', 'hi'));
    options = subIssues[options[1].next_step].map((t) => ({ title: translate(t.title, 'hi'), next_step: t.next_step }));
    expect(res.message.automatedChatOptions).to.deep.equal(options);
    expect(res.canWriteMessage).to.equal(false);

    options = res.message.automatedChatOptions;
    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem: options[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(translate(templates[options[0].next_step].title, 'hi'));
    expect(res.canWriteMessage).to.equal(false);

    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    additionalOptions = [{ title: "Yes", next_step: templates[options[0].next_step].next_step }, { title: "No", next_step: "end" }].map((t) => ({ title: translate(t.title, 'hi'), next_step: t.next_step }));
    expect(res.message.automatedChatOptions).to.deep.equal(additionalOptions);
    expect(res.message.text).to.equal(translate('Would you like more information on this?', 'hi'));
    expect(res.canWriteMessage).to.equal(false);

    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem: additionalOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(translate(templates[templates[options[0].next_step].next_step].title, 'hi'));
    expect(res.canWriteMessage).to.equal(false);

    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    additionalOptions = supportOptions.map((t) => ({ title: translate(t.title, 'hi'), next_step: t.next_step }));
    expect(res.message.automatedChatOptions).to.deep.equal(additionalOptions);
    expect(res.message.text).to.equal(translate('Would you like additional assistance with this?', 'hi'));
    expect(res.canWriteMessage).to.equal(false);

    sp2 = getSocketPromise(u1, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        selectedItem: additionalOptions[0],
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp2;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal(translate('Could you please describe your issue in more detail so we can assist you better?', 'hi'));
    expect(res.canWriteMessage).to.equal(true);

    // A socket message will be sent to user with support added event
    sp2 = getSocketPromise(u1, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const support_socket = await initSocket(BOO_SUPPORT_ID);
    const socket_promise_support = getSocketPromise(support_socket, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 1)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);

    results = await Promise.allSettled([sp2, socket_promise_support]);
    expect(results[0].status).to.equal('fulfilled');
    expect(results[1].status).to.equal('fulfilled');

    chat = await Chat.findOne({ _id: res.body.chat }).populate('users', 'lastMessage');
    users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    track = await TrackAutoreponseUsage.find({ user: 1 });
    expect(track.length).to.equal(1);
    expect(track[0].outcome).to.equal('forwarded');

    await destroySocket(s0);
    await destroySocket(s1);
    await destroySocket(u1);
    await destroySocket(support_socket);
  });

  it('test leaving automated chat /v1/chat/leaveAutomatedChat', async () => {
    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake((params) => new Promise((resolve, reject) => {
      console.log('Fake messaging().send() ', params);
      if (params.token === 'invalidToken') {
        return reject(new Error('Fake error'));
      }
      resolve({ response: 'success' });
    }));

    let s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[topics.length - 1],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(topics[topics.length - 1].title);

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(supportOptions));
    expect(res.canWriteMessage).to.equal(false);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: supportOptions[0],
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('Yes');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.message.text).to.equal('Could you please describe your issue in more detail so we can assist you better?');
    expect(res.canWriteMessage).to.equal(true);

    // A socket message will be sent to user with support added event
    sp0 = getSocketPromise(s0, 'supportAdded in automatedChat');

    // A socket message will be sent to support with approved automated chat
    const s1 = await initSocket(BOO_SUPPORT_ID);
    let sp1 = getSocketPromise(s1, 'approved chat');

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('some random issues');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.lastMessage.automatedChatOptions).to.equal(undefined);
    expect(res.lastMessage.text).to.equal('Boo invited Boo Support to the chat.');
    expect(res.automatedChat).to.equal(true);
    expect(res.supportAdded).to.equal(true);
    expect(res.noreply).to.equal(undefined);

    const results = await Promise.allSettled([sp1]);
    expect(results[0].status).to.equal('fulfilled');
    sinon.assert.calledTwice(sendStub); // also sent support discontinuation message

    let chat = await Chat.findOne({ _id: res._id }).populate('users', 'lastMessage');
    const users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(users.length).to.equal(3);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    let track = await TrackAutoreponseUsage.findOne({ user: 0 }).sort({ _id: -1 });
    expect(track.outcome).to.equal('forwarded');

    res = await request(app)
      .post(`/v1/message?chatId=${chat._id}`)
      .set('authorization', 0)
      .send({
        text: 'test message from user',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post(`/v1/message?chatId=${chat._id}`)
      .set('authorization', BOO_SUPPORT_ID)
      .send({
        text: 'test message from support user',
      });
    expect(res.status).to.equal(200);

    // User tries to re-initiate automated chat, message should be saved as a message in the chat
    // An message socket event will be sent to support user
    sp1 = getSocketPromise(s1, 'message');
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'hello',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('hello');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp1;

    expect(res.text).to.equal('hello');
    expect(res.sender).to.equal('0');

    // chat state will not be changed
    chat = await Chat.findOne({ _id: res.chat });
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal();
    expect(chat.users.length).to.equal(3);

    sp0 = getSocketPromise(s0, 'support left automatedChat');
    let _sp0 = getSocketPromise(s0, 'automatedChat session end');
    res = await request(app)
      .patch(`/v1/chat/leaveAutomatedChat`)
      .set('authorization', BOO_SUPPORT_ID)
      .send({
        chatId: chat._id,
      });
    expect(res.status).to.equal(200);

    const [end, result] = await Promise.allSettled([_sp0, sp0]);
    expect(end.status).to.equal('fulfilled');
    expect(result.status).to.equal('fulfilled');
    res = result.value;

    expect(res.lastMessage.text).to.equal('Boo Support left the chat.');
    expect(res.automatedChat).to.equal(true);
    expect(res.supportAdded).to.equal(undefined);
    expect(res.noreply).to.equal(true);

    await new Promise((r) => setTimeout(r, 100));
    track = await TrackAutoreponseUsage.findOne({ user: 0 }).sort({ _id: -1 });
    expect(track.outcome).to.equal('resolved');

    chat = await Chat.findOne({ _id: chat._id });
    expect(chat.users.length).to.equal(2);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal(true);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', BOO_SUPPORT_ID);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', BOO_SUPPORT_ID);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', BOO_SUPPORT_ID);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // User won't be able to send any message via message route
    res = await request(app)
      .post(`/v1/message?chatId=${chat._id}`)
      .set('authorization', 0)
      .send({
        text: 'test message from user',
      });
    expect(res.status).to.equal(422);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    // User can reinitiate automated steps via feedback routes
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'hello',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('hello');

    await new Promise((r) => setTimeout(r, 100));
    res = await sp0;

    expect(res.message.text).to.equal('Welcome back to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    // Still chat should have popupshown as true
    chat = await Chat.findOne({ _id: chat._id });
    expect(chat.automatedChatState.stage).to.equal('topics');
    expect(chat.automatedChatState.supportAdded).to.equal(false);
    expect(chat.automatedChatState.rateSupportShown).to.equal();
    expect(chat.automatedChatState.rateSupportShownV2).to.equal(true);

    await destroySocket(s0);
    await destroySocket(s1);
  });

  it('test flow for banned users', async () => {
    // shadow ban user for device id
    const user = await User.findOne({ _id: 0 });
    await shadowBan(user, null, `Auto-ban: Device ID`, 'testing banned users support flow');

    const s0 = await initSocket(0);
    let sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');

    let result = await Promise.allSettled([sp0]);
    expect(result[0].status).to.equal('fulfilled');
    res = result[0].value;

    // User will not get automated flow, instead they will get a message to email support
    expect(res.message.text).to.equal(`I'm sorry to hear. For better assistance, please email <NAME_EMAIL> with your Boo ID and a description of the issue. Thank you for your patience.`);
    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.canWriteMessage).to.equal(false);

    let chat = await Chat.findOne({ _id: res.message.chat });
    expect(chat.automatedChat).to.equal(true);
    expect(chat.automatedChatState.stage).to.equal(null);

    // User will get same message, if they send another feedback
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending another feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending another feedback from user');

    result = await Promise.allSettled([sp0]);
    expect(result[0].status).to.equal('fulfilled');
    res = result[0].value;

    expect(res.message.text).to.equal(`I'm sorry to hear. For better assistance, please email <NAME_EMAIL> with your Boo ID and a description of the issue. Thank you for your patience.`);
    expect(res.message.automatedChatOptions).to.equal(undefined);
    expect(res.canWriteMessage).to.equal(false);

    // Remove ban from user and check if user get automated flow
    await unban(user);

    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback once ban removed' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback once ban removed');

    result = await Promise.allSettled([sp0]);
    expect(result[0].status).to.equal('fulfilled');
    res = result[0].value;

    expect(res.message.text).to.equal('Welcome back to Boo Support. What can we help you with?\nChoose from one option below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(topics));
    expect(res.canWriteMessage).to.equal(false);

    // Now user can select options from automated flow
    sp0 = getSocketPromise(s0, 'automatedChatAutoResponse');
    res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ selectedItem: topics[0] });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal(topics[0].title);

    result = await Promise.allSettled([sp0]);
    expect(result[0].status).to.equal('fulfilled');
    res = result[0].value;

    expect(res.message.text).to.equal('Please select one of the options below:');
    expect(res.message.automatedChatOptions).to.deep.equal(getObjectForExistingVersions(issues[topics[0].next_step]));
    expect(res.canWriteMessage).to.equal(false);

    await destroySocket(s0);
  });

  it('test IncomingSupportChat metric increment from automated chat', async () => {
    const putMetricDataStub = sinon.stub(fakeCloudwatch, 'putMetricData').callThrough();
    const params = {
      MetricData: [
        {
          MetricName: 'IncomingSupportChat',
          Value: 1,
          Unit: 'Count',
        },
      ],
      Namespace: 'SupportTeamMetrics_test',
    };

    let res = await request(app)
      .post('/v1/feedback/chat')
      .set('authorization', 0)
      .send({ message: 'sending feedback from user' });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('sending feedback from user');
    await new Promise((r) => setTimeout(r, 100));

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: topics[topics.length - 1],
      });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));

    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        selectedItem: supportOptions[0],
      });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));

    // support will be added to the chat
    res = await request(app)
      .post(`/v1/feedback/chat`)
      .set('authorization', 0)
      .send({
        message: 'some random issues',
      });
    expect(res.status).to.equal(200);
    expect(res.body.text).to.equal('some random issues');
    await new Promise((r) => setTimeout(r, 100));

    const chat = await Chat.findOne({ _id: res.body.chat }).populate('users', 'lastMessage');
    const users = chat.users.map((u) => u._id.toString());
    expect(users).to.include(BOO_SUPPORT_ID);
    expect(chat.automatedChatState.stage).to.equal(null);
    expect(chat.automatedChatState.supportAdded).to.equal(true);

    expect(putMetricDataStub.calledOnce).to.equal(true);
    // Params should match with first calls first argument
    expect(putMetricDataStub.args[0][0]).to.deep.equal(params);
    putMetricDataStub.restore();
  });
});

it('send feedback', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/feedback/rateApp')
    .set('authorization', 0)
    .send({ feedback: 'test' })
  expect(res.status).to.equal(200);

  const docs = await Feedback.find();
  expect(docs.length).to.equal(1);
  expect(docs[0].user).to.equal('0');
  expect(docs[0].feedback).to.equal('test');
});

it('is_boo_support', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.is_boo_support).to.equal();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', BOO_SUPPORT_ID);
  expect(res.status).to.equal(200);
  expect(res.body.is_boo_support).to.equal(true);
});

it('prevent support account from getting reported or banned', async () => {
  await createSupportUsers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // report should be ignored
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: BOO_SUPPORT_ID,
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: BOO_BOT_ID,
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  reports = await Report.find();
  expect(reports.length).to.equal(0);

  // should not be banned
  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { support: true, approveQod: true };
  await user.save();

  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 0)
    .send({ user: BOO_SUPPORT_ID });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 0)
    .send({ user: BOO_BOT_ID });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/admin/tempBan')
    .set('authorization', 0)
    .send({ reason: 'text', banUser: BOO_SUPPORT_ID });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/admin/tempBan')
    .set('authorization', 0)
    .send({ reason: 'text', banUser: BOO_BOT_ID });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: BOO_SUPPORT_ID });
  expect(user.shadowBanned).to.equal(false);
  expect(user.tempBanReason).to.equal();
  expect(user.banHistory.length).to.equal(0);

  user = await User.findOne({ _id: BOO_BOT_ID });
  expect(user.shadowBanned).to.equal(false);
  expect(user.tempBanReason).to.equal();
  expect(user.banHistory.length).to.equal(0);
});
