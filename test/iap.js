const { expect } = require('chai');
const request = require('supertest');
const async = require('async');
const axios = require("axios");
const sinon = require('sinon');
const moment = require('moment');
const iap = require('in-app-purchase');
const appStoreApi = require('app-store-server-api');
const { DateTime } = require('luxon');
const {
  app, mongoose, validImagePath, initialCoins,
} = require('./common');
const { notifs, reset, waitFor, restoreAxiosStub, AppsFlyerClient } = require('./stub');
const stub = require('./stub');

const { IAP_ERROR_RECEIPTS } = stub;
const User = require('../models/user');
const PurchaseReceipt = require('../models/purchase-receipt');
const PurchasePremiumResponse = require('../models/purchase-premium-response');
const PromoCode = require('../models/promo-code');
const {
  initApp, getMyProfile, setDeviceInfo, setLocation, stripeWebhook
} = require('./helper/api');
const StickerPack = require('../models/sticker-pack');
const StickerPackPurchaseReceipt = require('../models/sticker-pack-purchase-receipt');
const iapHelper = require('./helper/iap');
const { getMockStickerPackId } = require('./helper/sticker-packs');
const curExLib = require('../lib/currency-exchange');
const metricsLib = require('../lib/metrics');
const iapLib = require('../lib/iap');
const appStoreConnectLib = require('../lib/app-store-connect');
const WebVisitor = require('../models/web-visitor');

const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const premium = require('../lib/premium');

const { getMockCheckoutSessionEvent } = require('./helper/stripe');
const basic = require('../lib/basic');

const oneDayMs = 86400000;
const now = Date.now();
const validExpirationDate = now + oneDayMs;
const valid1MonthExpirationDate = now + 30 * oneDayMs;
const renewedExpirationDate = now + 2 * oneDayMs;
const expiredExpirationDate = now - oneDayMs;
const diffTimeTolerance = 1000;

const googleTransactionId = 'GPA.3369-7229-4364-41990';
const googleSubscriptionId = 'ocpeipimpkfiedhdhhhfgchh.AO-J1Ox6Z9Ug4uaWLizHE9-bG3gdmoGU2-Y32R3xTMG84_h0ZC_bIRuGh7ZtPaMkF2L0gHD2uNMOih5U23LZk_hEjitrG9hDuBf6s0PBlNe5MoTOylkBM_8';

const validGoogleReceipt = {
  orderId: googleTransactionId,
  packageName: 'enterprises.dating.boo',
  productId: 'boo_infinity_1_month',
  purchaseTime: now,
  purchaseState: undefined,
  purchaseToken: googleSubscriptionId,
  autoRenewing: true,
  acknowledged: false,
  status: 0,
  startTimeMillis: now,
  expiryTimeMillis: validExpirationDate,
  priceCurrencyCode: 'USD',
  priceAmountMicros: 19990000,
  countryCode: 'US',
  developerPayload: NaN,
  paymentState: 1,
  acknowledgementState: 0,
  introductoryPriceInfo: {
    introductoryPriceCurrencyCode: 'USD',
    introductoryPriceAmountMicros: '9990000',
    introductoryPricePeriod: 'P1M',
    introductoryPriceCycles: 1,
  },
  kind: 'androidpublisher#subscriptionPurchase',
  service: 'google',
  expirationTime: validExpirationDate,
  cancelReason: undefined,
  transactionId: 'becmioofnfoojhpedmhedkap.AO-J1Ox9eDstVxgfiobOux4qgN0DF6vvRyWWZBBOex2PPMRGXqiQh3QvigjXpmpozr3XYxElO4g5dH3G3e0FWKuAKXDMg1LhrgifTLU4h4nmrogCv4ppLM5TG8d7PY3xpEHWGm4-qfpN',
  purchaseDate: now,
  quantity: 1,
  expirationDate: `${validExpirationDate}`,
};

const valid1MonthGoogleReceipt = {
  orderId: googleTransactionId,
  packageName: 'enterprises.dating.boo',
  productId: 'boo_infinity_1_month',
  purchaseTime: now,
  purchaseState: undefined,
  purchaseToken: googleSubscriptionId,
  autoRenewing: true,
  acknowledged: false,
  status: 0,
  startTimeMillis: now,
  expiryTimeMillis: valid1MonthExpirationDate,
  priceCurrencyCode: 'USD',
  priceAmountMicros: 19990000,
  countryCode: 'US',
  developerPayload: NaN,
  paymentState: 1,
  acknowledgementState: 0,
  introductoryPriceInfo: {
    introductoryPriceCurrencyCode: 'USD',
    introductoryPriceAmountMicros: '9990000',
    introductoryPricePeriod: 'P1M',
    introductoryPriceCycles: 1,
  },
  kind: 'androidpublisher#subscriptionPurchase',
  service: 'google',
  expirationTime: validExpirationDate,
  cancelReason: undefined,
  transactionId: 'becmioofnfoojhpedmhedkap.AO-J1Ox9eDstVxgfiobOux4qgN0DF6vvRyWWZBBOex2PPMRGXqiQh3QvigjXpmpozr3XYxElO4g5dH3G3e0FWKuAKXDMg1LhrgifTLU4h4nmrogCv4ppLM5TG8d7PY3xpEHWGm4-qfpN',
  purchaseDate: now,
  quantity: 1,
  expirationDate: `${valid1MonthExpirationDate}`,
};

const validButCanceledGoogleReceipt = {
  orderId: googleTransactionId,
  packageName: 'enterprises.dating.boo',
  productId: 'boo_infinity_1_month',
  purchaseTime: now,
  purchaseState: undefined,
  purchaseToken: googleSubscriptionId,
  autoRenewing: false,
  acknowledged: false,
  status: 0,
  startTimeMillis: now,
  expiryTimeMillis: validExpirationDate,
  priceCurrencyCode: 'USD',
  priceAmountMicros: 19990000,
  countryCode: 'US',
  developerPayload: NaN,
  paymentState: 1,
  acknowledgementState: 0,
  introductoryPriceInfo: {
    introductoryPriceCurrencyCode: 'USD',
    introductoryPriceAmountMicros: '9990000',
    introductoryPricePeriod: 'P1M',
    introductoryPriceCycles: 1,
  },
  kind: 'androidpublisher#subscriptionPurchase',
  service: 'google',
  expirationTime: validExpirationDate,
  cancelReason: 1,
  transactionId: 'becmioofnfoojhpedmhedkap.AO-J1Ox9eDstVxgfiobOux4qgN0DF6vvRyWWZBBOex2PPMRGXqiQh3QvigjXpmpozr3XYxElO4g5dH3G3e0FWKuAKXDMg1LhrgifTLU4h4nmrogCv4ppLM5TG8d7PY3xpEHWGm4-qfpN',
  purchaseDate: now,
  quantity: 1,
  expirationDate: `${validExpirationDate}`,
};

const expiredGoogleReceipt = {
  orderId: googleTransactionId,
  packageName: 'enterprises.dating.boo',
  productId: 'boo_infinity_1_month',
  purchaseTime: now,
  purchaseState: undefined,
  purchaseToken: googleSubscriptionId,
  autoRenewing: true,
  acknowledged: false,
  status: 0,
  startTimeMillis: now,
  expiryTimeMillis: expiredExpirationDate,
  priceCurrencyCode: 'USD',
  priceAmountMicros: 19990000,
  countryCode: 'US',
  developerPayload: NaN,
  paymentState: 1,
  acknowledgementState: 0,
  introductoryPriceInfo: {
    introductoryPriceCurrencyCode: 'USD',
    introductoryPriceAmountMicros: '9990000',
    introductoryPricePeriod: 'P1M',
    introductoryPriceCycles: 1,
  },
  kind: 'androidpublisher#subscriptionPurchase',
  service: 'google',
  expirationTime: expiredExpirationDate,
  cancelReason: undefined,
  transactionId: 'becmioofnfoojhpedmhedkap.AO-J1Ox9eDstVxgfiobOux4qgN0DF6vvRyWWZBBOex2PPMRGXqiQh3QvigjXpmpozr3XYxElO4g5dH3G3e0FWKuAKXDMg1LhrgifTLU4h4nmrogCv4ppLM5TG8d7PY3xpEHWGm4-qfpN',
  purchaseDate: now,
  quantity: 1,
  expirationDate: `${expiredExpirationDate}`,
};

const googleInitialPurchaseRtdnBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6NCwicHVyY2hhc2VUb2tlbiI6Im9jcGVpcGltcGtmaWVkaGRoaGhmZ2NoaC5BTy1KMU94Nlo5VWc0dWFXTGl6SEU5LWJHM2dkbW9HVTItWTMyUjN4VE1HODRfaDBaQ19iSVJ1R2g3WnRQYU1rRjJMMGdIRDJ1Tk1PaWg1VTIzTFprX2hFaml0ckc5aER1QmY2czBQQmxOZTVNb1RPeWxrQk1fOCIsInN1YnNjcmlwdGlvbklkIjoiYm9vX2luZmluaXR5XzFfbW9udGgifX0=',
    messageId: '3060979050628477',
    message_id: '3060979050628477',
    publishTime: '2021-09-16T07:50:12.994Z',
    publish_time: '2021-09-16T07:50:12.994Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const googleRenewalRtdnBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6MiwicHVyY2hhc2VUb2tlbiI6Im9jcGVpcGltcGtmaWVkaGRoaGhmZ2NoaC5BTy1KMU94Nlo5VWc0dWFXTGl6SEU5LWJHM2dkbW9HVTItWTMyUjN4VE1HODRfaDBaQ19iSVJ1R2g3WnRQYU1rRjJMMGdIRDJ1Tk1PaWg1VTIzTFprX2hFaml0ckc5aER1QmY2czBQQmxOZTVNb1RPeWxrQk1fOCIsInN1YnNjcmlwdGlvbklkIjoiYm9vX2luZmluaXR5XzFfbW9udGgifX0=',
    messageId: '3060979050628477',
    message_id: '3060979050628477',
    publishTime: '2021-09-16T07:50:12.994Z',
    publish_time: '2021-09-16T07:50:12.994Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const googleRevokeRtdnBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6MTIsInB1cmNoYXNlVG9rZW4iOiJvY3BlaXBpbXBrZmllZGhkaGhoZmdjaGguQU8tSjFPeDZaOVVnNHVhV0xpekhFOS1iRzNnZG1vR1UyLVkzMlIzeFRNRzg0X2gwWkNfYklSdUdoN1p0UGFNa0YyTDBnSEQydU5NT2loNVUyM0xaa19oRWppdHJHOWhEdUJmNnMwUEJsTmU1TW9UT3lsa0JNXzgiLCJzdWJzY3JpcHRpb25JZCI6ImJvb19pbmZpbml0eV8xX21vbnRoIn19',
    messageId: '3060979050628477',
    message_id: '3060979050628477',
    publishTime: '2021-09-16T07:50:12.994Z',
    publish_time: '2021-09-16T07:50:12.994Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const googleCancelRtdnBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6MywicHVyY2hhc2VUb2tlbiI6Im9jcGVpcGltcGtmaWVkaGRoaGhmZ2NoaC5BTy1KMU94Nlo5VWc0dWFXTGl6SEU5LWJHM2dkbW9HVTItWTMyUjN4VE1HODRfaDBaQ19iSVJ1R2g3WnRQYU1rRjJMMGdIRDJ1Tk1PaWg1VTIzTFprX2hFaml0ckc5aER1QmY2czBQQmxOZTVNb1RPeWxrQk1fOCIsInN1YnNjcmlwdGlvbklkIjoiYm9vX2luZmluaXR5XzFfbW9udGgifX0=',
    messageId: '3060979050628477',
    message_id: '3060979050628477',
    publishTime: '2021-09-16T07:50:12.994Z',
    publish_time: '2021-09-16T07:50:12.994Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const googleHoldRtdnBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6NSwicHVyY2hhc2VUb2tlbiI6Im9jcGVpcGltcGtmaWVkaGRoaGhmZ2NoaC5BTy1KMU94Nlo5VWc0dWFXTGl6SEU5LWJHM2dkbW9HVTItWTMyUjN4VE1HODRfaDBaQ19iSVJ1R2g3WnRQYU1rRjJMMGdIRDJ1Tk1PaWg1VTIzTFprX2hFaml0ckc5aER1QmY2czBQQmxOZTVNb1RPeWxrQk1fOCIsInN1YnNjcmlwdGlvbklkIjoiYm9vX2luZmluaXR5XzFfbW9udGgifX0=',
    messageId: '3060979050628477',
    message_id: '3060979050628477',
    publishTime: '2021-09-16T07:50:12.994Z',
    publish_time: '2021-09-16T07:50:12.994Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const googleCoinPurchaseNotificationBody = {
  message: {
    data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMzkxNjA0NDAzOCIsIm9uZVRpbWVQcm9kdWN0Tm90aWZpY2F0aW9uIjp7InZlcnNpb24iOiIxLjAiLCJub3RpZmljYXRpb25UeXBlIjoxLCJwdXJjaGFzZVRva2VuIjoiY2xnY25oaGduaGpjaWxiaWRtbmdhbWtjLkFPLUoxT3pjQ1ZNU2hiUHVrU29BVHVUVllrZDFsYi1nSl9kLU9WVDRvclVwMmpjeHplbHdXWWhvM1h4X0JaZ1JaTkItU1NacUZVYXc0SEZGT1BQTi1BbG4wQmh6bFUtYklxbmRveFFrVVdrZnM1MFl4ang3XzhrIiwic2t1IjoiNDAwMF9jb2lucyJ9fQ==',
    messageId: '3200604065629438',
    message_id: '3200604065629438',
    publishTime: '2021-10-11T01:34:04.182Z',
    publish_time: '2021-10-11T01:34:04.182Z',
  },
  subscription: 'projects/boo-dating-prod/subscriptions/renewals',
};

const renewedGoogleReceipt = {
  orderId: 'renewal_transaction_id',
  packageName: 'enterprises.dating.boo',
  productId: 'boo_infinity_1_month',
  purchaseState: undefined,
  purchaseToken: googleSubscriptionId,
  autoRenewing: true,
  acknowledged: false,
  status: 0,
  startTimeMillis: now,
  expiryTimeMillis: renewedExpirationDate,
  priceCurrencyCode: 'USD',
  priceAmountMicros: 19990000,
  countryCode: 'US',
  developerPayload: NaN,
  paymentState: 1,
  acknowledgementState: 0,
  introductoryPriceInfo: {
    introductoryPriceCurrencyCode: 'USD',
    introductoryPriceAmountMicros: '9990000',
    introductoryPricePeriod: 'P1M',
    introductoryPriceCycles: 1,
  },
  kind: 'androidpublisher#subscriptionPurchase',
  service: 'google',
  expirationTime: renewedExpirationDate,
  cancelReason: undefined,
  transactionId: 'becmioofnfoojhpedmhedkap.AO-J1Ox9eDstVxgfiobOux4qgN0DF6vvRyWWZBBOex2PPMRGXqiQh3QvigjXpmpozr3XYxElO4g5dH3G3e0FWKuAKXDMg1LhrgifTLU4h4nmrogCv4ppLM5TG8d7PY3xpEHWGm4-qfpN',
  quantity: 1,
  expirationDate: `${renewedExpirationDate}`,
};

const appleTransactionId = '410000761950383';
const appleSubscriptionId = '410000761950383';

const validAppleReceipt = {
  receipt: {
    bundle_id: 'enterprises.dating.boo',
    in_app: [{
      quantity: 1,
      product_id: 'boo_infinity_1_month',
      transaction_id: appleTransactionId,
      original_transaction_id: appleSubscriptionId,
      purchase_date: '2020-09-20 06:37:38 Etc/GMT',
      purchase_date_ms: now,
      purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
      original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
      original_purchase_date_ms: now,
      original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
      expires_date: '2020-10-20 06:37:38 Etc/GMT',
      expires_date_ms: validExpirationDate,
      expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
      web_order_line_item_id: 410000303240049,
      is_trial_period: 'false',
      is_in_intro_offer_period: 'true',
      in_app_ownership_type: 'PURCHASED',
      subscription_group_identifier: '20679156',
    }],
  },
  latest_receipt_info: [{
    quantity: 1,
    product_id: 'boo_infinity_1_month',
    transaction_id: appleTransactionId,
    original_transaction_id: appleSubscriptionId,
    purchase_date: '2020-09-20 06:37:38 Etc/GMT',
    purchase_date_ms: now,
    purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
    original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
    original_purchase_date_ms: now,
    original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
    expires_date: '2020-10-20 06:37:38 Etc/GMT',
    expires_date_ms: validExpirationDate,
    expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
    web_order_line_item_id: 410000303240049,
    is_trial_period: 'false',
    is_in_intro_offer_period: 'true',
    in_app_ownership_type: 'PURCHASED',
    subscription_group_identifier: '20679156',
  }],
  service: 'apple',
};

const renewedAppleReceipt = {
  receipt: {
    bundle_id: 'enterprises.dating.boo',
    in_app: [{
      quantity: 1,
      product_id: 'boo_infinity_1_month',
      transaction_id: 'renewal_transaction_id',
      original_transaction_id: appleSubscriptionId,
      purchase_date: '2020-09-20 06:37:38 Etc/GMT',
      purchase_date_ms: validExpirationDate,
      purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
      original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
      original_purchase_date_ms: now,
      original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
      expires_date: '2020-10-20 06:37:38 Etc/GMT',
      expires_date_ms: renewedExpirationDate,
      expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
      web_order_line_item_id: 410000303240049,
      is_trial_period: 'false',
      is_in_intro_offer_period: 'true',
      in_app_ownership_type: 'PURCHASED',
      subscription_group_identifier: '20679156',
    }],
  },
  latest_receipt_info: [{
    quantity: 1,
    product_id: 'boo_infinity_1_month',
    transaction_id: 'renewal_transaction_id',
    original_transaction_id: appleSubscriptionId,
    purchase_date: '2020-09-20 06:37:38 Etc/GMT',
    purchase_date_ms: validExpirationDate,
    purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
    original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
    original_purchase_date_ms: now,
    original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
    expires_date: '2020-10-20 06:37:38 Etc/GMT',
    expires_date_ms: renewedExpirationDate,
    expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
    web_order_line_item_id: 410000303240049,
    is_trial_period: 'false',
    is_in_intro_offer_period: 'true',
    in_app_ownership_type: 'PURCHASED',
    subscription_group_identifier: '20679156',
  }],
  service: 'apple',
};

const appleRenewalRtdnBody = {
  signedPayload: {
    notificationType: 'DID_RENEW',
    notificationUUID: 'cc6c79c4-8c43-45d5-9995-29a3ce3be5d8',
    data: {
      bundleId: 'enterprises.dating.boo',
      bundleVersion: '*******',
      environment: 'Sandbox',
      signedRenewalInfo: {
        originalTransactionId: appleSubscriptionId,
        autoRenewProductId: 'boo_infinity_1_month',
        productId: 'boo_infinity_1_month',
        autoRenewStatus: 1,
        signedDate: 1683472713491,
        environment: 'Sandbox',
        recentSubscriptionStartDate: 1683471769000,
      },
      signedTransactionInfo: {
        transactionId: '2000000326314832',
        originalTransactionId: appleSubscriptionId,
        webOrderLineItemId: '2000000026814447',
        bundleId: 'enterprises.dating.boo',
        productId: 'boo_infinity_1_month',
        subscriptionGroupIdentifier: '20679156',
        purchaseDate: validExpirationDate,
        originalPurchaseDate: now,
        expiresDate: renewedExpirationDate,
        quantity: 1,
        type: 'Auto-Renewable Subscription',
        inAppOwnershipType: 'PURCHASED',
        signedDate: 1683472713516,
        environment: 'Sandbox',
      },
    },
  },
};

const expiredAppleReceipt = {
  receipt: {
    bundle_id: 'enterprises.dating.boo',
    in_app: [{
      quantity: 1,
      product_id: 'boo_infinity_1_month',
      transaction_id: appleTransactionId,
      original_transaction_id: appleSubscriptionId,
      purchase_date: '2020-09-20 06:37:38 Etc/GMT',
      purchase_date_ms: now,
      purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
      original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
      original_purchase_date_ms: now,
      original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
      expires_date: '2020-10-20 06:37:38 Etc/GMT',
      expires_date_ms: expiredExpirationDate,
      expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
      web_order_line_item_id: 410000303240049,
      is_trial_period: 'false',
      is_in_intro_offer_period: 'true',
      in_app_ownership_type: 'PURCHASED',
      subscription_group_identifier: '20679156',
    }],
  },
  latest_receipt_info: [{
    quantity: 1,
    product_id: 'boo_infinity_1_month',
    transaction_id: appleTransactionId,
    original_transaction_id: appleSubscriptionId,
    purchase_date: '2020-09-20 06:37:38 Etc/GMT',
    purchase_date_ms: now,
    purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
    original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
    original_purchase_date_ms: now,
    original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
    expires_date: '2020-10-20 06:37:38 Etc/GMT',
    expires_date_ms: expiredExpirationDate,
    expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
    web_order_line_item_id: 410000303240049,
    is_trial_period: 'false',
    is_in_intro_offer_period: 'true',
    in_app_ownership_type: 'PURCHASED',
    subscription_group_identifier: '20679156',
  }],
  service: 'apple',
};

const secondAppleReceipt = {
  receipt: {
    bundle_id: 'enterprises.dating.boo',
    in_app: [{
      quantity: 1,
      product_id: 'boo_infinity_1_month',
      transaction_id: 'renewal_transaction_id',
      original_transaction_id: 'second_subscription_id',
      purchase_date: '2020-09-20 06:37:38 Etc/GMT',
      purchase_date_ms: validExpirationDate,
      purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
      original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
      original_purchase_date_ms: now,
      original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
      expires_date: '2020-10-20 06:37:38 Etc/GMT',
      expires_date_ms: renewedExpirationDate,
      expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
      web_order_line_item_id: 410000303240049,
      is_trial_period: 'false',
      is_in_intro_offer_period: 'true',
      in_app_ownership_type: 'PURCHASED',
      subscription_group_identifier: '20679156',
    }],
  },
  latest_receipt_info: [{
    quantity: 1,
    product_id: 'boo_infinity_1_month',
    transaction_id: 'renewal_transaction_id',
    original_transaction_id: 'second_subscription_id',
    purchase_date: '2020-09-20 06:37:38 Etc/GMT',
    purchase_date_ms: validExpirationDate,
    purchase_date_pst: '2020-09-19 23:37:38 America/Los_Angeles',
    original_purchase_date: '2020-09-20 06:37:40 Etc/GMT',
    original_purchase_date_ms: now,
    original_purchase_date_pst: '2020-09-19 23:37:40 America/Los_Angeles',
    expires_date: '2020-10-20 06:37:38 Etc/GMT',
    expires_date_ms: renewedExpirationDate,
    expires_date_pst: '2020-10-19 23:37:38 America/Los_Angeles',
    web_order_line_item_id: 410000303240049,
    is_trial_period: 'false',
    is_in_intro_offer_period: 'true',
    in_app_ownership_type: 'PURCHASED',
    subscription_group_identifier: '20679156',
  }],
  service: 'apple',
};

// valid/expired x google/apple x promo/none

it('coin bonus', async () => {
  const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
  const testdata = [
    ['boo_infinity_1_month', 1000],
    ['boo_infinity_1_month_discount_50', 1000],
    ['boo_infinity_3_months', 3000],
    ['boo_infinity_3_months_discount_50', 3000],
    ['boo_infinity_12_months', 12000],
    ['boo_infinity_12_months_discount_50', 12000],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    receipt.productId = data[0];

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(initialCoins + data[1]);

    await mongoose.connection.db.dropDatabase();
  }

  // 1.11.53 - 1k coins for all products
  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    receipt.productId = data[0];

    var res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const { coins } = res.body;

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(coins + 1000);

    await mongoose.connection.db.dropDatabase();
  }

  // 1.11.58 - no coins for all products
  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    receipt.productId = data[0];

    var res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.58' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const { coins } = res.body;

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(coins);

    await mongoose.connection.db.dropDatabase();
  }
});

describe('signup country', async () => {
  it('correct', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'America/Los_Angeles' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.signupCountry).to.equal('United States');
    expect(user.googlePlayCountry).to.equal('United States');
    expect(user.signupCountryIncorrect).to.equal();
  });

  it('incorrect', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.signupCountry).to.equal('Indonesia');
    expect(user.googlePlayCountry).to.equal('United States');
    expect(user.signupCountryIncorrect).to.equal(true);
  });

  it('signup country should not change after being set', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'America/Los_Angeles' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.signupCountry).to.equal('Indonesia');
  });
});

it('lifetime purchase', async () => {
  const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
  receipt.productId = 'boo_infinity_lifetime';

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.premium).to.equal(true);

  user = await User.findById(0);
  expect(moment(user.premiumExpiration).diff(moment(), 'years')).to.equal(99);
});

it('app revenue metrics', async () => {
  const receipt = JSON.parse(JSON.stringify(validAppleReceipt));
  const renewed = JSON.parse(JSON.stringify(renewedAppleReceipt));
  const testdata = [
    ['boo_infinity_1_month', 20, 20],
    ['boo_infinity_1_month_discount_50', 10, 20],
    ['boo_infinity_3_months', 40, 40],
    ['boo_infinity_3_months_discount_50', 20, 40],
    ['boo_infinity_3_months_1_week_free_trial', 0, 40],
    ['boo_infinity_12_months', 130, 130],
    ['boo_infinity_12_months_discount_50', 65, 130],
    ['boo_infinity_1_month_variant', 30, 30],
    ['boo_infinity_1_month_discount_50_variant', 15, 30],
    ['boo_infinity_3_months_variant', 60, 60],
    ['boo_infinity_3_months_discount_50_variant', 30, 60],
    ['boo_infinity_12_months_variant', 195, 195],
    ['boo_infinity_12_months_discount_50_variant', 97.5, 195],
    ['boo_infinity_1_month_variant_1', 10, 10],
    ['boo_infinity_1_month_discount_50_variant_1', 5, 10],
    ['boo_infinity_3_months_variant_1', 20, 20],
    ['boo_infinity_3_months_discount_50_variant_1', 10, 20],
    ['boo_infinity_12_months_variant_1', 65, 65],
    ['boo_infinity_12_months_discount_50_variant_1', 32.5, 65],
    ['boo_infinity_6_months', 70, 70],
    ['boo_infinity_6_months_discount_50', 35, 70],
    ['boo_infinity_6_months_variant_1', 35, 35],
    ['boo_infinity_6_months_discount_50_variant_1', 17.5, 35],
    ['boo_infinity_6_months_v2', 80, 80],
    ['boo_infinity_6_months_discount_50_v2', 40, 80],
    ['boo_infinity_6_months_v2_variant_1', 40, 40],
    ['boo_infinity_6_months_discount_50_v2_variant_1', 20, 40],
    ['boo_infinity_1_week', 10, 10],
    ['boo_infinity_1_week_variant', 15, 15],
    ['boo_infinity_1_week_variant_1', 5, 5],
    ['boo_infinity_lifetime', 200, 200],
    ['boo_infinity_lifetime_variant_1', 100, 100],
    ['unlimited_likes_1_month', 15, 15],
    ['unlimited_likes_1_month_variant_1', 7.5, 7.5],
    ['unlimited_dms_1_month', 15, 15],
    ['unlimited_dms_1_month_variant_1', 7.5, 7.5],

    ['infinity_w1_v1', 10, 10],
    ['infinity_m1_v3', 20, 20],
    ['infinity_m1_v3_x1', 30, 30],
    ['infinity_m1_v3_x2', 10, 10],
    ['infinity_m3_v3', 40, 40],
    ['infinity_m3_v3_x1', 60, 60],
    ['infinity_m3_v3_x2', 20, 20],
    ['infinity_m6_v3', 70, 70],
    ['infinity_m6_v3_x1', 105, 105],
    ['infinity_m6_v3_x2', 35, 35],
    ['infinity_m12_v3', 130, 130],
    ['infinity_m12_v3_x1', 195, 195],
    ['infinity_m12_v3_x2', 65, 65],
    ['infinity_m3_d50_v3', 20, 40],
    ['infinity_m3_d50_v3_x1', 30, 60],
    ['infinity_m3_d50_v3_x2', 10, 20],
    ['infinity_m6_d50_v3', 35, 70],
    ['infinity_m6_d50_v3_x1', 52.5, 105],
    ['infinity_m6_d50_v3_x2', 17.5, 35],
    ['infinity_m12_d50_v3', 65, 130],
    ['infinity_m12_d50_v3_x1', 97.5, 195],
    ['infinity_m12_d50_v3_x2', 32.5, 65],
    ['infinity_lifetime_v3', 200, 200],

    ['Infinity_m1_v3_x2', 10, 10],
    ['Infinity_m3_v3_x2', 20, 20],
    ['Infinity_m6_v3_x2', 35, 35],
    ['Infinity_m12_v3_x2', 65, 65],
    ['Infinity_m3_v3_x2_d50', 10, 20],
    ['Infinity_m6_v3_x2_d50', 17.5, 35],
    ['Infinity_m12_v3_x2_d50', 32.5, 65],
  ];
  const countrydata = [
    [null, undefined, 1, null],
    [[-157.85, 21.30], undefined, 1, 'United States'],
    [null, 'Antarctica/Davis', 1, 'Antarctica'],
    [null, 'Africa/Casablanca', 0.05, 'Morocco'],
  ];

  for (let i = 0; i < testdata.length; i++) {
    for (let j = 0; j < countrydata.length; j++) {
      await User.ensureIndexes();
      const coordinates = countrydata[j][0];
      const timezone = countrydata[j][1];
      const multiplier = countrydata[j][2];
      const country = countrydata[j][3];

      const data = testdata[i];
      receipt.receipt.in_app[0].product_id = data[0];
      renewed.receipt.in_app[0].product_id = data[0];
      const initialRevenue = data[1] * multiplier;
      const renewalRevenue = data[2] * multiplier;
      let purchaseReceipt;

      let res = await request(app)
        .get('/v1/user')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      if (coordinates) {
        res = await request(app)
          .put('/v1/user/location')
          .set('authorization', 0)
          .send({
            longitude: coordinates[0],
            latitude: coordinates[1],
          });
        expect(res.status).to.equal(200);
      }
      if (timezone) {
        res = await request(app)
          .put('/v1/user/deviceInfo')
          .set('authorization', 0)
          .send({
            timezone,
          });
        expect(res.status).to.equal(200);
      }

      user = await User.findById(0);
      expect(user.metrics.madePurchase).to.equal();
      expect(user.metrics.numPurchases).to.equal(0);
      expect(user.metrics.numRenewals).to.equal(0);
      expect(user.metrics.revenue).to.equal(0);
      expect(user.currentDayMetrics.numPurchases).to.equal(0);
      expect(user.currentDayMetrics.revenue).to.equal(0);

      res = await request(app)
        .put('/v1/user/purchasePremium')
        .set('authorization', 0)
        .send({
          receipt,
        });
      expect(res.status).to.equal(200);

      user = await User.findById(0);
      expect(user.metrics.numRenewals).to.equal(0);
      if (data[0] == 'boo_infinity_3_months_1_week_free_trial') {
        expect(user.metrics.madePurchase).to.equal();
        expect(user.metrics.numPurchases).to.equal(0);
        expect(user.metrics.revenue).to.equal(0);
        expect(user.currentDayMetrics.numPurchases).to.equal(0);
        expect(user.currentDayMetrics.revenue).to.equal(0);
      } else {
        expect(user.metrics.madePurchase).to.equal(true);
        expect(user.metrics.numPurchases).to.equal(1);
        expect(user.metrics.revenue).to.equal(initialRevenue);
        expect(user.currentDayMetrics.numPurchases).to.equal(1);
        expect(user.currentDayMetrics.revenue).to.equal(initialRevenue);
      }

      purchaseReceipt = await PurchaseReceipt.findOne().sort('-createdAt');
      expect(purchaseReceipt.renewalNumber).to.equal(0);
      expect(purchaseReceipt.country).to.equal(country);
      expect(purchaseReceipt.timezone).to.equal(timezone);
      expect(purchaseReceipt.revenue).to.equal(initialRevenue);

      res = await request(app)
        .put('/v1/user/purchasePremium')
        .set('authorization', 0)
        .send({
          receipt: renewed,
        });
      expect(res.status).to.equal(200);

      user = await User.findById(0);
      expect(user.metrics.madePurchase).to.equal(true);
      expect(user.metrics.numRenewals).to.equal(1);
      if (data[0] == 'boo_infinity_3_months_1_week_free_trial') {
        expect(user.metrics.numPurchases).to.equal(1);
        expect(user.currentDayMetrics.numPurchases).to.equal(1);
      } else {
        expect(user.metrics.numPurchases).to.equal(2);
        expect(user.currentDayMetrics.numPurchases).to.equal(2);
      }
      expect(user.metrics.revenue).to.equal(initialRevenue + renewalRevenue);
      expect(user.currentDayMetrics.revenue).to.equal(initialRevenue + renewalRevenue);

      if (initialRevenue > 0 && renewalRevenue > initialRevenue) {
        expect(user.metrics.numSalePurchases).to.equal(1);
        expect(user.metrics.saleRevenue).to.equal(initialRevenue);
      } else {
        expect(user.metrics.numSalePurchases).to.equal(0);
        expect(user.metrics.saleRevenue).to.equal(0);
      }

      purchaseReceipt = await PurchaseReceipt.findOne().sort('-createdAt');
      expect(purchaseReceipt.renewalNumber).to.equal(1);
      expect(purchaseReceipt.country).to.equal(country);
      expect(purchaseReceipt.timezone).to.equal(timezone);
      expect(purchaseReceipt.revenue).to.equal(renewalRevenue);

      await mongoose.connection.db.dropDatabase();
    }
  }
});

describe('Premium purchase', () => {
  const uid = '0';

  beforeEach(async () => {
    const res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    await request(app)
      .put('/v1/user/personality')
      .set('authorization', uid)
      .send({
        mbti: 'ESTJ',
      });

    expect(res.body.premium).to.equal(false);
    expect(res.body.preferences.distance).to.equal(12500);

    const promoCode = new PromoCode({
      promoCode: 'bob50',
      discount: 50,
    });
    await promoCode.save();

    restoreAxiosStub()
  });

  it('expired, google', async () => {
    // change to android
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'android' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: expiredGoogleReceipt,
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(false);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt).to.equal(null);

    docs = await PurchasePremiumResponse.find();
    expect(docs.length).to.equal(0);
  });

  it('expired, apple', async () => {
    // change to ios
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: expiredAppleReceipt,
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      purchaseData: null,
    });

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(false);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt).to.equal(null);

    docs = await PurchasePremiumResponse.find();
    expect(docs.length).to.equal(1);
    expect(docs[0].user).to.equal(uid);
    expect(docs[0].currency).to.equal();
    expect(docs[0].price).to.equal();
    expect(docs[0].revenue).to.equal();
    expect(docs[0].productId).to.equal();
  });

  it('valid, google, invalid promo code', async () => {
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', uid)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'invalid' });
    expect(res.body.valid).to.equal(false);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.promoCode).to.equal(undefined);
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
    expect(purchaseReceipt.country).to.equal('United States');
  });

  it('valid, apple, invalid promo code', async () => {
    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'invalid' });
    expect(res.body.valid).to.equal(false);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.promoCode).to.equal(undefined);
    expect(purchaseReceipt.service).to.equal('apple');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(appleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(appleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
  });

  it('apple with price and currency', async () => {
    // change to ios
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      purchaseData: {
        productId: 'boo_infinity_1_month',
        price: 1000,
        currency: 'JPY',
      }
    });

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.revenue).to.equal(10);
    expect(purchaseReceipt.price).to.equal(1000);
    expect(purchaseReceipt.currency).to.equal('JPY');
    expect(purchaseReceipt.country).to.equal(null);
    expect(purchaseReceipt.isFraudulent).to.equal();

    docs = await PurchasePremiumResponse.find();
    expect(docs.length).to.equal(1);
    expect(docs[0].user).to.equal(uid);
    expect(docs[0].currency).to.equal('JPY');
    expect(docs[0].price).to.equal(1000);
    expect(docs[0].revenue).to.equal(10);
    expect(docs[0].productId).to.equal('boo_infinity_1_month');
  });

  it('price and currency and country from app store connect', async () => {
    // change to ios
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios' });
    expect(res.status).to.equal(200);

    appStoreConnectLib.getTransaction.restore();
    sinon.stub(appStoreConnectLib, 'getTransaction').callsFake((originalTransactionId, transactionId) => {
      console.log('Fake appStoreConnectLib.getTransaction override', originalTransactionId, transactionId);
      return {
        price: 500,
        currency: 'JOD',
        storefront: 'USA',
      };
    });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      purchaseData: {
        productId: 'boo_infinity_1_month',
        price: 500,
        currency: 'JOD',
      }
    });

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.revenue).to.equal(1000);
    expect(purchaseReceipt.price).to.equal(500);
    expect(purchaseReceipt.currency).to.equal('JOD');
    expect(purchaseReceipt.country).to.equal('United States');
    expect(purchaseReceipt.isFraudulent).to.equal();

    user = await User.findOne({ _id: uid.toString() });
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedBy).to.equal();
    expect(user.bannedReason).to.equal();
    expect(user.bannedNotes).to.equal();

    docs = await PurchasePremiumResponse.find();
    expect(docs.length).to.equal(1);
    expect(docs[0].user).to.equal(uid);
    expect(docs[0].currency).to.equal('JOD');
    expect(docs[0].price).to.equal(500);
    expect(docs[0].revenue).to.equal(1000);
    expect(docs[0].productId).to.equal('boo_infinity_1_month');
  });

  it('apple with invalid bundle id', async () => {
    // change to ios
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios' });
    expect(res.status).to.equal(200);

    const receipt = JSON.parse(JSON.stringify(validAppleReceipt));
    receipt.receipt.bundle_id = 'com.mutyrgef.faxjp';

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: receipt,
        price: 1000,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      purchaseData: null,
    });

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.revenue).to.equal(0);
    expect(purchaseReceipt.price).to.equal(1000);
    expect(purchaseReceipt.currency).to.equal('JPY');
    expect(purchaseReceipt.country).to.equal(null);
    expect(purchaseReceipt.isFraudulent).to.equal(true);

    user = await User.findOne({ _id: uid.toString() });
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal(null);
    expect(user.bannedReason).to.equal('fraudulent purchase with invalid bundleId');
    expect(user.bannedNotes).to.equal('com.mutyrgef.faxjp');

    docs = await PurchasePremiumResponse.find();
    expect(docs.length).to.equal(1);
    expect(docs[0].user).to.equal(uid);
    expect(docs[0].currency).to.equal();
    expect(docs[0].price).to.equal();
    expect(docs[0].revenue).to.equal();
    expect(docs[0].productId).to.equal();
  });

  it('valid, google, valid promo code', async () => {
    // change to android
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'android' });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'bob50' });
    expect(res.body.valid).to.equal(true);
    expect(res.body.discount).to.equal(50);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.promoCode).to.equal('bob50');
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
    expect(purchaseReceipt.price).to.equal(19.99);
    expect(purchaseReceipt.currency).to.equal('USD');
  });

  it('valid, apple, valid promo code', async () => {
    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'bob50' });
    expect(res.body.valid).to.equal(true);
    expect(res.body.discount).to.equal(50);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.promoCode).to.equal('bob50');
    expect(purchaseReceipt.service).to.equal('apple');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(appleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(appleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
  });

  it('purchasedFrom', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
        purchasedFrom: 'teleport',
      });
    expect(res.status).to.equal(200);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.purchasedFrom).to.equal('teleport');

    expect((await User.findById('0')).metrics.purchasedPremiumFrom).to.equal('teleport');
  });

  it('apple subscription renewal', async () => {
    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'bob50' });
    expect(res.body.valid).to.equal(true);
    expect(res.body.discount).to.equal(50);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    // renew
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: renewedAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid });
    expect(purchaseReceipts.length).to.equal(2);

    purchaseReceipts = await PurchaseReceipt.find({ subscriptionId: appleSubscriptionId }, null, { sort: { purchaseDate: 1 } });
    expect(purchaseReceipts.length).to.equal(2);

    expect(purchaseReceipts[0].renewalNumber).to.equal(0);
    expect(purchaseReceipts[1].renewalNumber).to.equal(1);

    expect(purchaseReceipts[0].promoCode).to.equal('bob50');
    expect(purchaseReceipts[1].promoCode).to.equal('bob50');

    purchaseReceipts = await PurchaseReceipt.aggregate([
      { $sort: { _id: 1 } },
      {
        $group:
        {
          _id: '$subscriptionId',
          promoCode: { $first: '$promoCode' },
          numPayments: { $sum: 1 },
        },
      },
    ]);
    expect(purchaseReceipts.length).to.equal(1);
    expect(purchaseReceipts[0]._id).to.equal(appleSubscriptionId);
    expect(purchaseReceipts[0].promoCode).to.equal('bob50');
    expect(purchaseReceipts[0].numPayments).to.equal(2);
  });

  it('two separate subscriptions (not renew)', async () => {
    var res = await request(app)
      .put('/v1/user/usePromoCode')
      .set('authorization', uid)
      .send({ promoCode: 'bob50' });
    expect(res.body.valid).to.equal(true);
    expect(res.body.discount).to.equal(50);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    // separate subscription
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: secondAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid }, null, { sort: { purchaseDate: 1 } });
    expect(purchaseReceipts.length).to.equal(2);

    firstPurchase = purchaseReceipts[0];
    secondPurchase = purchaseReceipts[1];
    expect(firstPurchase.subscriptionId).to.equal(appleSubscriptionId);
    expect(firstPurchase.promoCode).to.equal('bob50');
    expect(firstPurchase.renewalNumber).to.equal(0);
    expect(secondPurchase.subscriptionId).to.equal('second_subscription_id');
    expect(secondPurchase.promoCode).to.equal(undefined);
    expect(firstPurchase.renewalNumber).to.equal(0);
  });

  it('duplicate receipts (same user)', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid });
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    expect(purchaseReceipt.promoCode).to.equal(undefined);
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
  });

  it('duplicate receipts - google (different users)', async () => {
    // create second user
    uid2 = '2';
    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid2);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', uid2)
      .send({
        mbti: 'ESTJ',
      });

    // first user should become premium after using receipt
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);

    // second user should not become premium after using the same receipt
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid2)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid2);
    expect(res.body.premium).to.equal(false);

    // there should only be one receipt stored
    purchaseReceipts = await PurchaseReceipt.find({});
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    expect(purchaseReceipt.promoCode).to.equal(undefined);
    expect(purchaseReceipt.user).to.equal(uid);
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
  });

  it('duplicate receipts - apple (different users)', async () => {
    // create second user
    uid2 = '2';
    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid2);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', uid2)
      .send({
        mbti: 'ESTJ',
      });

    // first user should become premium after using receipt
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);

    // second user should not become premium after using the same receipt
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid2)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid2);
    expect(res.body.premium).to.equal(false);

    // there should only be one receipt stored
    purchaseReceipts = await PurchaseReceipt.find({});
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    expect(purchaseReceipt.promoCode).to.equal(undefined);
    expect(purchaseReceipt.user).to.equal(uid);
    expect(purchaseReceipt.service).to.equal('apple');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(appleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(appleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
  });

  it('daysOnPlatformBeforePurchase', async () => {
    const user = await User.findOne({ _id: uid });
    user.createdAt = now - oneDayMs * 1.5;
    await user.save();

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(1);
  });

  it('numberOfMatchesBeforePurchase no matches', async () => {
    // create second user
    var res = await request(app)
      .get('/v1/user')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', '1')
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);

    // send a like
    var res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', uid)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfLikesSentBeforePurchase).to.equal(1);
  });

  it('numberOfMatchesBeforePurchase 1 match', async () => {
    // create second user
    var res = await request(app)
      .get('/v1/user')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', '1')
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);

    // send a like
    var res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', uid)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // send a like back
    var res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', '1')
      .send({ user: uid });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(1);
    expect(purchaseReceipt.numberOfLikesSentBeforePurchase).to.equal(1);
  });

  it('userMetrics and userEvents', async () => {
    // send like
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    var res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // send event
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ open_purchase_unlimitedLikes: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    const purchaseReceipt = await PurchaseReceipt.findOne({ user: uid });
    expect(purchaseReceipt.userMetrics.numLikesSent).to.equal(1);
    expect(purchaseReceipt.userEvents.open_purchase_unlimitedLikes).to.equal(1);
  });

  it('simultaneous receipts', (done) => {
    const parallelRuns = 10;
    let actualRuns = 0;
    const asyncTasks = [];

    for (i = 0; i < parallelRuns; i++) {
      asyncTasks.push((err, result) => {
        request(app)
          .put('/v1/user/purchasePremium')
          .set('authorization', 0)
          .send({
            receipt: validAppleReceipt,
          })
          .expect(200)
          .end((err, res) => {
            request(app)
              .get('/v1/coins')
              .set('authorization', 0)
              .expect(200)
              .end(async (err, res) => {
                actualRuns++;

                if (err) {
                  return done(err);
                }

                expect(res.body.coins).to.equal(initialCoins + 1000);

                if (actualRuns == parallelRuns) {
                  const purchaseReceipts = await PurchaseReceipt.find({});
                  expect(purchaseReceipts.length).to.equal(1);
                  done()
                }
              });
          });
      });
    }
    async.parallel(asyncTasks, done);
  });

  it('valid, google, canceled', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validButCanceledGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
    expect(res.body.premium).to.equal(true);
  });

  it('hacked google', async () => {
    // custom stub
    iap.validate.restore();
    sinon.stub(iap, 'validate')
      .callsFake((receipt, cb) => {
        cb(new Error(`Status:${400}`), { status: 1 });
      });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validButCanceledGoogleReceipt, // doesn't matter
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
    expect(res.body.premium).to.equal(false);

    // restore prior stubs
    stub.createStubs();
  });

  it('hacked apple', async () => {
    iap.validate.restore();
    sinon.stub(iap, 'validate')
      .callsFake((receipt, cb) => {
        cb(new Error('failed to validate'), { status: 2 });
      });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validButCanceledGoogleReceipt,
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
    expect(res.body.premium).to.equal(false);

    stub.createStubs();
  });

  it('google receipt from elsewhere', async () => {
    // custom stub
    iap.validate.restore();
    sinon.stub(iap, 'validate')
      .callsFake((receipt, cb) => {
        cb(new Error(`Status:${403}`), { status: 1 });
      });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validButCanceledGoogleReceipt, // doesn't matter
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
    expect(res.body.premium).to.equal(false);
  });

  it('google rtdn initial purchase', async () => {
    sinon.stub(iap, 'validateOnce')
      .callsFake((receipt, credentials, cb) => {
        cb(null, validGoogleReceipt);
      });

    sinon.stub(axios, 'request').resolves({data: {obfuscatedExternalAccountId: '0'} });

    res = await request(app)
      .post('/google-renewal')
      .send(googleInitialPurchaseRtdnBody);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    purchaseReceipts = await PurchaseReceipt.find();
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    console.log(purchaseReceipt);
    expect(purchaseReceipt.user).to.equal('0');
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
    expect(purchaseReceipt.price).to.equal(19.99);
    expect(purchaseReceipt.currency).to.equal('USD');
    expect(purchaseReceipt.purchasedFrom).to.equal();

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.purchasedPremiumFrom).to.equal();

    // insert purchasedFrom
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
        purchasedFrom: 'teleport',
      });
    expect(res.status).to.equal(200);

    purchaseReceipts = await PurchaseReceipt.find();
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    console.log(purchaseReceipt);
    expect(purchaseReceipt.user).to.equal('0');
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('boo_infinity_1_month');
    expect(purchaseReceipt.purchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.expirationDate.getTime()).to.equal(validExpirationDate);
    expect(purchaseReceipt.transactionId).to.equal(googleTransactionId);
    expect(purchaseReceipt.subscriptionId).to.equal(googleSubscriptionId);
    expect(purchaseReceipt.originalPurchaseDate.getTime()).to.equal(now);
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
    expect(purchaseReceipt.renewalNumber).to.equal(0);
    expect(purchaseReceipt.price).to.equal(19.99);
    expect(purchaseReceipt.currency).to.equal('USD');
    expect(purchaseReceipt.purchasedFrom).to.equal('teleport');

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.purchasedPremiumFrom).to.equal('teleport');
  });

  it('google rtdn coin purchase', async () => {
    const coinReceipt = {
      orderId: 'GPA.**************-02809',
      packageName: 'enterprises.dating.boo',
      productId: '4000_coins_discount_30',
      purchaseTime: *************,
      purchaseState: 0,
      purchaseToken: 'fake',
      obfuscatedAccountId: '0',
      quantity: 1,
      acknowledged: false,
      status: 0,
      purchaseTimeMillis: *************,
      consumptionState: 1,
      developerPayload: NaN,
      purchaseType: 0,
      acknowledgementState: 1,
      kind: 'androidpublisher#productPurchase',
      obfuscatedExternalAccountId: '0',
      regionCode: 'US',
      service: 'google',
      transactionId: 'fake',
      purchaseDate: *************,
    };
    sinon.stub(iap, 'validateOnce')
      .callsFake((receipt, credentials, cb) => {
        cb(null, coinReceipt);
      });

    sinon.stub(axios, 'request').resolves({
      data: {
        obfuscatedExternalAccountId: '0',
        regionCode: 'US',
        prices: {
          US: {
            priceMicros: 1000000,
            currency: 'USD',
          },
        },
      }
    });

    res = await request(app)
      .post('/google-renewal')
      .send(googleCoinPurchaseNotificationBody);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    purchaseReceipts = await CoinPurchaseReceipt.find();
    expect(purchaseReceipts.length).to.equal(1);
    purchaseReceipt = purchaseReceipts[0];
    console.log(purchaseReceipt);
    expect(purchaseReceipt.user).to.equal('0');
    expect(purchaseReceipt.service).to.equal('google');
    expect(purchaseReceipt.productId).to.equal('4000_coins_discount_30');
    expect(purchaseReceipt.price).to.equal(1);
    expect(purchaseReceipt.currency).to.equal('USD');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.be.greaterThan(4000);
  });

  it('google renewal', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());

    sinon.stub(iap, 'validateOnce')
      .callsFake((receipt, credentials, cb) => {
        cb(null, renewedGoogleReceipt);
      });

    // coin purchase
    sinon.stub(axios, 'request').resolves({data: {obfuscatedExternalAccountId: '0'} });
    res = await request(app)
      .post('/google-renewal')
      .send(googleCoinPurchaseNotificationBody);
    expect(res.status).to.equal(200);

    // renew
    res = await request(app)
      .post('/google-renewal')
      .send(googleRenewalRtdnBody);
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid });
    expect(purchaseReceipts.length).to.equal(2);

    purchaseReceipts = await PurchaseReceipt.find({ subscriptionId: googleSubscriptionId }, null, { sort: { purchaseDate: 1 } });
    expect(purchaseReceipts.length).to.equal(2);

    expect(purchaseReceipts[0].renewalNumber).to.equal(0);
    expect(purchaseReceipts[1].renewalNumber).to.equal(1);
    expect(purchaseReceipts[1].purchaseDate).to.not.equal();

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(renewedExpirationDate).getTime());

    iap.validateOnce.restore();
  });

  it('google revoke', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());

    // revoke
    res = await request(app)
      .post('/google-renewal')
      .send(googleRevokeRtdnBody);
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(false);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid }).sort({purchaseDate:1});
    expect(purchaseReceipts.length).to.equal(2);
    expect(purchaseReceipts[0].revokedAt).to.not.equal();
    expect(purchaseReceipts[1].isRefund).to.equal(true);
    expect(purchaseReceipts[1].revenue).to.equal(-1 * purchaseReceipts[0].revenue);
    expect(purchaseReceipts[1].country).to.equal(purchaseReceipts[0].country);

    user = await User.findById(uid);
    expect(user.metrics.numRefunds).to.equal(1);
    expect(user.metrics.revenue).to.equal(0);
    expect(user.metrics.revenueRefunded).to.equal(purchaseReceipts[0].revenue);
  });

  it('google cancel', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());

    // cancel
    res = await request(app)
      .post('/google-renewal')
      .send(googleCancelRtdnBody);
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid }).sort({purchaseDate:1});
    expect(purchaseReceipts.length).to.equal(1);
    expect(purchaseReceipts[0].cancelledAt).to.not.equal();

    user = await User.findById(uid);
    expect(user.metrics.numSubscriptionCancellations).to.equal(1);
  });

  it('google hold', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', uid)
      .send({ fcmToken: 'token' });
    expect(res.status).to.equal(200);

    // hold
    res = await request(app)
      .post('/google-renewal')
      .send(googleHoldRtdnBody);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token');
    expect(notifs.recent.notification.title).to.equal('');
    expect(notifs.recent.notification.body).to.equal("There are issues with your billing method. Please let us know if you're facing any issues and how we can help!");
    expect(notifs.recent.data).to.eql({
      messageSupport: JSON.stringify({}),
    });
  });

  /*
  it('ios renewal', async () => {
    // stub not working
    sinon.stub(appStoreApi, 'decodeNotificationPayload')
      .callsFake((body) => {
        console.log('fake decodeNotificationPayload', body);
        const impl = function (resolve, reject) {
          resolve(body);
        };
        return new Promise(impl);
      });
    sinon.stub(appStoreApi, 'decodeRenewalInfo')
      .callsFake((body) => {
        const impl = function (resolve, reject) {
          resolve(body);
        };
        return new Promise(impl);
      });
    sinon.stub(appStoreApi, 'decodeTransaction')
      .callsFake((body) => {
        const impl = function (resolve, reject) {
          resolve(body);
        };
        return new Promise(impl);
      });

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', uid)
      .send({
        receipt: validAppleReceipt,
      });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());

    // renew
    res = await request(app)
      .post('/ios-renewal')
      .send(appleRenewalRtdnBody);
    expect(res.status).to.equal(200);

    var res = await request(app)
      .get('/v1/user')
      .set('authorization', uid);
    expect(res.body.premium).to.equal(true);
    expect(res.body.preferences.distance).to.equal(12500);

    purchaseReceipts = await PurchaseReceipt.find({ user: uid });
    expect(purchaseReceipts.length).to.equal(2);

    purchaseReceipts = await PurchaseReceipt.find({ subscriptionId: appleSubscriptionId }, null, { sort: { purchaseDate: 1 } });
    expect(purchaseReceipts.length).to.equal(2);

    expect(purchaseReceipts[0].renewalNumber).to.equal(0);
    expect(purchaseReceipts[1].renewalNumber).to.equal(1);
    expect(purchaseReceipts[1].purchaseDate).to.not.equal();

    user = await User.findById(uid);
    expect(user.premiumExpiration.getTime()).to.equal(new Date(renewedExpirationDate).getTime());
  });
  */

  it('social proof', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/social-proof')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(0);

    res = await request(app)
      .get('/web/cached/social-proof')
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(0);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    const pictureId = res.body.pictures[0];

    res = await request(app)
      .get('/v1/social-proof')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(1);
    expect(res.body.images[0]).to.equal(pictureId);

    res = await request(app)
      .get('/web/cached/social-proof')
    expect(res.status).to.equal(200);
    expect(res.body.images.length).to.equal(1);
    expect(res.body.images[0]).to.equal(pictureId);
  });
});

describe('StickerPack Purchase', () => {
  const purchaseTime = Date.now();
  const premiumStickerPackIds = [
    getMockStickerPackId(1),
    getMockStickerPackId(3),
    getMockStickerPackId(5),
  ];
  beforeEach(async () => {
    // create user 0
    await initApp(0);
  });

  async function purchaseStickerPack(userId, receipt) {
    return await request(app)
      .put('/v1/sticker-pack/purchase')
      .set('authorization', userId)
      .send({ receipt });
  }

  it('purchase stickerPack', async () => {
    // Invalid Receipts
    res = await purchaseStickerPack(0, IAP_ERROR_RECEIPTS.HACK_GOOGLE);
    expect(res.status).to.equal(422);
    res = await purchaseStickerPack(0, IAP_ERROR_RECEIPTS.HACK_APPLE);
    expect(res.status).to.equal(422);
    res = await purchaseStickerPack(0, IAP_ERROR_RECEIPTS.ELSEWHERE_GOOGLE);
    expect(res.status).to.equal(422);

    // key is not sent for client versions earlier than 1.11.45
    let userData = (await initApp(0)).user;
    expect(userData.stickerPackPurchases).to.equal(undefined);

    userData = await getMyProfile(0);
    expect(userData.stickerPackPurchases).to.equal(undefined);

    // upgrading to 1.11.45, key starts showing
    const ownedStickerPacks = [];
    userData = (await initApp(0, { appVersion: '1.11.45' })).user;
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);

    userData = await getMyProfile(0);
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);

    // adding using googleReceipt stickerPack 1
    let currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[0], purchaseTime);
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);

    // stickerPackIsAddedToUser
    ownedStickerPacks.push(premiumStickerPackIds[0]);
    userData = (await initApp(0)).user;
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);
    userData = await getMyProfile(0);
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);

    const savedReceipts = [];
    {
      const receipts = await StickerPackPurchaseReceipt.find().sort({ _id: 1 });
      expect(receipts.length).to.equal(1);
      const receiptData = receipts[0];
      savedReceipts.push(receiptData.toString());
      expect(receiptData.user).to.equal('0');
      expect(receiptData.service).to.equal('google');
      expect(receiptData.productId).to.equal(premiumStickerPackIds[0]);
      expect(receiptData.purchaseDate.getTime()).to.equal(purchaseTime);
      expect(receiptData.transactionId).to.equal(currentReceipt.orderId);
      expect(receiptData.revenue).to.equal(1);
      expect(receiptData.daysOnPlatformBeforePurchase).to.equal(0);
      expect(receiptData.numberOfMatchesBeforePurchase).to.equal(0);
      expect(receiptData.numberOfLikesSentBeforePurchase).to.equal(0);
    }

    // adding using same receipt again(does not throw error)
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);
    // no new receipt created
    {
      const receipts = await StickerPackPurchaseReceipt.find();
      expect(receipts.length).to.equal(1);
      expect(receipts[0].toString()).to.eql(savedReceipts[0]);
    }

    // create user 1
    await initApp(1, { appVersion: '1.11.45' });

    // adding using same receipt but different user(does not throw error)
    res = await purchaseStickerPack(1, currentReceipt);
    expect(res.status).to.equal(200);

    // no new receipt created
    {
      const receipts = await StickerPackPurchaseReceipt.find().sort({ _id: 1 });
      expect(receipts.length).to.equal(1);
      expect(receipts[0].toString()).to.eql(savedReceipts[0]);
    }

    // stickerPack Not Added To User
    userData = (await initApp(1)).user;
    expect(userData.stickerPackPurchases).to.eql([]);
    userData = await getMyProfile(1);
    expect(userData.stickerPackPurchases).to.eql([]);

    // adding using appleReceipt
    currentReceipt = iapHelper.getValidAppleReceipt(premiumStickerPackIds[0], purchaseTime);
    res = await purchaseStickerPack(1, currentReceipt);
    expect(res.status).to.equal(200);

    userData = (await initApp(1)).user;
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);
    userData = await getMyProfile(1);
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);

    {
      const receipts = await StickerPackPurchaseReceipt.find().sort({ _id: 1 });
      expect(receipts.length).to.equal(2);
      expect(receipts[0].toString()).to.eql(savedReceipts[0]);
      const receiptData = receipts[1];
      savedReceipts.push(receiptData.toString());
      expect(receiptData.user).to.equal('1');
      expect(receiptData.service).to.equal('apple');
      expect(receiptData.productId).to.equal(premiumStickerPackIds[0]);
      expect(receiptData.purchaseDate.getTime()).to.equal(purchaseTime);
      expect(receiptData.transactionId).to.equal(iapHelper.appleTransactionId);
      expect(receiptData.revenue).to.equal(1);
      expect(receiptData.daysOnPlatformBeforePurchase).to.equal(0);
      expect(receiptData.numberOfMatchesBeforePurchase).to.equal(0);
      expect(receiptData.numberOfLikesSentBeforePurchase).to.equal(0);
    }

    // adding new stickerPack to user 0 using google receipt
    currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[1], purchaseTime, 'google_2');
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);

    ownedStickerPacks.push(premiumStickerPackIds[1]);
    {
      const receipts = await StickerPackPurchaseReceipt.find().sort({ _id: 1 });
      expect(receipts.length).to.equal(3);
      const receiptData = receipts[2];
      savedReceipts.push(receiptData.toString());
      expect(receiptData.user).to.equal('0');
      expect(receiptData.service).to.equal('google');
      expect(receiptData.productId).to.equal(premiumStickerPackIds[1]);
      expect(receiptData.purchaseDate.getTime()).to.equal(purchaseTime);
      expect(receiptData.transactionId).to.equal('google_2');
      expect(receiptData.revenue).to.equal(1);
      expect(receiptData.daysOnPlatformBeforePurchase).to.equal(0);
      expect(receiptData.numberOfMatchesBeforePurchase).to.equal(0);
      expect(receiptData.numberOfLikesSentBeforePurchase).to.equal(0);
    }

    userData = (await initApp(0)).user;
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);
    userData = await getMyProfile(0);
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);

    // adding new stickerPack to user 1 using google receipt
    currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[1], purchaseTime, 'google_3');
    res = await purchaseStickerPack(1, currentReceipt);
    expect(res.status).to.equal(200);

    {
      const receipts = await StickerPackPurchaseReceipt.find().sort({ _id: 1 });
      expect(receipts.length).to.equal(4);
      const receiptData = receipts[3];
      savedReceipts.push(receiptData.toString());
      expect(receiptData.user).to.equal('1');
      expect(receiptData.service).to.equal('google');
      expect(receiptData.productId).to.equal(premiumStickerPackIds[1]);
      expect(receiptData.purchaseDate.getTime()).to.equal(purchaseTime);
      expect(receiptData.transactionId).to.equal('google_3');
      expect(receiptData.revenue).to.equal(1);
      expect(receiptData.daysOnPlatformBeforePurchase).to.equal(0);
      expect(receiptData.numberOfMatchesBeforePurchase).to.equal(0);
      expect(receiptData.numberOfLikesSentBeforePurchase).to.equal(0);
    }

    userData = (await initApp(1)).user;
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);
    userData = await getMyProfile(1);
    expect(userData.stickerPackPurchases).to.eql(ownedStickerPacks);
  });

  const getUserMetrics = async () => {
    const userData = await User.findOne(
      { _id: 0 },
      [
        'metrics.revenue',
        'metrics.stickerPackRevenue',
        'metrics.numStickerPackPurchases',
        'currentDayMetrics.revenue',
      ],
    );
    return [
      userData.metrics.revenue || 0,
      userData.metrics.stickerPackRevenue || 0,
      userData.metrics.numStickerPackPurchases || 0,
      userData.currentDayMetrics.revenue || 0,
    ];
  };

  function updateMetrics(data, revenue) {
    data[0] += revenue;
    data[1] += revenue;
    data[2] += 1;
    data[3] += revenue;
  }
  it('metrics', async () => {
    const data = await getUserMetrics();
    // adding stickerPack using googleReceipt
    let currentReceipt = iapHelper.getValidAppleReceipt(premiumStickerPackIds[0], purchaseTime);
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);

    updateMetrics(data, 1);
    expect(await getUserMetrics()).to.eql(data);

    // adding again
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);

    // no change
    expect(await getUserMetrics()).to.eql(data);

    // when revenue changes
    currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[1], purchaseTime);
    await setDeviceInfo(0, { timezone: 'Africa/Casablanca' });
    res = await purchaseStickerPack(0, currentReceipt);
    expect(res.status).to.equal(200);

    updateMetrics(data, 0.05);
    expect(await getUserMetrics()).to.eql(data);
  });

  describe('receipt location and revenue', () => {
    async function lastReceiptData() {
      return await StickerPackPurchaseReceipt.findOne({}, ['country', 'timezone', 'revenue']).sort({ _id: -1 });
    }
    it('timezone', async () => {
      const timezone = 'Africa/Casablanca';
      await setDeviceInfo(0, { timezone });
      // adding using googleReceipt
      let currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[0], purchaseTime);
      res = await purchaseStickerPack(0, currentReceipt);
      expect(res.status).to.equal(200);

      let receipt = await lastReceiptData();
      // console.log(receipt);
      expect(receipt.country).to.equal('Morocco');
      expect(receipt.timezone).to.equal(timezone);
      expect(receipt.revenue).to.equal(0.05);

      // adding using googleReceipt
      currentReceipt = iapHelper.getValidAppleReceipt(premiumStickerPackIds[1], purchaseTime);
      res = await purchaseStickerPack(0, currentReceipt);
      expect(res.status).to.equal(200);
      receipt = await lastReceiptData();
      // console.log(receipt);
      expect(receipt.country).to.equal('Morocco');
      expect(receipt.timezone).to.equal(timezone);
      expect(receipt.revenue).to.equal(1);// no multiplier effect applied on apple receipts
    });
    it('coordinates', async () => {
      const coordinates = [-157.85, 21.30];
      await setLocation(0, coordinates);
      // adding using googleReceipt
      let currentReceipt = iapHelper.getValidGoogleReceipt(premiumStickerPackIds[0], purchaseTime);
      res = await purchaseStickerPack(0, currentReceipt);
      expect(res.status).to.equal(200);

      let receipt = await lastReceiptData();
      // console.log(receipt);
      expect(receipt.country).to.equal('United States');
      expect(receipt.timezone).to.equal(undefined);
      expect(receipt.revenue).to.equal(1);

      // adding using googleReceipt
      currentReceipt = iapHelper.getValidAppleReceipt(premiumStickerPackIds[1], purchaseTime);
      res = await purchaseStickerPack(0, currentReceipt);
      expect(res.status).to.equal(200);
      receipt = await lastReceiptData();
      // console.log(receipt);
      expect(receipt.country).to.equal('United States');
      expect(receipt.timezone).to.equal(undefined);
      expect(receipt.revenue).to.equal(1);// no multiplier effect applied on apple receipts
    });
  });
});

describe('google-revenue', () => {
  beforeEach(async () => {
    for (let i = 0; i < 5; i++) {
      await initApp(i);
    }
  });

  afterEach(async () => {
  });

  const receipts = [];
  {
    // default receipt
    let receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 0);
    receipts.push(receipt);

    // incomplete receipts
    receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
    receipt.priceAmountMicros = undefined;
    receipts.push(receipt);
    receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
    receipt.priceCurrencyCode = undefined;
    receipts.push(receipt);

    // with unknown currency data
    receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 3);
    receipt.priceCurrencyCode = 'XYZ';
    receipt.priceAmountMicros = 1000000;
    receipts.push(receipt);

    // complete with known currency data
    receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 4);
    receipt.priceCurrencyCode = 'ABC';
    receipt.priceAmountMicros = 12345;
    receipts.push(receipt);

    // complete with known currency data (renewal)
    receipt = iapHelper.getValidGoogleReceipt('', Date.now() + 24 * 3600 * 1000, 5);
    receipt.priceCurrencyCode = 'ABC';
    receipt.priceAmountMicros = 12345;
    receipts.push(receipt);
  }

  it('stickerPacks', async () => {
    async function purchaseStickerPack(userId, receipt) {
      const res = await request(app)
        .put('/v1/sticker-pack/purchase')
        .set('authorization', userId)
        .send({ receipt });
      expect(res.status).to.equal(200);
    }
    const revenues = [];
    for (let i = 0; i < 5; i++) {
      const receipt = receipts[i];
      receipt.productId = getMockStickerPackId(1);
      await purchaseStickerPack(i, receipts[i]);
      const savedReceipt = await StickerPackPurchaseReceipt.findOne({ transactionId: i }, { revenue: 1 });
      revenues.push(savedReceipt.revenue);
    }
    expect(revenues[0]).to.equal(revenues[1]);
    expect(revenues[0]).to.equal(revenues[2]);
    expect(revenues[0]).to.equal(revenues[3]);
    expect(revenues[4]).to.equal(12345 * 1.5 / 1000000);
    console.log(revenues);
  });

  const premiumPurchases = [
    ['boo_infinity_1_month', 1],
    ['boo_infinity_1_month_discount_50', 0.5],
    ['boo_infinity_3_months', 1],
    ['boo_infinity_3_months_discount_50', 0.5],
    ['boo_infinity_3_months_1_week_free_trial', 0],
    ['boo_infinity_12_months', 1],
    ['boo_infinity_12_months_discount_50', 0.5],
    ['boo_infinity_1_month_variant', 1],
    ['boo_infinity_1_month_discount_50_variant', 0.5],
    ['boo_infinity_3_months_variant', 1],
    ['boo_infinity_3_months_discount_50_variant', 0.5],
    ['boo_infinity_12_months_variant', 1],
    ['boo_infinity_12_months_discount_50_variant', 0.5],
    ['boo_infinity_1_month_variant_1', 1],
    ['boo_infinity_1_month_discount_50_variant_1', 0.5],
    ['boo_infinity_3_months_variant_1', 1],
    ['boo_infinity_3_months_discount_50_variant_1', 0.5],
    ['boo_infinity_12_months_variant_1', 1],
    ['boo_infinity_12_months_discount_50_variant_1', 0.5],
    ['boo_infinity_6_months', 1],
    ['boo_infinity_6_months_discount_50', 0.5],
    ['boo_infinity_6_months_variant_1', 1],
    ['boo_infinity_6_months_discount_50_variant_1', 0.5],
    ['boo_infinity_6_months_v2', 1],
    ['boo_infinity_6_months_discount_50_v2', 0.5],
    ['boo_infinity_6_months_v2_variant_1', 1],
    ['boo_infinity_6_months_discount_50_v2_variant_1', 0.5],
    ['boo_infinity_1_week', 1],
    ['boo_infinity_1_week_variant', 1],
    ['boo_infinity_1_week_variant_1', 1],
    ['boo_infinity_lifetime', 1],
    ['boo_infinity_lifetime_variant_1', 1],
    ['unlimited_likes_1_month', 1],
    ['unlimited_likes_1_month_variant_1', 1],
    ['unlimited_dms_1_month', 1],
    ['unlimited_dms_1_month_variant_1', 1],
  ];
  for (const testcase of premiumPurchases) {
    const productId = testcase[0];
    const discount = testcase[1];

    it(`premium purchase google revenue ${productId}`, async () => {
      async function makePremiumPurchase(userId, receipt) {
        const res = await request(app)
          .put('/v1/user/purchasePremium')
          .set('authorization', userId)
          .send({
            receipt,
          });
        expect(res.status).to.equal(200);
      }
      const revenues = [];
      for (let i = 0; i < 5; i++) {
        const receipt = receipts[i];
        receipt.productId = productId;
        await makePremiumPurchase(i, receipts[i]);
        const savedReceipt = await PurchaseReceipt.findOne({ transactionId: i }, { revenue: 1 });
        revenues.push(savedReceipt.revenue);
      }
      expect(revenues[0]).to.equal(revenues[1]);
      expect(revenues[0]).to.equal(revenues[2]);
      expect(revenues[0]).to.equal(revenues[3]);
      expect(revenues[4]).to.equal(discount * 12345 * 1.5 / 1000000);
      console.log(revenues);

      // test renewal
      const receipt = receipts[5];
      receipt.productId = productId;
      await makePremiumPurchase(4, receipts[5]);
      const savedReceipt = await PurchaseReceipt.findOne({ transactionId: 5 }, { revenue: 1 });
      revenues.push(savedReceipt.revenue);
      expect(revenues[5]).to.equal(12345 * 1.5 / 1000000);
    });
  }

  async function makePremiumPurchase(userId, receipt) {
    const res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', userId)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);
  }

  it('lifetime revenue based on 1 month', async () => {
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 10000000;
      receipt.productId = 'boo_infinity_1_month';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'boo_infinity_1_month' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10000000 * 1.5 / 1000000);
    }
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
      receipt.priceCurrencyCode = undefined;
      receipt.priceAmountMicros = undefined;
      receipt.productId = 'boo_infinity_lifetime';
      receipt.countryCode = undefined;
      receipt.regionCode = 'US';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'boo_infinity_lifetime' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10 * 10000000 * 1.5 / 1000000);
    }
  });

  it('lifetime revenue based on 3 month', async () => {
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 10000000;
      receipt.productId = 'boo_infinity_3_months_discount_50';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'boo_infinity_3_months_discount_50' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(0.5 * 10000000 * 1.5 / 1000000);
    }
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
      receipt.priceCurrencyCode = undefined;
      receipt.priceAmountMicros = undefined;
      receipt.productId = 'boo_infinity_lifetime';
      receipt.countryCode = undefined;
      receipt.regionCode = 'US';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'boo_infinity_lifetime' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10 * 0.5 * 10000000 * 1.5 / 1000000);
    }
  });

  it('lifetime revenue based on 1 month new naming', async () => {
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 10000000;
      receipt.productId = 'infinity_m1_v3';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'infinity_m1_v3' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10000000 * 1.5 / 1000000);
    }
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
      receipt.priceCurrencyCode = undefined;
      receipt.priceAmountMicros = undefined;
      receipt.productId = 'infinity_lifetime_v3';
      receipt.countryCode = undefined;
      receipt.regionCode = 'US';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'infinity_lifetime_v3' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10 * 10000000 * 1.5 / 1000000);
    }
  });

  it('lifetime revenue based on 3 month new naming', async () => {
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 10000000;
      receipt.productId = 'infinity_m3_v3_d50';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'infinity_m3_v3_d50' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(0.5 * 10000000 * 1.5 / 1000000);
    }
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
      receipt.priceCurrencyCode = undefined;
      receipt.priceAmountMicros = undefined;
      receipt.productId = 'infinity_lifetime_v3';
      receipt.countryCode = undefined;
      receipt.regionCode = 'US';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'infinity_lifetime_v3' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10 * 0.5 * 10000000 * 1.5 / 1000000);
    }
  });

  it('lifetime revenue check version and country', async () => {
    {
      // incorrect country
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 12345;
      receipt.productId = 'boo_infinity_1_month';
      receipt.countryCode = 'CA';
      await makePremiumPurchase(0, receipt);
    }
    {
      // incorrect version
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 2);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 12345;
      receipt.productId = 'boo_infinity_1_month';
      receipt.countryCode = 'NZ';
      await makePremiumPurchase(0, receipt);
    }
    {
      // correct country and version
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 3);
      receipt.priceCurrencyCode = 'ABC';
      receipt.priceAmountMicros = 1;
      receipt.productId = 'boo_infinity_1_month';
      receipt.countryCode = 'NZ';
      await makePremiumPurchase(0, receipt);
    }
    {
      const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 4);
      receipt.priceCurrencyCode = undefined;
      receipt.priceAmountMicros = undefined;
      receipt.productId = 'boo_infinity_lifetime';
      receipt.countryCode = undefined;
      receipt.regionCode = 'NZ';
      await makePremiumPurchase(0, receipt);

      const savedReceipt = await PurchaseReceipt.findOne({ productId: 'boo_infinity_lifetime' }, { revenue: 1 });
      expect(savedReceipt.revenue).to.equal(10 * 1 * 1.5 / 1000000);
    }
  });

  const coinProducts = [
    '100_coins',
    '1000_coins',
    '4000_coins',
    '10000_coins',
  ];
  for (const productId of coinProducts) {
    it(`coin purchase google revenue ${productId}`, async () => {
      async function makeCoinPurchase(userId, receipt) {
        const res = await request(app)
          .put('/v1/coins/purchaseCoins')
          .set('authorization', userId)
          .send({
            receipt,
          });
        expect(res.status).to.equal(200);
      }
      const revenues = [];
      for (let i = 0; i < 5; i++) {
        const receipt = receipts[i];
        receipt.productId = productId;
        await makeCoinPurchase(i, receipts[i]);
        const savedReceipt = await CoinPurchaseReceipt.findOne({ transactionId: i }, { revenue: 1 });
        revenues.push(savedReceipt.revenue);
      }
      expect(revenues[0]).to.equal(revenues[1]);
      expect(revenues[0]).to.equal(revenues[2]);
      expect(revenues[0]).to.equal(revenues[3]);
      expect(revenues[4]).to.equal(12345 * 1.5 / 1000000);
      console.log(revenues);
    });
  }
});

it('numUniqueUsersWhoPurchasedInfinity metrics', async () => {
  async function check(expected) {
    res = await request(app)
      .post('/v1/worker/recordMetrics');
    expect(res.status).to.equal(200);

    await metricsLib.loadMostRecentMetrics();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.numUniqueUsersWhoPurchasedInfinity).to.equal(expected);
  }

  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  await check(0);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  await check(1);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: renewedGoogleReceipt });
  expect(res.status).to.equal(200);

  await check(1);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 1)
    .send({ receipt: validAppleReceipt });
  expect(res.status).to.equal(200);

  await check(2);
});

describe('sale reason', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({appVersion: '1.10.0'})
    expect(res.status).to.equal(200);
  });

  it('no sale', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt: validGoogleReceipt });
    expect(res.status).to.equal(200);

    expect((await PurchaseReceipt.findOne()).saleReason).to.equal();
  });

  it('first', async () => {
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.productId = 'boo_infinity_1_month_discount_50';

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    expect((await PurchaseReceipt.findOne()).saleReason).to.equal('first');
  });
});

it('kochava', async () => {

  // user 0: kochava
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/kochava')
    .set('authorization', 0)
    .send({kochava: { network: 'fb' }})
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  expect((await PurchaseReceipt.findOne({user:'0'})).kochavaNetwork).to.equal('fb');
  expect((await PurchaseReceipt.findOne({user:'0'})).kochava).to.eql({network:'fb'});

  // user 1: no kochava
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 1)
    .send({ receipt: validAppleReceipt });
  expect(res.status).to.equal(200);

  expect((await PurchaseReceipt.findOne({user:'1'})).kochavaNetwork).to.equal();
  expect((await PurchaseReceipt.findOne({user:'1'})).kochava).to.eql();

  const originalReceipt0 = await PurchaseReceipt.findOne({user:'0'}).lean();
  const originalReceipt1 = await PurchaseReceipt.findOne({user:'1'}).lean();

  // test backfill
  await PurchaseReceipt.updateMany({}, { $unset: {kochavaNetwork:1,kochava:1} });
  expect((await PurchaseReceipt.findOne({user:'0'})).kochavaNetwork).to.equal();
  expect((await PurchaseReceipt.findOne({user:'0'})).kochava).to.eql();
  expect((await PurchaseReceipt.findOne({user:'1'})).kochavaNetwork).to.equal();
  expect((await PurchaseReceipt.findOne({user:'1'})).kochava).to.eql();

  await iapLib.backfillKochavaNetwork();
  await iapLib.backfillKochava();
  expect((await PurchaseReceipt.findOne({user:'0'})).kochavaNetwork).to.equal('fb');
  expect((await PurchaseReceipt.findOne({user:'0'})).kochava).to.eql({network:'fb'});
  expect((await PurchaseReceipt.findOne({user:'1'})).kochavaNetwork).to.equal();
  expect((await PurchaseReceipt.findOne({user:'1'})).kochava).to.eql();

  const updatedReceipt0 = await PurchaseReceipt.findOne({user:'0'}).lean();
  const updatedReceipt1 = await PurchaseReceipt.findOne({user:'1'}).lean();

  expect(originalReceipt0).to.eql(updatedReceipt0);
  expect(originalReceipt1).to.eql(updatedReceipt1);

  console.log((await PurchaseReceipt.find()));
});

it('kochava after purchase', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  expect((await PurchaseReceipt.findOne({user:'0'})).kochavaNetwork).to.equal();
  expect((await PurchaseReceipt.findOne({user:'0'})).kochava).to.eql();

  res = await request(app)
    .put('/v1/user/kochava')
    .set('authorization', 0)
    .send({kochava: { network: 'fb' }})
  expect(res.status).to.equal(200);

  expect((await PurchaseReceipt.findOne({user:'0'})).kochavaNetwork).to.equal('fb');
  expect((await PurchaseReceipt.findOne({user:'0'})).kochava).to.eql({network:'fb'});
});

it('appsflyer android', async () => {
  appsflyerData= {
    appsflyer_id: '*************-1234567',
    status: 'success',
    payload: {
        adgroup_id: null,
        af_adset_id: "************",
        af_ad_type: "ClickToDownload",
        retargeting_conversion_type: "none",
        orig_cost: "0.0",
        network: "Search",
        is_first_launch: false,
        af_click_lookback: "7d",
        af_cpi: null,
        iscache: true,
        external_account_id: {
            $numberDouble: "**********.0"
        },
        click_time: "2025-03-16 05:15:52.363",
        adset: null,
        match_type: "srn",
        af_channel: "ACI_Search",
        af_viewthrough_lookback: "1d",
        campaign_id: "***********",
        lat: "0",
        install_time: "2025-03-16 05:24:27.545",
        af_c_id: "***********",
        agency: null,
        media_source: "googleadwords_int",
        ad_event_id: "ClxDajBLQ1Fqd3l0Uy1CaENLQVJJc0FNR0p5enEtTWJQTFVRbC1RV0VwUFhFNzFQTjB0Nl9tMi1BX1JHYURWUkVsZ3FVZGllaVV1MTdRYlZBYUFyaGlFQUx3X3djQhITCO6l9ZXujYwDFdBe9ggd6MwTcBjll5vNASAAKMPvv94tMlpDajhLRVFqd3l0Uy1CaENlMFlxTjZabW8tb3NCRWlZQUdzcXRaQmJDMTJnYzAyTzFCT3F2VElpa3FUTlZpNXZrZFgyRE1xVlVjamJ1VlJGNVFSb0NSTGdZQVE",
        af_siteid: "GoogleSearch",
        af_status: "Non-organic",
        af_sub1: null,
        gclid: null,
        referrer_gclid: "Cj0KCQjwytS-BhCKARIsAMGJyzq-MbPLUQl-QWEpPXE71PN0t6_m2-A_RGaDVRElgqUdieiUu17QbVAaArhiEALw_wcB",
        cost_cents_USD: "0",
        af_ad_id: "",
        af_reengagement_window: "30d",
        af_sub5: null,
        af_sub4: null,
        af_adset: "General Dating",
        'click-timestamp': "1742102152363",
        af_sub3: null,
        af_sub2: null,
        adset_id: null,
        gbraid: null,
        http_referrer: null,
        campaign: "US - Installs",
        af_ad: "",
        adgroup: null
    }
  }

  basic.assignConfig.restore();
  sinon.stub(basic, 'assignConfig').returns(true);

  // user 0: appsflyer
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({appsflyer: appsflyerData, os: 'android', appVersion: "1.13.87",});
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  sinon.assert.calledWith(
    AppsFlyerClient.sendEvent,
    'android',   
    {
      eventName: 'af_purchase',
      eventValue: {
        af_currency: 'USD',
        af_revenue: 19.99,
        af_quantity: 1,
        af_validated: true,
        af_content_id: 'boo_infinity_1_month'
      },
      appsflyer_id: '*************-1234567',
      os: undefined
    } 
  );

  expect((await PurchaseReceipt.findOne({user:'0'})).appsflyer).to.deep.equal(appsflyerData);


});

it('appsflyer_id not found sendevent not called', async () => {
  appsflyerData= {
    status: 'success',
    payload: {
        adgroup_id: null,
        af_adset_id: "************",
        af_ad_type: "ClickToDownload",
        retargeting_conversion_type: "none",
        orig_cost: "0.0",
        network: "Search",
        is_first_launch: false,
        af_click_lookback: "7d",
        af_cpi: null,
        iscache: true,
        external_account_id: {
            $numberDouble: "**********.0"
        },
        click_time: "2025-03-16 05:15:52.363",
        adset: null,
        match_type: "srn",
        af_channel: "ACI_Search",
        af_viewthrough_lookback: "1d",
        campaign_id: "***********",
        lat: "0",
        install_time: "2025-03-16 05:24:27.545",
        af_c_id: "***********",
        agency: null,
        media_source: "googleadwords_int",
        ad_event_id: "ClxDajBLQ1Fqd3l0Uy1CaENLQVJJc0FNR0p5enEtTWJQTFVRbC1RV0VwUFhFNzFQTjB0Nl9tMi1BX1JHYURWUkVsZ3FVZGllaVV1MTdRYlZBYUFyaGlFQUx3X3djQhITCO6l9ZXujYwDFdBe9ggd6MwTcBjll5vNASAAKMPvv94tMlpDajhLRVFqd3l0Uy1CaENlMFlxTjZabW8tb3NCRWlZQUdzcXRaQmJDMTJnYzAyTzFCT3F2VElpa3FUTlZpNXZrZFgyRE1xVlVjamJ1VlJGNVFSb0NSTGdZQVE",
        af_siteid: "GoogleSearch",
        af_status: "Non-organic",
        af_sub1: null,
        gclid: null,
        referrer_gclid: "Cj0KCQjwytS-BhCKARIsAMGJyzq-MbPLUQl-QWEpPXE71PN0t6_m2-A_RGaDVRElgqUdieiUu17QbVAaArhiEALw_wcB",
        cost_cents_USD: "0",
        af_ad_id: "",
        af_reengagement_window: "30d",
        af_sub5: null,
        af_sub4: null,
        af_adset: "General Dating",
        'click-timestamp': "1742102152363",
        af_sub3: null,
        af_sub2: null,
        adset_id: null,
        gbraid: null,
        http_referrer: null,
        campaign: "US - Installs",
        af_ad: "",
        adgroup: null
    }
  }

  // user 0: appsflyer
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({appsflyer: appsflyerData});
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  sinon.assert.notCalled(AppsFlyerClient.sendEvent);

  expect((await PurchaseReceipt.findOne({user:'0'})).appsflyer).to.deep.equal(appsflyerData);


});

it('utm', async () => {

  // user 0: utm
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({
      utm_source: 'source',
      utm_medium: 'medium',
      utm_campaign: 'campaign',
      utm_content: 'content',
      adset_name: 'adset_name',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({ receipt: validGoogleReceipt });
  expect(res.status).to.equal(200);

  receipt = await PurchaseReceipt.findOne({user:'0'});
  expect(receipt.utm_source).to.equal('source');
  expect(receipt.utm_medium).to.equal('medium');
  expect(receipt.utm_campaign).to.equal('campaign');
  expect(receipt.utm_content).to.equal('content');
  expect(receipt.adset_name).to.equal('adset_name');

  // user 1: no utm
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 1)
    .send({ receipt: validAppleReceipt });
  expect(res.status).to.equal(200);

  receipt = await PurchaseReceipt.findOne({user:'1'});
  expect(receipt.utm_source).to.equal();
  expect(receipt.utm_medium).to.equal();
  expect(receipt.utm_campaign).to.equal();
  expect(receipt.utm_content).to.equal();
  expect(receipt.adset_name).to.equal();

  // test backfill
  await PurchaseReceipt.updateMany({}, { $unset: {utm_source:1, utm_medium:1, utm_campaign:1} });
  receipt = await PurchaseReceipt.findOne({user:'0'});
  expect(receipt.utm_source).to.equal();
  expect(receipt.utm_medium).to.equal();
  expect(receipt.utm_campaign).to.equal();
  receipt = await PurchaseReceipt.findOne({user:'1'});
  expect(receipt.utm_source).to.equal();
  expect(receipt.utm_medium).to.equal();
  expect(receipt.utm_campaign).to.equal();

  await iapLib.backfillUTM();
  receipt = await PurchaseReceipt.findOne({user:'0'});
  expect(receipt.utm_source).to.equal('source');
  expect(receipt.utm_medium).to.equal('medium');
  expect(receipt.utm_campaign).to.equal('campaign');
  receipt = await PurchaseReceipt.findOne({user:'1'});
  expect(receipt.utm_source).to.equal();
  expect(receipt.utm_medium).to.equal();
  expect(receipt.utm_campaign).to.equal();

  console.log((await PurchaseReceipt.find()));
});

it('productIdPurchased', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
  receipt.productId = 'premium_m3_v5';
  receipt.expiryTimeMillis = DateTime.utc().plus({ months: 3 }).toJSDate().getTime();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt,
    });
  expect(res.status).to.equal(200);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.productIdPurchased).to.equal('premium_m3_v5');

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt,
      productIdPurchased: 'premium_m1_v5'
    });
  expect(res.status).to.equal(200);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.productIdPurchased).to.equal('premium_m1_v5');
});

it('god mode - super like anniversary', async () => {
  let clock = sinon.useFakeTimers();

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  const receipt = iapHelper.getValidGoogleReceipt('', Date.now(), 1);
  receipt.productId = 'premium_m3_v5';
  receipt.expiryTimeMillis = DateTime.utc().plus({ months: 3 }).toJSDate().getTime();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt,
    });
  expect(res.status).to.equal(200);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.premium).to.equal(true);
  expect(res.body.user.premiumV2).to.equal(false);
  expect(res.body.user.godMode).to.equal(true);
  expect(res.body.user.productIdPurchased).to.equal('premium_m3_v5');
  expect(res.body.user.numSuperLikes).to.equal(4);

  // 32 days
  clock.tick(32 * 24 * 3600 * 1000);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.godMode).to.equal(true);
  expect(res.body.user.numSuperLikes).to.equal(8);

  // 32 days
  clock.tick(32 * 24 * 3600 * 1000);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.godMode).to.equal(true);
  expect(res.body.user.numSuperLikes).to.equal(12);

  // 32 days
  clock.tick(32 * 24 * 3600 * 1000);

  var res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.premium).to.equal(false);
  expect(res.body.user.premiumV2).to.equal(false);
  expect(res.body.user.godMode).to.equal(false);
  expect(res.body.user.productIdPurchased).to.equal();
  expect(res.body.user.numSuperLikes).to.equal(12);
});

it('daysOnPlatformBeforePurchase d0', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt: validGoogleReceipt,
    });
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal(0);
  user.createdAt = moment().subtract(2, 'days').toDate();
  await user.save();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt: validAppleReceipt,
    });
  expect(res.status).to.equal(200);

  user = await User.findById(0).lean();
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal(0);
  expect(user.purchases).to.eql([{
    productType: 'infinity',
    productId: 'boo_infinity_1_month',
    daysOnPlatformBeforePurchase: 0,
    revenue: 20,
  }]);
});

it('daysOnPlatformBeforePurchase d7', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal();
  user.createdAt = moment().subtract(7, 'days').toDate();
  await user.save();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt: validGoogleReceipt,
    });
  expect(res.status).to.equal(200);

  user = await User.findById(0).lean();
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal(7);
  expect(user.purchases).to.eql([{
    productType: 'infinity',
    productId: 'boo_infinity_1_month',
    daysOnPlatformBeforePurchase: 7,
    revenue: 20,
  }]);
  user = await User.findById(0);
  user.createdAt = moment().subtract(10, 'days').toDate();
  user.premiumExpiration = undefined;
  await user.save();

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt: validAppleReceipt,
    });
  expect(res.status).to.equal(200);

  user = await User.findById(0).lean();
  expect(user.metrics.daysOnPlatformBeforePurchase).to.equal(7);
  expect(user.purchases).to.eql([
    {
      productType: 'infinity',
      productId: 'boo_infinity_1_month',
      daysOnPlatformBeforePurchase: 7,
      revenue: 20,
    },
    {
      productType: 'infinity',
      productId: 'boo_infinity_1_month',
      daysOnPlatformBeforePurchase: 10,
      revenue: 20,
    },
  ]);
});

it('web visitor revenue metrics', async () => {
  res = await request(app)
    .put('/web/visitor')
    .send({ webDeviceId: '0', locale: 'en' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await WebVisitor.findOne({ user: '0' });
  expect(visitor.revenue).to.equal(0);
  expect(visitor.madePurchase).to.equal(0);
  expect(visitor.purchasedInfinity).to.equal(0);

  res = await request(app)
    .put('/v1/user/purchasePremium')
    .set('authorization', 0)
    .send({
      receipt: validGoogleReceipt,
    });
  expect(res.status).to.equal(200);

  visitor = await WebVisitor.findOne({ user: '0' });
  expect(visitor.revenue).to.equal(20);
  expect(visitor.madePurchase).to.equal(1);
  expect(visitor.purchasedInfinity).to.equal(1);
});

describe('post purchase screen', () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
  });

  it('no purchase yet', async () => {
    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(404);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo).to.equal(null);
  });

  it('purchase active, then cancelled, then expired, then new purchase', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    // cancel
    res = await request(app)
      .post('/google-renewal')
      .send(googleCancelRtdnBody);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    // expire
    let clock = sinon.useFakeTimers(Date.now());
    clock.tick(oneDayMs);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');

    // new purchase
    let newPurchaseDate = Date.now();
    let newReceipt = iapHelper.getValidGoogleReceipt('boo_infinity_12_months', newPurchaseDate, 'mock-transaction-id');
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: newReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_12_months');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(newPurchaseDate+oneDayMs);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_12_months');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(newPurchaseDate+oneDayMs);
  });

  it('purchase active, then cancelled, then expired, then new purchase via stripe', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    // cancel
    res = await request(app)
      .post('/google-renewal')
      .send(googleCancelRtdnBody);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    // expire
    let clock = sinon.useFakeTimers(Date.now());
    clock.tick(oneDayMs);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');

    const newExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    const newProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_0' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: newProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(newExpirationDate);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(newExpirationDate);
  });

  it('purchase via stripe, expired', async () => {
    const newExpirationDate = Date.now() - oneDayMs;
    console.log('newExpirationDate : ', newExpirationDate)
    const newProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_1' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_1',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: newProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('expired');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);
  });

  it('purchase via stripe, cancelled', async () => {
    const newExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    console.log('newExpirationDate : ', newExpirationDate)
    const newProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_2' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_2',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: newProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);
  });

  it('purchase via stripe canceled, purchase IAP active then cancelled, then expired', async () => {
    const newExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    console.log('newExpirationDate : ', newExpirationDate)
    const newProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_2' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_2',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: newProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - newExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    // cancel
    res = await request(app)
      .post('/google-renewal')
      .send(googleCancelRtdnBody);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    // expire
    let clock = sinon.useFakeTimers(Date.now());
    clock.tick(oneDayMs);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');

    console.log('purchase Info :', res.body.purchaseInfo)
  });

  it('purchase via stripe active, purchase IAP active', async () => {
    // subscription via stripe for 12 months
    const stripeExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    const stripeProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_0' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: stripeProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);

    // subscription via app for 1 month
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct); // getPurchaseInfo should return product with the greater expirtaion date
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct); // getPurchaseInfo should return product with the greater expirtaion date
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);
    console.log('purchase Info :', res.body.purchaseInfo)
  });

  it('purchase via stripe cancelled, purchase IAP active', async () => {
    // subscription via stripe for 12 months
    const stripeExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    const stripeProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_2' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_2',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: stripeProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('cancelled');
    expect(res.body.purchaseInfo.productId).to.equal(stripeProduct);
    expect(Math.abs(new Date(res.body.purchaseInfo.expirationDate).getTime() - stripeExpirationDate)).to.be.most(diffTimeTolerance);

    // subscription via app for 1 month
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month'); // getPurchaseInfo should return product with the greater expirtaion date
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate); // getPurchaseInfo should return the greater expirtaion date

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month'); // getPurchaseInfo should return product with the greater expirtaion date
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate); // getPurchaseInfo should return the greater expirtaion date

    console.log('purchase Info :', res.body.purchaseInfo)
  });

  it('purchase infinity 1 month before lifetime purchase', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month');
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(validExpirationDate);

    //user buy infinity_lifetime
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.orderId = 'GPA.3369-7229-4364-41991'
    receipt.productId = 'boo_infinity_lifetime';
    receipt.expirationDate = undefined;
    receipt.expiryTimeMillis = undefined;

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: receipt,
      });
    expect(res.status).to.equal(200);


    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_lifetime');
    expect(res.body.purchaseInfo.expirationDate).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_lifetime');
    expect(res.body.purchaseInfo.expirationDate).to.equal();

    const user = await User.findOne({_id: '0'})
    console.log('premiumExpiration : ',user.premiumExpiration)

    const expectedDate = new Date();
    expectedDate.setFullYear(new Date().getFullYear() + 100);

    expect(user.premiumExpiration.getFullYear()).to.equal(expectedDate.getFullYear());
  });

  it('manually added subscription, active', async () => {
    const expectedDate = new Date();
    expectedDate.setFullYear(new Date().getFullYear() + 100);
    console.log('expectedDate :', expectedDate)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.69'})
    expect(res.status).to.equal(200);

    const user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)


    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.be.null;
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(expectedDate.setFullYear(new Date().getFullYear() + 100));
  })

  it('manually added subscription, expired', async () => {
    const expectedDate = new Date();
    expectedDate.setFullYear(new Date().getFullYear() - 1);
    console.log('expectedDate :', expectedDate)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.69'})
    expect(res.status).to.equal(200);

    const user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)


    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('expired');
    expect(res.body.purchaseInfo.productId).to.be.null;
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(expectedDate.setFullYear(new Date().getFullYear() - 1));
  })

  it('manually added subscription, after IAP purchase', async () => {
    const expectedDate = new Date();
    console.log('expectedDate :', expectedDate)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.69'})
    expect(res.status).to.equal(200);

    // subscription via app for 1 month
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: valid1MonthGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)

    //manually added  a week subscription
    user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate.setDate(expectedDate.getDate() + 7);
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_1_month'); // getPurchaseInfo should return product with the greater expirtaion date
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(valid1MonthExpirationDate);

    //manually added infinity lifetime
    user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate.setFullYear(new Date().getFullYear() + 100)
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.be.null;
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(expectedDate.setFullYear(new Date().getFullYear() + 100));
  })

  it('manually added subscription, after Stripe purchase', async () => {
    const expectedDate = new Date();
    console.log('expectedDate :', expectedDate)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.69'})
    expect(res.status).to.equal(200);

    // subscription via stripe for 1 year
    const stripeExpirationDate = (Math.floor(Date.now() / 1000) + 31536000) * 1000; // convert to milliseconds
    const newProduct = 'infinity_m12_x0'
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_0' } });
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: newProduct,
    });
    await stripeWebhook(subEvent);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(stripeExpirationDate);

    console.log('ADD A WEEK Subscription')
    //manually added  a week subscription
    user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate.setDate(expectedDate.getDate() + 7);
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal(newProduct);
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(stripeExpirationDate);

    //manually added infinity lifetime
    console.log('ADD A LIFETIME Subscription')
    user = await User.findOne({_id: '0'})
    user.premiumExpiration = expectedDate.setFullYear(new Date().getFullYear() + 100)
    await user.save()
    console.log('premiumExpiration : ',user.premiumExpiration)

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log('res.body.purchaseInfo:', res.body.purchaseInfo)
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.be.null;
    expect(new Date(res.body.purchaseInfo.expirationDate).getTime()).to.equal(expectedDate.setFullYear(new Date().getFullYear() + 100));
  })

  it('lifetime purchase', async () => {
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.productId = 'boo_infinity_lifetime';
    receipt.expirationDate = undefined;
    receipt.expiryTimeMillis = undefined;

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log(res.body.purchaseInfo);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_lifetime');
    expect(res.body.purchaseInfo.expirationDate).to.equal();
  });

  it('lifetime purchase with invalid expiration date', async () => {
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.productId = 'boo_infinity_lifetime';
    receipt.expirationDate = 0;
    receipt.expiryTimeMillis = 0;

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    console.log(res.body.purchaseInfo);
    expect(res.body.purchaseInfo.status).to.equal('active');
    expect(res.body.purchaseInfo.productId).to.equal('boo_infinity_lifetime');
    expect(res.body.purchaseInfo.expirationDate).to.equal();
  });

  it('match and profile view pictures', async () => {
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: validGoogleReceipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.matchPictures).to.eql([]);
    expect(res.body.profileViewPictures).to.eql([]);

    // user 1 views profile
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(1);
    pictureId = res.body.pictures[0];

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.matchPictures).to.eql([]);
    expect(res.body.profileViewPictures).to.eql([pictureId]);

    // user 1 matches with user 0
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/post-purchase-screen')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.matchPictures).to.eql([pictureId]);
    expect(res.body.profileViewPictures).to.eql([pictureId]);
  });
});

module.exports = {
  validGoogleReceipt,
  validAppleReceipt,
};
