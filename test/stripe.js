const { expect } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app } = require('./common');
const { initApp, stripeWebhook } = require('./helper/api');
const { getMockCheckoutSessionEvent, getMockSubscriptionEvent, getMockChargeSucceededEvent, getMockPaymentIntentSucceededEvent } = require('./helper/stripe');
const User = require('../models/user');
const StripeReceipt = require('../models/stripe-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt');
const curExLib = require('../lib/currency-exchange');
const moment = require('moment');
const async = require('async');
const stripe = require('../lib/stripe');
const userLib = require('../lib/user');
const ses = require('../lib/ses');
const {getStripeSubscriptionByCustomer} = require('../lib/purchase')

const oneDayMs = 86400000;
const now = Date.now();
const validExpirationDate = now + oneDayMs;

describe('stripe webhook', async function () {
  beforeEach(async () => {
    await initApp(0);
    await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_0' } });
  });

  it('subscription active', async () => {
    const subEvent = getMockSubscriptionEvent({
      customerId: 'stripeCustomer_0',
      current_period_end: validExpirationDate / 1000,
      status: 'active',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.premiumExpiration.getTime()).to.equal(new Date(validExpirationDate).getTime());
  });

  it('charge succeeded', async () => {
    const subEvent = getMockChargeSucceededEvent({
      customerId: 'stripeCustomer_0',
      cardCountry: 'US',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.stripePaymentCountry).to.equal('United States');
  });

  it('payment country does not match actual country', async () => {
    // actual country US
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // payment country JP
    const subEvent = getMockChargeSucceededEvent({
      customerId: 'stripeCustomer_0',
      cardCountry: 'JP',
    });
    await stripeWebhook(subEvent);

    // admin views reports
    const user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal(null);
    expect(res.body.reports[0].reportedUser).to.equal('0');
    expect(res.body.reports[0].reason).to.eql(['Auto-report: stripePaymentCountry does not match actualCountry']);
    expect(res.body.reports[0].comment).to.equal('stripePaymentCountry: Japan, actualCountry: United States');
  });

  it('payment country matches actual country', async () => {
    // actual country US
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // payment country US
    const subEvent = getMockChargeSucceededEvent({
      customerId: 'stripeCustomer_0',
      cardCountry: 'US',
    });
    await stripeWebhook(subEvent);

    // admin views reports
    const user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);
  });

  it('store stripe currency', async () => {
    const subEvent = getMockPaymentIntentSucceededEvent({
      customerId: 'stripeCustomer_0',
      currency: 'jpy',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.stripeCurrency).to.equal('jpy');

    // stored currency should be used even if user's IP changes to a different country
    // USA
    const ip = '*************';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('jpy');
    expect(res.body.m1.price).to.equal(1435);

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);

    // global variable set by the stubbed method to facilitate checking params
    console.log('fakeStripeCheckoutSessionsCreateParams', fakeStripeCheckoutSessionsCreateParams);
    expect(fakeStripeCheckoutSessionsCreateParams.currency).to.equal('jpy');
  });

  it('auto report ruble payments', async () => {
    const subEvent = getMockPaymentIntentSucceededEvent({
      customerId: 'stripeCustomer_0',
      currency: 'rub',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.stripeCurrency).to.equal('rub');

    // admin views reports
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal(null);
    expect(res.body.reports[0].reportedUser).to.equal('0');
    expect(res.body.reports[0].reason).to.eql(['Auto-report: stripe currency is Russian ruble']);
    expect(res.body.reports[0].comment).to.equal('');
  });

  it('backfill stripe currency', async () => {
    // see test/stub.js fakeStripe.paymentIntents.list
    user = await User.findById('0');
    expect(user.stripeCurrency).to.equal();
    expect(user.stripeCurrencyBackfilled).to.equal();
    user.stripeCustomerId = 'test_paymentIntents_currency_backfill';
    await user.save();

    // currency should be backfilled and used even if user's IP changes to a different country
    // USA
    const ip = '*************';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('jpy');
    expect(res.body.m1.price).to.equal(1435);

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);

    // global variable set by the stubbed method to facilitate checking params
    console.log('fakeStripeCheckoutSessionsCreateParams', fakeStripeCheckoutSessionsCreateParams);
    expect(fakeStripeCheckoutSessionsCreateParams.currency).to.equal('jpy');

    user = await User.findById('0');
    expect(user.stripeCurrency).to.equal('jpy');
    expect(user.stripeCurrencyBackfilled).to.equal(true);

    // a user without payment intents should not have the currency backfilled
    await initApp(1);
    user = await User.findById('1');
    expect(user.stripeCurrency).to.equal();
    expect(user.stripeCurrencyBackfilled).to.equal();
    user.stripeCustomerId = 'stripeCustomer_1';
    await user.save();

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1.price).to.equal(19.99);

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);

    // global variable set by the stubbed method to facilitate checking params
    console.log('fakeStripeCheckoutSessionsCreateParams', fakeStripeCheckoutSessionsCreateParams);
    expect(fakeStripeCheckoutSessionsCreateParams.currency).to.equal('usd');

    user = await User.findById('1');
    expect(user.stripeCurrency).to.equal();
    expect(user.stripeCurrencyBackfilled).to.equal(true);
  });

  it('subscription incomplete', async () => {
    const subEvent = getMockSubscriptionEvent({
      customerId: 'stripeCustomer_0',
      current_period_end: validExpirationDate / 1000,
      status: 'incomplete',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.premiumExpiration).to.equal();
  });

  it('subscription customer not found', async () => {
    const subEvent = getMockSubscriptionEvent({
      customerId: 'stripeCustomer_1',
      current_period_end: validExpirationDate / 1000,
      status: 'active',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.premiumExpiration).to.equal();
  });

  it('checkout subscription', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.stripeRevenue).to.equal(20);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.metrics.numSalePurchases).to.equal(0);
    expect(user.metrics.saleRevenue).to.equal(0);
    expect(user.premiumExpiration).to.equal();

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(1);
    expect(receipts[0].user).to.equal('0');
    expect(receipts[0].stripeCustomerId).to.equal('stripeCustomer_0');
  });

  it('checkout customer not found', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_1',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(0);
    expect(user.metrics.stripeRevenue).to.equal(0);

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(0);
  });

  it('checkout lifetime', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'payment',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 20000,
      currency: 'ufc',
      lookup_key: 'infinity_lifetime',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(200);
    expect(user.metrics.stripeRevenue).to.equal(200);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(moment(user.premiumExpiration).diff(moment(), 'years')).to.equal(99);
  });

  it('checkout discount', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      amount_discount: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.stripeRevenue).to.equal(20);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.metrics.numSalePurchases).to.equal(1);
    expect(user.metrics.saleRevenue).to.equal(20);
    expect(user.premiumExpiration).to.equal();
  });

  it('checkout incomplete', async () => {
    let subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'incomplete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'rejected',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_1',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(0);
    expect(user.metrics.stripeRevenue).to.equal(0);
    expect(user.metrics.madePurchase).to.equal();
    expect(user.metrics.numPurchases).to.equal(0);
    expect(user.metrics.numStripePurchases).to.equal(0);
    expect(user.premiumExpiration).to.equal();
  });

  it('async payment', async () => {
    subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'unpaid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(0);
    expect(user.metrics.stripeRevenue).to.equal(0);
    expect(user.metrics.madePurchase).to.equal();
    expect(user.metrics.numPurchases).to.equal(0);
    expect(user.metrics.numStripePurchases).to.equal(0);
    expect(user.premiumExpiration).to.equal();

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(0);

    subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
      type: 'checkout.session.async_payment_succeeded',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.stripeRevenue).to.equal(20);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.metrics.numSalePurchases).to.equal(0);
    expect(user.metrics.saleRevenue).to.equal(0);
    expect(user.premiumExpiration).to.equal();

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(1);
    expect(receipts[0].user).to.equal('0');
    expect(receipts[0].stripeCustomerId).to.equal('stripeCustomer_0');

    // duplicate events with the same session id should be ignored
    subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'infinity_m1_x0',
      type: 'checkout.session.async_payment_succeeded',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.stripeRevenue).to.equal(20);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.metrics.numSalePurchases).to.equal(0);
    expect(user.metrics.saleRevenue).to.equal(0);
    expect(user.premiumExpiration).to.equal();

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(1);
    expect(receipts[0].user).to.equal('0');
    expect(receipts[0].stripeCustomerId).to.equal('stripeCustomer_0');
  });

  it('checkout jpy', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2200,
      currency: 'jpy',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(22);
    expect(user.metrics.stripeRevenue).to.equal(22);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.premiumExpiration).to.equal();
  });

  it('checkout jod', async () => {
    const subEvent = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'subscription',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 20000,
      currency: 'jod',
      lookup_key: 'infinity_m1_x0',
    });
    await stripeWebhook(subEvent);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(40);
    expect(user.metrics.stripeRevenue).to.equal(40);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.premiumExpiration).to.equal();
  });

  it('renewal payment', async () => {
    const event = {
      type: 'invoice.payment_succeeded',
      data: {
        object: {
          billing_reason: 'subscription_cycle',
          customer: 'stripeCustomer_0',
          amount_paid: 2000,
          currency: 'ufc',
          lines: {
            data: [
              {
                price: {
                  lookup_key: 'infinity_m1_x0',
                },
              },
            ],
          },
        },
      },
    };
    await stripeWebhook(event);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.stripeRevenue).to.equal(20);
    expect(user.metrics.madePurchase).to.equal(true);
    expect(user.metrics.numPurchases).to.equal(1);
    expect(user.metrics.numStripePurchases).to.equal(1);
    expect(user.metrics.numSalePurchases).to.equal(0);
    expect(user.metrics.saleRevenue).to.equal(0);
    expect(user.premiumExpiration).to.equal();

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(1);
    expect(receipts[0].user).to.equal('0');
    expect(receipts[0].stripeCustomerId).to.equal('stripeCustomer_0');
    expect(receipts[0].revenue).to.equal(20);
    expect(receipts[0].product).to.equal('infinity_m1_x0');
    expect(receipts[0].currency).to.equal('ufc');
    expect(receipts[0].isRenewal).to.equal(true);
  });

  it('initial payment event ignored', async () => {
    const event = {
      type: 'invoice.payment_succeeded',
      data: {
        object: {
          billing_reason: 'subscription_create',
          customer: 'stripeCustomer_0',
          amount_paid: 2000,
          currency: 'ufc',
          lines: {
            data: [
              {
                price: {
                  lookup_key: 'infinity_m1_x0',
                },
              },
            ],
          },
        },
      },
    };
    await stripeWebhook(event);

    receipts = await StripeReceipt.find();
    expect(receipts.length).to.equal(0);
  });

  it('checkout coins', async () => {
    const event = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'payment',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'IT_10000_coins',
    });
    await stripeWebhook(event);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.coinRevenue).to.equal(20);
    expect(user.metrics.numCoinPurchases).to.equal(1);
    expect(user.metrics.numCoinsPurchased).to.equal(10000);

    const purchaseReceipt = await CoinPurchaseReceipt.findOne({ user: '0' });
    console.log(purchaseReceipt);
    expect(purchaseReceipt.service).to.equal('stripe');
    expect(purchaseReceipt.productId).to.equal('10000_coins');
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
  });

  it('checkout super like', async () => {
    const event = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'payment',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'IT_super_love_50_v1',
    });
    await stripeWebhook(event);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.superLikeRevenue).to.equal(20);
    expect(user.metrics.numSuperLikePurchases).to.equal(1);
    expect(user.metrics.numSuperLikesPurchased).to.equal(50);

    const purchaseReceipt = await SuperLikePurchaseReceipt.findOne({ user: '0' });
    console.log(purchaseReceipt);
    expect(purchaseReceipt.service).to.equal('stripe');
    expect(purchaseReceipt.productId).to.equal('super_love_50_v1');
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
  });

  it('checkout neurons', async () => {
    const event = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'payment',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 2000,
      currency: 'ufc',
      lookup_key: 'IT_1000_neurons',
    });
    await stripeWebhook(event);

    user = await User.findById('0');
    expect(user.metrics.revenue).to.equal(20);
    expect(user.metrics.neuronRevenue).to.equal(20);
    expect(user.metrics.numNeuronPurchases).to.equal(1);
    expect(user.metrics.numNeuronsPurchased).to.equal(1000);

    const purchaseReceipt = await NeuronPurchaseReceipt.findOne({ user: '0' });
    console.log(purchaseReceipt);
    expect(purchaseReceipt.service).to.equal('stripe');
    expect(purchaseReceipt.productId).to.equal('1000_neurons');
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
  });

  it('checkout boost', async () => {
    const event = getMockCheckoutSessionEvent({
      customerId: 'stripeCustomer_0',
      session_mode: 'payment',
      session_payment_status: 'paid',
      session_status: 'complete',
      amount_total: 3400,
      currency: 'UFC',
      lookup_key: 'IT_boost_5_v1',
    });
    await stripeWebhook(event);

    user = await User.findById('0');
    console.log('user.metrics : ', user.metrics)
    expect(user.metrics.revenue).to.equal(34);
    expect(user.metrics.boostRevenue).to.equal(34);
    expect(user.metrics.numBoostPurchases).to.equal(1);
    expect(user.metrics.numBoostsPurchased).to.equal(5);

    const purchaseReceipt = await BoostPurchaseReceipt.findOne({ user: '0' });
    console.log(purchaseReceipt);
    expect(purchaseReceipt.service).to.equal('stripe');
    expect(purchaseReceipt.productId).to.equal('boost_5_v1');
    expect(purchaseReceipt.daysOnPlatformBeforePurchase).to.equal(0);
    expect(purchaseReceipt.numberOfMatchesBeforePurchase).to.equal(0);
  });
});

it('kochava, utm and appsflyer', async () => {
  appsflyerData= {
    status: 'success',
    payload: {
        adgroup_id: null,
        af_adset_id: "************",
        af_ad_type: "ClickToDownload",
        retargeting_conversion_type: "none",
        orig_cost: "0.0",
        network: "Search",
        is_first_launch: false,
        af_click_lookback: "7d",
        af_cpi: null,
        iscache: true,
        external_account_id: {
            $numberDouble: "**********.0"
        },
        click_time: "2025-03-16 05:15:52.363",
        adset: null,
        match_type: "srn",
        af_channel: "ACI_Search",
        af_viewthrough_lookback: "1d",
        campaign_id: "***********",
        lat: "0",
        install_time: "2025-03-16 05:24:27.545",
        af_c_id: "***********",
        agency: null,
        media_source: "googleadwords_int",
        ad_event_id: "ClxDajBLQ1Fqd3l0Uy1CaENLQVJJc0FNR0p5enEtTWJQTFVRbC1RV0VwUFhFNzFQTjB0Nl9tMi1BX1JHYURWUkVsZ3FVZGllaVV1MTdRYlZBYUFyaGlFQUx3X3djQhITCO6l9ZXujYwDFdBe9ggd6MwTcBjll5vNASAAKMPvv94tMlpDajhLRVFqd3l0Uy1CaENlMFlxTjZabW8tb3NCRWlZQUdzcXRaQmJDMTJnYzAyTzFCT3F2VElpa3FUTlZpNXZrZFgyRE1xVlVjamJ1VlJGNVFSb0NSTGdZQVE",
        af_siteid: "GoogleSearch",
        af_status: "Non-organic",
        af_sub1: null,
        gclid: null,
        referrer_gclid: "Cj0KCQjwytS-BhCKARIsAMGJyzq-MbPLUQl-QWEpPXE71PN0t6_m2-A_RGaDVRElgqUdieiUu17QbVAaArhiEALw_wcB",
        cost_cents_USD: "0",
        af_ad_id: "",
        af_reengagement_window: "30d",
        af_sub5: null,
        af_sub4: null,
        af_adset: "General Dating",
        'click-timestamp': "1742102152363",
        af_sub3: null,
        af_sub2: null,
        adset_id: null,
        gbraid: null,
        http_referrer: null,
        campaign: "US - Installs",
        af_ad: "",
        adgroup: null
    }
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({
      utm_source: 'source',
      utm_medium: 'medium',
      utm_campaign: 'campaign',
      utm_content: 'content',
      adset_name: 'adset_name',
      appsflyer: appsflyerData
    });
  expect(res.status).to.equal(200);

  await User.updateOne({ _id: 0 }, { $set: { stripeCustomerId: 'stripeCustomer_0' } });

  res = await request(app)
    .put('/v1/user/kochava')
    .set('authorization', 0)
    .send({kochava: { network: 'fb' }})
  expect(res.status).to.equal(200);

  const subEvent = getMockCheckoutSessionEvent({
    customerId: 'stripeCustomer_0',
    session_mode: 'subscription',
    session_payment_status: 'paid',
    session_status: 'complete',
    amount_total: 2000,
    currency: 'ufc',
    lookup_key: 'infinity_m1_x0',
  });
  await stripeWebhook(subEvent);

  receipts = await StripeReceipt.find();
  console.log('receipts: ', receipts)
  expect(receipts.length).to.equal(1);
  expect(receipts[0].user).to.equal('0');
  expect(receipts[0].stripeCustomerId).to.equal('stripeCustomer_0');
  expect(receipts[0].kochavaNetwork).to.equal('fb');
  expect(receipts[0].kochava).to.eql({network:'fb'});
  expect(receipts[0].utm_source).to.equal('source');
  expect(receipts[0].utm_medium).to.equal('medium');
  expect(receipts[0].utm_campaign).to.equal('campaign');
  expect(receipts[0].utm_content).to.equal('content');
  expect(receipts[0].adset_name).to.equal('adset_name');
  expect(receipts[0].appsflyer).to.deep.equal(appsflyerData);
});

describe('stripe products and checkout', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  });

  it('USA flash sale x0', async () => {
    const ip = '*************';

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 1,
      }));

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1).to.eql({ price: 19.99 });
    expect(res.body.m3).to.eql({ price: 39.99, discount: 50 });
    expect(res.body.m6).to.eql({ price: 69.60, discount: 50 });
    expect(res.body.m12).to.eql({ price: 129.99, discount: 50 });
    expect(res.body.lifetime).to.eql({ price: 200.00 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m1_x0-subscription');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m3'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m3_x0-flash_sale_50-subscription');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'lifetime'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_lifetime-payment');
  });

  it('unavailable because price too low', async () => {
    const ip = '*************';

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 0.1,
      }));

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1).to.eql({ price: 19.99, unavailable: true });
    expect(res.body.m3).to.eql({ price: 39.99 });
    expect(res.body.m6).to.eql({ price: 69.60, discount: 50 });
    expect(res.body.m12).to.eql({ price: 129.99, discount: 50 });
    expect(res.body.lifetime).to.eql({ price: 200.00 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('https://boo.world');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m3'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m3_x0-subscription');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m6'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m6_x0-flash_sale_50-subscription');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'lifetime'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_lifetime-payment');
  });

  it('webFlashSale', async () => {
    const ip = '*************';

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 0.1,
      }));

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1).to.eql({ price: 19.99, unavailable: true });
    expect(res.body.m3).to.eql({ price: 39.99 });
    expect(res.body.m6).to.eql({ price: 69.60, discount: 50 });
    expect(res.body.m12).to.eql({ price: 129.99, discount: 50 });
    expect(res.body.lifetime).to.eql({ price: 200.00 });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.webFlashSale.discount).to.equal(50);

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 0.02,
      }));

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1).to.eql({ price: 19.99, unavailable: true });
    expect(res.body.m3).to.eql({ price: 39.99, unavailable: true });
    expect(res.body.m6).to.eql({ price: 69.60, unavailable: true });
    expect(res.body.m12).to.eql({ price: 129.99 });
    expect(res.body.lifetime).to.eql({ price: 200.00 });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.webFlashSale).to.equal();
  });

  it('race condition creating customer', (done) => {
    const ip = '*************';
    const parallelRuns = 10;
    let actualRuns = 0;
    const asyncTasks = [];

    let currentId = 0;
    fakeStripe.customers.create = async function(params) {
      const id = (currentId++).toString();
      console.log('create stripe customer', id);
      return { id };
    }
    fakeStripe.checkout.sessions.create = async function(params) {
      console.log('create stripe checkout session', params.customer);
      expect(params.customer).to.equal('0');
      return { url: 'url' };
    }

    for (i = 0; i < parallelRuns; i++) {
      asyncTasks.push((err, result) => {
        request(app)
          .post('/v1/stripe/create-checkout-session')
          .set('authorization', 0)
          .set('X-Forwarded-For', ip)
          .send({product:'m1'})
          .expect(200)
          .end((err, res) => {
            actualRuns++;

            if (err) {
              return done(err);
            }

            if (actualRuns == parallelRuns) {
              done();
            }
          });
      });
    }
    async.parallel(asyncTasks, done);
  });

  it('Japan', async () => {
    const ip = '************';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('jpy');
    expect(res.body.m1.price).to.equal(1435);
  });

  it('Panama', async () => {
    const ip = '**********';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('pab');
    expect(res.body.m1.price).to.equal(2.54);
  });

  it('Jordan', async () => {
    const ip = '*************';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1.price).to.equal(19.99);
  });

  it('China', async () => {
    const ip = '*********';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1.price).to.equal(19.99);
  });

  it('Italy', async () => {
    const ip = '**********';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('eur');
    expect(res.body.m1.price).to.equal(8.68);

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_infinity_m1_x0-subscription');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'lifetime'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_infinity_lifetime-payment');
  });

  it('no flash sale', async () => {
    const ip = '*************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.m1.price).to.equal(19.99);
    expect(res.body.m1.discount).to.equal();

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 1)
      .set('X-Forwarded-For', ip)
      .send({product:'m3'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m3_x0-subscription');
  });

  it('x1', async () => {
    const ip = '*************';

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: [],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body.m1.price).to.equal(36);

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('infinity_m1_x1-subscription');
  });

  it('delete account', async () => {

    const ip = '*************';

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1'})
    expect(res.status).to.equal(200);

    const user = await User.findOne({ _id: 0 });
    await userLib.deleteAccount(user);

    expect(stripe.stripe.deletedCustomerIds).to.eql(['fake']);
  });

  it('redirectUrl', async () => {
    const ip = '*************';

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'m1', redirectUrl: 'https://boo.world/messages'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('https://boo.world/messages');
  });

  it('consumable products', async () => {
    let ip = '*************';

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 1,
      }));

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body['1000_coins']).to.eql({ price: 10 });
    expect(res.body['4000_coins']).to.eql({ price: 30 });
    expect(res.body['10000_coins']).to.eql({ price: 50 });
    expect(res.body.super_love_3_v1).to.eql({ price: 10 });
    expect(res.body.super_love_12_v1).to.eql({ price: 30.00 });
    expect(res.body.super_love_50_v1).to.eql({ price: 75.00 });
    expect(res.body['40_neurons']).to.eql({ price: 10.00 });
    expect(res.body['100_neurons']).to.eql({ price: 20.00 });
    expect(res.body['300_neurons']).to.eql({ price: 40.00 });
    expect(res.body['1000_neurons']).to.eql({ price: 80.00 });
    expect(res.body.boost_1_v1).to.eql({ price: 6.99 });
    expect(res.body.boost_5_v1).to.eql({ price: 31.99 });
    expect(res.body.boost_10_v1).to.eql({ price: 55.99 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'10000_coins'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('10000_coins-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'super_love_50_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('super_love_50_v1-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'1000_neurons'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('1000_neurons-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'boost_10_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('boost_10_v1-payment');

    // Japan
    ip = '************';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('jpy');
    expect(res.body['10000_coins']).to.eql({ price: 3700 });
    expect(res.body.super_love_50_v1).to.eql({ price: 5400 });
    expect(res.body['1000_neurons']).to.eql({ price: 5300 });
    expect(res.body.boost_10_v1).to.eql({ price: 6000 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'10000_coins'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('10000_coins-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'super_love_50_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('super_love_50_v1-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'1000_neurons'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('1000_neurons-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'boost_10_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('boost_10_v1-payment');

    // Italy
    ip = '**********';

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('eur');
    expect(res.body['10000_coins']).to.eql({ price: 22 });
    expect(res.body.super_love_50_v1).to.eql({ price: 33 });
    expect(res.body['1000_neurons']).to.eql({ price: 34 });
    expect(res.body.boost_10_v1).to.eql({ price: 50.99 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'10000_coins'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_10000_coins-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'super_love_50_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_super_love_50_v1-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'1000_neurons'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_1000_neurons-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'boost_10_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('IT_boost_10_v1-payment');
  });

  it('consumable products flash sale', async () => {
    let ip = '*************';

    curExLib.getExchangeData.restore();
    sinon.stub(curExLib, 'getExchangeData')
      .callsFake(() => ({
        USD: 1,
      }));

    user = await User.findById('0');
    user.createdAt = 0;
    await user.save();

    res = await request(app)
      .get('/v1/stripe/products')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.currency).to.equal('usd');
    expect(res.body['1000_coins']).to.eql({ price: 10, discount: 30 });
    expect(res.body['4000_coins']).to.eql({ price: 30, discount: 30 });
    expect(res.body['10000_coins']).to.eql({ price: 50, discount: 30 });
    expect(res.body.super_love_3_v1).to.eql({ price: 10, discount: 50 });
    expect(res.body.super_love_12_v1).to.eql({ price: 30.00, discount: 50 });
    expect(res.body.super_love_50_v1).to.eql({ price: 75.00, discount: 50 });
    expect(res.body['40_neurons']).to.eql({ price: 10.00 });
    expect(res.body['100_neurons']).to.eql({ price: 20.00 });
    expect(res.body['300_neurons']).to.eql({ price: 40.00 });
    expect(res.body['1000_neurons']).to.eql({ price: 80.00 });
    expect(res.body.boost_1_v1).to.eql({ price: 6.99 });
    expect(res.body.boost_5_v1).to.eql({ price: 31.99 });
    expect(res.body.boost_10_v1).to.eql({ price: 55.99 });

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'10000_coins'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('10000_coins-flash_sale_30-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'super_love_50_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('super_love_50_v1-flash_sale_50-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'1000_neurons'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('1000_neurons-payment');

    res = await request(app)
      .post('/v1/stripe/create-checkout-session')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({product:'boost_10_v1'})
    expect(res.status).to.equal(200);
    expect(res.body.redirect).to.equal('boost_10_v1-payment');
  });

});

describe('get Stripe Subscription By Customer', function () {
  //stripe lib was stub at stub.js (fakeStripe)

  it('should return the subscription with the greatest current_period_end', async function () {
    const customerId = 'stripeCustomer_123';
    const subscription = await getStripeSubscriptionByCustomer(customerId);
    console.log('subscription :', subscription)

    expect(subscription).to.have.property('id', 'sub2');
  });

  it('should return null if no subscriptions are found', async function () {
    const customerId = 'stripeCustomer_234';
    const subscription = await getStripeSubscriptionByCustomer(customerId);

    expect(subscription).to.be.null;
  });

  it('should return nullr if stripe.subscriptions.list fails', async function () {
    const customerId = 'stripeCustomer_345';
    const subscription = await getStripeSubscriptionByCustomer(customerId);

    expect(subscription).to.be.null;
  });
});



