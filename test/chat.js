const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const fs = require('fs');
const { notifs, reset, waitFor } = require('./stub');
const {
  app, validGif, validResponseGif, validImagePath, initSocket, destroySocket, getSocketPromise, createAdminUser,
} = require('./common');
const userMiddleware = require('../middleware/user');
const User = require('../models/user');
const Action = require('../models/action');
const Chat = require('../models/chat');
const Message = require('../models/message');
const Question = require('../models/question');
const Comment = require('../models/comment');
const UserMetadata = require('../models/user-metadata');
const chatLib = require('../lib/chat');
const promptsLib = require('../lib/prompts');
const constants = require('../lib/constants');
const { pageSize } = require('../lib/constants');
const { validGoogleReceipt } = require('./iap');
const { createQuestion } = require('../lib/social');
const FriendList = require('../models/friend-list');
const { initApp } = require('./helper/api');
const iapHelper = require('./helper/iap');
const ChatMetric = require('../models/chat-metric')
const ChatExportHistory = require('../models/chat-export-history');
const moment = require('moment');
const { translate, translate_frontend } = require('../lib/translate');
const premiumLib = require('../lib/premium');
const basic = require('../lib/basic');
const interestLib = require('../lib/interest');
const { OpenAI } = require('../lib/prompt')
const temp = require('temp').track();
const ChatAnalysisYourTurn = require('../models/chat-analysis-your-turn');
const userLib = require('../lib/user');
const Unmatched = require('../models/unmatched');

describe('Add whats new dm from Boo', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: interestLib.whatsNewMessage.Version });
    expect(res.status).to.equal(200);
  });

  it('should trigger whats new message from Boo', async () => {
    let user = await User.findById(0);
    expect(user.booMessages.whatsNewSentAtVersion).to.equal(undefined);
    expect(user.signupAppVersion).to.equal(interestLib.whatsNewMessage.Version);

    // Will not trigger message because user signed up with latest version
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    let chats = await Chat.find({ users: '0' });
    expect(chats.length).to.equal(0);

    // condition setup: user signed up with older version and updated to new version
    user.signupAppVersion = '1.13.69';
    user.appVersion = '1.13.68';
    user.locale = 'es';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: interestLib.whatsNewMessage.Version });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    user = await User.findById(0);
    expect(user.booMessages.whatsNewSentAtVersion).to.equal(interestLib.whatsNewMessage.Version);
    user.booMessages.whatsNewSentAtVersion = '1.13.73';
    await user.save();

    chats = await Chat.find({ users: '0' });
    expect(chats.length).to.equal(1);

    let messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(interestLib.whatsNewMessage.es);
    expect(messages[0].additionalInfo).to.equal(undefined);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    // User has no signupAppVersion and no appVersion, so message is not triggered
    chats = await Chat.find({ users: '1' });
    expect(chats.length).to.equal(0);

    user = await User.findById(1);
    expect(user.signupAppVersion).to.equal(undefined);
    expect(user.appVersion).to.equal(undefined);
    expect(user.booMessages.whatsNewSentAtVersion).to.equal(undefined);

    // condition setup: no signupAppVersion but has app version
    user.signupAppVersion = undefined;
    user.appVersion = interestLib.whatsNewMessage.Version;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chats = await Chat.find({ users: '1' });
    expect(chats.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.70' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    // User should get message with update now
    chats = await Chat.find({ users: '2' });
    expect(chats.length).to.equal(1);

    messages = await Message.find({ chat: chats[0]._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(`${interestLib.whatsNewMessage.en}\n\nUpdate Now`);
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: 'Update Now', openPage: 'appStoreLink' }));

    // condition setup: user already got whats new message for 1.13.74, update it to send again with latest message format
    user = await User.findById(2);
    user.booMessages.whatsNewSentAtVersion = '1.13.73';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.73' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    // User should get message with update now
    messages = await Message.find({ chat: chats[0]._id }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(2);
    expect(messages[0].text).to.equal(`${interestLib.whatsNewMessage.en}\n\n[Update Now](/appStoreLink)`);
    expect(messages[0].additionalInfo).to.equal();

    // validate translation
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.73' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chats = await Chat.find({ users: '0' });
    expect(chats.length).to.equal(1);

    messages = await Message.find({ chat: chats[0]._id }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(2);
    expect(messages[0].text).to.equal(`${interestLib.whatsNewMessage.es}\n\n[${translate('Update Now', 'es')}](/appStoreLink)`);
    expect(messages[0].additionalInfo).to.equal();
  });
});

describe('Add dms from Boo', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);
  });

  it('should trigger welcome message from Boo', async () => {
    // Check that no message is sent initially
    let res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    // Ensure no chat was created
    await new Promise((r) => setTimeout(r, 200));

    let chat = await Chat.find({ users: '0' });
    expect(chat.length).to.equal(0);

    let user = await User.findById(0);
    expect(user.booMessages.welcomeMessage).to.equal(undefined);
    user.locale = 'es';
    await user.save();

    // Upload a picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '0')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // Picture uploaded but not verified, should trigger welcome message from Boo
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));
    let messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(translate('Welcome to Boo! Verify your profile to start messaging, build trust, get shown more often, and receive up to {{matchPlaceholder}}% more matches.\n\nVerify', 'es', { matchPlaceholder: '198' }));
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: translate('Verify', 'es'), openPage: 'verifyPage' }));

    user = await User.findById(0);
    expect(user.booMessages.welcomeMessage).to.equal(true);
    user.locale = 'en';
    await user.save();

    // Verify no second message is sent
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1); // Still one message

    // Reset welcome message marker
    user.booMessages.welcomeMessage = undefined;
    user.verification.status = 'verified';
    await user.save();

    // Should trigger message from Boo for verified but incomplete profile
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));
    messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(2);
    expect(messages[1].text).to.equal('Welcome to Boo! You’re almost there. Completing your profile to 100% helps other souls get to know you better and can lead to up to 198% more matches.');

    // Complete user profile
    user = await User.findById(0);
    expect(user.booMessages.welcomeMessage).to.equal(true);
    user.booMessages.welcomeMessage = undefined;
    const profilePictures = ["image1", "image2", "image3", "image4", "image5", "image6", "image7", "image8", "image9"];
    user.pictures = profilePictures;
    user.prompts = [{ id: 'prompt1', answer: 'answer1' }, { id: 'prompt2', answer: 'answer2' }, { id: 'prompt3', answer: 'answer3' }];
    user.education = 'education';
    user.work = 'work';
    user.interestNames = ['interest1', 'interest2', 'interest3'];
    user.description = 'description';
    user.ethnicities = ["Asian"];
    user.height = 180;
    user.moreAboutUser = { exercise: 'Active', educationLevel: 'Undergraduate degree', drinking: 'Socially', smoking: 'Regularly', kids: 'Want someday', religion: 'Muslim' };
    await user.save();

    // Should trigger message from Boo for completed profile
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(3);
    expect(messages[2].text).to.equal('Welcome to Boo! Connect with souls who share your interests and find helpful tips here along the way.');

    // validate markdown format in version 1.13.73
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.73' });
    expect(res.status).to.equal(200);

    // Upload a picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '1')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // Picture uploaded but not verified, should trigger welcome message from Boo
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 1)
      .send({ finished_signup: true });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chat = await Chat.find({ users: '1' });

    messages = await Message.find({ chat: chat[0]._id }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(2);

    const messageBody = translate('Welcome to Boo! Verify your profile to start messaging, build trust, get shown more often, and receive up to {{matchPlaceholder}}% more matches.', 'en', { matchPlaceholder: '198' });
    const buttonText = translate('Verify', 'en');
    expect(messages[0].text).to.equal(`${messageBody}\n\n[${buttonText}](/verifyPage)`);

    user = await User.findById(1);
    expect(user.booMessages.welcomeMessage).to.equal(true);
  });

  it('should trigger message to enable notifications', async () => {
    let user_0 = await User.findOne({ _id: 0 });
    expect(user_0.fcmToken).to.equal(undefined);
    expect(user_0.booMessages.notificationTriggerInit).to.equal(undefined);
    expect(user_0.booMessages.notificationTrigger).to.equal(undefined);
    expect(user_0.booMessages.messageSender).to.equal(undefined);

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 1)
      .send({ firstName: 'User 1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    user_0 = await User.findById(0);
    expect(user_0.booMessages).to.have.property('notificationTriggerInit');
    expect(user_0.booMessages.messageSender).to.equal('User 1');

    // need to call init app
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    let chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    user_0.booMessages.notificationTriggerInit = moment().subtract(6, 'minutes').toDate();
    await user_0.save();

    // should add the message from boo
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    let messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal('User 1 sent you a message. Enable notifications to stay updated and not miss out on any matches or messages.\n\nEnable notifications');
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: 'Enable notifications', openPage: 'notificationPage' }));

    // Test message translation
    let user_1 = await User.findById(1);
    expect(user_1.booMessages.notificationTriggerInit).to.equal(undefined);
    expect(user_1.booMessages.notificationTrigger).to.equal(undefined);
    expect(user_1.booMessages.messageSender).to.equal(undefined);
    user_1.locale = 'bn';
    await user_1.save();

    // approving chat from user 1
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'User 0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    user_1 = await User.findById(1);
    expect(user_1.booMessages).to.have.property('notificationTriggerInit');
    expect(user_1.booMessages.messageSender).to.equal('User 0');
    user_1.booMessages.notificationTriggerInit = moment().subtract(6, 'minutes').toDate();
    await user_1.save();

    // should add the message from boo
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    const translatedText = translate('{{messageSender}} sent you a message. Enable notifications to stay updated and not miss out on any matches or messages.\n\nEnable notifications', 'bn', { messageSender: 'User 0' });

    chat = await Chat.findDirectChat('1', chatLib.BOO_BOT_ID);
    messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(translatedText);
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: translate('Enable notifications', 'bn'), openPage: 'notificationPage' }));

    user_1 = await User.findById(1);
    expect(user_1.booMessages.notificationTriggerInit).to.equal(undefined);
    expect(user_1.booMessages.notificationTrigger).to.equal(true);
    expect(user_1.booMessages.messageSender).to.equal(undefined);

    // should not trigger message if user has fcmtoken
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // adding fcm token
    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 2)
      .send({
        fcmToken: 'token2',
      });
    expect(res.status).to.equal(200);

    let user_2 = await User.findById(2);
    expect(user_2.fcmToken).to.equal('token2');

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '2',
        text: 'msg for user 2',
      });
    expect(res.status).to.equal(200);

    user_2 = await User.findById(2);
    expect(user_2.booMessages.notificationTriggerInit).equal(undefined);

    // remove fcm token and update user app version to 1.13.73
    user_2.fcmToken = undefined;
    user_2.appVersion = '1.13.73';
    await user_2.save();

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '2',
        text: 'another msg for user 2',
      });
    expect(res.status).to.equal(200);

    user_2 = await User.findById(2);
    expect(user_2.booMessages).to.have.property('notificationTriggerInit');
    expect(user_2.booMessages.messageSender).to.equal('User 0');
    user_2.booMessages.notificationTriggerInit = moment().subtract(6, 'minutes').toDate();
    await user_2.save();

    // should add the message from boo
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('2', chatLib.BOO_BOT_ID);
    messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(2); // whats new message included
    expect(messages[0].text).to.equal('User 0 sent you a message. Enable notifications to stay updated and not miss out on any matches or messages.\n\n[Enable notifications](/notificationPage)');
    expect(messages[0].additionalInfo).to.equal();
  });

  it('test D1 message trigger', async () => {
    // should trigger day one message for user with no likes
    let user = await User.findById(0);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(1, 'days').toDate();
    await user.save();

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    user = await User.findById(0);
    expect(user.booMessages.dayOneTrigger).to.equal(true);

    let chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    let messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal('If you’re having difficulties getting matches, try these helpful tips:\n\n1. Upload clear photos with your face.\n2. Verify your profile to build trust and show you’re real.\n3. Complete your profile to 100% with your interests and hobbies so others can learn more about you.\n4. Stay active daily and send loves. Matches happen when two souls send love to each other.');

    // should trigger day one message for user with likes but no matches
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.69', locale: 'jp' });
    expect(res.status).to.equal(200);

    user = await User.findById(1);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(1, 'days').toDate();
    user.metrics.numLikesReceived = 1;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    user = await User.findById(1);
    expect(user.metrics.numLikesReceived).to.equal(1);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(true);

    chat = await Chat.findDirectChat('1', chatLib.BOO_BOT_ID);
    messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(translate('If you’re having difficulties getting matches, try these helpful tips:\n\n1. Upload clear photos with your face.\n2. Verify your profile to build trust and show you’re real.\n3. Complete your profile to 100% with your interests and hobbies so others can learn more about you.\n4. Stay active daily and send loves. Matches happen when two souls send love to each other.', 'jp'));

    // should NOT trigger message if user has likes and matches
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('2', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    user = await User.findById(2);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(1, 'days').toDate();
    user.metrics.numLikesReceived = 1;
    user.metrics.numMatches = 1;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('2', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    // should NOT trigger message: user has likes, no matches but premium
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('3', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    user = await User.findById(3);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);

    // condition setup
    user.metrics.numLikesReceived = 1;
    user.createdAt = moment().subtract(1, 'days').toDate();
    user.premiumExpiration = moment().add(2, 'days').toDate();
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('3', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    // should not trigger message if user is not on day one
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('4', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    user = await User.findById(4);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(2, 'days').toDate();
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('3', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    user = await User.findById(4);
    expect(user.metrics.numLikesReceived).to.equal(0);
    expect(user.metrics.numMatches).to.equal(0);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.booMessages.dayOneTrigger).to.equal(undefined);
  });

  it('should trigger sale related messages from boo', async () => {
    const triggerAppInit = async (userId) => {
      const res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', userId);
      expect(res.status).to.equal(200);
      await new Promise((r) => setTimeout(r, 200));
    };

    const checkMessage = async (userId, expectedIndex, expectedText, expectedAdditionalInfo) => {
      const userIdHash = Chat.createUserIdHash([userId, chatLib.BOO_BOT_ID]);
      const chat = await Chat.findOne({
        userIdHash,
        groupChat: { $ne: true },
        deletedAt: null,
        automatedChat: { $ne: true } });
      const messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });

      expect(messages.length).to.equal(expectedIndex);
      expect(messages[expectedIndex - 1].text).to.equal(expectedText);
      expect(messages[expectedIndex - 1].additionalInfo).to.equal(JSON.stringify(expectedAdditionalInfo));
    };

    let user = await User.findById(0);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.coinsFlashSaleEndDate).to.equal(undefined);
    expect(user.superLikeFlashSaleEndDate).to.equal(undefined);
    expect(user).to.have.property('premiumFlashSaleEndDate');
    expect(user.premiumFlashSaleReason).to.equal('first');

    // condition setup for boosts Sale Message
    user.boostsFlashSaleEndDate = moment().add(6, 'hours').toDate();
    await user.save();

    await triggerAppInit('0');
    await checkMessage('0', 1, 'Limited time: Save 50% on Boost for the next 6 hours.\n\nActivate now to become a top soul in your area for the next 60 minutes.\n\nActivate', { replaceText: 'Activate', openPage: 'boostsSalePage' });

    // condition setup for coins Sale
    user.coinsFlashSaleEndDate = moment().add(6, 'hours').toDate();
    await user.save();

    await triggerAppInit('0');
    await checkMessage('0', 2, 'Limited time: Save 30% on coins for the next 6 hours.\n\nWith your coins, unlock features like DMs, Time Travel, and Revival.\n\nGet Coins', { replaceText: 'Get Coins', openPage: 'coinsSalePage' });

    // condition setup for super love sale
    user.superLikeFlashSaleEndDate = moment().add(6, 'hours').toDate();
    await user.save();

    // Disabled for later experiment: User has active super love but message will not be sent
    await triggerAppInit('0');
    // await checkMessage('0', 3, 'Limited time: Save 50% on Super Loves for the next 6 hours.\n\nSuper Loves are pinned at the top of requests and can lead to up to 198% more matches.\n\nGet Super Loves', { replaceText: 'Get Super Loves', openPage: 'superLikeSalePage' });
    // Instead of super love message, the last message will be of coin sale
    await checkMessage('0', 2, 'Limited time: Save 30% on coins for the next 6 hours.\n\nWith your coins, unlock features like DMs, Time Travel, and Revival.\n\nGet Coins', { replaceText: 'Get Coins', openPage: 'coinsSalePage' });

    // condition setup for premium flash sale
    user.premiumFlashSaleEndDate = moment().subtract(30, 'days').toDate();
    await user.save();

    await triggerAppInit('0');
    await checkMessage('0', 3, 'Limited time: Save 50% on Boo Infinity for the next 6 hours.\n\nUpgrade and unlock unlimited Loves and DMs, See Who Loves You, 2 Super Loves per week and more.\n\nUpgrade', { replaceText: 'Upgrade', openPage: 'infinitySalePage' });

    // cleanup
    await Chat.deleteMany({});
    await Message.deleteMany({});

    // should not trigger message if user has no active sale
    user.boostsFlashSaleEndDate = moment().subtract(1, 'hours').toDate();
    user.coinsFlashSaleEndDate = moment().subtract(1, 'hours').toDate();
    user.superLikeFlashSaleEndDate = moment().subtract(1, 'hours').toDate();
    user.premiumFlashSaleEndDate = moment().subtract(1, 'hours').toDate();
    await user.save();

    await triggerAppInit('0');
    let chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    // should not trigger sale messages if they were sent for this sale
    user = await User.findById(0);
    user.booMessages.boostsSaleTriggerDate = undefined;
    user.boostsFlashSaleEndDate = moment().add(6, 'hours').toDate();
    user.locale = 'bn';
    await user.save();

    await triggerAppInit('0');
    await checkMessage('0', 1, translate('Limited time: Save 50% on Boost for the next {{placeholder}}.\n\nActivate now to become a top soul in your area for the next 60 minutes.\n\nActivate', 'bn', { placeholder: translate_frontend('{}h', 'bn').replace('{}', 6) }), { replaceText: translate('Activate', 'bn'), openPage: 'boostsSalePage' });

    await triggerAppInit('0');
    chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    let messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);

    // Set user app version to 1.13.73 to check markdown format
    await triggerAppInit('1');

    user = await User.findById(1);
    user.appVersion = '1.13.73';
    user.boostsFlashSaleEndDate = moment().add(6, 'hours').toDate();
    user.locale = 'bn';
    await user.save();

    await triggerAppInit('1');
    await new Promise((r) => setTimeout(r, 200));

    const messageBody = translate('Limited time: Save 50% on Boost for the next {{placeholder}}.\n\nActivate now to become a top soul in your area for the next 60 minutes.', 'bn', { placeholder: translate_frontend('{}h', 'bn').replace('{}', 6) });
    const buttonText = translate('Activate', 'bn');

    chat = await Chat.findDirectChat('1', chatLib.BOO_BOT_ID);
    messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(2);
    expect(messages[0].text).to.equal(`${messageBody}\n\n[${buttonText}](/boostsSalePage)`);
    expect(messages[0].additionalInfo).to.equal();
  });

  it('test D3 message trigger', async () => {
    // should trigger message on day 3 for users with no filter popup
    let user = await User.findById(0);
    expect(user.booMessages.dayThreeTrigger).to.equal(undefined);
    expect(user.events.open_filter_popup).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(3, 'days').toDate();
    await user.save();

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    let chat = await Chat.findDirectChat('0', chatLib.BOO_BOT_ID);
    let messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal('Personalize your experience with flexible filters. Discover your soulmate based on interests, personality, and lifestyle.\n\nChange Filter');
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: 'Change Filter', openPage: 'filter' }));

    // should NOT trigger message on day 3 if the filter popup has been opened
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    user = await User.findById(1);
    expect(user.booMessages.dayThreeTrigger).to.equal(undefined);
    expect(user.events.open_filter_popup).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(3, 'days').toDate();
    await user.save();

    // send filter popup open event
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 1)
      .send({ open_filter_popup: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    chat = await Chat.findDirectChat('1', chatLib.BOO_BOT_ID);
    expect(chat).to.equal(null);

    // test translation
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.69' });
    expect(res.status).to.equal(200);

    user = await User.findById(2);
    expect(user.booMessages.dayThreeTrigger).to.equal(undefined);
    expect(user.events.open_filter_popup).to.equal(undefined);

    // condition setup
    user.createdAt = moment().subtract(3, 'days').toDate();
    user.locale = 'bn';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chat = await Chat.findDirectChat('2', chatLib.BOO_BOT_ID);
    messages = await Message.find({ chat: chat._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(1);
    expect(messages[0].text).to.equal(translate('Personalize your experience with flexible filters. Discover your soulmate based on interests, personality, and lifestyle.\n\nChange Filter', 'bn'));
    expect(messages[0].additionalInfo).to.equal(JSON.stringify({ replaceText: translate('Change Filter', 'bn'), openPage: 'filter' }));

    // condition setup for markdown format
    user = await User.findById(2);
    user.booMessages.dayThreeTrigger = undefined;
    user.events.open_filter_popup = undefined;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.73' });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 200));

    const messageBody = translate('Personalize your experience with flexible filters. Discover your soulmate based on interests, personality, and lifestyle.', 'bn');
    const buttonText = translate('Change Filter', 'bn');

    messages = await Message.find({ chat: chat._id }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(3); // In this version whats new message is also sent as the last message
    expect(messages[1].text).to.equal(`${messageBody}\n\n[${buttonText}](/filter)`);
    expect(messages[1].additionalInfo).to.equal();
  });
});

describe('Test chat export route', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);
  });
  it('should save and retrieve a successful chat export request', async () => {
    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 1)
      .send({ chatId });
    expect(res.status).to.equal(200);

    let exportRequest = await ChatExportHistory.findOne({ chatId });
    expect(exportRequest.status).to.equal('pending');

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);

    exportRequest = await ChatExportHistory.findOne({ chatId });
    expect(exportRequest.status).to.equal('approved');
  });

  it('should return a 403 error when user is not part of the chat', async () => {
    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 2)
      .send({ chatId });
    expect(res.status).to.equal(403);
  });

  it('should return a 403 error when a request is already being processed', async () => {
    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(403);

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 1)
      .send({ chatId });
    expect(res.status).to.equal(200);

    const exportRequest = await ChatExportHistory.findOne({ chatId });
    expect(exportRequest.status).to.equal('approved');

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 1)
      .send({ chatId });
    expect(res.status).to.equal(403);
  });

  it('should return 403 error for a request to export the chat within the last 24 hours', async () => {
    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);

    let exportRequest = await ChatExportHistory.findOne({ chatId });
    exportRequest.status = 'completed';
    await exportRequest.save();

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(403);

    exportRequest.requestedAt = new Date(Date.now() - 25 * 60 * 60 * 1000);
    await exportRequest.save();

    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);
  });

  it('should return 403 error when a pending chat is attempted to be exported', async () => {
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    const chats = await Chat.find({ users: '2' });
    res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 2)
      .send({ chatId: chats[0]._id });
    expect(res.status).to.equal(403);
  });

  it('should return a 403 error when a group chat is attempted to be exported', async () => {
    const chat = await Chat.findOne({ users: '1' });
    chat.groupChat = true;
    await chat.save();

    let res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 2)
      .send({ chatId: chat._id });
    expect(res.status).to.equal(403);
  });

  it('should return a 403 error when a deleted chat is attempted to be exported', async () => {
    const chat = await Chat.findOne({ users: '1' });
    chat.deletedAt = new Date(Date.now());
    await chat.save();

    let res = await request(app)
      .post('/v1/chat/exportChat')
      .set('authorization', 2)
      .send({ chatId: chat._id });
    expect(res.status).to.equal(403);
  });
});

describe('match indicator tests', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.13.52',
        });
      expect(res.status).to.equal(200);
    }
  });
  it(`should add match indicator 'boost' to chat`, async () => {
    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now(), '1');
    let res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User1 not premium, so they should not see a match indicator
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);

    const user1 = await User.findById('1');
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.equal('boost');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);
  });

  it(`should add match indicator 'boost' when a user-initiated chat is approved while the user is boosted`, async () => {
    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now(), '1');
    let res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    const user1 = await User.findById('1');
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql('boost');
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);
  });

  it(`should add match indicator 'superLike' to chat`, async () => {
    const user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    await user.save();

    let res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    // User 0 not premium, so they should not see a match indicator
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql(undefined);

    const user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = Date.now() + 86400000;
    await user0.save();

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('superLike');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);
  });

  it(`should add match indicator 'infinity' to chat`, async () => {
    const user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = new Date(Date.now() + 86400000);
    await user0.save();

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql('infinity');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);
  });

  it(`should approve chat by sending superLike when recieved a superLike`, async () => {
    const user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    user.premiumExpiration = Date.now() + 864000;
    await user.save();

    let res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('superLike');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);

    const user1 = await User.findOne({ _id: 1 });
    user1.numSuperLikes = 5;
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql('superLike');
  });

  it(`should approve chat with directDM by super like receiving user`, async () => {
    const user = await User.findOne({ _id: 0 });
    user.numSuperLikes = 5;
    user.premiumExpiration = Date.now() + 864000;
    await user.save();

    let res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('superLike');

    const user1 = await User.findOne({ _id: 1 });
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 1)
      .send({
        user: '0',
        message: 'Hi',
        price: 0,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql('infinity');
  });

  it(`should check precedence for matchIndicator, 'boost' > 'infinity'`, async () => {
    const user1 = await User.findOne({ _id: 1 });
    user1.premiumExpiration = new Date(Date.now() + 86400000);
    await user1.save();

    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now(), '1');
    let res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql('boost');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].matchIndicator).to.eql(undefined);
  });

  it(`should check boost added for a new pending chat when superLike is sent`, async () => {
    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now(), '1');
    let res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    const user0 = await User.findOne({ _id: 0 });
    user0.numSuperLikes = 5;
    user0.premiumExpiration = Date.now() + 864000;
    await user0.save();

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // should add match indicator 'superLike' for user 0
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('superLike');

    const user1 = await User.findById('1');
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    // should add match indicator 'boost' for user 1 in pending chat
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('boost');
    expect(res.body.chats[0].numUnreadMessages).to.eql(1);
  });

  it(`should check boost added for /sendDirectMessage route`, async () => {
    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now(), '1');
    let res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 1)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    const user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
        price: 0,
      });
    expect(res.status).to.equal(200);

    const user1 = await User.findById('1');
    user1.premiumExpiration = Date.now() + 86400000;
    await user1.save();

    // should add match indicator 'boost' for user 1 in pending chat
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].matchIndicator).to.eql('boost');
  });
});


describe('chats', async () => {
  const numUsers = 2;

  const supportProfile = {
    _id: chatLib.BOO_SUPPORT_ID,
    crown: false,
    description: 'description',
    education: 'education',
    work: 'work',
    firstName: 'Boo',
    handle: 'boo',
    pictures: ['boo0', 'boo1'],
    interests: [],
    verified: true,
    verificationStatus: 'verified',
  };
  const userProfile0 = {
    _id: '0',
    age: 31,
    crown: false,
    description: 'description0',
    location: 'Honolulu, HI 🇺🇸',
    teleport: false,
    education: 'education0',
    work: 'work0',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '0',
    }],
    interests: [],
    interestNames: [],
    firstName: 'name0',
    gender: 'female',
    handle: 'handle0',
    personality: {
      mbti: 'ISTJ',
      avatar: 'Realist',
      EI: 0.5,
      NS: 0.5,
      FT: 0.5,
      JP: 2 / 3,
    },
    hideQuestions: false,
    hideComments: false,
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
  };
  const userProfile1 = {
    _id: '1',
    age: 31,
    crown: false,
    description: 'description1',
    location: 'Kailua, HI 🇺🇸',
    teleport: true,
    education: 'education1',
    work: 'work1',
    prompts: [{
      id: promptsLib.promptsArray[0].id,
      prompt: promptsLib.promptsArray[0].prompt,
      answer: '1',
    }],
    interests: [],
    interestNames: [],
    firstName: 'name1',
    gender: 'female',
    handle: 'handle1',
    personality: {
      mbti: 'ISTJ',
      avatar: 'Realist',
      EI: 0.5,
      NS: 0.5,
      FT: 0.5,
      JP: 2 / 3,
    },
    hideQuestions: false,
    hideComments: false,
    pictures: ['picture0', 'picture1'],
    profilePicture: 'picture0',
    preferences: { purpose: ['friends'] },
    horoscope: 'Capricorn',
    karma: 0,
    numFollowers: 0,
    verified: false,
    verificationStatus: 'unverified',
  };

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.10.0',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/education')
        .set('authorization', uid)
        .send({
          education: `education${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/description')
        .set('authorization', uid)
        .send({
          description: `description${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/work')
        .set('authorization', uid)
        .send({
          work: `work${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .get('/v1/user/quizQuestions')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      answers = Object.keys(res.body.questions).reduce((map, id) => {
        map[id] = 0;
        return map;
      }, {});
      res = await request(app)
        .put('/v1/user/quizAnswers')
        .set('authorization', uid)
        .send({ answers });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/handle')
        .set('authorization', uid)
        .send({
          handle: `handle${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          purpose: ['friends'],
        });
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      // Honolulu, HI
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/profilePromptAnswers')
        .set('authorization', uid)
        .send({
          prompts: [
            { id: promptsLib.promptsArray[0].id, answer: `${uid}` },
          ],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/approveAllFollowers')
        .set('authorization', uid)
        .send({ approveAllFollowers: false });
      expect(res.status).to.equal(200);
      // mock upload two pictures
      const user = await User.findOne({ _id: uid });
      user.pictures.push('picture0');
      user.pictures.push('picture1');
      res = await user.save();
    }
    // teleport for user 1
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 1)
      .send({
        latitude: 21.40,
        longitude: -157.74,
      });
    expect(res.status).to.equal(200);
  });

  it('change uid', async () => {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // create new account with new uid
    const newUid = '1000';
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', newUid)
      .send({
        appVersion: '1.10.0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', newUid);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // set uid of old account to new uid
    user = await User.findById('0');
    user.uid = newUid;
    await user.save();

    // disable new account
    user = await User.findById(newUid);
    user.disabled = true;
    await user.save();

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', newUid);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile1);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', newUid)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/message')
      .query({ user: 0 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('msg1');

    socket = await initSocket(newUid);
    socketPromise = getSocketPromise(socket, 'message');

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/message')
      .query({ user: 1 })
      .set('authorization', newUid);
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('msg2');
    message = res.body[0];

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql(message);

    await destroySocket(socket);
  });

  it('long text in socket message', async () => {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'message');

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: '#'.repeat(1000),
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/message')
      .query({ user: 1 })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('#'.repeat(1000));
    message = res.body[0];

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql(message);

    await destroySocket(socket);
  });

  /*
  it('hide likes from user with pending report', async () => {
    // user 0 reports user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // User 1 sends like
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // no notif
    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
    reset();

    // user 1 hidden from likes
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // dismiss report
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.admin = false;
    user.adminPermissions = undefined;
    res = await user.save();

    // user 1 not hidden from likes
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // backwards compatibility
    user = await User.findById('1');
    user.metrics = undefined;
    await user.save();

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
  });
  */

  it('show matches with user with pending report', async () => {
    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user 0 reports user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
  });

  it('banned user should not appear in chat list', async () => {

    // make user 0 admin
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // ======================================================
    // User 0 sends like to user 1

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // ======================================================
    // User 1 approves the chat request

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].lastMessage).to.equal(undefined);

    // ban user 1
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '1'});
    expect(res.status).to.equal(200);

    // send a message should not result in notification
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // User 0 should not see user 1 in chat list, but user 1 should see user 0
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    // unban user 1
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    // ban both user 0 and user 1
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '0'});
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '1'});
    expect(res.status).to.equal(200);

    // should not see each other
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('new pending likes from banned user should not appear in chat list', async () => {

    // make user 0 admin
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // ban user 1
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '1'});
    expect(res.status).to.equal(200);

    // User 1 sends like to user 0
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should not see user 1 in chat list, but user 1 should see user 0 in sent likes
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // unban user 1
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // ban both user 0 and user 1
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '0'});
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({user: '1'});
    expect(res.status).to.equal(200);

    // should not see each other
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);
  });

  it('real-time chat - sockets', async () => {
    // create socket for communication
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(1);

    // Initial state - both users have no chats
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/chat')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.length).to.equal(0);
    }

    // ======================================================
    // User 0 sends like to user 1

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('pending chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the pending chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal('1');
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile0);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // ======================================================
    // User 1 approves the chat request

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the approved chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile1);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Both users should see the chat as non-pending
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    let chatId = res.body[0]._id;

    // ======================================================
    // User 1 sends a message
    socketResponse = new Promise((resolve, reject) => {
      socket0.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.text).to.equal('msg1');
    expect(res.sender).to.equal('1');
    expect(res.chat).to.equal(chatId);
    assert(new Date(res.createdAt).getTime() - msgTime < 1000);

    // ======================================================
    // User 0 sends a gif

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.gif).to.equal(validResponseGif);
    expect(res.sender).to.equal('0');
    expect(res.chat).to.equal(chatId);
    assert(new Date(res.createdAt).getTime() - msgTime < 1000);

    // ======================================================
    // User 0 sends an image

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message/image')
      .query({ recipient: 1 })
      .set('authorization', 0)
      .attach('image', validImagePath)
    expect(res.status).to.equal(200);
    console.log(res.body);
    const imageUrl = res.body.image;

    // User 1 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.image).to.equal(imageUrl);

    // ======================================================
    // Unmatch

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('deleted chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the deleted chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res._id).to.equal(chatId);

    // Both users should not have any chats left
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // ======================================================
    // User 0 sends DM to user 1

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('pending chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
        price: 50,
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the pending chat through the socket
    res = await socketResponse;
    expect(res.pendingUser).to.equal('1');
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile0);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);

    chatId = res.body[0]._id;

    // ======================================================
    // Block

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('deleted chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the deleted chat through the socket
    res = await socketResponse;
    expect(res._id).to.equal(chatId);

    // Both users should not have any chats left
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // ======================================================
    // User 0 sends DM to user 1 - fails due to block

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'Hi',
        price: 50,
      });
    expect(res.status).to.equal(404);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // ======================================================
    // cleanup
    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('send dm', async () => {
    // User 0 sends dm to user 1 - fails because not premium
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
      });
    expect(res.status).to.equal(403);

    // no pending chats
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // dm should work now
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
      });
    expect(res.status).to.equal(200);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg0');
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // get messages from pending chat
    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    // cannot send message to pending chat
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' })
      .send({ text: 'msg2' });
    expect(res.status).to.equal(404);

    // mark pending chat seen
    res = await request(app)
      .put('/v1/message/seen')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);
  });

  it('send dm, gif', async () => {
    // User 0 sends dm to user 1 - fails because not premium
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(403);

    // no pending chats
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // dm should work now
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.gif).to.equal(validGif);
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // get messages from pending chat
    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    // cannot send message to pending chat
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' })
      .send({ text: 'msg2' });
    expect(res.status).to.equal(404);

    // mark pending chat seen
    res = await request(app)
      .put('/v1/message/seen')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);
  });

  it('send dm, gif with text', async () => {
    // User 0 sends dm to user 1 - fails because not premium
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
        message: 'msg0'
      });
    expect(res.status).to.equal(403);

    // no pending chats
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // dm should work now
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
        message: 'msg0'
      });
    expect(res.status).to.equal(200);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(2);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg0');
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // get messages from pending chat
    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('res.body messages:',res.body);

    expect(res.body[0].text).to.equal('msg0');
    expect(res.body[1].gif).to.equal(validGif);

    // cannot send message to pending chat
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' })
      .send({ text: 'msg2' });
    expect(res.status).to.equal(404);

    // mark pending chat seen
    res = await request(app)
      .put('/v1/message/seen')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);
  });

  it('send dm with coins', async () => {
    // Set app version
    let user = await User.findOne({ _id: 0 });
    user.appVersion = '1.8.2';
    user = await user.save();

    // Create a third user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // check initial coin balance
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let userCoins = res.body.coins;

    // wrong coin price
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
        price: 0,
      });
    expect(res.status).to.equal(409);

    // spend coins
    userCoins -= 200;
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
        price: 200,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(userCoins);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(userCoins);

    // Not enough coins for another DM
    metadata = await UserMetadata.findOne({ user: 0 });
    metadata.coins = 50;
    await metadata.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'msg0',
        price: 200,
      });
    expect(res.status).to.equal(403);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // Now can send DM for free
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'msg0',
        price: 0,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(50);
  });

  it('send dm gif & message with coins', async () => {

    // Create a third user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // check initial coin balance
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let userCoins = res.body.coins;

    // wrong coin price
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
        message: 'msg0',
        price: 0,
      });
    expect(res.status).to.equal(409);

    // spend coins
    userCoins -= 200;
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        gif: validGif,
        message: 'msg0',
        price: 200,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(userCoins);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(userCoins);

    // Not enough coins for another DM
    metadata = await UserMetadata.findOne({ user: 0 });
    metadata.coins = 50;
    await metadata.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'msg0',
        gif: validGif,
        price: 200,
      });
    expect(res.status).to.equal(403);

    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // Now can send DM for free
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        gif: validGif,
        message: 'msg0',
        price: 0,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(50);
  });

  it('unlimited dms subscription', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.unlimitedDmsExpiration).to.equal();

    // Set app version
    let user = await User.findOne({ _id: 0 });
    user.appVersion = '1.8.2';
    user = await user.save();

    // Create a third user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // check initial coin balance
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const userCoins = res.body.coins;

    // purchase unlimited dms
    const receipt = JSON.parse(JSON.stringify(validGoogleReceipt));
    receipt.productId = 'unlimited_dms_1_month';
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.unlimitedDmsExpiration).to.not.equal();

    // Now can send DM for free
    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '2',
        message: 'msg0',
        price: 0,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(userCoins);
  });

  it('v2 dm with coins', async () => {
    // check initial coin balance
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let userCoins = res.body.coins;

    userCoins -= 50;
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
        price: 50,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(userCoins);
  });

  it('dm - sockets', async () => {
    // Make user 0 premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    // create socket for communication
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(1);

    // Initial state - both users have no chats
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/chat')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      expect(res.body.length).to.equal(0);
    }

    // ======================================================
    // User 0 sends like to user 1

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('pending chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the pending chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal('1');
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile0);
    expect(res.lastMessage.text).to.equal('msg0');
    expect(res.lastMessage.sender).to.equal('0');
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg0');
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // ======================================================
    // User 1 approves the chat request

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the approved chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile1);
    expect(res.lastMessage.text).to.equal('msg0');
    expect(res.lastMessage.sender).to.equal('0');
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Both users should see the chat as non-pending
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].lastMessage.text).to.equal('msg0');
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg0');
    expect(res.body[0].lastMessage.sender).to.equal('0');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    const chatId = res.body[0]._id;

    // ======================================================
    // User 1 sends a message
    socketResponse = new Promise((resolve, reject) => {
      socket0.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.text).to.equal('msg1');
    expect(res.sender).to.equal('1');
    expect(res.chat).to.equal(chatId);
    assert(new Date(res.createdAt).getTime() - msgTime < 1000);

    // ======================================================
    // User 0 sends a gif

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.gif).to.equal(validResponseGif);
    expect(res.sender).to.equal('0');
    expect(res.chat).to.equal(chatId);
    assert(new Date(res.createdAt).getTime() - msgTime < 1000);

    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('follow-up dm', async () => {
    // create sockets
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(1);

    // User 0 sends initial dm to user 1
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg0',
        price: 50,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let coins0 = res.body.coins;

    // user 0 sends follow-up dm to user 1 - coins deducted
    socketPromise = getSocketPromise(socket1, 'pending chat');
    coins0 -= 50;
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '1',
        message: 'msg1',
        price: 50,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(coins0);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(coins0);

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res.pendingUser).to.equal('1');
    expect(res.numUnreadMessages).to.equal(2);
    expect(res.user).to.eql(userProfile0);
    expect(res.lastMessage.text).to.equal('msg1');
    expect(res.lastMessage.sender).to.equal('0');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].numUnreadMessages).to.equal(2);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessage.sender).to.equal('0');

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    expect(res.body[0].text).to.equal('msg1');
    expect(res.body[0].sender).to.equal('0');
    expect(res.body[1].text).to.equal('msg0');
    expect(res.body[1].sender).to.equal('0');

    // user 1 sends dm to user 0 - approves chat, no coins deducted
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    const coins1 = res.body.coins;

    socketPromise = getSocketPromise(socket0, 'approved chat');
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 1)
      .send({
        user: '0',
        message: 'msg2',
        price: 50,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(coins1);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(coins1);

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile1);
    expect(res.lastMessage.text).to.equal('msg1');
    expect(res.lastMessage.sender).to.equal('0');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(2);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessage.sender).to.equal('1');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessage.sender).to.equal('1');

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(3);
    expect(res.body[0].text).to.equal('msg2');
    expect(res.body[0].sender).to.equal('1');
    expect(res.body[1].text).to.equal('msg1');
    expect(res.body[1].sender).to.equal('0');
    expect(res.body[2].text).to.equal('msg0');
    expect(res.body[2].sender).to.equal('0');

    // user 1 sends another dm to user 0 - no coins deducted
    socketPromise = getSocketPromise(socket0, 'message');
    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 1)
      .send({
        user: '0',
        message: 'msg3',
        price: 50,
      });
    expect(res.status).to.equal(200);
    expect(res.body.coinsRemaining).to.equal(coins1);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(coins1);

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res.text).to.equal('msg3');
    expect(res.sender).to.equal('1');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(3);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].lastMessage.text).to.equal('msg3');
    expect(res.body[0].lastMessage.sender).to.equal('1');

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal('msg3');
    expect(res.body[0].lastMessage.sender).to.equal('1');

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    expect(res.body[0].text).to.equal('msg3');
    expect(res.body[0].sender).to.equal('1');
    expect(res.body[1].text).to.equal('msg2');
    expect(res.body[1].sender).to.equal('1');
    expect(res.body[2].text).to.equal('msg1');
    expect(res.body[2].sender).to.equal('0');
    expect(res.body[3].text).to.equal('msg0');
    expect(res.body[3].sender).to.equal('0');

    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('cannot create chat with self', async () => {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(422);

    // if there is a chat with self, it should not be returned
    chat = new Chat({
      users: ['0', '0'],
      pendingUser: '0',
    });
    await chat.save();

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('no dm - unread 1.8.5', async () => {
    // user 0 has upgraded to 1.8.5
    user = await User.findOne({ _id: 0 });
    user.appVersion = '1.8.5';
    user = await user.save();

    // user 1 has upgraded to 1.8.5
    user = await User.findOne({ _id: 1 });
    user.appVersion = '1.8.5';
    user = await user.save();

    // create socket for communication
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(1);

    // ======================================================
    // User 0 sends like without dm to user 1

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('pending chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the pending chat through the socket
    // with unread = 1
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal('1');
    expect(res.user).to.eql(userProfile0);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // ======================================================
    // User 1 approves the chat request

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the approved chat through the socket
    // with unread = 1
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.user).to.eql(userProfile1);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Both users should see the chat as non-pending
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // For user 0, unread = 0
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('no dm - sockets', async () => {
    // prior to 1.8.5, num unread for chats without dm should always be 0

    // user 0 has upgraded to 1.8.0
    user = await User.findOne({ _id: 0 });
    user.appVersion = '1.8.0';
    user = await user.save();

    // user 1 has upgraded to 1.8.0 yet
    user = await User.findOne({ _id: 1 });
    user.appVersion = '1.8.0';
    user = await user.save();

    // create socket for communication
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(1);

    // ======================================================
    // User 0 sends like without dm to user 1

    socketResponse = new Promise((resolve, reject) => {
      socket1.on('pending chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 should receive the pending chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal('1');
    expect(res.user).to.eql(userProfile0);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Pending chat should appear for user 1 only
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal('1');
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    // ======================================================
    // User 1 approves the chat request

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the approved chat through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.user).to.eql(userProfile1);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.lastMessage).to.equal(undefined);
    assert(new Date(res.lastMessageTime).getTime() - msgTime < 1000);

    // Both users should see the chat as non-pending
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].lastMessage).to.equal(undefined);
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    const chatId = res.body[0]._id;

    // ======================================================
    // User 1 sends a message
    socketResponse = new Promise((resolve, reject) => {
      socket0.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    msgTime = new Date().getTime();
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    userProfile1.karma = 5;

    // User 0 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.text).to.equal('msg1');
    expect(res.sender).to.equal('1');
    expect(res.chat).to.equal(chatId);
    assert(new Date(res.createdAt).getTime() - msgTime < 1000);

    // Both users should see the message in the chat
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile1);
    expect(res.body[0].numUnreadMessages).to.equal(2);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessage.sender).to.equal('1');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].numUnreadMessages).to.equal(0);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessage.sender).to.equal('1');
    assert(new Date(res.body[0].lastMessageTime).getTime() - msgTime < 1000);

    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('notification badge counts', async () => {
    // ======================================================
    // User 0 sends like to user 1

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 should have incremented notification badge count
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(1);

    // ======================================================
    // User 1 connects via socket

    const socket1 = await initSocket(1);

    // User 1 notification badge count should be reset
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);

    // ======================================================
    // User 1 approves the chat request

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(1);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);

    // ======================================================
    // User 0 opens the app

    res = await request(app)
      .put('/v1/user/openApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // User 0 notification badge count should be reset
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);

    // ======================================================
    // User 0 sends a message and gif

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(2);

    // ======================================================
    // User 1 sends a message and gif

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 1)
      .send({
        recipient: '0',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(2);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(2);

    // ======================================================
    // User 1 socket disconnects

    await destroySocket(socket1);

    // User 1 notification badge count should be reset
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(2);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);

    // ======================================================
    // User 0 closes the app

    res = await request(app)
      .put('/v1/user/closeApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // User 0 notification badge count should be reset
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);

    // ======================================================
    // User 0 sends a message and gif

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(2);

    // ======================================================
    // User 1 updates fcm token

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({
        fcmToken: 'token',
      });
    expect(res.status).to.equal(200);

    // User 1 notification badge count should be reset
    await new Promise((r) => setTimeout(r, 100));
    metadata = await UserMetadata.findOne({ user: 0 });
    expect(metadata.notificationBadgeCount).to.equal(0);
    metadata = await UserMetadata.findOne({ user: 1 });
    expect(metadata.notificationBadgeCount).to.equal(0);
  });

  it('feedback', async () => {
    const supportId = chatLib.BOO_SUPPORT_ID;

    // create support user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', supportId);
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', supportId)
      .send({
        firstName: 'Boo',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/education')
      .set('authorization', supportId)
      .send({
        education: 'education',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', supportId)
      .send({
        description: 'description',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/work')
      .set('authorization', supportId)
      .send({
        work: 'work',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', supportId)
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/handle')
      .set('authorization', supportId)
      .send({
        handle: 'boo',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', supportId)
      .send({ gender: 'female' });
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', supportId)
      .send({
        purpose: ['friends'],
      });
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', supportId)
      .send({
        year: 1990,
        month: 1,
        day: 1,
      });
    // Honolulu, HI
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', supportId)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);
    // mock upload two pictures
    user = await User.findOne({ _id: supportId });
    user.pictures.push('boo0');
    user.pictures.push('boo1');
    res = await user.save();

    // create socket for communication
    const socket0 = await initSocket(0);
    const socket1 = await initSocket(supportId);

    // ======================================================
    // User 0 sends feedback

    socketResponse0 = new Promise((resolve, reject) => {
      socket0.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });
    socketResponse1 = new Promise((resolve, reject) => {
      socket1.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', 0)
      .send({
        message: 'feedback',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numFeedbackSubmitted).to.equal(1);

    // User 0 should receive the support chat through the socket
    res = await socketResponse0;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(supportProfile);
    expect(res.lastMessage.text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.lastMessage.sender).to.equal(supportId);

    // Boo Support should receive the support chat through the socket
    res = await socketResponse1;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(userProfile0);
    expect(res.lastMessage.text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.lastMessage.sender).to.equal(supportId);

    // Get support chat
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(supportProfile);
    expect(res.body[0].lastMessage.text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].lastMessage.sender).to.equal(supportId);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', supportId);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(userProfile0);
    expect(res.body[0].lastMessage.text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].lastMessage.sender).to.equal(supportId);

    const chatId = res.body[0]._id;

    // Get messages in support chat
    res = await request(app)
      .get('/v1/message')
      .query({ user: supportId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body.length).to.equal(2);
    expect(res.body[0].text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].sender).to.equal(supportId);
    expect(res.body[1].text).to.equal('feedback');
    expect(res.body[1].sender).to.equal('0');

    res = await request(app)
      .get('/v1/message')
      .query({ user: 0 })
      .set('authorization', supportId);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body.length).to.equal(2);
    expect(res.body[0].text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].sender).to.equal(supportId);
    expect(res.body[1].text).to.equal('feedback');
    expect(res.body[1].sender).to.equal('0');

    socketResponse = new Promise((resolve, reject) => {
      socket0.on('message', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .post('/v1/message')
      .set('authorization', supportId)
      .send({
        user: '0',
        text: 'reply',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the message through the socket
    res = await socketResponse;
    console.log(res);
    expect(res.text).to.equal('reply');
    expect(res.sender).to.equal(supportId);
    expect(res.chat).to.equal(chatId);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    expect(res.body[0].user).to.eql(supportProfile);
    expect(res.body[0].lastMessage.text).to.equal('reply');
    expect(res.body[0].lastMessage.sender).to.equal(supportId);

    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', 0)
      .send({
        message: 'feedback2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].pendingUser).to.equal(null);
    expect(res.body[0].numUnreadMessages).to.equal(2);
    expect(res.body[0].user).to.eql(supportProfile);
    expect(res.body[0].lastMessage.text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].lastMessage.sender).to.equal(supportId);

    // Get messages in support chat
    res = await request(app)
      .get('/v1/message')
      .query({ user: supportId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body.length).to.equal(5);
    expect(res.body[0].text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].sender).to.equal(supportId);
    expect(res.body[1].text).to.equal('feedback2');
    expect(res.body[1].sender).to.equal('0');

    // user 0 blocks boo support
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({
        user: supportId,
      });
    expect(res.status).to.equal(200);

    // sending feedback should still work
    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', 0)
      .send({
        message: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', supportId)
      .send({
        user: '0',
        text: 'reply',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/message')
      .query({ user: supportId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body.length).to.equal(3);
    expect(res.body[0].text).to.equal('reply');
    expect(res.body[0].sender).to.equal(supportId);
    expect(res.body[1].text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[1].sender).to.equal(supportId);
    expect(res.body[2].text).to.equal('feedback');
    expect(res.body[2].sender).to.equal('0');

    // sending message to the support via message route will also create automated support reply
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: supportId,
        text: 'message',
      });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));

    res = await request(app)
      .get('/v1/message')
      .query({ user: supportId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(5);
    expect(res.body[0].text).to.equal(chatLib.AUTOMATED_SUPPORT_REPLY);
    expect(res.body[0].sender).to.equal(supportId);

    // different feedback for banned users

    await initApp(1);
    let bannedSocket = await initSocket(1);

    // shadowBan user
    await User.updateOne({ _id: '1' }, { $set: { shadowBanned: true } });
    let curSocketPromise = new Promise((resolve, reject) => {
      bannedSocket.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });

    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', 1)
      .send({
        message: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await curSocketPromise;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(supportProfile);
    expect(res.lastMessage.text).to.equal("I'm sorry to hear. For better assistance, please email <NAME_EMAIL> with your Boo ID and a description of the issue. Thank you for your patience.");
    expect(res.lastMessage.sender).to.equal(supportId);

    await destroySocket(bannedSocket);

    await initApp(2);
    bannedSocket =await initSocket(2);
    // ban user
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 0,
        longitude: 0,
      });
    expect(res.status).to.equal(200);
    curSocketPromise=new Promise((resolve, reject) => {
      bannedSocket.on('approved chat', (data4Client) => {
        resolve(data4Client);
      });
      setTimeout(() => {
        reject(new Error('Failed to get reponse, connection timed out...'));
      }, 1000);
    });


    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', 2)
      .send({
        message: 'feedback',
      });
    expect(res.status).to.equal(200);

    // User 0 should receive the support chat through the socket
    res = await curSocketPromise;
    console.log(res);
    expect(res.pendingUser).to.equal(null);
    expect(res.numUnreadMessages).to.equal(1);
    expect(res.user).to.eql(supportProfile);
    expect(res.lastMessage.text).to.equal("I'm sorry to hear. For better assistance, please email <NAME_EMAIL> with your Boo ID and a description of the issue. Thank you for your patience.");
    expect(res.lastMessage.sender).to.equal(supportId);
    await destroySocket(bannedSocket);

    // cleanup
    await destroySocket(socket0);
    await destroySocket(socket1);
  });

  it('boo support sending feedback', async () => {
    const supportId = chatLib.BOO_SUPPORT_ID;

    // create support user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', supportId);
    expect(res.status).to.equal(200);

    // ======================================================
    // boo support sends feedback

    res = await request(app)
      .post('/v1/feedback')
      .set('authorization', supportId)
      .send({
        message: 'feedback',
      });
    expect(res.status).to.equal(200);

    // no chat created
    res = await request(app)
      .get('/v1/chat')
      .set('authorization', supportId);
    console.log(res.body[0]);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });
});

describe('friends feed', async () => {
  const numUsers = 5;
  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({
          appVersion: '1.11.60',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      // mock upload two pictures
      const user = await User.findOne({ _id: uid });
      user.pictures.push('picture0');
      user.pictures.push('picture1');
      res = await user.save();
    }
  });

  it('see only friends feed and not other feed', async () => {
    // load qod
    await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });

    // user 0 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'my title',
        text: 'my text',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'friend\'s title',
        text: 'friend\'s text',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // no friends - empty friends feed
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // get friend's feed excluding other feeds
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);
    expect(res.body.questions[0].title).to.equal('friend\'s title');
    expect(res.body.questions[0].text).to.equal('friend\'s text');

    // 1.11.59 should also see friend's post
    user = await User.findOne({ _id: 0 });
    user.appVersion = '1.11.59';
    await user.save();

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);
  });

  it('should see posts created by my friends and posts my friends commented on', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // user 2 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'kpop',
        title: 'title from user 2',
        text: 'text from user 2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // user 0 comment on question from user 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q2Id,
        text: 'User0 has one comment on question3',
        parentId: q2Id,
      });
    c1 = res.body._id;

    // get the comment
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c1);

    // friends tab feed must include posts that friends commented on
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 1);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);
    expect(res.body.questions[0].title).to.equal('title from user 2');
    expect(res.body.questions[0].text).to.equal('text from user 2');
  });

  it('should not see posts that were created by non-friends and have comments from users that are not friends', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title from user 1',
        text: 'text from user 1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 2 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'User2 has one comment on question1',
        parentId: q1Id,
      });
    c1 = res.body._id;

    // get the comment
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 2);

    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c1);

    // user 2 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'kpop',
        title: 'title from user 2',
        text: 'text from user 2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // friends tab feed must NOT include posts created by non-friends and posts without comment from friends
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
  });

  it('should list friends that commented on each post', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 sends like to user 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    // User 2 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 sends like to user 3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    // User 3 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title from user 1',
        text: 'text from user 1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 0 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'User0 has one comment on question1',
        parentId: q1Id,
      });

    // user 1 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'User1 has one comment on question1',
        parentId: q1Id,
      });

    // user 2 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'User2 has one comment on question1',
        parentId: q1Id,
      });

    // user 3 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'User3 has one comment on question1',
        parentId: q1Id,
      });

    // friends tab feed must include list of friends that commented (excluding current user)
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(2);
    expect(res.body.questions[0].friendsThatCommented[0]).to.eql(
      { _id: '2', firstName: 'name2', picture: 'picture0' },
    );
    expect(res.body.questions[0].friendsThatCommented[1]).to.eql(
      { _id: '3', firstName: 'name3', picture: 'picture0' },
    );

    // shadow banned friends should not be in the list
    user = await User.findById('3');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(1);
    expect(res.body.questions[0].friendsThatCommented[0]).to.eql(
      { _id: '2', firstName: 'name2', picture: 'picture0' },
    );
  });

  describe('friends who commented - special cases', async () => {
    let q1Id; let
      c2Id;

    beforeEach(async () => {
      // User 0 sends like to user 2
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: '2',
        });
      expect(res.status).to.equal(200);

      // User 2 approves the chat request
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', 2)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);

      // user 1 creates question
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'kpop',
          title: 'title from user 1',
          text: 'text from user 1',
        });
      expect(res.status).to.equal(200);
      q1Id = res.body._id;

      // user 2 comment on question from user 1
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 2)
        .send({
          questionId: q1Id,
          text: 'User2 has one comment on question1',
          parentId: q1Id,
        });
      c2Id = res.body._id;

      // list should include user 2
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[0].friendsThatCommented.length).to.equal(1);
      expect(res.body.questions[0].friendsThatCommented[0]._id).to.equal('2');
    });

    it('deleting comment should remove from friendsThatCommented', async () => {

      // user 2 comments again
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 2)
        .send({
          questionId: q1Id,
          text: 'User2 has two comments on question1',
          parentId: q1Id,
        });
      c3Id = res.body._id;

      // user 2 deletes first comment
      res = await request(app)
        .delete('/v1/comment')
        .set('authorization', 2)
        .query({ commentId: c2Id });
      expect(res.status).to.equal(200);

      // list should still include user 2
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[0].friendsThatCommented.length).to.equal(1);
      expect(res.body.questions[0].friendsThatCommented[0]._id).to.equal('2');

      // user 2 deletes second comment
      res = await request(app)
        .delete('/v1/comment')
        .set('authorization', 2)
        .query({ commentId: c3Id });
      expect(res.status).to.equal(200);

      // list should not include user 2
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'explore' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[0].friendsThatCommented.length).to.equal(0);
    });

    it('friends with banned comments should not be in the list', async () => {
      // ban comment from user 2
      user = await User.findOne({ _id: 0 });
      user.admin = true;
      user.adminPermissions = { all: true };
      await user.save();

      res = await request(app)
        .put('/v1/admin/banComment')
        .set('authorization', 0)
        .send({
          commentId: c2Id,
        });
      expect(res.status).to.equal(200);

      // post should not appear in friends feed
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // user 2 should not be in the list of friends who commented
      res = await request(app)
        .get('/v1/question')
        .query({ questionId: q1Id })
        .set('authorization', 0);
      expect(res.body.question._id).to.equal(q1Id);
      expect(res.body.question.friendsThatCommented.length).to.equal(0);
    });

    it('banned friends should not appear in the list', async () => {
      // ban user 2
      user = await User.findById('2');
      user.shadowBanned = true;
      await user.save();

      // post should not appear in friends feed
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // user 2 should not be in the list of friends who commented
      res = await request(app)
        .get('/v1/question')
        .query({ questionId: q1Id })
        .set('authorization', 0);
      expect(res.body.question._id).to.equal(q1Id);
      expect(res.body.question.friendsThatCommented.length).to.equal(0);
    });

    it('old versions should not see friends who commented in friends feed', async () => {
      user = await User.findOne({ _id: 0 });
      user.appVersion = '1.11.59';
      await user.save();

      // now post should not be in feed
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);
    });

    it('old questions should not appear in friends feed and should have empty list', async () => {
      // manually change createdAt
      const doc = await Question.findById(q1Id);
      doc.createdAt = new Date('2022-09-16T10:00:00');
      await doc.save();

      // now post should not be in feed
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // user 2 should not be in the list of friends who commented
      res = await request(app)
        .get('/v1/question')
        .query({ questionId: q1Id })
        .set('authorization', 0);
      expect(res.body.question._id).to.equal(q1Id);
      expect(res.body.question.friendsThatCommented.length).to.equal(0);
    });

    it('backwards compatibility - questions without usersWithBannedComments', async () => {
      // manually remove usersWithBannedComments
      const doc = await Question.findById(q1Id);
      doc.usersWithBannedComments = undefined;
      await doc.save();

      // should not error
      res = await request(app)
        .get('/v1/question/feed')
        .query({ filter: 'friends' })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[0].friendsThatCommented.length).to.equal(1);
      expect(res.body.questions[0].friendsThatCommented[0]._id).to.equal('2');
    });
  });

  it('should NOT add other users to the friends list and should NOT add friends that did NOT comment on the post', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title from user 1',
        text: 'text from user 1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 0 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'User0 has one comment on question1',
        parentId: q1Id,
      });

    // user 1 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'User1 has one comment on question1',
        parentId: q1Id,
      });

    // user 2 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'User2 has one comment on question1',
        parentId: q1Id,
      });

    // user 3 comment on question from user 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'User3 has one comment on question1',
        parentId: q1Id,
      });

    // friendsThatCommented must NOT include users that are not friends
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(0);

    // User 0 sends like to user 4
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '4',
      });
    expect(res.status).to.equal(200);

    // User 4 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 4)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // friendsThatCommented must NOT include friends that did not comment
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(0);

    // User 0 sends like to user 3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    // User 3 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
    expect(res.body.questions[0].friendsThatCommented.length).to.equal(1);
    expect(res.body.questions[0].friendsThatCommented[0]).to.eql(
      { _id: '3', firstName: 'name3', picture: 'picture0' },
    );
  });

  it('should see all friends posts regardless of language', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title from user 1',
        text: 'text from user 1',
        language: 'fr',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // friends feed regardless of language
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
  });

  it('should update friend collection and use it for friend filter', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // set user 0 to premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    for (let uid = 1; uid <=10; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);

      // User 0 sends like to user n
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: uid.toString(),
        });
      expect(res.status).to.equal(200);

      // User n approves the chat request
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', uid)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);
    }

    // user 1 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title from user 1',
        text: 'text from user 1',
        language: 'fr',
      });
    expect(res.status).to.equal(200);

    // friends feed regardless of language
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'friends' })
      .set('authorization', 0);

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].title).to.equal('title from user 1');
    expect(res.body.questions[0].text).to.equal('text from user 1');
  });

  it('should update friend list when user approves or unmatch friend', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 0 should see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    let user0Friends = await FriendList.findOne({ userId: 0 });
    expect(user0Friends.friends.length).to.equal(1);
    expect(user0Friends.friends[0]).to.equal('1');

    let user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(1);
    expect(user1Friends.friends[0]).to.equal('0');


    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // User 0 should not see user 1 in chat list
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    user0Friends = await FriendList.findOne({ userId: 0 });
    expect(user0Friends.friends.length).to.equal(0);

    user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(0);
  });

  it('should remove friend list when user blocks friend', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    let user0Friends = await FriendList.findOne({ userId: 0 });
    expect(user0Friends.friends.length).to.equal(1);
    expect(user0Friends.friends[0]).to.equal('1');

    let user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(1);
    expect(user1Friends.friends[0]).to.equal('0');


    // block user
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    user0Friends = await FriendList.findOne({ userId: 0 });
    expect(user0Friends.friends.length).to.equal(0);

    user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(0);
  });

  it('should remove friend list when user account is deleted', async () => {
    const uid = constants.IMMEDIATE_DELETION_ID;

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);
    expect(res.status).to.equal(200);

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', uid)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: uid,
      });
    expect(res.status).to.equal(200);

    let user0Friends = await FriendList.findOne({ userId: uid });
    expect(user0Friends.friends.length).to.equal(1);
    expect(user0Friends.friends[0]).to.equal('1');

    let user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(1);
    expect(user1Friends.friends[0]).to.equal(uid);


    // delete user account
    res = await request(app)
      .delete('/v1/user')
      .set('authorization', uid);
    expect(res.status).to.equal(200);

    const userFriends = await FriendList.findOne({ userId: uid });
    expect(userFriends).to.be.null;

    user1Friends = await FriendList.findOne({ userId: 1 });
    expect(user1Friends.friends.length).to.equal(0);
  });
});

describe('pending vs approved vs sent chats', async () => {
  it('smoke test', async () => {
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.19' });
      expect(res.status).to.equal(200);
      expect(res.body.numPendingChats).to.equal(0);
    }

    // users 1 and 2 send like to user 0
    for (let i = 1; i < 3; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    for (let i = 0; i < 3; i++) {
      // no approved chats
      res = await request(app)
        .get('/v1/chat/approved')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);
    }
    for (let i = 1; i < 3; i++) {
      // users 1 and 2 have 0 pending chats, 1 sent chat
      res = await request(app)
        .get('/v1/chat/pending')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);

      res = await request(app)
        .get('/v1/chat/sent')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(1);
      expect(res.body.chats[0].pendingUser).to.equal('0');
      expect(res.body.chats[0].numUnreadMessages).to.equal(0);
      expect(res.body.chats[0].user._id).to.equal('0');
    }

    // user 0 has 2 pending chats, 0 sent chats
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.numPendingChats).to.equal(2);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // get pending chats for user 0
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    chat2Id = res.body.chats[0]._id;
    chat1Id = res.body.chats[1]._id;

    // user 2 not allowed to approve/reject/unmatch chat between users 1 and 0
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);
    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 2)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 2)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);

    // user 1 not allowed to approve/reject
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);
    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 1)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);

    // user 0 approves user 1 only
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(200);

    // double approving or rejecting after approval should fail
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);
    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 0)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);

    // one fewer pending chat
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.numPendingChats).to.equal(1);

    // get pending chats
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('2');

    // get approved chats
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('1');

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('0');

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // get sent chats
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(200);

    // double unmatch should return error
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 0)
      .query({ chatId: chat1Id });
    expect(res.status).to.equal(404);

    for (let i = 0; i < 3; i++) {
      // no approved chats
      res = await request(app)
        .get('/v1/chat/approved')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);
    }

    // user 0 rejects user 2
    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 0)
      .query({ chatId: chat2Id });
    expect(res.status).to.equal(200);

    for (let i = 0; i < 3; i++) {
      // no pending, approved, or sent chats
      res = await request(app)
        .get('/v1/chat/approved')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);
      res = await request(app)
        .get('/v1/chat/pending')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);
      res = await request(app)
        .get('/v1/chat/sent')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if(i == 2){ // rejected chats stays in sent chat
        expect(res.body.chats.length).to.equal(1);
      }else{
        expect(res.body.chats.length).to.equal(0);
      }
    }
  });

  it('retain deleted chats and messages for 30 days', async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.19' });
      expect(res.status).to.equal(200);
    }

    // create a match and send a message
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // chat and message should still be retained
    expect((await Chat.find()).length).to.equal(1);
    expect((await Message.find()).length).to.equal(1);

    // user cannot view deleted chat or message
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: 1 });
    expect(res.status).to.equal(404);

    // users can make a new chat
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('1');

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: 1 });
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('msg2');
  });

  it('profileDetails isMatched', async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.19' });
      expect(res.status).to.equal(200);
    }

    // not matched
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);

    // user 1 sends like to user 0
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // still not matched
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);

    // user 0 approves user 1
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    // now matched
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);

    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // not matched anymore
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(false);
  });

  it('no time limit on viewing sent chats', async () => {
    let clock;
    try {
      clock = sinon.useFakeTimers();

      for (let i = 0; i < 2; i++) {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', i);
        expect(res.status).to.equal(200);
      }

      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 1)
        .send({ user: '0' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/chat/sent')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(1);

      // 1 week
      clock.tick(7 * 24 * 3600 * 1000);

      res = await request(app)
        .get('/v1/chat/sent')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(1);

      res = await request(app)
        .get('/v1/chat/sent')
        .set('authorization', 1)
        .query({ before: 1 });
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(1);
    } finally {
      clock.restore();
    }
  });

  it('sent chats should not be hidden on pending report', async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
  });

  it('chat pagination', async () => {
    const numUsers = pageSize * 3;
    for (let i = 0; i < numUsers + 1; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.19' });
      expect(res.status).to.equal(200);
    }

    // every user sends a like
    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({ user: numUsers.toString() });
      expect(res.status).to.equal(200);
    }

    // shadow ban the last set of users
    for (let i = 2 * pageSize; i < 3 * pageSize; i++) {
      user = await User.findById(i);
      user.shadowBanned = true;
      await user.save();
    }

    // get num pending chats
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.numPendingChats).to.equal(pageSize * 2);

    // get first page
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.chats[i].user._id).to.equal(`${numUsers - 1 - pageSize - i}`);
    }

    // should not error on invalid date - should return first page
    res = await request(app)
      .get('/v1/chat/pending')
      .query({ before: 'undefined' })
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.chats[i].user._id).to.equal(`${numUsers - 1 - pageSize - i}`);
    }

    // get second page
    before = res.body.chats[pageSize - 1].lastMessageTime;
    res = await request(app)
      .get('/v1/chat/pending')
      .query({ before })
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.chats[i].user._id).to.equal(`${numUsers - 1 - 2 * pageSize - i}`);
    }

    // try get third page - no more
    before = res.body.chats[pageSize - 1].lastMessageTime;
    res = await request(app)
      .get('/v1/chat/pending')
      .query({ before })
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // approve all the chats
    for (let i = 0; i < 2 * pageSize; i++) {
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', numUsers)
        .send({ user: i.toString() });
      expect(res.status).to.equal(200);
    }

    // get first page
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.chats[i].user._id).to.equal(`${numUsers - 1 - pageSize - i}`);
    }

    // get second page
    before = res.body.chats[pageSize - 1].lastMessageTime;
    res = await request(app)
      .get('/v1/chat/approved')
      .query({ before })
      .set('authorization', numUsers);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.chats[i].user._id).to.equal(`${numUsers - 1 - 2 * pageSize - i}`);
    }
  });
});

describe('expired chats', async () => {
  let clock; let chatId; let
    initialCoins;

  beforeEach(async () => {
    clock = sinon.useFakeTimers();

    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    chatId = res.body.chats[0]._id;

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    initialCoins = res.body.coins;
  });

  afterEach(async () => {
    clock.restore();
  });

  it('sender reactivates expired chat', async () => {
    // not expired yet
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal(72 * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(false);

    // no coins deducated if try to reactivate
    res = await request(app)
      .put('/v1/coins/reactivateChat')
      .set('authorization', 0)
      .query({ chatId })
      .send({ price: 50 });
    expect(res.status).to.equal(200);
    expect(new Date(res.body.expirationDate).getTime()).to.equal(72 * 3600 * 1000);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(initialCoins);

    // 1 week
    clock.tick(7 * 24 * 3600 * 1000);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    initialCoins = res.body.coins;

    // expired
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal(72 * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(true);

    // reactivate
    res = await request(app)
      .put('/v1/coins/reactivateChat')
      .set('authorization', 0)
      .query({ chatId })
      .send({ price: 50 });
    expect(res.status).to.equal(200);
    expect(new Date(res.body.expirationDate).getTime()).to.equal((7 * 24 + 72) * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(false);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(initialCoins - 50);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal((7 * 24 + 72) * 3600 * 1000);
  });

  it('send super like to expired chat', async () => {
    // 1 week
    clock.tick(7 * 24 * 3600 * 1000);

    // expired
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal(72 * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(true);

    // send super like
    user = await User.findById('0');
    user.numSuperLikes = 1;
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // chat should be reactivated and marked by super like
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(false);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].initiatedBySuperLike).to.equal(true);
    expect(res.body.chats[0].expirationDate).to.equal();

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].initiatedBySuperLike).to.equal(true);
    expect(res.body.chats[0].expirationDate).to.equal();
  });

  it('god mode - reactivate chat for free', async () => {
    user = await User.findOne({ _id: 0 });
    user.godModeExpiration = Date.now() + 10*86400000;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.reactivateChat.price).to.equal(0);

    // 1 week
    clock.tick(7 * 24 * 3600 * 1000);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    initialCoins = res.body.coins;

    // expired
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal(72 * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(true);

    // reactivate
    res = await request(app)
      .put('/v1/coins/reactivateChat')
      .set('authorization', 0)
      .query({ chatId })
      .send({ price: 0 });
    expect(res.status).to.equal(200);
    expect(new Date(res.body.expirationDate).getTime()).to.equal((7 * 24 + 72) * 3600 * 1000);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(false);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins).to.equal(initialCoins);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.chats[0].expirationDate).getTime()).to.equal((7 * 24 + 72) * 3600 * 1000);
  });

  it('approved chat does not have expiration date', async () => {
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].expirationDate).to.equal();

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isChatExpired).to.equal(false);
  });

  it('get individual chat', async () => {
    let chatObj;

    // not expired yet
    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    chatObj = res.body.chats[0];

    res = await request(app)
      .get('/v1/chat/individualChat')
      .set('authorization', 0)
      .query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.chat).to.eql(chatObj);

    // expired
    clock.tick(7 * 24 * 3600 * 1000);

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    chatObj = res.body.chats[0];

    res = await request(app)
      .get('/v1/chat/individualChat')
      .set('authorization', 0)
      .query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.chat).to.eql(chatObj);
  });
});

describe('pinned chats', async () => {
  beforeEach(async () => {
    for (let i = 0; i < 5; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 1; i < 5; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: i.toString(),
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', i)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);
    }
  });

  it('pin chat', async () => {
    let chatId;

    // test backwards compatibility
    chat = await Chat.findOne();
    chat.pinned = undefined;
    await chat.save();

    // chat 1 is last
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');
    expect(res.body.chats[2].user._id).to.equal('2');
    expect(res.body.chats[3].user._id).to.equal('1');
    expect(res.body.chats[3].pinned).to.equal();
    chatId = res.body.chats[3]._id;

    // pin chat 1
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);

    // chat 1 should be first
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[0].pinned).to.equal(true);
    expect(res.body.chats[1].user._id).to.equal('4');
    expect(res.body.chats[2].user._id).to.equal('3');
    expect(res.body.chats[3].user._id).to.equal('2');

    // send message to chat 2
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '2',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // chat 1 should still be first
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('2');
    expect(res.body.chats[2].user._id).to.equal('4');
    expect(res.body.chats[3].user._id).to.equal('3');

    // unpin chat 1
    res = await request(app)
      .patch('/v1/chat/unpin')
      .set('authorization', 0)
      .send({ chatId });
    expect(res.status).to.equal(200);

    // chat 1 should be last
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('2');
    expect(res.body.chats[1].user._id).to.equal('4');
    expect(res.body.chats[2].user._id).to.equal('3');
    expect(res.body.chats[3].user._id).to.equal('1');
    expect(res.body.chats[3].pinned).to.equal();
  });

  it('multiple users pin chat', async () => {
    // fill up user 1 chats
    for (let i = 2; i < 5; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 1)
        .send({
          user: i.toString(),
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', i)
        .send({
          user: '1',
        });
      expect(res.status).to.equal(200);
    }

    // user 0 pins chat 1
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('4');
    expect(res.body.chats[2].user._id).to.equal('3');
    expect(res.body.chats[3].user._id).to.equal('2');

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');
    expect(res.body.chats[2].user._id).to.equal('2');
    expect(res.body.chats[3].user._id).to.equal('0');

    // user 1 pins chat 3
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 1)
      .send({ user: '3' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('4');
    expect(res.body.chats[2].user._id).to.equal('3');
    expect(res.body.chats[3].user._id).to.equal('2');

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('4');
    expect(res.body.chats[2].user._id).to.equal('2');
    expect(res.body.chats[3].user._id).to.equal('0');
  });

  it('pagination with pinned query param', async () => {
    sinon.stub(constants, 'getPageSize').returns(2);
    let before; let
      pinned;

    // pin chat 3
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '3' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('4');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('2');
    expect(res.body.chats[1].user._id).to.equal('1');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 1
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('1');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('2');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 4
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '4' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('2');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 2
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('2');
    expect(res.body.chats[1].user._id).to.equal('1');

    before = res.body.chats[1].lastMessageTime;
    pinned = res.body.chats[1].pinned;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ before, pinned });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);
  });

  it('pagination with beforeId query param', async () => {
    sinon.stub(constants, 'getPageSize').returns(2);
    let beforeId;

    // pin chat 3
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '3' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('4');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('2');
    expect(res.body.chats[1].user._id).to.equal('1');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 1
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('1');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('2');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 4
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '4' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('2');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // pin chat 2
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('2');
    expect(res.body.chats[1].user._id).to.equal('1');

    beforeId = res.body.chats[1]._id;
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);
  });

  it('messaged chat', async () => {
    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('3');
    expect(res.body.chats[2].user._id).to.equal('2');
    expect(res.body.chats[3].user._id).to.equal('1');

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 3)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('1');

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('2');

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('0');

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('0');

    // test backfill
    await Chat.updateMany(
      {},
      { $unset: { messaged: 1 } },
    );

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('1');
    expect(res.body.chats[2].user._id).to.equal('4');
    expect(res.body.chats[3].user._id).to.equal('2');

    await chatLib.backfillMessagedField();

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('3');
    expect(res.body.chats[1].user._id).to.equal('1');

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('4');
    expect(res.body.chats[1].user._id).to.equal('2');

    // pin chat
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].user._id).to.equal('1');
    expect(res.body.chats[1].user._id).to.equal('3');
  });

  it('sent vs not messaged chat', async () => {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/notMessaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('0');

    res = await request(app)
      .get('/v1/chat/sent')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.equal('2');
  });

});

describe('chat', async () => {
  beforeEach(async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 1; i < 2; i++) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: i.toString(),
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', i)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);
    }
  });

  it('mark unread', async () => {
    let chatId;

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].numUnreadMessages).to.equal(1);
    chatId = res.body.chats[0]._id;

    res = await request(app)
      .put('/v1/message/seen')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].numUnreadMessages).to.equal(0);

    res = await request(app)
      .patch('/v1/chat/markUnread')
      .set('authorization', 0)
      .query({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].numUnreadMessages).to.equal(1);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].numUnreadMessages).to.equal(0);
  });

  it('hide chat', async () => {
    let chatId;

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    chatId = res.body.chats[0]._id;

    // user 0 hides chat
    res = await request(app)
      .patch('/v1/chat/hide')
      .set('authorization', 0)
      .query({ chatId });
    expect(res.status).to.equal(200);

    // hidden for user 0, visible for user 1
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // user 1 hides chat
    res = await request(app)
      .patch('/v1/chat/hide')
      .set('authorization', 1)
      .query({ chatId });
    expect(res.status).to.equal(200);

    // hidden for user 0 and user 1
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // user 0 sends message to user 1
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // chat is now visible for user 0 and user 1
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
  });

  it('beforeId not found', async () => {
    res = await request(app)
      .get('/v1/chat/approved')
      .set('authorization', 0)
      .query({ beforeId: '6280590c36459e153770290f' });
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);
  });
});

it('get contacts', async () => {
  sinon.stub(constants, 'getPageSize').returns(2);

  for (let i = 0; i < 7; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // create hidden chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  let chatId;
  res = await request(app)
    .get('/v1/chat/approved')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
  chatId = res.body.chats[0]._id;

  res = await request(app)
    .patch('/v1/chat/hide')
    .set('authorization', 0)
    .query({ chatId });
  expect(res.status).to.equal(200);

  // create approved chats
  for (let i = 2; i < 5; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: i.toString(),
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', i)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
  }

  // create sent chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({ user: '5' });
  expect(res.status).to.equal(200);

  // create pending chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 6)
    .send({ user: '0' });
  expect(res.status).to.equal(200);

  // create group chat
  res = await request(app)
    .patch('/v1/chat/createGroupChat')
    .set('authorization', 0)
    .send({ users: ['0', '1', '2'] });
  expect(res.status).to.equal(200);

  // get all contacts - only returns first page
  res = await request(app)
    .get('/v1/chat/allContacts')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('4');
  expect(res.body.chats[1].user._id).to.equal('3');

  // get paginated contacts - latest
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('4');
  expect(res.body.chats[1].user._id).to.equal('3');

  beforeId = res.body.chats[res.body.chats.length - 1]._id;
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('2');
  expect(res.body.chats[1].user._id).to.equal('1');

  beforeId = res.body.chats[res.body.chats.length - 1]._id;
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

  // get paginated contacts - earliest
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest' });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('1');
  expect(res.body.chats[1].user._id).to.equal('2');

  beforeId = res.body.chats[res.body.chats.length - 1]._id;
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest', beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('3');
  expect(res.body.chats[1].user._id).to.equal('4');

  beforeId = res.body.chats[res.body.chats.length - 1]._id;
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest', beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

  // ban 1, 2
  for (let i = 1; i < 3; i++) {
    user = await User.findById(i);
    user.shadowBanned = true;
    await user.save();
  }

  res = await request(app)
    .get('/v1/chat/allContacts')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('4');
  expect(res.body.chats[1].user._id).to.equal('3');

  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest' });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].user._id).to.equal('3');
  expect(res.body.chats[1].user._id).to.equal('4');

  beforeId = res.body.chats[res.body.chats.length - 1]._id;
  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest', beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

  // unmatch user 4
  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 0)
    .send({ user: '4' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/allContacts')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
  expect(res.body.chats[0].user._id).to.equal('3');

  res = await request(app)
    .get('/v1/chat/contacts')
    .set('authorization', 0)
    .query({ sort: 'earliest' });
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
  expect(res.body.chats[0].user._id).to.equal('3');
});

it('user id hash', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // create chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  const originalChat = await Chat.findOne().lean();
  expect(originalChat.userIdHash).to.equal('0-1');

  // test backfill
  let chat;
  await Chat.updateMany(
    {},
    { $unset: { userIdHash: 1 } },
  );
  chat = await Chat.findOne().lean();
  expect(chat.userIdHash).to.equal();

  await chatLib.backfillUserIdHash();
  chat = await Chat.findOne().lean();
  expect(chat.userIdHash).to.equal('0-1');
  expect(chat).to.eql(originalChat);

  console.log(chat);
});

it('pending and createdBy', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  let chat;

  // pending chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  const originalChat = await Chat.findOne().lean();
  expect(originalChat.pending).to.equal(true);
  expect(originalChat.createdBy).to.equal('0');

  // test backfill
  await Chat.updateMany(
    {},
    { $unset: { pending: 1, createdBy: 1 } },
  );
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal();
  expect(chat.createdBy).to.equal();

  await chatLib.backfillPending();
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal(true);
  expect(chat.createdBy).to.equal('0');
  expect(chat).to.eql(originalChat);

  console.log(chat);

  // approve chat
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal(false);
  expect(chat.createdBy).to.equal('0');

  // test backfill
  await Chat.updateMany(
    {},
    { $unset: { pending: 1, createdBy: 1 } },
  );
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal();
  expect(chat.createdBy).to.equal();

  await chatLib.backfillPending();
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal(false);
  expect(chat.createdBy).to.equal();

  console.log(chat);

  // test case where pendingUser is undefined
  await Chat.updateMany(
    {},
    { $unset: { pending: 1, createdBy: 1, pendingUser: 1 } },
  );
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal();
  expect(chat.createdBy).to.equal();
  expect(chat.pendingUser).to.equal();

  await chatLib.backfillPending();
  chat = await Chat.findOne().lean();
  expect(chat.pending).to.equal(false);
  expect(chat.createdBy).to.equal();

  console.log(chat);
});

it('blocked user should not be able to send dm', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // user 1 sends like to user 0
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

  // user 0 blocks user 1
  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

  // user 1 should not be able to send dm to user 0
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: '0',
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(404);

  // user 0 should not see dm
  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);
});

it('send like to potential match', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // not potential match - should not be approved
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  expect(res.body.approvedChat).to.equal();

  // potential match - chat should be approved
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.approvedChat).to.not.equal();
  approvedChat = res.body.approvedChat;

  // correct some fields
  approvedChat.user.numFollowers = 1;
  approvedChat.users[0].numFollowers = 1;

  res = await request(app)
    .get('/v1/chat/approved')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.chats).to.eql([approvedChat]);
});

it('send dm to potential match', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // not potential match - should not be approved
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);
  expect(res.body.approvedChat).to.equal();

  // potential match - chat should be approved
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: '0',
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.approvedChat).to.not.equal();
  approvedChat = res.body.approvedChat;

  // correct some fields
  approvedChat.user.numFollowers = 1;
  approvedChat.users[0].numFollowers = 1;

  res = await request(app)
    .get('/v1/chat/approved')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.chats).to.eql([approvedChat]);
});

it('send super like to potential match', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    user = await User.findById(i.toString());
    user.numSuperLikes = 1;
    res = await user.save();
  }

  // not potential match - should not be approved
  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);
  expect(res.body.approvedChat).to.equal();

  // potential match - chat should be approved
  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 1)
    .send({
      user: '0',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.approvedChat).to.not.equal();
  approvedChat = res.body.approvedChat;

  // correct some fields
  approvedChat.user.numFollowers = 1;
  approvedChat.users[0].numFollowers = 1;

  res = await request(app)
    .get('/v1/chat/approved')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(JSON.stringify(res.body,null,2));
  expect(res.body.chats).to.eql([approvedChat]);
});

it('pass on potential match', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  // not potential match
  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '2',
    });
  expect(res.status).to.equal(200);
  expect(res.body.missedPotentialMatch).to.equal();

  // potential match
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/pass')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  expect(res.body.missedPotentialMatch).to.equal(true);

  // pending chat not removed
  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
});

it('numPendingLikes - basic', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    expect(res.body.user.numPendingLikes).to.equal(0);
  }

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 2)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(2);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '2',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(0);
});

it('numPendingLikes - unmatch', async () => {
  for (let i = 0; i < 4; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    expect(res.body.user.numPendingLikes).to.equal(0);
  }

  for (let i = 1; i < 4; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', i)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(3);

  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(2);

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 2)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);
});

it('numPendingLikes - dm and super like', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    expect(res.body.user.numPendingLikes).to.equal(0);
  }

  userMetadata = await UserMetadata.findOne({ user: 1 });
  userMetadata.coins = 1000;
  await userMetadata.save();

  user = await User.findById('2');
  user.numSuperLikes = 10;
  await user.save();

  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: '0',
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 2)
    .send({
      user: '0',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(2);

  // sending additional dm or super like should not increase count
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 1)
    .send({
      user: '0',
      message: 'Hi',
      price: 50,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.numPendingLikes).to.equal(2);

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 2)
    .send({
      user: '0',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.numPendingLikes).to.equal(2);
});

it('numPendingLikes - recalculate', async () => {
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(1);

  adminId = await createAdminUser();
  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', adminId)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.numPendingLikes).to.equal(0);
});

it('numLikesReceivedChange', async () => {
  for (let i = 0; i < 4; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.13.51' })
    expect(res.status).to.equal(200);
    expect(res.body.numLikesReceivedChange).to.equal();
  }

  // receive one like
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numLikesReceivedChange).to.equal(1);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numLikesReceivedChange).to.equal();

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  // receive two more likes
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 2)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 3)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numLikesReceivedChange).to.equal(1);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.numLikesReceivedChange).to.equal();
});

it('when chat is reject remove it from pending list and keep on sent likes', async () => {
  for (let i = 0; i < 4; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({ appVersion: '1.13.51' })
    expect(res.status).to.equal(200);
    expect(res.body.numLikesReceivedChange).to.equal();
  }

  // receive one like
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 2)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 3)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(3);

  res = await request(app)
    .get('/v1/chat/sent')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

  let user = await User.findOne({ _id: '0' })
  expect(user.metrics.numPendingLikes).to.equal(3);

  let rejectedChatCount = await Chat.countDocuments({ rejected: true })
  expect(rejectedChatCount).to.equal(0);

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  rejectedChatCount = await Chat.countDocuments({ rejected: true })
  expect(rejectedChatCount).to.equal(1);

  user = await User.findOne({ _id: '0' })
  expect(user.metrics.numPendingLikes).to.equal(2);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);  // change in pending chats

  res = await request(app)
    .get('/v1/chat/sent')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);  // no change in sent chats

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: '0' })
  expect(user.metrics.numPendingLikes).to.equal(2);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);  // change in pending chats

  res = await request(app)
    .get('/v1/chat/sent')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);  // no change in sent chats

  //handling edge cases
  userFriends = await FriendList.findOne({ userId: '0' });
  expect(userFriends.friends.length).to.equal(0);

  userFriends = await FriendList.findOne({ userId: '1' });
  expect(userFriends.friends.length).to.equal(0);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: '0' })
  expect(user.metrics.numPendingLikes).to.equal(1);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  res = await request(app)
    .get('/v1/chat/sent')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

  userFriends = await FriendList.findOne({ userId: '0' });
  expect(userFriends.friends.length).to.equal(1);
  expect(userFriends.friends[0]).to.equal('1');

  userFriends = await FriendList.findOne({ userId: '1' });
  expect(userFriends.friends.length).to.equal(1);
  expect(userFriends.friends[0]).to.equal('0');

  user = await User.findOne({ _id: 3 });
  user.premiumExpiration = moment().add(2, 'days').toDate();
  user.numSuperLikesFree = 5
  await user.save();

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '3',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

  res = await request(app)
    .patch('/v1/user/sendSuperLike')
    .set('authorization', 3)
    .send({
      user: '0',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '3',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 3)
    .send({
      user: '0',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 0)
    .send({
      user: '3',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 3)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);

});

it('add perState in new messages for bot id', async () => {

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', chatLib.BOO_BOT_ID);
  expect(res.status).to.equal(200);

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 0)
  .send({ appVersion: '1.13.69' });
  expect(res.status).to.equal(200);

  let user = await User.findOne({_id: '0' })
  await chatLib.sendAutomatedReply(user)


  let chat = await Chat.findOne({}).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0','MOCK_BOO_BOT_ID'])
  expect(chat.perUserState).to.eql([
    { userId: '0',
      unread: true
    },
    {
      userId: 'MOCK_BOO_BOT_ID',
      unread: false
    }
  ])

  chat = await Chat.findOne({})
  chat.perUserState = []
  await chat.save()

  user = await User.findOne({_id: '0' })
  user.createdAt = new Date('2022-11-11')
  await user.save()

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 500));

  chat = await Chat.findOne({}).lean()
  expect(chat.perUserState).to.eql([
    { userId: '0',
      unread: true
    },
    {
      userId: 'MOCK_BOO_BOT_ID',
      unread: false
    }
  ])

  chat = await Chat.findOne({})
  chat.perUserState = undefined
  await chat.save()
  const chatBotUser = chat._id

  user = await User.findOne({_id: '0' })
  user.backfillChatPerUserStateComplete = undefined
  user.backfillChatPerUserStateBegan = undefined
  await user.save()

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 500));

  chat = await Chat.findOne({}).lean()
  expect(chat.perUserState).to.eql([
    { userId: '0',
      unread: true
    },
    {
      userId: 'MOCK_BOO_BOT_ID',
      unread: false
    }
  ])

  for (let uid = 0; uid < 3; uid++) {
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', uid)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    if (uid > 0) {
      res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: uid.toString(),
      });
      expect(res.status).to.equal(200);
      res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', uid)
      .send({
        user: '0',
      });
      expect(res.status).to.equal(200);

      res = await request(app)
      .post('/v1/message')
      .set('authorization', uid)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);
    }
  }

  //test routes for recent
  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'recent' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(3);
  expect(res.body.chats[2]._id).to.equal(chatBotUser.toString())

  //test routes for unread
  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'unread' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(3);
  expect(res.body.chats[2]._id).to.equal(chatBotUser.toString())

  user = await User.findOne({_id: '0' })
  await chatLib.sendAutomatedReply(user)

  });

describe('sort messages unread and your turn', () => {

it('add perState in new messages', async () => {
  clock = sinon.useFakeTimers(new Date('2026-06-06'));
  for (let uid = 0; uid < 3; uid++) {
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', uid)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    if (uid > 0) {
      res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: uid.toString(),
      });
      expect(res.status).to.equal(200);
      res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', uid)
      .send({
        user: '0',
      });
      expect(res.status).to.equal(200);
    }
  }
  clock.restore();

  let chat = await Chat.findOne({ users: '1' }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(undefined)
  expect(chat.users).to.eql(['0','1'])
  expect(chat.perUserState).to.eql([])

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 1)
    .send({
      user: '0',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ users: '1' }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0','1'])
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
      "yourTurnState": "uncategorized",
    },
    {
      "unread": false,
      "userId": "1",
      "yourTurnState": "messageSent",
    },
  ])

  res = await request(app)
    .put('/v1/message/seen')
    .set('authorization', 0)
    .send({ chatId: chat._id });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 0)
  .send({
    user: '1',
    text: 'msg2',
  });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ users: '1' }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0','1'])
  expect(chat.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": true,
      "userId": "1",
      "yourTurnState": "uncategorized",
    },
  ])

  // marked as seen
  res = await request(app)
    .put('/v1/message/seen')
    .set('authorization', 1)
    .send({ chatId: chat._id });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ users: '1' }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0','1'])
  expect(chat.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": false,
      "userId": "1",
      "yourTurnState": "uncategorized",
    },
  ])

    //marked as unseen
    res = await request(app)
    .patch('/v1/chat/markUnread')
    .set('authorization', 1)
    .send({ chatId: chat._id });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ users: '1' }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0','1'])
  expect(chat.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": true,
      "userId": "1",
      "yourTurnState": "uncategorized",
    },
  ])

  //for group chat

  res = await request(app)
    .patch('/v1/chat/createGroupChat')
    .set('authorization', 0)
    .send({ users: ['1', '2'] });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ groupChat: true }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0', '1', '2'])
  expect(chat.perUserState).to.eql([])
  let chatId = chat._id

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 1)
    .send({
      chatId: chatId,
      text: 'msg by user 1',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ groupChat: true }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0', '1', '2'])
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": false,
      "userId": "1",
    },
    {
      "unread": true,
      "userId": "2",
    },
  ])

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      chatId: chatId,
      text: 'msg by user 0',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ groupChat: true }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0', '1', '2'])
  expect(chat.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
    },
    {
      "unread": true,
      "userId": "1",
    },
    {
      "unread": true,
      "userId": "2",
    },
  ])

  res = await request(app)
    .put('/v1/message/seen')
    .set('authorization', 2)
    .send({ chatId: chatId });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 2)
    .send({
      chatId: chatId,
      text: 'msg by user 2',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ groupChat: true }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0', '1', '2'])
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": true,
      "userId": "1",
    },
    {
      "unread": false,
      "userId": "2",
    },
  ])

  // marked as seen
  res = await request(app)
    .put('/v1/message/seen')
    .set('authorization', 1)
    .send({ chatId: chat._id });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne({ groupChat: true }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chat.messaged).to.eql(true)
  expect(chat.users).to.eql(['0', '1', '2'])
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": false,
      "userId": "1",
    },
    {
      "unread": false,
      "userId": "2",
    },
  ])

  //backfill
  let user = await User.findOne({_id: '0' })
  expect(user.backfillChatPerUserStateComplete).to.equal(undefined)
  expect(user.backfillChatPerUserStateBegan).to.equal(undefined)
  expect(user.backfillNumYourTurnChatsComplete).to.equal(undefined)
  expect(user.backfillNumYourTurnChatsBegan).to.equal(undefined)

  user.createdAt = new Date('2022-11-11')
  await user.save()

  user = await User.findOne({_id: '1' })
  expect(user.backfillChatPerUserStateComplete).to.equal(undefined)
  expect(user.backfillChatPerUserStateBegan).to.equal(undefined)
  expect(user.backfillNumYourTurnChatsComplete).to.equal(undefined)
  expect(user.backfillNumYourTurnChatsBegan).to.equal(undefined)

  user.createdAt = new Date('2022-11-11')
  await user.save()

  await Chat.updateMany({ messaged: true },{ $unset: { perUserState: 1 }})
  let chats = await Chat.find({ messaged: true }).select({ perUserState: 1, users: 1 }).lean()
  let perUserState = chats.map(x => x.perUserState)
  expect(perUserState).to.eql([undefined, undefined])

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '0');
  expect(res.status).to.equal(200);

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', '1');
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 2000));

  chats = await Chat.find({ messaged: true }).select({ perUserState: 1, users: 1 }).lean()
  perUserState = chats.map(x => x.perUserState)
  expect(perUserState).to.eql(
  [
    [
      {
        "unread": false,
        "userId": "0",
        "yourTurnState": "messageSent",
      },
      {
        "unread": true,
        "userId": "1",
        "yourTurnState": "uncategorized",
      }
    ],
    [
      {
        "unread": true,
        "userId": "0",
      },
      {
        "unread": false,
        "userId": "1"
      },
      {
        "unread": false,
        "userId": "2"
      }
    ]
  ])

  user = await User.findOne({_id: '0' })
  expect(user.backfillChatPerUserStateComplete).to.equal(true)
  expect(user.backfillNumYourTurnChatsComplete).to.equal(true)
  expect(user.metrics.numYourTurnChats).to.equal(0)

  user = await User.findOne({_id: '1' })
  expect(user.backfillChatPerUserStateComplete).to.equal(true)
  expect(user.backfillNumYourTurnChatsComplete).to.equal(true)
  expect(user.metrics.numYourTurnChats).to.equal(1)

  //test routes for yourTurn
  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'yourTurn', paginationToken: 'null' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  let firstChat = res.body.chats[0]
  let secondChat = res.body.chats[1]

  chats = await Chat.findOne({ _id: firstChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": false,
      "userId": "1"
    },
    {
      "unread": false,
      "userId": "2"
    }
  ])

  chats = await Chat.findOne({ _id: secondChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": true,
      "userId": "1",
      "yourTurnState": "uncategorized",
    }
  ])


  let paginationToken = chatLib.encodePaginationToken(firstChat, false)
  let decodePaginationToken = chatLib.decodePaginationToken(paginationToken)
  expect(decodePaginationToken.pinned).to.eql(false)
  expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(firstChat.lastMessageTime))
  expect(decodePaginationToken.chatType).to.eql(false)

  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'yourTurn', paginationToken: paginationToken })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
  expect(res.body.chats[0]).to.eql(secondChat)

    //user 1 has to be un-categorized first then remaining
    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 1)
    .query({ sort: 'yourTurn' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  firstChat = res.body.chats[0]
  secondChat = res.body.chats[1]

  chats = await Chat.findOne({ _id: firstChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": true,
      "userId": "1",
      "yourTurnState": "uncategorized",
    }
  ])

  chats = await Chat.findOne({ _id: secondChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": false,
      "userId": "1"
    },
    {
      "unread": false,
      "userId": "2"
    }
  ])

  //test routes for unread
  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 1)
    .query({ sort: 'unread' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].pinned).to.equal(undefined);
  expect(res.body.chats[1].pinned).to.equal(undefined);

  res = await request(app)
    .patch('/v1/chat/pin')
    .set('authorization', 1)
    .send({ chatId: res.body.chats[0]._id });
  expect(res.status).to.equal(200);


  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 1)
    .query({ sort: 'unread' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);
  expect(res.body.chats[0].pinned).to.equal(true);
  expect(res.body.chats[1].pinned).to.equal(undefined);
  firstChat = res.body.chats[0]
  secondChat = res.body.chats[1]

  chats = await Chat.findOne({ _id: firstChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": false,
      "userId": "0",
      "yourTurnState": "messageSent",
    },
    {
      "unread": true,
      "userId": "1",
      "yourTurnState": "uncategorized",
    }
  ])

  chats = await Chat.findOne({ _id: secondChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.perUserState).to.eql([
    {
      "unread": true,
      "userId": "0",
    },
    {
      "unread": false,
      "userId": "1"
    },
    {
      "unread": false,
      "userId": "2"
    }
  ])

  //test route for recent
  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'recent', paginationToken: 'null' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  firstChat = res.body.chats[0]
  secondChat = res.body.chats[1]

  chats = await Chat.findOne({ _id: firstChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.messaged).to.eql(true)

  chats = await Chat.findOne({ _id: secondChat._id }).select({ users: 1, perUserState: 1, messaged: 1 }).lean()
  expect(chats.messaged).to.eql(true)

  paginationToken = chatLib.encodePaginationToken(firstChat, true)
  decodePaginationToken = chatLib.decodePaginationToken(paginationToken)
  expect(decodePaginationToken.pinned).to.eql(false)
  expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(firstChat.lastMessageTime))
  expect(decodePaginationToken.chatType).to.eql(true)

  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'recent', paginationToken: paginationToken })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(1);
  expect(res.body.chats[0]).to.eql(secondChat)
  expect(res.body.paginationToken).eql(null)

  //pinned
  res = await request(app)
    .patch('/v1/chat/pin')
    .set('authorization', 0)
    .send({ chatId: secondChat._id });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/chat/sortMessages')
  .set('authorization', 0)
  .query({ sort: 'recent' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(2);

  firstChat = res.body.chats[0]
  secondChat = res.body.chats[1]

  chats = await Chat.findOne({ _id: firstChat._id }).select({ users: 1, perUserState: 1, messaged: 1, pinned: 1 }).lean()
  expect(chats.messaged).to.eql(true)
  expect(chats.pinned).to.eql(['1','0'])

  chats = await Chat.findOne({ _id: secondChat._id }).select({ users: 1, perUserState: 1, messaged: 1, pinned: 1 }).lean()
  expect(chats.messaged).to.eql(true)

  user = await User.findOne({_id: '1' })
  expect(user.backfillChatPerUserStateComplete).to.equal(true)
  expect(user.backfillNumYourTurnChatsComplete).to.equal(true)
  expect(user.metrics.numYourTurnChats).to.equal(1)

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 4)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  res = await request(app)
  .patch('/v1/user/sendLike')
  .set('authorization', 1)
  .send({
    user: '4',
  });
  expect(res.status).to.equal(200);
  res = await request(app)
  .patch('/v1/user/approve')
  .set('authorization', 4)
  .send({
    user: '1',
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 4)
  .send({
    user: '1',
    text: 'Hi, user 1',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 1)
  .send({
    user: '4',
    text: 'Hi, user 4',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 4)
  .send({
    user: '1',
    text: 'Hi, user 1 second time',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 5)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  res = await request(app)
  .patch('/v1/user/sendLike')
  .set('authorization', 1)
  .send({
    user: '5',
  });
  expect(res.status).to.equal(200);
  res = await request(app)
  .patch('/v1/user/approve')
  .set('authorization', 5)
  .send({
    user: '1',
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 5)
  .send({
    user: '1',
    text: 'Hi, user 1',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(3)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  // ban user

  res = await request(app)
    .get('/v1/user')
    .set('authorization', 10);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 10 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  //before ban numYourTurnChats
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(3)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 10)
    .send({
      user: '5',
    });
  expect(res.status).to.equal(200);
  expect((await User.findOne({ _id: 5 })).shadowBanned).to.equal(true);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  // no change for banned other user
  res = await request(app)
  .post('/v1/message')
  .set('authorization', 5)
  .send({
    user: '1',
    text: 'Hi, user 1',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  // no change for banned other user but decrease for self
  res = await request(app)
  .post('/v1/message')
  .set('authorization', 1)
  .send({
    user: '5',
    text: 'Hi, user 5',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 5)
    .send({
      user: '1',
      text: 'Hi, user 1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/admin/unban')
    .set('authorization', 10)
    .send({
      user: '5',
      notes: 'not scammer',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(3)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
    .get('/v1/chat')
    .set('authorization', 5);
  expect(res.status).to.equal(200);
  expect(res.body.length).to.equal(1);
  expect(res.body[0].yourTurn).to.equal(false);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 1)
    .send({
      user: '5',
      text: 'Hi, user 5, again',
    });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  res = await request(app)
    .get('/v1/chat')
    .set('authorization', 5);
  expect(res.status).to.equal(200);
  expect(res.body.length).to.equal(1);
  expect(res.body[0].yourTurn).to.equal(true);

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 1)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);
  expect(res.body.numYourTurnChats).to.equal(2);

  });

  it('check pagination of sortMessages', async () => {
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 10)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    user = await User.findById(10);
    user.premiumExpiration = moment().add(2, 'days').toDate();
    await user.save();

    for (let uid = 11; uid < 25; uid++) {
      res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

      res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 10)
      .send({
        user: uid.toString(),
      });
      expect(res.status).to.equal(200);

      res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', uid)
      .send({
        user: '10',
      });
      expect(res.status).to.equal(200);

      res = await request(app)
      .post('/v1/message')
      .set('authorization', uid)
      .send({
        user: '10',
        text: `msg-${uid}`,
      });
      expect(res.status).to.equal(200);
    }

    let chatIds = new Set();

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ sort: 'recent' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    let lastChatId = res.body.chats.at(-1)
    let paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    let decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken)
    expect(decodePaginationToken.pinned).to.eql(false)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ sort: 'recent', paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response

    chatIds = new Set();

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken)
    expect(decodePaginationToken.pinned).to.eql(false)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response

    //now pinned to page size less than 10
    chatIds = new Set();
    let pinnedUsers = ['22', '21', '13', '14', '15', '16', '20', '18', '19']
    for(i = 0; i < 9; i++){
      let chat = await Chat.findOne({ users: {$in: pinnedUsers[i]}})
      res = await request(app)
        .patch('/v1/chat/pin')
        .set('authorization', 10)
        .send({ chatId: chat._id });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(undefined);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));


    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken, true)
    expect(decodePaginationToken.pinned).to.eql(false)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].pinned).to.equal(undefined);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response

    //now pinned to page size equal to 10
    chatIds = new Set();
    chat = await Chat.findOne({ users: '11' })
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 10)
      .send({ chatId: chat._id });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));


    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken, true)
    expect(decodePaginationToken.pinned).to.eql(true)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].pinned).to.equal(undefined);
    expect(res.body.chats[1].pinned).to.equal(undefined);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response

    //now pinned more than page size to 11
    chatIds = new Set();
    chat = await Chat.findOne({ users: '12' })
    res = await request(app)
      .patch('/v1/chat/pin')
      .set('authorization', 10)
      .send({ chatId: chat._id });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));


    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken, true)
    expect(decodePaginationToken.pinned).to.eql(true)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: res.body.paginationToken })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].pinned).to.equal(true);
    expect(res.body.chats[1].pinned).to.equal(undefined);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response

    chatIds = new Set();
    //request should not appear on chat
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 30)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    user = await User.findById(30);
    user.premiumExpiration = moment().add(2, 'days').toDate();
    await user.save();

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 30)
      .send({
        user: '10',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 30)
    .send({
      user: '10',
      message: 'Hi',
    });
  expect(res.status).to.equal(200);

    chat = await Chat.findOne({users: ['30','10'] })
    chat.messaged = true
    chat.perUserState = [
      {
        userId: '10',
        yourTurnState: 'uncategorized'
      },
      {
        userId: '30',
        yourTurnState: 'messageSent'
      },
    ]
    await chat.save()

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: null, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken, true)
    expect(decodePaginationToken.pinned).to.eql(true)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
      .get('/v1/chat/sortMessages')
      .set('authorization', 10)
      .query({ paginationToken: res.body.paginationToken, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].pinned).to.equal(true);
    expect(res.body.chats[1].pinned).to.equal(undefined);
    expect(res.body.chats[0].yourTurn).to.equal(true);
    expect(res.body.chats[1].yourTurn).to.equal(true);
    expect(res.body.chats[2].yourTurn).to.equal(true);
    expect(res.body.chats[3].yourTurn).to.equal(true);

    let blockUser = res.body.chats[1].users[0]._id
    let blockChat = res.body.chats[1]._id

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response
    chat = await Chat.findOne({users: ['30','10'] })
    expect(chatIds.has(chat._id.toString())).to.be.false;
    expect(chatIds.has(chat._id)).to.be.false;

    chatIds = new Set();

    //hiding chat no 14
    res = await request(app)
    .patch('/v1/chat/hide')
    .set('authorization', 10)
    .query({ chatId : res.body.chats[3]._id });
    expect(res.status).to.equal(200);

    chat = await Chat.findOne({ _id: blockChat })
    chat.bannedUsers = ['10']
    await chat.save()

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: null, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    lastChatId = res.body.chats.at(-1)
    paginationToken = chatLib.encodePaginationToken(lastChatId, true)
    decodePaginationToken = chatLib.decodePaginationToken(res.body.paginationToken, true)
    expect(decodePaginationToken.pinned).to.eql(true)
    expect(new Date(decodePaginationToken.beforeDate)).to.eql(new Date(lastChatId.lastMessageTime))
    expect(decodePaginationToken.chatType).to.eql(true)
    expect(res.body.paginationToken).to.equal(paginationToken);

    res = await request(app)
      .get('/v1/chat/sortMessages')
      .set('authorization', 10)
      .query({ paginationToken: res.body.paginationToken, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(4);
    expect(res.body.chats[0].pinned).to.equal(true);
    expect(res.body.chats[1].pinned).to.equal(undefined);
    expect(res.body.chats[0].yourTurn).to.equal(true);
    expect(res.body.chats[1].yourTurn).to.equal(false); // now yourTurn false with bannedUser
    expect(res.body.chats[1]._id).to.equal(blockChat);
    expect(res.body.chats[1].users[0]._id).to.equal(blockUser);
    expect(res.body.chats[2].yourTurn).to.equal(true);
    expect(res.body.chats[3].yourTurn).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(14); //making sure non of the id are repeated in the response
    chat = await Chat.findOne({users: ['30','10'] })
    expect(chatIds.has(chat._id.toString())).to.be.false;
    expect(chatIds.has(chat._id)).to.be.false;

    chatIds = new Set();

    // user 10 hides chat and should not appear in unread/recent
    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: null, sort: 'unread' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    res = await request(app)
      .get('/v1/chat/sortMessages')
      .set('authorization', 10)
      .query({ paginationToken: res.body.paginationToken, sort: 'unread' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(3); // not showing hidden chat

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(13); //making sure non of the id are repeated in the response

    chatIds = new Set();

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 100);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 100 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // shadowBanning user 10
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 100)
      .send({
        user: '10',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 10)
    .query({ paginationToken: null, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);
    expect(res.body.chats[8].pinned).to.equal(true);
    expect(res.body.chats[9].pinned).to.equal(true);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    res = await request(app)
      .get('/v1/chat/sortMessages')
      .set('authorization', 10)
      .query({ paginationToken: res.body.paginationToken, sort: 'yourTurn' })
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(3);
    expect(res.body.chats[0].pinned).to.equal(true);
    expect(res.body.chats[1].pinned).to.equal(undefined);
    expect(res.body.chats[0].yourTurn).to.equal(false);
    expect(res.body.chats[1].yourTurn).to.equal(false);
    expect(res.body.chats[2].yourTurn).to.equal(false);

    res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

    expect(chatIds.size).to.equal(13); //making sure non of the id are repeated in the response
  })
});

describe('process yourTurnState', () => {
  let openAiStub;
  beforeEach(() => {
    openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt').resolves({
      output: JSON.stringify({
        output: {
          Status: 'Active',
          'Contact details': 'yes',
          'Met up': 'yes',
          Transactions: 'no',
          Explanation: 'Explanation of status and any yes responses',
        },
      }),
      cost: 0.002,
      promptTokens: 50,
      outputTokens: 10,
      errorMessage: null,
      processingTime: 1089,
    });
  });
  afterEach(() => {
    openAiStub.restore();
  });
  it('yourTurn more than 8 then process yourTurnState thorugh openAI', async () => {
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 10)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    user = await User.findById(10);
    user.premiumExpiration = moment().add(2, 'days').toDate();
    await user.save();

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 100);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 100 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    for (let uid = 11; uid < 25; uid++) {
      res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

      res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 10)
      .send({
        user: uid.toString(),
      });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', uid)
        .send({
          user: '10',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', uid)
        .send({
          user: '10',
          text: `msg-${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', '10')
        .send({
          user: `${uid}`,
          text: `msg-from-user-10-to${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', uid)
        .send({
          user: '10',
          text: `msg-from-user-${uid}-to-10`,
        });
      expect(res.status).to.equal(200);

      buffer = 'a'.repeat(100);
      filePath = temp.openSync({ suffix: '.mp3' }).path;
      fs.writeFileSync(filePath, buffer);

      res = await request(app)
        .post('/v1/message/audio')
        .set('authorization', uid)
        .query({ recipient: 10 })
        .attach('audio', filePath);
      expect(res.status).to.equal(200);

      if(uid == 24){
        res = await request(app)
          .put('/v1/admin/ban')
          .set('authorization', 100)
          .send({
            user: '24',
          });
        expect(res.status).to.equal(200);
        expect((await User.findOne({ _id: 24 })).shadowBanned).to.equal(true);
      }

      res = await request(app)
        .post('/v1/message')
        .set('authorization', uid)
        .send({
          user: '10',
          text: `msg-from-user-${uid}-to-10-last`,
        });
      expect(res.status).to.equal(200);
    }

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(13)

    user = await User.findOne({_id: '24' })
    expect(user.metrics.numYourTurnChats).to.equal(0)

    res = await request(app)
    .post('/v1/message')
    .set('authorization', 10)
    .send({
      user: '24',
      text: `msg-from-user-10-to-24-banned-1`,
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .post('/v1/message')
    .set('authorization', 10)
    .send({
      user: '24',
      text: `msg-from-user-10-to-24-banned-2`,
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(13)

    user = await User.findOne({_id: '24' })
    expect(user.metrics.numYourTurnChats).to.equal(0)

    res = await request(app)
    .post('/v1/message')
    .set('authorization', 24)
    .send({
      user: '10',
      text: `msg-from-user-24-to-10-banned`,
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(13)

    user = await User.findOne({_id: '24' })
    expect(user.metrics.numYourTurnChats).to.equal(0)

    user = await User.findOne({_id: '23' })
    expect(user.metrics.numYourTurnChats).to.equal(0)

    res = await request(app)
    .post('/v1/message')
    .set('authorization', 10)
    .send({
      user: '23',
      text: `msg-from-user-10-to-23-not-banned`,
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(12)

    user = await User.findOne({_id: '23' })
    expect(user.metrics.numYourTurnChats).to.equal(1)

    res = await request(app)
    .post('/v1/message')
    .set('authorization', 23)
    .send({
      user: '10',
      text: `msg-from-user-23-to-10-not-banned`,
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(13)

    user = await User.findOne({_id: '23' })
    expect(user.metrics.numYourTurnChats).to.equal(0)

    res = await request(app)
      .get('/v1/chat/sortMessages')
      .set('authorization', 10)
      .query({ sort: 'recent' })
      expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(10);

    let chatsCountUnCategorized = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'uncategorized',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    let chatsCountYourTurn = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'yourTurn',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    let chatsCountConcluded = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'concluded',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })

    expect(chatsCountUnCategorized).to.equal(13);
    expect(chatsCountYourTurn).to.equal(0);
    expect(chatsCountConcluded).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 10)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chatsCountUnCategorized = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'uncategorized',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountYourTurn = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'yourTurn',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountConcluded = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'concluded',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })

    expect(chatsCountUnCategorized).to.equal(0);
    expect(chatsCountYourTurn).to.equal(13);
    expect(chatsCountConcluded).to.equal(0);

    await Chat.updateMany({ 'perUserState.userId': 10 }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });

    chatsCountUnCategorized = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'uncategorized',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountYourTurn = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'yourTurn',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountConcluded = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'concluded',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })

    expect(chatsCountUnCategorized).to.equal(13);
    expect(chatsCountYourTurn).to.equal(0);
    expect(chatsCountConcluded).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 10)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    chatsCountUnCategorized = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'uncategorized',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountYourTurn = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'yourTurn',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })
    chatsCountConcluded = await Chat.countDocuments({
      perUserState:{
        $elemMatch: {
          userId: '10',
          yourTurnState: 'concluded',
        },
      },
      bannedUsers: { $in: [ null, []] },
    })

    expect(chatsCountUnCategorized).to.equal(0);
    expect(chatsCountYourTurn).to.equal(13);
    expect(chatsCountConcluded).to.equal(0);

    let messageAudioWithTranscriptionSaved = await Message.countDocuments({ transcribedAudio: 'mock transcription'})
    expect(messageAudioWithTranscriptionSaved).to.equal(13);

    let chatPromptHistoryCount =await ChatAnalysisYourTurn.countDocuments({ userId: 10 })
    expect(chatPromptHistoryCount).to.equal(13*2);

    let chatPromptHistory = await ChatAnalysisYourTurn.findOne({ userId: 10 })
    expect(chatPromptHistory.cost).to.equal(0.002);
    expect(chatPromptHistory.promptTokens).to.equal(50);
    expect(chatPromptHistory.outputTokens).to.equal(10);
    expect(chatPromptHistory.processingTime).to.equal(1089);
    expect(chatPromptHistory.result).to.equal('active');
    expect(chatPromptHistory.meetUp).to.equal(true);
    expect(chatPromptHistory.contactExchange).to.equal(true);
    expect(chatPromptHistory.transactions).to.equal(false);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(13)

    res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 10)
    .send({
      user: '11',
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(12)

    res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 12)
    .send({
      user: '10',
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(11)

    res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 13)
    .send({
      user: '10',
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(10)

    res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 13)
    .send({
      user: '10',
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(10)

    res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 10)
    .send({
      user: '14',
    });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: '10' })
    expect(user.metrics.numYourTurnChats).to.equal(9)
  })
})

it('ghost meter API', async () => {

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  let user = await User.findOne({ _id: 0 });
  expect(user.metrics.numYourTurnChats).to.equal(0);
  user.premiumExpiration = moment().add(2, 'days').toDate();
  await user.save();

  for (let targetUserId = 1; targetUserId < 16; targetUserId++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', targetUserId)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: `${targetUserId}`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', targetUserId)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', targetUserId)
      .send({
        user: '0',
        text: `Hello from user ${targetUserId} to user 0`,
      });
    expect(res.status).to.equal(200);

    if(targetUserId < 3){
      res = await request(app)
        .patch('/v1/chat/pin')
        .set('authorization', 0)
        .send({ user: `${targetUserId}` });
      expect(res.status).to.equal(200);

      if(targetUserId == 1){
        res = await request(app)
          .post('/v1/message')
          .set('authorization', 0)
          .send({
            user: '1',
            text: `Hello from user 0 to user 1`,
          });
        expect(res.status).to.equal(200);
      }
    }
  }

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.numYourTurnChats).to.equal(14);

  res = await request(app)
    .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '8',
        text: `Hello from user 0 to user 8`,
      });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.metrics.numYourTurnChats).to.equal(13);

  let chatIds = new Set();

  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'ghostMeter' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(10);
  expect(res.body.chats[0].pinned).to.equal(undefined);
  res.body.chats.forEach(chat => {
    expect(chat.yourTurn).to.equal(true);
  })
  res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'ghostMeter', paginationToken: res.body.paginationToken })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(3);
  res.body.chats.forEach(chat => {
    expect(chat.yourTurn).to.equal(true);
  })
  res.body.chats.forEach(chat => chatIds.add(chat._id.toString()));

  expect(chatIds.size).to.equal(13);
  expect(chatIds.has('1')).to.be.false;
  expect(chatIds.has('8')).to.be.false;

  // add admin user 100
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 100)
    .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 100 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  // shadowBanning user 0
  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 100)
    .send({
      user: '0',
      bannedReason: 'temp shadow ban due to inappropriate profile'
    });
  expect(res.status).to.equal(200);

  //banning again user 0
  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 100)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat/sortMessages')
    .set('authorization', 0)
    .query({ sort: 'ghostMeter' })
  expect(res.status).to.equal(200);
  expect(res.body.chats.length).to.equal(0);

})

it('handle yourTurnState and unsend', async () => {

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 10)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  let user = await User.findById(10);
  user.premiumExpiration = moment().add(2, 'days').toDate();
  await user.save();

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 11)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  res = await request(app)
  .patch('/v1/user/sendLike')
  .set('authorization', 10)
  .send({
    user: '11',
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .patch('/v1/user/approve')
  .set('authorization', 11)
  .send({
    user: '10',
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', 11)
  .send({
    user: '10',
    text: `msg-${11}`,
  });
  expect(res.status).to.equal(200);
  let messageId = res.body._id
  let chatId = res.body.chat

  let chat = await Chat.findById(chatId).select('perUserState').lean()
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "10",
      "yourTurnState": "uncategorized",
    },
    {
      "unread": false,
      "userId": "11",
      "yourTurnState": "messageSent",
    },
  ])

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
  .patch('/v1/message/unsend')
  .set('authorization', 11)
  .query({
    messageId: messageId,
  });
  expect(res.status).to.equal(200);

  chat = await Chat.findById(chatId).select('perUserState').lean()
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "10",
    },
    {
      "unread": false,
      "userId": "11",
    },
  ])

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  res = await request(app)
  .post('/v1/message')
  .set('authorization', '11')
  .send({
    user: `${10}`,
    text: `msg-from-user-11-to${10}-initial`,
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', '10')
  .send({
    user: `${11}`,
    text: `msg-from-user-10-to${11}-1`,
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/message')
  .set('authorization', '10')
  .send({
    user: `${11}`,
    text: `msg-from-user-10-to${11}-2`,
  });
  expect(res.status).to.equal(200);
  messageId = res.body._id

  res = await request(app)
  .post('/v1/message')
  .set('authorization', '11')
  .send({
    user: `${10}`,
    text: `msg-from-user-11-to${10}-1`,
  });
  expect(res.status).to.equal(200);
  let lastMessageId = res.body._id

  chat = await Chat.findById(chatId).select('perUserState').lean()
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "10",
      "yourTurnState": "uncategorized",
    },
    {
      "unread": false,
      "userId": "11",
      "yourTurnState": "messageSent",
    },
  ])

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  //should not change perUserState because its older message unsent
  res = await request(app)
  .patch('/v1/message/unsend')
  .set('authorization', 10)
  .query({
    messageId: messageId,
  });
  expect(res.status).to.equal(200);


  chat = await Chat.findById(chatId).select('perUserState').lean()
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "10",
      "yourTurnState": "uncategorized",
    },
    {
      "unread": false,
      "userId": "11",
      "yourTurnState": "messageSent",
    },
  ])

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(0)

  //should change perUserState because latest message is unsent
  res = await request(app)
  .patch('/v1/message/unsend')
  .set('authorization', 11)
  .query({
    messageId: lastMessageId,
  });
  expect(res.status).to.equal(200);

  chat = await Chat.findById(chatId).select('perUserState').lean()
  expect(chat.perUserState).to.eql([
    {
      "unread": true,
      "userId": "10",
      "yourTurnState": "messageSent",
    },
    {
      "unread": false,
      "userId": "11",
      "yourTurnState": "uncategorized",
    },
  ])

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
})

describe('ChatMetric daily tracking tests', () => {
  let openAiStub;

  beforeEach(async () => {

    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
  });

  afterEach(() => {
    if (openAiStub) {
      openAiStub.restore();
      openAiStub = null;
    }
  });

  it('should track numMeetUp, numContactExchange and numMatchesAnalyzed metrics when processing yourTurnState', async () => {
    let clock = sinon.useFakeTimers();
    for (let i = 1; i < 3; i++) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', i)
        .send({ user: '0' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', 0)
        .send({ user: i.toString() });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: i.toString(),
          text: `Hello from user 0`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', i)
        .send({
          user: '0',
          text: `Hello back from user ${i}`,
        });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 2)
        .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', 1)
        .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '2',
          text: `Hello from user 0`,
        });
    expect(res.status).to.equal(200);

    let chats = await Chat.countDocuments({})
    expect(chats).to.equal(3);

    let metrics = await ChatMetric.find({});
    expect(metrics.length).to.equal(0);

    // First stub configuration for initial analysis
    openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt').resolves({
      output: JSON.stringify({
        output: {
          Status: 'Active',
          'Contact details': 'no',
          'Met up': 'no',
          Transactions: 'no',
          Explanation: 'Explanation of status and any yes responses',
        },
      }),
      cost: 0.002,
      promptTokens: 50,
      outputTokens: 10,
      errorMessage: null,
      processingTime: 1089,
    });

    try {
      let today = new Date();
      let startOfDay = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()));

      await chatLib.finalizeYourTurnStateInChats('0');

      let metrics = await ChatMetric.find({});
      expect(metrics.length).to.equal(1);
      expect(metrics[0].date.getTime()).to.equal(startOfDay.getTime());
      expect(metrics[0].numMeetUp).to.equal(0);
      expect(metrics[0].numContactExchange).to.equal(0);
      expect(metrics[0].numMatchesAnalyzed).to.equal(2);

      let user = await User.findOne({_id: '0' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '1' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(1)

      user = await User.findOne({_id: '2' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(1)

      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await chatLib.finalizeYourTurnStateInChats('1');

      metrics = await ChatMetric.find({});
      expect(metrics.length).to.equal(1);
      expect(metrics[0].date.getTime()).to.equal(startOfDay.getTime());
      expect(metrics[0].numMeetUp).to.equal(0);
      expect(metrics[0].numContactExchange).to.equal(0);
      expect(metrics[0].numMatchesAnalyzed).to.equal(3);

      user = await User.findOne({_id: '0' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '1' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '2' })
      expect(user.metrics.numMeetUpChats).to.equal(0)
      expect(user.metrics.numContactExchangeChats).to.equal(0)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      // Restore the previous stub before creating a new one
      openAiStub.restore();

      // Second stub configuration for different analysis results
      openAiStub = sinon.stub(OpenAI.prototype, 'executePrompt').resolves({
      output: JSON.stringify({
        output: {
          Status: 'Active',
          'Contact details': 'yes',
          'Met up': 'yes',
          Transactions: 'yes',
          Explanation: 'Explanation of status and any yes responses',
        },
      }),
      cost: 0.002,
      promptTokens: 50,
      outputTokens: 10,
      errorMessage: null,
      processingTime: 1089,
    });

      await chatLib.finalizeYourTurnStateInChats('0');

      metrics = await ChatMetric.find({});
      expect(metrics.length).to.equal(1);
      expect(metrics[0].date.getTime()).to.equal(startOfDay.getTime());
      expect(metrics[0].numMeetUp).to.equal(2);
      expect(metrics[0].numContactExchange).to.equal(2);
      expect(metrics[0].numMatchesAnalyzed).to.equal(3);

      user = await User.findOne({_id: '0' })
      expect(user.metrics.numMeetUpChats).to.equal(2)
      expect(user.metrics.numContactExchangeChats).to.equal(2)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '1' })
      expect(user.metrics.numMeetUpChats).to.equal(1)
      expect(user.metrics.numContactExchangeChats).to.equal(1)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '2' })
      expect(user.metrics.numMeetUpChats).to.equal(1)
      expect(user.metrics.numContactExchangeChats).to.equal(1)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['1','0'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '0' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','0'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '1' }, { $set: { 'perUserState.$.yourTurnState': 'uncategorized' } });
      await Chat.updateOne({ users: ['2','1'], 'perUserState.userId': '2' }, { $set: { 'perUserState.$.yourTurnState': 'messageSent' } });

      clock.tick(24 * 60 * 60 * 1000);
      today = new Date();
      startOfDay = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()));
      await chatLib.finalizeYourTurnStateInChats('1');

      metrics = await ChatMetric.find({}).sort({ createdAt: 1 })
      expect(metrics.length).to.equal(2);
      expect(metrics[0].date.getTime()).to.equal(0);
      expect(metrics[0].numMeetUp).to.equal(2);
      expect(metrics[0].numContactExchange).to.equal(2);
      expect(metrics[0].numMatchesAnalyzed).to.equal(3);

      expect(metrics[1].date.getTime()).to.equal(startOfDay.getTime());
      expect(metrics[1].numMeetUp).to.equal(1);
      expect(metrics[1].numContactExchange).to.equal(1);
      expect(metrics[1].numMatchesAnalyzed).to.equal(0);

      user = await User.findOne({_id: '0' })
      expect(user.metrics.numMeetUpChats).to.equal(2)
      expect(user.metrics.numContactExchangeChats).to.equal(2)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '1' })
      expect(user.metrics.numMeetUpChats).to.equal(2)
      expect(user.metrics.numContactExchangeChats).to.equal(2)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

      user = await User.findOne({_id: '2' })
      expect(user.metrics.numMeetUpChats).to.equal(2)
      expect(user.metrics.numContactExchangeChats).to.equal(2)
      expect(user.metrics.numMatchesAnalyzed).to.equal(2)

    } finally {
      if (openAiStub && openAiStub.restore) {
        openAiStub.restore();
      }
      if (clock && clock.restore) {
        clock.restore();
      }
    }
  });

});



it('handle yourTurnState and perUserState edge cases', async () => {

  res = await request(app)
  .get('/v1/user')
  .set('authorization', 100);
  expect(res.status).to.equal(200);

  let user = await User.findOne({ _id: 100 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();


  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 10)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  user = await User.findById(10);
  user.premiumExpiration = moment().add(2, 'days').toDate();
  await user.save();

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 11)
  .send({ appVersion: '1.13.53' });
  expect(res.status).to.equal(200);

  user = await User.findById(11);
  user.premiumExpiration = moment().add(2, 'days').toDate();
  await user.save();

  for(let uid = 0; uid < 6; uid ++){

    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', uid)
    .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    // interaction between 10 and other users
    res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 10)
    .send({
      user: `${uid}`,
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', uid)
    .send({
      user: '10',
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .post('/v1/message')
    .set('authorization', uid)
    .send({
      user: '10',
      text: `msg-${uid}`,
    });
    expect(res.status).to.equal(200);

    if(uid > 2){
      res = await request(app)
      .post('/v1/message')
      .set('authorization', 10)
      .send({
        user: `${uid}`,
        text: `msg-${uid}`,
      });
      expect(res.status).to.equal(200);
    }

    // interaction between 11 and other users
    res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 11)
    .send({
      user: `${uid}`,
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', uid)
    .send({
      user: '11',
    });
    expect(res.status).to.equal(200);

    res = await request(app)
    .post('/v1/message')
    .set('authorization', uid)
    .send({
      user: '11',
      text: `msg-${uid}`,
    });
    expect(res.status).to.equal(200);

    if(uid > 2){
      res = await request(app)
      .post('/v1/message')
      .set('authorization', 11)
      .send({
        user: `${uid}`,
        text: `msg-${uid}`,
      });
      expect(res.status).to.equal(200);
    }
  }

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(3)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(3)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  res = await request(app)
  .put('/v1/admin/ban')
  .set('authorization', 100)
  .send({
    user: '10',
    bannedReason: 'soft ban for unverified users'
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(3)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  // chat send during banned shouldn't change the flag yourTurn
  for(let uid = 0; uid < 6; uid ++){

    // interaction between 10 and other users
    res = await request(app)
    .post('/v1/message')
    .set('authorization', uid)
    .send({
      user: '10',
      text: `msg-${uid}`,
    });
    expect(res.status).to.equal(200);

    if(uid > 2){
      res = await request(app)
      .post('/v1/message')
      .set('authorization', 10)
      .send({
        user: `${uid}`,
        text: `msg-${uid}`,
      });
      expect(res.status).to.equal(200);
    }

    // interaction between 11 and other users
    res = await request(app)
    .post('/v1/message')
    .set('authorization', uid)
    .send({
      user: '11',
      text: `msg-${uid}`,
    });
    expect(res.status).to.equal(200);

    if(uid > 3){
      res = await request(app)
      .post('/v1/message')
      .set('authorization', 11)
      .send({
        user: `${uid}`,
        text: `msg-${uid}`,
      });
      expect(res.status).to.equal(200);
    }
  }

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(4)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  res = await request(app)
  .put('/v1/admin/unban')
  .set('authorization', 100)
  .send({user: '10'});
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(3)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(4)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  res = await request(app)
  .put('/v1/admin/ban')
  .set('authorization', 100)
  .send({user: '1'});
  expect(res.status).to.equal(200);

  res = await request(app)
  .put('/v1/admin/ban')
  .set('authorization', 100)
  .send({user: '3'});
  expect(res.status).to.equal(200);

  res = await request(app)
  .put('/v1/admin/ban')
  .set('authorization', 100)
  .send({user: '4'});
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  //performing unmatch by banned user
  res = await request(app)
  .patch('/v1/user/unmatch')
  .set('authorization', 1)
  .send({
    user: '10',
  });
  expect(res.status).to.equal(200);

  //performing unmatch to banned user
  res = await request(app)
  .patch('/v1/user/unmatch')
  .set('authorization', 10)
  .send({
    user: '4',
  });
  expect(res.status).to.equal(200);

  //should not change the numYourTurnChats
  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  // now deleting banned account also should not change numYourTurnChats
  //should not change the numYourTurnChats

  user = await User.findOne({_id: '3' })
  await user.deleteAccount();

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '3' })
  expect(user).to.equal(null)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(2)

  // normal unmatch should reduce the count
  res = await request(app)
  .patch('/v1/user/unmatch')
  .set('authorization', 10)
  .send({
    user: '5',
  });
  expect(res.status).to.equal(200);

  // normal unmatch should reduce the count
  res = await request(app)
  .patch('/v1/user/unmatch')
  .set('authorization', 11)
  .send({
    user: '0',
  });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '10' })
  expect(user.metrics.numYourTurnChats).to.equal(2)
  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(1)

  res = await request(app)
    .delete('/v1/admin/user')
    .set('authorization', 100)
    .send({ userId: 10 });
  expect(res.status).to.equal(200);

  user = await User.findOne({_id: '11' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
  user = await User.findOne({_id: '0' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '1' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '2' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '4' })
  expect(user.metrics.numYourTurnChats).to.equal(0)
  user = await User.findOne({_id: '5' })
  expect(user.metrics.numYourTurnChats).to.equal(1)
})

describe('APP-755: Unmatched', () => {
  beforeEach(async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', i)
        .send({ firstName: `User ${i}` });
      expect(res.status).to.equal(200);
    }
  });

  it('should create record when unmatched', async () => {
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let chat = res.body[0];

    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // unmatch by either user will add a record
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.eql('0');
    expect(res.body.chats[0].user.name).to.eql('User 0');
    expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);

    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.eql('1');
    expect(res.body.chats[0].user.name).to.eql('User 1');
    expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);
  });

  it('should create record when block', async () => {
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    let chat = res.body[0];

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // block by either user will add a record
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.eql('0');
    expect(res.body.chats[0].user.name).to.eql('User 0');
    expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);

    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.eql('1');
    expect(res.body.chats[0].user.name).to.eql('User 1');
    expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);
  });

  it('should create record when user account is deleted', async () => {
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // user 0 request for account deletion
    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', 0)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    let chat = res.body[0];

    let user0 = await User.findOne({ _id: 0 });
    await userLib.deleteAccount(user0);

    // user 1 will see the record
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].user._id).to.eql('0');
    expect(res.body.chats[0].user.name).to.eql('User 0');
    expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);
  });

  it('shadow banned users chat will be shown in unmatched list for other users', async () => {
    for (let i = 1; i < 5; i++) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: `${i}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', i)
        .send({
          user: `0`,
        });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    let chat = res.body[0];

    // shadow ban user 0
    setMockPromptResponse('{"ban": true}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: '0' });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

    // user 0 will not see the record
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // all other users will see the record
    for (let i = 1; i < 5; i++) {
      res = await request(app)
        .get('/v1/chat/unmatched')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(1);
      expect(res.body.chats[0].user._id).to.eql('0');
      expect(res.body.chats[0].user.name).to.eql('User 0');
      if (i === 1) {
        expect(res.body.chats[0].lastMessageTime).to.eql(chat.lastMessageTime);
      }
    }

    // unban user 0 will remove the records from unmatched list
    user = await User.findOne({ _id: 1 });
    user.admin = true;
    user.adminPermissions = { support: true, manager: true };
    await user.save();

    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    for (let i = 1; i < 5; i++) {
      res = await request(app)
        .get('/v1/chat/unmatched')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(res.body.chats.length).to.equal(0);
    }
  });

  it('test deletes unmatched chat record', async () => {
    // Create match user 0 & 1
    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // Shadow ban user 0 by user 1
    setMockPromptResponse('{"ban": true}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // Get unmatched record for user 1
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    const shadowUnmatchedId = res.body.chats[0]._id;

    // Invalid query param
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 1)
      .query({ id: null });
    expect(res.status).to.equal(422);

    // Unmatched record is not for this user → success false
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 4)
      .query({ id: shadowUnmatchedId });
    expect(res.status).to.equal(200);
    expect(res.body.success).to.equal(false);

    // Shadow-banned user tries to delete → success false
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 0)
      .query({ id: shadowUnmatchedId });
    expect(res.status).to.equal(200);
    expect(res.body.success).to.equal(false);

    // Other user (user 1) deletes unmatched record
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 1)
      .query({ id: shadowUnmatchedId });
    expect(res.status).to.equal(200);
    expect(res.body.success).to.equal(true);

    let doc = await Unmatched.findById(shadowUnmatchedId);
    expect(doc).to.equal(null);

    // User 1 tries again → success false
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 1)
      .query({ id: shadowUnmatchedId });
    expect(res.status).to.equal(200);
    expect(res.body.success).to.equal(false);

    // Create match user 1 & 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // User 1 unmatches user 2 (creates unmatched record)
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    // two unmatched records created, one for user 1 and one for user 2
    doc = await Unmatched.find({});
    expect(doc.length).to.equal(2);

    // Get unmatched record for user 2
    res = await request(app)
      .get('/v1/chat/unmatched')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    const regularUnmatchedId = res.body.chats[0]._id;

    // User 2 deletes
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 2)
      .query({ id: regularUnmatchedId });
    expect(res.status).to.equal(200);
    expect(res.body.success).to.equal(true);

    // record exists for user 1
    doc = await Unmatched.find({});
    expect(doc.length).to.equal(1);
    expect(doc[0].user).to.equal('1');
    expect(doc[0].otherUser).to.equal('2');

    // User 1 deletes
    res = await request(app)
      .delete('/v1/chat/unmatched')
      .set('authorization', 1)
      .query({ id: doc[0]._id.toString() });
    expect(res.status).to.equal(200);

    doc = await Unmatched.find({});
    expect(doc.length).to.equal(0);
  });

  it('test get hidden chats and unhide a chat', async () => {
    for (let i = 1; i < 5; i++) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: `${i}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', i)
        .send({
          user: `0`,
        });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    const candidateChatIds = res.body.map(chat => chat._id.toString()).slice(0, 2);
    console.log(candidateChatIds);

    // User 0 hides chat with user 3, 4
    for (const chatId of candidateChatIds) {
      res = await request(app)
        .patch('/v1/chat/hide')
        .set('authorization', 0)
        .query({ chatId });
      expect(res.status).to.equal(200);
    }

    // User 0 gets hidden chats
    res = await request(app)
      .get('/v1/chat/hidden')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);

    res = await request(app)
      .patch('/v1/chat/unhide')
      .set('authorization', 0)
      .query({ chatId: candidateChatIds[0] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/hidden')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // add deletedAt to the chat
    let chat = await Chat.findById(candidateChatIds[1]);
    chat.deletedAt = new Date();
    await chat.save();

    // Deleted chat should not be returned in hidden chats
    res = await request(app)
      .get('/v1/chat/hidden')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);
  });
});
