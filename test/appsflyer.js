const { expect, assert } = require('chai');
const request = require('supertest');
const { DateTime } = require('luxon');
const sinon = require('sinon');
const {
  app, mongoose, validAudioPath, validImagePath, validVideoPath, validGif, initialCoins, createUser, getProfile, waitMs, initSocket, destroySocket, getSocketPromise
} = require('./common');
const basic = require('../lib/basic');
const constants = require('../lib/constants');
const User = require('../models/user');
const AppVisitor = require('../models/app-visitor');

describe('add appsflyer_id', async () => {
    it('success add appsflyer_id', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
        expect(res.status).to.equal(200);
        console.log('user data: ', res.body.user)

        res = await request(app)
          .put('/v1/user/appsflyer')
          .set('authorization', 0)
          .send({appsflyer:{appsflyer_id: "1234567890123-1234567" }});
        expect(res.status).to.equal(200);

        user = await User.findOne({_id: 0}).lean()
        expect(user.appsflyer.appsflyer_id).to.equal('1234567890123-1234567')
    })

    it('add appsflyer_id to use who already have it', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({appsflyer: {appsflyer_id: "1234567890123-1234567" }});
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/appsflyer')
          .set('authorization', 0)
          .send({appsflyer: {appsflyer_id: "0000000000000" }});
        expect(res.status).to.equal(200);

        user = await User.findOne({_id: 0}).lean()
        expect(user.appsflyer.appsflyer_id).to.equal('1234567890123-1234567')
    })
    
})

describe('convert appsflyer to kochava: ', async () => {
    beforeEach(async () => {
        appsflyerData= {
            status: 'success',
            payload: {
                adgroup_id: null,
                af_adset_id: "************",
                af_ad_type: "ClickToDownload",
                retargeting_conversion_type: "none",
                orig_cost: "0.0",
                network: "Search",
                is_first_launch: false,
                af_click_lookback: "7d",
                af_cpi: null,
                iscache: true,
                external_account_id: {
                    $numberDouble: "**********.0"
                },
                click_time: "2025-03-16 05:15:52.363",
                adset: null,
                match_type: "srn",
                af_channel: "ACI_Search",
                af_viewthrough_lookback: "1d",
                campaign_id: "***********",
                lat: "0",
                install_time: "2025-03-16 05:24:27.545",
                af_c_id: "***********",
                agency: null,
                media_source: "googleadwords_int",
                ad_event_id: "ClxDajBLQ1Fqd3l0Uy1CaENLQVJJc0FNR0p5enEtTWJQTFVRbC1RV0VwUFhFNzFQTjB0Nl9tMi1BX1JHYURWUkVsZ3FVZGllaVV1MTdRYlZBYUFyaGlFQUx3X3djQhITCO6l9ZXujYwDFdBe9ggd6MwTcBjll5vNASAAKMPvv94tMlpDajhLRVFqd3l0Uy1CaENlMFlxTjZabW8tb3NCRWlZQUdzcXRaQmJDMTJnYzAyTzFCT3F2VElpa3FUTlZpNXZrZFgyRE1xVlVjamJ1VlJGNVFSb0NSTGdZQVE",
                af_siteid: "GoogleSearch",
                af_status: "Non-organic",
                af_sub1: null,
                gclid: null,
                referrer_gclid: "Cj0KCQjwytS-BhCKARIsAMGJyzq-MbPLUQl-QWEpPXE71PN0t6_m2-A_RGaDVRElgqUdieiUu17QbVAaArhiEALw_wcB",
                cost_cents_USD: "0",
                af_ad_id: "",
                af_reengagement_window: "30d",
                af_sub5: null,
                af_sub4: null,
                af_adset: "General Dating",
                'click-timestamp': "1742102152363",
                af_sub3: null,
                af_sub2: null,
                adset_id: null,
                gbraid: null,
                http_referrer: null,
                campaign: "US - Installs",
                af_ad: "",
                adgroup: null
            }
        }

        appsflyerDataFB = {
            status: 'success',
            payload: {
                adgroup_id: "120216245391630350",
                retargeting_conversion_type: "none",
                is_fb: true,
                is_first_launch: false,
                iscache: false,
                click_time: "2025-03-16 05:19:28.000",
                adset: "C. Brazil || AAA || Installs   Ad set",
                match_type: "gp_referrer",
                af_channel: "Instagram",
                is_paid: false,
                campaign_id: "120212179577430350",
                install_time: "2025-03-16 05:22:05.699",
                agency: null,
                media_source: "Facebook Ads",
                af_siteid: null,
                af_status: "Non-organic",
                af_sub1: null,
                ad_id: "120216245413750350",
                af_sub5: null,
                af_sub4: null,
                af_sub3: null,
                af_sub2: null,
                adset_id: "120212179577380350",
                http_referrer: null,
                campaign: "C. Brazil L. Portuguese || AAA || Installs   Campaign",
                is_mobile_data_terms_signed: false,
                adgroup: "Portuguese (Brazilian) - CantSleep - Dating.mp4 - Copy"
            }
        }

        appsflyerDataSnap = {
            status: 'success',
            payload: {
                adgroup_id: null,
                af_adset_id: "65abeeaf-8b44-43fb-8bd5-f2e82b2cece5",
                retargeting_conversion_type: "none",
                orig_cost: "0.0",
                is_first_launch: false,
                af_click_lookback: "7d",
                af_cpi: null,
                iscache: true,
                network_account_id: "0e561815-0623-4530-a5dc-b9bf6ab9a3cc",
                click_time: "2025-03-19 20:19:25.250",
                adset: null,
                match_type: "srn",
                af_channel: "Snapchat",
                af_viewthrough_lookback: "1d",
                campaign_id: "abf9df09-494d-43e3-9c37-6b8b9471296e",
                "is_claimed?": "true",
                install_time: "2025-03-20 03:28:24.506",
                af_c_id: "abf9df09-494d-43e3-9c37-6b8b9471296e",
                agency: null,
                media_source: "snapchat_int",
                af_siteid: null,
                af_status: "Non-organic",
                af_sub1: null,
                cost_cents_USD: "0",
                af_ad_id: "582e8e8d-6b18-4c10-beb2-c5ce48c77115",
                partner: "appsflyer",
                af_sub5: null,
                af_sub4: null,
                af_adset: "UK, All Genders",
                af_sub3: null,
                af_sub2: null,
                adset_id: null,
                http_referrer: null,
                campaign: "C.UK || Android || Install",
                af_ad: "ENGLISH-PICKWISELY-INTROVERT",
                request_id: "062b6a65-a42a-4493-a845-9ea85a0d7149",
                adgroup: null
            }
        }

        appsflyerDataNewPlatform = {
            status: 'success',
            payload: {
                adgroup_id: null,
                af_adset_id: "65abeeaf-8b44-43fb-8bd5-f2e82b2cece5",
                retargeting_conversion_type: "none",
                orig_cost: "0.0",
                is_first_launch: false,
                af_click_lookback: "7d",
                af_cpi: null,
                iscache: true,
                network_account_id: "0e561815-0623-4530-a5dc-b9bf6ab9a3cc",
                click_time: "2025-03-19 20:19:25.250",
                adset: null,
                match_type: "srn",
                af_channel: "Snapchat",
                af_viewthrough_lookback: "1d",
                campaign_id: "abf9df09-494d-43e3-9c37-6b8b9471296e",
                "is_claimed?": "true",
                install_time: "2025-03-20 03:28:24.506",
                af_c_id: "abf9df09-494d-43e3-9c37-6b8b9471296e",
                agency: null,
                media_source: "Test_Ad_Platform",
                af_siteid: null,
                af_status: "Non-organic",
                af_sub1: null,
                cost_cents_USD: "0",
                af_ad_id: "582e8e8d-6b18-4c10-beb2-c5ce48c77115",
                partner: "appsflyer",
                af_sub5: null,
                af_sub4: null,
                af_adset: "UK, All Genders",
                af_sub3: null,
                af_sub2: null,
                adset_id: null,
                http_referrer: null,
                campaign: "C.UK || Android || Install",
                af_ad: "ENGLISH-PICKWISELY-INTROVERT",
                request_id: "062b6a65-a42a-4493-a845-9ea85a0d7149",
                adgroup: null
            }
        }

        appsflyerDataAppleSearch = {
            status: 'success',
            payload: {
                af_status: "Non-organic",
                af_keywords: "1680522310",
                af_sub3: null,
                af_click_lookback: "7d",
                af_sub5: null,
                af_cpi: null,
                orig_cost: "0.0",
                af_sub1: null,
                campaign_id: "1680393348",
                af_sub2: null,
                af_adset_id: "1680491233",
                retargeting_conversion_type: "re-attribution",
                af_c_id: "1680393348",
                af_siteid: null,
                media_source: "Apple Search Ads",
                agency: null,
                match_type: "srn",
                adset_id: null,
                http_referrer: null,
                campaign: "1680393348",
                adgroup_id: null,
                af_sub4: null,
                install_time: "2025-03-22 20:30:18.987",
                is_first_launch: false,
                cost_cents_USD: "0",
                adset: null,
                iscache: true,
                adgroup: null,
                keyword_id: "1680522310",
                af_adset: "1680491233",
                click_time: ""
            }
        }

        appsflyerDataDataseat = {
            status: 'success',
            payload: {
                af_pmod_lookback_window: "1d",
                iscache: true,
                af_sub5: null,
                af_sub4: null,
                af_status: "Non-organic",
                af_sub2: null,
                af_cpi: null,
                af_ad: "13084ef5-6197-40e0-95e2-aea0ab0ba7b8",
                media_source: "dataseat_int",
                ad_ad_id: "13084ef5-6197-40e0-95e2-aea0ab0ba7b8",
                expires: "1743749120480",
                campaign_id: "BSDTST",
                orig_cost: "0.0",
                adset: null,
                is_retargeting: "false",
                adgroup: null,
                adset_id: null,
                adgroup_id: null,
                http_referrer: null,
                af_c_id: "BSDTST",
                af_cost_model: "CPM",
                clickid: "AlZ2dW5nbOVib-8xOTVFRDg1QkZERTAwMjcwQUY0RDcwtjE5NUVEODVCRkRFMDAyMzBBRjRENzC2MTQ5ODQwNzI3smJv70JTRFRT1KUwY2JmZGFkZS0xODNkLTQ2OTctOWRmNS03ZDNjY2MxMmRlNDClMTMwODRlZjUtNjE5Ny00MGUwLTk1ZTItYWVhMGFiMGJhN2I4MjZEMzA3MDItMUE1MS00OTdCLUFGQUEtODU2MTMzODAzMTbE4P-W7N4yADViZTljMTE5NGQzYWRmMDQ0MTRiMTVk5QAAAAAAABBAAKs4gTkyMTc2NTg4uAIAgG0",
                install_time: "2025-04-01 01:35:40.000",
                engmnt_source: null,
                signature: "6mgGcAoWcddnv0hYon2x-FxFrrg7tnwkqA5mRA0dyw0",
                af_sub3: null,
                is_incentivized: "false",
                is_branded_link: null,
                af_cost_currency: "USD",
                campaign: "C. US - English - iOS | Dataseat",
                click_time: "2025-03-31 18:45:26.667",
                af_adset: "0cbfdade-183d-4697-9df5-7d3ccc12de40",
                idfa: "26D30702-1A51-497B-AFAA-85613380316D",
                af_ad_type: "VIDEO_VAST",
                esp_name: null,
                af_adset_id: "0cbfdade-183d-4697-9df5-7d3ccc12de40",
                CB_preload_equal_priority_enabled: false,
                af_siteid: "921765888",
                af_sub1: null,
                is_universal_link: null,
                af_click_lookback: "7d",
                match_type: "id_matching",
                retargeting_conversion_type: "none",
                agency: null,
                cost_cents_USD: "0",
                is_first_launch: false,
                redirect_response_data: null
            }
        }

        appsflyerDataInfluencer = {
            status: 'success',
            payload: {
                redirect_response_data: null,
                adgroup_id: null,
                engmnt_source: null,
                is_incentivized: "false",
                retargeting_conversion_type: "none",
                orig_cost: "0.0",
                is_first_launch: false,
                af_click_lookback: "7d",
                CB_preload_equal_priority_enabled: false,
                af_web_dp: "https://boo.world/u/anime",
                af_cpi: null,
                iscache: true,
                click_time: "2025-04-09 14:01:36.398",
                is_branded_link: null,
                adset: null,
                match_type: "gp_referrer",
                af_channel: "Influencer - IG - Anime - sa1hil_ae",
                campaign_id: null,
                shortlink: "sa1hilae",
                af_pmod_lookback_window: "15m",
                af_dp: "boo://enterprises.dating.boo",
                install_time: "2025-04-09 14:02:15.105",
                fbclid: "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
                agency: null,
                media_source: "Influencer - Anime",
                af_siteid: null,
                af_status: "Non-organic",
                af_sub1: null,
                cost_cents_USD: "0",
                af_sub5: null,
                af_sub4: null,
                af_sub3: null,
                af_sub2: null,
                adset_id: null,
                esp_name: null,
                http_referrer: "https://l.instagram.com/",
                campaign: "Influencer",
                is_universal_link: null,
                is_retargeting: "false",
                adgroup: null
            }
        }

        appsflyerDataLiftoff = {
            status: 'success',
            payload: {
                sha1_idfa: "6E6A3B5B29FA36266FF8E79821D880F5DF4CB72A",
                retargeting_conversion_type: "none",
                adset: null,
                af_os: "18.3.1",
                signature_v2: "y0cm3ZgmNHot0OpD9od6b-w1gSYc__WKOiDsOl4RD2U",
                af_ad: "320x480_Boo Enterprises, Inc._458837_vast_18.0",
                af_sub2: null,
                idfa: "E98493D0-64E3-4A46-B79A-F9C0D91353C7",
                http_referrer: null,
                af_siteid: "1498889847",
                orig_cost: "0.0",
                click_time: "2025-04-12 19:54:18.045",
                af_pmod_lookback_window: "1d",
                af_ad_type: "video",
                af_cpi: null,
                campaign_id: "45751bb19a",
                af_sub5: null,
                is_first_launch: false,
                af_ip: "**************",
                engmnt_source: null,
                af_status: "Non-organic",
                is_retargeting: "false",
                af_click_lookback: "7d",
                agency: null,
                af_sub1: null,
                af_c_id: "45751bb19a",
                CB_preload_equal_priority_enabled: false,
                af_sub3: null,
                af_ad_id: "2fd8c4b3a5",
                af_os_version: "18.3.1",
                clickid: "v.2_g.193725_a.67fac43c62b43974ac821f4a_c.24_t.ua_u.6e6a3b5b29fa3626",
                af_channel: "ACCELERATE",
                is_universal_link: null,
                af_lang: "es",
                af_ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
                is_branded_link: null,
                adgroup_id: null,
                install_time: "2025-04-12 19:57:39.220",
                adgroup: null,
                is_incentivized: "false",
                media_source: "liftoff_int",
                iscache: true,
                campaign: "Signup - C. Mexico L. All",
                af_model: "iPhone 11 Pro Max",
                af_sub4: null,
                match_type: "id_matching",
                redirect: "false",
                cost_cents_USD: "0",
                adset_id: null,
                redirect_response_data: null,
                expires: "1744488557",
                af_viewthrough_lookback: "24h",
                esp_name: null
            }
        }

        appsflyerDataTikTok = {
            status: 'success',
            payload: {
                "is_claimed?": "true",
                agency: null,
                af_status: "Non-organic",
                cost_cents_USD: "0",
                af_ad: "Ads I Can_t Sleep Dating - English Portrait - Meet New People Outro_kBwUgC1p.mp4",
                adgroup_id: null,
                match_type: "srn",
                af_sub1: null,
                http_referrer: null,
                retargeting_conversion_type: "none",
                af_cpi: null,
                click_time: "2025-04-01 03:10:00.000",
                af_viewthrough_lookback: "24h",
                campaign_id: "****************",
                af_sub_siteid: "",
                is_first_launch: true,
                event: "InstallApp",
                media_source: "tiktokglobal_int",
                af_sub4: null,
                af_sub2: null,
                adgroup: null,
                campaign: "C. US L. English || App || Purchase || Android",
                af_click_lookback: "7d",
                af_c_id: "****************",
                af_ad_id: "****************",
                event_id: "210460c7-1b4a-4c17-9bf7-ed4bfa4590d3",
                af_sub5: null,
                adset_id: null,
                orig_cost: "0.0",
                network_account_id: "7465306270531125265",
                af_siteid: null,
                af_adset: "C. US L. English || App || Purchase || Dating",
                install_time: "2025-04-04 17:23:28.817",
                clickid: "E.C.P.CpcBE4xpFtODtn9Hm_NqQR9DUWYh5-PaMZr-A6043qTBixoq5fm7moYRSt5AosZA2cdCEm5AHAjvibzlWHqR8jXJoXVYSZk05e226SxY-Dv8Ff6Z7bahz_DTjPwPiRI1QcXC2vS5eAWAaMsd7Jr-KA043JRNDvr8xLITpcdfjHh-t1_K-2M6d8iw9hzteaNb1kitI-tUBWt9XxIEdjIuMBogJOcZjsrzjN1IQBhAvfE-IMGx-Wxbnryokl-vZEJJr8E",
                af_adset_id: "****************",
                af_ad_type: "short_video",
                iscache: true,
                adset: null,
                af_channel: "TikTok",
                af_sub3: null
            }
        }

        appsflyerDataMoloco = {
            status: 'success',
            payload: {
                engmnt_source: null,
                af_model: "SM-X216B",
                install_time: "2025-04-15 02:38:39.956",
                media_source: "moloco_int",
                cost_cents_USD: "0",
                adgroup: null,
                adset_id: null,
                http_referrer: null,
                is_first_launch: false,
                redirect: "false",
                af_os_version: "14.0.0",
                af_sub3: null,
                af_ad: "index (6).html",
                af_sub4: null,
                retargeting_conversion_type: "none",
                orig_cost: "0.0",
                iscache: true,
                click_time: "2025-04-15 02:37:49.254",
                match_type: "id_matching",
                af_viewthrough_lookback: "24h",
                advertising_id: "df4b3c54-b753-4292-88b0-25c9c2a95c63",
                is_retargeting: "false",
                af_lang: "en",
                af_sub1: null,
                campaign: "Signup - C. India L. English",
                is_incentivized: "false",
                campaign_id: "LgWE2Tsug5KkYkjl",
                clickid: "ChC79jZ-wd5M767wZb_GYtjTEPuM978GGhQIARoQ30s8VLdTQpKIsCXJwqlcYyACKgAyAA",
                af_siteid: "ai.socialapps.speakmaster",
                af_status: "Non-organic",
                adgroup_id: null,
                af_ip: "*************",
                af_channel: "INMOBI",
                af_ua: "Mozilla/5.0 (Linux; Android 14; SM-X216B Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/134.0.6998.135 Safari/537.36",
                af_c_id: "LgWE2Tsug5KkYkjl",
                af_ad_id: "KU8EMiQeqY4AyJ44:a:CwHIdWqVQ6ztMyaR",
                af_ad_type: "playable",
                af_cpi: null,
                adset: null,
                agency: null,
                af_sub5: null,
                af_sub2: null
            }
        }

        sesStub = sinon.stub(fakeSES, 'sendEmail').callsFake((params) => ({
            promise: () => new Promise((resolve, reject) => {
              resolve({});
            }),
        }));
    })

    it('init user with appsflyer data', async () => {

        let res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0)
            .send({appsflyer: appsflyerData});
        expect(res.status).to.equal(200);
        user = await User.findOne({_id: 0}).lean().lean()
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerData.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerData.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerData.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerData.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerData.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerData.payload.af_ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerData.payload.media_source);
        console.log('user.appsflyer: ', user.appsflyer)
        expect(user.appsflyer).to.deep.equal(appsflyerData);
    })

    it('init user without appsflyer & kochava: ', async () => {

        let res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0);
        expect(res.status).to.equal(200);
        user = await User.findOne({_id: 0}).lean()
        expect(user.kochava).to.be.undefined;
        expect(user.appsflyer).to.be.undefined;
    })

    it('init user with kochava && appsflyer data: ', async () => {
        let res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', 0)
            .send({kochava: { network: 'google', partner_campaign_id: 'random_campaign' } , appsflyer: appsflyerData});
        expect(res.status).to.equal(200);
        user = await User.findOne({_id: 0}).lean()
        console.log('user.kochava: ', user.kochava)

        expect(user.kochava.network).to.equal(appsflyerData.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerData.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerData.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerData.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerData.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerData.payload.af_ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerData.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerData);
    })

    it('app visitor with appsflyer data', async () => {

        res = await request(app)
            .put('/web/appVisitor')
            .send({ deviceId: '0', appsflyer: appsflyerData });
        expect(res.status).to.equal(200);

        // res = await request(app)
        //   .put('/v1/user/initApp')
        //   .set('authorization', 0)
        //   .send({ deviceId: '0' });
        // expect(res.status).to.equal(200);

        visitor = await AppVisitor.findOne({ deviceId: '0' }).lean();
        expect(visitor.kochava.network).to.equal(appsflyerData.payload.media_source);
        expect(visitor.kochava.partner_campaign_id).to.equal(appsflyerData.payload.campaign_id);
        expect(visitor.kochava.partner_campaign_name).to.equal(appsflyerData.payload.campaign);
        expect(visitor.kochava.adgroup_id).to.equal(appsflyerData.payload.af_adset_id);
        expect(visitor.kochava.adgroup_name).to.equal(appsflyerData.payload.af_adset);
        expect(visitor.kochava.creative_id).to.equal(appsflyerData.payload.af_ad_id);
        expect(visitor.kochava.tracker).to.equal(appsflyerData.payload.media_source);
        expect(visitor.appsflyer).to.deep.equal(appsflyerData);
    });

    it('app visitor only with appsflyer_id ', async () => {

        res = await request(app)
            .put('/web/appVisitor')
            .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        appsflyerData = {
            appsflyer_id: "test_Appsflyer_id"
        }

        visitor = await AppVisitor.findOne({ deviceId: '0' }).lean();
        expect(visitor.appsflyer).to.deep.equal(appsflyerData);
    });

    it('app visitor with appsflyer_id and appsFlyer Data ', async () => {

        res = await request(app)
            .put('/web/appVisitor')
            .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id", appsflyer: appsflyerData });
        expect(res.status).to.equal(200);

        appsflyerData.appsflyer_id = "test_Appsflyer_id"

        visitor = await AppVisitor.findOne({ deviceId: '0' }).lean();
        expect(visitor.kochava.network).to.equal(appsflyerData.payload.media_source);
        expect(visitor.kochava.partner_campaign_id).to.equal(appsflyerData.payload.campaign_id);
        expect(visitor.kochava.partner_campaign_name).to.equal(appsflyerData.payload.campaign);
        expect(visitor.kochava.adgroup_id).to.equal(appsflyerData.payload.af_adset_id);
        expect(visitor.kochava.adgroup_name).to.equal(appsflyerData.payload.af_adset);
        expect(visitor.kochava.creative_id).to.equal(appsflyerData.payload.af_ad_id);
        expect(visitor.kochava.tracker).to.equal(appsflyerData.payload.media_source);
        expect(visitor.appsflyer).to.deep.equal(appsflyerData);
    });

    it('init user with appsflyer_id then set appsflyer data from FB via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataFB.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataFB });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataFB.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataFB.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataFB.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerDataFB.payload.adgroup_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerDataFB.payload.adgroup);
        expect(user.kochava.creative_id).to.equal(appsflyerDataFB.payload.ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerDataFB.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataFB);
    });

    it('init user with appsflyer_id then set appsflyer data from Snapchat via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataSnap.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataSnap });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataSnap.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataSnap.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataSnap.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerDataSnap.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerDataSnap.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerDataSnap.payload.af_ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerDataSnap.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataSnap);
    });

    it('init user with appsflyer_id then set appsflyer data from Apple Search via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataAppleSearch.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataAppleSearch });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataAppleSearch.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataAppleSearch.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataAppleSearch.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerDataAppleSearch.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerDataAppleSearch.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerDataAppleSearch.payload.keyword_id);
        expect(user.kochava.tracker).to.equal(appsflyerDataAppleSearch.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataAppleSearch);
    });

    it('init user with appsflyer_id then set appsflyer data from Dataseat via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataDataseat.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataDataseat });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataDataseat.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataDataseat.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataDataseat.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerDataDataseat.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerDataDataseat.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerDataDataseat.payload.af_ad);
        expect(user.kochava.tracker).to.equal(appsflyerDataDataseat.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataDataseat);
    });

    it('init user with appsflyer_id then set appsflyer data from Influencer via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataInfluencer.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataInfluencer });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataInfluencer.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(null);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataInfluencer.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(null);
        expect(user.kochava.adgroup_name).to.equal(null);
        expect(user.kochava.creative_id).to.equal(null);
        expect(user.kochava.tracker).to.equal(appsflyerDataInfluencer.payload.af_channel);
        expect(user.appsflyer).to.deep.equal(appsflyerDataInfluencer);
    });

    it('init user with appsflyer_id then set appsflyer data from Liftoff via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataLiftoff.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataLiftoff });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataLiftoff.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataLiftoff.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataLiftoff.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(null);
        expect(user.kochava.adgroup_name).to.equal(null);
        expect(user.kochava.creative_id).to.equal(appsflyerDataLiftoff.payload.af_ad);
        expect(user.kochava.tracker).to.equal(appsflyerDataLiftoff.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataLiftoff);
    });

    it('init user with appsflyer_id then set appsflyer data from Tiktok via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataTikTok.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataTikTok });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataTikTok.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataTikTok.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataTikTok.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerDataTikTok.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerDataTikTok.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerDataTikTok.payload.af_ad);
        expect(user.kochava.tracker).to.equal(appsflyerDataTikTok.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataTikTok);
    });

    it('init user with appsflyer_id then set appsflyer data from Moloco via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataMoloco.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataMoloco });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerDataMoloco.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerDataMoloco.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerDataMoloco.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(null);
        expect(user.kochava.adgroup_name).to.equal(null);
        expect(user.kochava.creative_id).to.equal(appsflyerDataMoloco.payload.af_ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerDataMoloco.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerDataMoloco);
    });

    it('init user with appsflyer_id then set appsflyer data from unrecognize ad platform via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerDataNewPlatform.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerDataNewPlatform });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.appsflyer).to.deep.equal(appsflyerDataNewPlatform);


    });

    it('init user with appsflyer_id then set appsflyer data from google via v1/user/appsflyer', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

        // set appsflyer_id on expected result
        appsflyerData.appsflyer_id = "test_Appsflyer_id"

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerData });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        console.log('user.kochava: ', user.kochava)
        expect(user.kochava.network).to.equal(appsflyerData.payload.media_source);
        expect(user.kochava.partner_campaign_id).to.equal(appsflyerData.payload.campaign_id);
        expect(user.kochava.partner_campaign_name).to.equal(appsflyerData.payload.campaign);
        expect(user.kochava.adgroup_id).to.equal(appsflyerData.payload.af_adset_id);
        expect(user.kochava.adgroup_name).to.equal(appsflyerData.payload.af_adset);
        expect(user.kochava.creative_id).to.equal(appsflyerData.payload.af_ad_id);
        expect(user.kochava.tracker).to.equal(appsflyerData.payload.media_source);
        expect(user.appsflyer).to.deep.equal(appsflyerData);
    });

    it('set appsflyer data via v1/user/appsflyer then add appsflyer_id when second init', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0' });
        expect(res.status).to.equal(200);

        res = await request(app)
            .put('/v1/user/appsflyer')
            .set('authorization', 0)
            .send({ appsflyer: appsflyerData });
        expect(res.status).to.equal(200);

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        expect(user.appsflyer).to.deep.equal(appsflyerData);

        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 0)
          .send({ deviceId: '0', appsflyer_id: "test_Appsflyer_id" });
        expect(res.status).to.equal(200);

         // set appsflyer_id on expected result
        appsflyerData.appsflyer_id = "test_Appsflyer_id"

        user = await User.findOne({ _id: '0' }).lean();
        console.log('user.appsflyer: ', user.appsflyer)
        // expect(user.kochava.network).to.equal(appsflyer.pid);
        // expect(user.kochava.partner_campaign_id).to.equal(appsflyer.af_c_id);
        // expect(user.kochava.partner_campaign_name).to.equal(appsflyer.c);
        // expect(user.kochava.adgroup_id).to.equal(appsflyer.af_adset_id);
        // expect(user.kochava.adgroup_name).to.equal(appsflyer.af_adset);
        // expect(user.kochava.creative_id).to.equal(appsflyer.af_ad_id);
        // expect(user.kochava.tracker).to.equal(appsflyer.clickid);
        expect(user.appsflyer).to.deep.equal(appsflyerData);
    });
})
