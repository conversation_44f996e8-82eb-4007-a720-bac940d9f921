function getMockCheckoutSessionEvent({
    customerId,
    session_mode,
    session_payment_status,
    session_status,
    amount_total,
    amount_discount,
    currency,
    lookup_key,
    type,
}) {
    return {
        "id": "evt_1MBBiZLKeZ1AWr83ZWklMOpn",
        "object": "event",
        "api_version": "2022-11-15",
        "created": 1670135427,
        "data": {
            "object": {
                id: lookup_key,
                "object": "checkout.session",
                "after_expiration": null,
                "allow_promotion_codes": null,
                "amount_subtotal": amount_total,
                "amount_total": amount_total,
                "automatic_tax": {
                    "enabled": false,
                    "status": null
                },
                "billing_address_collection": "auto",
                "cancel_url": "https://d5frix9lkmc68.cloudfront.net",
                "client_reference_id": null,
                "consent": null,
                "consent_collection": null,
                "created": 1670135419,
                "currency": currency,
                "custom_text": {
                    "shipping_address": null,
                    "submit": null
                },
                customer: customerId,
                "customer_creation": null,
                "customer_details": {
                    "address": {
                        "city": null,
                        "country": "JP",
                        "line1": null,
                        "line2": null,
                        "postal_code": null,
                        "state": null
                    },
                    "email": "<EMAIL>",
                    "name": "ocean",
                    "phone": null,
                    "tax_exempt": "none",
                    "tax_ids": []
                },
                "customer_email": null,
                "expires_at": 1670221818,
                "livemode": false,
                "locale": null,
                //"metadata": {},//apparently mongoose does not save this field
                mode: session_mode,
                "payment_intent": null,
                "payment_link": null,
                "payment_method_collection": "always",
                "payment_method_options": null,
                "payment_method_types": [
                    "card"
                ],
                payment_status: session_payment_status,
                "phone_number_collection": {
                    "enabled": false
                },
                "recovered_from": null,
                "setup_intent": null,
                "shipping_address_collection": null,
                "shipping_cost": null,
                "shipping_details": null,
                "shipping_options": [],
                status: session_status,
                "submit_type": null,
                "subscription": "sub_1MBBiWLKeZ1AWr833sYNrU4c",
                "success_url": "https://d5frix9lkmc68.cloudfront.net",
                "total_details": {
                    "amount_discount": amount_discount || 0,
                    "amount_shipping": 0,
                    "amount_tax": 0
                },
                "url": null
            }
        },
        "livemode": false,
        "pending_webhooks": 1,
        "request": {
            "id": null,
            "idempotency_key": null
        },
        "type": type || "checkout.session.completed",
    }

}

function getMockSubscriptionEvent({
    customerId,
    current_period_end,
    status
}) {
    return {
        "id": "evt_1MBBiZLKeZ1AWr83w64YsWxb",
        "object": "event",
        "api_version": "2022-11-15",
        "created": 1670135426,
        "data": {
            "object": {
                id: 'id',
                "object": "subscription",
                "application": null,
                "application_fee_percent": null,
                "automatic_tax": {
                    "enabled": false
                },
                "billing_cycle_anchor": **********,
                "billing_thresholds": null,
                "cancel_at": null,
                "cancel_at_period_end": false,
                "canceled_at": null,
                "collection_method": "charge_automatically",
                "created": **********,
                "currency": "usd",
                current_period_end: current_period_end,
                "current_period_start": **********,
                customer: customerId,
                "days_until_due": null,
                "default_payment_method": "pm_1MAsMGLKeZ1AWr83noXnEb3I",
                "default_source": null,
                "default_tax_rates": [],
                "description": null,
                "discount": null,
                "ended_at": null,
                "items": {
                    "object": "list",
                    "data": [
                        {
                            "id": "si_Mv1m1gwkyh4KYD",
                            "object": "subscription_item",
                            "billing_thresholds": null,
                            "created": 1670135425,
                            "metadata": {},
                            "plan": {
                                "id": "price_1MAQwZLKeZ1AWr83IrW8RWZY",
                                "object": "plan",
                                "active": true,
                                "aggregate_usage": null,
                                "amount": 2000,
                                "amount_decimal": "2000",
                                "billing_scheme": "per_unit",
                                "created": **********,
                                "currency": "usd",
                                "interval": "month",
                                "interval_count": 1,
                                "livemode": false,
                                "metadata": {},
                                "nickname": null,
                                "product": "prod_MuFRgD6BRwGbkf",
                                "tiers_mode": null,
                                "transform_usage": null,
                                "trial_period_days": null,
                                "usage_type": "licensed"
                            },
                            "price": {
                                "id": "price_1MAQwZLKeZ1AWr83IrW8RWZY",
                                "object": "price",
                                "active": true,
                                "billing_scheme": "per_unit",
                                "created": **********,
                                "currency": "usd",
                                "custom_unit_amount": null,
                                "livemode": false,
                                "lookup_key": "web_infinity_m1",
                                "metadata": {},
                                "nickname": null,
                                "product": "prod_MuFRgD6BRwGbkf",
                                "recurring": {
                                    "aggregate_usage": null,
                                    "interval": "month",
                                    "interval_count": 1,
                                    "trial_period_days": null,
                                    "usage_type": "licensed"
                                },
                                "tax_behavior": "unspecified",
                                "tiers_mode": null,
                                "transform_quantity": null,
                                "type": "recurring",
                                "unit_amount": 2000,
                                "unit_amount_decimal": "2000"
                            },
                            "quantity": 1,
                            "subscription": "sub_1MBBiWLKeZ1AWr833sYNrU4c",
                            "tax_rates": []
                        }
                    ],
                    "has_more": false,
                    "total_count": 1,
                    "url": "/v1/subscription_items?subscription=sub_1MBBiWLKeZ1AWr833sYNrU4c"
                },
                "latest_invoice": "in_1MBBiWLKeZ1AWr83AzAs6Qqe",
                "livemode": false,
                "metadata": {},
                "next_pending_invoice_item_invoice": null,
                "on_behalf_of": null,
                "pause_collection": null,
                "payment_settings": {
                    "payment_method_options": null,
                    "payment_method_types": null,
                    "save_default_payment_method": "off"
                },
                "pending_invoice_item_interval": null,
                "pending_setup_intent": null,
                "pending_update": null,
                "plan": {
                    "id": "price_1MAQwZLKeZ1AWr83IrW8RWZY",
                    "object": "plan",
                    "active": true,
                    "aggregate_usage": null,
                    amount: 2000,
                    "amount_decimal": "2000",
                    "billing_scheme": "per_unit",
                    "created": **********,
                    currency: 'usd',
                    "interval": "month",
                    "interval_count": 1,
                    "livemode": false,
                    "metadata": {},
                    "nickname": null,
                    "product": "prod_MuFRgD6BRwGbkf",
                    "tiers_mode": null,
                    "transform_usage": null,
                    "trial_period_days": null,
                    "usage_type": "licensed"
                },
                "quantity": 1,
                "schedule": null,
                "start_date": **********,
                status: status,
                "test_clock": null,
                "transfer_data": null,
                "trial_end": null,
                "trial_start": null
            },
            "previous_attributes": {
                "default_payment_method": null,
                "status": "incomplete"
            }
        },
        "livemode": false,
        "pending_webhooks": 1,
        "request": {
            "id": "req_Wk4kfP0AWV1Qf8",
            "idempotency_key": "d5d7d564-1fb1-422e-b28e-89592332f87e"
        },
        type: 'customer.subscription.updated',
    }

}

function getMockChargeSucceededEvent({
  customerId,
  cardCountry,
}) {
  return {
    "id": "evt_1Z0n3o2eZvKYlo2CrJ3G4f3Q",
    "object": "event",
    "api_version": "2025-05-28.basil",
    "created": **********,
    "data": {
      "object": {
        "id": "ch_3Z0n3n2eZvKYlo2C0u3mN1qP",
        "object": "charge",
        "amount": 2000,
        "amount_captured": 2000,
        "amount_refunded": 0,
        "application": null,
        "application_fee": null,
        "application_fee_amount": null,
        "balance_transaction": "txn_3Z0n3n2eZvKYlo2C0FDQ7Ajy",
        "billing_details": {
          "address": {
            "city": null,
            "country": null,
            "line1": null,
            "line2": null,
            "postal_code": null,
            "state": null
          },
          "email": null,
          "name": null,
          "phone": null
        },
        "calculated_statement_descriptor": "STRIPE",
        "captured": true,
        "created": 1760004998,
        "currency": "usd",
        "customer": customerId,
        "description": "(created by Stripe CLI)",
        "disputed": false,
        "fraud_details": {},
        "livemode": false,
        "metadata": {},
        "outcome": {
          "network_status": "approved_by_network",
          "reason": null,
          "risk_level": "normal",
          "seller_message": "Payment complete.",
          "type": "authorized",
          "advice_code": null
        },
        "paid": true,
        "payment_intent": "pi_3Z0n3n2eZvKYlo2C2nXYdG7o",
        "payment_method": "pm_1Z0n3m2eZvKYlo2CADX5iKQx",
        "payment_method_details": {
          "card": {
            "brand": "visa",
            "checks": {
              "address_line1_check": null,
              "address_postal_code_check": null,
              "cvc_check": "pass"
            },
            "country": cardCountry,
            "exp_month": 4,
            "exp_year": 2034,
            "fingerprint": "Xt5EWLLDS7FJjR1c",
            "funding": "credit",
            "installments": null,
            "last4": "4242",
            "mandate": null,
            "network": "visa",
            "three_d_secure": null,
            "wallet": null
          },
          "type": "card"
        },
        "receipt_email": null,
        "receipt_number": null,
        "receipt_url": "https://pay.stripe.com/receipts/test_...",
        "refunded": false,
        "refunds": {
          "object": "list",
          "data": [],
          "has_more": false,
          "total_count": 0,
          "url": "/v1/charges/ch_3Z0n3n2eZvKYlo2C0u3mN1qP/refunds"
        },
        "review": null,
        "shipping": null,
        "statement_descriptor": null,
        "statement_descriptor_suffix": null,
        "status": "succeeded",
        "transfer_group": null
      },
      "previous_attributes": null
    },
    "livemode": false,
    "pending_webhooks": 1,
    "request": {
      "id": "req_PZj1Fb4Sg3bZB9",
      "idempotency_key": null
    },
    "type": "charge.succeeded"
  }
}

function getMockPaymentIntentSucceededEvent({
  customerId,
  currency,
}) {
  return {
    "id": "evt_3RfVlqLKeZ1AWr830SSYWXTV",
    "object": "event",
    "api_version": "2022-11-15",
    "created": 1751244033,
    "data": {
      "object": {
        "id": "pi_3RfVlqLKeZ1AWr830tFcQJ2d",
        "object": "payment_intent",
        "amount": 500,
        "amount_capturable": 0,
        "amount_details": {
          "tip": {
          }
        },
        "amount_received": 500,
        "application": null,
        "application_fee_amount": null,
        "automatic_payment_methods": null,
        "canceled_at": null,
        "cancellation_reason": null,
        "capture_method": "automatic",
        "client_secret": "pi_3RfVlqLKeZ1AWr830tFcQJ2d_secret_ukmCt1Ux6oH9HmG320A8k1j2d",
        "confirmation_method": "automatic",
        "created": 1751244030,
        "currency": currency,
        "customer": customerId,
        "description": "Subscription creation",
        "invoice": "in_1RfVlqLKeZ1AWr83ZNWz3m2C",
        "last_payment_error": null,
        "latest_charge": "ch_3RfVlqLKeZ1AWr830W7D1HAF",
        "livemode": true,
        "metadata": {
        },
        "next_action": null,
        "on_behalf_of": null,
        "payment_method": "pm_1RfVlpLKeZ1AWr83fb7tx22H",
        "payment_method_configuration_details": null,
        "payment_method_options": {
          "card": {
            "installments": null,
            "mandate_options": null,
            "network": null,
            "request_three_d_secure": "automatic",
            "setup_future_usage": "off_session"
          },
          "link": {
            "persistent_token": null
          }
        },
        "payment_method_types": [
          "card",
          "link"
        ],
        "processing": null,
        "receipt_email": null,
        "review": null,
        "setup_future_usage": "off_session",
        "shipping": null,
        "source": null,
        "statement_descriptor": null,
        "statement_descriptor_suffix": null,
        "status": "succeeded",
        "transfer_data": null,
        "transfer_group": null
      }
    },
    "livemode": true,
    "pending_webhooks": 1,
    "request": {
      "id": null,
      "idempotency_key": "681d3fe4-1706-47f1-9053-d9a720dcf3e3"
    },
    "type": "payment_intent.succeeded"
  }
}

module.exports = {
    getMockCheckoutSessionEvent,
    getMockSubscriptionEvent,
    getMockChargeSucceededEvent,
    getMockPaymentIntentSucceededEvent,
}
