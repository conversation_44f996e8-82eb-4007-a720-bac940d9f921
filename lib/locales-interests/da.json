{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "kognitivefunktioner", "psychology": "psykologi", "philosophy": "filos<PERSON><PERSON>", "history": "historie", "physics": "fysik", "science": "videnskab", "culture": "kultur", "languages": "sprog", "technology": "teknologi", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologimemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "showerthoughts", "funny": "sjovt", "videos": "videoer", "gadgets": "gadgets", "politics": "politik", "relationshipadvice": "forholdsrådgivning", "lifeadvice": "livsrådgivning", "crypto": "crypto", "news": "n<PERSON><PERSON><PERSON>", "worldnews": "verdensnyheder", "archaeology": "arkæologi", "learning": "<PERSON><PERSON><PERSON>", "debates": "debatter", "conspiracytheories": "konspirationsteorier", "universe": "universet", "meditation": "meditation", "mythology": "mytologi", "art": "kunst", "crafts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dance": "dans", "design": "design", "makeup": "makeup", "beauty": "sk<PERSON><PERSON><PERSON>", "fashion": "mode", "singing": "sang", "writing": "litteratur", "photography": "fotografering", "cosplay": "cosplay", "painting": "maleri", "drawing": "tegne", "books": "<PERSON><PERSON><PERSON>", "movies": "film", "poetry": "poesi", "television": "<PERSON><PERSON><PERSON><PERSON>", "filmmaking": "filmproduktion", "animation": "animation", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumentarfilm", "mystery": "mysterie", "comedy": "komedie", "crime": "kriminalitet", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "gys", "romance": "romantik", "realitytv": "realitytv", "action": "action", "music": "musik", "blues": "blues", "classical": "klassisk", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronisk", "folk": "folkemusik", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "rejse", "concerts": "koncerter", "festivals": "festivaler", "museums": "museer", "standup": "standup", "theater": "teater", "outdoors": "<PERSON><PERSON>d<PERSON><PERSON>", "gardening": "have<PERSON><PERSON>j<PERSON>", "partying": "fester", "gaming": "gaming", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "skak", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "mad", "baking": "bagning", "cooking": "madlavning", "vegetarian": "vegetar", "vegan": "vegansk", "birds": "fugle", "cats": "katte", "dogs": "hunde", "fish": "fisk", "animals": "dyr", "blacklivesmatter": "blacklivesmatter", "environmentalism": "miljøbevidsthed", "feminism": "feminisme", "humanrights": "menneskerettigheder", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "frivilligtarbejde", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "basketball", "boxing": "boksning", "cricket": "cricket", "cycling": "cykling", "fitness": "fitness", "football": "fodbold", "golf": "golf", "gym": "fitnesscenter", "gymnastics": "gymnastik", "hockey": "hockey", "martialarts": "kampsport", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON>ø<PERSON>", "skateboarding": "skateboarding", "skiing": "ski", "snowboarding": "snowboard", "surfing": "surfing", "swimming": "svømning", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "vægtløftning", "yoga": "yoga", "scubadiving": "dykning", "hiking": "<PERSON><PERSON><PERSON>", "capricorn": "stenbukken", "aquarius": "<PERSON><PERSON><PERSON>", "pisces": "fiskene", "aries": "væ<PERSON><PERSON>", "taurus": "tyren", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON><PERSON>", "leo": "<PERSON><PERSON><PERSON>", "virgo": "jom<PERSON><PERSON>n", "libra": "væ<PERSON>en", "scorpio": "skorpionen", "sagittarius": "skytten", "shortterm": "kort<PERSON><PERSON><PERSON>", "casual": "afslappet", "longtermrelationship": "langvarigforhold", "single": "single", "polyamory": "polyamour<PERSON><PERSON>", "enm": "ikkemonogam", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "homoseksuel", "lesbian": "lesbisk", "bisexual": "biseksuel", "pansexual": "pan<PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "<PERSON><PERSON><PERSON>", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "vagthunde", "dislyte": "dislyte", "rougelikes": "roguelikes", "kingsquest": "kingsquest", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiv", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "solnedgangsoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gil<PERSON><PERSON><PERSON><PERSON>", "openworld": "åbenvworld", "heroesofthestorm": "heltefrastormen", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "stam<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "farveliv", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "rollespil", "witcher": "trol<PERSON><PERSON>", "dishonored": "skuffet", "eldenring": "elden<PERSON>", "darksouls": "darksoulsspiel", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "faldout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "tegnkarakter", "immersive": "immersiv", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivation", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "suckerforlove", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampyrspilletmaskeleg", "dimension20": "dimension20", "gaslands": "gaskerland", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndudgave", "shadowrun": "<PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "blodpåurværket", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "elsknikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "tyng<PERSON><PERSON><PERSON>", "rpg": "rollespil", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overherre", "yourturntodie": "din_tur_at_dø", "persona3": "persona3", "rpghorror": "rpgskræmmende", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "falloutshelter", "gurps": "gurps", "darkestdungeon": "mørkestehule", "eclipsephase": "formørkelsefase", "disgaea": "disgaea", "outerworlds": "yderverdenen", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastiskekrigere", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "nattenby", "hogwartslegacy": "hog<PERSON><PERSON><PERSON>", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "vej96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamriddere", "forgottenrealms": "glemteverdener", "dragonlance": "dragekaste", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "barn<PERSON>ly", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonverden", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "økopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "brudte_troner", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "skyggepunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "lasttidspunkt", "starfinder": "stjernesøger", "goldensun": "gyldensol", "divinityoriginalsin": "guddommeligoriginalsynd", "bladesinthedark": "bladeidtmørket", "twilight2000": "skumring2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrød", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "faldentorden", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ondelande", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "devil<PERSON><PERSON>er", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "gudd<PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "gamleverdensblues", "adventurequest": "<PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rollespilgames", "roleplayinggames": "rollespilsgames", "finalfantasy9": "finalfantasy9", "sunhaven": "solhaven", "talesofsymphonia": "historierfrasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfalldk", "torncity": "torncity", "myfarog": "minfarog", "sacredunderworld": "sacredeunderverden", "chainedechoes": "kædedeekkok", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "sj<PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON>", "mountandblade": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "søjlerneafevigheden", "palladiumrpg": "palladiumrpg", "rifts": "revner", "tibia": "tibia", "thedivision": "divisionen", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendafdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octopathrejsende", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "varulvenapokalypse", "aveyond": "aveyond", "littlewood": "lilletræ", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorhjerte", "fable3": "fable3", "fablethelostchapter": "fabeldettabtekapitel", "hiveswap": "hiveswap", "rollenspiel": "rollespil", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "stjernehimmel", "oldschoolrevival": "gamleklassikere", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "mørketsdungeon", "juegosrpg": "rollespil_eventyr", "kingdomhearts": "kongerigetilhjerter", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "<PERSON>ggehjerter", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblod", "breathoffire4": "åndedrætsild4", "mother3": "mor3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "andeteden", "roleplaygames": "rollespilgames", "roleplaygame": "rollespil<PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "vejrhj<PERSON><PERSON>perrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirlamaske", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kronokryds", "cocttrpg": "cocttrpg", "huntroyale": "jagtkonge", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterjægerverden", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "skyggehjertersammenslutning", "bladesoul": "bladeånd", "baldursgate3": "baldursgate3", "kingdomcome": "riketkommer", "awplanet": "awplanet", "theworldendswithyou": "verdenendermeddig", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "døendelys2", "finalfantasytactics": "finalfantasytaktik", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "sortbog", "skychildrenoflight": "himmelsbørnaflys", "gryrpg": "gryrpg", "sacredgoldedition": "helliguldudgave", "castlecrashers": "<PERSON><PERSON>lusker<PERSON>", "gothicgame": "gothicspil", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "byensdamp", "indierpg": "indierpg", "pointandclick": "punktogklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "uadskillelig", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7everkrise", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "dødvejentilcanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "spøgelsetaftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterjægerstige", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktiskrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "evigsonate", "princessconnect": "prinsesseforbindelse", "hexenzirkel": "heksestation", "cristales": "krystaller", "vcs": "vcs", "pes": "pise", "pocketsage": "lommevise", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efodbold", "nba2k": "nba2k", "egames": "espil", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "drømmerligaen", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efodbold", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "cybersport", "crazyraccoon": "skørlandyr", "test1test": "test1test", "fc24": "fc24", "riotgames": "ragespil", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorant<PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantdk", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "halflife", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "evi<PERSON><PERSON><PERSON>", "goatsimulator": "gedesimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "frihedsplanet", "transformice": "transformice", "justshapesandbeats": "bareformerogrytmer", "battlefield4": "battleground4", "nightinthewoods": "nattiviaskovene", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "dybberockgalaktisk", "riskofrain2": "risikofoverregn2", "metroidvanias": "metroidvanias", "overcooked": "overcooked", "interplanetary": "interplanetarisk", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "ræ<PERSON><PERSON>", "stray": "<PERSON><PERSON><PERSON><PERSON>", "battlefield": "krigsskueplads", "battlefield1": "battlefield1", "swtor": "swtordk", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "øje", "blackdesert": "<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "bordspilssimulator", "partyhard": "festh<PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON>keppbrækker", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "fangetmedjester", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON>", "rainworld": "regnverden", "cavesofqud": "<PERSON><PERSON><PERSON>fa<PERSON>", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "krigensmorgon", "minionmasters": "minion<PERSON><PERSON>", "grimdawn": "grimdawn", "darkanddarker": "mørktogmørkere", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "soulworker", "datingsims": "datingsims", "yaga": "yaga", "cubeescape": "kubeflugt", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nyby", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtueltkenopsia", "snowrunner": "snedriver", "libraryofruina": "bibliotekforruina", "l4d2": "l4d2", "thenonarygames": "denonærelege", "omegastrikers": "omegastrikers", "wayfinder": "vejviser", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "roligplastikand", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialbyen", "smileforme": "smiltilmig", "catnight": "kattenat", "supermeatboy": "supermeatboy", "tinnybunny": "tinny<PERSON><PERSON>", "cozygrove": "hyggesump", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "regnbue6", "apexlegends": "apexlegends", "cod": "kode", "borderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "toppe", "r6siege": "r6belægning", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "farcryspil", "paladins": "paladins", "earthdefenseforce": "jordforsvarsstyrken", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "spøgelsesgennemgang", "grandtheftauto5": "grandtheftauto5", "warz": "krigz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "blivdelafholdet", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "oprørsandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "regnbueseksbelægning", "deathstranding": "dødsstranding", "b4b": "b4b", "codwarzone": "codkrigszonen", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombier", "mirrorsedge": "spe<PERSON><PERSON><PERSON>", "divisions2": "divisioner2", "killzone": "dr<PERSON><PERSON><PERSON>", "helghan": "he<PERSON><PERSON>", "coldwarzombies": "coldwar<PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "esportkrig", "crosscode": "krydskode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernekrigsførelse", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "g<PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overvagt", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "verdenskrigsskibe", "back4blood": "back4blood", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6belægning", "xcom": "xcom", "hitman": "hit<PERSON>en", "masseffect": "masseffect", "systemshock": "systemchok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "hulehistorie", "doometernal": "doometernal", "centuryageofashes": "århundredetsasker", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generationzero", "enterthegungeon": "gåindepumpen", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernekrigsførelse2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "krigans<PERSON><PERSON>", "crossfire": "krydsild", "atomicheart": "atomhjertet", "blackops3": "blackops3", "vampiresurvivors": "vampyroverlevere", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "fridom", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "spilpubg", "necromunda": "necrom<PERSON>", "metalgearsonsoflibert": "metalgearsoflibert", "juegosfps": "fpsspil", "convertstrike": "konverteringsslagsmål", "warzone2": "krigszonen2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "blodi<PERSON>d", "republiccommando": "republikkommandør", "elitedangerous": "elitefarer", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "bande", "destiny1": "skæbne1", "gamingfps": "gamingfps", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pub<PERSON><PERSON><PERSON>", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "indskrevet", "farlight": "<PERSON>li<PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "panserkernen", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinasunderverden", "halo2": "halo2", "payday2": "lønindbetaling2", "cs16": "cs16", "pubgindonesia": "pubgindonesien", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgcz", "titanfall2": "titanfall2", "soapcod": "sæbesild", "ghostcod": "spøgelsescod", "csplay": "csleg", "unrealtournament": "uvirkeligtturnering", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "grænselande2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "jordskælvshelte", "halo3": "halo3", "halo": "halo", "killingfloor": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "skæbne2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON>", "remnant": "rester", "azurelane": "azurelane", "worldofwar": "krigernesverden", "gunvolt": "gunvolt", "returnal": "retur", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON>ggemand", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "batalje3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "piraterne", "rust": "ruster", "conqueronline": "erobreonline", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "krigsskibe", "dayofdragons": "dragenesdag", "warthunder": "warthunder", "flightrising": "flyvstejr", "recroom": "recroom", "legendsofruneterra": "legendersforruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "verdenskrigstanke", "crossout": "stregud", "agario": "agario", "secondlife": "andreliv", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "netleg", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtasonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ridderonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "kodeåre", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "skarn", "newworld": "nyverden", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "hæderafkonger", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmospil", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostalgi", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "mobaspil", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "askeserafskabelse", "riotmmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "mullegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "drageprofet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "englesonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversonline", "growtopia": "vækstopia", "starwarsoldrepublic": "starwarsgamlerepublik", "grandfantasia": "grandfant<PERSON>", "blueprotocol": "blå<PERSON><PERSON><PERSON><PERSON>", "perfectworld": "perfektverden", "riseonline": "stigne<PERSON>t", "corepunk": "kernepunk", "adventurequestworlds": "eventyrjagtworlds", "flyforfun": "flyforfun", "animaljam": "dyrjam", "kingdomofloathing": "kongedommenafkedsomhed", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "for<PERSON>or", "tekken": "tekken", "guiltygear": "skyldiggear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "gaderneharråbt", "mkdeadlyalliance": "mkdødeligalliances", "nomoreheroes": "ingenfle<PERSON><PERSON>te", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kongenafk<PERSON>ere", "likeadragon": "somadrage", "retrofightinggames": "retrofightspil", "blasphemous": "blasfemisk", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmashe", "mugen": "mugen", "warofthemonsters": "krigmodmonstrene", "jogosdeluta": "kampenervibe", "cyberbots": "cyberbots", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "afslutningskamp", "poweredgear": "kraftudstyr", "beatemup": "sparkdemtiljorden", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "slåskampe", "killerinstinct": "dræberinstinkt", "kingoffigthers": "kampsportskongen", "ghostrunner": "spøgelsesløber", "chivalry2": "ridderlighed2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongnyheder", "silksong": "silksong", "undernight": "undernat", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutionsturnering", "evomoment": "evomoment", "lollipopchainsaw": "lollipopkædesav", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "historierfraberseria", "bloodborne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizon": "horisont", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "krashbandicoot", "bloodbourne": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "ukendt", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "be<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationvenner", "ps1": "ps1", "oddworld": "mærkeligeverden", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "kaniner", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiler", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "grise", "trove": "skatkiste", "detroitbecomehuman": "detroitblivmenneskelig", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "indtildaggry", "touristtrophy": "turisttrofæ", "lspdfr": "lspdfr", "shadowofthecolossus": "skyggenafkolosserne", "crashteamracing": "crashteamracing", "fivepd": "fempd", "tekken7": "tekken7", "devilmaycry": "djævlenkangræde", "devilmaycry3": "djævlenkangræde3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samura<PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "densidstevogter", "soulblade": "sjælsblad", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "jag<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "sidstevokter", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "festdyr", "warharmmer40k": "warhammer40k", "fightnightchampion": "fightnightchampion", "psychonauts": "psykonauter", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "denældrescrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "slagsfelt", "dontstarvetogether": "ladikkedinevennerståsammested", "ori": "ori", "spelunky": "spilunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stjernerejsende", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "husrenovator", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "kongernesliga", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "skraldtv", "skycotl": "himmelseng", "erica": "erica", "ancestory": "anfædre", "cuphead": "cuphead", "littlemisfortune": "lillemisheld", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "ydertjek", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "lammesekt", "duckgame": "andespil", "thestanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "langtur", "satisfactory": "tilfredsstillende", "pluviophile": "pluviophile", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geometrihop", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "ånderfarer", "darkdome": "mørkdome", "pizzatower": "pizzatornet", "indiegame": "indiespil", "itchio": "itchio", "golfit": "golfit", "truthordare": "sandhedellervovestykke", "game": "spil", "rockpaperscissors": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "v<PERSON>g", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "vä<PERSON><PERSON><PERSON><PERSON>mer", "trueorfalse": "sandellerfalsk", "beerpong": "beerpong", "dicegoblin": "ternedreng", "cosygames": "hyggespil", "datinggames": "<PERSON><PERSON><PERSON>", "freegame": "gratiss<PERSON>l", "drinkinggames": "drikspil", "sodoku": "sodoku", "juegos": "spil", "mahjong": "mahjong", "jeux": "spil", "simulationgames": "simulationspil", "wordgames": "ordspil", "jeuxdemots": "ordlege", "juegosdepalabras": "ordleg", "letsplayagame": "ladoslegeetspil", "boredgames": "kederomme", "oyun": "leg", "interactivegames": "interaktivelege", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "spille", "giochi": "spil", "geoguessr": "geoguessr", "iphonegames": "iphonespil", "boogames": "boogames", "cranegame": "kranespil", "hideandseek": "legegemmeogsøge", "hopscotch": "sten<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "arkadespil", "yakuzagames": "yakuzagames", "classicgame": "klassiskspil", "mindgames": "tankespil", "guessthelyric": "gæ<PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "romancegame", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "tungev<PERSON><PERSON><PERSON>", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "arkadespil", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "spil90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "racinggames", "ets2": "ets2", "realvsfake": "ægtevsfalsk", "playgames": "spilspil", "gameonline": "spilonline", "onlinegames": "onlinegames", "jogosonline": "onlinegames", "writtenroleplay": "skrevenspillere", "playaballgame": "spilaboldspil", "pictionary": "tegnespil", "coopgames": "coopspil", "jenga": "jenga", "wiigames": "wiispil", "highscore": "h<PERSON><PERSON><PERSON>re", "jeuxderôles": "rollespil", "burgergames": "burgerlege", "kidsgames": "b<PERSON><PERSON>spil", "skeeball": "skeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "konku<PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "spil", "managementgame": "ledelsesspil", "hiddenobjectgame": "skjultobjektspil", "roolipelit": "rulospil", "formula1game": "formel1spil", "citybuilder": "bybygger", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "arcadegames", "memorygames": "hukommelsesspil", "vulkan": "vulkan", "actiongames": "aktionsspil", "blowgames": "blowgames", "pinballmachines": "flippermaskiner", "oldgames": "gam<PERSON>e", "couchcoop": "sofa<PERSON>ie", "perguntados": "spørgsmålsplash", "gameo": "spilmo", "lasergame": "laserspil", "imessagegames": "imessagegames", "idlegames": "dovnespil", "fillintheblank": "fyldudfyldningen", "jeuxpc": "pcspil", "rétrogaming": "retrospil", "logicgames": "logikspil", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "metrogallop", "jeuxdecelebrite": "berømthedspil", "exitgames": "exitgames", "5vs5": "5vs5", "rolgame": "rolspil", "dashiegames": "dashiegames", "gameandkill": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "traditionellespil", "kniffel": "kniffel", "gamefps": "spilfps", "textbasedgames": "tekstbaseredespil", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantafodbold", "retrospel": "retrospil", "thiefgame": "tyvespil", "lawngames": "plænelege", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "bordsfodbold", "tischfußball": "bordfodbold", "spieleabende": "spilleaften", "jeuxforum": "spilforum", "casualgames": "afslappetspil", "fléchettes": "dartskud", "escapegames": "flugtspil", "thiefgameseries": "tyvspilserie", "cranegames": "kranespil", "játék": "spil", "bordfodbold": "bordfodbold", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "mave", "cargames": "bilspil", "onlineplay": "onlineleg", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "gamedage", "pursebingos": "pursebingos", "randomizer": "tilfældiggenerator", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "spilpc", "socialdeductiongames": "socialdeduktionsspil", "dominos": "dominos", "domino": "domino", "isometricgames": "isometriskespil", "goodoldgames": "godtgamlespil", "truthanddare": "sandhedogudfordring", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "spi<PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pspiltype", "free2play": "free2play", "fantasygame": "fantasyspil", "gryonline": "gryonline", "driftgame": "driftspil", "gamesotomes": "spiltilmig", "halotvseriesandgames": "halotvseriesogspil", "mushroomoasis": "svampeoase", "anythingwithanengine": "altmedenmotor", "everywheregame": "overaltspil", "swordandsorcery": "sværdogsjæle", "goodgamegiving": "godtgavengiving", "jugamos": "spiller", "lab8games": "lab8spil", "labzerogames": "labzerospil", "grykomputerowe": "boospil", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "ryt<PERSON>pi<PERSON>", "minaturegames": "miniaturespil", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "selvkærlighedspil", "gamemodding": "spilmodding", "crimegames": "kriminalspil", "dobbelspellen": "dobbelspellen", "spelletjes": "spil", "spacenerf": "rummaske", "charades": "charader", "singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "coopgame": "coopspil", "gamed": "spillet", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "hovedspil", "kingdiscord": "kingdiscord", "scrabble": "skrabble", "schach": "skak", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemiarv", "camelup": "kamelspring", "monopolygame": "monopolspil", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "selskabsspil", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "blodskål", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "se<PERSON>t", "goboardgame": "gobordspil", "connectfour": "forbindfire", "heroquest": "helteeventyr", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "terningspil", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "bordspil", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmiskmøde", "creationludique": "legeskabelse", "tabletoproleplay": "bordrollespil", "cardboardgames": "papirs<PERSON>l", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchboardgames", "infinitythegame": "<PERSON><PERSON><PERSON>gspillet", "kingdomdeath": "kongerigetdød", "yahtzee": "yatzy", "chutesandladders": "rutsjebanerogstige", "társas": "boo<PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "redneckliv", "boardom": "kedsomhed", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "selskabsleg", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "hesteopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "mansionsofmadness", "gomoku": "gomoku", "giochidatavola": "spilpåbordet", "shadowsofbrimstone": "skyggersombrændstof", "kingoftokyo": "kongenafkjøbenhavn", "warcaby": "k<PERSON><PERSON>by", "táblajátékok": "táblajátékok", "battleship": "flådeskib", "tickettoride": "billettilatride", "deskovehry": "deskovehry", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "kinesiskeskak", "jeuxsociete": "bestefamiliespil", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "krigszonen", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dagz", "identityv": "identitetv", "theisle": "øen", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyogblekkemaskinen", "conanexiles": "conanexiles", "eft": "eft", "amongus": "blandos", "eco": "økologisk", "monkeyisland": "a<PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafting", "daysgone": "dageneergået", "fobia": "fobi", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologisk", "zomboid": "zomboid", "northgard": "nordgard", "7dtd": "7dtd", "thelongdark": "denlangemørke", "ark": "ark", "grounded": "jordenpåkødet", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "madfar", "dontstarve": "sultikendøre", "eternalreturn": "evigtilbagevenden", "pathoftitans": "titanstien", "frictionalgames": "frictionalgames", "hexen": "he<PERSON>e", "theevilwithin": "detondeindeni", "realrac": "ædreal<PERSON>", "thebackrooms": "baglokalerne", "backrooms": "backrooms", "empiressmp": "empiresmp", "blockstory": "blockhistorie", "thequarry": "brudstedet", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "vierværdsatfå", "riseofempires": "rigernesopstigning", "stateofsurvivalgame": "overlevelsespillet", "vintagestory": "vintagehistorie", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotraume", "breathedge": "breathedeep", "alisa": "alisa", "westlendsurvival": "vestlændingsoverlevelse", "beastsofbermuda": "beastsofber<PERSON>da", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "overlevelseskræk", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentond4", "residentevil3": "residentondød3", "voidtrain": "tom<PERSON>g", "lifeaftergame": "liveteftergame", "survivalgames": "overlevelsesspil", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "thiswarofmine", "scpfoundation": "scpfoundation", "greenproject": "grønprojekt", "kuon": "kuon", "cryoffear": "grædafskrækken", "raft": "<PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "gr<PERSON><PERSON><PERSON>", "residentevil5": "residentondat5", "deadpoly": "dødpoly", "residentevil8": "residentondk8", "onironauta": "onironauta", "granny": "bedstemor", "littlenightmares2": "småmareridt2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rustvideospil", "outlasttrials": "overleveprøverne", "alienisolation": "alienisolation", "undawn": "undawn", "7day2die": "7dage2dø", "sunlesssea": "solløsse", "sopravvivenza": "overlevelse", "propnight": "propnat", "deadisland2": "dødisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikkemenvampyr", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "katastrofemørkedage", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "livefter", "ageofdarkness": "mørketsalders", "clocktower3": "klokketår3", "aloneinthedark": "aleneimørket", "medievaldynasty": "middelalderdynasti", "projectnimbusgame": "projektnimbusspil", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "outlastforsøgene", "bunker": "bunker", "worlddomination": "verdensherredømme", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dvæ<PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kforelskelse", "wh40": "wh40", "warhammer40klove": "warhammer40kelsker", "warhammer40klore": "warhammer40kmyte", "warhammer": "krig<PERSON>", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "jegelskersororitas", "ilovevindicare": "jegelskervindicare", "iloveassasinorum": "j<PERSON>lskerassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeralderfagsigmar", "civilizationv": "civilisationv", "ittakestwo": "<PERSON>tk<PERSON><PERSON><PERSON><PERSON>", "wingspan": "vingefang", "terraformingmars": "terra<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "helteafstyrkeogmagi", "btd6": "btd6", "supremecommander": "supremekommandør", "ageofmythology": "mytolog<PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "udpost2", "banished": "forvist", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "be<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "e<PERSON><PERSON><PERSON><PERSON>", "strategygames": "strategispil", "anno2070": "anno2070", "civilizationgame": "civilisationsspil", "civilization4": "civilisation4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totalkrig", "travian": "travian", "forts": "forts", "goodcompany": "godtselskab", "civ": "civ", "homeworld": "hjemverden", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "hurtigereendlyset", "forthekings": "tilkongerne", "realtimestrategy": "realtidsstrategi", "starctaft": "stjerneaftale", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kongemadsråder", "eu4": "eu4", "vainglory": "ærgerrighed", "ww40k": "ww40k", "godhood": "guddom<PERSON><PERSON>ed", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesmorsommatematikklasse", "plagueinc": "pestinc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilisation3", "4inarow": "4erpårad", "crusaderkings3": "crusaderkings3", "heroes3": "helte3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "disciple2", "plantsvszombies": "planter<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "strategispil", "stratejioyunları": "strategispil", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "vidundernesalder", "dinosaurking": "dinosaurkongen", "worldconquest": "verdensherredømme", "heartsofiron4": "hjerterafjern4", "companyofheroes": "firmaafhelte", "battleforwesnoth": "kamp<PERSON>for<PERSON>not<PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammer<PERSON><PERSON><PERSON>", "goosegooseduck": "gåsgooseandand", "phobies": "fobier", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "ydreplan", "turnbased": "turbaseret", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilisation5", "victoria2": "victoria2", "crusaderkings": "krusserkonger", "cultris2": "cultris2", "spellcraft": "magiogkreativitet", "starwarsempireatwar": "stjernekrigeimperietkriger", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulmail", "shiningforce": "skinnendekraft", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transporttycoon", "unrailed": "udafsporet", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "flyvudafbesvær", "uplandkingdoms": "uplandkongerigheder", "galaxylife": "galaxyliv", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "kampkat<PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "behovforspeed", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "tabsvs4", "fnaf": "fnaf", "outlast": "overleve", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicegalimatiaskommertilbage", "darkhorseanthology": "mørkehestantologi", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "femterdagehosfreddy", "saiko": "saiiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "småmareridt", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "hjemmebundet", "deadisland": "dødisland", "litlemissfortune": "lillemissuheld", "projectzero": "projektzero", "horory": "skræmmende", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hejnabo2", "gamingdbd": "gamingdbd", "thecatlady": "kat<PERSON>a", "jeuxhorreur": "skræmmendeleg", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardsmodmenneskeheden", "cribbage": "kribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodenavne", "dixit": "dixit", "bicyclecards": "cykelkort", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitaire", "poker": "poker", "hearthstone": "kortspil", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "nøglefyr", "cardtricks": "korttricks", "playingcards": "spillekort", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "samlekort", "pokemoncards": "pokemonkort", "fleshandbloodtcg": "kødogblodtcg", "sportscards": "sportskort", "cardfightvanguard": "kortkampvanguard", "duellinks": "duellinks", "spades": "spar", "warcry": "krigssk<PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kærlighedenskonge", "truco": "trucoklar", "loteria": "lotteri", "hanafuda": "hana<PERSON>da", "theresistance": "modstanden", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkort", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspil", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON>viddrage", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kortspil", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkommandør", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kortspil", "mtgjudge": "mtgdommer", "juegosdecartas": "kortspil", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kortspil", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "kortspil", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "bingo", "facecard": "ansigtkort", "cardfight": "kortkamp", "biriba": "biriba", "deckbuilders": "kortbyggerne", "marvelchampions": "marvelchampions", "magiccartas": "magiskekort", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "skyggedimension", "skipbo": "skip<PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "cyberse", "classicarcadegames": "klassiskarkadespil", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansespil", "fridaynightfunkin": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "k<PERSON><PERSON>t", "justdance": "baredans", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockdead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "rytmegamer", "stepmania": "stepmania", "highscorerythmgames": "højeachivmentrytmespil", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "ungeki", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "en<PERSON><PERSON>ndedanspåil<PERSON><PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ryt<PERSON>pi<PERSON>", "cryptofthenecrodancer": "kryptofthenecrodancer", "rhythmdoctor": "rytmelægen", "cubing": "kubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puslespil", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logikpuzzles", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "hjernegymnastik", "rubikscube": "rubik<PERSON><PERSON>", "crossword": "<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "ordleg", "nonogram": "nonogram", "bookworm": "bogorm", "jigsawpuzzles": "puslespil", "indovinello": "gætteleg", "riddle": "g<PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON>", "rompecabezas": "puslespil", "tekateki": "tekateki", "inside": "inde", "angrybirds": "vredefugle", "escapesimulator": "flugtsimulator", "minesweeper": "minery<PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "haveudsigtspil", "puzzlesport": "puslespilside", "escaperoomgames": "escaperoomspil", "escapegame": "escapespil", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgame", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistisk", "kulaworld": "kulaverden", "myst": "mystdk", "riddletales": "gådehistorier", "fishdom": "fishdom", "theimpossiblequiz": "denumuligequiz", "candycrush": "candycrush", "littlebigplanet": "lillebigplanet", "match3puzzle": "match3puslespil", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "skør", "rubikcube": "rubik<PERSON><PERSON>", "cuborubik": "kubekube", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "hjemmescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "gådefuldmig", "tycoongames": "tycoongames", "cubosderubik": "rubikskuber", "cruciverba": "<PERSON><PERSON><PERSON><PERSON>", "ciphers": "koder", "rätselwörter": "gådenord", "buscaminas": "minestryger", "puzzlesolving": "puslespilsløsning", "turnipboy": "rapsdreng", "adivinanzashot": "bøvsedansk", "nobodies": "ingen", "guessing": "gætte", "nonograms": "nonogrammer", "kostkirubika": "kostkirubika", "crypticcrosswords": "kryptiskekrydsord", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "kattetyv", "quebracabeça": "hjernegymnastik", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "densidstelejrbål", "autodefinidos": "selvdefinerede", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "untitledgåsebrgame", "cassetête": "båndbande", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrint", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "<PERSON><PERSON><PERSON><PERSON>", "pieces": "stykker", "portalgame": "portalspil", "bilmece": "bilmece", "puzzelen": "puzzlen", "picross": "picross", "rubixcube": "rubixkubus", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubemagi", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "vridtetunderland", "monopoly": "monopol", "futurefight": "fremtidskamp", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "enligulv", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON>ner", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookiekongeriget", "alchemystars": "alchemystars", "stateofsurvival": "overlevelsesstatus", "mycity": "minby", "arknights": "arknights", "colorfulstage": "farverigscene", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "skæbnesuperbestilling", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemhelte", "honkaiimpact": "honkaiimpact", "soccerbattle": "fodboldkamp", "a3": "a3", "phonegames": "telefonspil", "kingschoice": "<PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktiskcool", "cookierun": "<PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "udeafsløret", "craftsman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supersus": "supersus", "slowdrive": "langsomkø<PERSON>l", "headsup": "<PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "<PERSON><PERSON><PERSON>", "freefire": "freefire", "mobilegaming": "mobilgaming", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "dyrekrydsning", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystiskbeskedgiver", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8boldpool", "emergencyhq": "nødcentral", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "høstdag", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "rys<PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "stjernestaldonline", "dragonraja": "drageraja", "timeprincess": "tidprinsesse", "beatstar": "beatstar", "dragonmanialegend": "dragemandlegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON>", "androidgames": "androidspil", "criminalcase": "kriminalsag", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leagueofangels", "lordsmobile": "herremobil", "tinybirdgarden": "tinybirdgarden", "gachalife": "gachaliv", "neuralcloud": "neuralcloud", "mysingingmonsters": "minesyngendemonstre", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "blåarkiv", "raidshadowlegends": "raidshadowlegends", "warrobots": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "spejlværk", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "indgang", "slugitout": "slug<PERSON><PERSON>", "mpl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coinmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "straffendegråravn", "petpals": "<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "løbecityspil", "juegodemovil": "mobilspil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rutsjebaneimperiet", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "bilxdriftræsning2", "pathtonowhere": "vejtil<PERSON><PERSON>ed", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "nedrivningsderby3", "wordswithfriends2": "ordmedvenner2", "soulknight": "sjælekæmper", "purrfecttale": "purrfectfortælling", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "h<PERSON>stbyen", "perfectworldmobile": "perfektverdenmobil", "empiresandpuzzles": "imperierogpuslespil", "empirespuzzles": "empirepuzzles", "dragoncity": "dragby", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "littlenightmare", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobil", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiekastaway", "eveechoes": "<PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaklub", "v4": "v4", "cookingmama": "madmor", "cabalmobile": "cabalmobil", "streetfighterduel": "gademesterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "komoverdet", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "månesnakhistories", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilsjov", "legendofneverland": "legendofneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "tidsslidere", "gamingmobile": "gamingmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "battlecats", "dnd": "dnd", "quest": "quest", "giochidiruolo": "rollespil", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "verdenafmørke", "travellerttrpg": "travellerttrpg", "2300ad": "2300<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "larp": "larp", "romanceclub": "romanceklub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokémonmysteriumhulen", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON><PERSON>", "chatot": "snakot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "asketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furriet", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON>hie", "teamystic": "teammystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbren", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmester", "pokémonsleep": "pokémonsøvn", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON>", "ajedrez": "skak", "catur": "kattekat", "xadrez": "skak", "scacchi": "skak", "schaken": "skak랖", "skak": "skak", "ajedres": "skak", "chessgirls": "s<PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "verdenslyn", "jeudéchecs": "jeudéchecs", "japanesechess": "japanskeschak", "chinesechess": "kinesiskeskak", "chesscanada": "skakdanmark", "fide": "fide", "xadrezverbal": "skakprat", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "ryge", "chesscom": "skakcom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeon<PERSON>g<PERSON>ger", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "dungeon<PERSON>g<PERSON>ger", "oxventure": "oxeventyr", "darksun": "darksun", "thelegendofvoxmachina": "legendenivoxmachina", "doungenoanddragons": "dounogenoanddragons", "darkmoor": "mørkebakke", "minecraftchampionship": "minecraftmesterskabet", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dr<PERSON><PERSON><PERSON><PERSON>", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodder", "mcc": "mcc", "candleflame": "st<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "mincraftlomme", "minecraft360": "minecraft360", "moddedminecraft": "moddetminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "mellemverdener", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftebyen", "pcgamer": "pcspiller", "jeuxvideo": "spilvideospil", "gambit": "gambit", "gamers": "gamere", "levelup": "niveauop", "gamermobile": "gamermobil", "gameover": "gameover", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "spilleøje", "pcgames": "pcspil", "casualgaming": "afslappetgaming", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmasterrace", "pcgame": "pcspil", "gamerboy": "gamerdreng", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "episkgamere", "onlinegaming": "onlinegaming", "semigamer": "semigamer", "gamergirls": "gamerpiger", "gamermoms": "gamermoms", "gamerguy": "gamerdreng", "gamewatcher": "spilbeobobserver", "gameur": "gamingbitch", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerpiger", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "holdetdergiveralt", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "opgaver", "alax": "alax", "avgn": "avgn", "oldgamer": "gamermor", "cozygaming": "hyggegaming", "gamelpay": "gammelpay", "juegosdepc": "pcspil", "dsswitch": "dsswitch", "competitivegaming": "konkurrencegaming", "minecraftnewjersey": "minecraftnewjersey", "faker": "fakes", "pc4gamers": "pc4gamers", "gamingff": "gamerliv", "yatoro": "yatoro", "heterosexualgaming": "heteroseksuelgaming", "gamepc": "gamingpc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "dagligquest", "gamegirl": "spilpige", "chicasgamer": "gamergirl", "gamesetup": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "overpowered", "socialgamer": "socialgamer", "gamejam": "spi<PERSON><PERSON><PERSON>", "proplayer": "prospiller", "roleplayer": "rollespiller", "myteam": "mitteam", "republicofgamers": "republikaforgamere", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "triplelegend", "gamerbuddies": "gamerkompiser", "butuhcewekgamers": "brugforkvindegamers", "christiangamer": "kristengamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "casualspiller", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "s<PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "lanfest", "videogamer": "spilnerd", "wspólnegranie": "fællesleg", "mortdog": "mortdog", "playstationgamer": "playstationspiller", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "sundgamers", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "åbenlystærgamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "indsamler", "humanfallflat": "menneskefaldfladt", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nulflugt", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonisk", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "skift", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "himmelsbørneneaflyset", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tårerneafkongeriget", "walkingsimulators": "gangsimulatorer", "nintendogames": "nintendospil", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "<PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "vildesus", "myfriendpedro": "minvenpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "æselkong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spil", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "snak", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "dyrekrydsning", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "triangstrategi", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kastanjeondag", "nintendos": "nintendos", "new3ds": "ny3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyr<PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioogsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "vildr<PERSON><PERSON>", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendsvild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsdanmark", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "forbundetmedlegenderne", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexporte", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrospil", "scaryvideogames": "skræmmendevideospil", "videogamemaker": "videogamemager", "megamanzero": "megamanzero", "videogame": "videospil", "videosgame": "videospil", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblockteater", "arcades": "a<PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "landbrugssimulator", "robloxchile": "robloxdk", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdanmark", "robloxdeutsch": "robloxdansk", "erlc": "erlc", "sanboxgames": "sandboxspil", "videogamelore": "spi<PERSON>g<PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasi<PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "d<PERSON>ø<PERSON>land", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "gta", "deadspace": "dødrum", "amordoce": "amordoce", "videogiochi": "videospil", "theoldrepublic": "detgamleimperium", "videospiele": "videospil", "touhouproject": "touhouprojekt", "dreamcast": "dr<PERSON><PERSON><PERSON><PERSON>", "adventuregames": "eventyrspil", "wolfenstein": "wolfenstein", "actionadventure": "actioneventyr", "storyofseasons": "sæsonhistorier", "retrogames": "retrogames", "retroarcade": "retroarkade", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "legedate", "commanderkeen": "kommandokeen", "bugsnax": "bugsnap", "injustice2": "uretfærdighed2", "shadowthehedgehog": "<PERSON>ggeh<PERSON>n", "rayman": "rayman", "skygame": "skygame", "zenlife": "zenliv", "beatmaniaiidx": "beatmaniaiidx", "steep": "bratt", "mystgames": "mystspil", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "konsolgaming", "konsolen": "konsollen", "outrun": "overhale", "bloomingpanic": "blomstrendepanik", "tobyfox": "tobyfox", "hoyoverse": "hojoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaminggysser", "monstergirlquest": "monstergirlquest", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightdal", "farmingsims": "landmandssi<PERSON><PERSON><PERSON>", "juegosviejos": "gamlerøvr", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktivfiktion", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "elskermindet", "visualnovel": "visualnovel", "visualnovels": "visuel<PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "<PERSON>ggehund", "tcrghost": "tcrghost", "payday": "lønningsdag", "chatherine": "katherine", "twilightprincess": "twilightprinsesse", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandkasse", "aestheticgames": "æstetiskespil", "novelavisual": "novel<PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "suk", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "bladblæserrevolution", "wiiu": "wiiu", "leveldesign": "niveaudesign", "starrail": "starrail", "keyblade": "nøgleklinge", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsomegange", "novelasvisuales": "visualiserier", "robloxbrasil": "robloxdanmark", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospil", "videogamedates": "<PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "barefordi3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "dagforb<PERSON><PERSON>", "maniacmansion": "maniacmansion", "crashracing": "crashracing", "3dplatformers": "3dplatformere", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "hellblade", "storygames": "historielege", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "lydskubber", "beyondtwosouls": "beyondtwosouls", "gameuse": "gamebrug", "offmortisghost": "offmortisghost", "tinybunny": "lille<PERSON>in", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafiskeeventyr", "quickflash": "hurtigblink", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkader", "f123": "f123", "wasteland": "ørk<PERSON>liv", "powerwashsim": "powerwashsim", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxf<PERSON>t", "anotherworld": "<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animekrigere2", "footballfusion": "fodboldfusion", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomagnus", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "twistedmetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "skamstak", "simulator": "simulator", "symulatory": "symulatorisk", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "samogmax", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "kærestehulen", "toontownrewritten": "toontowngenoplivet", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "by<PERSON><PERSON>", "heavenlybodies": "himmelige_kroppe", "seum": "seum", "partyvideogames": "festvideospil", "graveyardkeeper": "graveyardkeeper", "spaceflightsimulator": "rumflyvesimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackoghug", "foodandvideogames": "madogvideospil", "oyunvideoları": "spi<PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "truckingsimulator", "horizonworlds": "horizonverdener", "handygame": "handygame", "leyendasyvideojuegos": "legendernesvideospil", "oldschoolvideogames": "gamerehus", "racingsimulator": "racingsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agenterskæbne", "songpop": "sangpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "oprørsstjerne", "indievideogaming": "indievideospil", "indiegaming": "indiegaming", "indievideogames": "indievideospil", "indievideogame": "indiegame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffortress", "unbeatable": "uover<PERSON><PERSON>ig", "projectl": "projektl", "futureclubgames": "fremtidsklubspil", "mugman": "k<PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "supergigantspil", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestespil", "aperturescience": "åbningsvidenskab", "backlog": "baglog", "gamebacklog": "spilbacklog", "gamingbacklog": "spilbearbejdning", "personnagejeuxvidéos": "personagevideos<PERSON><PERSON>", "achievementhunter": "målopnårer", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "superaberbold", "deponia": "deponia", "naughtydog": "lille<PERSON><PERSON><PERSON>d", "beastlord": "bæstherre", "juegosretro": "retrospil", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriogblindeskoven", "alanwake": "alanwake", "stanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "videospilost", "dragonsync": "dragensynk", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "vildt", "baki": "baki", "sailormoon": "<PERSON><PERSON><PERSON>måne", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "tristeanime", "darkerthanblack": "mørk<PERSON><PERSON>tsvart", "animescaling": "animeopgradering", "animewithplot": "animemedplot", "pesci": "peix", "retroanime": "retroanime", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "mørkef<PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "ladmankensup", "animecover": "animecover", "thevisionofescaflowne": "visionenforescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90erne", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananafisk", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "firekraft", "moriartythepatriot": "moriartyentatrioten", "futurediary": "fremtidsdagbog", "fairytail": "fejltalg", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "<PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragepige", "blacklagoon": "sortlagoen", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "enbestemtmagiskindeks", "sao": "sao", "blackclover": "sortkløver", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouprojekt", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioritet", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismisk", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "engelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "<PERSON>ren<PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "hvordanmanholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "fuldmånewoosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "sødeogkræmmende", "martialpeak": "kampens_top", "bakihanma": "b<PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "highscorepige", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "nulto", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstervgirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "bff", "sailorsaturn": "sejlerepåsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "gam<PERSON><PERSON><PERSON>", "chainsawman": "motorsavmand", "bungoustraydogs": "bungoustraydogs", "jogo": "spil", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "sortbutler", "ergoproxy": "ergoproxy", "claymore": "klapvåben", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "frugtkurv", "devilmancrybaby": "djævlemændgræderbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangaliv", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "elsklivet", "sakuracardcaptor": "sakurakortfanger", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "ovarinoseraph", "thepromisedneverland": "detlovedealdeland", "monstermanga": "monstermanga", "yourlieinapril": "dinløgniapril", "buggytheclown": "buggyklovnen", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "dybvandspfanget", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bannafisk", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "h<PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON>", "cardcaptorsakura": "kortfan<PERSON>ak<PERSON>", "stolas": "stolas", "devilsline": "djævelslinje", "toyoureternity": "tildineternitet", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blåperiode", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "hemmeligalliance", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "slettet", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detektiveconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampyrridder", "mugi": "mugi", "blueexorcist": "blåeksorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spionfamilie", "airgear": "luftruller", "magicalgirl": "magisk<PERSON>ge", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "fængselskole", "thegodofhighschool": "gudenforhøjskolen", "kissxsis": "<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "minudklædningsven", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozenmaden", "animeuniverse": "animeunivers", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "udødeligtuvenlig", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeaargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "<PERSON><PERSON><PERSON>r<PERSON><PERSON>ilsv<PERSON><PERSON><PERSON>", "bloodlad": "blodfyr", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "firepunch", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stjernernehængerudenfor", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "husk<PERSON><PERSON><PERSON>", "recordragnarok": "optagragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tenniskongen", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "morderklasse", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "dødspar<PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskanime", "animespace": "animespace", "girlsundpanzer": "pigerunderpanser", "akb0048": "akb0048", "hopeanuoli": "håbonded", "animedub": "animeredub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratmand", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "ferskenpige", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mekaniskpige", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklub", "dragonquestdai": "dragequestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "lækkertidunderen", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "sjovamus<PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialerervanskelige", "overgeared": "overgeared", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravechef", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "heksedragtatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kammerat", "mangaislife": "manga<PERSON>liv", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "omvendtharem", "saintsaeya": "sanktseiya", "greatteacheronizuka": "fantastisklæreronizuka", "gridman": "gitterman<PERSON>", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "blodpluss", "bloodplusanime": "blodplusanime", "bloodcanime": "blodkanime", "bloodc": "blodc", "talesofdemonsandgods": "historieromdemonerogguder", "goreanime": "goreanime", "animegirls": "anime<PERSON>ger", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kragerxsvin", "splatteranime": "splatteranime", "splatter": "splatter", "risingoftheshieldhero": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimetagtagen", "animeyuri": "animeyuri", "animeespaña": "animespanien", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "barnafraaskildene", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "anime<PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magis<PERSON><PERSON><PERSON>", "callofthenight": "nattenskalden", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON>ggehave", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "prinsessegeléfishe", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverden", "persocoms": "persocoms", "omniscientreadersview": "omniscientreadersview", "animecat": "animekat", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON>", "openinganime": "åbningsanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minteenromantiskekomedie", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "gigantiskerobots", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilsuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "b<PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "frost", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militæ<PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeby", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "brolys", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON>j<PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "angrebpåtitans", "erenyeager": "erenyeager", "myheroacademia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "undersøgelseskorps", "onepieceanime": "onepieceanime", "attaquedestitans": "angrebepaiserne", "theonepieceisreal": "denetestedereal", "revengers": "hæv<PERSON>e", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "gladpudeeffekt", "digimonstory": "digimonhistorie", "digimontamers": "digimonvæmmere", "superjail": "superfængsel", "metalocalypse": "metalokalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "fejlfritwebtoon", "kemonofriends": "kemonovenner", "utanoprincesama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "flyvendeheks", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "livetsrevolutioner"}