{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrológia", "cognitivefunctions": "kognitívnefunkcie", "psychology": "psychológia", "philosophy": "filozofia", "history": "história", "physics": "fyzika", "science": "veda", "culture": "kult<PERSON>ra", "languages": "jazyky", "technology": "technológie", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologické<PERSON><PERSON>", "enneagrammemes": "enneagramovémemes", "showerthoughts": "spŕškamyšlienok", "funny": "zábavné", "videos": "videá", "gadgets": "gadgety", "politics": "politika", "relationshipadvice": "vzťahovéporadenstvo", "lifeadvice": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "krypto", "news": "<PERSON>vinky", "worldnews": "novinkyzosveta", "archaeology": "archeológia", "learning": "učenie", "debates": "roz<PERSON>ory", "conspiracytheories": "konšpiračnéteórie", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "meditácia", "mythology": "mytológia", "art": "umenie", "crafts": "vyrábanie", "dance": "tanec", "design": "<PERSON><PERSON><PERSON>", "makeup": "makeup", "beauty": "krása", "fashion": "m<PERSON><PERSON>", "singing": "spievanie", "writing": "písanie", "photography": "fotografovanie", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "kreslenie", "books": "knihy", "movies": "filmy", "poetry": "poézia", "television": "televízia", "filmmaking": "filmovanie", "animation": "anim<PERSON><PERSON>", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumentárnefilmy", "mystery": "tajomno", "comedy": "komédie", "crime": "krimi", "drama": "d<PERSON><PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "kdráma", "horror": "horor", "romance": "romantika", "realitytv": "realitytv", "action": "akč<PERSON><PERSON>", "music": "hudba", "blues": "blues", "classical": "vážnahudba", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronick<PERSON>", "folk": "folklórna", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latinskoamerická", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "cestovanie", "concerts": "koncerty", "festivals": "festivaly", "museums": "múzeá", "standup": "standup", "theater": "diva<PERSON><PERSON>", "outdoors": "outdoor", "gardening": "záhradníctvo", "partying": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pok<PERSON><PERSON>", "food": "jedlo", "baking": "pečenie", "cooking": "varenie", "vegetarian": "vegetariánstvo", "vegan": "vegánstvo", "birds": "vtáci", "cats": "<PERSON><PERSON><PERSON>", "dogs": "psy", "fish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animals": "zvieratá", "blacklivesmatter": "blacklivesmatter", "environmentalism": "environmentalizmus", "feminism": "feminizmus", "humanrights": "ľudsképráva", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "dobrovoľníctvo", "sports": "športy", "badminton": "<PERSON><PERSON><PERSON>", "baseball": "bejzbal", "basketball": "basketbal", "boxing": "box", "cricket": "kriket", "cycling": "cyklist<PERSON>", "fitness": "fitnes", "football": "futbal", "golf": "golf", "gym": "posilňovňa", "gymnastics": "gymnast<PERSON>", "hockey": "hokej", "martialarts": "bojovéumenia", "netball": "netbal", "pilates": "pilates", "pingpong": "pingpong", "running": "beh", "skateboarding": "skateboarding", "skiing": "lyžovanie", "snowboarding": "snowboardovanie", "surfing": "surfovanie", "swimming": "plávanie", "tennis": "tenis", "volleyball": "volejbal", "weightlifting": "vzpieranie", "yoga": "joga", "scubadiving": "potápanie", "hiking": "turistika", "capricorn": "kozorožec", "aquarius": "vodnár", "pisces": "ryby", "aries": "baran", "taurus": "b<PERSON>k", "gemini": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancer": "rak", "leo": "lev", "virgo": "panna", "libra": "v<PERSON><PERSON>", "scorpio": "škorpión", "sagittarius": "strelec", "shortterm": "kratkodobyvztah", "casual": "ne<PERSON><PERSON>e", "longtermrelationship": "dlhodobyvztah", "single": "slobodný", "polyamory": "polyamoria", "enm": "etick<PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON>", "bisexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "pansexu<PERSON><PERSON>", "asexual": "asexuálny", "reddeadredemption2": "červenémysečredempcie2", "dragonage": "drakoviek", "assassinscreed": "assassinscreed", "saintsrow": "svätírad", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kráľovskávýprava", "soulreaver": "<PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverzia", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "zápádsluncevoľne", "arkham": "arkham", "deusex": "boh<PERSON><PERSON>", "fireemblemfates": "ohniváikonaosudu", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildovévojny", "openworld": "<PERSON>t<PERSON><PERSON>ý<PERSON>vet", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "dušazdruženie", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "kmenemidgardu", "planescape": "plánovéštevanie", "lordsoftherealm2": "lordoviarealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "farebnarovnica", "medabots": "medaboti", "lodsoftherealm2": "lodzavlady2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "ponorenesimgames", "okage": "okage", "juegoderol": "hrajrolňe", "witcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dishonored": "hanba", "eldenring": "elden<PERSON>", "darksouls": "temn<PERSON><PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "zaklínač3", "fallout": "<PERSON><PERSON>d", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modovanie", "charactercreation": "tvorbapostáv", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasystaroskolský", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidnamotivacia", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "srdciarprelásku", "otomegames": "<PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampírovaskrývačka", "dimension20": "dimension20", "gaslands": "plyn<PERSON><PERSON><PERSON>", "pathfinder": "hľada<PERSON>_ciest", "pathfinder2ndedition": "průvodce2edícia", "shadowrun": "tienovýbeh", "bloodontheclocktower": "krvnapasovomhodinach", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitacnystar", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "jednookamžité", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "nadvládca", "yourturntodie": "tvojťahzomrieť", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "staríbolionline", "reka": "reka", "honkai": "honkai", "marauders": "marauderi", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "dé<PERSON><PERSON>diel", "mu": "mu", "falloutshelter": "falloutshelter", "gurps": "gurps", "darkestdungeon": "najtemnejšiedungeon", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastičníbojovníci", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "nočnédedina", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "šialenstvoboj", "jaggedalliance2": "jaggedalliance2", "neverwinter": "nikdyzmrazu", "road96": "cesta96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamskíhrdinovia", "forgottenrealms": "zabudnutékráľovstvá", "dragonlance": "dračiačeň", "arenaofvalor": "arénavalue", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "theddivision2", "lineage2": "lineage2", "digimonworld": "digimonsvet", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "roz<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonzakázanýzápad", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltazelená", "diablo": "<PERSON><PERSON><PERSON><PERSON>", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "zabíjať", "lastepoch": "poslednáepocha", "starfinder": "hľadačhviezd", "goldensun": "zlatéslnko", "divinityoriginalsin": "božskovýoriginálnyhriech", "bladesinthedark": "nože_v_tme", "twilight2000": "úsvit2000", "sandevistan": "sandevistan", "cyberpunk": "kyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "kyberpunkčervený", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "padlýporiadok", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlodejskavzduchach", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diablovprežívateľ", "oldschoolrunescape": "starš<PERSON>lon<PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božskosť", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "starecasyzhurene", "adventurequest": "dobrodružstvovyzva", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "hrynarolovanie", "roleplayinggames": "hrynarolovania", "finalfantasy9": "finalfantasy9", "sunhaven": "slnečnázáhrada", "talesofsymphonia": "pribehysymfonie", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "zničmestom", "myfarog": "mojafrog", "sacredunderworld": "sacredunderworld", "chainedechoes": "reťazováozvena", "darksoul": "temnásrdce", "soulslikes": "<PERSON><PERSON><PERSON>", "othercide": "inýzločin", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "stĺpyvečnosti", "palladiumrpg": "palladiumrpg", "rifts": "trhliny", "tibia": "tibia", "thedivision": "divízia", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "cestovateľsosmičiek", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "vlkodlakapokalypsy", "aveyond": "aveyond", "littlewood": "ma<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "de<PERSON><PERSON><PERSON>", "engineheart": "motorovésrdce", "fable3": "bajka3", "fablethelostchapter": "fableztratenáskapitola", "hiveswap": "hiveswap", "rollenspiel": "hranarolach", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "hviezdnepole", "oldschoolrevival": "star<PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworldy", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kráľovstvosrdca1", "ff9": "ff9", "kingdomheart2": "kráľovstvodecť2", "darknessdungeon": "temnédunge", "juegosrpg": "hryrpg", "kingdomhearts": "kráľovstvesrdcí", "kingdomheart3": "kráľovstvokrvi3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "divokésrdcia", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "neboarcadie", "shadowhearts": "tieňovésrdcia", "nierreplicant": "nierreplikant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "dychodna4", "mother3": "matka3", "cyberpunk2020": "kyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "hranírolí", "roleplaygame": "hračkárskehraní", "fabulaultima": "fabulault<PERSON>", "witchsheart": "srdcečarodejnice", "harrypottergame": "harry<PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "cestakoučovania2e", "vampirilamasquerade": "vampírskamaskáda", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammr", "dragonageorigins": "dragonagepôvod", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "lovehunting", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "lovecien", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforumin", "shadowheartscovenant": "zmluvastinabrán", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "kráľovstvopríde", "awplanet": "awplanet", "theworldendswithyou": "svetkončízvetou", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "umierajúcesvetlo2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "temnáheréza", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "zázrakprírody", "blackbook": "čiernakni<PERSON>", "skychildrenoflight": "nebesk<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "posvätnázlatáedícia", "castlecrashers": "hradovíutusy", "gothicgame": "gothická<PERSON>", "scarletnexus": "scarlétnexus", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "hrámesrpg", "prophunt": "prophunt", "starrails": "starrailsy", "cityofmist": "<PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "novýapunkclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7navždykríza", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postkyberpunk", "deathroadtocanada": "smrtoncestekkanady", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "ohňováéria", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremácia", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monstervyšplhavé", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "taktickýrpg", "mahoyo": "mahojo", "animegames": "<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "princeznakonnect", "hexenzirkel": "hexenzirkel", "cristales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "vreckovysage", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligas<PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "strednásobek", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "kybersport", "crazyraccoon": "š<PERSON>enýhroch", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "polovickyživot", "left4dead": "left4mŕtvy", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portál", "teamfortress2": "teamfortress2", "everlastingsummer": "navždyletné", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "slobodneplaneta", "transformice": "transformice", "justshapesandbeats": "lentvariaavzory", "battlefield4": "battlefield4", "nightinthewoods": "nocv<PERSON><PERSON>", "halflife2": "polovazivot2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "rizikapadu2", "metroidvanias": "metroidvanias", "overcooked": "prepálené", "interplanetary": "medziplanetárny", "helltaker": "helltaker", "inscryption": "inskripcia", "7d2d": "7d2d", "deadcells": "mŕtvebunky", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "trpasličiahrad", "foxhole": "norovýzbor", "stray": "stratené", "battlefield": "bo<PERSON><PERSON><PERSON>", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboot", "eyeb": "oko", "blackdesert": "čiernypúšť", "tabletopsimulator": "stolnehráčik", "partyhard": "partyujnaplno", "hardspaceshipbreaker": "toughspaceshipbreaker", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "uveznenísšaškom", "dinkum": "dinkum", "predecessor": "predchodca", "rainworld": "džunglovýsvet", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonysim", "noita": "noita", "dawnofwar": "úsvitvojny", "minionmasters": "minionmasters", "grimdawn": "temnézore", "darkanddarker": "temnšieatmenejšie", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON>šovprá<PERSON>", "datingsims": "datingsims", "yaga": "yaga", "cubeescape": "kockovaprika", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nov<PERSON><PERSON><PERSON>", "citiesskylines": "mestskeobzory", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtuálnakenopsia", "snowrunner": "snežnýbežec", "libraryofruina": "knižnicaruiny", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "cestovateľ", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "pokojnáplastovákačka", "battlebit": "bit<PERSON>zen<PERSON>", "ultimatechickenhorse": "ultimá<PERSON><PERSON>kura<PERSON>áľ", "dialtown": "dialtown", "smileforme": "usmievajsapremna", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermasokluk", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "útulnýles", "doom": "koneczrnu", "callofduty": "<PERSON>av<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kód", "borderlands": "pohraničia", "pubg": "pubg", "callofdutyzombies": "volanienakladnskehrdzavce", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "ďalekoúkrižovanie", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "obrana<PERSON>me", "huntshowdown": "huntshowdown", "ghostrecon": "duchovarezist<PERSON>cia", "grandtheftauto5": "grandtheftauto5", "warz": "vojna", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelyzium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codvojna", "callofdutywarzone": "vol<PERSON>ez<PERSON><PERSON><PERSON><PERSON>", "codzombies": "codzombíci", "mirrorsedge": "zrkadlovyhranice", "divisions2": "divisions2", "killzone": "zónazabíjania", "helghan": "hel<PERSON>", "coldwarzombies": "coldwar<PERSON>mbíci", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "asfaltovábomba", "crosscode": "krížovýkód", "goldeneye007": "zlatéoko007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernávojna", "neonabyss": "neonabysse", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "hraničnépokrajiny", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "svetvojnovýchlodí", "back4blood": "back4blood", "warframe": "vojnovýrám", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "systémovetrepanie", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "jaskynapríbeh", "doometernal": "doometernal", "centuryageofashes": "storočiačasupekla", "farcry4": "farcry4", "gearsofwar": "zbranevojny", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "ty<PERSON><PERSON><PERSON><PERSON><PERSON>skytyger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "vstúpdozbrane", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernavojna2", "blackops1": "čierneoperácie1", "sausageman": "klobásovýmacho", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "duchovábolesť", "warface": "v<PERSON>jnovátvár", "crossfire": "<PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomickésrdce", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "oslobodenie", "battlegrounds": "bojištia", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON><PERSON>", "gamepubg": "hrajpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearotcovslobody", "juegosfps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertstrike": "konvertujúciúder", "warzone2": "warzone2", "shatterline": "rozbiťčiaru", "blackopszombies": "čierneoperáciezombíky", "bloodymess": "krvavýchaos", "republiccommando": "republikovýkomando", "elitedangerous": "elitnýchnebezpečných", "soldat": "v<PERSON>k", "groundbranch": "groundbranch", "squad": "tím", "destiny1": "osud1", "gamingfps": "hrani<PERSON><PERSON>", "redfall": "červenýp<PERSON>d", "pubggirl": "pubggirl", "worldoftanksblitz": "svetakompankovblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "ob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaszázračnékráľovstvá", "halo2": "halo2", "payday2": "výplata2", "cs16": "cs16", "pubgindonesia": "pubgindonésia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgslovensko", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "mydlocod", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "neuveritelnýturnaj", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "zemetraseniechampioni", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "rozštiepenábunka", "neonwhite": "neonwhite", "remnant": "pozostatok", "azurelane": "azurelane", "worldofwar": "svetvojny", "gunvolt": "gunvolt", "returnal": "retoural", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "tieňovýmuž", "quake2": "zemetrasenie2", "microvolts": "mikrov<PERSON>y", "reddead": "červenýmŕtvy", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "stratenáarka", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "morskézlodejníctvo", "rust": "rusti", "conqueronline": "doznesonline", "dauntless": "neohrozený", "warships": "vojensképlavidlá", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "letovvyserákaní", "recroom": "recroom", "legendsofruneterra": "legendydobežnehoúzemia", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantázystaronline2", "maidenless": "bezprinceznej", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON>", "crossout": "preškrtnúť", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "nethráboli", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "červenýzomrelonline", "superanimalroyale": "superzvieraroya", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "rytierenline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "bindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubovápingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "smet<PERSON><PERSON>", "newworld": "novy<PERSON><PERSON>", "blackdesertonline": "č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirát101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "starwarsbitkaفيfront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponyville", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "svetvojnovvojny", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopety", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "popolyt<PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON>", "silkroad": "hodvábnacesta", "spiralknights": "spiralknighti", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "pomsta", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "drakova_prorokyňa", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "anjelionline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsstararestauracia", "grandfantasia": "veľkáfantázia", "blueprotocol": "modrýprot<PERSON>l", "perfectworld": "dokonalysvet", "riseonline": "vystuponline", "corepunk": "jadrovýpunk", "adventurequestworlds": "dobrodruzstvovysvetoch", "flyforfun": "letzabavný", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "kráľovstvoodporu", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "ulicnybojovnik", "hollowknight": "prázdnyrytier", "metalgearsolid": "metalgearsolid", "forhonor": "začestnosť", "tekken": "tekken", "guiltygear": "vinnábattle", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuálnyzabijak", "streetsofrage": "uliceodþákania", "mkdeadlyalliance": "mknebezpečnéaliancie", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kráľbitiek", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightinghry", "blasphemous": "bleskovo", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "hryzá<PERSON><PERSON>", "cyberbots": "kyberboty", "armoredwarriors": "ozbrojeníbojovníci", "finalfight": "poslednyboj", "poweredgear": "silovévybavenie", "beatemup": "rozbicie", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "zabijackainštinkt", "kingoffigthers": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "duchbežec", "chivalry2": "rytierstvo2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "pokračovaniehollowknighta", "hollowknightsilksong": "prázdnyritierhlásenie", "silksonghornet": "silksonghornet", "silksonggame": "silksonghra", "silksongnews": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silksong": "silksong", "undernight": "podnesom", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolučný<PERSON>aj", "evomoment": "evomoment", "lollipopchainsaw": "lollipopřetězová_pila", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "príbehyberserie", "bloodborne": "bloodborne", "horizon": "horizont", "pathofexile": "cestavadexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "krvavýpríchod", "uncharted": "ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizontnápadnýúsvit", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "poslednízná<PERSON>", "infamous": "<PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationka<PERSON><PERSON><PERSON>", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "kr<PERSON><PERSON>č<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "pek<PERSON>jeuvoľnené", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "bohvojny", "gris": "gris", "trove": "pokladnica", "detroitbecomehuman": "detroitostančlovekom", "beatsaber": "beats<PERSON>r", "rimworld": "rimsvet", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turistickytrof", "lspdfr": "lspdfr", "shadowofthecolossus": "tieňobrochov", "crashteamracing": "crashteamracing", "fivepd": "fivepd", "tekken7": "tekken7", "devilmaycry": "<PERSON>ablov<PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "diablovpláč3", "devilmaycry5": "diablomôžekričať5", "ufc4": "ufc4", "playingstation": "hracistanica", "samuraiwarriors": "samurajskíbojovníci", "psvr2": "psvr2", "thelastguardian": "poslednstrá<PERSON><PERSON>", "soulblade": "<PERSON>š<PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "manhunt", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "poslednstrá<PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "hernýprístup", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "bitkovanoccham<PERSON>", "psychonauts": "psychonauti", "mhw": "mhw", "princeofpersia": "princzpersie", "theelderscrollsskyrim": "starévředenieskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gxbox": "gxbox", "battlefront": "bojištia", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "hviezdneputovanie", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americkýmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakr<PERSON><PERSON>ov", "fable2": "bajka2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "trashtvsk", "skycotl": "skycotl", "erica": "erica", "ancestory": "predkovia", "cuphead": "cuphead", "littlemisfortune": "malánešťastie", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterskýbal", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stanleyparábola", "towerunite": "vežaunite", "occulto": "okulto", "longdrive": "dlhacesta", "satisfactory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "milovníkdažďa", "underearth": "podzemie", "assettocorsa": "assettocorsa", "geometrydash": "geometriadash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "temnástrecha", "pizzatower": "pizzatorn", "indiegame": "indiehra", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "hra", "rockpaperscissors": "kameňpapiernozíra", "trampoline": "trampolína", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "vyzva", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "záhradnéhry", "pickanumber": "vyberčíslo", "trueorfalse": "pravdaaleboloz", "beerpong": "pivnýpong", "dicegoblin": "kockovýtrpaslík", "cosygames": "útulnéhry", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "zadarmohranie", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sudoku", "juegos": "hry", "mahjong": "mahjong", "jeux": "hrávame", "simulationgames": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "slovnéhry", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "hlavolamy", "letsplayagame": "poďmezahraťhru", "boredgames": "nud<PERSON><PERSON>y", "oyun": "hra", "interactivegames": "interaktívnehranie", "amtgard": "amtgard", "staringcontests": "súťaževpozeraní", "spiele": "spielet<PERSON>", "giochi": "hranie", "geoguessr": "geoguessr", "iphonegames": "iphonehry", "boogames": "boohry", "cranegame": "žeriavohra", "hideandseek": "skrytasažbu", "hopscotch": "sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klasick<PERSON>ra", "mindgames": "hrynauvažovanie", "guessthelyric": "uhádnitext", "galagames": "galahry", "romancegame": "<PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "jazykolamy", "4xgames": "4xhier", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "s<PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "hry90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "prete<PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "realavsfejk", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "hrajonline", "onlinegames": "onlinehry", "jogosonline": "hryonline", "writtenroleplay": "p<PERSON><PERSON><PERSON><PERSON>", "playaballgame": "zahrajsihrukolobke", "pictionary": "pictionary", "coopgames": "hraciekooperacie", "jenga": "jenga", "wiigames": "wiihry", "highscore": "vysokéhoskóre", "jeuxderôles": "hryn<PERSON><PERSON>", "burgergames": "hamburgerhry", "kidsgames": "detsk<PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwčiernaedícia", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "hrafotazky", "gioco": "hra", "managementgame": "her<PERSON><PERSON>vy", "hiddenobjectgame": "skryt<PERSON><PERSON><PERSON><PERSON><PERSON>hra", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "f1hra", "citybuilder": "mestotvorca", "drdriving": "drdriving", "juegosarcade": "hernearcade", "memorygames": "pamäťovéhr<PERSON>", "vulkan": "vulkan", "actiongames": "akč<PERSON>é<PERSON><PERSON>", "blowgames": "blowgames", "pinballmachines": "flipperovéautomaty", "oldgames": "<PERSON><PERSON><PERSON>", "couchcoop": "gaučovka", "perguntados": "pýtania", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imess<PERSON><PERSON>y", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "vyplňmediera", "jeuxpc": "p<PERSON>ry", "rétrogaming": "rétrogaming", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "metromax", "jeuxdecelebrite": "hraslavnecelebrity", "exitgames": "vystupovehry", "5vs5": "5na5", "rolgame": "r<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tradičnehry", "kniffel": "kniffel", "gamefps": "herafps", "textbasedgames": "text<PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafové", "fantacalcio": "fantakal<PERSON>", "retrospel": "retrohranie", "thiefgame": "zlodejskahra", "lawngames": "trávnikovéhry", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stolnýfotbal", "tischfußball": "futbalovystol", "spieleabende": "<PERSON>rac<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "hryforum", "casualgames": "štandardnehry", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "únikovéhry", "thiefgameseries": "zlodejnickahra", "cranegames": "žralokhierolníhry", "játék": "hraj<PERSON>", "bordfodbold": "bordaftbal", "jogosorte": "<PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON>", "onlineplay": "onlinehranie", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "náhodníčka", "msx": "msx", "anagrammi": "anagramy", "gamespc": "hrypc", "socialdeductiongames": "hrysozmyslením", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrickéhry", "goodoldgames": "dobréstaréhranie", "truthanddare": "<PERSON>rav<PERSON>av<PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "hľadaniepokladov", "jeuxvirtuel": "virtuálnahra", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "free2play", "fantasygame": "fantazijnahranie", "gryonline": "gryonline", "driftgame": "driftky", "gamesotomes": "hryzotomas", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "vsetkozmatrikom", "everywheregame": "všetkdekame", "swordandsorcery": "mečamágia", "goodgamegiving": "dobráhraodvzdávania", "jugamos": "hr<PERSON><PERSON>", "lab8games": "lab8hry", "labzerogames": "<PERSON><PERSON><PERSON><PERSON>", "grykomputerowe": "<PERSON>rani<PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "hrá<PERSON><PERSON>u", "jeuxderythmes": "hrysozyvom", "minaturegames": "miniatúrnehry", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "gamemodding", "crimegames": "zloč<PERSON>ckehry", "dobbelspellen": "dobbelspellen", "spelletjes": "hry", "spacenerf": "spacenerf", "charades": "<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "kooperacna_hra", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON>nah<PERSON>", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON>", "shogi": "<PERSON><PERSON><PERSON>", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "pandemickýodkaz", "camelup": "<PERSON><PERSON><PERSON>", "monopolygame": "monopolygame", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON>", "boardgame": "sto<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "plánzowe", "risiko": "riziko", "permainanpapan": "hracsklady", "zombicide": "zombicída", "tabletop": "sto<PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "krvavázápas", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "gopartinogame", "connectfour": "prepojeniesetri", "heroquest": "hrdinovskáúloha", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "karambol", "tablegames": "s<PERSON><PERSON><PERSON><PERSON>", "dicegames": "hraškyšťasteniem", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "hrajmesiisvetom", "deskgames": "<PERSON><PERSON><PERSON>", "alpharius": "alfarius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelkrízovýprotokol", "cosmicencounter": "kozmickézá<PERSON>itky", "creationludique": "hravévytváranie", "tabletoproleplay": "stolnehranie", "cardboardgames": "kartónovéhry", "eldritchhorror": "el<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "prenosnýhry", "infinitythegame": "nekonečnáhra", "kingdomdeath": "kráľovstvosmrti", "yahtzee": "yahtzee", "chutesandladders": "chutepadlazdí", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "s<PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "červenýkrk", "boardom": "nudou", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudesociété", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "spoločenskéhry", "twilightimperium": "twilightimperium", "horseopoly": "koňopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "vilyzblaznenia", "gomoku": "gomoku", "giochidatavola": "hracieknastole", "shadowsofbrimstone": "tieňmokašťa", "kingoftokyo": "kráľtokya", "warcaby": "warcaby", "táblajátékok": "táblajátékok", "battleship": "bojnáloď", "tickettoride": "vstupenkanavezenie", "deskovehry": "<PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "s<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "stolnýhry", "xiángqi": "<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "spoločenskéhry", "starwarslegion": "hviezdnevojnylegion", "gochess": "<PERSON>ša<PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "s<PERSON><PERSON><PERSON><PERSON>", "terraria": "<PERSON><PERSON><PERSON><PERSON>", "dsmp": "dsmp", "warzone": "bojov<PERSON>zóna", "arksurvivalevolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayz": "dayz", "identityv": "identitav", "theisle": "ostrov", "thelastofus": "poslednízná<PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "volaniecthulhu", "bendyandtheinkmachine": "bendyainktlač<PERSON>ni", "conanexiles": "conanexiles", "eft": "eft", "amongus": "me<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "ekologické", "monkeyisland": "opičíostrov", "valheim": "valheim", "planetcrafter": "planetcraftér", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "f<PERSON><PERSON>", "witchit": "č<PERSON><PERSON><PERSON>", "pathologic": "patologický", "zomboid": "zomboid", "northgard": "severnáhrada", "7dtd": "7dtd", "thelongdark": "d<PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "uzemnený", "stateofdecay2": "stavrozkladu2", "vrising": "vrising", "madfather": "šialenámatka", "dontstarve": "nezostanúťhl<PERSON>ný", "eternalreturn": "večnýnávrat", "pathoftitans": "cestatitanov", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "zlovnútrou", "realrac": "skutočnáryba", "thebackrooms": "zázemí", "backrooms": "zadnéizby", "empiressmp": "emp<PERSON><PERSON><PERSON>", "blockstory": "blockpríbeh", "thequarry": "lomy", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "chodiacezmrtvolygame", "wehappyfew": "myjsmešťastnímalí", "riseofempires": "vzrastríšiteľstiev", "stateofsurvivalgame": "stavprežitiahry", "vintagestory": "vintagepríbeh", "arksurvival": "arkprežitie", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "prežitievowestlande", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "mrazovápunk", "darkwood": "tmavýdrevný", "survivalhorror": "<PERSON>žitiehr<PERSON><PERSON>", "residentevil": "rezident<PERSON>lo", "residentevil2": "rezidentzla2", "residentevil4": "residentevil4", "residentevil3": "residentvil3", "voidtrain": "prá<PERSON><PERSON><PERSON>vlak", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "prežívaciehry", "sillenthill": "tich<PERSON><PERSON>", "thiswarofmine": "tatovojnavonašom", "scpfoundation": "scpfoundation", "greenproject": "zelenýprojekt", "kuon": "kuon", "cryoffear": "plačzabo<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "loďka", "rdo": "rdo", "greenhell": "zelenépek<PERSON>", "residentevil5": "rezidentzla5", "deadpoly": "mŕtvypoly", "residentevil8": "rezidentzlo8", "onironauta": "onironauta", "granny": "babička", "littlenightmares2": "malénočnémožnosti2", "signalis": "signalis", "amandatheadventurer": "amandanaadventurerka", "sonsoftheforest": "synovialesa", "rustvideogame": "rust<PERSON><PERSON><PERSON>", "outlasttrials": "<PERSON>žiť<PERSON><PERSON><PERSON><PERSON>", "alienisolation": "izoláciaaliena", "undawn": "<PERSON><PERSON><PERSON><PERSON>", "7day2die": "7dni2umrieť", "sunlesssea": "bezslnečne_more", "sopravvivenza": "<PERSON>ž<PERSON><PERSON>", "propnight": "propnight", "deadisland2": "mŕtvydovodostrova2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenupír", "deathverse": "smrtnaverze", "cataclysmdarkdays": "kataklyzmotmavédni", "soma": "soma", "fearandhunger": "strachav<PERSON>ad", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "životpotom", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "vežahodín3", "aloneinthedark": "osamelavnaot<PERSON>e", "medievaldynasty": "stredovekárodina", "projectnimbusgame": "projekt<PERSON><PERSON><PERSON>", "eternights": "večnénoci", "craftopia": "remeslovensko", "theoutlasttrials": "testyposlednéhoprežitia", "bunker": "bunker", "worlddomination": "svetovádobitie", "rocketleague": "raketováliga", "tft": "tft", "officioassassinorum": "oficiálnizabijaci", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dwarf<PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kľúbenec", "wh40": "wh40", "warhammer40klove": "warhammer40klasika", "warhammer40klore": "warhammer40kpríbehy", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "milujemvindicare", "iloveassasinorum": "milujemassasinorum", "templovenenum": "tmplovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "zabijackaprofesie", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "vojnovákoväziežesigmaru", "civilizationv": "civil<PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "trebasa2", "wingspan": "rozpätiesiотип", "terraformingmars": "teraformovaniemarsa", "heroesofmightandmagic": "hrdinoviasilyamagie", "btd6": "btd6", "supremecommander": "supremekomandant", "ageofmythology": "vekmytológie", "args": "argumenty", "rime": "rime", "planetzoo": "planétazoo", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "červenávýstraha", "civilization6": "civilizácia6", "warcraft2": "warcraft2", "commandandconquer": "povelaťaprevládať", "warcraft3": "warcraft3", "eternalwar": "večnávojna", "strategygames": "strategickehry", "anno2070": "anno2070", "civilizationgame": "civilizacnahra", "civilization4": "civilizácia4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "celkovávojna", "travian": "travian", "forts": "<PERSON><PERSON><PERSON><PERSON>", "goodcompany": "dob<PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "domovskýsvet", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "reálnastratégia", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kráľovstvomdvochkorún", "eu4": "eu4", "vainglory": "nádejnehrdinstvo", "ww40k": "ww40k", "godhood": "božstvo", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davovzabav<PERSON><PERSON><PERSON><PERSON><PERSON>", "plagueinc": "plagueinc", "theorycraft": "teóriakraft", "mesbg": "mesbg", "civilization3": "civilizácia3", "4inarow": "4v<PERSON><PERSON>", "crusaderkings3": "králikrižiaci3", "heroes3": "hrdinovia3", "advancewars": "advancewars", "ageofempires2": "vekempírií2", "disciples2": "učeníci2", "plantsvszombies": "rastlinyvzombikoch", "giochidistrategia": "strategickéhr<PERSON>", "stratejioyunları": "strategickéhr<PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosaur<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "svetovénávrhy", "heartsofiron4": "srdcadocela4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "bitkazawesnoth", "aoe3": "aoe3", "forgeofempires": "kovaniek<PERSON>žov", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON>gus<PERSON><PERSON>", "phobies": "fóbije", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "naťahované", "bomberman": "bomberman", "ageofempires4": "vekempíriiv4", "civilization5": "civilizácia5", "victoria2": "victoria2", "crusaderkings": "krí<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "čarovanie", "starwarsempireatwar": "starwarsimperiumvvojne", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stratégia", "popfulmail": "popfulmail", "shiningforce": "žiariváenergia", "masterduel": "majsterzápas", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "dopravný<PERSON><PERSON><PERSON>č", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "plánujúúnikzúfalstva", "uplandkingdoms": "uplandskékráľovstvá", "galaxylife": "galaxylife", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "zničvystúpateľ", "battlecats": "bojovécats", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potrebujemrýchlosť", "needforspeedcarbon": "potrebujemrýchlosťcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "stratiliams4", "fnaf": "fnaf", "outlast": "prežiť", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicaznovašalieť", "darkhorseanthology": "temnýkôňantológia", "phasmophobia": "fázmofóbia", "fivenightsatfreddys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "malénočnémory", "deadrising": "mŕtvavzpoura", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "doma", "deadisland": "mŕtvysvet", "litlemissfortune": "malápaníšťastie", "projectzero": "projektzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "ahojsused", "helloneighbor2": "ahojsused2", "gamingdbd": "gamingdbd", "thecatlady": "mačaciažena", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON>nie", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartyprot<PERSON>", "cribbage": "krabica", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kódovémená", "dixit": "dixit", "bicyclecards": "bicyklové<PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kľúčovýkovan", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "hrac<PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "tradingkarty", "pokemoncards": "poke<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "sportovékar<PERSON>", "cardfightvanguard": "kartybojvanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON><PERSON>", "warcry": "vojnovývýkrik", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "odpor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON>hbitka", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yug<PERSON><PERSON><PERSON>", "darkmagician": "tmavýčarodejník", "blueeyeswhitedragon": "modrozrak<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kart<PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandér", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "hryzkariet", "mtgjudge": "mtgrozhodca", "juegosdecartas": "kartovéhry", "duelyst": "duelist", "mtgplanschase": "mtgplanciasem", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "hrejmekar<PERSON>", "carteado": "kartovanie", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "karcia<PERSON>", "battlespirits": "bitkoveduchy", "battlespiritssaga": "bitkoveduchysaga", "jogodecartas": "jogodekartičiek", "žolíky": "žolíky", "facecard": "<PERSON><PERSON><PERSON>", "cardfight": "kardov<PERSON>rit<PERSON>", "biriba": "biriba", "deckbuilders": "stavebn<PERSON><PERSON>v", "marvelchampions": "marvelšampióni", "magiccartas": "magickarty", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "tieňovádimenza", "skipbo": "skip<PERSON>", "unstableunicorns": "nestabilnekonjíky", "cyberse": "kyberse", "classicarcadegames": "klasick<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "gitarovýhrdina", "clonehero": "klonhrdina", "justdance": "ibatancuj", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockujmrtvych", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tanecentral", "rhythmgamer": "rytmový<PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "highscorerytmhry", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rytmovejneba", "hypmic": "hypmic", "adanceoffireandice": "tancujpoplamea<PERSON>ě", "auditiononline": "audíciavonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptozombidancer", "rhythmdoctor": "rytmodoktor", "cubing": "kockovanie", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "hrypremozgovézá<PERSON>ky", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "k<PERSON><PERSON><PERSON>ov<PERSON>", "motscroisés": "k<PERSON><PERSON><PERSON>ov<PERSON>", "krzyżówki": "k<PERSON><PERSON><PERSON>ov<PERSON>", "nonogram": "nonogram", "bookworm": "kn<PERSON>žnýčerv", "jigsawpuzzles": "sklada<PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "hlavolam", "tekateki": "tekateki", "inside": "vnútri", "angrybirds": "nahnevanévtáky", "escapesimulator": "útiekasosimulator", "minesweeper": "minesweeper", "puzzleanddragons": "puzzleazhlavy", "crosswordpuzzles": "k<PERSON><PERSON><PERSON>ov<PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "puzzlešport", "escaperoomgames": "únikovéhry", "escapegame": "<PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapeshračka", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "záhadnépríbehy", "fishdom": "rybárstvo", "theimpossiblequiz": "impossiblenakviz", "candycrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlebigplanet": "malýveľ<PERSON>vet", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "taletosprinciples", "homescapes": "domovskéscenáre", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "<PERSON><PERSON><PERSON>", "cruciverba": "k<PERSON><PERSON><PERSON>ov<PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON>", "buscaminas": "bucaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "repačlovek", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON>", "nobodies": "niktošovia", "guessing": "<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogramy", "kostkirubika": "kostkirubika", "crypticcrosswords": "kr<PERSON>žovkyzcrypta", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "hľadaniehádankovýchskrytýchpokladov", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "hlavolam", "hlavolamy": "boohl<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "poslednystanok", "autodefinidos": "autodefinovaní", "picopark": "picopark", "wandersong": "pieseňputovania", "carto": "<PERSON><PERSON><PERSON><PERSON>", "untitledgoosegame": "neoznač<PERSON><PERSON><PERSON>a", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubikovaškatuľa", "maze": "b<PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "rý<PERSON><PERSON>kocka", "pieces": "kusy", "portalgame": "<PERSON><PERSON><PERSON>", "bilmece": "h<PERSON>rovesk<PERSON>", "puzzelen": "<PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagic", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "zkreslenýraj", "monopoly": "monopol", "futurefight": "budúcnosťboja", "mobilelegends": "mobilnélegendy", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "osamelývlk", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblehviezdy", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "kralovst<PERSON>_cookie_run", "alchemystars": "alchemystars", "stateofsurvival": "stavprežitia", "mycity": "mojemes<PERSON>", "arknights": "arknighti", "colorfulstage": "farebnystage", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "osudgrandsúťaž", "hyperfront": "hyperfront", "knightrun": "rytieribeh", "fireemblemheroes": "fireemblemhrdinovia", "honkaiimpact": "honkaiimpact", "soccerbattle": "futbalovabattle", "a3": "a3", "phonegames": "hrynaphone", "kingschoice": "kingskevyber", "guardiantales": "strážcovsképríbehy", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikule", "cookierun": "peč<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "mimo<PERSON><PERSON>", "craftsman": "remeselník", "supersus": "supersus", "slowdrive": "pomalávôža", "headsup": "pozor", "wordfeud": "wordfeud", "bedwars": "postelevojny", "freefire": "zadaromfire", "mobilegaming": "mobilne<PERSON>y", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "tímovéboje", "clashofclans": "<PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "núdzov<PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "trávnydeň", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "traseniaazabavnenie", "ml": "ml", "bangdream": "bangdream", "clashofclan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "časovásprincezná", "beatstar": "beatstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "and<PERSON><PERSON><PERSON>", "criminalcase": "kriminálnypřípad", "summonerswar": "summonerswar", "cookingmadness": "variaciemadnes", "dokkan": "dokkán", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "l<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "pánovmobilné", "tinybirdgarden": "malývt<PERSON><PERSON>ikovzáhradný", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mojespevajucemonštrá", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "modarchiv", "raidshadowlegends": "raidshadowlegends", "warrobots": "vojnovéroboty", "mirrorverse": "zrkadlovýsvet", "pou": "pou", "warwings": "vojenskýkrídla", "fifamobile": "fifamobil", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "protihraciemu", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "b<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "vlkysúcool", "runcitygame": "runcityhra", "juegodemovil": "<PERSON><PERSON>nahrack<PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "čiernapúšťmobil", "rollercoastertycoon": "kolotočovýtycoon", "grandchase": "veľkáhonba", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendyonline", "otomegame": "o<PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "volaniehadov", "shiningnikki": "žiarivánikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demoláčnýderby3", "wordswithfriends2": "slovamiskamaráti2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfektnározprávka", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "zberatelmestá", "perfectworldmobile": "dokonalýsvetmobil", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "puzzlej<PERSON>rnenáempíria", "dragoncity": "drakmestocentrum", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "prdel", "littlenightmare": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "slzyzeme", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "db<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "<PERSON>oz<PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilnélegendybangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "k<PERSON><PERSON>skamama", "cabalmobile": "cabalmobilný", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "hranienabgm", "girlsfrontline": "frontline<PERSON><PERSON>", "jurassicworldalive": "jurassicworldoživene", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "prekonávamto", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "mesiačnovýchod", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mob<PERSON><PERSON>", "legendofneverland": "legend<PERSON>be<PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "timeraiders", "gamingmobile": "mobilne<PERSON>y", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bitkatiek", "dnd": "dnd", "quest": "úloha", "giochidiruolo": "hramydurulom", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "cestovatelttprpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "romantickýklub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poké<PERSON>legends<PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pok<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON><PERSON>", "pokemonranger": "pokémonstráž<PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "p<PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "teamystik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "rozbite", "shinypokemon": "lesklépokémony", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "železnéruky", "kabutops": "kabutops", "psyduck": "psyžaba", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "p<PERSON><PERSON><PERSON>", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pohodovýpokémonmistr", "pokémonsleep": "pokémonspánok", "kidsandpokemon": "detiav<PERSON>k<PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "lesklovysluchac", "ajedrez": "<PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON>", "scacchi": "šachy", "schaken": "šakujem", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "šachov<PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "<PERSON><PERSON><PERSON>", "japanesechess": "japonská<PERSON><PERSON>", "chinesechess": "<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šachycanada", "fide": "fide", "xadrezverbal": "básnevohry", "openings": "otvorenia", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "pánjaskynnýchdobrodr<PERSON>žstiev", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxvenda", "darksun": "tmavéslunce", "thelegendofvoxmachina": "legendaovoxmachina", "doungenoanddragons": "dungeonsanddragons", "darkmoor": "temnáčierňava", "minecraftchampionship": "minecraftšampionát", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbetón", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmódy", "mcc": "mcc", "candleflame": "plameňsviečky", "fru": "fru", "addons": "dopln<PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftvrecko", "minecraft360": "minecraft360", "moddedminecraft": "modif<PERSON><PERSON>ý<PERSON>craft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftjaskyne", "minecraftcity": "minecraftmesto", "pcgamer": "pchrac", "jeuxvideo": "hernyvideo", "gambit": "gambit", "gamers": "gamery", "levelup": "zdvihnisa", "gamermobile": "gamermobil", "gameover": "koniechry", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON>", "pcgames": "p<PERSON>ry", "casualgaming": "neformálnyhry", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmajsterzávod", "pcgame": "pchračka", "gamerboy": "gamerchlapeček", "vrgaming": "vrhernýsvet", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "hernepoužívateľ", "boxi": "boxi", "pro": "pro", "epicgamers": "epickígamery", "onlinegaming": "onlinehraní", "semigamer": "<PERSON><PERSON><PERSON>", "gamergirls": "hraciek", "gamermoms": "gamermamy", "gamerguy": "gamerchlapa", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ľ", "gameur": "gam<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerky", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "tímovápodpora", "mallugaming": "mallugaming", "pawgers": "pawmiláčikovia", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgamer": "staryhrac", "cozygaming": "útulnéhranie", "gamelpay": "gamelpay", "juegosdepc": "hrani<PERSON><PERSON>", "dsswitch": "dsswitch", "competitivegaming": "súťažnéhraním", "minecraftnewjersey": "minecraftnovejersej", "faker": "falošník", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosexuálnyhr<PERSON>", "gamepc": "her<PERSON><PERSON>", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmodsy", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "gamerky", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "prevalcovaný", "socialgamer": "<PERSON><PERSON><PERSON><PERSON>", "gamejam": "hernáak<PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "roler", "myteam": "mójtím", "republicofgamers": "republik<PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "kocurnahry", "triplelegend": "tripleleg<PERSON>a", "gamerbuddies": "gamerkámošovia", "butuhcewekgamers": "potrebu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "christiangamer": "krestanskhrac", "gamernerd": "hráčskýšprt", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "<PERSON><PERSON><PERSON><PERSON>", "casualgamer": "uvoln<PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "ne<PERSON><PERSON>", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "<PERSON><PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "<PERSON><PERSON><PERSON>", "wspólnegranie": "spoločnehhranie", "mortdog": "mortdog", "playstationgamer": "playstationhráč", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "prototyp", "womangamer": "wabagamer", "obviouslyimagamer": "jasnélendímgamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "samosaran", "forager": "<PERSON>bie<PERSON><PERSON>", "humanfallflat": "clovekpad<PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonicž<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "prepnúť", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "majsteradvokat", "ssbm": "ssbm", "skychildrenofthelight": "nebeskidsdetizlaya", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "slzykráľovstva", "walkingsimulators": "prechádzkovésimul<PERSON>tory", "nintendogames": "nintendohry", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "<PERSON>rak<PERSON><PERSON>", "harvestmoon": "zberateľskémesiac", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "dychprírody", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51hier", "earthbound": "zemskýlimit", "tales": "prí<PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktaj", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "t<PERSON><PERSON>ž", "trianglestrategy": "trojuholníkovástrategia", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "školynerobnezvyšok", "nintendos": "nintendos", "new3ds": "nový3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "divokýrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligašampiónovlas", "urgot": "urgot", "zyra": "zyra", "redcanids": "červen<PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftsk", "lolph": "lolph", "leagueoflegend": "ligalegiend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligatrialokwild", "adcarry": "adcarry", "lolzinho": "lolko", "leagueoflegendsespaña": "leagueoflegendsslovensko", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "ligalegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "šako", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "zedhlavný", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitehra", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideohry", "scaryvideogames": "strašidelnéhry", "videogamemaker": "tvorcahraciek", "megamanzero": "megamanzero", "videogame": "videohra", "videosgame": "<PERSON><PERSON><PERSON>", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "<PERSON>zrite<PERSON><PERSON><PERSON>", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "čarodej101", "battleblocktheater": "battleblocktheater", "arcades": "herne", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "farmingimulator", "robloxchile": "robloxslovensko", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxnemecko", "robloxdeutsch": "robloxslovensko", "erlc": "erlc", "sanboxgames": "sanboxhry", "videogamelore": "hernýpríbeh", "rollerdrome": "rolldrom", "parasiteeve": "parazitkaeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "mŕtvaoblasť", "amordoce": "amordoce", "videogiochi": "<PERSON><PERSON><PERSON>", "theoldrepublic": "stararepublika", "videospiele": "<PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "dobrodruzstvenehry", "wolfenstein": "wolfenstein", "actionadventure": "akčnáadventure", "storyofseasons": "príbehr<PERSON>č<PERSON>", "retrogames": "retrogames", "retroarcade": "retroherňa", "vintagecomputing": "vintage<PERSON>čí<PERSON>če", "retrogaming": "retrogaming", "vintagegaming": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "hráčkovanie", "commanderkeen": "veliteľkeen", "bugsnax": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "injustice2": "neprávosť2", "shadowthehedgehog": "tienistahroch", "rayman": "rayman", "skygame": "skygame", "zenlife": "zenživot", "beatmaniaiidx": "beatmaniaiidx", "steep": "str<PERSON><PERSON>", "mystgames": "mystgames", "blockchaingaming": "blockchainhranie", "medievil": "stredovek", "consolegaming": "konzoľovéhraní", "konsolen": "konsolen", "outrun": "utie<PERSON><PERSON>", "bloomingpanic": "kvitnúcipanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "hrydes<PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "superobor", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "farmingsims", "juegosviejos": "<PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackbox<PERSON>y", "interactivefiction": "interaktívnefikcie", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "poslednísnás2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "vizuálnanovela", "visualnovels": "vizuálneromány", "rgg": "rgg", "shadowolf": "tieňovývlk", "tcrghost": "tcrduch", "payday": "výplata", "chatherine": "katherine", "twilightprincess": "twilightprincezná", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON>ko", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "novellook", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolúciafúka<PERSON>", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "starrail", "keyblade": "kľúčovázbraň", "aplaguetale": "aplagueyourstory", "fnafsometimes": "fnafniekedy", "novelasvisuales": "vizuálnepríbehy", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "<PERSON><PERSON><PERSON>", "videogamedates": "videohračkyschôdzky", "mycandylove": "mojacukrslaskअक", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "bat<PERSON><PERSON><PERSON>", "returnofreckoning": "návratpočí<PERSON>če", "gamstergaming": "gamstergaming", "dayofthetantacle": "džúsovýdeň", "maniacmansion": "man<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "crashracing", "3dplatformers": "3dplatformery", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "rezidentzlo6", "soundodger": "soundodger", "beyondtwosouls": "mimo_d<PERSON>h_du<PERSON>", "gameuse": "hranie", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "zvýšsilásk", "katanazero": "katana0", "famicom": "famicom", "aventurasgraficas": "grafickeadentury", "quickflash": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>k", "fzero": "fzero", "gachagaming": "gach<PERSON><PERSON>e", "retroarcades": "retroadrenalin", "f123": "f123", "wasteland": "púšť", "powerwashsim": "powerwashsim", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animebojovníci2", "footballfusion": "fúziafutbalu", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "zkru<PERSON>ékov<PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simul<PERSON><PERSON>", "symulatory": "symulátory", "speedrunner": "rýchlostnýbežec", "epicx": "epickéx", "superrobottaisen": "superrobotťaženie", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gryvideo", "gaiaonline": "gaiaonline", "korkuoyunu": "hr<PERSON><PERSON><PERSON>šnáhra", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "prí<PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownprepisané", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "mestskýchaos", "heavenlybodies": "nebeske_tela", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "priest<PERSON>vysi<PERSON>lator", "legacyofkain": "dedičstvokaina", "hackandslash": "hackaseknúť", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "her<PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "vlkmedzi_nami", "truckingsimulator": "kamionovysimulator", "horizonworlds": "horizonworlds", "handygame": "handygame", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "staréškolskévide<PERSON><PERSON>", "racingsimulator": "pretekarskisimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentibezdôvodov", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterhunterteraz", "rebelstar": "rebelskáhviezda", "indievideogaming": "indievideohry", "indiegaming": "indiegaming", "indievideogames": "indievideohry", "indievideogame": "indievideohra", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "bufortress", "unbeatable": "neprekonate<PERSON>ý", "projectl": "projektl", "futureclubgames": "budúceklubovéhry", "mugman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "insomnia<PERSON><PERSON>ne", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "zásoba", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON>", "cityskylines": "mestskéhorizonty", "supermonkeyball": "superopičkovaľba", "deponia": "deponia", "naughtydog": "naughtypes", "beastlord": "bestievládca", "juegosretro": "retrohranie", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriadnemlstvommore", "alanwake": "alanwake", "stanleyparable": "stanley<PERSON><PERSON><PERSON>", "reservatoriodedopamin": "rezervátordopamínu", "staxel": "staxel", "videogameost": "videohraost", "dragonsync": "drač<PERSON>úl<PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "milujemkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "<PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "svät<PERSON><PERSON><PERSON>i", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "sma<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "tmavšínežčierny", "animescaling": "animescaling", "animewithplot": "animesozápletkou", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "temnýpán", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "animezo2000ok", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "animeobal", "thevisionofescaflowne": "víziamakaflowne", "slayers": "zabijaci", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaleteobmedzenýhanako<PERSON>n", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "ohnovépodpory", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "buducnostizapisník", "fairytail": "roz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "vyrobenévabyss", "parasyte": "para<PERSON>ta", "punpun": "punpun", "shingekinokyojin": "útočištěnaobry", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "morsk<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horormanga", "romancemangas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "čiernejašterice", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terra<PERSON><PERSON><PERSON>", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "určitýmagickýindex", "sao": "sao", "blackclover": "čiernado<PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8neobmedzene", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriorita", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosmický", "goldenkamuy": "zlatýkamuy", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportoveanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "<PERSON>jels<PERSON><PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "<PERSON><PERSON><PERSON>", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "pekniteľka", "theboyandthebeast": "chlapecazviera", "fistofthenorthstar": "fistsevernéhohviezdneho", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "<PERSON><PERSON>", "servamp": "servamp", "howtokeepamummy": "jak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "plnámesiacboo", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "martialpeak", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON>core<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerodva", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "s<PERSON><PERSON>úš", "amiti": "amiti", "sailorsaturn": "námornícinasaturne", "dio": "dio", "sailorpluto": "námorníčkapluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "motorovápílač", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "čiernysluha", "ergoproxy": "ergoproxy", "claymore": "kláčimor", "loli": "loli", "horroranime": "horo<PERSON><PERSON>", "fruitsbasket": "ovoc<PERSON><PERSON><PERSON>ša", "devilmancrybaby": "diablovpláčbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "<PERSON><PERSON><PERSON>", "lovelive": "žijmepreľásku", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "sľúbenákrajina", "monstermanga": "monstermanga", "yourlieinapril": "tvojaklamasaapríli", "buggytheclown": "buggyklovn", "bokunohero": "bokunohero", "seraphoftheend": "seraf<PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "hlbinnýzajatec", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mŕtvyčlovekzázrak", "bannafish": "bannafish", "sukuna": "<PERSON><PERSON><PERSON>", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "j<PERSON><PERSON>zavojny", "cardcaptorsakura": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "ktojevečnosti", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "tajnáaliancia", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "vymazané", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detektívconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shojobeat": "shojobeat", "vampireknight": "upírskaželeznica", "mugi": "mugi", "blueexorcist": "modrýexorcista", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "špiónskafamília", "airgear": "vzdušnékolesá", "magicalgirl": "magickádievča", "thesevendeadlysins": "t<PERSON>chsedemhlav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "vezenska_skola", "thegodofhighschool": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "bozkxzrs", "grandblue": "grandblue", "mydressupdarling": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animevesmír", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromantika", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "démonovyničiteľkmeču", "bloodlad": "krvavýfrér", "goodbyeeri": "<PERSON>bohom<PERSON>", "firepunch": "ohnivépunch", "adioseri": "zbohomseru", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "hviezdysez<PERSON><PERSON>", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "čarovné<PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "stredn<PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "smrtnýpriechod", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japonskéanime", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedab", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "potkan", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "broskyňa", "cavalieridellozodiaco": "kavalierizverokruhu", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "srdcemanga", "deliciousindungeon": "lahodnévpodzemí", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "záznamozragnarok", "funamusea": "zabavnaubytka", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "mangaaním", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialjestrvdoťaž<PERSON>é", "overgeared": "premož<PERSON>ý", "toriko": "<PERSON><PERSON>o", "ravemaster": "maj<PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "čarodejn<PERSON><PERSON>k<PERSON>búkatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kameň", "mangaislife": "<PERSON>ž<PERSON><PERSON>", "dropsofgod": "kvapkyboha", "loscaballerosdelzodia": "panskáastriológia", "animeshojo": "animeshojo", "reverseharem": "obratnéharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "skvelyucitelonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "bojovník", "mybossdaddy": "môjšéfotec", "gear5": "gear5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "krvplus", "bloodplusanime": "krvplusanime", "bloodcanime": "krvavéanime", "bloodc": "krvnatyd", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vranyxnajhoršie", "splatteranime": "splattersanime", "splatter": "rozmazanie", "risingoftheshieldhero": "vzrasthieldhero", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animešpanielsko", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "klamalklamal", "supercampeones": "supermadričovia", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiťomboltelfónom", "midorinohibi": "midorinoinhiby", "magicalgirls": "magickédievcatá", "callofthenight": "<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "tieňovázáhrada", "tsubasachronicle": "tsubasachronicle", "findermanga": "nájdimanga", "princessjellyfish": "princez<PERSON><PERSON>ú<PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradízkovýbozok", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevrs", "persocoms": "perso<PERSON><PERSON>", "omniscientreadersview": "pohľadomniscientnéhočítača", "animecat": "animemačka", "animerecommendations": "odporúčaniaanimé", "openinganime": "otváranieanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mójteenromanticomédia", "evangelion": "evan<PERSON><PERSON>", "gundam": "gundam", "macross": "macross", "gundams": "<PERSON><PERSON><PERSON>", "voltesv": "voltesv", "giantrobots": "obrovskéroboty", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilnýfighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilnésútygundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bielenie", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "plnýmetalovýalchymista", "ghiaccio": "ľad", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "vojenskéanime", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeville", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "strednáškoladxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "bojovníkprotidémonom", "hinokamikagurademonsl": "hinokamikagurademonsk", "attackontitan": "útoknatitána", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "mojaheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "prieskumníkovia", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pra<PERSON>ý", "revengers": "pomstitelia", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efektšťastnéhochlapca", "digimonstory": "digimonpríbeh", "digimontamers": "digimontamers", "superjail": "superväzenie", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "našihostclub", "flawlesswebtoon": "bezchybnýwebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "létajúcikňažka", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "lentak", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "všetkísvätíulica", "recuentosdelavida": "príbehyzoživotu"}