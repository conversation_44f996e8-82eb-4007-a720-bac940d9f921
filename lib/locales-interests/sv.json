{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "kognitivafunktioner", "psychology": "psykologi", "philosophy": "filos<PERSON><PERSON>", "history": "historia", "physics": "fysik", "science": "vetenskap", "culture": "kultur", "languages": "språk", "technology": "teknologi", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologimemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "roligt", "videos": "videor", "gadgets": "prylar", "politics": "politik", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "livsråd", "crypto": "kryptovalutor", "news": "nyheter", "worldnews": "världsnyheter", "archaeology": "arkeologi", "learning": "<PERSON><PERSON><PERSON><PERSON>", "debates": "debatter", "conspiracytheories": "konspirationsteorier", "universe": "universum", "meditation": "meditation", "mythology": "mytologi", "art": "konst", "crafts": "hantver<PERSON>", "dance": "dans", "design": "design", "makeup": "makeup", "beauty": "skönhet", "fashion": "mode", "singing": "sjunga", "writing": "skrivande", "photography": "fotografering", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "teckna", "books": "<PERSON><PERSON><PERSON>", "movies": "filmer", "poetry": "poesi", "television": "television", "filmmaking": "filmskapande", "animation": "animation", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumentärer", "mystery": "mysterier", "comedy": "komedi", "crime": "deckare", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "s<PERSON><PERSON><PERSON><PERSON>", "romance": "romans", "realitytv": "reality", "action": "action", "music": "musik", "blues": "blues", "classical": "klassiskt", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronisk", "folk": "folkmusik", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "resor", "concerts": "konserter", "festivals": "festivaler", "museums": "museer", "standup": "standup", "theater": "teater", "outdoors": "f<PERSON>uftsliv", "gardening": "trädgårdsarbete", "partying": "fester", "gaming": "gaming", "boardgames": "brädspel", "dungeonsanddragons": "dungeonsanddragons", "chess": "schack", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "mat", "baking": "baka", "cooking": "<PERSON><PERSON><PERSON>", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "<PERSON><PERSON><PERSON><PERSON>", "cats": "katter", "dogs": "hundar", "fish": "<PERSON><PERSON>", "animals": "djur", "blacklivesmatter": "blacklivesmatter", "environmentalism": "miljöaktivism", "feminism": "feminism", "humanrights": "mänskligarättigheter", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "volontärarbete", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "basket", "boxing": "boxning", "cricket": "cricket", "cycling": "cykling", "fitness": "fitness", "football": "fotboll", "golf": "golf", "gym": "gym", "gymnastics": "gymnastik", "hockey": "ishockey", "martialarts": "kampsport", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON><PERSON><PERSON>", "skateboarding": "skateboard", "skiing": "skidåkning", "snowboarding": "snowboard", "surfing": "surfa", "swimming": "simning", "tennis": "tennis", "volleyball": "volleyboll", "weightlifting": "tyngdlyftning", "yoga": "yoga", "scubadiving": "dykning", "hiking": "vandring", "capricorn": "stenbocken", "aquarius": "<PERSON><PERSON><PERSON><PERSON>", "pisces": "<PERSON><PERSON><PERSON>", "aries": "väduren", "taurus": "oxen", "gemini": "t<PERSON><PERSON><PERSON><PERSON>", "cancer": "kräftan", "leo": "lejonet", "virgo": "j<PERSON><PERSON><PERSON>", "libra": "vågen", "scorpio": "skorpionen", "sagittarius": "skytten", "shortterm": "k<PERSON><PERSON><PERSON>", "casual": "avslappnat", "longtermrelationship": "långsiktigtförhållande", "single": "singel", "polyamory": "polyamori", "enm": "öppetförhållande", "lgbt": "hbtq", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbisk", "bisexual": "bisexuell", "pansexual": "pansexuell", "asexual": "asexuell", "reddeadredemption2": "rödadödenåterlösning2", "dragonage": "<PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kungsäventyr", "soulreaver": "själsfiskare", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiv", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "drakensdömning", "sunsetoverdrive": "solnedgångöverdrift", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "eldsymbolöden", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "g<PERSON><PERSON><PERSON>", "openworld": "öppenvärld", "heroesofthestorm": "hjältarnaistormen", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dungeon<PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "stam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "<PERSON><PERSON><PERSON>", "lordsoftherealm2": "herrarnaiparalärdom2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "färgvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivasimuleringar", "okage": "okage", "juegoderol": "rollspel", "witcher": "häxmästare", "dishonored": "skymfad", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "karaktärsskapande", "immersive": "uppslukande", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "slutligafantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivation", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "suckerförkärlek", "otomegames": "otomes<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampyrernasmaskerad", "dimension20": "dimension20", "gaslands": "gaslandet", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ed", "shadowrun": "skuggjakt", "bloodontheclocktower": "blodpåklocktornet", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "tyngdkraftspräng", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "överherre", "yourturntodie": "dinhämtningattdö", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "äldrevärldenonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsålar", "mu": "mu", "falloutshelter": "falloutshelter", "gurps": "gurps", "darkestdungeon": "mörkastehålan", "eclipsephase": "solförmörkelsefas", "disgaea": "disgaea", "outerworlds": "ytrevärldar", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastikrigare", "skullgirls": "skullgirls", "nightcity": "nattstad", "hogwartslegacy": "hog<PERSON><PERSON><PERSON>", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "aldrigvintern", "road96": "väg96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "glömdariken", "dragonlance": "drak<PERSON><PERSON><PERSON>", "arenaofvalor": "arenaförvalor", "ffxv": "ffxv", "ornarpg": "örnarpg", "toontown": "teckentown", "childoflight": "ljusetsbarn", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "linje2", "digimonworld": "digimonvärlden", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkan<PERSON>n", "fracturedthrones": "brutna_troner", "horizonforbiddenwest": "horizonförbjudenväst", "twewy": "twewy", "shadowpunk": "sku<PERSON><PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartsmysteriet", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "stjärnfinnare", "goldensun": "gyllenesol", "divinityoriginalsin": "gudomligoriginalsynd", "bladesinthedark": "bladeniaskuggan", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkröd", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenordern", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ondalandskap", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "djävulsöverlevare", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "bondrpg", "oldworldblues": "gamlavärldensblues", "adventurequest": "äventyrsuppdrag", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "rollspel", "finalfantasy9": "finalfantasy9", "sunhaven": "solhimmel", "talesofsymphonia": "sagornasymfonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "dagskärva", "torncity": "tornstad", "myfarog": "minfarog", "sacredunderworld": "heligaundervärlden", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "mörksjäl", "soulslikes": "sj<PERSON><PERSON><PERSON>s", "othercide": "annorlundaskap", "mountandblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "evighets<PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "divisionen", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "drak<PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octopathresenär", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "lillaträdet", "childrenofmorta": "barnenav<PERSON><PERSON>", "engineheart": "motorhjärta", "fable3": "fabel3", "fablethelostchapter": "fabledenförloradekapitel", "hiveswap": "hiveswap", "rollenspiel": "rollspel", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "stjärnfält", "oldschoolrevival": "gamla_skolan_<PERSON><PERSON>up<PERSON>livad", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "mörkborg", "savageworlds": "savagevärldar", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "mörkretshåla", "juegosrpg": "<PERSON><PERSON><PERSON><PERSON>", "kingdomhearts": "kungarikeshjärtan", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "vildahjärtan", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "skugghjärtan", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblod", "breathoffire4": "eldandedrag4", "mother3": "mor3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "rollspelsledningar", "roleplaygame": "rollspelslek", "fabulaultima": "fabulault<PERSON>", "witchsheart": "häxhjärta", "harrypottergame": "harry<PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "jak<PERSON>yal<PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterjaktvärlden", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "skugghj<PERSON>rtaavtal", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "konungadomer", "awplanet": "awplanet", "theworldendswithyou": "världenavslutasmeddig", "dragalialost": "dragalialost", "elderscroll": "äldreuppdrag", "dyinglight2": "dödligtljus2", "finalfantasytactics": "finalfantasytaktik", "grandia": "grandia", "darkheresy": "mörkheteri", "shoptitans": "shop<PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "jordmagik", "blackbook": "svartbok", "skychildrenoflight": "himmelsbarnavlju<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "heligtguldedition", "castlecrashers": "<PERSON><PERSON>kra<PERSON><PERSON>", "gothicgame": "gothspel", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "spöksnörentokyo", "fallout2d20": "fallout2d20", "gamingrpg": "spelarpg", "prophunt": "prophunt", "starrails": "stjärnspår", "cityofmist": "<PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "pekaochklicka", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "odelbar", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7föralltidkris", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "dödsvägentillkanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "eldsymbol", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "spöketfråntushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gång", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "sj<PERSON><PERSON>ätare", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonaryspel", "tacticalrpg": "taktiskrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "evi<PERSON><PERSON><PERSON>", "princessconnect": "prinsesskoppla", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON>", "cristales": "kristaller", "vcs": "vcs", "pes": "pes", "pocketsage": "fickadoktor", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valorant<PERSON>ien", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efotboll", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "drömmarnasliga", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "overwatchligan", "cybersport": "cybersport", "crazyraccoon": "tokigaskunkar", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valoranttävlingsspel", "t3arena": "t3arena", "valorantbr": "valo<PERSON><PERSON>", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "halvliv", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "evi<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "getsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "frihetsplaneten", "transformice": "transformice", "justshapesandbeats": "baraformerochtakter", "battlefield4": "battlefield4", "nightinthewoods": "nattitenskogen", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanor", "overcooked": "överkokt", "interplanetary": "interplanetär", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dv<PERSON>rgf<PERSON><PERSON>", "foxhole": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "stray", "battlefield": "slagfält", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON>", "eyeb": "ögonböj", "blackdesert": "svartöken", "tabletopsimulator": "bordspelssimulator", "partyhard": "festahårt", "hardspaceshipbreaker": "hårtrymdskeppsavdelare", "hades": "hades", "gunsmith": "vapensmed", "okami": "<PERSON>ami", "trappedwithjester": "trapped<PERSON><PERSON><PERSON>", "dinkum": "dinkum", "predecessor": "föregångare", "rainworld": "regnvärlden", "cavesofqud": "qudsgrottor", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "krig<PERSON>g<PERSON>ing", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "mörkareochmörkare", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "sj<PERSON><PERSON>rb<PERSON>re", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kubflykt", "hifirush": "hifirush", "svencoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newcity": "nystad", "citiesskylines": "stadssilanser", "defconheavy": "defconheavy", "kenopsia": "kenopsi", "virtualkenopsia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowrunner": "snörunner", "libraryofruina": "biblioteketförruin", "l4d2": "l4d2", "thenonarygames": "deicknoförbunden", "omegastrikers": "omegastrejkarna", "wayfinder": "vägvisare", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "plaskigplastanda", "battlebit": "battlebit", "ultimatechickenhorse": "ulti<PERSON><PERSON><PERSON><PERSON>", "dialtown": "dialstaden", "smileforme": "leendeförmig", "catnight": "kattnatt", "supermeatboy": "supermeatboy", "tinnybunny": "tinny<PERSON><PERSON>", "cozygrove": "myskoga", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "regnbåge6", "apexlegends": "apexlegends", "cod": "codsnack", "borderlands": "gränsland", "pubg": "pubg", "callofdutyzombies": "call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6belägring", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcryspel", "paladins": "paladins", "earthdefenseforce": "jordförsvarsstyrka", "huntshowdown": "jagauppgörelse", "ghostrecon": "spökslag", "grandtheftauto5": "gta5", "warz": "krigz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "upproretsandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "dödskoppling", "b4b": "b4b", "codwarzone": "codkrigszona", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombier", "mirrorsedge": "spegelkants", "divisions2": "divisioner2", "killzone": "<PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "coldwar<PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "korskod", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernakrig", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechkrigare", "boarderlands": "grä<PERSON><PERSON><PERSON>", "owerwatch": "övervakning", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "världenavkrigsskepp", "back4blood": "tillbaka4blod", "warframe": "krigsram", "rainbow6siege": "regnbåge6belägring", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "grottberättelse", "doometernal": "doometernal", "centuryageofashes": "århundradeskorpor", "farcry4": "farcry4", "gearsofwar": "gears_of_war", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generationnoll", "enterthegungeon": "gåiniblandningen", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernkrigföring2", "blackops1": "svartoperation1", "sausageman": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "fantomsmärta", "warface": "krigansikte", "crossfire": "korseld", "atomicheart": "atomhjärtat", "blackops3": "blackops3", "vampiresurvivors": "vampyrsurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "fri<PERSON>t", "battlegrounds": "slagsfälten", "frag": "fragga", "tinytina": "<PERSON><PERSON>", "gamepubg": "spelapubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fpsspel", "convertstrike": "konverteringsstrejk", "warzone2": "krigszon2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "blodigröra", "republiccommando": "republikkommandon", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "<PERSON><PERSON>", "squad": "gänget", "destiny1": "öde1", "gamingfps": "spelaf<PERSON>", "redfall": "rödskymning", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "inskriven", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinasunderland", "halo2": "halo2", "payday2": "betalning2", "cs16": "cs16", "pubgindonesia": "pubgsverige", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "tvålkod", "ghostcod": "spöklika", "csplay": "cslek", "unrealtournament": "overkligtturnering", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "gränslandet2", "counterstrike": "motståndskraft", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "skakmästare", "halo3": "halo3", "halo": "halo", "killingfloor": "dödsplan", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonvit", "remnant": "<PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "krigetsvärld", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "<PERSON>ereach", "shadowman": "skugg<PERSON><PERSON>n", "quake2": "basta2", "microvolts": "mikromoln", "reddead": "r<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rost", "conqueronline": "erövranätet", "dauntless": "<PERSON><PERSON><PERSON>", "warships": "krigsfartyg", "dayofdragons": "drakdag", "warthunder": "warthunder", "flightrising": "flygtoppande", "recroom": "recroom", "legendsofruneterra": "legendernaföruneterra", "pso2": "pso2", "myster": "mystisk", "phantasystaronline2": "fantasystjärnornaonline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "<PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "andral<PERSON>t", "aion": "aion", "toweroffantasy": "tornavfantasi", "netplay": "nätlek", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superdjurroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "riddaronline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubbpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "skit", "newworld": "nyavärlden", "blackdesertonline": "svartökenonline", "multiplayer": "flerspelarläge", "pirate101": "pirat101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmorpg", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassisk", "worldofwarcraft": "världenavkrigshantverk", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "mobaspel", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "askoravskapelse", "riotmmo": "upprors<PERSON>o", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "<PERSON><PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "hämnd", "albiononline": "albiononline", "bladeandsoul": "bladof<PERSON>", "evony": "evony", "dragonsprophet": "dragonsprofet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "änglarpåinternet", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversespelet", "growtopia": "växtopia", "starwarsoldrepublic": "stjärnornakrigensgamlarepublik", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "perfektsvärld", "riseonline": "ställsigupponline", "corepunk": "kärnsynd", "adventurequestworlds": "äventyrquestvärldar", "flyforfun": "flygaförkul", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "kungariketavskyltar", "cityofheroes": "stadförhjältar", "mortalkombat": "mortalcombat", "streetfighter": "gatukrigare", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversum", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "sj<PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "gatornaåtboo", "mkdeadlyalliance": "mkdödligallians", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kungenavstriderna", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightingspel", "blasphemous": "blasfemisk", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "monstrenskrig", "jogosdeluta": "spelslag", "cyberbots": "cyberbots", "armoredwarriors": "rust<PERSON><PERSON><PERSON><PERSON>", "finalfight": "slutstrid", "poweredgear": "kraftgrejer", "beatemup": "knockaner", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "fightspel", "killerinstinct": "killerinstinkt", "kingoffigthers": "kampsportskungen", "ghostrunner": "spöklöpare", "chivalry2": "gentleman2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowk<PERSON>uppf<PERSON><PERSON>jar<PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongspel", "silksongnews": "silksongnytt", "silksong": "silksong", "undernight": "undernatt", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutionsturnering", "evomoment": "evomoment", "lollipopchainsaw": "lolli<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "berserietales", "bloodborne": "blodburet", "horizon": "horisont", "pathofexile": "exilevägen", "slimerancher": "slimerancher", "crashbandicoot": "krashbandicoot", "bloodbourne": "blodborn", "uncharted": "okartlagt", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "bannad", "playstationbuddies": "playstationkompisar", "ps1": "ps1", "oddworld": "konstigavärlden", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "kaniner", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "slä<PERSON>loss", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiler", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "krigsgud", "gris": "grisen", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitblirmänniska", "beatsaber": "beats<PERSON>r", "rimworld": "rimvärl<PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "tillsovastår", "touristtrophy": "turisttrofé", "lspdfr": "lspdfr", "shadowofthecolossus": "kolossensskugga", "crashteamracing": "kraschteamracing", "fivepd": "fivepd", "tekken7": "tekken7", "devilmaycry": "djävulenmåstegråta", "devilmaycry3": "devilmaycry3", "devilmaycry5": "djävulenkangråta5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samura<PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "manhunt", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "skuggahjärtan2pakt", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "festdjursvän", "warharmmer40k": "warhammer40k", "fightnightchampion": "fightnightchampion", "psychonauts": "psykonauter", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "elderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxboks", "battlefront": "stridsfront", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stjärnbunden", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "husvändare", "americanmcgeesalice": "amerikanskamcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxserien", "r6xbox": "r6xbox", "leagueofkingdoms": "kungadömmen", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "skräptv", "skycotl": "skycotl", "erica": "erica", "ancestory": "släktträd", "cuphead": "cuphead", "littlemisfortune": "lillaoturen", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterbal", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "ytvärlden", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "ankspelen", "thestanleyparable": "stanleydetektivhistorien", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "oculto", "longdrive": "långtbilåkande", "satisfactory": "nöjd", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "undergjord", "assettocorsa": "assettocorsa", "geometrydash": "geometridash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "andesk<PERSON><PERSON><PERSON>", "darkdome": "mörkdome", "pizzatower": "pizzatorn", "indiegame": "indiespel", "itchio": "itchio", "golfit": "golfit", "truthordare": "sanningellerutmaning", "game": "spel", "rockpaperscissors": "s<PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON>", "scavengerhunt": "skattjakt", "yardgames": "gårdsspel", "pickanumber": "väljettal", "trueorfalse": "santellerfalskt", "beerpong": "beerpong", "dicegoblin": "tärningsgoblin", "cosygames": "mysigaspel", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "gratiss<PERSON>", "drinkinggames": "dryckesspel", "sodoku": "sodoku", "juegos": "spel", "mahjong": "mahjong", "jeux": "spel", "simulationgames": "simulationspel", "wordgames": "ordspel", "jeuxdemots": "ordspel", "juegosdepalabras": "<PERSON><PERSON><PERSON>", "letsplayagame": "låtsspelanågonting", "boredgames": "tråkspel", "oyun": "spel", "interactivegames": "interaktivaspel", "amtgard": "amtgard", "staringcontests": "tittaiktävlingar", "spiele": "spela", "giochi": "spel", "geoguessr": "geoguessr", "iphonegames": "iphonespel", "boogames": "boospel", "cranegame": "kranspel", "hideandseek": "gömmaochsöka", "hopscotch": "hoppa", "arcadegames": "arkadspel", "yakuzagames": "yakuzaspel", "classicgame": "klassiskspel", "mindgames": "sinnes<PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galaspelet", "romancegame": "romansspel", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "tungvrickare", "4xgames": "4xspel", "gamefi": "spelaochtjäna", "jeuxdarcades": "arkadspel", "tabletopgames": "brädspel", "metroidvania": "metroidvania", "games90": "spel90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "racingspel", "ets2": "ets2", "realvsfake": "äktaellerfalsk", "playgames": "spelaspel", "gameonline": "spelaonline", "onlinegames": "onlinespel", "jogosonline": "spelaonline", "writtenroleplay": "skriftligrollspel", "playaballgame": "speletpåbollen", "pictionary": "pictionary", "coopgames": "coopspel", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON>", "highscore": "högnivå", "jeuxderôles": "rollspel", "burgergames": "burgerspel", "kidsgames": "barnspel", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgspelare", "juegodepreguntas": "frågespel", "gioco": "spel", "managementgame": "förvaltningsspel", "hiddenobjectgame": "doltföremålspel", "roolipelit": "rollspelet", "formula1game": "formel1spel", "citybuilder": "stadsbyggare", "drdriving": "drkörning", "juegosarcade": "arkadspel", "memorygames": "<PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "aktionspel", "blowgames": "blowgames", "pinballmachines": "flippermaskiner", "oldgames": "gam<PERSON>el", "couchcoop": "soffcoop", "perguntados": "frågade", "gameo": "spelao", "lasergame": "lasergame", "imessagegames": "imessagespel", "idlegames": "lataspel", "fillintheblank": "fylliatributen", "jeuxpc": "pcspel", "rétrogaming": "retrospel", "logicgames": "logikspel", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "tunnelbanesurf", "jeuxdecelebrite": "kändisspel", "exitgames": "utgångsspel", "5vs5": "5vs5", "rolgame": "roligaspel", "dashiegames": "dashiegames", "gameandkill": "spelaochdö", "traditionalgames": "traditionellaspel", "kniffel": "tärningsspel", "gamefps": "spelfps", "textbasedgames": "textbaseradespel", "gryparagrafowe": "gryparagrafen", "fantacalcio": "fantacalcet", "retrospel": "retrogaming", "thiefgame": "tjuv<PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "bordshockey", "tischfußball": "bordsfotboll", "spieleabende": "spelafton", "jeuxforum": "spelsnack", "casualgames": "casualspel", "fléchettes": "dartar", "escapegames": "utmaningsspel", "thiefgameseries": "tjuvspelserien", "cranegames": "kranespel", "játék": "spela", "bordfodbold": "bordfotboll", "jogosorte": "spelurval", "mage": "mage", "cargames": "bilspel", "onlineplay": "onlinelek", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON>", "pursebingos": "väskspelet", "randomizer": "slumptagare", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "spelpc", "socialdeductiongames": "socialadduktionsspel", "dominos": "dominos", "domino": "domino", "isometricgames": "isometriskaspel", "goodoldgames": "gamlahemsidor", "truthanddare": "sanning<PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "s<PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "spelsomärkäna", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "gratisa<PERSON><PERSON><PERSON>", "fantasygame": "fantasyspel", "gryonline": "gryonline", "driftgame": "driftspel", "gamesotomes": "spelärlivet", "halotvseriesandgames": "halotvserierochspel", "mushroomoasis": "svampoas", "anythingwithanengine": "alltmedmotor", "everywheregame": "överalltspel", "swordandsorcery": "svärdochtrollkonst", "goodgamegiving": "bra_spel_getillbaka", "jugamos": "vispelar", "lab8games": "lab8spel", "labzerogames": "labzerospel", "grykomputerowe": "speldatorer", "virgogami": "virgogami", "gogame": "speletärpå", "jeuxderythmes": "rytmmiljö", "minaturegames": "miniatyrspel", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "självkärleksspelande", "gamemodding": "spelmobbing", "crimegames": "brottsspel", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "charader", "singleplayer": "singelspelare", "coopgame": "coopspel", "gamed": "gammad", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenu", "maingame": "huvu<PERSON><PERSON>", "kingdiscord": "kungdiscord", "scrabble": "s<PERSON><PERSON><PERSON>", "schach": "schack", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemiarvet", "camelup": "<PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolygame", "brettspiele": "brädspel", "bordspellen": "b<PERSON><PERSON><PERSON><PERSON>", "boardgame": "brädspel", "sällskapspel": "sällskapspel", "planszowe": "brädspel", "risiko": "risiko", "permainanpapan": "brädspel", "zombicide": "zombicidе", "tabletop": "brädspel", "baduk": "baduk", "bloodbowl": "blodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "gobordspel", "connectfour": "connectfour", "heroquest": "hjältarnasäventyr", "giochidatavolo": "spelaentablå", "farkle": "farkle", "carrom": "carrom", "tablegames": "bordsspel", "dicegames": "tärningsspel", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaboo", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmiskmöte", "creationludique": "skapandelek", "tabletoproleplay": "bordrollspel", "cardboardgames": "pap<PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "spelsvängar", "infinitythegame": "infinityspelet", "kingdomdeath": "kungadöd", "yahtzee": "yahtzee", "chutesandladders": "rutschkanorochstegar", "társas": "sällskap", "juegodemesa": "brädspel", "planszówki": "brädspel", "rednecklife": "rödnecksliv", "boardom": "tråkdrom", "applestoapples": "äppleföräpple", "jeudesociété": "sällskapslek", "gameboard": "spelbräda", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "brädspelsmycket", "twilightimperium": "skymningsimperium", "horseopoly": "<PERSON><PERSON><PERSON><PERSON>", "deckbuilding": "kortbyggande", "mansionsofmadness": "mansionsofmadness", "gomoku": "gomoku", "giochidatavola": "brädspel", "shadowsofbrimstone": "skuggoravbrimstone", "kingoftokyo": "kungenavtokyo", "warcaby": "krig<PERSON>el", "táblajátékok": "brädspel", "battleship": "battleship", "tickettoride": "biljetttattur", "deskovehry": "skrivbordsspel", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "brädspel", "stolníhry": "brädspel", "xiángqi": "schack", "jeuxsociete": "brädspel", "gesellschaftsspiele": "sällskapsspel", "starwarslegion": "starwarslegion", "gochess": "go<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "sällskapsspel", "terraria": "terraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dagar", "identityv": "identitetv", "theisle": "öarna", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "kalla<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyochbläckmaskinen", "conanexiles": "conanexiles", "eft": "efterfest", "amongus": "blandaross", "eco": "ekologiskt", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetbyggare", "daysgone": "dagarnaärförbi", "fobia": "fobi", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologisk", "zomboid": "zomboid", "northgard": "nordgård", "7dtd": "7dtd", "thelongdark": "thelångamörka", "ark": "ark", "grounded": "jordad", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "galna<PERSON>der", "dontstarve": "hungriginte", "eternalreturn": "evigåterkomst", "pathoftitans": "titanernasväg", "frictionalgames": "frictionalgames", "hexen": "<PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "detondasinnet", "realrac": "rik<PERSON>grasb<PERSON><PERSON>", "thebackrooms": "baksalen", "backrooms": "bakrum", "empiressmp": "empiressmp", "blockstory": "blockberättelse", "thequarry": "gruvan", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "wehappyfew", "riseofempires": "imperiernasuppgång", "stateofsurvivalgame": "överlevnadsstatsspiel", "vintagestory": "vintagestory", "arksurvival": "arköverlevnad", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "bajswanspåbermuda", "frostpunk": "frostpunk", "darkwood": "mörkskog", "survivalhorror": "överlevnadsskräck", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "liveteftergame", "survivalgames": "överlevnadsspel", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "thiswarofmine", "scpfoundation": "scpfoundation", "greenproject": "grönaprojekt", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "flotte", "rdo": "rdo", "greenhell": "grönhelvete", "residentevil5": "residentevil5", "deadpoly": "död<PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onironaut", "granny": "mormor", "littlenightmares2": "littlenightmares2", "signalis": "signalis", "amandatheadventurer": "amanda<PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "skogensöner", "rustvideogame": "rustspel", "outlasttrials": "overlevaförsöken", "alienisolation": "utomjordingisolering", "undawn": "undawn", "7day2die": "7dagar2dö", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "överlevnad", "propnight": "propnatt", "deadisland2": "dödöarna2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampyr", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "kataklysmmörkadagar", "soma": "soma", "fearandhunger": "rädslochochsvält", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "livetefter", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "klocktorn3", "aloneinthedark": "ensamimörkret", "medievaldynasty": "medeltidsdynasti", "projectnimbusgame": "projektmolnspel", "eternights": "evigakvällar", "craftopia": "hantverkslandet", "theoutlasttrials": "outlasttrials", "bunker": "bunker", "worlddomination": "världsherravälde", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dvärgslaktare", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "warhammer40kherregud", "warhammer40klore": "warhammer40kmytologi", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ilovesororitas", "ilovevindicare": "ilovevindicare", "iloveassasinorum": "iloveassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "yrkesmördare", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "åldernförimperier", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeråldernavsigmar", "civilizationv": "civilisationv", "ittakestwo": "detkrävs2", "wingspan": "vingbredd", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "hjältaravstyrkaochmagin", "btd6": "btd6", "supremecommander": "supremekommandant", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "utpost2", "banished": "bortdriven", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "befallao<PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "evigakriget", "strategygames": "strategispel", "anno2070": "anno2070", "civilizationgame": "civilisationspelet", "civilization4": "civilisation4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totalkrig", "travian": "travian", "forts": "forts", "goodcompany": "braföretag", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "sna<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "förkungarna", "realtimestrategy": "realtidsstrategi", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kungarntvåkronor", "eu4": "eu4", "vainglory": "snobbighet", "ww40k": "ww40k", "godhood": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "åretsmoment", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveskulmatteclass", "plagueinc": "pestinc", "theorycraft": "teorihantverk", "mesbg": "mesbg", "civilization3": "civilisation3", "4inarow": "4irad", "crusaderkings3": "korsfararkungar3", "heroes3": "hjältar3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "lärjungar2", "plantsvszombies": "växtervszombier", "giochidistrategia": "strategispel", "stratejioyunları": "strategispel", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "tidsåldernförunder", "dinosaurking": "dinosaurkung", "worldconquest": "världserövring", "heartsofiron4": "heartsofiron4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "striden<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "empiresmedsmidjärn", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "g<PERSON>seng<PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "phobier", "phobiesgame": "phobies<PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "ytplan", "turnbased": "turordning", "bomberman": "<PERSON><PERSON><PERSON>", "ageofempires4": "ageofempires4", "civilization5": "civilisation5", "victoria2": "victoria2", "crusaderkings": "korsfararkungar", "cultris2": "cultris2", "spellcraft": "trollkonst", "starwarsempireatwar": "stjärnornaskrigimperietkrigar", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulltbrev", "shiningforce": "lysandekraft", "masterduel": "mästarduell", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportmagnat", "unrailed": "<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "flygplanfrånplåga", "uplandkingdoms": "övrekungadömen", "galaxylife": "galaxyliv", "wolvesvilleonline": "wwolvesvilleonline", "slaythespire": "slayaåttan", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "behovfartkollektiv", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicemadnessåterkommer", "darkhorseanthology": "mörkhästantologi", "phasmophobia": "fasmofobi", "fivenightsatfreddys": "femnätterpåfreddys", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "sm<PERSON><PERSON><PERSON>", "deadrising": "dödsutveckling", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON>mahäng", "deadisland": "dödisland", "litlemissfortune": "lillamissotur", "projectzero": "projektzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hellonabo2", "gamingdbd": "gamingdbd", "thecatlady": "ka<PERSON><PERSON><PERSON>", "jeuxhorreur": "skräckspel", "horrorgaming": "skräckspel", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "kortspel", "cardsagainsthumanity": "kortmotmänniskor", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodnamn", "dixit": "dixit", "bicyclecards": "cykelkort", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "kortstenar", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "nyckelsmedjan", "cardtricks": "korttricks", "playingcards": "spelkort", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kort<PERSON>ten", "pokemoncards": "pokemonkort", "fleshandbloodtcg": "köttochblodtcg", "sportscards": "sportkort", "cardfightvanguard": "kortstridvanguard", "duellinks": "duellinks", "spades": "spader", "warcry": "krigsrop", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "hjärtatskung", "truco": "truco", "loteria": "lotteri", "hanafuda": "hana<PERSON>da", "theresistance": "motståndet", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkort", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON><PERSON><PERSON>ll", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspel", "darkmagician": "mörkamagiker", "blueeyeswhitedragon": "bl<PERSON>ög<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kortspel", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kortspel", "mtgjudge": "mtgdomare", "juegosdecartas": "kortspel", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kortspel", "carteado": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "stri<PERSON><PERSON><PERSON>", "battlespiritssaga": "stri<PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "kortgalskap", "žolíky": "boožolíky", "facecard": "ansiktskort", "cardfight": "kortstrid", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "magiskakort", "yugiohmasterduel": "yugiohmästerspel", "shadowverse": "skuggauniversum", "skipbo": "skip<PERSON>", "unstableunicorns": "ostadigaunicorns", "cyberse": "cyberse", "classicarcadegames": "klassiskaarkadspel", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansspel", "fridaynightfunkin": "fredagskvällfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "g<PERSON>rrhj<PERSON>lt<PERSON>", "clonehero": "klonhjälte", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmästare", "dancecentral": "danscentral", "rhythmgamer": "rytmgamer", "stepmania": "stepmania", "highscorerythmgames": "högapoängrytmspel", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "taktskydd", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON>", "auditiononline": "auditiononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kryptonecrodancer", "rhythmdoctor": "rytmdoktor", "cubing": "kubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puzzelspelet", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logiskagåtor", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON>rn<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "korsord", "motscroisés": "motscroisés", "krzyżówki": "korsord", "nonogram": "nonogram", "bookworm": "bokmal", "jigsawpuzzles": "pussel", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "g<PERSON><PERSON>", "riddles": "g<PERSON><PERSON>", "rompecabezas": "pussel", "tekateki": "tekateki", "inside": "inne", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "escapesimulator", "minesweeper": "gruvsprängare", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "kryssningar", "kurushi": "k<PERSON>hi", "gardenscapesgame": "trädgårdsspelet", "puzzlesport": "puzzelsport", "escaperoomgames": "escaperoomspel", "escapegame": "flyktspelet", "3dpuzzle": "3dpussel", "homescapesgame": "homescapesgame", "wordsearch": "ordjakt", "enigmistica": "enigmistiska", "kulaworld": "kulavärlden", "myst": "myst", "riddletales": "gåtberättelser", "fishdom": "fiskekungdom", "theimpossiblequiz": "detomöjligafrågan", "candycrush": "godisexplosion", "littlebigplanet": "litenstorpstora", "match3puzzle": "match3pussel", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "knasig", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "hemträdgårdar", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "gåtoråmig", "tycoongames": "tycoongames", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "korsord", "ciphers": "koder", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON>", "buscaminas": "boobananer", "puzzlesolving": "pussell<PERSON>sning", "turnipboy": "raps<PERSON><PERSON>n", "adivinanzashot": "gissningarnaärklara", "nobodies": "ingenборs", "guessing": "gissa", "nonograms": "nonogrammar", "kostkirubika": "kostkurbita", "crypticcrosswords": "kryptiskakorsord", "syberia2": "syberia2", "puzzlehunt": "puzzeljakt", "puzzlehunts": "pusseljakten", "catcrime": "kattbrott", "quebracabeça": "knäckahuvudet", "hlavolamy": "boobiten", "poptropica": "poptropica", "thelastcampfire": "densistaeldsjälen", "autodefinidos": "självdefinierade", "picopark": "picopark", "wandersong": "<PERSON>dr<PERSON>ång", "carto": "carto", "untitledgoosegame": "ointitladgåspektion", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrint", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "snabbkub", "pieces": "bitar", "portalgame": "portalspel", "bilmece": "bilbabe", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixkuben", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "vridennunderbarvärld", "monopoly": "monopol", "futurefight": "framtidsfight", "mobilelegends": "mobillegender", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "en<PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitliv", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestjärnor", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookiekungariket", "alchemystars": "alchemystars", "stateofsurvival": "överlevnadsläget", "mycity": "<PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "färgglattsteg", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "ödesstorslag", "hyperfront": "hyperfront", "knightrun": "riddartur", "fireemblemheroes": "eldensymbolhjältar", "honkaiimpact": "honkaiimpact", "soccerbattle": "fotbollstrid", "a3": "a3", "phonegames": "telefonspel", "kingschoice": "kung<PERSON><PERSON>", "guardiantales": "väktarsagor", "petrolhead": "<PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktisktcool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "utanförloopen", "craftsman": "ha<PERSON><PERSON><PERSON><PERSON>", "supersus": "supersus", "slowdrive": "långsamkörning", "headsup": "headsupp", "wordfeud": "ord<PERSON>jd", "bedwars": "sängkrig", "freefire": "freefire", "mobilegaming": "mobilspel", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "djurövergång", "bgmi": "bgmi", "teamfighttactics": "lagstridstaktik", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "akuthq", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "höstdag", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "skakarochfipplar", "ml": "ml", "bangdream": "bangdream", "clashofclan": "k<PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON>", "timeprincess": "tidsprinsessa", "beatstar": "beatstar", "dragonmanialegend": "drakmanialegenden", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "fickkärlek", "androidgames": "androidspel", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "matlagningstokighet", "dokkan": "dokkan", "aov": "aov", "triviacrack": "trivia<PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "änglarnasliga", "lordsmobile": "lordsmobile", "tinybirdgarden": "minifågelträdgård", "gachalife": "gachaliv", "neuralcloud": "neuralmoln", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "spegeluniversum", "pou": "pou", "warwings": "krigsvingar", "fifamobile": "fifamobil", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobil", "ingress": "ingress", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "myntmästare", "punishinggrayraven": "straffandegråkran", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "sultanspelet", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "springstadsspelen", "juegodemovil": "mobilspel", "avakinlife": "a<PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "mimicry", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "bergochdalbanatycoon", "grandchase": "storslagenträning", "bombmebrasil": "bombamebrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "vägtilingenstans", "sealm": "sealm", "shadowfight3": "skuggstrid3", "limbuscompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "nedrivningsderby3", "wordswithfriends2": "ordmedvänner2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfekthistorie", "showbyrock": "showbyrock", "ladypopular": "damsuccess", "lolmobile": "lolmobil", "harvesttown": "skördefesten", "perfectworldmobile": "perfektvärldmobil", "empiresandpuzzles": "imperierochpussel", "empirespuzzles": "empirespuzzles", "dragoncity": "drakstad", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "littlenightmare", "aethergazer": "aethergazer", "mudrunner": "leraäventyr", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "vapenbundna", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "prisstallet", "zombiecastaways": "zombiekastawayz", "eveechoes": "<PERSON>ek<PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobil", "streetfighterduel": "gatefighterduell", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "tjejerpåfrontlinjen", "jurassicworldalive": "jurassicworldalive", "soulseeker": "själssökare", "gettingoverit": "kommaöverdet", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "bilxdriftningonline", "jogosmobile": "mobilspel", "legendofneverland": "legendenföraldrigland", "pubglite": "pubglite", "gamemobilelegends": "spelmobillegender", "timeraiders": "timeraiders", "gamingmobile": "spelmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "battlekatter", "dnd": "dnd", "quest": "äventyr", "giochidiruolo": "rollspejlspel", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "världenavmörker", "travellerttrpg": "resenärtrpg", "2300ad": "2300<PERSON><PERSON><PERSON><PERSON><PERSON>", "larp": "larp", "romanceclub": "romanceklubb", "d20": "tärning20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "snackaboo", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "poke<PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON>hie", "teamystic": "teammystic", "pokeball": "pokeboll", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "shine<PERSON>ké<PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "povore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmästare", "pokémonsleep": "pokémonsömn", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "glänssökare", "ajedrez": "schack", "catur": "ka<PERSON>r", "xadrez": "schack", "scacchi": "schack", "schaken": "schaken", "skak": "skak", "ajedres": "chess", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "världsnabbt", "jeudéchecs": "jeudéchecs", "japanesechess": "japanskschack", "chinesechess": "kinesisktchess", "chesscanada": "s<PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "s<PERSON><PERSON><PERSON><PERSON>", "openings": "öppningar", "rook": "r<PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "dungeonmästare", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxäventyr", "darksun": "mörksol", "thelegendofvoxmachina": "voxmachinasagan", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmoor": "mörkeskog", "minecraftchampionship": "minecraftmästerskap", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "drömmarsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmoddar", "mcc": "mcc", "candleflame": "ljuslåga", "fru": "fru", "addons": "<PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftfickan", "minecraft360": "minecraft360", "moddedminecraft": "moddadminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftstad", "pcgamer": "pcspelare", "jeuxvideo": "spelkonsol", "gambit": "gambit", "gamers": "spelfantaster", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "spelmobil", "gameover": "spelöver", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "spelaomspel", "pcgames": "pcspel", "casualgaming": "avslappnatspelande", "gamingsetup": "spelsystem", "pcmasterrace": "pcmasterrace", "pcgame": "pcspel", "gamerboy": "gamerkille", "vrgaming": "vrspelande", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "spelgenomgångar", "consoleplayer": "konsolspelare", "boxi": "boxi", "pro": "pro", "epicgamers": "episkagamers", "onlinegaming": "onlinegaming", "semigamer": "semigamer", "gamergirls": "spel<PERSON><PERSON><PERSON><PERSON>", "gamermoms": "gamerföräldrar", "gamerguy": "speldude", "gamewatcher": "spelbevakar<PERSON>", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "lagförsök", "mallugaming": "mallugaming", "pawgers": "pawsar", "quests": "uppdrag", "alax": "alax", "avgn": "avgn", "oldgamer": "gammalgamer", "cozygaming": "<PERSON>sigt<PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "pcspel", "dsswitch": "dsswitch", "competitivegaming": "tävlingsspel", "minecraftnewjersey": "minecraftnyjersey", "faker": "falsk", "pc4gamers": "pc4spelare", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosex<PERSON><PERSON><PERSON>", "gamepc": "speldator", "girlsgamer": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmoddar", "dailyquest": "dagligaäventyret", "gamegirl": "spelatjej", "chicasgamer": "chicasgamer", "gamesetup": "spelsetup", "overpowered": "överkörda", "socialgamer": "socialspelsnack", "gamejam": "spelskapande", "proplayer": "prospelare", "roleplayer": "rollspelare", "myteam": "mittl<PERSON>", "republicofgamers": "republikenförspelare", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "tripllegendar", "gamerbuddies": "gamerkompisar", "butuhcewekgamers": "behöverbrudarspelare", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nördgamer", "afk": "borta", "andregamer": "andregamer", "casualgamer": "avslappnadspelares", "89squad": "89gänget", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "spelspelet", "gamertag": "spelnamn", "lanparty": "lanparty", "videogamer": "spelandesnutte", "wspólnegranie": "kompisarspelar", "mortdog": "mortdog", "playstationgamer": "playstationspelare", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "hälsosamgamer", "gtracing": "gtracing", "notebookgamer": "anteckningsspelare", "protogen": "protogen", "womangamer": "womangamer", "obviouslyimagamer": "uppenbarligenärjagspelare", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "plockare", "humanfallflat": "människafallplatt", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nollutflykt", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonicdahedgehog", "sonic": "sonisk", "fallguys": "fallguyz", "switch": "byta", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mariokartmästare", "wii": "wii", "aceattorney": "aceadvokat", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilivet", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tårarnaförkungariket", "walkingsimulators": "walkingsimulatorer", "nintendogames": "nintendospel", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "drakäventyr", "harvestmoon": "skördemåne", "mariobros": "mario<PERSON>s", "runefactory": "ruenefabrik", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "andningenavdetvilda", "myfriendpedro": "minvänpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spel", "earthbound": "jordbunden", "tales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "djurf<PERSON><PERSON>ing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendosverige", "tloz": "tloz", "trianglestrategy": "trianglestrategi", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kastanjekastardagen", "nintendos": "nintendos", "new3ds": "ny3ds", "donkeykongcountry2": "dkcountry2", "hyrulewarriors": "hyrulekrigare", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioochsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "vildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftse", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendsvild", "adcarry": "adcarry", "lolzinho": "lolzen", "leagueoflegendsespaña": "leagueoflegendsespanien", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "skrattafbanor", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegenderna", "gaminglol": "spelalol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgrind", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideospel", "scaryvideogames": "skräckspel", "videogamemaker": "spelskapare", "megamanzero": "megamanzero", "videogame": "videogame", "videosgame": "videospel", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "översikt", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "trollkarls101", "battleblocktheater": "battleblockteater", "arcades": "spelhallar", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "jordbrukssimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxsverige", "robloxdeutsch": "robloxsvenska", "erlc": "erlc", "sanboxgames": "sandboxspel", "videogamelore": "tvspelsmycket", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "drömvärld", "starcitizen": "stjärnmedborgare", "yanderesimulator": "yanderesimulator", "grandtheftauto": "stöldavbil", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "tvspel", "theoldrepublic": "dengamlarepubliken", "videospiele": "videospel", "touhouproject": "touhouprojekt", "dreamcast": "<PERSON><PERSON><PERSON><PERSON>", "adventuregames": "äventyrsspel", "wolfenstein": "wolfenstein", "actionadventure": "aktionsäventyr", "storyofseasons": "säsongernasberättelse", "retrogames": "retrogames", "retroarcade": "retrokiosk", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "lekdate", "commanderkeen": "kommandokeen", "bugsnax": "bugsnax", "injustice2": "orättvisa2", "shadowthehedgehog": "skugganpåkottåret", "rayman": "rayman", "skygame": "skygame", "zenlife": "zenliv", "beatmaniaiidx": "beatmaniaiidx", "steep": "brant", "mystgames": "mysts<PERSON>", "blockchaingaming": "blockkedjespelande", "medievil": "medieval", "consolegaming": "konsolspelande", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "blommandeångest", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "spel<PERSON><PERSON>r", "monstergirlquest": "monstertjejäventyr", "supergiant": "superjättar", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "bond<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "gamlamacarena", "bethesda": "bethesda", "jackboxgames": "jackboxspel", "interactivefiction": "interaktivfiktion", "pso2ngs": "pso2ngs", "grimfandango": "grimmfandango", "thelastofus2": "thelastofus2", "amantesamentes": "älskarochnörder", "visualnovel": "visualnovell", "visualnovels": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "skugg<PERSON>", "tcrghost": "tcrspöke", "payday": "<PERSON><PERSON><PERSON>", "chatherine": "katrin", "twilightprincess": "skymningsprinsessan", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "äestetiskaspel", "novelavisual": "novelavisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "godhand", "leafblowerrevolution": "blåsmaskinsrevolution", "wiiu": "wiiu", "leveldesign": "nivådesign", "starrail": "stjärnspår", "keyblade": "<PERSON>yckelsv<PERSON>rd", "aplaguetale": "aplaguetal", "fnafsometimes": "fnafsåibland", "novelasvisuales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospel", "videogamedates": "speldatum", "mycandylove": "mincandylove", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "återkomstavdomedagen", "gamstergaming": "gamstergaming", "dayofthetantacle": "dagentantakeln", "maniacmansion": "maniacmansion", "crashracing": "krockracing", "3dplatformers": "3dplattformsspel", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "g<PERSON><PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "berättelsespel", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "bortomtvåsj<PERSON><PERSON>", "gameuse": "spelbruk", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "<PERSON><PERSON><PERSON>", "katanazero": "katananoll", "famicom": "famicom", "aventurasgraficas": "grafiskaäventyr", "quickflash": "snabbklick", "fzero": "fzero", "gachagaming": "gachaspelande", "retroarcades": "retroarkader", "f123": "f123", "wasteland": "ökenland", "powerwashsim": "powerwashsim", "coralisland": "koralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfrukt", "anotherworld": "enannatvärld", "metaquest": "metaquest", "animewarrios2": "animekrigare2", "footballfusion": "fotbollsfusion", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "vridenmetall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "skamstapeln", "simulator": "simulator", "symulatory": "symule<PERSON>ar", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "samochmax", "grywideo": "grytvideo", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "pojkvänsdeponi", "toontownrewritten": "toontownomarbetad", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "stadkaos", "heavenlybodies": "heavenlykroppar", "seum": "seum", "partyvideogames": "festvideospel", "graveyardkeeper": "gravvaktare", "spaceflightsimulator": "rymdflygssimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hack<PERSON><PERSON><PERSON><PERSON>ss", "foodandvideogames": "matochvideospel", "oyunvideoları": "spelaovideo", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "lastbilsimulator", "horizonworlds": "horizonvärldar", "handygame": "smidigtspel", "leyendasyvideojuegos": "legendernasvideospel", "oldschoolvideogames": "gammalskolatvspel", "racingsimulator": "racingsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentermayhem", "songpop": "sjungpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "rebellstjärna", "indievideogaming": "indiespelgaming", "indiegaming": "indiegaming", "indievideogames": "indievideospel", "indievideogame": "indiespel", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomnia", "bufffortress": "bufffortress", "unbeatable": "oslagbar", "projectl": "projektl", "futureclubgames": "framtidsklubbspel", "mugman": "<PERSON><PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestespel", "aperturescience": "öppningsteknik", "backlog": "backlog", "gamebacklog": "spelsamling", "gamingbacklog": "gamingeftersläpning", "personnagejeuxvidéos": "spelfigurer", "achievementhunter": "måljägare", "cityskylines": "stadsprofiler", "supermonkeyball": "superapaoboll", "deponia": "deponia", "naughtydog": "busighund", "beastlord": "odjursfurste", "juegosretro": "retrospel", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriockersvartsogen", "alanwake": "alanwake", "stanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "spelsoundtrack", "dragonsync": "draksynk", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "älskarkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "sorgliganime", "darkerthanblack": "mörkareänsvart", "animescaling": "animeskalning", "animewithplot": "animemedhandling", "pesci": "<PERSON><PERSON>", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "mörkherre", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "mästarpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonessäsong1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "animecover", "thevisionofescaflowne": "visionenavescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90tal", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananfisk", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toalettbundnahanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "eldstyrka", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "framtidsdagbok", "fairytail": "fairytail", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "skapadienavg<PERSON>den", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasingenkarta", "mermaidmelody": "sjöjungfrusång", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "skräckmanga", "romancemangas": "romance<PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "äggdrake", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "skamkungen", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "ensäkermagiskindex", "sao": "sao", "blackclover": "svartklöver", "tokyoghoul": "tokyoghoul", "onepunchman": "enbox<PERSON>man", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8infinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "svärdskonstonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wunderäggprioritet", "angelsofdeath": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosimisk", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "änglaslagsmål", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "killenochbeastet", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "gudstornet", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "hurmanhållermammaglad", "fullmoonwosagashite": "fullmoonwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "sötaochläskiga", "martialpeak": "martialpeak", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "highscoregirl", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revansvans", "shinji": "shinji", "zerotwo": "nolltvå", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "seglarnasaturn", "dio": "dio", "sailorpluto": "sjöfararpluto", "aloy": "aloy", "runa": "runa", "oldanime": "gammaltanime", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "spelet", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "s<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "klippavbehållning", "loli": "loli", "horroranime": "skr<PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "fruktkorg", "devilmancrybaby": "djävulskamannakrabbel", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "älskavivet", "sakuracardcaptor": "sakurakortfångare", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "dethägrandenågot", "monstermanga": "monstermanga", "yourlieinapril": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggydaskloven", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "djuphavsfången", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bannafisk", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON>", "cardcaptorsakura": "kortfångaresakura", "stolas": "stolas", "devilsline": "djävulsstigen", "toyoureternity": "tilldinåterfödelse", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blåperiod", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "hemligallians", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "utsu<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detektivconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampyrknight", "mugi": "mugi", "blueexorcist": "blåexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "skådat", "spyfamily": "spionfamilj", "airgear": "luftutrustning", "magicalgirl": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "de7dödligasynder", "prisonschool": "fängelseskola", "thegodofhighschool": "gudenförhighschool", "kissxsis": "kyssxsystrar", "grandblue": "grandblue", "mydressupdarling": "minklädeskompis", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniversum", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "förstasteget", "undeadunluck": "odödligaotur", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromans", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinska", "lolicon": "lolikon", "demonslayertothesword": "demonjägaretillsvärdet", "bloodlad": "blodko<PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "eldpunch", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stjärnornaåterförenas", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "k<PERSON>rsbärsmagi", "housekinokuni": "husk<PERSON><PERSON><PERSON>", "recordragnarok": "spelaragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "gymnasietfördedöda", "germantechno": "tyskteknos", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "mördarklassrummet", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "deathparade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskanime", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animesynk", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "u<PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashk<PERSON>a", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "mangasjärtat", "deliciousindungeon": "läckertidungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokinspelning", "funamusea": "roligtmuseum", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialernaärförsvåra", "overgeared": "överutrustad", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemester", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "dropparavgud", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "omvändaharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "storslaharenonizuka", "gridman": "ruteman", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "utrustning5", "grandbluedreaming": "stordjupdrömmande", "bloodplus": "bloodplus", "bloodplusanime": "blodplussanime", "bloodcanime": "blodanime", "bloodc": "blodc", "talesofdemonsandgods": "berättelseromdemonerochgudar", "goreanime": "goreanime", "animegirls": "animeflickor", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kråkorxvärst", "splatteranime": "sprutandeanime", "splatter": "spruta", "risingoftheshieldhero": "sköldhjältensuppvaknande", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeesverige", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON>j<PERSON><PERSON><PERSON>", "liarliar": "ljugljug", "supercampeones": "supercampeoner", "animeidols": "animeikoner", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "nattensanrop", "bakuganbrawler": "bak<PERSON><PERSON>bat<PERSON>", "bakuganbrawlers": "bakuganbr<PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "skugghäng", "tsubasachronicle": "tsubasachronicle", "findermanga": "hittamanga", "princessjellyfish": "prinses<PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradiskyss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestjärnljus", "animeverse": "animevärlden", "persocoms": "persocoms", "omniscientreadersview": "allvetandelesare", "animecat": "animekatt", "animerecommendations": "animerelationer", "openinganime": "öppninganime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minungdomromantiskakomedi", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "gigantiskarobotar", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilkrigareggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilsuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "bleka", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojossjukaäventyr", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "isiga", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "milit<PERSON><PERSON><PERSON>", "greenranger": "grönjaguar", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "stjärnfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupin3rd", "animecity": "animebyn", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonäventyr", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "<PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "minhjälteakademi", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachispel", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "enkätkåren", "onepieceanime": "onepieceanime", "attaquedestitans": "attackdestornen", "theonepieceisreal": "denäradelenärverklig", "revengers": "<PERSON>ä<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "glädjesnubbeeffekt", "digimonstory": "digimonberättelse", "digimontamers": "digimontamers", "superjail": "superfängelse", "metalocalypse": "metalokalyps", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "felf<PERSON><PERSON><PERSON><PERSON>", "kemonofriends": "kemonovänner", "utanoprincesama": "utanoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "var<PERSON>gsliv", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "flygandehäxa", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "livsnarrativ"}