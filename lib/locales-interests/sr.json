{"2048": "2048", "mbti": "mbti", "enneagram": "enegram", "astrology": "astrologija", "cognitivefunctions": "kognitivnefunkcije", "psychology": "psihologija", "philosophy": "filozofija", "history": "istorija", "physics": "fizika", "science": "nauka", "culture": "kultura", "languages": "jez<PERSON>", "technology": "tehnologija", "memes": "memeovi", "mbtimemes": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "astrologijamemeovi", "enneagrammemes": "enegramme<PERSON><PERSON><PERSON>", "showerthoughts": "mis<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "s<PERSON>š<PERSON>", "videos": "snimci", "gadgets": "gedžeti", "politics": "politika", "relationshipadvice": "savetveze", "lifeadvice": "životnisavet", "crypto": "kripto", "news": "novosti", "worldnews": "<PERSON>vet<PERSON><PERSON><PERSON>", "archaeology": "arheologija", "learning": "učenje", "debates": "rasprave", "conspiracytheories": "teorijazavere", "universe": "univerzum", "meditation": "meditacija", "mythology": "mitologija", "art": "umetnost", "crafts": "zana<PERSON>", "dance": "ples", "design": "<PERSON><PERSON><PERSON>", "makeup": "šminka", "beauty": "lepota", "fashion": "moda", "singing": "pevanje", "writing": "pisanje", "photography": "fotografija", "cosplay": "kozplej", "painting": "slikanje", "drawing": "crtanje", "books": "knjige", "movies": "filmovi", "poetry": "poezija", "television": "televizija", "filmmaking": "snimanjefilma", "animation": "animacija", "anime": "anime", "scifi": "scifi", "fantasy": "fantazija", "documentaries": "dokumentarci", "mystery": "<PERSON><PERSON><PERSON>", "comedy": "komedija", "crime": "krimi", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horor", "romance": "romansa", "realitytv": "rijalititv", "action": "ak<PERSON><PERSON>", "music": "muzika", "blues": "blues", "classical": "klasično", "country": "country", "desi": "desi", "edm": "edm", "electronic": "electronic", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rep", "reggae": "reggae", "rock": "rock", "techno": "tehno", "travel": "putova<PERSON>je", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "festival", "museums": "muzeji", "standup": "standup", "theater": "poz<PERSON>š<PERSON>", "outdoors": "naotvorenom", "gardening": "baštovanstvo", "partying": "zabavljatise", "gaming": "gaming", "boardgames": "društveneigre", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ah", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "hrana", "baking": "pečenje", "cooking": "kuvanje", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "ptice", "cats": "<PERSON><PERSON><PERSON>", "dogs": "psi", "fish": "riba", "animals": "životinje", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ekologija", "feminism": "feminizam", "humanrights": "<PERSON>juds<PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqally", "stopasianhate": "stopmržnjiazijata", "transally": "transally", "volunteering": "voluntiranje", "sports": "sportovi", "badminton": "badminton", "baseball": "bejzbol", "basketball": "<PERSON><PERSON><PERSON><PERSON>", "boxing": "boks", "cricket": "kriket", "cycling": "bicik<PERSON><PERSON>", "fitness": "fitnes", "football": "fudbal", "golf": "golf", "gym": "teretana", "gymnastics": "gimnast<PERSON>", "hockey": "hokej", "martialarts": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "stonite<PERSON>", "running": "trčanje", "skateboarding": "skejtbord", "skiing": "<PERSON><PERSON><PERSON>", "snowboarding": "snowboarding", "surfing": "surfovanje", "swimming": "plivanje", "tennis": "tenis", "volleyball": "od<PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "joga", "scubadiving": "ronjenje", "hiking": "planinarenje", "capricorn": "jarac", "aquarius": "vodolija", "pisces": "ribe", "aries": "ovan", "taurus": "bik", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "rak", "leo": "lav", "virgo": "de<PERSON><PERSON>", "libra": "vaga", "scorpio": "škorpija", "sagittarius": "strelac", "shortterm": "kratkeveze", "casual": "ležern<PERSON>", "longtermrelationship": "<PERSON><PERSON><PERSON>", "single": "sama", "polyamory": "poliamor", "enm": "nenormativnaveza", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gej", "lesbian": "<PERSON><PERSON><PERSON>jk<PERSON>", "bisexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "aseksualan", "reddeadredemption2": "crvenazadovoljstvobroj2", "dragonage": "zmajeviage", "assassinscreed": "ubistvokode", "saintsrow": "sve<PERSON>ulice", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "čuvajrke", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kraljevskizadatak", "soulreaver": "<PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverzija", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "sibir<PERSON>", "rdr2": "rdr2", "spyrothedragon": "špajrotdragona", "dragonsdogma": "zmajevapdogma", "sunsetoverdrive": "zalazakdrajv", "arkham": "arkham", "deusex": "b<PERSON>žijaforč<PERSON>", "fireemblemfates": "fireemblemfates", "yokaiwatch": "j<PERSON><PERSON><PERSON><PERSON>", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloainfinite", "guildwars": "gilde<PERSON><PERSON>_ratovi", "openworld": "o<PERSON><PERSON>eni<PERSON>vet", "heroesofthestorm": "junaciolujevanja", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "zarobljeničkeavanture", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "planinskaavantura", "lordsoftherealm2": "gospodariizdomenap2", "baldursgate": "baldurskapi<PERSON>", "colorvore": "kolorvore", "medabots": "medaboti", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "imercivnisims", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "<PERSON><PERSON><PERSON><PERSON>", "dishonored": "nepočastvovan", "eldenring": "elden<PERSON>", "darksouls": "mračneдуше", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "veštac3", "fallout": "<PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "staričarobnjaci", "modding": "modovanje", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "uronjivo", "falloutnewvegas": "falloutnewvegas", "bioshock": "b<PERSON><PERSON><PERSON>", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "disidija", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "mračnainspiracija", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "zavedenljubavlju", "otomegames": "otomei<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaod<PERSON>mena", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampирскаmaskarada", "dimension20": "dimenzija20", "gaslands": "gasilandi", "pathfinder": "putokaz", "pathfinder2ndedition": "istrazivač2izdanje", "shadowrun": "senkaotrčanja", "bloodontheclocktower": "krvnaputusatnice", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitacijskapropast", "rpg": "rpg", "dota2": "dota2", "xenoblade": "zenoblade", "oneshot": "jedanpot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord", "yourturntodie": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "starivite<PERSON><PERSON><PERSON><PERSON>", "reka": "reka", "honkai": "honkai", "marauders": "marauderi", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "mor<PERSON><PERSON>", "starwarskotor": "ratovizvezdakotor", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "zaklonod<PERSON><PERSON>š<PERSON>", "gurps": "gurps", "darkestdungeon": "najmračnijatamnica", "eclipsephase": "eclipsefazа", "disgaea": "disgaea", "outerworlds": "spoljnisvetovi", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "vezivanjeisaaca", "diabloimmortal": "diabloimortal", "dynastywarriors": "dinastijskevojne", "skullgirls": "lobanjedevojke", "nightcity": "noćnagrad", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madneszborba", "jaggedalliance2": "zaglaiduzaviđenja2", "neverwinter": "neverwinter", "road96": "putna96", "vtmb": "vtmb", "chimeraland": "čimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikeigre", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "zaboravljenisvetovi", "dragonlance": "zmajskaštit", "arenaofvalor": "arenavrednosti", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "deteluce", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonsvet", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ekopank", "vermintide2": "verminotida2", "xeno": "kseno", "vulcanverse": "vulkanverz", "fracturedthrones": "naprsnutaprijestolja", "horizonforbiddenwest": "horizontzabranjenozapad", "twewy": "twewy", "shadowpunk": "senč<PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltazeleno", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON><PERSON>", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "tragačzvezda", "goldensun": "zlatnisunce", "divinityoriginalsin": "b<PERSON><PERSON><PERSON><PERSON><PERSON>nalnig<PERSON><PERSON>", "bladesinthedark": "oštriceusmračenju", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "sajberpank", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkcrveno", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "palaordija", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlezemlje", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božanstvo", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "starebabežaljanjem", "adventurequest": "avantura<PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "pričeosimfonije", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "raskidanograd", "myfarog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "sacredunderworld", "chainedechoes": "lančanaekočina", "darksoul": "darksoul", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "drugodica", "mountandblade": "montiikablade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "hronokontroler", "pillarsofeternity": "stubovietern<PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "podela", "hellocharlotte": "zdravočarlote", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladehronike2", "vampirolamascarada": "vampirolamaskarada", "octopathtraveler": "oktaputnik", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorčeznja", "fable3": "fable3", "fablethelostchapter": "bajkagubljenaepizoda", "hiveswap": "hiveswap", "rollenspiel": "rolnaigra", "harpg": "harpg", "baldursgates": "<PERSON>urs<PERSON><PERSON><PERSON>", "edeneternal": "jednodavanвечни", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "zvezdanopolje", "oldschoolrevival": "<PERSON><PERSON>š<PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworlds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kraljevstvosrce1", "ff9": "ff9", "kingdomheart2": "kraljevstvosrce2", "darknessdungeon": "tamnicezla", "juegosrpg": "rpgigre", "kingdomhearts": "kraljevskazvijezda", "kingdomheart3": "kraljevstvosrca3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "divljisrca", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "nebaarkadije", "shadowhearts": "senčne_srca", "nierreplicant": "nierreplikant", "gnosia": "gnosia", "pennyblood": "kovanicakrvi", "breathoffire4": "dahvatre4", "mother3": "mama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "igreuloge", "roleplaygame": "igralica", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON>ra", "pathfinderrpg": "putnikigrica", "pathfinder2e": "putokaz2e", "vampirilamasquerade": "vampirskamasquerada", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "zmajevaavek", "chronocross": "hronok<PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "lovacmonstarasvet<PERSON>", "bg3": "bg3", "xenogear": "ksenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "senčneionice", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "kraljevstvoje_došlo", "awplanet": "awplanet", "theworldendswithyou": "svetsezavršavazatumom", "dragalialost": "dragalialost", "elderscroll": "starijiskrol", "dyinglight2": "umiranjesvetlost2", "finalfantasytactics": "finalfantasytaktika", "grandia": "grandia", "darkheresy": "mra<PERSON><PERSON><PERSON>lu<PERSON>", "shoptitans": "š<PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "zemaljskamagija", "blackbook": "crnabuka", "skychildrenoflight": "nebonebeske_dece", "gryrpg": "gryrpg", "sacredgoldedition": "svetosvetaizdanja", "castlecrashers": "kraljevskipadobrani", "gothicgame": "gotskaigra", "scarletnexus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostwiretokyo": "duhovnavezatokyo", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "zvezdakesa", "cityofmist": "gradma<PERSON>", "indierpg": "indierpg", "pointandclick": "pokaziiklikni", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON>", "freeside": "slobodnastrana", "epic7": "epic7", "ff7evercrisis": "ff7zauvekkriza", "xenogears": "k<PERSON>og<PERSON>s", "megamitensei": "megamitensei", "symbaroum": "simbarum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "sm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "vitezjdr", "monsterhunter": "lovačamonstara", "fireemblem": "vat<PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacija", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "lovcimonstarauskoku", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "j<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarneigre", "tacticalrpg": "taktičkirpg", "mahoyo": "mahojo", "animegames": "animeigre", "damganronpa": "дамганронпа", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "večnasonata", "princessconnect": "princezakanect", "hexenzirkel": "vešticekrug", "cristales": "krist<PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valoranter", "valorantindian": "valorantindijan", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efoobal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligasnova", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON>", "efootball": "efoobol", "dreamhack": "dreamhack", "gaimin": "gejmer", "overwatchleague": "overwatchliga", "cybersport": "ciberport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brazilskasvodka", "valorantcompetitive": "valoranttakmičenje", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "polu<PERSON><PERSON>", "left4dead": "ostavljenzadaed", "left4dead2": "ostavljen4mrtav2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "večnoleto", "goatsimulator": "kozesimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "slobodanplanet", "transformice": "transformice", "justshapesandbeats": "samooblic<PERSON>i", "battlefield4": "bojnopolje4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "polovinaživot2", "hacknslash": "hacknslash", "deeprockgalactic": "deprockgalactic", "riskofrain2": "rizikoddesa2", "metroidvanias": "metroidvanije", "overcooked": "prekuvano", "interplanetary": "interplanetarni", "helltaker": "helltaker", "inscryption": "inskripcija", "7d2d": "7d2d", "deadcells": "mrtvećelije", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "lisica", "stray": "lutalica", "battlefield": "bojnopolje", "battlefield1": "bataljones1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oko", "blackdesert": "crnopesak", "tabletopsimulator": "simu<PERSON><PERSON><PERSON><PERSON>", "partyhard": "žuristapuno", "hardspaceshipbreaker": "teškiprostorstapagaitelja", "hades": "hades", "gunsmith": "kovačoružja", "okami": "<PERSON>ami", "trappedwithjester": "zarobljenusdžesterom", "dinkum": "dinkum", "predecessor": "predecessor", "rainworld": "kišnoljubav", "cavesofqud": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON>", "minionmasters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grimdawn": "grimsmrak", "darkanddarker": "mračnijeidarkije", "motox": "motox", "blackmesa": "crnammesa", "soulworker": "duhworker", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "jaga", "cubeescape": "kubbeizlaz", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novigrad", "citiesskylines": "gradskeprizore", "defconheavy": "defconheavy", "kenopsia": "kenopsija", "virtualkenopsia": "virtuelnakénopsija", "snowrunner": "snežnitrka", "libraryofruina": "bibliotekaruinacije", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrajkeri", "wayfinder": "putokaz", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "smirenirazvodnjak", "battlebit": "bitkabattles", "ultimatechickenhorse": "ultimativazecakoko", "dialtown": "dialtown", "smileforme": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermesnjak", "tinnybunny": "tinji_zec", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "doom", "callofduty": "pozivdužnosti", "callofdutyww2": "pozivdužnostiww2", "rainbow6": "dugini6", "apexlegends": "apexlegendse", "cod": "kode", "borderlands": "graničnezemlje", "pubg": "pubg", "callofdutyzombies": "poziv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "vrh", "r6siege": "r6ops", "megamanx": "megamanx", "touhou": "тоухоу", "farcry": "dalekicry", "farcrygames": "farcryigrice", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "zemaljskaodbrambenasnaga", "huntshowdown": "lovnaskup<PERSON><PERSON>", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "ratovi", "sierra117": "sierra117", "dayzstandalone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultrakill": "ultrakill", "joinsquad": "pridružitim", "echovr": "echovr", "discoelysium": "diskolezium", "insurgencysandstorm": "p<PERSON><PERSON>ćaj<PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemajami", "maxpayne": "<PERSON><PERSON><PERSON><PERSON>", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "smrtnopovezivanje", "b4b": "b4b", "codwarzone": "kodratnogzona", "callofdutywarzone": "callofdutywarzone", "codzombies": "kodzombija", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divizije2", "killzone": "ubilačkazona", "helghan": "he<PERSON><PERSON>", "coldwarzombies": "hladnazombijada", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON>v<PERSON>mlad<PERSON>", "crosscode": "crosscode", "goldeneye007": "zlatnooko007", "blackops2": "crneoperacije2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernaratovanja", "neonabyss": "neonaspektrum", "planetside2": "planetside2", "mechwarrior": "mechratnik", "boarderlands": "graničnezemlje", "owerwatch": "overwatch", "rtype": "rtip", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalsluz", "primalcarnage": "primalcarnage", "worldofwarships": "svetbrodskihratova", "back4blood": "back4blood", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "ubica", "masseffect": "masseffect", "systemshock": "siste<PERSON>šok", "valkyriachronicles": "valkyrijanehronike", "specopstheline": "specopstheline", "killingfloor2": "ubistvenopodrum2", "cavestory": "pećinskapriča", "doometernal": "doometernal", "centuryageofashes": "vijekpepelа", "farcry4": "farcry4", "gearsofwar": "<PERSON>remazaud<PERSON>", "mwo": "mwo", "division2": "divizija2", "tythetasmaniantiger": "tytasmanijskitigar", "generationzero": "<PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "uđiuor<PERSON>ž<PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernaferabattle2", "blackops1": "crnoprojekt1", "sausageman": "čoveksašalšom", "ratchetandclank": "ratchetandclank", "chexquest": "čeksavantura", "thephantompain": "duhovnabolešćina", "warface": "ratnolice", "crossfire": "<PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomskizalazak", "blackops3": "crneteam3", "vampiresurvivors": "vampirs<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "sloboda", "battlegrounds": "bojnapolja", "frag": "frag", "tinytina": "malitina", "gamepubg": "igrajpubg", "necromunda": "nekrumunda", "metalgearsonsoflibert": "metalgearsinovaaplibert", "juegosfps": "fpsigra", "convertstrike": "konversionstrike", "warzone2": "ratnodzona2", "shatterline": "shatterline", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "krvavakarambol", "republiccommando": "republikačeta", "elitedangerous": "elitenopasnost", "soldat": "soldat", "groundbranch": "grindgrana", "squad": "ekipa", "destiny1": "sudbina1", "gamingfps": "gejmingfps", "redfall": "crvenapada", "pubggirl": "pubgdevo<PERSON><PERSON>", "worldoftanksblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "zovdužnosti_crneoperacije", "enlisted": "prijavljen", "farlight": "dalekoсветло", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "ojačanojjezgra", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "malitininsveta", "halo2": "halo2", "payday2": "isplata2", "cs16": "cs16", "pubgindonesia": "pubgindonezija", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sapunskikod", "ghostcod": "duhcod", "csplay": "csp<PERSON>j", "unrealtournament": "nepravednotakmičenje", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "graničnezemlje2", "counterstrike": "protivnapad", "cs2": "cs2", "pistolwhip": "pištoljoprivijanje", "callofdutymw2": "pozivdužnostimw2", "quakechampions": "zemljotresšampioni", "halo3": "halo3", "halo": "halo", "killingfloor": "ubijanjeplana", "destiny2": "destiny2", "exoprimal": "eksoprimal", "splintercell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neonwhite": "neonbelo", "remnant": "o<PERSON><PERSON>", "azurelane": "azuravenija", "worldofwar": "<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "povratak", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "senčančovek", "quake2": "zemljotres2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "crvenazivota", "standoff2": "standoff2", "harekat": "harak<PERSON>", "battlefield3": "bojište3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "morepi<PERSON>", "rust": "rust", "conqueronline": "osvojionline", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "brod<PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>va", "warthunder": "<PERSON><PERSON><PERSON>č<PERSON>", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "legendeiztr<PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON>", "phantasystaronline2": "fantazijasvetonline2", "maidenless": "bezdevojke", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "precrtaj", "agario": "agario", "secondlife": "drugalife", "aion": "aion", "toweroffantasy": "kulaizmaštanja", "netplay": "mrežnigaming", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superživotinjskakraljevka", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "vitezonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "vezanjesaizakom", "dragonageinquisition": "zmajageinkvizicija", "codevein": "kodneven", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "vakfu", "scum": "s<PERSON><PERSON>e", "newworld": "novisvet", "blackdesertonline": "crnaboravonline", "multiplayer": "višekorisnički", "pirate101": "pirat101", "honorofkings": "častkraljeva", "fivem": "fivem", "starwarsbattlefront": "ratoviuzvezdamabitkanafrontu", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "ratovistarace2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dćaskanje", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasik", "worldofwarcraft": "<PERSON><PERSON><PERSON><PERSON>", "warcraft": "rat<PERSON>brt", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "pep<PERSON>dstvar<PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "svilenskiput", "spiralknights": "spiralknightс", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "zmajevprofit", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "anje<PERSON>uonline", "lunia": "lunija", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsstararepublika", "grandfantasia": "velikafantazija", "blueprotocol": "plavip<PERSON><PERSON><PERSON>", "perfectworld": "sav<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "uzletimoonline", "corepunk": "corepunk", "adventurequestworlds": "avanturazabavnogsveta", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "životinjskajam", "kingdomofloathing": "kraljevstvočuđenja", "cityofheroes": "grad<PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "boracuzulice", "hollowknight": "praznivenac", "metalgearsolid": "metalgearsolid", "forhonor": "začast", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "ksenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtufajter", "streetsofrage": "uliceparage", "mkdeadlyalliance": "mkubijalsavez", "nomoreheroes": "krajherojima", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "kraljboraca", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retroborbeneigre", "blasphemous": "blasfemično", "rivalsofaether": "rivaliizvesni", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmaš", "mugen": "mugen", "warofthemonsters": "ratprotivmonstruma", "jogosdeluta": "igra<PERSON>ke", "cyberbots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "oklopniratnici", "finalfight": "poslednjeborba", "poweredgear": "moćnaprek<PERSON><PERSON>i", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "borbeneigre", "killerinstinct": "ubilačkiinstinkt", "kingoffigthers": "kraljboraca", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "vitezstvo2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "krivogearstrive", "hollowknightsequel": "nastavakhollowknight", "hollowknightsilksong": "praznivitezsvilenaznaška", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongvesti", "silksong": "svilenpevac", "undernight": "undernight", "typelumina": "tipav<PERSON>ina", "evolutiontournament": "evolucijaturnir", "evomoment": "evomoment", "lollipopchainsaw": "bombonjerepe", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "pričefromberserije", "bloodborne": "krvoproliće", "horizon": "horizont", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "<PERSON><PERSON>a", "crashbandicoot": "krašbandikut", "bloodbourne": "krvavaputanja", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spajro", "playstationplus": "playstationplus", "lastofus": "pos<PERSON><PERSON><PERSON>", "infamous": "s<PERSON>ot<PERSON>", "playstationbuddies": "playstationdrugari", "ps1": "ps1", "oddworld": "čudnovatiсвет", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "zabijte", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "bogorata", "gris": "griz", "trove": "blago", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "bičevanje", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "dozoredozore", "touristtrophy": "turističkikup", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "petpd", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "đavolovplač3", "devilmaycry5": "đavolmoždaplače5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samuraijunaci", "psvr2": "psvr2", "thelastguardian": "poslednjegardijan", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "lovnapatnju", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "senčnesrce2savez", "pcsx2": "pcsx2", "lastguardian": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gejmpas", "armello": "armello", "partyanimal": "žuralica", "warharmmer40k": "ratnihammer40k", "fightnightchampion": "borbenocablechampion", "psychonauts": "psihonauti", "mhw": "mhw", "princeofpersia": "princperzije", "theelderscrollsskyrim": "starivezivotiskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "starievideigre", "gxbox": "gxbox", "battlefront": "bataljnalinija", "dontstarvetogether": "neglad<PERSON>j<PERSON><PERSON><PERSON>dn<PERSON>", "ori": "ori", "spelunky": "spelučkanje", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "zvezdasto", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "preprav<PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "američkialisaboo", "xboxs": "xboxs", "xboxseriesx": "xboxserijax", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakraljevstava", "fable2": "bajka2", "xboxgamepass": "xboxgamepass", "undertale": "undertalekrew", "trashtv": "trashtv", "skycotl": "nebo<PERSON>", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projekatzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "kulturnagoveda", "duckgame": "<PERSON><PERSON><PERSON>", "thestanleyparable": "stanlejevapriča", "towerunite": "kulaunite", "occulto": "okultno", "longdrive": "dugavožnja", "satisfactory": "zadovoljstvo", "pluviophile": "pluviophile", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geometrijskaoscilacija", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "кенши", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "tam<PERSON><PERSON>", "pizzatower": "<PERSON><PERSON><PERSON>", "indiegame": "indieigra", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "igra", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolina", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "dvorišneigre", "pickanumber": "izaberibroj", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "pivopong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "udob<PERSON>ig<PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "slobodnaigra", "drinkinggames": "igranapijenje", "sodoku": "sudoku", "juegos": "igre", "mahjong": "mahjong", "jeux": "igrice", "simulationgames": "simulacioneigre", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "ig<PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "igricerečinjenje", "letsplayagame": "ajdemodaigramoigrice", "boredgames": "dosadneigre", "oyun": "igra", "interactivegames": "interaktivneigre", "amtgard": "amtgard", "staringcontests": "takmicenjapogledom", "spiele": "<PERSON><PERSON><PERSON>", "giochi": "igre", "geoguessr": "geoguessr", "iphonegames": "iphoneigre", "boogames": "boogames", "cranegame": "kranigra", "hideandseek": "skrivalice", "hopscotch": "čikanje", "arcadegames": "arkadneigre", "yakuzagames": "jakuzagames", "classicgame": "klasičnagdansk", "mindgames": "igrekalkulator", "guessthelyric": "pogodit<PERSON><PERSON>", "galagames": "galagames", "romancegame": "romantičnaigra", "yanderegames": "jandereigrice", "tonguetwisters": "jez<PERSON><PERSON><PERSON>", "4xgames": "4xigre", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "tabletopigre", "metroidvania": "metroidvania", "games90": "igre90", "idareyou": "izazivamte", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "trkeuspesim", "ets2": "ets2", "realvsfake": "pravoizlaženo", "playgames": "igrajigre", "gameonline": "igraonline", "onlinegames": "onlineigre", "jogosonline": "igrajmoonline", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>var<PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "saradnič<PERSON>ig<PERSON>", "jenga": "jenga", "wiigames": "wiigames", "highscore": "visokrezultat", "jeuxderôles": "igreujloga", "burgergames": "burgerigre", "kidsgames": "dečijeig<PERSON>", "skeeball": "skibol", "nfsmwblackedition": "nfsmwcrnaredicija", "jeuconcour": "igrajkonkurs", "tcgplayer": "tcgplayer", "juegodepreguntas": "igrapitanja", "gioco": "igra", "managementgame": "menadžerskagames", "hiddenobjectgame": "igralicašunja", "roolipelit": "rolipelit", "formula1game": "formula1igra", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drvož<PERSON>", "juegosarcade": "arcadnaigra", "memorygames": "igrepamćenja", "vulkan": "vulkan", "actiongames": "akcioneigre", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "flipermašine", "oldgames": "stareigre", "couchcoop": "kaučkooperacija", "perguntados": "<PERSON><PERSON><PERSON>", "gameo": "<PERSON><PERSON><PERSON><PERSON>", "lasergame": "lazergame", "imessagegames": "imessageigre", "idlegames": "igraonice", "fillintheblank": "popuniprazno", "jeuxpc": "<PERSON>grezap<PERSON>", "rétrogaming": "retroigra", "logicgames": "logičkeigre", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "metropljanje", "jeuxdecelebrite": "igrepoznatih", "exitgames": "izlazneigre", "5vs5": "5na5", "rolgame": "roligra", "dashiegames": "dashiegames", "gameandkill": "igrajidokubijes", "traditionalgames": "tradicionalneigre", "kniffel": "kniffel", "gamefps": "igricefps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafove", "fantacalcio": "fantakalčo", "retrospel": "retrospila", "thiefgame": "igrepljačka", "lawngames": "igrice_na_travi", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stolni_fudbal", "tischfußball": "ston<PERSON><PERSON><PERSON><PERSON>", "spieleabende": "večeri<PERSON><PERSON>", "jeuxforum": "igraforumi", "casualgames": "opušteniigre", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "lopovskeigre", "cranegames": "craneigre", "játék": "igre", "bordfodbold": "bordfudbal", "jogosorte": "igrajzajedno", "mage": "mage", "cargames": "igricezaautomobile", "onlineplay": "onlineigra", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "igranje<PERSON><PERSON>", "pursebingos": "torbicebingo", "randomizer": "randomizator", "msx": "msx", "anagrammi": "anagrami", "gamespc": "igrepc", "socialdeductiongames": "društvenepisanjeigara", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrijskeigre", "goodoldgames": "dobrivelikeigre", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON>v<PERSON><PERSON><PERSON>", "jeuxvirtuel": "virtuelneigre", "romhack": "romhak", "f2pgamer": "f2pgamer", "free2play": "slobodno2igraj", "fantasygame": "fantazijskagrada", "gryonline": "gryonline", "driftgame": "driftigra", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvserijeigru", "mushroomoasis": "gljivskiraj", "anythingwithanengine": "biloštosmotora", "everywheregame": "svugdeigra", "swordandsorcery": "ma<PERSON>evii<PERSON><PERSON>", "goodgamegiving": "dobragamepoklanjanje", "jugamos": "igramo", "lab8games": "lab8igrice", "labzerogames": "labzerogames", "grykomputerowe": "igricezaškolu", "virgogami": "virgogami", "gogame": "igrajigrice", "jeuxderythmes": "igrerit<PERSON>", "minaturegames": "minijaturneigre", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "ljubavpremaigri", "gamemodding": "gamemodovanje", "crimegames": "igriceooblasti", "dobbelspellen": "dobbelspelen", "spelletjes": "igre", "spacenerf": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "ka<PERSON><PERSON>i", "singleplayer": "singleplayer", "coopgame": "coopigre", "gamed": "<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "glavnaigra", "kingdiscord": "kraljdiscorda", "scrabble": "skrabble", "schach": "<PERSON>ah", "shogi": "шоги", "dandd": "dandd", "catan": "katana", "ludo": "ludo", "backgammon": "tavla", "onitama": "onitama", "pandemiclegacy": "nasleđepandemije", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolijagame", "brettspiele": "društvenekarte", "bordspellen": "d<PERSON><PERSON><PERSON><PERSON>eklase", "boardgame": "društveneigre", "sällskapspel": "društveneigre", "planszowe": "<PERSON><PERSON><PERSON><PERSON>", "risiko": "riziko", "permainanpapan": "društveneklub", "zombicide": "zombicid", "tabletop": "s<PERSON>zaigra<PERSON><PERSON>", "baduk": "<PERSON><PERSON><PERSON>", "bloodbowl": "krvotrc", "cluedo": "kluedo", "xiangqi": "сјенела", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "povezidva", "heroquest": "herojskizadatak", "giochidatavolo": "igrajuz<PERSON>", "farkle": "farkle", "carrom": "karambol", "tablegames": "društveneigre", "dicegames": "igreod<PERSON><PERSON><PERSON>", "yatzy": "j<PERSON><PERSON>", "parchis": "<PERSON><PERSON><PERSON><PERSON>", "jogodetabuleiro": "igrajmozajedno", "jocuridesocietate": "društvenepartije", "deskgames": "stočneig<PERSON>", "alpharius": "<PERSON><PERSON><PERSON><PERSON>", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelkrizniprotokol", "cosmicencounter": "kosmičkoiskustvo", "creationludique": "kreativnapustolovina", "tabletoproleplay": "stolneigre", "cardboardgames": "kartonskeigre", "eldritchhorror": "el<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "igricezaprekidače", "infinitythegame": "neograničenigra", "kingdomdeath": "kraljevskadevstvom", "yahtzee": "j<PERSON><PERSON>i", "chutesandladders": "kuleiiplitice", "társas": "društvo", "juegodemesa": "igrajstolneigre", "planszówki": "igrenašpilu", "rednecklife": "seoskiživot", "boardom": "dosadno", "applestoapples": "jab<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON>", "gameboard": "igrenaoblasti", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinole", "jeuxdesociétés": "društvenekaraoke", "twilightimperium": "<PERSON>rak<PERSON><PERSON><PERSON><PERSON><PERSON>", "horseopoly": "konjopolis", "deckbuilding": "<PERSON>z<PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "igrajmaporedstola", "shadowsofbrimstone": "senkeizbrimstona", "kingoftokyo": "kraljtokija", "warcaby": "<PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "battleship", "tickettoride": "karikirajtevožnju", "deskovehry": "stolneigre", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "društvenekvadrate", "stolníhry": "stolneigre", "xiángqi": "<PERSON>ah", "jeuxsociete": "društvenekolone", "gesellschaftsspiele": "drustveneigre", "starwarslegion": "ratovizvezdalegion", "gochess": "gocahes", "weiqi": "weiqi", "jeuxdesocietes": "d<PERSON><PERSON><PERSON><PERSON>eklase", "terraria": "terarija", "dsmp": "dsmp", "warzone": "ratnazona", "arksurvivalevolved": "arkpreživljavanjerazvijeno", "dayz": "daniz", "identityv": "identitetv", "theisle": "ostrvo", "thelastofus": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "poz<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendiidainkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetkreator", "daysgone": "pro<PERSON><PERSON>_dani", "fobia": "fobija", "witchit": "veštičarenje", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "nordgard", "7dtd": "7dtd", "thelongdark": "dugmrak", "ark": "ark", "grounded": "nazemlji", "stateofdecay2": "stanjepropadanja2", "vrising": "vrising", "madfather": "ludotac", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "večnितповорак", "pathoftitans": "putti<PERSON>", "frictionalgames": "frictionalgames", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "zloiznutra", "realrac": "pravirac", "thebackrooms": "zadnjeodeje", "backrooms": "pozadine", "empiressmp": "empiresmp", "blockstory": "blokp<PERSON>ča", "thequarry": "kamenolom", "tlou": "tlou", "dyinglight": "umirućesvetlost", "thewalkingdeadgame": "hodajucimrtvaciigra", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "usponimpsalava", "stateofsurvivalgame": "državapreživljavanjeигра", "vintagestory": "vintageprica", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathež", "alisa": "alisa", "westlendsurvival": "preživljavanjesvestlenda", "beastsofbermuda": "zveriizbermude", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "opstanakhorror", "residentevil": "rezidentnivo", "residentevil2": "rezidentevil2", "residentevil4": "residentevil4", "residentevil3": "rezidentnivo3", "voidtrain": "praznvoz", "lifeaftergame": "životposleigre", "survivalgames": "igra<PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "ovajratmijepokrenuo", "scpfoundation": "scpfundacija", "greenproject": "zeleniprojekat", "kuon": "kuon", "cryoffear": "plačiizstraha", "raft": "čamac", "rdo": "rdo", "greenhell": "zelenainferno", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "rezidentzlo8", "onironauta": "onironauta", "granny": "baba", "littlenightmares2": "malanoćnamora2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "sinu<PERSON><PERSON><PERSON>", "rustvideogame": "rustigrica", "outlasttrials": "izdržiizazove", "alienisolation": "vanzemskaizolacija", "undawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "7dana2umre", "sunlesssea": "bezanjesunca", "sopravvivenza": "opstanak", "propnight": "<PERSON><PERSON><PERSON>", "deadisland2": "mrtvipулјан2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "smrtnavers", "cataclysmdarkdays": "kataklizmadarkdani", "soma": "soma", "fearandhunger": "strahizagodavanje", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "do<PERSON><PERSON>rak<PERSON>", "clocktower3": "satkulap3", "aloneinthedark": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "srednjovekovnedinastije", "projectnimbusgame": "projektnimbusigra", "eternights": "večnonoci", "craftopia": "craftopia", "theoutlasttrials": "nastavazavršetka", "bunker": "bunker", "worlddomination": "svetskaosvajanja", "rocketleague": "raketnaliga", "tft": "tft", "officioassassinorum": "oficijalniubice", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "ubijalacpatuljaka", "warhammer40kcrush": "ratnipovrat40k", "wh40": "wh40", "warhammer40klove": "ratnjak40kljubav", "warhammer40klore": "warhammer40kpriče", "warhammer": "ratnapogodak", "warhammer30k": "ratnjammer30k", "warhammer40k": "ratnjak40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "os<PERSON>a", "ilovesororitas": "volimzororite", "ilovevindicare": "volimvindikaciju", "iloveassasinorum": "volimassasinorum", "templovenenum": "templovenenum", "templocallidus": "templokalidus", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetrix", "lioden": "lioden", "ageofempires": "dobazajedličine", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "<PERSON><PERSON>kw<PERSON>ageos<PERSON><PERSON>", "civilizationv": "civilizacijav", "ittakestwo": "zatrebadujudva", "wingspan": "rasponk<PERSON>a", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "herojimoćiimagije", "btd6": "btd6", "supremecommander": "supremnikomandant", "ageofmythology": "eramitologije", "args": "<PERSON><PERSON><PERSON>", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "izgnanici2", "banished": "proterana", "caesar3": "caesar3", "redalert": "crvenaalarm", "civilization6": "civilizacija6", "warcraft2": "warcraft2", "commandandconquer": "zapovediistradanje", "warcraft3": "warcraft3", "eternalwar": "večniorat", "strategygames": "strate<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "civilizacijasveta", "civilization4": "civilizacija4", "factorio": "faktori<PERSON><PERSON>", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totalnaobmana", "travian": "travian", "forts": "tvr<PERSON><PERSON>", "goodcompany": "dobrodruštvo", "civ": "civ", "homeworld": "domovinskisvet", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "zakraljeve", "realtimestrategy": "pravnatrategija", "starctaft": "starctaft", "sidmeierscivilization": "sidmajerovacivilizacija", "kingdomtwocrowns": "kraljevstvodvokrat", "eu4": "eu4", "vainglory": "bravura", "ww40k": "ww40k", "godhood": "božanstvo", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveovazabavnaalgebraklasa", "plagueinc": "plagueinc", "theorycraft": "teorijaskraftovanja", "mesbg": "mesbg", "civilization3": "civilizacija3", "4inarow": "4<PERSON>u", "crusaderkings3": "kraljevikrižari3", "heroes3": "heroji3", "advancewars": "naprednevojneigre", "ageofempires2": "<PERSON><PERSON><PERSON><PERSON>", "disciples2": "učenici2", "plantsvszombies": "biljkeprotišavcima", "giochidistrategia": "strategijske<PERSON><PERSON>", "stratejioyunları": "stratejioigra", "europauniversalis4": "evropauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "vremečudesа", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "osvajanjesveta", "heartsofiron4": "srcaodgvožđa4", "companyofheroes": "društ<PERSON><PERSON>ki<PERSON>junaka", "battleforwesnoth": "bitkazaweznot", "aoe3": "aoe3", "forgeofempires": "kovanjeimpeija", "warhammerkillteam": "ratnjakmkillteam", "goosegooseduck": "guskaguska_patka", "phobies": "fobije", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "spoljniavion", "turnbased": "naizmenično", "bomberman": "bomberman", "ageofempires4": "dobaimperija4", "civilization5": "civilizacija5", "victoria2": "vikorija2", "crusaderkings": "kraljevskikrstaši", "cultris2": "kultris2", "spellcraft": "čarobnjaštvo", "starwarsempireatwar": "ratovizvezdaimperijanaratu", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategija", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportnimogul", "unrailed": "nerazumljivo", "magicarena": "<PERSON><PERSON>na", "wolvesville": "vukovgrad", "ooblets": "ooblets", "planescapetorment": "planirajbežanjeodmuke", "uplandkingdoms": "uzbudijenazemlje", "galaxylife": "galaksživot", "wolvesvilleonline": "vukoviugradu", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "<PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "simsi4", "thesims": "simsi", "simcity": "simgrad", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potrebnabrzina", "needforspeedcarbon": "potrebnakontrolašljiva", "realracing3": "pravautrka3", "trackmania": "trackmania", "grandtourismo": "<PERSON><PERSON><PERSON><PERSON>", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "sims2", "thesims3": "sims3", "thesims1": "sims1", "lossims4": "gubitnici4", "fnaf": "fnaf", "outlast": "izdrži", "deadbydaylight": "smrtnidonuždanju", "alicemadnessreturns": "alicinavadarotakom", "darkhorseanthology": "tamnikonjantologija", "phasmophobia": "fob<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "petnoćiuzfreddya", "saiko": "<PERSON><PERSON>o", "fatalframe": "fatalframe", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "podizanjemrtvih", "ladydimitrescu": "ladydi<PERSON><PERSON><PERSON>", "homebound": "kodkuće", "deadisland": "mrtvotoka", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "projektzero", "horory": "horori", "jogosterror": "jogosterr<PERSON><PERSON>", "helloneighbor": "zdravokomšija", "helloneighbor2": "zdravosused2", "gamingdbd": "gamingdbd", "thecatlady": "mačkažena", "jeuxhorreur": "hororigrice", "horrorgaming": "hororigre", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "karteprotivčovečanstva", "cribbage": "k<PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "<PERSON><PERSON><PERSON><PERSON>", "codenames": "kodenames", "dixit": "dixit", "bicyclecards": "biciklističkartice", "lor": "lor", "euchre": "juker", "thegwent": "tegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "pokер", "hearthstone": "srceka<PERSON><PERSON>", "uno": "uno", "schafkopf": "šafkopf", "keyforge": "kljeforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "kartezaigranje", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "trgovinskekarte", "pokemoncards": "p<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "mesooikrvtcg", "sportscards": "sportskukartice", "cardfightvanguard": "kartoborbaovanguard", "duellinks": "duellinks", "spades": "trefovi", "warcry": "ratnik", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kraljsrca", "truco": "truko", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "otpor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yug<PERSON>hkartice", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohigra", "darkmagician": "tamničar", "blueeyeswhitedragon": "plav<PERSON><PERSON>_beli_zmaj", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "ka<PERSON><PERSON><PERSON><PERSON>", "burraco": "burako", "rummy": "rumy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "kotorac", "jeuxdecartes": "kartezaigre", "mtgjudge": "mtgpravioc", "juegosdecartas": "karticeigre", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "igrica", "carteado": "karteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "<PERSON><PERSON><PERSON><PERSON>", "battlespirits": "bitkenezmajeva", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "jogodečarka", "žolíky": "žolíki", "facecard": "facecard", "cardfight": "b<PERSON><PERSON><PERSON><PERSON>a", "biriba": "biriba", "deckbuilders": "deckbuilderi", "marvelchampions": "marvel<PERSON><PERSON>i", "magiccartas": "magicnekarte", "yugiohmasterduel": "jug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "senkovnisvет", "skipbo": "skip<PERSON>", "unstableunicorns": "nestabilnijijednorogi", "cyberse": "cyberse", "classicarcadegames": "klasičnearkadneigre", "osu": "osu", "gitadora": "gitadora", "dancegames": "plesneigre", "fridaynightfunkin": "petakveč<PERSON>unkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektnirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "gitarabog", "clonehero": "klonheroj", "justdance": "<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockujmrtve", "chunithm": "čunithm", "idolmaster": "idolmajstor", "dancecentral": "plescentral", "rhythmgamer": "ritamigrači", "stepmania": "<PERSON><PERSON><PERSON>", "highscorerythmgames": "igrevisokihrezultata", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hajpmik", "adanceoffireandice": "plesnpaljivanjakrviizmrzlin", "auditiononline": "audicijaonline", "itgmania": "itgmanija", "juegosderitmo": "ritmiskihigara", "cryptofthenecrodancer": "kriptozamrtvogaigrača", "rhythmdoctor": "ritamdoktora", "cubing": "kockanje", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "igralicezaogledala", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "logičke_zagonetke", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "mozgalice", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "krosvord", "motscroisés": "križaljke", "krzyżówki": "ukrštenice", "nonogram": "nonogram", "bookworm": "knjigoljubac", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "zagonetka", "riddles": "zagonetke", "rompecabezas": "složenica", "tekateki": "tekateki", "inside": "unutra", "angrybirds": "ljutepatke", "escapesimulator": "beživotimulator", "minesweeper": "minesweeper", "puzzleanddragons": "zaguglaikaveizmajevi", "crosswordpuzzles": "ukrštenice", "kurushi": "k<PERSON>hi", "gardenscapesgame": "baš<PERSON>izglediš<PERSON>", "puzzlesport": "zagonetnasport", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "bežanjemигра", "3dpuzzle": "3dmozgalica", "homescapesgame": "homescapesigra", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "zagonetnepriče", "fishdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "nemogokviz", "candycrush": "slatkički", "littlebigplanet": "maliogromniplanet", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kwirky": "k<PERSON>i", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "domskeepanje", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "zagonetkajme", "tycoongames": "tajkunskeigre", "cubosderubik": "kockicazarubik", "cruciverba": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "zagonetnereči", "buscaminas": "minesweeper", "puzzlesolving": "rešavanjezagonetki", "turnipboy": "repačovek", "adivinanzashot": "poga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "nikoji", "guessing": "pogađanje", "nonograms": "nonogrami", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptičkekrstaše", "syberia2": "siberija2", "puzzlehunt": "lovnapuzzle", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "macaubistvo", "quebracabeça": "zagonetka", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "poslednjiognjište", "autodefinidos": "autodefinisani", "picopark": "pikopark", "wandersong": "wandersong", "carto": "<PERSON><PERSON><PERSON>", "untitledgoosegame": "neimenovanaguskaigra", "cassetête": "kasetete", "limbo": "limbo", "rubiks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "komadi", "portalgame": "portaligra", "bilmece": "bilmece", "puzzelen": "puzzle<PERSON><PERSON><PERSON>", "picross": "pikros", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubofantazija", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobilni", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopolija", "futurefight": "budućnostborba", "mobilelegends": "mobilnelegende", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "kok", "lonewolf": "usamljenivuk", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "kraljevstvocookierun", "alchemystars": "alchemystars", "stateofsurvival": "državauvijestima", "mycity": "mojgrad", "arknights": "arknights", "colorfulstage": "šarenasscena", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "sudbinskodredstvo", "hyperfront": "hiperfront", "knightrun": "vitezotrčanje", "fireemblemheroes": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "fudbalskabatalja", "a3": "a3", "phonegames": "telefonskeigre", "kingschoice": "kraljevskiizbor", "guardiantales": "prič<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "ljubiteljautomobila", "tacticool": "taktikularno", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pik<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "vankruga", "craftsman": "majstor", "supersus": "supersus", "slowdrive": "sporavožnja", "headsup": "pripremisede", "wordfeud": "<PERSON><PERSON><PERSON><PERSON>", "bedwars": "ratnapodnožju", "freefire": "slobodnivozilo", "mobilegaming": "mobilnigaming", "lilysgarden": "lilysinvrt", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "timskeborbe", "clashofclans": "klanovskabitka", "pjsekai": "pjsekai", "mysticmessenger": "mistic<PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "<PERSON>nah<PERSON>", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "slobodandan", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "šejkoviifidžeti", "ml": "ml", "bangdream": "bangdream", "clashofclan": "<PERSON><PERSON><PERSON><PERSON>", "starstableonline": "zvezdanastablaonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "vremeprinceza", "beatstar": "beatstar", "dragonmanialegend": "zmajčoveklegenda", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "androidigre", "criminalcase": "kriminalnis<PERSON>", "summonerswar": "sakupljačarazbijača", "cookingmadness": "kuvarskamanija", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviaklik", "leagueofangels": "ligaanjela", "lordsmobile": "gospodarimobil", "tinybirdgarden": "tinybirdgarden", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mojimuzikalisvijetovi", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "p<PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "ogledalniversum", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendsmobilno", "ingress": "<PERSON>az", "slugitout": "izvucise", "mpl": "mpl", "coinmaster": "koinmajstor", "punishinggrayraven": "kazneniosiviavran", "petpals": "prijateljizakucneljubimce", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenapobed<PERSON>", "wolfy": "v<PERSON><PERSON><PERSON>", "runcitygame": "trčanjgradu", "juegodemovil": "mobilneigre", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "crnopedesmobile", "rollercoastertycoon": "rolerkostermajstor", "grandchase": "grandchase", "bombmebrasil": "bombujmebrazil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "otomeg<PERSON>", "mindustry": "<PERSON><PERSON><PERSON><PERSON>", "callofdragons": "pozivzmajeva", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "putnaizgubljenosti", "sealm": "sealm", "shadowfight3": "senkeborba3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolicionidermbi3", "wordswithfriends2": "rečisaprijateljima2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "savršenapriča", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "berbagrad", "perfectworldmobile": "savršensvetmobilno", "empiresandpuzzles": "carstvaiuklayanjapuzzle", "empirespuzzles": "empirepuzzle", "dragoncity": "zmajevgrad", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "malanoćnamora", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "večnostдуша", "gunbound": "gunbound", "gamingmlbb": "gejmingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombijažičari", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilnilegendebangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "kuvar<PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobilni", "streetfighterduel": "uličniboracduel", "lesecretdhenri": "lesecretjednoga", "gamingbgmi": "gamingsrb", "girlsfrontline": "devojkeizprvevrste", "jurassicworldalive": "jurassicworldživi", "soulseeker": "tragačzasoulom", "gettingoverit": "prebolavanje", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "mesecnicepriče", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilegamers", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "igrajmobilelegends", "timeraiders": "timeraiders", "gamingmobile": "mobilnigaming", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bitkakaraca", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "svetmračnosti", "travellerttrpg": "putnicitttrpg", "2300ad": "2300godina", "larp": "larp", "romanceclub": "romanticiklub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmisterijskazatvoreni", "pokemonlegendsarceus": "pok<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonskakristalna", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemongrlo", "pokemongo": "pokemongo", "pokemonshowdown": "pokémonrazbijanje", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonsjedinjenje", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "pald<PERSON>a", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonaviolet", "pokemonpurpura": "pokémonpurpura", "ashketchum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gengar": "gengar", "natu": "natu", "teamrocket": "tim<PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "<PERSON><PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON><PERSON>", "teamystic": "tim<PERSON><PERSON><PERSON><PERSON>", "pokeball": "pokeball", "charmander": "<PERSON><PERSON><PERSON>", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "littan", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokemoni", "ironhands": "željeznaruke", "kabutops": "kabutops", "psyduck": "psidak", "umbreon": "umbreon", "pokevore": "pokebor", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmajstor", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "decakiiipokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "<PERSON><PERSON><PERSON>", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON>č<PERSON><PERSON>", "ajedrez": "<PERSON>ah", "catur": "katur", "xadrez": "<PERSON>ah", "scacchi": "<PERSON>ah", "schaken": "šahovanje", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "šahovskedevojke", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "svetskiblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "japanskašah", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šah<PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "otvorenja", "rook": "rook", "chesscom": "šahkom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "tamniceidragon", "dungeonmaster": "dunjamaster", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "tamnisunce", "thelegendofvoxmachina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doungenoanddragons": "doungenoanddragons", "darkmoor": "crnamoar", "minecraftchampionship": "minecraftšampionat", "minecrafthive": "minecraftkošnica", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodovi", "mcc": "mcc", "candleflame": "plam<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftdžep", "minecraft360": "mincraft360", "moddedminecraft": "modifiko<PERSON>minecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "izmeđuokolja", "minecraftdungeons": "minecraftdžungla", "minecraftcity": "minecraftgrad", "pcgamer": "pc<PERSON><PERSON><PERSON>", "jeuxvideo": "videoigrice", "gambit": "gambit", "gamers": "g<PERSON><PERSON><PERSON>", "levelup": "nivelup", "gamermobile": "gamermobil", "gameover": "krajigame", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gejman", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcigre", "casualgaming": "opustenoigraње", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmajstornacija", "pcgame": "pcigrica", "gamerboy": "gamerujdečko", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gejmerbr", "gameplays": "g<PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "konzolaš", "boxi": "boxi", "pro": "pro", "epicgamers": "epič<PERSON><PERSON>ri", "onlinegaming": "onlajnigračenje", "semigamer": "polovinaigrača", "gamergirls": "gejmerke", "gamermoms": "gamermame", "gamerguy": "gamerguy", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gejmer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerke", "otoge": "otoge", "dedsafio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamtryhard": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "š<PERSON><PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "a<PERSON><PERSON><PERSON>", "oldgamer": "stariigrači", "cozygaming": "udobnogaming", "gamelpay": "gamelpay", "juegosdepc": "pcigre", "dsswitch": "dsswitch", "competitivegaming": "takmičarskigejming", "minecraftnewjersey": "minecraftnovajersija", "faker": "lažnjak", "pc4gamers": "pc4gejmere", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksualnigaming", "gamepc": "igrapc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmodovi", "dailyquest": "dnevnavrhunac", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "devojkigreja", "gamesetup": "igrapostavka", "overpowered": "<PERSON><PERSON><PERSON>", "socialgamer": "društvenigamer", "gamejam": "igrajpartiju", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "ulogaigrača", "myteam": "mojeteam", "republicofgamers": "republicaigrača", "aorus": "aorus", "cougargaming": "ljubiteljkeigrica", "triplelegend": "triplelegend", "gamerbuddies": "gamerdrugari", "butuhcewekgamers": "trebamdevojkeigrače", "christiangamer": "hriscans<PERSON>ig<PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerd<PERSON>j<PERSON>", "afk": "afk", "andregamer": "and<PERSON><PERSON><PERSON>", "casualgamer": "opuštenigamer", "89squad": "89ek<PERSON><PERSON>", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "gemeri", "oyunizlemek": "gledajigram", "gamertag": "gejmertag", "lanparty": "lanpartija", "videogamer": "videoig<PERSON>č", "wspólnegranie": "zajedničkigranje", "mortdog": "mortdog", "playstationgamer": "playstationgejmer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "zdravigamer", "gtracing": "gtracing", "notebookgamer": "sveskigamer", "protogen": "protogen", "womangamer": "wabukgirl", "obviouslyimagamer": "očiglednosamgejmer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "<PERSON><PERSON><PERSON><PERSON>", "forager": "berba", "humanfallflat": "čovekpadneoplanet", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "njegovaizlazna_zenavod_", "waluigi": "v<PERSON><PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "<PERSON><PERSON><PERSON><PERSON>", "fallguys": "<PERSON><PERSON><PERSON><PERSON>", "switch": "promeni", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "legendazelde", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mariokartmajstor", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "nebeskidalač<PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "suzekraljevstva", "walkingsimulators": "simulatoriprošetanja", "nintendogames": "nintendogames", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "zmajeviizazov", "harvestmoon": "berbinasmesec", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mojprijateljpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirbi", "51games": "51igre", "earthbound": "zemlјani", "tales": "prič<PERSON>", "raymanlegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "luigismansion": "lui<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "životinjskikrizanje", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktaj", "smashultimate": "smashultimate", "nintendochile": "nintendosrbija", "tloz": "tloz", "trianglestrategy": "trougluistrategija", "supermariomaker": "supermariomaker", "xenobladechronicles3": "ksenobladehronike3", "supermario64": "supermario64", "conkersbadfurday": "šljivicejakezrakusnovak", "nintendos": "nintendos", "new3ds": "novi3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperzvezde", "marioandsonic": "marioisonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogi", "thezelda": "zelda", "palia": "palija", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "j<PERSON><PERSON><PERSON><PERSON>", "wildrift": "divljiotok", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligalegendasrb", "urgot": "urgot", "zyra": "zyra", "redcanids": "crvenikljunac<PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligalegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligačudesašuma", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "ligalegendasšpanija", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "ligalegendasieu", "kayle": "kejl", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "šako", "ligadaslegendas": "<PERSON><PERSON><PERSON><PERSON>", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexkapije", "hextech": "hextech", "fortnitegame": "fortniteigra", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideogameovi", "scaryvideogames": "strašneigre", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videoigrica", "videosgame": "videoigrice", "professorlayton": "pro<PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatch": "prekapam", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "veštac101", "battleblocktheater": "bitkaiblokoviteatra", "arcades": "arkade", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "simulatorpol<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxnemačka", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sanjaboxigre", "videogamelore": "videogamežurke", "rollerdrome": "rollerdrom", "parasiteeve": "parazi<PERSON>ve", "gamecube": "gejmkub", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON><PERSON>", "dreamscape": "sanjanjepocinje", "starcitizen": "zvezdanograđanin", "yanderesimulator": "janderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "mrtvoprostor", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videoigre", "theoldrepublic": "stararepublika", "videospiele": "videoigrice", "touhouproject": "touhouprojekt", "dreamcast": "dreamkast", "adventuregames": "avanturaskigre", "wolfenstein": "wolfenstein", "actionadventure": "akcijavantura", "storyofseasons": "pričaesekundi", "retrogames": "retrogames", "retroarcade": "retroarkada", "vintagecomputing": "vintagekompjuterisanje", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "igra<PERSON><PERSON>", "commanderkeen": "koman<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "nepravda2", "shadowthehedgehog": "senkaježdžoslon", "rayman": "rejmen", "skygame": "nebeskapartija", "zenlife": "zenživot", "beatmaniaiidx": "beatmaniaiidx", "steep": "strmo", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchainigrica", "medievil": "srednjo<PERSON><PERSON><PERSON>", "consolegaming": "konzolaigra", "konsolen": "konsolice", "outrun": "p<PERSON><PERSON><PERSON>", "bloomingpanic": "bloomingpanika", "tobyfox": "tobyfox", "hoyoverse": "hojoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "igra<PERSON><PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "superdiv", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "farmingsims", "juegosviejos": "stareigre", "bethesda": "bet<PERSON><PERSON>", "jackboxgames": "jackboxigre", "interactivefiction": "interaktivnafikcija", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "poslednjiiznas2", "amantesamentes": "ljubavnik<PERSON>lova", "visualnovel": "vizuelnapovest", "visualnovels": "vizuelneknjige", "rgg": "rgg", "shadowolf": "senkač狼", "tcrghost": "tcrduh", "payday": "danisplate", "chatherine": "katja", "twilightprincess": "sumračnaprinceza", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "estetskeigre", "novelavisual": "novalavizija", "thecrew2": "ekipa2", "alexkidd": "alekskin", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "b<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolucijavetrack<PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "ključnooružje", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafnakatkad", "novelasvisuales": "vizuelnenovela", "robloxbrasil": "robloxsrbi", "pacman": "pacman", "gameretro": "gejmeretro", "videojuejos": "videoigre", "videogamedates": "datumiigara", "mycandylove": "mojslatkiljubav", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "samozato3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "p<PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamsterskaigrica", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "manijakdvorac", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformeri", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "staretajming", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "pričanjeigrice", "bioware": "bioware", "residentevil6": "rezidentmračni6", "soundodger": "<PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "izvandved<PERSON>š<PERSON>", "gameuse": "koris<PERSON>game", "offmortisghost": "offmortisghost", "tinybunny": "malizecica", "retroarch": "retroarch", "powerup": "<PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafi<PERSON><PERSON><PERSON><PERSON>", "quickflash": "brzispas", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkade", "f123": "f123", "wasteland": "pust<PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "koralnooke", "syberia3": "siberija3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animeratnici2", "footballfusion": "fuzijafudbala", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "<PERSON><PERSON><PERSON><PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "uvrnjenometal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "gromadašamacije", "simulator": "simulator", "symulatory": "simulatori", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epičnx", "superrobottaisen": "superrobotbitka", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gryvideo", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "čarobnjakonline", "skylander": "skylander", "boyfrienddungeon": "dečkotamnica", "toontownrewritten": "toontownponovo", "simracing": "simtrke", "simrace": "simtrka", "pvp": "pvp", "urbanchaos": "urbanahaos", "heavenlybodies": "nebeskitelbodies", "seum": "seum", "partyvideogames": "žurkastigra", "graveyardkeeper": "čuvargroblja", "spaceflightsimulator": "simulatorletovanjavezduhoplovom", "legacyofkain": "nasleđekaina", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "hranaivideogame", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulator<PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "igrauz<PERSON><PERSON>", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON>", "oldschoolvideogames": "stareškolevideogames", "racingsimulator": "simulatortrke", "beemov": "<PERSON><PERSON><PERSON><PERSON>", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "kapijedolimpijade", "monsterhunternow": "monsterhunternow", "rebelstar": "rebelzvezda", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "indievideogames", "indievideogame": "indieigre", "chellfreeman": "<PERSON><PERSON><PERSON><PERSON>", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "buffut<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "proje<PERSON>l", "futureclubgames": "budućnosťklubigara", "mugman": "šoljamandır", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "apertureznanost", "backlog": "zaostavština", "gamebacklog": "igra<PERSON>t", "gamingbacklog": "gamingzaostataka", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "gradskikrajolici", "supermonkeyball": "supermajmunskalopta", "deponia": "deponija", "naughtydog": "naughtypas", "beastlord": "zverovladar", "juegosretro": "retroigre", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriitlipavforest", "alanwake": "alanwake", "stanleyparable": "stanleypredstava", "reservatoriodedopamin": "rezervatoriodopamina", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "zmajosink", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "volimkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "besan", "baki": "baki", "sailormoon": "molitvazadusa", "saintseiya": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "prvobitno", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "tu<PERSON><PERSON>ani<PERSON>", "darkerthanblack": "dubljeodcrnog", "animescaling": "animepovećanje", "animewithplot": "animezaplotom", "pesci": "p<PERSON><PERSON><PERSON><PERSON>", "retroanime": "retroadultanime", "animes": "anime", "supersentai": "superosjećanja", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "mračnigospodar", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezon1", "rapanime": "rapanime", "chargemanken": "napuničoveka", "animecover": "animekorica", "thevisionofescaflowne": "vizijaescaflone", "slayers": "ubice", "tokyomajin": "tokyomajin", "anime90s": "anime90te", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "bananoriba", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletzakrijenihanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "vatrenapolica", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "bud<PERSON><PERSON>idnev<PERSON>", "fairytail": "bajka", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "napravljenouprovaliji", "parasyte": "para<PERSON>ta", "punpun": "punpun", "shingekinokyojin": "nap<PERSON><PERSON><PERSON>na", "mushishi": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastars": "bistar", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirename<PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horormanga", "romancemangas": "romantičnemanga", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "zmajevaka", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsyho100", "terraformars": "terrafarmers", "geniusinc": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "šamanskokraljenje", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bunghukluca", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "određenamagijskabrojka", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "<PERSON><PERSON><PERSON>", "kagerouproject": "kagerouproject", "haikyuu": "хајкију", "toaru": "toaru", "crunchyroll": "krančirol", "aot": "aot", "sk8theinfinity": "sk8beskončno", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "špijunizabava", "rezero": "rezero", "swordartonline": "mačjestaonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "priorite<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosmik", "goldenkamuy": "zlatnakanmu", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "anđelo<PERSON><PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanja", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "dečakibestošnje", "fistofthenorthstar": "ručaksevernezvezde", "mazinger": "mazinger", "blackbuttler": "crnigospodar", "towerofgod": "kulaodgospodara", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "<PERSON><PERSON>", "servamp": "servamp", "howtokeepamummy": "kakozadržatimumu", "fullmoonwosagashite": "fullmoonwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "slatkoikrvavo", "martialpeak": "martialvrh", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "visokaskorica", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "<PERSON><PERSON><PERSON>", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "jone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "какаши", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanjaj", "bakugo": "bakugo", "griffith": "grifit", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "skaramoška", "amiti": "amiti", "sailorsaturn": "morenasaturn", "dio": "deo", "sailorpluto": "mornarpluto", "aloy": "aloj", "runa": "runa", "oldanime": "starianime", "chainsawman": "čoveksačom", "bungoustraydogs": "bumgoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyoosvetnici", "blackbutler": "crnaglava", "ergoproxy": "ergoproxy", "claymore": "<PERSON><PERSON><PERSON><PERSON>", "loli": "loli", "horroranime": "horo<PERSON><PERSON>", "fruitsbasket": "voćnapodloga", "devilmancrybaby": "đavoljimalazcrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "živiljubav", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "ovarinoseraf", "thepromisedneverland": "obećanazemlja", "monstermanga": "monstermanga", "yourlieinapril": "tvojaljubavuaprilu", "buggytheclown": "bugiđua<PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "serapod<PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "dubokomorskizarobljenik", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mrtvacizemlja", "bannafish": "bananafis", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorinasrca", "yoimiya": "joimiја", "foodwars": "<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "đavoljalinija", "toyoureternity": "tvojaventurauvečnosti", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "plaviperiod", "griffithberserk": "gri<PERSON><PERSON><PERSON><PERSON>", "shinigami": "<PERSON><PERSON><PERSON>", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "махоутсукаиножемља", "yuki": "yuki", "erased": "izbrisan", "bluelock": "p<PERSON><PERSON><PERSON>", "goblinslayer": "goblinslayer", "detectiveconan": "detektivkonan", "shiki": "šiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "rijasgremori", "shojobeat": "shojobeat", "vampireknight": "vampirskivitez", "mugi": "mugi", "blueexorcist": "plaviegzorcista", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "špijunskafamilija", "airgear": "vazdušnaborci", "magicalgirl": "magičnasdevojk<PERSON>", "thesevendeadlysins": "ovihsedamgrehova", "prisonschool": "zatvorskaljionica", "thegodofhighschool": "bogsrednjosesija", "kissxsis": "poljubacxsestra", "grandblue": "grandblue", "mydressupdarling": "mojadragaoblačenja", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverzum", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hošizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "какогуруи", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromansa", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "demonubijacdooreza", "bloodlad": "krvavabanda", "goodbyeeri": "zelimozbogomeri", "firepunch": "vatrenipunch", "adioseri": "adios<PERSON>", "tatsukifujimoto": "тацукифујимото", "kinnikuman": "кинни<PERSON>у<PERSON>ан", "mushokutensei": "muškoliteljski", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zvezdeseporavnavaju", "romanceanime": "romantičnaanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "čarolijačešnje", "housekinokuni": "kućanacrnogore", "recordragnarok": "snimaj<PERSON><PERSON><PERSON>", "oyasumipunpun": "ojasumipunpun", "meliodas": "meliodas", "fudanshi": "фудан<PERSON>и", "retromanga": "retromanga", "highschoolofthedead": "srednjaskolaživihmrtvaca", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "ансасукиоушицу", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "ubicaučionica", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "sprovodposležurke", "shokugekinosouma": "šokugekinosouma", "japaneseanime": "japanskaanime", "animespace": "animesvemir", "girlsundpanzer": "devojkeunpanzeru", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON>", "animedub": "anid<PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "ko<PERSON><PERSON><PERSON>", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON>jak<PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonskaavanturadai", "heartofmanga": "srcemanga", "deliciousindungeon": "ukusnouš<PERSON>li", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "zapisiragnaroka", "funamusea": "zabavaamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "preskočišaltera", "shuumatsunovalkyrie": "šumatsunovalkirija", "tutorialistoohard": "tutorialjeprerazvijeno", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "majstor968", "kkondae": "kkondae", "chobits": "čobits", "witchhatatelier": "čarapomračneveštičješaltere", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "<PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "mangaživot", "dropsofgod": "kapljicebožje", "loscaballerosdelzodia": "lošicavaleridelazodija", "animeshojo": "animeshojo", "reverseharem": "ob<PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "superučiteljonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "mojbosstata", "gear5": "oprema5", "grandbluedreaming": "grandblueizsnom", "bloodplus": "krvplus", "bloodplusanime": "krvplusanime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON>", "bloodc": "krvc", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "animedevojke", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vranextnajgore", "splatteranime": "splatneranime", "splatter": "prskanje", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "so<PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animešpanija", "animeciudadreal": "animegradreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "decakitova", "liarliar": "lažljivalažljivica", "supercampeones": "superšampioni", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiživeosmartfonu", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "čarobnedevojke", "callofthenight": "poz<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuga<PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "senovnagarden", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON>na<PERSON><PERSON><PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "rajskizagrljaj", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeversum", "persocoms": "<PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "omniscijentovizija", "animecat": "animemačka", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "otvaranjeanimea", "shinichirowatanabe": "шинит<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>бе", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mójteenromantičnakomedija", "evangelion": "evangelion", "gundam": "gundam", "macross": "makros", "gundams": "gundami", "voltesv": "voltesv", "giantrobots": "giganti<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "кодгиас", "mobilefighterggundam": "mobilniboracggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilnisuitedgandam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "biganime", "bleach": "izbeljivanje", "deathnote": "sveskavce", "cowboybebop": "kaubojizabop", "jjba": "jjba", "jojosbizarreadventure": "džodžovečudneavanture", "fullmetalalchemist": "puničelnikalhemicar", "ghiaccio": "led", "jojobizarreadventures": "jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "v<PERSON><PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "zvezdanlisica", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animegrad", "animetamil": "animestreaming", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "jedinstvenode<PERSON>", "animeonepiece": "anime<PERSON>dan<PERSON>ljak", "dbz": "dbz", "dragonball": "zmajevoball", "yugioh": "ju<PERSON><PERSON><PERSON>", "digimon": "digimon", "digimonadventure": "digimonavantura", "hxh": "hxh", "highschooldxd": "srednjaskoladxd", "goku": "goku", "broly": "brat", "shonenanime": "šonenanime", "bokunoheroacademia": "bokunoheroakademija", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hajdehunter", "mha": "mha", "demonslayer": "ubijalacdemona", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "napadnigitana", "erenyeager": "ebeživot", "myheroacademia": "mojaheroakademija", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "anketnabrigada", "onepieceanime": "onepieceanime", "attaquedestitans": "napadajdeštitane", "theonepieceisreal": "jednodelovaljistikaјестварна", "revengers": "os<PERSON>a", "mobpsycho": "mobpsihop", "aonoexorcist": "aonoeksercista", "joyboyeffect": "joy<PERSON><PERSON><PERSON><PERSON>", "digimonstory": "digimonprica", "digimontamers": "dig<PERSON><PERSON><PERSON><PERSON>", "superjail": "superzatvor", "metalocalypse": "metalokalipsa", "shinchan": "š<PERSON><PERSON>lan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "урусејијатсура", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "našmangaimeclub", "flawlesswebtoon": "besprekornitwebtoon", "kemonofriends": "kemonoprijatelji", "utanoprincesama": "uzbooprincese", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "y<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "letećaveštica", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON>", "horimiya": "хоримија", "allsaintsstreet": "svecelebrate", "recuentosdelavida": "pričeizživota"}