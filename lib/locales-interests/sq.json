{"2048": "2048", "mbti": "mbti", "enneagram": "eneagram", "astrology": "astrolog<PERSON>a", "cognitivefunctions": "funksionetnjohëse", "psychology": "psikologji", "philosophy": "filozofi", "history": "histori", "physics": "fizikë", "science": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "culture": "kulturë", "languages": "gjuhë", "technology": "teknologjia", "memes": "meme", "mbtimemes": "meme<PERSON><PERSON>", "astrologymemes": "memeastrologjie", "enneagrammemes": "memeeneagrame", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "video", "gadgets": "veglat", "politics": "politikë", "relationshipadvice": "këshillapërmarrëdhënie", "lifeadvice": "këshillapërjetën", "crypto": "kripto", "news": "lajme", "worldnews": "lajmengabot<PERSON>", "archaeology": "arkeologjia", "learning": "<PERSON><PERSON><PERSON><PERSON>", "debates": "debatet", "conspiracytheories": "teorikonspirative", "universe": "universi", "meditation": "meditimi", "mythology": "mitologjia", "art": "art", "crafts": "artizan", "dance": "<PERSON><PERSON><PERSON><PERSON>", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "grim", "beauty": "bukuria", "fashion": "modë", "singing": "këndim", "writing": "shkrim", "photography": "fotografi", "cosplay": "loj<PERSON>", "painting": "pikturë", "drawing": "vizatim", "books": "libra", "movies": "filma", "poetry": "poezi", "television": "televizion", "filmmaking": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "animim", "anime": "anima", "scifi": "shkencore", "fantasy": "fantazia", "documentaries": "dokumentar", "mystery": "mister", "comedy": "komedi", "crime": "krime", "drama": "drama", "bollywood": "bolivud", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romancë", "realitytv": "realitettv", "action": "veprim", "music": "muzikë", "blues": "bluz", "classical": "klasike", "country": "shteti", "desi": "dëshirë", "edm": "edm", "electronic": "elektronik", "folk": "folklorik", "funk": "muzikfunk", "hiphop": "muzikëhiphop", "house": "sht<PERSON><PERSON>", "indie": "indie", "jazz": "xhaz", "kpop": "kpop", "latin": "latine", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "mb", "rap": "rrep", "reggae": "rege", "rock": "rrok", "techno": "tekno", "travel": "udhëtime", "concerts": "koncerte", "festivals": "festivale", "museums": "muze", "standup": "komedi", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "jashtënënatyrë", "gardening": "<PERSON><PERSON><PERSON><PERSON>", "partying": "duke<PERSON>uar", "gaming": "lo<PERSON>ra", "boardgames": "lojratavoline", "dungeonsanddragons": "birucadhedragonjtë", "chess": "shah", "fortnite": "fortnite", "leagueoflegends": "ligaelegjendave", "starcraft": "zanatmeyje", "minecraft": "minekraft", "pokemon": "pokemon", "food": "<PERSON>h<PERSON><PERSON>", "baking": "pjek<PERSON>", "cooking": "gatim", "vegetarian": "vegjeratarian", "vegan": "vegan", "birds": "zogj", "cats": "mace", "dogs": "qen", "fish": "peshq", "animals": "kafshë", "blacklivesmatter": "jetëtezezakanërëndësi", "environmentalism": "ambientalizëm", "feminism": "feminizëm", "humanrights": "tëdrejtatenjeriut", "lgbtqally": "lgbtkalli", "stopasianhate": "ndalurrejtjesndajaziatikëve", "transally": "transali", "volunteering": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "sporte", "badminton": "badminton", "baseball": "bejzboll", "basketball": "basketboll", "boxing": "boks", "cricket": "kriket", "cycling": "çiklizëm", "fitness": "fitnes", "football": "futboll", "golf": "golf", "gym": "palestër", "gymnastics": "gjimnastikë", "hockey": "hokej", "martialarts": "artemarciale", "netball": "lojanetball", "pilates": "pilates", "pingpong": "pingpong", "running": "v<PERSON><PERSON>", "skateboarding": "skejtbord", "skiing": "ski", "snowboarding": "snouboring", "surfing": "surf", "swimming": "not", "tennis": "tenis", "volleyball": "volejboll", "weightlifting": "peshëngrit<PERSON>", "yoga": "joga", "scubadiving": "zhytjenëskuba", "hiking": "hajking", "capricorn": "bric<PERSON><PERSON>", "aquarius": "<PERSON><PERSON><PERSON>", "pisces": "peshqit", "aries": "dashi", "taurus": "demi", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "gaforrja", "leo": "luani", "virgo": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libra": "peshorja", "scorpio": "<PERSON><PERSON><PERSON><PERSON>", "sagittarius": "s<PERSON>g<PERSON><PERSON>", "shortterm": "afatshkurter", "casual": "j<PERSON><PERSON><PERSON>", "longtermrelationship": "lidhjemafa<PERSON>te", "single": "beqar", "polyamory": "polia<PERSON><PERSON>", "enm": "martesëpërtëdyja", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lezbike", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragoniage", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "vëzhguesit", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kryetarëtestrave", "soulreaver": "shpëtimipërshpirtra", "suikoden": "su<PERSON><PERSON>", "subverse": "subversi", "legendofspyro": "legend<PERSON><PERSON><PERSON>", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "spyrodragoi", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "betejafundore", "yokaiwatch": "yokaiwatch", "rocksteady": "tëfortëdhetëqetë", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "luftatbegj", "openworld": "botëhapur", "heroesofthestorm": "heronjtëestormit", "cytus": "cytus", "soulslike": "shpirtrarëzë", "dungeoncrawling": "dungeonshkallmime", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribetemidgardit", "planescape": "planifikim", "lordsoftherealm2": "lordëtembretërisë2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "ngj<PERSON>t", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simulimeimmersive", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "shtriga", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "eldensh<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "da<PERSON>ja", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modif<PERSON><PERSON>", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyshkollastare", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "sfidafinale", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivimemorbid", "finalfantasyvii": "fantazifundjavëvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaehtimes", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiriështja", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "rrugëtimtar", "pathfinder2ndedition": "pathfinder2e", "shadowrun": "shadowshtatë", "bloodontheclocktower": "gjaknëorën", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "graviteteplasja", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "njëgoditje", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "mbretëresh<PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "horrorrpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonetesh", "mu": "mu", "falloutshelter": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "boteepeshiradavetë", "eclipsephase": "<PERSON>az<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "botatjashtme", "arpg": "arpg", "crpg": "krpg", "bindingofisaac": "shpijaeisaacit", "diabloimmortal": "diabloimmortal", "dynastywarriors": "luftëtarëtedinastisë", "skullgirls": "skullgirls", "nightcity": "natënqytet", "hogwartslegacy": "trashëgimiaehogwarts", "madnesscombat": "luftëmadhe", "jaggedalliance2": "bashkëveprojmë2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "rruga96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "xhen<PERSON><PERSON><PERSON>", "forgottenrealms": "mbretëritëeforgotura", "dragonlance": "theshtojzë", "arenaofvalor": "arenaeveles", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "qytetiikarikaturave", "childoflight": "fëmijëdrite", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "divizioni2", "lineage2": "linjash2", "digimonworld": "digimonbota", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkaniversi", "fracturedthrones": "fraktaruartëronat", "horizonforbiddenwest": "horizoniipërjas<PERSON>uarperëndimin", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "misterihogwarts", "deltagreen": "ndryshojmëgjërat", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "epokelaste", "starfinder": "gjetësistarëve", "goldensun": "<PERSON><PERSON><PERSON><PERSON>j<PERSON>", "divinityoriginalsin": "mëkatitorigjinal", "bladesinthedark": "thikatnëmbrëmë", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkkuq", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "tokaebishtave", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "demonipë<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "bluzateregance", "adventurequest": "ekspeditëaventurash", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "lojratëeaktroleve", "roleplayinggames": "lojtarëtërolit", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "tim<PERSON><PERSON>", "sacredunderworld": "sacredunderworld", "chainedechoes": "tingujtëngjalur", "darksoul": "shpirtimratë", "soulslikes": "shpirtlike", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "kolonat_e_përjetësisë", "palladiumrpg": "palladiumrpg", "rifts": "ndarjet", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON>", "hellocharlotte": "përshëndetjecharlotte", "legendofdragoon": "legjendaedragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octopathudhëtar", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "isujëjthikësapokalipsi", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "fëmijëtëmorta", "engineheart": "zemraengjinjve", "fable3": "fable3", "fablethelostchapter": "pë<PERSON>llasherëndëtë<PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternale", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "yjetnepergjithesi", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "botëshpërthyese", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "mbretëreshadhërdhës1", "ff9": "ff9", "kingdomheart2": "mbretëriazemrave2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "lojërarpg", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "mbretëritëza3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "grumbullim", "gloomhaven": "gloomhaven", "wildhearts": "z<PERSON><PERSON>g<PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "drak<PERSON>chdemon<PERSON><PERSON>", "skiesofarcadia": "qiejtearcadia", "shadowhearts": "zemrathijeshtë", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "monedhaedhëmbjeve", "breathoffire4": "fl<PERSON>zjarr4", "mother3": "nënë3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "lojratëeformimit", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "zemraeshtrigës", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "rrugëtuesirpg", "pathfinder2e": "rrugëkërkuesi2e", "vampirilamasquerade": "maskaradava<PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "origjinatetdragons", "chronocross": "chronokros", "cocttrpg": "cocttrpg", "huntroyale": "hunt<PERSON>droni", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumirpg", "shadowheartscovenant": "pak<PERSON><PERSON><PERSON><PERSON><PERSON>rave", "bladesoul": "shpirtitetra", "baldursgate3": "baldursgate3", "kingdomcome": "ardhjenekrenarisë", "awplanet": "awplanet", "theworldendswithyou": "botapërfundonmety", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dielgja2", "finalfantasytactics": "taktikatefinalfantasy", "grandia": "grandia", "darkheresy": "here<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumirpg", "golarion": "golarion", "earthmagic": "magjiatoka", "blackbook": "libriipërdarkë", "skychildrenoflight": "fëmijëtqellorblerë", "gryrpg": "gryrpg", "sacredgoldedition": "edicioniaromëzateutdown", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "lojagothike", "scarletnexus": "nexusiktëkuqe", "ghostwiretokyo": "fantazmatëtokios", "fallout2d20": "fallout2d20", "gamingrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prophunt": "prophunt", "starrails": "sterrugët", "cityofmist": "qytetimist", "indierpg": "indierpg", "pointandclick": "pointandclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "tëpaparë", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7përjetësia", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "rrugaevettespërkanadë", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "gjuetarëmonsterësh", "fireemblem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacia", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>ë<PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "gjuetariemonstrave", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON>are", "tacticalrpg": "tacticalrpg", "mahoyo": "mahojo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonatamëeternale", "princessconnect": "princeshkonekt", "hexenzirkel": "hexenzirkel", "cristales": "kristalet", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketziġġ", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaedëshirave", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dhembeshkënë", "gaimin": "gaimin", "overwatchleague": "ligasuperiorëoverwatch", "cybersport": "cybersport", "crazyraccoon": "rakuniçmendur", "test1test": "test1test", "fc24": "fc24", "riotgames": "lojeratnëkaos", "eracing": "eracing", "brasilgameshow": "brazilgameshow", "valorantcompetitive": "valorantkkompetitive", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "pjesëjetë", "left4dead": "lënepërvdekur", "left4dead2": "ekatkuar2", "valve": "valva", "portal": "portali", "teamfortress2": "ekipifortesës2", "everlastingsummer": "veraëternale", "goatsimulator": "simulatoriikecave", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetiliber", "transformice": "transformice", "justshapesandbeats": "thjeshtformaandritmet", "battlefield4": "fushatëbeteje4", "nightinthewoods": "natënnëpyll", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "rrezikeshiu2", "metroidvanias": "metroidvanias", "overcooked": "etep<PERSON><PERSON>", "interplanetary": "interplanetar", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "q<PERSON>za<PERSON>dheut", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortesadwarfëve", "foxhole": "foxhole", "stray": "bllok", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "battfield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "syyt", "blackdesert": "bllakçeredhebregdeti", "tabletopsimulator": "simulatoritëtablo", "partyhard": "ballefort", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "hades", "gunsmith": "armëpunu<PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "dinkum", "predecessor": "<PERSON>ard<PERSON><PERSON><PERSON><PERSON>", "rainworld": "botëresheshojgajë", "cavesofqud": "shpellatbajzeve", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "agimiiluftës", "minionmasters": "masterateminionëve", "grimdawn": "agimiifundit", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "punëtorskshpirtërore", "datingsims": "lojratëdashurisë", "yaga": "yaga", "cubeescape": "shku<PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "qytetinew", "citiesskylines": "horizontetecityeve", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bibliotekaruinave", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON>are", "omegastrikers": "omegastrikers", "wayfinder": "udhëk<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "patkuqiplastikplacid", "battlebit": "battlbit", "ultimatechickenhorse": "pulabefjelloreultimate", "dialtown": "dialtown", "smileforme": "bëjnjëbuzëqeshjepërmua", "catnight": "nataemaceve", "supermeatboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "tinnybunny", "cozygrove": "koshakëndell", "doom": "doom", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kod", "borderlands": "kufijët", "pubg": "pubg", "callofdutyzombies": "thirrjedetëvdekurve", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "luftee<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "<PERSON>j<PERSON><PERSON>far<PERSON><PERSON>", "paladins": "paladinë<PERSON>", "earthdefenseforce": "forcatambrojtjesëdhetokës", "huntshowdown": "g<PERSON><PERSON>jas<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "fantazmërekon", "grandtheftauto5": "kradhtetautomate5", "warz": "luftë", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "bash<PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "rebelliiertësandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "kalimesabrenda", "b4b": "b4b", "codwarzone": "codluftëzone", "callofdutywarzone": "thirrjaduelswarzone", "codzombies": "codzombies", "mirrorsedge": "kuf<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisions2", "killzone": "zonaekërcënimit", "helghan": "hel<PERSON>", "coldwarzombies": "luf<PERSON>ef<PERSON><PERSON>ë<PERSON>ish", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "luftëmeaeroplanë", "crosscode": "crosscode", "goldeneye007": "sytëeartë007", "blackops2": "bllakops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "luftamoderne", "neonabyss": "neonabisi", "planetside2": "planetside2", "mechwarrior": "mekbete", "boarderlands": "kuf<PERSON>j", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "ikngaxtakov", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "bot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "prapakatërrimë", "warframe": "luftërafrikës", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON>ë<PERSON>", "masseffect": "masseffect", "systemshock": "shokssystemi", "valkyriachronicles": "kronikatevalkyrive", "specopstheline": "specopstheline", "killingfloor2": "pjesamortale2", "cavestory": "historiaskavash", "doometernal": "doometernal", "centuryageofashes": "shekulliepushkave", "farcry4": "farcry4", "gearsofwar": "armëtetluftës", "mwo": "mwo", "division2": "divizioni2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "gjeneratanzero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "luftëmoderne2", "blackops1": "blackops1", "sausageman": "njeriunësalçiçeve", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "dhimbjafantazmë", "warface": "luftëfytyrë<PERSON>", "crossfire": "zhbllokim", "atomicheart": "zemraatomike", "blackops3": "blackops3", "vampiresurvivors": "vampirëmbijetuesit", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "lirim", "battlegrounds": "fushatabat<PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "lojpugb", "necromunda": "nekrumundës", "metalgearsonsoflibert": "metalgearbijtëeështjesëlibrit", "juegosfps": "lojatfps", "convertstrike": "konvertoclash", "warzone2": "wzone2", "shatterline": "shatterline", "blackopszombies": "zombitblackops", "bloodymess": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikakomando", "elitedangerous": "elitedangerous", "soldat": "shokluftarak", "groundbranch": "dhembëatak", "squad": "<PERSON><PERSON>", "destiny1": "destiny1", "gamingfps": "lojratfps", "redfall": "rëniesëkuqe", "pubggirl": "vajzëpubg", "worldoftanksblitz": "botëkantankeshblitz", "callofdutyblackops": "thir<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enlisted": "eangazhuar", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "botaemrekullivetinetina", "halo2": "halo2", "payday2": "pagëmëpasë", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubg<PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON>pu<PERSON><PERSON>", "ghostcod": "fantasikod", "csplay": "csplay", "unrealtournament": "turneunreal", "callofdutydmz": "thirrjaduarvëdmz", "gamingcodm": "lojratcodm", "borderlands2": "terranshtrire2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "thirrjedutymw2", "quakechampions": "kampionetëtermetit", "halo3": "halo3", "halo": "halo", "killingfloor": "shkallëvrasëse", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "qelizashtypit", "neonwhite": "neonbardhë", "remnant": "mbetje", "azurelane": "azurelane", "worldofwar": "botakrigave", "gunvolt": "gunvolt", "returnal": "k<PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "hombrezgjedhje", "quake2": "termeti2", "microvolts": "mik<PERSON>oltet", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "fushabetejës3", "lostark": "lostark", "guildwars2": "luftaradhe2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "çelik", "conqueronline": "përfitoonline", "dauntless": "pafrikë", "warships": "anijetluftarake", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "flutur<PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "legjendatetëruntëterrës", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "fantastarealitetonline2", "maidenless": "pabojë", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "botatankeve", "crossout": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "jetaeveshteshtë", "aion": "aion", "toweroffantasy": "toweritëfantazisë", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtalonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubiçpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "plehra", "newworld": "botenovell", "blackdesertonline": "blackdesertonline", "multiplayer": "lojëmeçokë", "pirate101": "pirati101", "honorofkings": "nënderiikrallave", "fivem": "fivem", "starwarsbattlefront": "luftatestarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "luftatestarwars2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "bisedë3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasik", "worldofwarcraft": "botëapërluftë", "warcraft": "luf<PERSON><PERSON>", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknightë", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "<PERSON>k<PERSON><PERSON><PERSON>", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragonsprophet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "engel<PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversionline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikaevjetër", "grandfantasia": "fantaziagrande", "blueprotocol": "protokoliblu", "perfectworld": "botëperfekte", "riseonline": "ngjitemonline", "corepunk": "corepunk", "adventurequestworlds": "aventuratërgime<PERSON>bot<PERSON><PERSON>", "flyforfun": "fluturopërfun", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "qytetibashkëvepruesve", "mortalkombat": "mortalkombat", "streetfighter": "luftetariistradhe", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "luftësrrugor6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "rrugëtoragës", "mkdeadlyalliance": "mkaleancëvdekjeprurëse", "nomoreheroes": "sdukeheronjë<PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "lojraterefroting", "blasphemous": "b<PERSON><PERSON><PERSON>", "rivalsofaether": "rivaletdhetë", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supershkatërrojnë", "mugen": "mugen", "warofthemonsters": "luftaemonstrave", "jogosdeluta": "luftojmëbashkë", "cyberbots": "cyberbots", "armoredwarriors": "luftëtarëtëmbrojtur", "finalfight": "sfidafinale", "poweredgear": "shpejtesiaeenergjisë", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "luftagames", "killerinstinct": "instinktivrasës", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "kalorësi2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "pasardhësihollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "lajmetdesilksong", "silksong": "silksong", "undernight": "nënnatë", "typelumina": "tipilumina", "evolutiontournament": "turneuisevolucionit", "evomoment": "evomomenti", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "gjak<PERSON><PERSON><PERSON>", "horizon": "<PERSON><PERSON>", "pathofexile": "rrugaegrind<PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "gjakbumë", "uncharted": "tëpaftë", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "fundittonë", "infamous": "ifamshëm", "playstationbuddies": "shokëtplaystation", "ps1": "ps1", "oddworld": "bot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "ndarjeportash", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "kompanianëndarë", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "grisë", "trove": "trezore", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "beats<PERSON>r", "rimworld": "botarim", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON>in<PERSON><PERSON><PERSON>", "touristtrophy": "trofeteturistike", "lspdfr": "lspdfr", "shadowofthecolossus": "hijaekolosëve", "crashteamracing": "ekipngasje", "fivepd": "pesëpd", "tekken7": "tekken7", "devilmaycry": "djalloshtmëngjesi", "devilmaycry3": "djalliqeshfaq3", "devilmaycry5": "djallnukqaj5", "ufc4": "ufc4", "playingstation": "lojëpjesë", "samuraiwarriors": "luftëtarëtsamurai", "psvr2": "psvr2", "thelastguardian": "guardianifundit", "soulblade": "thikësoul", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "g<PERSON><PERSON>a", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "guardianifundit", "xboxone": "xboxone", "forza": "forca", "cd": "cd", "gamepass": "përgjigjepas", "armello": "armello", "partyanimal": "kafshaparty", "warharmmer40k": "luftanëtrake40k", "fightnightchampion": "natenekampionëve", "psychonauts": "psikonautët", "mhw": "mhw", "princeofpersia": "princiipersisë", "theelderscrollsskyrim": "pleqtëcrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "përgjithësiërashtrimit", "gxbox": "gxbox", "battlefront": "frontibetejës", "dontstarvetogether": "mosup<PERSON>bas<PERSON>ke", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "yjetpërtej", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "amerikanskaliceemcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "trashtv", "skycotl": "qiellcotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "jashtështëpisë", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "sekretitëp<PERSON><PERSON><PERSON>", "duckgame": "lojagjakmarrës", "thestanleyparable": "thedy<PERSON>par<PERSON><PERSON>", "towerunite": "kullabashkohen", "occulto": "oculto", "longdrive": "udhëtimëtëgjatë", "satisfactory": "kënaqësi", "pluviophile": "pluviophile", "underearth": "nëntoka", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "kapenëdarkësi", "pizzatower": "pizatower", "indiegame": "lojëindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "tëvërtetënapoaja", "game": "loj<PERSON>", "rockpaperscissors": "guriçpapërçkëmba", "trampoline": "trampolinë", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "sfido", "scavengerhunt": "gjuetikëngësh", "yardgames": "lojratnëoborr", "pickanumber": "zgjidhnjënumër", "trueorfalse": "evertetëapojo", "beerpong": "birravljatë", "dicegoblin": "goblin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "lojraalkooli", "sodoku": "sodoku", "juegos": "lo<PERSON>ra", "mahjong": "mahjong", "jeux": "lo<PERSON>ra", "simulationgames": "lojratësimulimit", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "lojërdhashë", "juegosdepalabras": "lojratëfjalëve", "letsplayagame": "vamosluajmënjëloje", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "loja", "interactivegames": "lojratinteraktive", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "loje", "giochi": "lo<PERSON>ra", "geoguessr": "geoguessr", "iphonegames": "lojratoniphone", "boogames": "boogames", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "fsheheshkërkosh", "hopscotch": "shkallëzë", "arcadegames": "lojratëarkadave", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "lojëklasike", "mindgames": "lojratemendore", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "lojëromantike", "yanderegames": "lojratayandere", "tonguetwisters": "ngatërrimilinguistique", "4xgames": "lojra4x", "gamefi": "lojafit", "jeuxdarcades": "lojratëarkadave", "tabletopgames": "lojëratetavolinës", "metroidvania": "metroidvania", "games90": "lojat90", "idareyou": "tiq<PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "lojratëegarave", "ets2": "ets2", "realvsfake": "evertëtnëngjesht", "playgames": "luajlojëra", "gameonline": "lojëonline", "onlinegames": "lojratëonline", "jogosonline": "lojratonline", "writtenroleplay": "lojtëshkruar", "playaballgame": "luajbollenlojë", "pictionary": "pik<PERSON>i", "coopgames": "lojratacoop", "jenga": "jenga", "wiigames": "lojratwiigames", "highscore": "pikëllohubashkë", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "lojratëburgerëve", "kidsgames": "lojratëfë<PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwbotakblack", "jeuconcour": "lojëkonkurrente", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "loj<PERSON>", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "lojëobjektetëfshehura", "roolipelit": "bajzëzave", "formula1game": "lojaformula1", "citybuilder": "ndërt<PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "lojezarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>mor<PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "lojrataksione", "blowgames": "bllokpërlozje", "pinballmachines": "makinatëpinball", "oldgames": "lojratëvjetra", "couchcoop": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "pyetjet", "gameo": "gameo", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "lojatëhumbura", "fillintheblank": "plotesopjesat", "jeuxpc": "lojrapc", "rétrogaming": "rétrogaming", "logicgames": "lojëratlogjike", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "nënterrenisurf", "jeuxdecelebrite": "lojratëcelebrimeve", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5për5", "rolgame": "roll<PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "lojratradicionale", "kniffel": "kniffel", "gamefps": "lojafps", "textbasedgames": "lojratmezhdëne", "gryparagrafowe": "gryparagrafëve", "fantacalcio": "fantacalcio", "retrospel": "retrospeli", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "lojranëbar", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "futbollitabla", "tischfußball": "tischfußball", "spieleabende": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "lojëforum", "casualgames": "lojracasuale", "fléchettes": "pikat", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "lojratngrica", "játék": "loj<PERSON>", "bordfodbold": "bordiifutbollit", "jogosorte": "lojatforta", "mage": "magji", "cargames": "lo<PERSON><PERSON>makinave", "onlineplay": "lojëonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "natë<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "bingoçanta", "randomizer": "rast<PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialdeductiongames": "lojratëededuksionitsocial", "dominos": "dominos", "domino": "domino", "isometricgames": "lojratizometrike", "goodoldgames": "lojratetëmira", "truthanddare": "tëmëdhejesëdheguajë", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "gjuetingakërkimeve", "jeuxvirtuel": "lojëratvirtuale", "romhack": "romhack", "f2pgamer": "lojtarif2p", "free2play": "falaspërt<PERSON><PERSON><PERSON><PERSON>", "fantasygame": "lojafantazie", "gryonline": "gryonline", "driftgame": "lojadrift", "gamesotomes": "lojësatëmëndjeve", "halotvseriesandgames": "halotvseriteledhelojrat", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "cdogjëmenjëmotor", "everywheregame": "lojapërgjithkund", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "lojaepërcaktuar", "jugamos": "luajmë", "lab8games": "lojratlab8", "labzerogames": "lojratlabzero", "grykomputerowe": "lojërakompjuterike", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "lojatmeritme", "minaturegames": "lojrateminaturë", "ridgeracertype4": "lidhjemarshtipo4", "selflovegaming": "vet<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "modifikimiligjërave", "crimegames": "lojratëekrimit", "dobbelspellen": "dobbelspellen", "spelletjes": "lo<PERSON>ra", "spacenerf": "spacenerf", "charades": "karak<PERSON>e", "singleplayer": "lojtarevetem", "coopgame": "lojëbashkëpunimi", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "gefërcetani", "maingame": "lojaeparë", "kingdiscord": "mbretdiscord", "scrabble": "shkruajme", "schach": "shah", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludosh", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "trashëgimipandemise", "camelup": "kameltop", "monopolygame": "lojamonopoli", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "lojëtabloje", "sällskapspel": "lojratëshoq<PERSON><PERSON><PERSON>", "planszowe": "lojatëbordit", "risiko": "risiko", "permainanpapan": "lojëmavështirë", "zombicide": "zombicide", "tabletop": "tavolinë", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "herokërk<PERSON>a", "giochidatavolo": "lojratëtryezë", "farkle": "farkle", "carrom": "karrom", "tablegames": "lojratetables", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "jat<PERSON>", "parchis": "par<PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "lojratemijesise", "deskgames": "lojratëmbireshpizë", "alpharius": "alfarius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "protokolliimekrizënemarvelit", "cosmicencounter": "takimkozmik", "creationludique": "k<PERSON><PERSON><PERSON>dh<PERSON>m", "tabletoproleplay": "lojenimbiqenë", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "horrorieldritik", "switchboardgames": "lojeteshkembit", "infinitythegame": "infinitylojase", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "j<PERSON><PERSON>", "chutesandladders": "përplasjeshedhëmbëve", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "lojetemeboo", "planszówki": "lojatëbordit", "rednecklife": "jetalneobojë", "boardom": "boredom", "applestoapples": "moll<PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "lojtarsociale", "gameboard": "<PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "lojatësocietetit", "twilightimperium": "perandoritevet<PERSON>ja", "horseopoly": "sh<PERSON><PERSON><PERSON>gdh<PERSON>", "deckbuilding": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "mansionsofmadness", "gomoku": "gomoku", "giochidatavola": "lojratë<PERSON>ë<PERSON>s<PERSON><PERSON>", "shadowsofbrimstone": "hijetebrimstone", "kingoftokyo": "mbretiitokyos", "warcaby": "warcaby", "táblajátékok": "bajamebajzë", "battleship": "betejadeteanijes", "tickettoride": "biletashpëtuari", "deskovehry": "stresoveshy", "catán": "këtë", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "lojeteshqipërisë", "gesellschaftsspiele": "lojratshoq<PERSON><PERSON><PERSON>", "starwarslegion": "legjionistëtstarwars", "gochess": "<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "lojëratemeqytetit", "terraria": "terraria", "dsmp": "dsmp", "warzone": "wzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "identiteti", "theisle": "ishulli", "thelastofus": "tëfunditqëjemi", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "th<PERSON>rja<PERSON>ëecthulhut", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "me<PERSON><PERSON>h", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetkrijuesit", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "witchit", "pathologic": "patologjik", "zomboid": "zomboid", "northgard": "verioregard", "7dtd": "7dtd", "thelongdark": "gjat<PERSON><PERSON>ë<PERSON>", "ark": "ark", "grounded": "rrënjësor", "stateofdecay2": "gjendjaezhbjes2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "mos<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "kthimëternale", "pathoftitans": "rrugaetitanëve", "frictionalgames": "lojratfrikshme", "hexen": "hexen", "theevilwithin": "tëkeqindërmëndje", "realrac": "realrac", "thebackrooms": "dhebackrooms", "backrooms": "prap<PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "b<PERSON><PERSON><PERSON><PERSON>", "thequarry": "miniera", "tlou": "tlou", "dyinglight": "dielit", "thewalkingdeadgame": "lojatheshembjesëvdekjeve", "wehappyfew": "ne<PERSON>mi<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "ngjitjaeperandorive", "stateofsurvivalgame": "lojëeeksistencës", "vintagestory": "historikavintage", "arksurvival": "arkmbijetesës", "barotrauma": "barotrauma", "breathedge": "breathegjatë", "alisa": "alisa", "westlendsurvival": "perballimiwestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "a<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "horrormbijetese", "residentevil": "banoritevilës", "residentevil2": "rezidentkeq2", "residentevil4": "rezidentevil4", "residentevil3": "rezidentet3", "voidtrain": "treniipashëm", "lifeaftergame": "jetëpasloje", "survivalgames": "lojratëmbijetese", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "këtëluftëtimeje", "scpfoundation": "scpfondacioni", "greenproject": "projektigjelbër", "kuon": "kuon", "cryoffear": "qajpërfrikën", "raft": "raft", "rdo": "rdo", "greenhell": "gjellëzelen", "residentevil5": "rezidentevil5", "deadpoly": "vdekurpoli", "residentevil8": "rezidentëvil8", "onironauta": "onironauta", "granny": "gj<PERSON><PERSON>ja", "littlenightmares2": "tmerritëvogla2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "djemtëepyllit", "rustvideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "zhbllokoshbutas", "alienisolation": "izolimialienëve", "undawn": "ndezur", "7day2die": "7ditë2vdesësh", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "mbijetesë", "propnight": "propnight", "deadisland2": "ishullivdekur2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "vdekjetverse", "cataclysmdarkdays": "kataklizmaditëtëerrta", "soma": "soma", "fearandhunger": "frikaedhuna", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "jetapastaj", "ageofdarkness": "epo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "kullakohës3", "aloneinthedark": "vetëmndezur", "medievaldynasty": "dinastiamedievale", "projectnimbusgame": "projektnimbusloje", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "provatetëdal<PERSON>s", "bunker": "bunker", "worlddomination": "dominimiparalajmit", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "vrasësitnëzyrë", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "vrasësiignomëve", "warhammer40kcrush": "përqafimwarhammer40k", "wh40": "wh40", "warhammer40klove": "dashuriaewarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "luftehammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarkaquarë", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vind<PERSON><PERSON>", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "eduavindicare", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasa<PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "epokadhempirëve", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "luftëhammerepokasigmarit", "civilizationv": "civilizimiv", "ittakestwo": "këtujanëtëdy", "wingspan": "krahëshkurtër", "terraformingmars": "terraformimimarsit", "heroesofmightandmagic": "heronjtëeforcësdhemagjisë", "btd6": "btd6", "supremecommander": "komandantisuprem", "ageofmythology": "epokaemiteve", "args": "<PERSON><PERSON><PERSON><PERSON>", "rime": "riime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilizimi6", "warcraft2": "warcraft2", "commandandconquer": "komandojedhefito", "warcraft3": "warcraft3", "eternalwar": "luftaeternale", "strategygames": "lojratstrategjike", "anno2070": "anno2070", "civilizationgame": "lojacivilizimi", "civilization4": "civilizimi4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "luftatotale", "travian": "travian", "forts": "forts", "goodcompany": "shoqërinëemirë", "civ": "civ", "homeworld": "botaime", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "mëshpejtësedrita", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "strategjiatrealet", "starctaft": "starctaft", "sidmeierscivilization": "sidmeiercivilizimi", "kingdomtwocrowns": "mbretëritë<PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "m<PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "annoo", "battletech": "teknologjitepejdeshkatimit", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "klasafalezadatëboo", "plagueinc": "plagueinc", "theorycraft": "teorikrijim", "mesbg": "mesbg", "civilization3": "civilizimi3", "4inarow": "4nërradhë", "crusaderkings3": "krishterëkings3", "heroes3": "heronjtë3", "advancewars": "luf<PERSON><PERSON><PERSON>", "ageofempires2": "epokameperandorive2", "disciples2": "disipujt2", "plantsvszombies": "bimëtndajzimëve", "giochidistrategia": "lojadistrategjike", "stratejioyunları": "strate<PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON>ep<PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozaurm<PERSON>t", "worldconquest": "pushtimiidokdondit", "heartsofiron4": "zemrateliron4", "companyofheroes": "shokëteheroismit", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgëeimperive", "warhammerkillteam": "ekipivrasëswarhammer", "goosegooseduck": "gandalakgandalak<PERSON>u", "phobies": "f<PERSON><PERSON><PERSON>", "phobiesgame": "lojëephobive", "gamingclashroyale": "lufta<PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "meturbaza", "bomberman": "bomberman", "ageofempires4": "eraeperive4", "civilization5": "civilizimi5", "victoria2": "victoria2", "crusaderkings": "mbretëritënëk<PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "magjinëeletërsisë", "starwarsempireatwar": "perandoriaestarwarëvenëluftë", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategjia", "popfulmail": "<PERSON><PERSON><PERSON><PERSON>", "shiningforce": "fuqiaequipe", "masterduel": "<PERSON><PERSON><PERSON>je<PERSON>t<PERSON><PERSON><PERSON>", "dysonsphereprogram": "programidysonsferës", "transporttycoon": "tykonitransportit", "unrailed": "unrailed", "magicarena": "<PERSON><PERSON>na", "wolvesville": "ujqëzvillë", "ooblets": "ooblets", "planescapetorment": "planifikoshikorrjesë", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "jetagalaksike", "wolvesvilleonline": "ujqëritonline", "slaythespire": "shkatërronpillin", "battlecats": "luftakacave", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "needyrapit", "needforspeedcarbon": "nevojëpërspeedcarbon", "realracing3": "gararealiste3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "si<PERSON><PERSON><PERSON>", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "humbësims4", "fnaf": "fnaf", "outlast": "m<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "vdekurnëdritë", "alicemadnessreturns": "kthimigjak<PERSON><PERSON><PERSON>", "darkhorseanthology": "antologjiaidarkë", "phasmophobia": "fobiaphasmave", "fivenightsatfreddys": "pesënatatfreddys", "saiko": "saiko", "fatalframe": "kornizafatale", "littlenightmares": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "ngritemortë", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nështëpi", "deadisland": "ishulliivdekur", "litlemissfortune": "vogëlbukuroshefa<PERSON>keqe", "projectzero": "projektizero", "horory": "horori", "jogosterror": "jogosterror", "helloneighbor": "përshëndetje_fqinji", "helloneighbor2": "përshëndetjefqinji2", "gamingdbd": "gamingdbd", "thecatlady": "gocatlady", "jeuxhorreur": "lojratëtmerrit", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magji<PERSON><PERSON><PERSON><PERSON>", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartatkundërnjerëzimit", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinoçkë", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "kartatnëbicycle", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legjendëeruneterës", "solitaire": "solitaire", "poker": "poker", "hearthstone": "zjarriikardashëve", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "çelësiforci", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kartelatetradh<PERSON><PERSON>", "pokemoncards": "kartatpokemon", "fleshandbloodtcg": "fleshëdhegjaktcg", "sportscards": "kartatësportit", "cardfightvanguard": "luftakartashvanguard", "duellinks": "duellinks", "spades": "bajame", "warcry": "gritmaeiluptarit", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "rezistenca", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kartatyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "luanijugioh", "darkmagician": "magjistidark", "blueeyeswhitedragon": "syzetebluatdragonshtatë", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandant", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "lojratëkartave", "mtgjudge": "gjyqtaritëmtg", "juegosdecartas": "luajmetëkartat", "duelyst": "duelist", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "<PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "boo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "facecard": "fytyrapasaportë", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "ndërtuesitekuvertës", "marvelchampions": "kampionetemarvel", "magiccartas": "mag<PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornëtepaqëndrueshëm", "cyberse": "cyberse", "classicarcadegames": "lojraarkadikeklasike", "osu": "osu", "gitadora": "gitadora", "dancegames": "lojra_të_vallet", "fridaynightfunkin": "fundjavë_muzikore", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projektidiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "klonheroi", "justdance": "vetëmvallek", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "lojratmeritminëepikëvetëlarta", "pkxd": "pkxd", "sidem": "anash", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "njevallemezflaketyferra", "auditiononline": "audiciononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "lojratërytmit", "cryptofthenecrodancer": "kriptoshkencemandarin", "rhythmdoctor": "rit<PERSON><PERSON><PERSON>", "cubing": "kube", "wordle": "wordle", "teniz": "tenisi", "puzzlegames": "lojëratëpuzzle", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "enigmatelogjike", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "sfidatëmendore", "rubikscube": "kubirubik", "crossword": "puzël<PERSON>", "motscroisés": "motskryq", "krzyżówki": "krzyżówki", "nonogram": "nonogram", "bookworm": "librashqip", "jigsawpuzzles": "puzzlepjesë", "indovinello": "provokim", "riddle": "enigme", "riddles": "puzzle", "rompecabezas": "puzzle", "tekateki": "tekateki", "inside": "brenda", "angrybirds": "angrybirds", "escapesimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "minesweeper", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "lojagardenave", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "lojratëdhomavet<PERSON>arra<PERSON>s", "escapegame": "lojëshkëputjeje", "3dpuzzle": "3dpuzzle", "homescapesgame": "lojahomescapes", "wordsearch": "kërkimfjalësh", "enigmistica": "enigmistik", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "historië<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "peshkatësia", "theimpossiblequiz": "kuiziimpossible", "candycrush": "candycrush", "littlebigplanet": "planetimëdhenjëtëvogla", "match3puzzle": "lojëmepuzzle3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kiv<PERSON>ke", "rubikcube": "kubirubiku", "cuborubik": "kubivertikal", "yapboz": "yapboz", "thetalosprinciple": "principathetalos", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "mërzita카오", "tycoongames": "breshkatloje", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "kodet", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "nëntatushdat<PERSON><PERSON>", "puzzlesolving": "zgjidhjepuzzleve", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "askush", "guessing": "hip<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogramet", "kostkirubika": "kostkirubika", "crypticcrosswords": "kryqëzimekriptuar", "syberia2": "syberia2", "puzzlehunt": "gjuetipuzzlesh", "puzzlehunts": "kërdhjevejtesh", "catcrime": "krimiicatëve", "quebracabeça": "truk<PERSON><PERSON>", "hlavolamy": "lojratëpërmendje", "poptropica": "poptropica", "thelastcampfire": "kampifundit", "autodefinidos": "autodefinuar", "picopark": "picopark", "wandersong": "këngëkreshëndikësh", "carto": "karton", "untitledgoosegame": "loja<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirinti", "tinykin": "tinykin", "rubikovakostka": "kubirubiku", "speedcube": "shpejtcube", "pieces": "pjesë", "portalgame": "lojaportrali", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "pik<PERSON>h", "rubixcube": "kubikrubik", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "botaehtjerrë", "monopoly": "monopoli", "futurefight": "luftafuturistike", "mobilelegends": "legjendatmobil", "brawlstars": "luftatëstars", "brawlstar": "brawlstar", "coc": "kokë", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "jetabite", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ylltartëegrupit", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "m<PERSON><PERSON><PERSON>reshae<PERSON><PERSON>", "alchemystars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknightset", "colorfulstage": "skenaicolore", "bloonstowerdefense": "mbrojtjepersonazheve", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "maratonëknight", "fireemblemheroes": "heroestëzjarrit", "honkaiimpact": "honkaiimpact", "soccerbattle": "luftëmefutboll", "a3": "a3", "phonegames": "lojratnëtelefon", "kingschoice": "zgjedhjaekrave", "guardiantales": "tregimetsguardianëve", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikool", "cookierun": "<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "jashtëqarkullimit", "craftsman": "ndërtuesi", "supersus": "supersus", "slowdrive": "ngasjeshpejtë", "headsup": "tëparalajmërosh", "wordfeud": "fjalëluftë", "bedwars": "luftatënjesëve", "freefire": "luftëshkaku", "mobilegaming": "lojratëmobile", "lilysgarden": "kopshtiiliza", "farmville2": "farmville2", "animalcrossing": "kaliminëpërracat", "bgmi": "bgmi", "teamfighttactics": "strateg<PERSON>ë<PERSON>am<PERSON>", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "thirrjaduararemobil", "thearcana": "arcana", "8ballpool": "8ballpool", "emergencyhq": "qendrakshtesë", "enstars": "yjet", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "historiemaple", "albion": "albioni", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tronditjedyfrym<PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "betejaclaneve", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "princeshatëtime", "beatstar": "beatstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "rastkriminal", "summonerswar": "luftëtarëteshumë", "cookingmadness": "çmenduriakohe", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviashkre<PERSON><PERSON>", "leagueofangels": "ligaeengjellve", "lordsmobile": "lordsmobile", "tinybirdgarden": "kopshtiipikë", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "monstratëmëngëngëta", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "arkiviblu", "raidshadowlegends": "raidshadowlegends", "warrobots": "luftarobotët", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiboo", "apexlegendmobile": "apexlegjendatmobile", "ingress": "hyrje", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "monedhamaster", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "shokëtmeqentë", "gameofsultans": "lojaesultanëve", "arenabreakout": "dolaqarcë", "wolfy": "wolfy", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "lojratmobile", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "imitim", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "tigonëgërricash", "grandchase": "ndiqnigrandchase", "bombmebrasil": "bombmepakbrasil", "ldoe": "ldoe", "legendonline": "legjendëonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "thirrjetëdragonjve", "shiningnikki": "dritëshkëlqyesenikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "<PERSON><PERSON>", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "fjaletmeortaket2", "soulknight": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "historiaeperfekte", "showbyrock": "shfaqjeshkëllçim", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "këtuqëmbjellim", "perfectworldmobile": "botaekperfectearkëtu", "empiresandpuzzles": "perand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "empirespuzla", "dragoncity": "qytetidragoneve", "garticphone": "garticphone", "battlegroundmobileind": "luftafushashmobileind", "fanny": "fanny", "littlenightmare": "koshkthnightmare", "aethergazer": "aeth<PERSON><PERSON><PERSON><PERSON>", "mudrunner": "mudrunner", "tearsofthemis": "lotethemisë", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "lojratmlbb", "dbdmobile": "dbdmobile", "arknight": "arknatë", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "echoete<PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegjendahekhekje", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "nënakuzhinë", "cabalmobile": "cabalmobil", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "lo<PERSON>ratbgmi", "girlsfrontline": "frontlinegjak<PERSON>", "jurassicworldalive": "jurasikbotaaktiv", "soulseeker": "shp<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "duketqëpoeçmë", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "garazhdhyr<PERSON>sonline", "jogosmobile": "lojratëmobile", "legendofneverland": "legjendaeasnjësëneverlandit", "pubglite": "pubglite", "gamemobilelegends": "lojamobilelegends", "timeraiders": "timeraiders", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "macetebetejave", "dnd": "dnd", "quest": "sfidë", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "bot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "udhetarettrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "klubiromancës", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemond<PERSON>z<PERSON>bimimist<PERSON>", "pokemonlegendsarceus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémonkrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonkuq", "pokemongo": "pokemongo", "pokemonshowdown": "poked<PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonbas<PERSON><PERSON>i", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "bisedo", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natyrë", "teamrocket": "ekipiroket", "furret": "buzëqeshje", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "mon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "plushpokemon", "teamystic": "ekipimistik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "masteripokémonëve", "pokémonsleep": "pokémongjumi", "kidsandpokemon": "fëmijëtëdhepokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "gjuetariiscilltë", "ajedrez": "shah", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "shakmat", "schaken": "schaken", "skak": "skak", "ajedres": "boochess", "chessgirls": "gjejgocat", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "botablitz", "jeudéchecs": "jeudéchecs", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "boka", "xadrezverbal": "xadrezverbal", "openings": "hapjet", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "ka<PERSON>ish<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>osh", "dungeonmaster": "udhëheqësiidungeonit", "tiamat": "tiamat", "donjonsetdragons": "donjonetedragoneve", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "legjendavorbësit", "doungenoanddragons": "dungenoanddragons", "darkmoor": "darkmoor", "minecraftchampionship": "kampionitminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "ëndrratsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modetminecraft", "mcc": "mcc", "candleflame": "flakaedhecandlit", "fru": "fru", "addons": "shtesa", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftimodifikuar", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "midisvendeve", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "qytetiminecraft", "pcgamer": "<PERSON>jtar<PERSON><PERSON>", "jeuxvideo": "lojratëvideo", "gambit": "gambit", "gamers": "lojtarë", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermobil", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "lojratëpërtëknologjinë", "gamen": "gamen", "oyunoynamak": "oyunojnamë", "pcgames": "lojratapc", "casualgaming": "lojratërelaksuese", "gamingsetup": "setupiigames", "pcmasterrace": "pcmasterrace", "pcgame": "lojracompju<PERSON>ësh", "gamerboy": "djaloshgamer", "vrgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbër", "gameplays": "<PERSON><PERSON><PERSON>", "consoleplayer": "lojtariikonsolës", "boxi": "boxi", "pro": "pro", "epicgamers": "epikgamerat", "onlinegaming": "lojëratnëinternet", "semigamer": "semigamer", "gamergirls": "vajzatgamer", "gamermoms": "mamatëgamer", "gamerguy": "djaloshgamer", "gamewatcher": "vëzhguesigamesh", "gameur": "<PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "vajzatgamere", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "ekipiprobues", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "sfida", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "lojëngrohtë", "gamelpay": "gamelpay", "juegosdepc": "lojratëpërpc", "dsswitch": "dsswitch", "competitivegaming": "lojratkonkuruese", "minecraftnewjersey": "minecraftnewjersey", "faker": "falsifikues", "pc4gamers": "pc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "lojratheteroseksualë", "gamepc": "lojëpc", "girlsgamer": "vajzatgamer", "fnfmods": "fnfmods", "dailyquest": "sfidaditore", "gamegirl": "lojëgoca", "chicasgamer": "vajzatgamer", "gamesetup": "setupygjakut", "overpowered": "tëfortë", "socialgamer": "lojtarigosociale", "gamejam": "lojëmaratona", "proplayer": "profili", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON>", "republicofgamers": "republikagamerëve", "aorus": "aorus", "cougargaming": "lojratemacave", "triplelegend": "legjendetriplet", "gamerbuddies": "shokëtgamer", "butuhcewekgamers": "ponakalongamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gjemers", "oyunizlemek": "shikoboo", "gamertag": "gamertag", "lanparty": "festël<PERSON><PERSON><PERSON>", "videogamer": "lojtarvideo", "wspólnegranie": "luajmebashke", "mortdog": "mortdog", "playstationgamer": "lojtariplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "lojtarishendetshem", "gtracing": "gtracing", "notebookgamer": "lojtaritnëfletore", "protogen": "protogen", "womangamer": "womangamer", "obviouslyimagamer": "si<PERSON>rishtqëjamlojtar", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "njerzikoreshenjat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroikapshëm", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musikënintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonik", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "nd<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "fëmijëteqiellittëdritës", "tomodachilife": "jetatëmodachit", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "lotëtëmbretërisë", "walkingsimulators": "simulator<PERSON><PERSON><PERSON><PERSON>", "nintendogames": "loj<PERSON><PERSON><PERSON>", "thelegendofzelda": "legjendapërzelda", "dragonquest": "dragonquest", "harvestmoon": "hënashkurtë", "mariobros": "mario<PERSON>s", "runefactory": "fermaeçmendu<PERSON>", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "shoku<PERSON><PERSON>", "legendsofzelda": "legjendatezelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51lojra", "earthbound": "to<PERSON><PERSON>", "tales": "tregime", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "kalimipërshqipërna", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "shkatërrimipërfundimtar", "nintendochile": "nintendoal", "tloz": "tloz", "trianglestrategy": "strategjiatrekëndëshave", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersnjëditëtëkeqe", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "luftetarëtehyrulit", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligashkëlqimipërfundimtar", "urgot": "urgot", "zyra": "zyra", "redcanids": "kanidëtnekuqe", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligabashkimeve", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligalikendjeswild", "adcarry": "adcarry", "lolzinho": "lo<PERSON><PERSON>o", "leagueoflegendsespaña": "leagueoflegendsshqipëri", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "ligathetlegendaveeu", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "lunar", "fnatic": "fnatic", "lollcs": "q<PERSON>me", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "lidhjenlegjendat", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hekshkurat", "hextech": "hextech", "fortnitegame": "lojafortnite", "gamingfortnite": "lojratfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "lojravideovehorror", "videogamemaker": "krijuesiigameve", "megamanzero": "megamanzero", "videogame": "lojëvideoveshje", "videosgame": "lojravideo", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "mbikqyrje", "ow2": "ow2", "overwatch2": "mbivëzhgim2", "wizard101": "wizard101", "battleblocktheater": "battleblockteatri", "arcades": "forumin", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simuluesiifarmerëve", "robloxchile": "robloxshqipëri", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxgjermania", "robloxdeutsch": "robloxshqip", "erlc": "erlc", "sanboxgames": "lojratansandbox", "videogamelore": "lojëratvideo", "rollerdrome": "rollerskating", "parasiteeve": "parazi<PERSON>ve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "p<PERSON>zaz<PERSON>dreami", "starcitizen": "yllqytetar", "yanderesimulator": "simu<PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "lojratvideo", "theoldrepublic": "republikaprakashme", "videospiele": "lojratëvideo", "touhouproject": "<PERSON>je<PERSON><PERSON><PERSON><PERSON>", "dreamcast": "<PERSON><PERSON><PERSON><PERSON>", "adventuregames": "lojratëaventurë", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "histor<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retrogjithçka", "vintagecomputing": "kompjuteravintage", "retrogaming": "retrogaming", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "takimloje", "commanderkeen": "komandantikeen", "bugsnax": "buxsnak", "injustice2": "përmbysja2", "shadowthehedgehog": "hijetepërgjakësit", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON>", "zenlife": "jetasëqetë", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "lojratëblockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON>", "consolegaming": "lojjapjesësëkonzolës", "konsolen": "konsola", "outrun": "shpejtasa", "bloomingpanic": "panicifloral", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "lojratfrikshme", "monstergirlquest": "kërkimmjeshësmonster", "supergiant": "supergjigand", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "farmingsims", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "lojratjackbox", "interactivefiction": "fikshioniinteraktiv", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "tëfunditnëna2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "romanjvisual", "visualnovels": "novelavizuale", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "ditaepagese", "chatherine": "katerina", "twilightprincess": "princittëagimit", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "lojatëestetikës", "novelavisual": "novelavizive", "thecrew2": "ekipii2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "lojratereja", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "do<PERSON>zap<PERSON><PERSON>", "leafblowerrevolution": "revolucioniijapaveçit", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON>lev<PERSON><PERSON>", "starrail": "starrail", "keyblade": "çelësi", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsherëpasherë", "novelasvisuales": "novela_visuale", "robloxbrasil": "robloxibrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamedates": "takimetegames", "mycandylove": "<PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "vetëmsepse3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "kthimiirekoneshëm", "gamstergaming": "gamstergaming", "dayofthetantacle": "ditatëkënduarit", "maniacmansion": "maniacmansion", "crashracing": "garazhbërje", "3dplatformers": "platformat3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "lojravjetështëkalura", "hellblade": "hellblade", "storygames": "lojraatregimtash", "bioware": "bioware", "residentevil6": "rezidentetete6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "lojtarët", "offmortisghost": "offmortisghost", "tinybunny": "tundërkuqe", "retroarch": "retroarch", "powerup": "forcohu", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventuratgrafike", "quickflash": "shpejtflash", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkadat", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "ishullicoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "njëbot<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animeluftëtarët2", "footballfusion": "futbollbashkimi", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalivdekur", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "grumbullturp", "simulator": "simulator", "symulatory": "simu<PERSON><PERSON><PERSON>", "speedrunner": "speedrunner", "epicx": "epikx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "sim<PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "kaositurban", "heavenlybodies": "trupatqiejore", "seum": "seum", "partyvideogames": "lojratëparty", "graveyardkeeper": "mbajtësitt<PERSON>var<PERSON><PERSON>sh", "spaceflightsimulator": "simulatoriifluturimevenëhapësirë", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hack<PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "videolojrash", "thewolfamongus": "ujkuarindeshtë", "truckingsimulator": "simulatoritëkamionëve", "horizonworlds": "horizonworlds", "handygame": "lojaime", "leyendasyvideojuegos": "legjendatëvevideojokëve", "oldschoolvideogames": "lojratëvjetra", "racingsimulator": "simulator<PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "këndimpop", "famitsu": "famitsu", "gatesofolympus": "portateolimpit", "monsterhunternow": "gjuetariimëmonstrave", "rebelstar": "ylli<PERSON><PERSON>", "indievideogaming": "indievideogaming", "indiegaming": "lojratindie", "indievideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "lojratindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "kullabufit", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projektl", "futureclubgames": "lojratëklubittës<PERSON>ardhmes", "mugman": "mugman", "insomniacgames": "lojratinsomnike", "supergiantgames": "lojratësupergjigante", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "shkencaemehapje", "backlog": "pritjet", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "përgjegj<PERSON>si<PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "gjuetariipërmbushjeve", "cityskylines": "linjatqytetit", "supermonkeyball": "supermajmuntopi", "deponia": "deponia", "naughtydog": "qeninekeq", "beastlord": "bosibeast", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "rrugakentuckyzero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "rezervatordedopamin", "staxel": "staxel", "videogameost": "ostluheshvideoje", "dragonsync": "dragonsync", "vivapiñata": "vivapinjatë", "ilovekofxv": "eduakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "p<PERSON><PERSON><PERSON><PERSON>", "berserk": "bërse<PERSON>", "baki": "baki", "sailormoon": "marinarëmo<PERSON>", "saintseiya": "shenjtorëjsheshtë", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeësad", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animeskalimi", "animewithplot": "animemeplot", "pesci": "peshku", "retroanime": "retroanime", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80sanimet", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "zakonimrat", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezoni1", "rapanime": "rapanime", "chargemanken": "burracharge", "animecover": "animecover", "thevisionofescaflowne": "vizioniiescaflowne", "slayers": "vrasësit", "tokyomajin": "tokyomajin", "anime90s": "anime90sat", "animcharlotte": "animçarlotë", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananafishi", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletmbuluarhanakokun", "bnha": "bnha", "hellsing": "helsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodiaej<PERSON>ю", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonbabe", "blacklagoon": "lagunabardhë", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "gje<PERSON>inc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "qentebungarvetëm", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "njëindeksmagjik", "sao": "sao", "blackclover": "luan<PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "njëgrushtnjeri", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8infinita", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spijtëfamilja", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioritetiwonderegg", "angelsofdeath": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesportive", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "lojaearwinit", "angelbeats": "engjellatالبشرة", "isekaianime": "isekaianimet", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "du<PERSON>ra", "prettycure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "biriitveriut", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "kullaeperëndive", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "se<PERSON><PERSON><PERSON>", "howtokeepamummy": "sixtemamën", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "tëbukurndheshëm", "martialpeak": "pjesëmartiale", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ël<PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "mierukoçan", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "v<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "grifit", "ririn": "rinovon", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "go<PERSON><PERSON>", "luci": "lucit", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "ma<PERSON>retëzaturnit", "dio": "dio", "sailorpluto": "navigator<PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "animeish", "chainsawman": "njeriumepajisjeprerëse", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyoavengjat", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "fru<PERSON><PERSON><PERSON>", "devilmancrybaby": "djalliqiuqbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "jetoshkënaqëse", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "tëpremtuaranukkthehen", "monstermanga": "monstermanga", "yourlieinapril": "gënjeshtrajoteprillit", "buggytheclown": "klounibuggy", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magjia", "deepseaprisoner": "burgosuri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "qytetilindjesitëvdekur", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "lojadarwinët", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "zemratpandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "luf<PERSON><PERSON><PERSON>", "cardcaptorsakura": "kartatcaptorsakura", "stolas": "stolas", "devilsline": "linsësedjallit", "toyoureternity": "përtyeeternitetin", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "periudhakurëblu", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "aleancesecret", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "shkruajtur", "bluelock": "bluelock", "goblinslayer": "shkatërruesitegoblinëve", "detectiveconan": "detektivkonan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirenight", "mugi": "mugi", "blueexorcist": "exorcistiipar<PERSON><PERSON><PERSON>", "slamdunk": "goditjepërfundimtare", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "familjespion", "airgear": "ajerarep", "magicalgirl": "vajzamagjike", "thesevendeadlysins": "këtë7mëkatetëvdekur", "prisonschool": "shkollakëteburgosjes", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON>llaselarte", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universianime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "tëvdekuritmefat", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromancë", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargjentinë", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonvrasësmegjuhën", "bloodlad": "gjakunushë", "goodbyeeri": "mirupaf<PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "yjetpërputhen", "romanceanime": "romancanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "mag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "shtëtëpëkinokuni", "recordragnarok": "regjistroragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "shkollëelevashdheveçantë", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "princiid<PERSON>ave", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "klasëvrasëse", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponeze", "animespace": "animespaci", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "shp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "mushkonjë", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "vajzapjeshkë", "cavalieridellozodiaco": "kavalierëtëzodiakut", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "klubigocaveyarichin", "dragonquestdai": "dragonquestdai", "heartofmanga": "zemraemanga", "deliciousindungeon": "<PERSON>ë<PERSON>j<PERSON><PERSON>nd<PERSON><PERSON>rr", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "regjistrimiiragnar<PERSON>t", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialisopërfortë", "overgeared": "<PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelieriaprëmëzishëtarëve", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaështjeta", "dropsofgod": "pikatngjallëse", "loscaballerosdelzodia": "loskavalieretezodiakut", "animeshojo": "animeshojo", "reverseharem": "haremikpërkundër", "saintsaeya": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "mësuesifantast<PERSON>ni<PERSON>", "gridman": "gridi", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gara5", "grandbluedreaming": "ëndrragrandblue", "bloodplus": "gjakplus", "bloodplusanime": "gjakplusanime", "bloodcanime": "gjaklike", "bloodc": "gjakp", "talesofdemonsandgods": "tregimetepërbindëshaveedheperëndive", "goreanime": "goreanime", "animegirls": "v<PERSON><PERSON><PERSON><PERSON>", "sharingan": "sharengan", "crowsxworst": "myshtakaxgjashtmëdhej", "splatteranime": "animepërmbysje", "splatter": "s<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "ngritjakaheroitështyllës", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animeispanjë", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "nëtrehuniësusume", "childrenofthewhales": "fëmijëtebalenave", "liarliar": "gënjeshtarigënjeshtar", "supercampeones": "superkampionët", "animeidols": "idolatani<PERSON>", "isekaiwasmartphone": "isekaishaishteinteligjent", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "vajzatmagjike", "callofthenight": "th<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "bakuganluftëtar", "bakuganbrawlers": "bakuganbrawlerët", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "prin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "puthjaeparadisë", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeuniversi", "persocoms": "persocoms", "omniscientreadersview": "pamjaelexuesveomnishjente", "animecat": "animekato", "animerecommendations": "propozimanime", "openinganime": "hapjenanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediaromantikepëradolescentët", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotetgiganter", "neongenesisevangelion": "neongjenezisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "luftetarëmobilboo", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilis<PERSON><PERSON><PERSON><PERSON><PERSON>", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "blekë", "deathnote": "shënimivdek<PERSON>s", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventuratëçuditshmeatijesë", "fullmetalalchemist": "alkimistiiplotëmetalik", "ghiaccio": "akull", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animeushtarake", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "yjetefoks", "ultraman": "ultraman", "salondelmanga": "salonipërmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "qytetianime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "njëkanë", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "aventureadigimon", "hxh": "hxh", "highschooldxd": "shkollasamoredxd", "goku": "goku", "broly": "broly", "shonenanime": "animegjeneratës", "bokunoheroacademia": "b<PERSON>akade<PERSON>un<PERSON><PERSON>", "jujustukaitsen": "jujustukaitsën", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "sulmonnëtitanët", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "lojaboo", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "trupatësurvey", "onepieceanime": "onepieceanime", "attaquedestitans": "sulmodesitans", "theonepieceisreal": "theonepieceështëivertetë", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efektijoyboy", "digimonstory": "historitëdigimon", "digimontamers": "digimontamers", "superjail": "superburg", "metalocalypse": "metalokalypsa", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "klubiouranhost", "flawlesswebtoon": "webtoonpaçortë", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprinceseve", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nicolas", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "shtrigaqëfluturon", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "thjeshtsepse", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "rrugaallsaintëve", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}