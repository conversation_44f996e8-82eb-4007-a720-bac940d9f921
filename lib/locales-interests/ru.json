{"2048": "2048", "mbti": "типличности", "enneagram": "эннеаграмма", "astrology": "астрология", "cognitivefunctions": "когнитивныефункции", "psychology": "психология", "philosophy": "философия", "history": "история", "physics": "физика", "science": "наука", "culture": "культура", "languages": "языки", "technology": "технологии", "memes": "мемы", "mbtimemes": "мемыличностей", "astrologymemes": "мемыастрологии", "enneagrammemes": "мемыэнеаграмм", "showerthoughts": "мысливдуше", "funny": "смешное", "videos": "видео", "gadgets": "гаджеты", "politics": "политика", "relationshipadvice": "советыпоотношениям", "lifeadvice": "советыжизни", "crypto": "крипто", "news": "новости", "worldnews": "мировыеновости", "archaeology": "археология", "learning": "обучение", "debates": "дебаты", "conspiracytheories": "теориизаговора", "universe": "вселенная", "meditation": "медитация", "mythology": "мифология", "art": "искусство", "crafts": "ремесла", "dance": "тан<PERSON><PERSON>", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "макияж", "beauty": "красота", "fashion": "мода", "singing": "пение", "writing": "писательство", "photography": "фотография", "cosplay": "косплей", "painting": "живопись", "drawing": "рисование", "books": "книги", "movies": "фильмы", "poetry": "поэзия", "television": "телевидение", "filmmaking": "кинопроизводство", "animation": "анимация", "anime": "аниме", "scifi": "научнаяфантастика", "fantasy": "фэнтези", "documentaries": "документальныефильмы", "mystery": "мистика", "comedy": "комедия", "crime": "криминальнаядрама", "drama": "драма", "bollywood": "болливуд", "kdrama": "кдрама", "horror": "ужасы", "romance": "мелодрама", "realitytv": "реалититв", "action": "боевики", "music": "музыка", "blues": "блюз", "classical": "классическая", "country": "кантри", "desi": "индийская", "edm": "эдм", "electronic": "электроннаямузыка", "folk": "фолк", "funk": "фанк", "hiphop": "хипхоп", "house": "<PERSON><PERSON><PERSON><PERSON>", "indie": "инди", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "кпоп", "latin": "латинская", "metal": "металл", "pop": "поп", "punk": "панк", "rnb": "рнб", "rap": "рэп", "reggae": "регги", "rock": "рок", "techno": "техно", "travel": "путешествия", "concerts": "концерты", "festivals": "фестивали", "museums": "музеи", "standup": "стэндап", "theater": "театр", "outdoors": "занятиянаоткрытомвоздухе", "gardening": "садоводство", "partying": "вечеринки", "gaming": "игры", "boardgames": "настольныеигры", "dungeonsanddragons": "подземельеидраконы", "chess": "шахматы", "fortnite": "фортнайт", "leagueoflegends": "лигалегенд", "starcraft": "старкр<PERSON><PERSON>т", "minecraft": "майнкра<PERSON>т", "pokemon": "покемоны", "food": "еда", "baking": "выпечка", "cooking": "готовка", "vegetarian": "вегетарианство", "vegan": "веганство", "birds": "птицы", "cats": "кошки", "dogs": "собаки", "fish": "рыбы", "animals": "животные", "blacklivesmatter": "блм", "environmentalism": "защитаокружающейсреды", "feminism": "феминизм", "humanrights": "правачеловека", "lgbtqally": "лгбт", "stopasianhate": "нетрасизму", "transally": "транссексуалы", "volunteering": "волонтерство", "sports": "спорт", "badminton": "бадминтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велосипед", "fitness": "фитнес", "football": "футбол", "golf": "гольф", "gym": "спортзал", "gymnastics": "гимнастика", "hockey": "хоккей", "martialarts": "боевыеискусства", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "бег", "skateboarding": "скейтбординг", "skiing": "лыжи", "snowboarding": "сноуборд", "surfing": "серфинг", "swimming": "плавание", "tennis": "теннис", "volleyball": "волейбол", "weightlifting": "тяжелаяатлетика", "yoga": "йога", "scubadiving": "да<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "хайкинг", "capricorn": "козерог", "aquarius": "водолей", "pisces": "рыбы", "aries": "овен", "taurus": "телец", "gemini": "близнецы", "cancer": "рак", "leo": "лев", "virgo": "дева", "libra": "весы", "scorpio": "скорпион", "sagittarius": "стрелец", "shortterm": "краткосрочка", "casual": "повседневный", "longtermrelationship": "долговременныеотношения", "single": "одинокий", "polyamory": "полиамория", "enm": "этичнаянемоногамия", "lgbt": "лгбт", "lgbtq": "лгбтқ", "gay": "гей", "lesbian": "лесбиянка", "bisexual": "бисексуал", "pansexual": "пансексуал", "asexual": "асексуальный", "reddeadredemption2": "реддедредемпшн2", "dragonage": "драконийвек", "assassinscreed": "ассасинскрид", "saintsrow": "святыйряд", "danganronpa": "данганронпа", "deltarune": "дел<PERSON><PERSON><PERSON><PERSON><PERSON>", "watchdogs": "хранители", "dislyte": "ди<PERSON><PERSON><PERSON><PERSON>т", "rougelikes": "ройглайки", "kingsquest": "королевскоеприключение", "soulreaver": "душевныйпохититель", "suikoden": "суйкоден", "subverse": "сабверс", "legendofspyro": "легендаошпиона", "rouguelikes": "ругилки", "syberia": "сибирь", "rdr2": "рдер2", "spyrothedragon": "спайротедракон", "dragonsdogma": "драконьегруди", "sunsetoverdrive": "закатнадвижении", "arkham": "арк<PERSON>ем", "deusex": "девусекс", "fireemblemfates": "огоньэмблемапредназначение", "yokaiwatch": "йокайвотч", "rocksteady": "рокстеди", "litrpg": "литрпг", "haloinfinite": "haloбесконечность", "guildwars": "гильдиевойны", "openworld": "открытыймир", "heroesofthestorm": "герои<PERSON><PERSON><PERSON><PERSON><PERSON>ов", "cytus": "ситус", "soulslike": "душеподобный", "dungeoncrawling": "подземельныйрудокоп2023", "jetsetradio": "жизньнаколесах", "tribesofmidgard": "племенамидгарда", "planescape": "планосфера", "lordsoftherealm2": "лордыподземелья2", "baldursgate": "балдурсгейт", "colorvore": "цветорор", "medabots": "медаботы", "lodsoftherealm2": "многореалмов2", "patfofexile": "патфоэксайл", "immersivesims": "иммерсивныесимы", "okage": "окейге", "juegoderol": "игралоль", "witcher": "ведьмак", "dishonored": "опозоренный", "eldenring": "элденринг", "darksouls": "дарксоу<PERSON>с", "kotor": "которая", "wynncraft": "винкрафт", "witcher3": "ведьмак3", "fallout": "падение", "fallout3": "фоллаут3", "fallout4": "фоллаут4", "skyrim": "скайрим", "elderscrolls": "старшиескроллы", "modding": "моддинг", "charactercreation": "созданиеперсонажа", "immersive": "иммерсивный", "falloutnewvegas": "фоллаутньювегас", "bioshock": "биошок", "omori": "омори", "finalfantasyoldschool": "финалфэнтезиолдскул", "ffvii": "ffvii", "ff6": "фф6", "finalfantasy": "финальнаяфантазия", "finalfantasy14": "финалка14", "finalfantasyxiv": "финалфэнтези14", "ff14": "ff14", "ffxiv": "ффxiv", "ff13": "фф13", "finalfantasymatoya": "финальнаяфантазияматоя", "lalafell": "лала<PERSON>елл", "dissidia": "диссидия", "finalfantasy7": "финалфэнтези7", "ff7": "фф7", "morbidmotivation": "мотивалкосмертие", "finalfantasyvii": "финалфэнтезивиi", "ff8": "фф8", "otome": "отоме", "suckerforlove": "бешеныйпоходам️", "otomegames": "отомеигры", "stardew": "стардью", "stardewvalley": "стардьювэли", "ocarinaoftime": "окаринавремени", "yiikrpg": "йиикр<PERSON><PERSON>", "vampirethemasquerade": "вампирымаскарад", "dimension20": "димен<PERSON>н20", "gaslands": "газостраны", "pathfinder": "искатели", "pathfinder2ndedition": "путеводитель2еиздание", "shadowrun": "теневойзабег", "bloodontheclocktower": "кровьначасы", "finalfantasy15": "финальнаяфантазия15", "finalfantasy11": "финалфэнтези11", "finalfantasy8": "финалфэнтези8", "ffxvi": "ffxvi", "lovenikki": "лавеникки", "drakengard": "дра<PERSON><PERSON><PERSON>га<PERSON>д", "gravityrush": "гравитационныйбуст", "rpg": "рпг", "dota2": "дота2", "xenoblade": "ксеноблейд", "oneshot": "одинвыстрел", "rpgmaker": "rpgmaker", "osrs": "оsрs", "overlord": "повелитель", "yourturntodie": "твойочередьумереть", "persona3": "персона3", "rpghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elderscrollsonline": "старшиескроллыонлайн", "reka": "река", "honkai": "хонкай", "marauders": "мародеры", "shinmegamitensei": "шинмегамитенсей", "epicseven": "эпик7", "rpgtext": "рпгтекст", "genshin": "ген<PERSON>ин", "eso": "есо", "diablo2": "диабло2", "diablo2lod": "диабло2лод", "morrowind": "моровинд", "starwarskotor": "звездныевойныкотор", "demonsouls": "демонсоулам", "mu": "мю", "falloutshelter": "убежищесъединился", "gurps": "гурпс", "darkestdungeon": "темныеподземелья", "eclipsephase": "закатпериода", "disgaea": "ди<PERSON><PERSON><PERSON><PERSON>", "outerworlds": "внешниемиры", "arpg": "арпг", "crpg": "крпг", "bindingofisaac": "связываемисаака", "diabloimmortal": "диаблоиммортал", "dynastywarriors": "дорадиксармий", "skullgirls": "черепашки", "nightcity": "ночнойгород", "hogwartslegacy": "наследие<PERSON><PERSON><PERSON><PERSON>ов", "madnesscombat": "безумныебои", "jaggedalliance2": "зазубренныйальянс2", "neverwinter": "невервинтер", "road96": "дорога96", "vtmb": "втмб", "chimeraland": "химернаяземля", "homm3": "гом3", "fe3h": "фе3х", "roguelikes": "рогалики", "gothamknights": "гwachtenknights", "forgottenrealms": "забытоемир", "dragonlance": "драконийчертеж", "arenaofvalor": "аренавалора", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "тунтаун", "childoflight": "дитясвета", "aq3d": "ак3д", "mogeko": "могеко", "thedivision2": "дивизия2", "lineage2": "линия2", "digimonworld": "дид<PERSON>и<PERSON>о<PERSON>нмир", "monsterrancher": "монстрферма", "ecopunk": "экопанк", "vermintide2": "верминтайд2", "xeno": "ксенo", "vulcanverse": "вулканвесна", "fracturedthrones": "разбитыепрестолы", "horizonforbiddenwest": "горизонтзапретныйзапад", "twewy": "твевай", "shadowpunk": "шадоу<PERSON>анк", "finalfantasyxv": "финалфэнтэзиксв", "everoasis": "эверайзис", "hogwartmystery": "гаррипоттертайны", "deltagreen": "доставьзеленый", "diablo": "диабло", "diablo3": "диабло3", "diablo4": "диабло4", "smite": "смрад", "lastepoch": "последняяэпоха", "starfinder": "компасзвезд", "goldensun": "золотоесолнце", "divinityoriginalsin": "божественногреха", "bladesinthedark": "лезвиявтемноте", "twilight2000": "сумерки2000", "sandevistan": "сандевистань", "cyberpunk": "киб<PERSON><PERSON><PERSON>анк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "киберпанккрасный", "dragonballxenoverse2": "драгонболлксеноверс2", "fallenorder": "падшийпорядок", "finalfantasyxii": "финалфэнтези12", "evillands": "злыеземли", "genshinimact": "ген<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>к", "aethyr": "эйтир", "devilsurvivor": "дьяволдоблести", "oldschoolrunescape": "олдскулрунескейп", "finalfantasy10": "финалфантази10", "anime5e": "аниме5е", "divinity": "божественность", "pf2": "пф2", "farmrpg": "фермерпг", "oldworldblues": "старыймирпечаль", "adventurequest": "приключенческийпоиск", "dagorhir": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "ролевки", "roleplayinggames": "ролевыеигры", "finalfantasy9": "финалфэнтези9", "sunhaven": "солярий", "talesofsymphonia": "историисимфонии", "honkaistarrail": "хонкайста<PERSON><PERSON><PERSON><PERSON>л", "wolong": "волонг", "finalfantasy13": "финалфэнтези13", "daggerfall": "дагарфолл", "torncity": "порванныйгород", "myfarog": "мойфарог", "sacredunderworld": "сакреданниймир", "chainedechoes": "цепочныеотголоски", "darksoul": "теневойдуши", "soulslikes": "душевныеигры", "othercide": "другойцид", "mountandblade": "мечиидоски", "inazumaeleven": "иназумаленевен", "acvalhalla": "acvalhalla", "chronotrigger": "хронотриггер", "pillarsofeternity": "столпывечности", "palladiumrpg": "палладиоrpg", "rifts": "разломы", "tibia": "тибия", "thedivision": "деление", "hellocharlotte": "приветшарлотта", "legendofdragoon": "легендадрагуна", "xenobladechronicles2": "ксеноблейдхроники2", "vampirolamascarada": "вампироламаскарада", "octopathtraveler": "октапутьпутешественника", "afkarena": "афкарена", "werewolftheapocalypse": "волчонокапокалипсиса", "aveyond": "авейонд", "littlewood": "<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "детиморты", "engineheart": "движоксердца", "fable3": "сказка3", "fablethelostchapter": "фабулаутеряннойглавы", "hiveswap": "хайвсвап", "rollenspiel": "ролевуха", "harpg": "хар<PERSON>г", "baldursgates": "бальдурсгейтс", "edeneternal": "эдэнвечный", "finalfantasy16": "финалфэнтези16", "andyandleyley": "андри<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ff15": "фф15", "starfield": "звёздноеполе", "oldschoolrevival": "ретровозрождение", "finalfantasy12": "финалфэнтези12", "ff12": "фф12", "morkborg": "моркборг", "savageworlds": "смешныемирки", "diabloiv": "ди<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pve": "пве", "kingdomheart1": "королевствосердец1", "ff9": "фф9", "kingdomheart2": "королевствосердец2", "darknessdungeon": "темницапьянства", "juegosrpg": "игрыприбг", "kingdomhearts": "королевствосердец", "kingdomheart3": "королевствосердец3", "finalfantasy6": "финалфэнтези6", "ffvi": "ффви", "clanmalkavian": "кланмалькавиан", "harvestella": "урожайнаяжизнь", "gloomhaven": "глумхавен", "wildhearts": "дикосердца", "bastion": "бастион", "drakarochdemoner": "дракарочдемонер", "skiesofarcadia": "небапарадиа", "shadowhearts": "теневыесердца", "nierreplicant": "ниеррепlicant", "gnosia": "гнозя", "pennyblood": "копейкакровь", "breathoffire4": "огоньвдыхании4", "mother3": "мама3", "cyberpunk2020": "киберпанк2020", "falloutbos": "фоллаутбос", "anothereden": "другойэдем", "roleplaygames": "ролевыеигры", "roleplaygame": "ролеваяигра", "fabulaultima": "фабулаультима", "witchsheart": "ведьминосердце", "harrypottergame": "гаррипоттеригра", "pathfinderrpg": "путеводительrpg", "pathfinder2e": "путеводитель2е", "vampirilamasquerade": "вампирскаямаскараднаяночь", "dračák": "дра<PERSON><PERSON>к", "spelljammer": "спеллджаммер", "dragonageorigins": "драгонэйджориджинс", "chronocross": "хронокросс", "cocttrpg": "коктднрпг", "huntroyale": "охотничьаякоролевскаябитва", "albertodyssey": "альбертоодиссея", "monsterhunterworld": "монстробойвмире", "bg3": "бг3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "рпгфорум", "shadowheartscovenant": "ковенсердцатени", "bladesoul": "блейдсоул", "baldursgate3": "балдурскиеворота3", "kingdomcome": "королевствопришло", "awplanet": "авпланета", "theworldendswithyou": "мирзакончитсястобой", "dragalialost": "драгалияпотеряна", "elderscroll": "elderс<PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "умирающийсвет2", "finalfantasytactics": "финалфэнтезитактика", "grandia": "грандия", "darkheresy": "темнаяересь", "shoptitans": "магазинныетитаны", "forumrpg": "форумрпг", "golarion": "голарион", "earthmagic": "землянаямагия", "blackbook": "чернаякнига", "skychildrenoflight": "небесныедетисвета", "gryrpg": "грырпг", "sacredgoldedition": "священноезолотоеиздание", "castlecrashers": "замокрыдят", "gothicgame": "готическаяигра", "scarletnexus": "скарлетнексус", "ghostwiretokyo": "призрачнаянитьтокио", "fallout2d20": "фоллаути2д20", "gamingrpg": "игровыерпг", "prophunt": "прохождение", "starrails": "звездныепути", "cityofmist": "городтумана", "indierpg": "индиrpg", "pointandclick": "точивайнасмотрим", "emilyisawaytoo": "емиливдругемного", "emilyisaway": "эмилитакуда", "indivisible": "неделимый", "freeside": "фриса<PERSON>д", "epic7": "эпик7", "ff7evercrisis": "ff7навсегдакризис", "xenogears": "ксеногиры", "megamitensei": "мегамитен<PERSON>эй", "symbaroum": "симбарум", "postcyberpunk": "посткиберпанк", "deathroadtocanada": "дорожкасмертивканаду", "palladium": "пал<PERSON><PERSON><PERSON><PERSON>", "knightjdr": "рыцарьждр", "monsterhunter": "монстрохотник", "fireemblem": "огненноефасадное", "genshinimpact": "геншинийудар", "geosupremancy": "гейосупремия", "persona5": "персона5", "ghostoftsushima": "привидениецусимы", "sekiro": "секиро", "monsterhunterrise": "охотанаменстр<PERSON>чков", "nier": "нир", "dothack": "дотхак", "ys": "<PERSON><PERSON><PERSON>", "souleater": "пожирательдуш", "fatestaynight": "фейтстэйнайт", "etrianodyssey": "этрианодиссея", "nonarygames": "нoнapыигpы", "tacticalrpg": "тактическаярпг", "mahoyo": "мах<PERSON><PERSON>", "animegames": "анимеигры", "damganronpa": "дамганронпа", "granbluefantasy": "гранблуфэнтези", "godeater": "едокбогов", "diluc": "дилук", "venti": "венти", "eternalsonata": "вечнаясоната", "princessconnect": "принцессасвязь", "hexenzirkel": "ведьмовскружок", "cristales": "кристаллики", "vcs": "всех", "pes": "пес", "pocketsage": "карманныймудрец", "valorant": "валерант", "valorante": "валора́нт", "valorantindian": "валора́нтинди́я", "dota": "дота", "madden": "мэдден", "cdl": "cdl", "efootbal": "эфутбол", "nba2k": "нба2к", "egames": "игры", "fifa23": "фифа23", "wwe2k": "ввэ2к", "esport": "киберспорт", "mlg": "млг", "leagueofdreamers": "лиг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "фифа14", "midlaner": "мид<PERSON><PERSON>р", "efootball": "эфутбол", "dreamhack": "дримхак", "gaimin": "геймин", "overwatchleague": "лигадапереполнения", "cybersport": "киберспорт", "crazyraccoon": "бешеныйенот", "test1test": "тест1тест", "fc24": "фк24", "riotgames": "райотгеймс", "eracing": "эрейсинг", "brasilgameshow": "бразильскоевыставлениегеймов", "valorantcompetitive": "валорентконкуренция", "t3arena": "t3арена", "valorantbr": "валрант<PERSON>р", "csgo": "ксго", "tf2": "тф2", "portal2": "портал2", "halflife": "половиннаяжизнь", "left4dead": "left4dead", "left4dead2": "лев4мертвых2", "valve": "клапан", "portal": "портал", "teamfortress2": "тимфортресс2", "everlastingsummer": "вечноеплетениелето", "goatsimulator": "козосимулятор", "garrysmod": "гаррисмод", "freedomplanet": "свободнаяпланета", "transformice": "трансформайс", "justshapesandbeats": "простоформыпимы", "battlefield4": "битва4", "nightinthewoods": "ночьвлесу", "halflife2": "половинажизни2", "hacknslash": "хакниисечи", "deeprockgalactic": "глубокаяскалагалактики", "riskofrain2": "рискдождя2", "metroidvanias": "метроидвании", "overcooked": "пережаренный", "interplanetary": "межпланетный", "helltaker": "адоходилец", "inscryption": "инскрипшн", "7d2d": "7д2д", "deadcells": "мертвыеклетки", "nierautomata": "ниеравтомата", "gmod": "гмод", "dwarffortress": "крепостьгномов", "foxhole": "фоксхолл", "stray": "брошенный", "battlefield": "поле_боя", "battlefield1": "поле_битвы1", "swtor": "свтор", "fallout2": "фоллаут2", "uboat": "убот", "eyeb": "глазик", "blackdesert": "чёрнаяпустыня", "tabletopsimulator": "симуляторнастольныхигр", "partyhard": "тусуйжестко", "hardspaceshipbreaker": "тяжелыйкосмическийломайщик", "hades": "<PERSON>а<PERSON><PERSON><PERSON>", "gunsmith": "гончар", "okami": "оками", "trappedwithjester": "запертосшутником", "dinkum": "динкум", "predecessor": "предшественник", "rainworld": "мирдождя", "cavesofqud": "пещерыкуда", "colonysim": "колониясим", "noita": "нэйта", "dawnofwar": "рассветвойны", "minionmasters": "миньонмастера", "grimdawn": "мрачныйрассвет", "darkanddarker": "тёмноатемнее", "motox": "мотох", "blackmesa": "блекмеза", "soulworker": "душевныйработник", "datingsims": "симуляторы_свиданий", "yaga": "яга", "cubeescape": "кубосбег", "hifirush": "хай<PERSON><PERSON><PERSON><PERSON>с", "svencoop": "свенкуп", "newcity": "новыйгород", "citiesskylines": "городскиепейзажи", "defconheavy": "дефконхэви", "kenopsia": "кенопсия", "virtualkenopsia": "виртуальнаякенопсия", "snowrunner": "снегобегун", "libraryofruina": "библиотекападений", "l4d2": "л4д2", "thenonarygames": "нормальныеигры", "omegastrikers": "омегаудары", "wayfinder": "искательнаправлений", "kenabridgeofspirits": "кенабрид<PERSON><PERSON><PERSON><PERSON>ов", "placidplasticduck": "плоскийпластиковыйутенок", "battlebit": "бат<PERSON><PERSON><PERSON><PERSON>т", "ultimatechickenhorse": "крутойпетухноздратень", "dialtown": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "улыбнисьдляменя", "catnight": "котосон", "supermeatboy": "супермясник", "tinnybunny": "тиннибанни", "cozygrove": "уютныйостров", "doom": "деньги", "callofduty": "зовдолга", "callofdutyww2": "колдоводидупдва", "rainbow6": "радужные6", "apexlegends": "апекслегендс", "cod": "код", "borderlands": "границы", "pubg": "пабг", "callofdutyzombies": "зомбивызовадолга", "apex": "апекс", "r6siege": "r6осада", "megamanx": "мегаменикс", "touhou": "тухоу", "farcry": "далекоплачь", "farcrygames": "дальниекрикиигры", "paladins": "паладины", "earthdefenseforce": "защитаземли", "huntshowdown": "охотанаустроение", "ghostrecon": "призрачноеуничтожение", "grandtheftauto5": "грантфефтаавто5", "warz": "войнушка", "sierra117": "сиерра117", "dayzstandalone": "dayzstandalone", "ultrakill": "ультраубийство", "joinsquad": "присоединяйсякотряду", "echovr": "эховр", "discoelysium": "дискоэлизий", "insurgencysandstorm": "инсургенциявпесках", "farcry3": "farcry3", "hotlinemiami": "горячаялиниямайами", "maxpayne": "макспейн", "hitman3": "хитмэн3", "r6s": "r6s", "rainbowsixsiege": "raindropsixsiege", "deathstranding": "проводниксмерти", "b4b": "б4б", "codwarzone": "кодоварзон", "callofdutywarzone": "зовдолгаварзон", "codzombies": "крудемоны", "mirrorsedge": "зеркаладорог", "divisions2": "divisions2", "killzone": "киллзона", "helghan": "хелган", "coldwarzombies": "холоднаявойназомби", "metro2033": "метро2033", "metalgear": "ме<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acecombat": "аскомбат", "crosscode": "кросскод", "goldeneye007": "золотойглаз007", "blackops2": "блэкопс2", "sniperelite": "снайперэлит", "modernwarfare": "современнаявойна", "neonabyss": "неонобсуждение", "planetside2": "планетасплошь2", "mechwarrior": "мехвоин", "boarderlands": "пограничья", "owerwatch": "оверквачу", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "сбежатьизтаркова", "metalslug": "металслуг", "primalcarnage": "первобытноепожирание", "worldofwarships": "мирвоенныхкораблей", "back4blood": "back4blood", "warframe": "ворфрейм", "rainbow6siege": "райд6осада", "xcom": "иксом", "hitman": "киллер", "masseffect": "массеэффект", "systemshock": "системныйшок", "valkyriachronicles": "валькирияхроники", "specopstheline": "спецоперациялиниягероя", "killingfloor2": "убийственныйпол", "cavestory": "пещернаяистория", "doometernal": "дюмавечный", "centuryageofashes": "векпепла", "farcry4": "farcry4", "gearsofwar": "шестернивойны", "mwo": "mwo", "division2": "дивижн2", "tythetasmaniantiger": "тайфунтасманийскийтигр", "generationzero": "поколениеноль", "enterthegungeon": "входвпушку", "jakanddaxter": "джаканддекстер", "modernwarfare2": "современнаявойна2", "blackops1": "черныеоперации1", "sausageman": "сосисочныйчеловек", "ratchetandclank": "ратчетипанк", "chexquest": "чексквест", "thephantompain": "фантомныеболи", "warface": "военныйликвидатор", "crossfire": "перекрестныйогонь", "atomicheart": "атомноесердце", "blackops3": "черныеоперации3", "vampiresurvivors": "вампирскиевыживатели", "callofdutybatleroyale": "коллофдьютибатлрояль", "moorhuhn": "морскиецуки", "freedoom": "свобода", "battlegrounds": "полебоя", "frag": "фраг", "tinytina": "маленькаятинка", "gamepubg": "играйpubg", "necromunda": "некромунда", "metalgearsonsoflibert": "металлгирсынысвободы", "juegosfps": "фпсигры", "convertstrike": "конвертбой", "warzone2": "война2", "shatterline": "шэтте<PERSON><PERSON><PERSON><PERSON>н", "blackopszombies": "блекопсзомби", "bloodymess": "кроваваябессмыслится", "republiccommando": "республиканскийкоммандос", "elitedangerous": "элитныйопасный", "soldat": "солдат", "groundbranch": "грa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "команда", "destiny1": "достигни1", "gamingfps": "геймингфпс", "redfall": "редфолл", "pubggirl": "пабгдевчонка", "worldoftanksblitz": "мир<PERSON><PERSON><PERSON><PERSON>овблиц", "callofdutyblackops": "колддутиблекопс", "enlisted": "встроен", "farlight": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "фарлайт84", "splatoon3": "сплатун3", "armoredcore": "бронированныйядро", "pavlovvr": "павловвр", "xdefiant": "xdefiant", "tinytinaswonderlands": "маленькиемирытинитины", "halo2": "галочка2", "payday2": "пэйдей2", "cs16": "кс16", "pubgindonesia": "пубгиндонезия", "pubgukraine": "пубгукрейт", "pubgeu": "пабгеу", "pubgczsk": "пубгцзск", "wotblitz": "вотблиц", "pubgromania": "пубгромания", "empyrion": "емпирион", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "титанпад2", "soapcod": "мылокод", "ghostcod": "призраккод", "csplay": "ксиплей", "unrealtournament": "нереальныйтурнир", "callofdutydmz": "вызовдолгадмз", "gamingcodm": "игрыкодм", "borderlands2": "границы2", "counterstrike": "контрстрайк", "cs2": "кс2", "pistolwhip": "пистолетпощёчина", "callofdutymw2": "коллоядудтимв2", "quakechampions": "квейкчемпионы", "halo3": "хало3", "halo": "гало", "killingfloor": "убийственнополуэтажье", "destiny2": "дестини2", "exoprimal": "эксопраймал", "splintercell": "снайперскаяячейка", "neonwhite": "неонбелый", "remnant": "остатки", "azurelane": "азурнаялавка", "worldofwar": "мирвойны", "gunvolt": "гунвольт", "returnal": "возвращение", "halo4": "хало4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "теневойчувак", "quake2": "квака2", "microvolts": "микроволны", "reddead": "реддэд", "standoff2": "стендоф2", "harekat": "харекат", "battlefield3": "баттлфилд3", "lostark": "лостарк", "guildwars2": "гильдиявойны2", "fallout76": "фоллаут76", "elsword": "элсворд", "seaofthieves": "морера<PERSON>б<PERSON>йников", "rust": "ржавчина", "conqueronline": "конкерон<PERSON><PERSON><PERSON>н", "dauntless": "бесстрашный", "warships": "боевыекорабли", "dayofdragons": "ден<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "вартандер", "flightrising": "взлетполетов", "recroom": "рекрум", "legendsofruneterra": "легендырунетеры", "pso2": "псо2", "myster": "мистер", "phantasystaronline2": "фантазиязвёздныетехнологии2", "maidenless": "бездевочка", "ninokuni": "нинокуни", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "зачеркнуть", "agario": "агарио", "secondlife": "втораяжизнь", "aion": "айон", "toweroffantasy": "башнсказок", "netplay": "нетплей", "everquest": "вечныйпоиск", "metin2": "метин2", "gtaonline": "gtaон<PERSON><PERSON><PERSON>н", "ninokunicrossworld": "нинокунитрансвсемир", "reddeadonline": "реддедонлайн", "superanimalroyale": "суперанималрояль", "ragnarokonline": "рагарноконлайн", "knightonline": "рыцар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "gw2": "гв2", "tboi": "тбой", "thebindingofisaac": "заключениеисаака", "dragonageinquisition": "драгонэйджинквизитор", "codevein": "кодоваяжилка", "eveonline": "евеон<PERSON><PERSON><PERSON>н", "clubpenguin": "клубпингвин", "lotro": "лотро", "wakfu": "вакуф", "scum": "ублюдки", "newworld": "новыймир", "blackdesertonline": "чернаяпустыняонлайн", "multiplayer": "мультиплеер", "pirate101": "пираты101", "honorofkings": "честькоролей", "fivem": "фаивем", "starwarsbattlefront": "звёздныевойнобойня", "karmaland": "кармаленд", "ssbu": "ssbu", "starwarsbattlefront2": "батлфронт2", "phigros": "фигрос", "mmo": "ммо", "pokemmo": "покеммо", "ponytown": "понита<PERSON>н", "3dchat": "3dчат", "nostale": "ностальгия", "tauriwow": "тауривоу", "wowclassic": "вауклассика", "worldofwarcraft": "мирварывойны", "warcraft": "варкрафт", "wotlk": "вовкч", "runescape": "ранскейп", "neopets": "неопеты", "moba": "моба", "habbo": "хаббо", "archeage": "аркейдж", "toramonline": "торомон<PERSON>айн", "mabinogi": "мабиноги", "ashesofcreation": "пепелтворения", "riotmmo": "райотммо", "silkroad": "шелковыйпуть", "spiralknights": "спиральныерыцари", "mulegend": "мулегенд", "startrekonline": "стартреконлайн", "vindictus": "вендиктус", "albiononline": "альбион<PERSON>нлайн", "bladeandsoul": "блейдандсоул", "evony": "эвони", "dragonsprophet": "драконыпророка", "grymmo": "гриммо", "warmane": "warmane", "multijugador": "мультиплеер", "angelsonline": "ангелычоноконлайн", "lunia": "луния", "luniaz": "лун<PERSON><PERSON>", "idleon": "айдлон", "dcuniverseonline": "дкуниверсон<PERSON><PERSON><PERSON>н", "growtopia": "гровтопия", "starwarsoldrepublic": "звёздныевйныстараярепублика", "grandfantasia": "грандфантазия", "blueprotocol": "синийпротокол", "perfectworld": "идеальныймир", "riseonline": "взлетайвонлайн", "corepunk": "корпунк", "adventurequestworlds": "приключенческийпоискмиров", "flyforfun": "летайдляудовольствия", "animaljam": "аним<PERSON><PERSON><PERSON><PERSON><PERSON>м", "kingdomofloathing": "королевствосоплей", "cityofheroes": "городгероев", "mortalkombat": "морталкомбат", "streetfighter": "уличныйбоец", "hollowknight": "пустошныйрыцарь", "metalgearsolid": "металгеарсолид", "forhonor": "зачесть", "tekken": "теккен", "guiltygear": "виноваттыигандер", "xenoverse2": "ксеновёрс2", "fgc": "фгц", "streetfighter6": "стритфайтер6", "multiversus": "мультивселенная", "smashbrosultimate": "смэшбросультимейт", "soulcalibur": "соукалибур", "brawlhalla": "бравлхолла", "virtuafighter": "виртуальныйбоец", "streetsofrage": "улицыраза", "mkdeadlyalliance": "мксмертельныйсоюз", "nomoreheroes": "большенетгероев", "mhr": "мхр", "mortalkombat12": "морталкомбат12", "thekingoffighters": "корольбитв", "likeadragon": "какдракон", "retrofightinggames": "ретрофайтингигры", "blasphemous": "богохульно", "rivalsofaether": "соперникиэфира", "persona4arena": "персона4арена", "marvelvscapcom": "марвелпротивкэпком", "supersmash": "суперудар", "mugen": "муген", "warofthemonsters": "войнамонстров", "jogosdeluta": "игрыборьбы", "cyberbots": "киберботы", "armoredwarriors": "бронетанки", "finalfight": "последняябитва", "poweredgear": "силовоепринадлежности", "beatemup": "бьемдругдруга", "blazblue": "блазблу", "mortalkombat9": "морталкомбат9", "fightgames": "бойцовскиегames", "killerinstinct": "килл<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "kingoffigthers": "корол<PERSON>б<PERSON><PERSON>цов", "ghostrunner": "призрачныйбегун", "chivalry2": "рыцарство2", "demonssouls": "демонссолз", "blazbluecrosstag": "блазблюкросстаг", "blazbluextagbattle": "блазблюэкстагбаттл", "blazbluextag": "блазблюэкстаг", "guiltygearstrive": "гильтигэрстрив", "hollowknightsequel": "сиквелхоллоукнайт", "hollowknightsilksong": "пустышкарыцаряшелк", "silksonghornet": "силксонгхорнет", "silksonggame": "сильксонггейм", "silksongnews": "новостисилксонг", "silksong": "силксонг", "undernight": "ночью", "typelumina": "типелумина", "evolutiontournament": "турнирэволюции", "evomoment": "эвомомент", "lollipopchainsaw": "лолиопечкаусечка", "dragonballfighterz": "драгонболлфайтерз", "talesofberseria": "сказкиоберсерии", "bloodborne": "кровопускание", "horizon": "горизонт", "pathofexile": "путьизгнания", "slimerancher": "слимеренчер", "crashbandicoot": "крашбандикут", "bloodbourne": "кровавыйпадение", "uncharted": "незначительные", "horizonzerodawn": "горизонт0рассвет", "ps4": "пс4", "ps5": "пс5", "spyro": "спайро", "playstationplus": "плэйстейшенплюс", "lastofus": "последниеизнас", "infamous": "безумный", "playstationbuddies": "плейстейшендружки", "ps1": "пс1", "oddworld": "странныймир", "playstation5": "плейстейшон5", "slycooper": "слайкупер", "psp": "псп", "rabbids": "раббиды", "splitgate": "сплитгейт", "persona4": "персона42", "hellletloose": "адвыпустили", "gta4": "gta4", "gta": "gta", "roguecompany": "бунтарскаякомпания", "aisomniumfiles": "файлыайсомниум", "gta5": "гта5", "gtasanandreas": "гтасанандреас", "godofwar": "богвойны", "gris": "грис", "trove": "сокровища", "detroitbecomehuman": "детройтстановчеловеком", "beatsaber": "бит<PERSON><PERSON><PERSON>б<PERSON>р", "rimworld": "риммир", "stellaris": "стелл<PERSON><PERSON><PERSON>с", "ps3": "пс3", "untildawn": "дорассвета", "touristtrophy": "туристическийтрофей", "lspdfr": "лспдфр", "shadowofthecolossus": "теньколосса", "crashteamracing": "крэштимрейсинг", "fivepd": "пятьпд", "tekken7": "теккен7", "devilmaycry": "дьяволможетплакать", "devilmaycry3": "дьяволплачет3", "devilmaycry5": "дьяволможетплакать5", "ufc4": "ufc4", "playingstation": "плейстейшен", "samuraiwarriors": "самурайскиевоины", "psvr2": "псвр2", "thelastguardian": "последнийстраж", "soulblade": "душевныйклинок", "gta5rp": "gta5рп", "gtav": "гта5", "playstation3": "плейстейшен3", "manhunt": "охотанамужчин", "gtavicecity": "гтавайсити", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "тенисердца2ковенант", "pcsx2": "pcsx2", "lastguardian": "последнийстраж", "xboxone": "иксбоксофан", "forza": "форца", "cd": "сд", "gamepass": "геймпасс", "armello": "арместо", "partyanimal": "вечеринкаживотное", "warharmmer40k": "вкровавойгре40к", "fightnightchampion": "ночьбоячемпионов", "psychonauts": "психонавты", "mhw": "mhw", "princeofpersia": "принцперсии", "theelderscrollsskyrim": "скyr<PERSON><PERSON>л<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "pantarhei": "пан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrolls": "древниесвитки", "gxbox": "gxbox", "battlefront": "фронтбитвы", "dontstarvetogether": "недопускайголодвместе", "ori": "ори", "spelunky": "спелунки", "xbox1": "xbox1", "xbox360": "иксбокс360", "starbound": "звездоброд", "xboxonex": "игроваябуква", "forzahorizon5": "forzahorizon5", "skate3": "скейт3", "houseflipper": "дома_перевертыши", "americanmcgeesalice": "американскаялуизаальиса", "xboxs": "игровые_приключения", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxсерия", "r6xbox": "r6xbox", "leagueofkingdoms": "лигацарств", "fable2": "фейбл2", "xboxgamepass": "xboxgamepass", "undertale": "ундертейл", "trashtv": "мусортв", "skycotl": "небоскотл", "erica": "эрика", "ancestory": "предки", "cuphead": "капхед", "littlemisfortune": "маленькаянеудача", "sallyface": "саллилицо", "franbow": "франбоу", "monsterprom": "монстрпром", "projectzomboid": "проектзомбоид", "ddlc": "ддлц", "motos": "мото", "outerwilds": "внешниедикиеземли", "pbbg": "пббг", "anshi": "ан<PERSON>и", "cultofthelamb": "культаягненка", "duckgame": "утинаяигра", "thestanleyparable": "стенлиипарабола", "towerunite": "башнямоя", "occulto": "оккульто", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "удовлетворительно", "pluviophile": "плювиофил", "underearth": "подземелье", "assettocorsa": "assettocorsa", "geometrydash": "геометриядаш", "kerbal": "кербал", "kerbalspaceprogram": "кербалкосмическаяпрограмма", "kenshi": "кенши", "spiritfarer": "душеводитель", "darkdome": "темныйкупол", "pizzatower": "пиццабашня", "indiegame": "индиигра", "itchio": "itchio", "golfit": "golfit", "truthordare": "правдаи<PERSON>ивызов", "game": "игра", "rockpaperscissors": "каменьножницыбумага", "trampoline": "трамплин", "hulahoop": "<PERSON>у<PERSON><PERSON><PERSON>у<PERSON>", "dare": "д<PERSON><PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "охотазасокровищами", "yardgames": "дворовыеигры", "pickanumber": "выберицифру", "trueorfalse": "правдаилиложь", "beerpong": "бипонг", "dicegoblin": "кубиковыйгоблин", "cosygames": "уютныеигры", "datinggames": "игрыдлязнакомств", "freegame": "бесплатнаяигра", "drinkinggames": "пьяныеигры", "sodoku": "содоку", "juegos": "игры", "mahjong": "маджонг", "jeux": "игры", "simulationgames": "симуляторыигр", "wordgames": "игрыслов", "jeuxdemots": "игрыслов", "juegosdepalabras": "игрыслов", "letsplayagame": "давайпоиграем", "boredgames": "настольныеигрушки", "oyun": "бойня", "interactivegames": "интерактивныеигры", "amtgard": "ам<PERSON>г<PERSON><PERSON>д", "staringcontests": "состязанияпристальноевзгляд", "spiele": "спиеле", "giochi": "игры", "geoguessr": "геогесср", "iphonegames": "игрынаайфоне", "boogames": "бугеймс", "cranegame": "игракран", "hideandseek": "прятки", "hopscotch": "скакалка", "arcadegames": "аркадныеигры", "yakuzagames": "ягузагеймс", "classicgame": "классическаяигра", "mindgames": "игрыума", "guessthelyric": "угадайтекст", "galagames": "галаигры", "romancegame": "игрателовлюбленных", "yanderegames": "яндереигры", "tonguetwisters": "скороговорки", "4xgames": "4xигры", "gamefi": "геймификации", "jeuxdarcades": "аркадныеигры", "tabletopgames": "настольныеигры", "metroidvania": "метроидвания", "games90": "игры90", "idareyou": "ятебявызываю", "mozaa": "мозаа", "fumitouedagames": "фумитоуэдагеймс", "racinggames": "гонкиигры", "ets2": "етс2", "realvsfake": "настоящееилиподделка", "playgames": "играйвигры", "gameonline": "игрыон<PERSON>а<PERSON>н", "onlinegames": "онлайнигры", "jogosonline": "онлайнигры", "writtenroleplay": "писательскаяролевая게임", "playaballgame": "играемвшарик", "pictionary": "пикционер", "coopgames": "коопигры", "jenga": "дженга", "wiigames": "виигеймс", "highscore": "высокийрекорд", "jeuxderôles": "настолки", "burgergames": "бургерыигры", "kidsgames": "детскиеигры", "skeeball": "скебол", "nfsmwblackedition": "нфсмвбледиция", "jeuconcour": "конкурс", "tcgplayer": "игрокиткг", "juegodepreguntas": "игра<PERSON>просов", "gioco": "игра", "managementgame": "игравмонтажом", "hiddenobjectgame": "играскрытыеобъекты", "roolipelit": "рулипелит", "formula1game": "формула1игра", "citybuilder": "городостроитель", "drdriving": "дра<PERSON><PERSON><PERSON>нг", "juegosarcade": "аркадныеигры", "memorygames": "игрыпамяти", "vulkan": "вулкан", "actiongames": "экшнигры", "blowgames": "блогодела", "pinballmachines": "пинбольныеавтоматы", "oldgames": "старыеигры", "couchcoop": "диваннаякооперация", "perguntados": "пертунедос", "gameo": "геймо", "lasergame": "лазертаг", "imessagegames": "имбеднополныеигры", "idlegames": "игрыдлянечего", "fillintheblank": "заполнипробел", "jeuxpc": "игрынапк", "rétrogaming": "ретрогейминг", "logicgames": "логическиеигры", "japangame": "японскаяигра", "rizzupgame": "ризированнымигра", "subwaysurf": "сабвэйсерф", "jeuxdecelebrite": "игрызвёзд", "exitgames": "выходныеигры", "5vs5": "5на5", "rolgame": "ролеваяигра", "dashiegames": "дэшигеймс", "gameandkill": "играйиплиши", "traditionalgames": "традиционныеигры", "kniffel": "кинфелл", "gamefps": "игровойfps", "textbasedgames": "игрыпонавиге", "gryparagrafowe": "грипараграфовые", "fantacalcio": "фан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrospel": "ретроигры", "thiefgame": "играворапреступнаяигра", "lawngames": "игрынагазоне", "fliperama": "флиперама", "heroclix": "героикликис", "tablesoccer": "настольныйфутбол", "tischfußball": "настольныйфутбол", "spieleabende": "игровыевечера", "jeuxforum": "игровойфорум", "casualgames": "казуальныеигры", "fléchettes": "дар<PERSON><PERSON>", "escapegames": "эскейпигры", "thiefgameseries": "игриподсмотрщиков", "cranegames": "петляигры", "játék": "игра", "bordfodbold": "бордфутбол", "jogosorte": "жогосорте", "mage": "мэдж", "cargames": "игранышках", "onlineplay": "онлайнигра", "mölkky": "мёлкки", "gamenights": "игровыевечера", "pursebingos": "пурсбингос", "randomizer": "рандомайзер", "msx": "мсх", "anagrammi": "анаграммы", "gamespc": "игрыпк", "socialdeductiongames": "игрысоцманипуляций", "dominos": "доминос", "domino": "домино", "isometricgames": "изометрическиеигры", "goodoldgames": "хорошиестарыеигры", "truthanddare": "правдаидерзость", "mahjongriichi": "махангричи", "scavengerhunts": "охотанасокровища", "jeuxvirtuel": "виртуальныеигры", "romhack": "ромхаки", "f2pgamer": "игрокбесплатно", "free2play": "бесплатноиграть", "fantasygame": "фэнтезиигра", "gryonline": "грио<PERSON><PERSON><PERSON><PERSON>н", "driftgame": "дрифтгейм", "gamesotomes": "игры01мес", "halotvseriesandgames": "халотвсериалыигры", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "всёсдвижком", "everywheregame": "вездеигра", "swordandsorcery": "мечиимагия", "goodgamegiving": "класснаяиградаром", "jugamos": "играем", "lab8games": "лаб8игры", "labzerogames": "лабораториянольигр", "grykomputerowe": "игрыкомпьютерные", "virgogami": "виргогами", "gogame": "гопоиграть", "jeuxderythmes": "игрыритма", "minaturegames": "миниатюрныеигры", "ridgeracertype4": "риджерацер4", "selflovegaming": "самолюбиеигры", "gamemodding": "гейммоддинг", "crimegames": "игрыпроуголовку", "dobbelspellen": "дббелспеллен", "spelletjes": "игрушки", "spacenerf": "спейсерф", "charades": "шарады", "singleplayer": "одиночныйигрок", "coopgame": "коопигра", "gamed": "геймеры", "forzahorizon": "форзагоризонт", "nexus": "нексус", "geforcenow": "гефорсенау", "maingame": "главнаяигра", "kingdiscord": "кинг<PERSON>искорд", "scrabble": "скрэбл", "schach": "шахматы", "shogi": "шоги", "dandd": "данди", "catan": "катан", "ludo": "лудо", "backgammon": "нарды", "onitama": "онитама", "pandemiclegacy": "наследиепандемии", "camelup": "верблюдуп", "monopolygame": "монополияигра", "brettspiele": "настолки", "bordspellen": "настольныеигры", "boardgame": "настолка", "sällskapspel": "настолки", "planszowe": "настолки", "risiko": "ризико", "permainanpapan": "настольныеигры", "zombicide": "зомбицид", "tabletop": "настольныеигры", "baduk": "бадук", "bloodbowl": "кроваваячаша", "cluedo": "клуэдо", "xiangqi": "сянцзи", "senet": "сенет", "goboardgame": "гобоардгейм", "connectfour": "свяжисчетырьмя", "heroquest": "геройквест", "giochidatavolo": "игрынастоле", "farkle": "фаркле", "carrom": "карром", "tablegames": "настолки", "dicegames": "игрысжетонами", "yatzy": "яйцовыйбум", "parchis": "партис", "jogodetabuleiro": "жогадет<PERSON>б<PERSON>лейру", "jocuridesocietate": "игрушкидлядрузей", "deskgames": "настольныеигры", "alpharius": "альфариус", "masaoyunları": "масофаны", "marvelcrisisprotocol": "маркризиспротокол", "cosmicencounter": "космическаявстреча", "creationludique": "игровоетворчество", "tabletoproleplay": "настольныеролевыеигры", "cardboardgames": "картонныеигры", "eldritchhorror": "элдритчужас", "switchboardgames": "поставимигры", "infinitythegame": "бесконечнаяигра", "kingdomdeath": "королевскаясмерть", "yahtzee": "яхтзи", "chutesandladders": "горкииможет", "társas": "тусовка", "juegodemesa": "настольнаяигра", "planszówki": "настолки", "rednecklife": "деревенскаяжизнь", "boardom": "скука", "applestoapples": "яблокикяблокам", "jeudesociété": "игрыобществa", "gameboard": "геймборд", "dominó": "домино", "kalah": "калаh", "crokinole": "крокенол", "jeuxdesociétés": "настольныеигры", "twilightimperium": "сумеречноеимпериум", "horseopoly": "конюполи", "deckbuilding": "сборкарты", "mansionsofmadness": "особнякибезумия", "gomoku": "гомоку", "giochidatavola": "игрынастоле", "shadowsofbrimstone": "тенивыебревна", "kingoftokyo": "корольтокио", "warcaby": "варкабы", "táblajátékok": "игрынастолах", "battleship": "линк<PERSON>р", "tickettoride": "билетнакатку", "deskovehry": "настольныеигры", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "настолки", "stolníhry": "настолки", "xiángqi": "шахматы", "jeuxsociete": "настолки", "gesellschaftsspiele": "настольныеигры", "starwarslegion": "звёздныевойнылегион", "gochess": "гочесс", "weiqi": "вэйци", "jeuxdesocietes": "настольныеигры", "terraria": "террария", "dsmp": "дсмп", "warzone": "военнополе", "arksurvivalevolved": "арксурвивалеволвед", "dayz": "деньки", "identityv": "идентичностьv", "theisle": "остров", "thelastofus": "последниеизнаснарусском", "nomanssky": "номанскски", "subnautica": "сабнаутика", "tombraider": "гробокопатель", "callofcthulhu": "зовктулху", "bendyandtheinkmachine": "гибкийитнековаямашина", "conanexiles": "конанэксайлс", "eft": "эфт", "amongus": "срединассрединас", "eco": "эко", "monkeyisland": "обезьяньий<PERSON>стров", "valheim": "вал<PERSON>ейм", "planetcrafter": "планетостроитель", "daysgone": "днипрошли", "fobia": "фобия", "witchit": "ведьмочитай", "pathologic": "патологичный", "zomboid": "зомбойд", "northgard": "нортгард", "7dtd": "7dtd", "thelongdark": "долгойтьмы", "ark": "арк", "grounded": "приземленный", "stateofdecay2": "состояниераспада2", "vrising": "вризинг", "madfather": "сумасшедшийпапа", "dontstarve": "неголодай", "eternalreturn": "вечныйвозврат", "pathoftitans": "путь<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "фрикшнлгеймс", "hexen": "ведьмы", "theevilwithin": "злодейвнутри", "realrac": "реальныйрак", "thebackrooms": "закулисья", "backrooms": "бэкрумы", "empiressmp": "империесмп", "blockstory": "блокистория", "thequarry": "кар<PERSON><PERSON>р", "tlou": "тлоу", "dyinglight": "светугасит", "thewalkingdeadgame": "игравзомби", "wehappyfew": "мысчастливынесмотряниначто", "riseofempires": "восстановлениеимперий", "stateofsurvivalgame": "состояниевыживанияигра", "vintagestory": "винтажнаяистория", "arksurvival": "арквыживание", "barotrauma": "баротравма", "breathedge": "дыхание", "alisa": "алиса", "westlendsurvival": "выживаниезапада", "beastsofbermuda": "бестиибермуды", "frostpunk": "морозныйпанк", "darkwood": "темныйлес", "survivalhorror": "выживан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "резидентэвил", "residentevil2": "residentevil2", "residentevil4": "резидентевил4", "residentevil3": "резидентевил3", "voidtrain": "вагонбезруля", "lifeaftergame": "жизньпослеигры", "survivalgames": "игрынавыживание", "sillenthill": "тихаягора", "thiswarofmine": "этойвойнымоей", "scpfoundation": "scpфонд", "greenproject": "зеленыйпроект", "kuon": "куон", "cryoffear": "плачотстраха", "raft": "плот", "rdo": "рдо", "greenhell": "зеленыйад", "residentevil5": "резидентевил5", "deadpoly": "мертваяполи", "residentevil8": "residentevil8", "onironauta": "ониронавт", "granny": "бабушка", "littlenightmares2": "маленькиекошмары2", "signalis": "сиг<PERSON><PERSON><PERSON>з", "amandatheadventurer": "амандапутешественница", "sonsoftheforest": "сыновьялеса", "rustvideogame": "русскаяигра", "outlasttrials": "выдержииспытания", "alienisolation": "чужоеизоляция", "undawn": "недосвет", "7day2die": "7днейчтобыумереть", "sunlesssea": "безсолнечноморье", "sopravvivenza": "сурвайвл", "propnight": "пропнайт", "deadisland2": "мертвыйостров2", "ikemensengoku": "икемэнсэнгоку", "ikemenvampire": "икементвампир", "deathverse": "смертельныймир", "cataclysmdarkdays": "катаклизмытемныедни", "soma": "сома", "fearandhunger": "страхийголод", "stalkercieńczarnobyla": "сталкерсеньчарнобыля", "lifeafter": "жизньпосле", "ageofdarkness": "эпохатьмы", "clocktower3": "часыбашни3", "aloneinthedark": "одинвтемноте", "medievaldynasty": "средневековаядинастия", "projectnimbusgame": "проектнимбусигра", "eternights": "вечныеночи", "craftopia": "крафтопия", "theoutlasttrials": "испытаниявышедших", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "мировое_господство", "rocketleague": "карбоноваялига", "tft": "тфт", "officioassassinorum": "офи<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "некрон", "wfrp": "вфрп", "dwarfslayer": "убийцадворфов", "warhammer40kcrush": "вашаммер40клюбовь", "wh40": "wh40", "warhammer40klove": "любовьквархаммер40k", "warhammer40klore": "вархаммер40клегенды", "warhammer": "вархаммер", "warhammer30k": "вархаммер30к", "warhammer40k": "вархаммер40к", "warhammer40kdarktide": "вархаммер40ктемнаявода", "totalwarhammer3": "тотальнаявойнахаммер3", "temploculexus": "темплокулексус", "vindicare": "виндикация", "ilovesororitas": "ялюблюсороритас", "ilovevindicare": "ялюблювиндикаре", "iloveassasinorum": "ялюбл<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "темпловененум", "templocallidus": "темплоумный", "templomaerorus": "темпломаерорус", "templovanus": "темплованус", "oficioasesinorum": "ремеслоспособителей", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40к", "tetris": "тетрис", "lioden": "лиоден", "ageofempires": "эпохаимперий", "aoe2": "ае2", "hoi4": "hoi4", "warhammerageofsigmar": "вояжввремявойны", "civilizationv": "цивилизацияv", "ittakestwo": "иттакестудво", "wingspan": "размахкрыльев", "terraformingmars": "терраформируеммарс", "heroesofmightandmagic": "героисилыимагии", "btd6": "бтд6", "supremecommander": "супремекомандующий", "ageofmythology": "эпоха<PERSON><PERSON><PERSON>ов", "args": "арги", "rime": "риме", "planetzoo": "планетныйзоопарк", "outpost2": "постоянный2", "banished": "изгнанный", "caesar3": "цезарь3", "redalert": "краснаятревога", "civilization6": "цивилизация6", "warcraft2": "варкрафт2", "commandandconquer": "командуйовладей", "warcraft3": "варкрафт3", "eternalwar": "вечнаявойна", "strategygames": "стратегическиеигры", "anno2070": "анно2070", "civilizationgame": "игравцивилизации", "civilization4": "цивилизация4", "factorio": "факторио", "dungeondraft": "подземельеволшебства", "spore": "спора", "totalwar": "тотальнаявойна", "travian": "травиан", "forts": "фортс", "goodcompany": "хорошаякомпания", "civ": "цив", "homeworld": "дом<PERSON><PERSON>ниймир", "heidentum": "хайдентум", "aoe4": "вaoe4", "hnefatafl": "хнефатавл", "fasterthanlight": "быстреезвука", "forthekings": "длякоролей", "realtimestrategy": "реальноестратегическоеуправление", "starctaft": "starctaft", "sidmeierscivilization": "сидмайерацивилизация", "kingdomtwocrowns": "королевстводвухкорон", "eu4": "европа4", "vainglory": "гордость", "ww40k": "ww40k", "godhood": "божественность", "anno": "анно", "battletech": "баттлтек", "malifaux": "малифо", "w40k": "w40k", "hattrick": "хаз<PERSON><PERSON>н", "davesfunalgebraclass": "дэйвсвеселаяалгебра", "plagueinc": "пандемияинк", "theorycraft": "теориякреатива", "mesbg": "месбг", "civilization3": "цивилизация3", "4inarow": "4вряддругом", "crusaderkings3": "крестоносцы3", "heroes3": "герои3", "advancewars": "продвижениевойн", "ageofempires2": "эпохаимперий2", "disciples2": "дисциплы2", "plantsvszombies": "растенияпротивзомби", "giochidistrategia": "игрыстратегии", "stratejioyunları": "страта<PERSON><PERSON><PERSON>мс", "europauniversalis4": "европауниверсалис4", "warhammervermintide2": "вархаммерверминвайд2", "ageofwonders": "эпохапрекрасного", "dinosaurking": "динозавркороль", "worldconquest": "мировоевладение", "heartsofiron4": "сердцажелеза4", "companyofheroes": "компаниягероев", "battleforwesnoth": "битвазавеслиот", "aoe3": "аоэ3", "forgeofempires": "кузницаимперий", "warhammerkillteam": "ворхаммерубийственнаякоманда", "goosegooseduck": "гусейгусинят", "phobies": "фобии", "phobiesgame": "фобииигра", "gamingclashroyale": "геймингклашроял", "adeptusmechanicus": "адептусмеханикуса", "outerplane": "внеплана", "turnbased": "пошаговая", "bomberman": "<PERSON><PERSON><PERSON>", "ageofempires4": "эпохаимперий4", "civilization5": "цивилизация5", "victoria2": "виктория2", "crusaderkings": "покорителикоролевств", "cultris2": "культрис2", "spellcraft": "заклинания", "starwarsempireatwar": "звёздныевйныимперияввойне", "pikmin4": "пикмин4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "попфулмейл", "shiningforce": "сияющаясила", "masterduel": "мастеркарточныйпоединок", "dysonsphereprogram": "дипсферопрограмм", "transporttycoon": "транспортныемагнаты", "unrailed": "недопустимо", "magicarena": "магическаяарена", "wolvesville": "вулканы", "ooblets": "ублёты", "planescapetorment": "планбегствизасосов", "uplandkingdoms": "упландскиекоролевства", "galaxylife": "галакидляжизни", "wolvesvilleonline": "волчийгородонлайн", "slaythespire": "сразисьсшпилем", "battlecats": "битвакотят", "sims3": "симс3", "sims4": "симс4", "thesims4": "симс4", "thesims": "симс", "simcity": "симсити", "simcity2000": "симсити2000", "sims2": "симс2", "iracing": "айрей<PERSON>инг", "granturismo": "грантуризмо", "needforspeed": "нужнаскорость", "needforspeedcarbon": "нужнаскоростьуголь", "realracing3": "реальныйзаезд3", "trackmania": "трекмания", "grandtourismo": "грандтуризмо", "gt7": "гт7", "simsfreeplay": "симсбесплатнаяигра", "ts4": "тс4", "thesims2": "соседи2", "thesims3": "сими3", "thesims1": "симсы1", "lossims4": "лоссимс4", "fnaf": "фна<PERSON>", "outlast": "продер<PERSON><PERSON><PERSON>ь", "deadbydaylight": "мертвыподсвету", "alicemadnessreturns": "возвращениебезумияалисы", "darkhorseanthology": "темнаялошадьантология", "phasmophobia": "фазмофобия", "fivenightsatfreddys": "пятьночейсфредди", "saiko": "сайко", "fatalframe": "фатальнаярамка", "littlenightmares": "маленькиестрашилки", "deadrising": "встатьизмертвых", "ladydimitrescu": "ледидимитреску", "homebound": "дома", "deadisland": "мертвыйостров", "litlemissfortune": "маленькаямисснеудача", "projectzero": "проектноль", "horory": "хороры", "jogosterror": "джогостеррор", "helloneighbor": "приветсосед", "helloneighbor2": "приветсосед2", "gamingdbd": "геймингдбд", "thecatlady": "котоподруга", "jeuxhorreur": "игр<PERSON><PERSON><PERSON><PERSON><PERSON>ов", "horrorgaming": "ужасныеигры", "magicthegathering": "магиясборища", "mtg": "мтг", "tcg": "тцг", "cardsagainsthumanity": "картыпротивчеловечности", "cribbage": "криби<PERSON><PERSON>", "minnesotamtg": "миннесотамтг", "edh": "эдх", "monte": "монте", "pinochle": "пинохлё", "codenames": "кодовыеимена", "dixit": "диксит", "bicyclecards": "велосипедныекарты", "lor": "лор", "euchre": "экюре", "thegwent": "гвент", "legendofrunetera": "легендарантеры", "solitaire": "солитер", "poker": "покер", "hearthstone": "хартсто<PERSON>н", "uno": "уно", "schafkopf": "шахтка", "keyforge": "ключеваякузня", "cardtricks": "фокусыскартами", "playingcards": "игральныекарты", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "netrunner": "нетраннер", "gwent": "гвент", "metazoo": "метазоо", "tradingcards": "трейдинговыекарточки", "pokemoncards": "покемончcards", "fleshandbloodtcg": "плотьикровьткг", "sportscards": "спортивныекарточки", "cardfightvanguard": "карточнаябитвавагнарда", "duellinks": "дуэлин<PERSON>с", "spades": "пиковые", "warcry": "боевойкрик", "digimontcg": "дигимонтгк", "toukenranbu": "токенран<PERSON>у", "kingofhearts": "корольсердец", "truco": "труко", "loteria": "лотерея", "hanafuda": "ханофуда", "theresistance": "сопротивление", "transformerstcg": "трансформерствкг", "doppelkopf": "доппельkopf", "yugiohcards": "югиохкарты", "yugiohtcg": "югиоткг", "yugiohduel": "югиохдуэль", "yugiohocg": "югиоиог", "dueldisk": "дуэльдиск", "yugiohgame": "югиохигра", "darkmagician": "тёмныймаг", "blueeyeswhitedragon": "голубыеглазабелыйдракон", "yugiohgoat": "югиохгока", "briscas": "брискас", "juegocartas": "игракарты", "burraco": "буррако", "rummy": "рамми", "grawkarty": "гравкарты", "dobble": "доббл", "mtgcommander": "мтгкоммандер", "cotorro": "которая", "jeuxdecartes": "игрыскороткихкарт", "mtgjudge": "судьямтг", "juegosdecartas": "игрынакартах", "duelyst": "дуэльст", "mtgplanschase": "погонямтгпоубежищу", "mtgpreconcommander": "мтгпреконкомандир", "kartenspiel": "карточнаяигра", "carteado": "картядо", "sueca": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карточныеигры", "battlespirits": "битвы<PERSON><PERSON><PERSON>ов", "battlespiritssaga": "боядушиагенты", "jogodecartas": "джогодекарт", "žolíky": "булики", "facecard": "лицеваякарта", "cardfight": "картофельнаябитва", "biriba": "бириба", "deckbuilders": "декостроители", "marvelchampions": "марвелчемпионов", "magiccartas": "магическакартас", "yugiohmasterduel": "югиохмастардюэль", "shadowverse": "шадовверс", "skipbo": "скепбо", "unstableunicorns": "недостаточностабильныеединороги", "cyberse": "киберсе", "classicarcadegames": "классическиегеймы", "osu": "осу", "gitadora": "гитадора", "dancegames": "танцевальныеигры", "fridaynightfunkin": "пятничнаяночьфанкин", "fnf": "фнф", "proseka": "просека", "projectmirai": "проектмирая", "projectdiva": "проектдива", "djmax": "дид<PERSON><PERSON><PERSON><PERSON>а<PERSON>с", "guitarhero": "гитарн<PERSON>йгерой", "clonehero": "клонгерой", "justdance": "просто_танцуй", "hatsunemiku": "хатсунэмику", "prosekai": "просекай", "rocksmith": "роксмита", "idolish7": "айдолиш7", "rockthedead": "порвиммертвечину", "chunithm": "чунитым", "idolmaster": "айдолмастер", "dancecentral": "танцеваленецентр", "rhythmgamer": "ритмгеймер", "stepmania": "степмания", "highscorerythmgames": "игрыритмасвысокимиочками", "pkxd": "pkxd", "sidem": "сайдэм", "ongeki": "онгэки", "soundvoltex": "саундвольтекс", "rhythmheaven": "ритмнеба", "hypmic": "хайпмик", "adanceoffireandice": "танецогняильда", "auditiononline": "онлайнпрослушивание", "itgmania": "итгмания", "juegosderitmo": "рит<PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "криптофтанекродансер", "rhythmdoctor": "ритмдоктор", "cubing": "кубинг", "wordle": "вордл", "teniz": "тениз", "puzzlegames": "головоломки", "spotit": "спотит", "rummikub": "руммикуб", "blockdoku": "блокдоку", "logicpuzzles": "логическиепазлы", "sudoku": "судоку", "rubik": "рубит", "brainteasers": "гадалки", "rubikscube": "кубикирубика", "crossword": "кроссворд", "motscroisés": "кроссворды", "krzyżówki": "кроссворды", "nonogram": "нонограмма", "bookworm": "книжныйчервь", "jigsawpuzzles": "пазлы", "indovinello": "индовинелло", "riddle": "загадка", "riddles": "загадки", "rompecabezas": "разбиваемголоволомку", "tekateki": "текатэки", "inside": "внутри", "angrybirds": "гневныептицы", "escapesimulator": "симуляторубежа", "minesweeper": "са<PERSON><PERSON>р", "puzzleanddragons": "пазлыидраконы", "crosswordpuzzles": "кроссворды", "kurushi": "куроши", "gardenscapesgame": "садовыеприключения", "puzzlesport": "пазлспорт", "escaperoomgames": "эскейпрумигры", "escapegame": "играубежища", "3dpuzzle": "3dпазл", "homescapesgame": "играboo", "wordsearch": "поискслов", "enigmistica": "энергомистика", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "мист", "riddletales": "загадочныеистории", "fishdom": "рыбныймир", "theimpossiblequiz": "невозможныйтест", "candycrush": "конфетныйразрушитель", "littlebigplanet": "маленькийбольшоймир", "match3puzzle": "матч3пазл", "huniepop": "гунейпоп", "katamaridamacy": "катамаридомасья", "kwirky": "квирки", "rubikcube": "рубиккьюб", "cuborubik": "кубокубика", "yapboz": "япб<PERSON>з", "thetalosprinciple": "талазпринцип", "homescapes": "домашниепейзажи", "puttputt": "путпут", "qbert": "кьюберт", "riddleme": "угадайменя", "tycoongames": "игрытикона", "cubosderubik": "кубосдерубика", "cruciverba": "крузиверба", "ciphers": "шифры", "rätselwörter": "загадочныеслова", "buscaminas": "будажмалы", "puzzlesolving": "решениепазлов", "turnipboy": "репамальчик", "adivinanzashot": "адивина<PERSON><PERSON><PERSON><PERSON><PERSON>т", "nobodies": "никтоизнас", "guessing": "угадайка", "nonograms": "нонорамы", "kostkirubika": "косткирубика", "crypticcrosswords": "криптографическиекроссворды", "syberia2": "сибирь2", "puzzlehunt": "пазлохота", "puzzlehunts": "паззлохота", "catcrime": "котыкриминал", "quebracabeça": "головоломка", "hlavolamy": "главоломки", "poptropica": "поптропика", "thelastcampfire": "последнийкостёр", "autodefinidos": "автодефинитед", "picopark": "пикопарк", "wandersong": "блуждающаяпесня", "carto": "картон", "untitledgoosegame": "неписанаяиграутки", "cassetête": "кассетэт", "limbo": "лимоно", "rubiks": "рубитокс", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "малюсики", "rubikovakostka": "рубиковаякостка", "speedcube": "спидкуб", "pieces": "кусочки", "portalgame": "порталигра", "bilmece": "билмеце", "puzzelen": "пазлы", "picross": "пикросс", "rubixcube": "бугирокскубик", "indovinelli": "индовинелли", "cubomagico": "кубомагико", "mlbb": "млбб", "pubgm": "пубгм", "codmobile": "кодмобайл", "codm": "кодм", "twistedwonderland": "извращеннаястрана", "monopoly": "монополия", "futurefight": "битвабудущего", "mobilelegends": "мобильныелегенды", "brawlstars": "бравлстарс", "brawlstar": "броулстар", "coc": "кок", "lonewolf": "одиночка", "gacha": "гача", "wr": "всёравно", "fgo": "фго", "bitlife": "бит<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "пикмин<PERSON>лум", "ff": "фф", "ensemblestars": "энсамблзвёзд", "asphalt9": "асфальт9", "mlb": "млб", "cookierunkingdom": "песочницабу", "alchemystars": "алхимиязвёзд", "stateofsurvival": "состояниенагрузки", "mycity": "моегорода", "arknights": "арка<PERSON>йты", "colorfulstage": "яркаясцена", "bloonstowerdefense": "блунстоверзащита", "btd": "бтд", "clashroyale": "клашрояль", "angela": "анджела", "dokkanbattle": "до<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "fategrandorder": "судьба_великий_порядок", "hyperfront": "гиперфронт", "knightrun": "рыцарскийзабег", "fireemblemheroes": "огненныегерои", "honkaiimpact": "хонкайимпакт", "soccerbattle": "футбольнаябитва", "a3": "a3", "phonegames": "игрынапланшете", "kingschoice": "выборкороля", "guardiantales": "стражисторий", "petrolhead": "бензомания", "tacticool": "тактикул", "cookierun": "кукержан", "pixeldungeon": "пиксельныйподземелье", "arcaea": "аркаэа", "outoftheloop": "внепотока", "craftsman": "ремесленник", "supersus": "суперподозрительно", "slowdrive": "медленнаяпоездка", "headsup": "непропусти", "wordfeud": "словеснаябитва", "bedwars": "батлвкровати", "freefire": "фрифа<PERSON>р", "mobilegaming": "мобильныеигры", "lilysgarden": "садлилы", "farmville2": "фармвилл2", "animalcrossing": "перекрестокживотных", "bgmi": "bgmi", "teamfighttactics": "командныебои", "clashofclans": "сражен<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "pjsekai": "пджсэкай", "mysticmessenger": "мистическийпосланник", "callofdutymobile": "колдвартмобайл", "thearcana": "арканум", "8ballpool": "8б<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emergencyhq": "экстренныйштаб", "enstars": "энстастары", "randonautica": "рандонаутика", "maplestory": "маплстори", "albion": "альбион", "hayday": "хэ<PERSON>дей", "onmyoji": "онмёдзи", "azurlane": "азур<PERSON>е<PERSON>н", "shakesandfidget": "шейкииздрузья", "ml": "мл", "bangdream": "бэнгдрим", "clashofclan": "сражен<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "starstableonline": "звёзднаястаблицаонлайн", "dragonraja": "драконрада", "timeprincess": "времянцесса", "beatstar": "битстар", "dragonmanialegend": "драконаманиялегенда", "hanabi": "хана<PERSON>и", "disneymirrorverse": "диснейзеркальнаявселенная", "pocketlove": "пocketlove", "androidgames": "андроидигры", "criminalcase": "криминальныйдело", "summonerswar": "суммонерсвар", "cookingmadness": "кулинарнаябешенство", "dokkan": "доккан", "aov": "аов", "triviacrack": "тривиабуст", "leagueofangels": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "лордсмобайл", "tinybirdgarden": "маленькийптичийсад", "gachalife": "gachalife", "neuralcloud": "нейросеть", "mysingingmonsters": "моипоющиемонстры", "nekoatsume": "некоаццуме", "bluearchive": "б<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "raidshadowlegends": "raidshadowlegends", "warrobots": "войноботов", "mirrorverse": "зеркальнаявселенная", "pou": "поу", "warwings": "войныкрылья", "fifamobile": "фифамобайл", "mobalegendbangbang": "мобалегендбэнгбэнг", "evertale": "эвар<PERSON><PERSON><PERSON>л", "futime": "фура<PERSON><PERSON><PERSON><PERSON>", "antiyoy": "анти<PERSON><PERSON>", "apexlegendmobile": "апекслегендмобайл", "ingress": "ингресс", "slugitout": "побейпобеду", "mpl": "мпл", "coinmaster": "коинмастер", "punishinggrayraven": "наказывающаясераяворобейка", "petpals": "друзьяпитомцы", "gameofsultans": "игро<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "arenabreakout": "аренаивыход", "wolfy": "вульфи", "runcitygame": "беггородигра", "juegodemovil": "мобильнаяигра", "avakinlife": "авак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "когама", "mimicry": "мимикрия", "blackdesertmobile": "чернаяпустынямобиль", "rollercoastertycoon": "роллеркостертайкун", "grandchase": "гранд<PERSON>е<PERSON>з", "bombmebrasil": "бомбимебразил", "ldoe": "ldoe", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "отомэигра", "mindustry": "майндестри", "callofdragons": "зовд<PERSON><PERSON><PERSON><PERSON><PERSON>ов", "shiningnikki": "сияющаяникки", "carxdriftracing2": "каркспроебанои2", "pathtonowhere": "путьнекуда", "sealm": "силам", "shadowfight3": "теньбой3", "limbuscompany": "лимбускомпания", "demolitionderby3": "демонстрационныйдерби3", "wordswithfriends2": "словасдрузьями2", "soulknight": "душевныйрыцарь", "purrfecttale": "пёрфекттейл", "showbyrock": "показатьрок", "ladypopular": "ледипопуляр", "lolmobile": "лол<PERSON>о<PERSON><PERSON>йл", "harvesttown": "сборгород", "perfectworldmobile": "идеальныймирмобиль", "empiresandpuzzles": "империиипазлы", "empirespuzzles": "эмпайрспаззлы", "dragoncity": "драконгород", "garticphone": "гартікфон", "battlegroundmobileind": "полебитвымобильныйинд", "fanny": "бантик", "littlenightmare": "маленькийкошмар", "aethergazer": "aethergazer", "mudrunner": "мудран<PERSON><PERSON>р", "tearsofthemis": "слезымертве<PERSON>ов", "eversoul": "эверсоул", "gunbound": "ган<PERSON><PERSON><PERSON><PERSON>д", "gamingmlbb": "геймерыбуба", "dbdmobile": "дбд<PERSON><PERSON><PERSON><PERSON><PERSON>л", "arknight": "арка<PERSON>йты", "pristontale": "пристонтайл", "zombiecastaways": "зомбифарерцы", "eveechoes": "евиэхо", "jogocelular": "дж<PERSON>госеллар", "mariokarttour": "мариокарттур", "zooba": "зуба", "mobilelegendbangbang": "мобильнаялегендабэнгбэнг", "gachaclub": "гачаклуб", "v4": "v4", "cookingmama": "кулинарнаямама", "cabalmobile": "кабала_мобиль", "streetfighterduel": "уличныйбоецдуэль", "lesecretdhenri": "лесекретдэнри", "gamingbgmi": "геймингбгми", "girlsfrontline": "линиядевушек", "jurassicworldalive": "юрскоемироваяживопись", "soulseeker": "искательдуши", "gettingoverit": "преодолеваемэто", "openttd": "openttd", "onepiecebountyrush": "одинкусокбонусрышка", "moonchaistory": "луначаистория", "carxdriftracingonline": "carxдрифтрейсингонлайн", "jogosmobile": "игрыдлямобила", "legendofneverland": "легенданевролэнд", "pubglite": "pubglite", "gamemobilelegends": "играйвмобильныелегенды", "timeraiders": "тимерайдеры", "gamingmobile": "мобильныеигры", "marvelstrikeforce": "мартвелсила", "thebattlecats": "бояшк<PERSON><PERSON><PERSON><PERSON>и<PERSON>ов", "dnd": "днд", "quest": "квест", "giochidiruolo": "играемвруле", "dnd5e": "дн5е", "rpgdemesa": "рпгдёмнастоле", "worldofdarkness": "миртьмы", "travellerttrpg": "путешественникттрпг", "2300ad": "2300гд", "larp": "лар<PERSON>", "romanceclub": "романтиклуб", "d20": "д20", "pokemongames": "покемоныигры", "pokemonmysterydungeon": "покемономистериабилдинг", "pokemonlegendsarceus": "покемонылегендыарсеус", "pokemoncrystal": "покемончystal", "pokemonanime": "покемонаниме", "pokémongo": "покемоныгдеобычно", "pokemonred": "покемонкрасный", "pokemongo": "покемоныго", "pokemonshowdown": "покемонысражение", "pokemonranger": "покемонтпатруль", "lipeep": "липи<PERSON><PERSON>", "porygon": "поригон", "pokemonunite": "покемонайт", "entai": "энтай", "hypno": "гипно", "empoleon": "эмполеон", "arceus": "арце<PERSON>с", "mewtwo": "мьюту", "paldea": "палдея", "pokemonscarlet": "покемонскарлет", "chatot": "чатоt", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "покемоновиолет", "pokemonpurpura": "покемонипурпуре", "ashketchum": "ашкетчум", "gengar": "генгар", "natu": "нату", "teamrocket": "командаракет", "furret": "фуррет", "magikarp": "магика<PERSON>п", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "снорлакс", "pocketmonsters": "карманныемонстры", "nuzlocke": "нузлок", "pokemonplush": "покемоноигрушка", "teamystic": "тимистик", "pokeball": "покебол", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "покемоныромхаки", "pubgmobile": "пубгмобиль", "litten": "литтен", "shinypokemon": "блестящиепокемоны", "mesprit": "месприт", "pokémoni": "покемоны", "ironhands": "железныеруки", "kabutops": "кабутопс", "psyduck": "псайдак", "umbreon": "умбрион", "pokevore": "покевор", "ptcg": "птсг", "piplup": "пиплап", "pokemonsleep": "покемондрем", "heyyoupikachu": "эййоупикачу", "pokémonmaster": "покемонмастер", "pokémonsleep": "покемонсон", "kidsandpokemon": "детиипокемоны", "pokemonsnap": "покемонснап", "bulbasaur": "бульбазавр", "lucario": "люкарио", "charizar": "чаризар", "shinyhunter": "блестящийохотник", "ajedrez": "шахматы", "catur": "катур", "xadrez": "шахматы", "scacchi": "шахматы", "schaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skak": "скак", "ajedres": "шахматы", "chessgirls": "шахматыдевчонки", "magnuscarlsen": "магнускарлсен", "worldblitz": "миров<PERSON>йблиц", "jeudéchecs": "шахма<PERSON>ункинг", "japanesechess": "японскиешахматы", "chinesechess": "китайскиешахматы", "chesscanada": "шахматыканада", "fide": "фиде", "xadrezverbal": "шахматывслух", "openings": "открытия", "rook": "бро", "chesscom": "шахматком", "calabozosydragones": "калабозыидраконы", "dungeonsanddragon": "подземельяидраконы", "dungeonmaster": "мастерподземелий", "tiamat": "тиамат", "donjonsetdragons": "донжоныидраконы", "oxventure": "оксприключение", "darksun": "тёмноесолнце", "thelegendofvoxmachina": "легендаобоксмашина", "doungenoanddragons": "добавьдрузейид<PERSON><PERSON><PERSON><PERSON>нов", "darkmoor": "тёмнаяпустошь", "minecraftchampionship": "чемпионатмайнкрафт", "minecrafthive": "майнкрафтулейка", "minecraftbedrock": "майнкрафтбедрок", "dreamsmp": "дримсmp", "hermitcraft": "гермитк<PERSON><PERSON><PERSON>т", "minecraftjava": "майнкрафтджава", "hypixelskyblock": "гипиксельскайблок", "minetest": "минетест", "hypixel": "хайпиксель", "karmaland5": "картамаланд5", "minecraftmods": "модынанайкрафт", "mcc": "мкк", "candleflame": "светпламени", "fru": "фру", "addons": "дополнения", "mcpeaddons": "добавкидлямкпе", "skyblock": "скайблок", "minecraftpocket": "майнкрафтпокет", "minecraft360": "майнкрафт360", "moddedminecraft": "модденмайнкрафт", "minecraftps4": "майнкрафтпс4", "minecraftpc": "майнкрафтпк", "betweenlands": "междумирья", "minecraftdungeons": "майнкрафтподземелья", "minecraftcity": "майнкрафтгород", "pcgamer": "пкгеймер", "jeuxvideo": "игры", "gambit": "гамбиты", "gamers": "геймеры", "levelup": "прокачайся", "gamermobile": "геймермобиль", "gameover": "играокончена", "gg": "гг", "pcgaming": "игрынаpc", "gamen": "геймим", "oyunoynamak": "оиграю", "pcgames": "пкигры", "casualgaming": "кэжуалгейминг", "gamingsetup": "gamingнастройка", "pcmasterrace": "пкмастеркла<PERSON>с", "pcgame": "пкигра", "gamerboy": "геймермальчик", "vrgaming": "вірге<PERSON><PERSON>инг", "drdisrespect": "дрдисреспект", "4kgaming": "4kгейминг", "gamerbr": "геймербр", "gameplays": "игровыестримы", "consoleplayer": "консольныйгеймер", "boxi": "бокси", "pro": "профессионал", "epicgamers": "эпикгеймеры", "onlinegaming": "онлайнигры", "semigamer": "полугеймер", "gamergirls": "геймерш", "gamermoms": "геймермамы", "gamerguy": "геймерпарень", "gamewatcher": "игровойнаблюдатель", "gameur": "геймер", "grypc": "грпц", "rangugamer": "рангуплеер", "gamerschicas": "геймерши", "otoge": "отове", "dedsafio": "дедсафио", "teamtryhard": "командапопробуйусерднее", "mallugaming": "маллуг<PERSON><PERSON>минг", "pawgers": "пауджеры", "quests": "квесты", "alax": "алекс", "avgn": "средненько", "oldgamer": "старыйгеймер", "cozygaming": "уютныеигры", "gamelpay": "gamelpay", "juegosdepc": "игрынапк", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "конкурентныеигры", "minecraftnewjersey": "майнкрафтньюджерси", "faker": "фейкер", "pc4gamers": "пк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "игроваятема", "yatoro": "ятороу", "heterosexualgaming": "гетеросексуальныеигры", "gamepc": "играпк", "girlsgamer": "девочкогеймер", "fnfmods": "фнфмоды", "dailyquest": "ежедневныйквест", "gamegirl": "игроваядевчонка", "chicasgamer": "геймершачки", "gamesetup": "игровойнабор", "overpowered": "переоцененный", "socialgamer": "социальныйгеймер", "gamejam": "геймджем", "proplayer": "профессионал", "roleplayer": "ролевик", "myteam": "моякоманда", "republicofgamers": "республикагеймеров", "aorus": "аорус", "cougargaming": "кугартgaming", "triplelegend": "тройнойлегенд", "gamerbuddies": "геймерскиедрузья", "butuhcewekgamers": "нужнадевочкагеймер", "christiangamer": "христианскийгеймер", "gamernerd": "геймердолбанутый", "nerdgamer": "гикигровик", "afk": "недоступен", "andregamer": "андрег<PERSON>ймер", "casualgamer": "повседневныйгеймер", "89squad": "89сsquad", "inicaramainnyagimana": "иницарамагина", "insec": "инсек", "gemers": "геймеры", "oyunizlemek": "осмотрфил<PERSON>мов", "gamertag": "геймертаг", "lanparty": "ланпати", "videogamer": "видеоигрок", "wspólnegranie": "общаяигра", "mortdog": "мортдог", "playstationgamer": "плейстейшенгеймер", "justinwong": "джастинвонг", "healthygamer": "здоровыйигрок", "gtracing": "геймерскоекресло", "notebookgamer": "ноутбокгеймер", "protogen": "протоген", "womangamer": "женщинаигрок", "obviouslyimagamer": "очевиднояигроман", "mario": "марио", "papermario": "бумажныймарий", "mariogolf": "мариогольф", "samusaran": "самусаран", "forager": "собиратель", "humanfallflat": "человечекупадении", "supernintendo": "супернидентдо", "nintendo64": "нитендо64", "zeroescape": "нулевоеизбавление", "waluigi": "валлуиджи", "nintendoswitch": "нинтендосвитч", "nintendosw": "нентендосв", "nintendomusic": "нинтендомузыка", "sonicthehedgehog": "соникежик", "sonic": "соник", "fallguys": "осеньгерои", "switch": "переключись", "zelda": "зельда", "smashbros": "смешанныебойцы", "legendofzelda": "легендаозельде", "splatoon": "сплатун", "metroid": "метроид", "pikmin": "пикмин", "ringfit": "рингфит", "amiibo": "амиибо", "megaman": "мегамен", "majorasmask": "маскамаджоры", "mariokartmaster": "мариокартмастер", "wii": "вии", "aceattorney": "аседелатель", "ssbm": "ссбм", "skychildrenofthelight": "детинебасвета", "tomodachilife": "томодачилифе", "ahatintime": "аха<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "слёзыкоролевства", "walkingsimulators": "симуляторыпоходов", "nintendogames": "ниндогеймы", "thelegendofzelda": "легендаозельде", "dragonquest": "драгонквест", "harvestmoon": "полнолуниеурожая", "mariobros": "мариоброс", "runefactory": "руническаяферма", "banjokazooie": "банжоказуи", "celeste": "целесте", "breathofthewild": "дыханиедикойприроды", "myfriendpedro": "мойприподруганпедро", "legendsofzelda": "легендыозельде", "donkeykong": "долларосвинья", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51игр", "earthbound": "земляк", "tales": "сказочки", "raymanlegends": "рэйменлегенды", "luigismansion": "люиджиуместе", "animalcrosssing": "анималкроссинг", "taikonotatsujin": "тайкоданасос", "nintendo3ds": "нінтендо3дс", "supermariobros": "супермариоброс", "mariomaker2": "мариомейкер2", "boktai": "бок<PERSON>ай", "smashultimate": "смэшультимейт", "nintendochile": "нинденточили", "tloz": "тлоz", "trianglestrategy": "треугольнаястратегия", "supermariomaker": "супермариомейкер", "xenobladechronicles3": "ксеноблейдхроникс3", "supermario64": "супермарио64", "conkersbadfurday": "конкерыплохаяшевелюра", "nintendos": "нитендос", "new3ds": "новый3дс", "donkeykongcountry2": "донкиконгстрана2", "hyrulewarriors": "гераймжвойны", "mariopartysuperstars": "мариопартиясуперзвезды", "marioandsonic": "мариоисоник", "banjotooie": "банжотоои", "nintendogs": "нинтендоги", "thezelda": "зельда", "palia": "палия", "marioandluigi": "мариоилю<PERSON><PERSON><PERSON>и", "mariorpg": "мариорпг", "zeldabotw": "зельдаботв", "yuumimain": "юумимайн", "wildrift": "вайлдрифты", "riven": "ривен", "ahri": "ахри", "illaoi": "иллаои", "aram": "арам", "cblol": "сблол", "leagueoflegendslas": "лигалегендлас", "urgot": "ургот", "zyra": "зира", "redcanids": "красныепсы", "vanillalol": "ванильлол", "wildriftph": "вайлдрифтпх", "lolph": "лолпх", "leagueoflegend": "лигалегенд", "tốcchiến": "токчьен", "gragas": "грагас", "leagueoflegendswild": "лигалегенддикое", "adcarry": "адкэри", "lolzinho": "лолзиньо", "leagueoflegendsespaña": "лигалегендиспании", "aatrox": "аатрокс", "euw": "еув", "leagueoflegendseuw": "лига传奇eu", "kayle": "кайла", "samira": "самира", "akali": "акали", "lunari": "лунари", "fnatic": "фнатиk", "lollcs": "лул<PERSON><PERSON>", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "миллио", "shaco": "шако", "ligadaslegendas": "слигадослегендами", "gaminglol": "гейминглол", "nasus": "насус", "teemo": "тимо", "zedmain": "зедмейн", "hexgates": "гексворота", "hextech": "хекстек", "fortnitegame": "фортифик<PERSON>йнт", "gamingfortnite": "геймингфор<PERSON><PERSON>йт", "fortnitebr": "фортнайтбрат", "retrovideogames": "ретровидеоигры", "scaryvideogames": "страшныеигры", "videogamemaker": "создательигр", "megamanzero": "мегаменноль", "videogame": "видеоигра", "videosgame": "видеоигры", "professorlayton": "профессорлейтон", "overwatch": "оверва<PERSON><PERSON>", "ow2": "ов2", "overwatch2": "оверватч2", "wizard101": "волшебник101", "battleblocktheater": "баттлблоктеатр", "arcades": "аркады", "acnh": "acnh", "puffpals": "пухляши", "farmingsimulator": "симуляторфермы", "robloxchile": "роблоксчиле", "roblox": "роблокс", "robloxdeutschland": "роблоксдойчленд", "robloxdeutsch": "роблоксдойч", "erlc": "ерлц", "sanboxgames": "песочницаигры", "videogamelore": "видеоигроваясказка", "rollerdrome": "роллердром", "parasiteeve": "паразитывечера", "gamecube": "геймкуб", "starcraft2": "старкрафт2", "duskwood": "сумеречныйлес", "dreamscape": "сказочныймир", "starcitizen": "звёздныйгражданин", "yanderesimulator": "яндересимулятор", "grandtheftauto": "грандворуютавто", "deadspace": "мертвоепространство", "amordoce": "сладкаялюбовь", "videogiochi": "видеоигры", "theoldrepublic": "стараяреспублика", "videospiele": "видеоигры", "touhouproject": "touhouproject", "dreamcast": "мечтателипроекции", "adventuregames": "приключенческиеигры", "wolfenstein": "волчонок", "actionadventure": "экшнприключение", "storyofseasons": "историясезонов", "retrogames": "ретрогеймс", "retroarcade": "ретроаркады", "vintagecomputing": "винтажныекомпьютеры", "retrogaming": "ретрогейминг", "vintagegaming": "винтажныеигры", "playdate": "игроваявстреча", "commanderkeen": "командоркин", "bugsnax": "ба<PERSON><PERSON><PERSON><PERSON>с", "injustice2": "несправедливость2", "shadowthehedgehog": "шаншедгог", "rayman": "райман", "skygame": "небеснаяигра", "zenlife": "дзенжизнь", "beatmaniaiidx": "битманияиidx", "steep": "круто", "mystgames": "мисти<PERSON><PERSON><PERSON><PERSON>с", "blockchaingaming": "блокчейнигры", "medievil": "медевил", "consolegaming": "консольныеигры", "konsolen": "консолен", "outrun": "обгони", "bloomingpanic": "цветущеепаника", "tobyfox": "тобифокс", "hoyoverse": "хойоверс", "senrankagura": "сенранкагура", "gaminghorror": "игровойужас", "monstergirlquest": "походзомбиательницей", "supergiant": "супергромила", "disneydreamlightvalle": "диснейдримлайтвалли", "farmingsims": "фермерскиемсимы", "juegosviejos": "старыеигры", "bethesda": "бeth<PERSON><PERSON>", "jackboxgames": "джекбоксигры", "interactivefiction": "интерактивнаяпроза", "pso2ngs": "псо2нжс", "grimfandango": "гримфанданго", "thelastofus2": "последниеизнасящих2", "amantesamentes": "любовьвлюбленности", "visualnovel": "визуальнаяновелла", "visualnovels": "визуальныероманы", "rgg": "ргг", "shadowolf": "шадоувулф", "tcrghost": "тцрпризрак", "payday": "деньзаплаты", "chatherine": "катерина", "twilightprincess": "сумеречнаяпринцесса", "jakandaxter": "дж<PERSON><PERSON>анддастер", "sandbox": "песочница", "aestheticgames": "эстетическиеигры", "novelavisual": "новелавизуал", "thecrew2": "крутаякоманда2", "alexkidd": "алекскид", "retrogame": "ретрогейм", "tonyhawkproskater": "тонихокпрофессиональныйскейтбордист", "smbz": "smbz", "lamento": "ламентозно", "godhand": "божественнаярука", "leafblowerrevolution": "революциявоздушныхгазонокосилок", "wiiu": "вииу", "leveldesign": "дизайнуровня", "starrail": "стардорога", "keyblade": "ключевоймеч", "aplaguetale": "аплагу<PERSON>йл", "fnafsometimes": "фнафиногда", "novelasvisuales": "визуальныеновеллы", "robloxbrasil": "роблоксбразилия", "pacman": "пакман", "gameretro": "геймеретро", "videojuejos": "видеоигрушки", "videogamedates": "геймерскедневники", "mycandylove": "мояконфетка", "megaten": "мегатен", "mortalkombat11": "морталкомбат11", "everskies": "эвирскайс", "justcause3": "простотак3", "hulkgames": "хулкигры", "batmangames": "батманигры", "returnofreckoning": "возвращениерасплаты", "gamstergaming": "геймерскаяигра", "dayofthetantacle": "деньщупальца", "maniacmansion": "маньякскипейзаже", "crashracing": "крэшрейсинг", "3dplatformers": "3dплатформеры", "nfsmw": "нфсмв", "kimigashine": "камигашине", "oldschoolgaming": "олдскульныеигры", "hellblade": "адскийшлем", "storygames": "историйныеигры", "bioware": "биовары", "residentevil6": "резидентивил6", "soundodger": "саундоджер", "beyondtwosouls": "запредельнодуэли", "gameuse": "играю", "offmortisghost": "позабудьпривидение", "tinybunny": "маленькийзаяц", "retroarch": "ретроарх", "powerup": "подзарядка", "katanazero": "катаназеро", "famicom": "фамиcom", "aventurasgraficas": "графическиеприключения", "quickflash": "быстросекундочка", "fzero": "фзеро", "gachagaming": "гачаигры", "retroarcades": "ретроатари", "f123": "f123", "wasteland": "пустошь", "powerwashsim": "супермойкасим", "coralisland": "коралловыйостров", "syberia3": "сибирь3", "grymmorpg": "гриммрпг", "bloxfruit": "блокфрут", "anotherworld": "другоймир", "metaquest": "метаквест", "animewarrios2": "анимевойны2", "footballfusion": "футбольныйфьюжн", "edithdlc": "едитдлс", "abzu": "абзу", "astroneer": "астронер", "legomarvel": "легомарвел", "wranduin": "врандуюн", "twistedmetal": "изогнутыйметалл", "beamngdrive": "beamngdrive", "twdg": "твдг", "pileofshame": "куча_срамоты", "simulator": "симулятор", "symulatory": "симуляторы", "speedrunner": "спидранн<PERSON>р", "epicx": "эпикс", "superrobottaisen": "суперроботтаисен", "dcuo": "дкуо", "samandmax": "саминама<PERSON>с", "grywideo": "гривидео", "gaiaonline": "гайдыо<PERSON><PERSON><PERSON><PERSON>н", "korkuoyunu": "корукоигра", "wonderlandonline": "мертваястранаонлайн", "skylander": "скаландр", "boyfrienddungeon": "дungeonдруг", "toontownrewritten": "переписанныйгород<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "пвп", "urbanchaos": "городскойхаос", "heavenlybodies": "божественныетела", "seum": "сум", "partyvideogames": "видеоигрынапати", "graveyardkeeper": "кладбищенскийстраж", "spaceflightsimulator": "симуляторкосмическихполетов", "legacyofkain": "наследиекайна", "hackandslash": "хакни_и_рубить", "foodandvideogames": "едаивидеоигры", "oyunvideoları": "игровыевидео", "thewolfamongus": "волксрединас", "truckingsimulator": "симуляторгрузовиков", "horizonworlds": "горизо<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "игралка", "leyendasyvideojuegos": "легендыигр", "oldschoolvideogames": "олдскульныеигры", "racingsimulator": "симуляторгонок", "beemov": "би<PERSON><PERSON>", "agentsofmayhem": "агентыпа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "songpop": "песенкидень", "famitsu": "фамицу", "gatesofolympus": "вратаолимпа", "monsterhunternow": "монстроловащас", "rebelstar": "бунтарскаягерл", "indievideogaming": "индигейминга", "indiegaming": "индигейминг", "indievideogames": "индиигры", "indievideogame": "индиигра", "chellfreeman": "челлфриман", "spidermaninsomniac": "человекпаукинсомнии", "bufffortress": "багфортресс", "unbeatable": "непобедимый", "projectl": "проектл", "futureclubgames": "будущиеигрыклуба", "mugman": "мягкийчашечник", "insomniacgames": "инсомниакгеймс", "supergiantgames": "супергигантскиеигры", "henrystickman": "хенристикмен", "henrystickmin": "хенристикмин", "celestegame": "целествигра", "aperturescience": "апертурнаянаука", "backlog": "бэклог", "gamebacklog": "игровойбэклог", "gamingbacklog": "игровойзадолженность", "personnagejeuxvidéos": "персона<PERSON><PERSON><PERSON><PERSON>р", "achievementhunter": "охотникнадостижения", "cityskylines": "городскиепейзажи", "supermonkeyball": "супермонкибол", "deponia": "депония", "naughtydog": "плохойпёс", "beastlord": "зверовластелин", "juegosretro": "ретроигры", "kentuckyroutezero": "кентуккийскаядорожка0", "oriandtheblindforest": "ориимслепомлесу", "alanwake": "аланв<PERSON><PERSON>к", "stanleyparable": "стэнлипарабола", "reservatoriodedopamin": "резервуардопамина", "staxel": "стаксель", "videogameost": "саундтреквидеоигр", "dragonsync": "драконисинк", "vivapiñata": "живипиñата", "ilovekofxv": "ялюблюкофxv", "arcanum": "арканум", "neoy2k": "неой2к", "pcracing": "пкгонки", "berserk": "беспредел", "baki": "баки", "sailormoon": "сейлормун", "saintseiya": "сейнтсейя", "inuyasha": "иниюаша", "yuyuhakusho": "ююх<PERSON><PERSON><PERSON><PERSON>o", "initiald": "инициалд", "elhazard": "элхазард", "dragonballz": "драгонбол<PERSON>з", "sadanime": "грустнаяаниме", "darkerthanblack": "темнейчемчерный", "animescaling": "анимескалипинг", "animewithplot": "анимесюжетом", "pesci": "песчи", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентай", "samuraichamploo": "самурайшампу", "madoka": "мадока", "higurashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "80sanime": "аниме80х", "90sanime": "90sаниме", "darklord": "тёмныйповелитель", "popeetheperformer": "попитеперформер", "masterpogi": "мастепоги", "samuraix": "самура<PERSON>х", "dbgt": "дбгт", "veranime": "вераниме", "2000sanime": "аниме2000х", "lupiniii": "лупиниии", "drstoneseason1": "докторкаменясезон1", "rapanime": "рэпаниме", "chargemanken": "зарядизарядных", "animecover": "анимекавер", "thevisionofescaflowne": "видениеэскафлоунэ", "slayers": "слайеры", "tokyomajin": "токийскиймоджи", "anime90s": "аниме90х", "animcharlotte": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>та", "gantz": "ганд<PERSON>", "shoujo": "сёдзё", "bananafish": "банановаярыба", "jujutsukaisen": "джюдзюцукейсен", "jjk": "jjk", "haikyu": "хайкью", "toiletboundhanakokun": "туалетныйханакокун", "bnha": "бнха", "hellsing": "хел<PERSON><PERSON><PERSON><PERSON>г", "skipbeatmanga": "скипбитманга", "vanitas": "ванитас", "fireforce": "огненнаясила", "moriartythepatriot": "мориартипатриот", "futurediary": "дневникбудущего", "fairytail": "фейритейл", "dorohedoro": "до<PERSON><PERSON>hed<PERSON>", "vinlandsaga": "винландсага", "madeinabyss": "сделановбездна", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushishi": "мушиши", "beastars": "бистары", "vanitasnocarte": "ванидаснокарт", "mermaidmelody": "русалочкоймелодия", "kamisamakiss": "камисамапоцелуй", "blmanga": "блманга", "horrormanga": "ужастикиманги", "romancemangas": "романтичныеманги", "karneval": "карнавальчик", "dragonmaid": "драконша", "blacklagoon": "чернаялагуна", "kentaromiura": "кентаромиура", "mobpsycho100": "мобпсихо100", "terraformars": "терраформары", "geniusinc": "гений<PERSON><PERSON><PERSON>", "shamanking": "шам<PERSON>н<PERSON><PERSON>нг", "kurokonobasket": "куроконобаскет", "jugo": "джуго", "bungostraydogs": "бунгострейдоги", "jujustukaisen": "джу<PERSON><PERSON>уцукайсен", "jujutsu": "дзюдзюцу", "yurionice": "юрионейсе", "acertainmagicalindex": "определенныймагическийиндекс", "sao": "сао", "blackclover": "блеккловер", "tokyoghoul": "токийскиймонстр", "onepunchman": "одинударчеловек", "hetalia": "хеталия", "kagerouproject": "кагероuproject", "haikyuu": "хайкью", "toaru": "тоару", "crunchyroll": "кранч<PERSON><PERSON><PERSON><PERSON>л", "aot": "атаковантитанами", "sk8theinfinity": "скейтбум", "siriusthejaeger": "сириустехантер", "spyxfamily": "шпионскаясемейка", "rezero": "резер0", "swordartonline": "мастерам<PERSON>чаонлайн", "dororo": "дороро", "wondereggpriority": "приоритетяйцачуда", "angelsofdeath": "ангелыморя", "kakeguri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "драгонболсупер", "hypnosismic": "гипносимсик", "goldenkamuy": "голденкамуй", "monstermusume": "монстрмусуме", "konosuba": "коносуба", "aikatsu": "айкатсу", "sportsanime": "спортивноеаниме", "sukasuka": "<PERSON><PERSON><PERSON><PERSON>", "arwinsgame": "арвинсигра", "angelbeats": "ангелыбита", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "сагадобанианасмерть", "shounenanime": "шounenаниме", "bandori": "бандори", "tanya": "таня", "durarara": "дюрарара", "prettycure": "красивоеисцеление", "theboyandthebeast": "мальчикилизверь", "fistofthenorthstar": "кулаксевернойзвезды", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "черныйдворецкий", "towerofgod": "башнябога", "elfenlied": "эльфенлид", "akunohana": "акуноконе", "chibi": "чиби", "servamp": "сервамп", "howtokeepamummy": "какдержатьмаму", "fullmoonwosagashite": "полнаялункавозажите", "shugochara": "шуготхара", "tokyomewmew": "токийскиекиски", "gugurekokkurisan": "гугурекокуккурисан", "cuteandcreepy": "миленькоиужасно", "martialpeak": "боевыевершины", "bakihanma": "бакыханма", "hiscoregirl": "хайскоргерл", "orochimaru": "орочимару", "mierukochan": "мирекочан", "dabi": "да<PERSON>и", "johnconstantine": "джонконстантин", "astolfo": "астolfo", "revanantfae": "реваншфэй", "shinji": "шид<PERSON>и", "zerotwo": "нольдва", "inosuke": "иносуке", "nezuko": "незуко", "monstergirl": "монстродевочка", "kanae": "канаэ", "yone": "<PERSON>он", "mitsuki": "митсуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "сайтама", "sanji": "сан<PERSON>и", "bakugo": "бакаго", "griffith": "гриффит", "ririn": "<PERSON>и<PERSON><PERSON><PERSON>", "korra": "кора", "vanny": "ванные", "vegeta": "вегета", "goromi": "горомий", "luci": "люци", "reigen": "рейген", "scaramouche": "скарамуш", "amiti": "амити", "sailorsaturn": "матросысатурн", "dio": "дио", "sailorpluto": "сейлорплюто", "aloy": "алой", "runa": "бегуна", "oldanime": "староеаниме", "chainsawman": "человекпилы", "bungoustraydogs": "бунгôустрейдоги", "jogo": "жога", "franziska": "франциска", "nekomimi": "некоминами", "inumimi": "инумиими", "isekai": "исекай", "tokyorevengers": "токийскиевестники", "blackbutler": "черныйдворецкий", "ergoproxy": "эргопрокси", "claymore": "клэймор", "loli": "лоли", "horroranime": "ужастикианиме", "fruitsbasket": "бананасмешки", "devilmancrybaby": "дьяволмужчинаплачущиймладенец", "noragami": "норагами", "mangalivre": "манга<PERSON>и<PERSON>р", "kuroshitsuji": "курошицуидзи", "seinen": "сэйнэн", "lovelive": "живилюбовью", "sakuracardcaptor": "сакуракарточныйзахватчик", "umibenoetranger": "умибеностранник", "owarinoseraph": "овакиносероф", "thepromisedneverland": "обещаннаястрана", "monstermanga": "монстрманга", "yourlieinapril": "твояложьвапреле", "buggytheclown": "баггиклоун", "bokunohero": "бокунохиро", "seraphoftheend": "серафимконца", "trigun": "тригун", "cyborg009": "киборг009", "magi": "маги", "deepseaprisoner": "глубоководныйузник", "jojolion": "жоёлойон", "deadmanwonderland": "мертвецвстранемечтателей", "bannafish": "бан<PERSON><PERSON><PERSON><PERSON>", "sukuna": "суккуна", "darwinsgame": "дарвиновыигры", "husbu": "мужик", "sugurugeto": "сегуругетто", "leviackerman": "левиаккерман", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "пандемисердца", "yoimiya": "йоимия", "foodwars": "бит<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "карточныезахватчикисакуры", "stolas": "столас", "devilsline": "дьявольскаялиния", "toyoureternity": "твоевечность", "infpanime": "инфпаниме", "eleceed": "элсиду", "akamegakill": "акамегакkill", "blueperiod": "синийпериод", "griffithberserk": "гриффит<PERSON><PERSON><PERSON>шек", "shinigami": "шинигами", "secretalliance": "секретныйальянс", "mirainikki": "мираини<PERSON>и", "mahoutsukainoyome": "маутсукайнойоме", "yuki": "юки", "erased": "стерто", "bluelock": "блюлок", "goblinslayer": "убийца<PERSON><PERSON><PERSON><PERSON><PERSON>нов", "detectiveconan": "детективконаан", "shiki": "шики", "deku": "деку", "akitoshinonome": "акитошинономэ", "riasgremory": "риасгрэмори", "shojobeat": "шодж<PERSON>б<PERSON>т", "vampireknight": "вампирскийрыцарь", "mugi": "муги", "blueexorcist": "голубойэкзорцист", "slamdunk": "слэмданк", "zatchbell": "зачарованныйдзюнджю", "mashle": "машле", "scryed": "сказала", "spyfamily": "шпионскаясемья", "airgear": "воздушныеколеса", "magicalgirl": "магическаядевочка", "thesevendeadlysins": "семьсмертныхгрехов", "prisonschool": "школавтюрьме", "thegodofhighschool": "божествошколы", "kissxsis": "поцелуйксис", "grandblue": "грандву", "mydressupdarling": "мойдрессапдарлинг", "dgrayman": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "rozenmaiden": "розенмейден", "animeuniverse": "анимеуниверсум", "swordartonlineabridge": "мостмоудртартонлайн", "saoabridged": "саосокращенное", "hoshizora": "хошизора", "dragonballgt": "драгонболлтг", "bocchitherock": "боккитерок", "kakegurui": "какегуруи", "mobpyscho100": "мобпсихо100", "hajimenoippo": "первыйшаг", "undeadunluck": "недоумереть", "romancemanga": "романтическаяманга", "blmanhwa": "блманхва", "kimetsunoyaba": "кимецунодзёба", "kohai": "кохай", "animeromance": "анимеромантика", "senpai": "сенпай", "blmanhwas": "блманхвы", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "демонубийца_к_мечу", "bloodlad": "кровянойбрат", "goodbyeeri": "прощайери", "firepunch": "огненныйудар", "adioseri": "адьёсери", "tatsukifujimoto": "татсукифудзимото", "kinnikuman": "кинни<PERSON>у<PERSON>ан", "mushokutensei": "мушокутенсей", "shoujoai": "схуджоай", "starsalign": "звёздысходятся", "romanceanime": "романтикааниме", "tsundere": "цундере", "yandere": "яандере", "mahoushoujomadoka": "махоушодзёмадока", "kenganashura": "кэнга<PERSON>шура", "saointegralfactor": "саоинтегральфактор", "cherrymagic": "вишневое_волшебство", "housekinokuni": "домакнигокунь", "recordragnarok": "записьрагнарок", "oyasumipunpun": "оясумипунпун", "meliodas": "мелиодас", "fudanshi": "фудан<PERSON>и", "retromanga": "ретроманга", "highschoolofthedead": "школа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "гертекно", "oshinoko": "ошиноко", "ansatsukyoushitsu": "ансат<PERSON>укиёсицу", "vindlandsaga": "виндландасага", "mangaka": "мангака", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "принцтенниса", "tonikawa": "тониква", "esdeath": "есдэс", "dokurachan": "докурачан", "bjalex": "б<PERSON>а<PERSON><PERSON><PERSON>с", "assassinclassroom": "классубийств", "animemanga": "аниме漫画", "bakuman": "бакуман", "deathparade": "парадсмерти", "shokugekinosouma": "шокугэкиносома", "japaneseanime": "японскоеаниме", "animespace": "анимеосвобождение", "girlsundpanzer": "девчонки_в_танке", "akb0048": "акб0048", "hopeanuoli": "надеждаанули", "animedub": "анимеозвучка", "animanga": "анимага", "tsurune": "цуруне", "uqholder": "юкхолдер", "indieanime": "индианиме", "bungoustray": "бунгомастера", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "гандом0", "animescifi": "анимекосмос", "ratman": "крысиныйчеловек", "haremanime": "хареаниме", "kochikame": "kochikame", "nekoboy": "некобой", "gashbell": "гашбелл", "peachgirl": "персиковаядевочка", "cavalieridellozodiaco": "рыцаризодиака", "mechamusume": "механодевочки", "nijigasaki": "нидзукасаки", "yarichinbitchclub": "ярычинбичклаб", "dragonquestdai": "драгонквестдай", "heartofmanga": "сердцеаниме", "deliciousindungeon": "вкусновданже", "manhviyaoi": "манхваиайоий", "recordofragnarok": "записьрагарока", "funamusea": "веселаямузейка", "hiranotokagiura": "хиранотаканура", "mangaanime": "mangaаниме", "bochitherock": "бочитирок", "kamisamahajimemashita": "камисамахадзимэмасита", "skiptoloafer": "скиптолафер", "shuumatsunovalkyrie": "шуумацуновалькирия", "tutorialistoohard": "учебаислишкомтрудна", "overgeared": "надоумел", "toriko": "торико", "ravemaster": "рэйвмастер", "kkondae": "ккндае", "chobits": "чобиццы", "witchhatatelier": "ведьминскапюшонка", "lansizhui": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангатсунолайон", "kamen": "камень", "mangaislife": "мангаж<PERSON>знь", "dropsofgod": "каплибога", "loscaballerosdelzodia": "рыцари_зодиака", "animeshojo": "аниме_девчонка", "reverseharem": "реверсхарем", "saintsaeya": "сейнтсейя", "greatteacheronizuka": "крутойучительонидзука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "салдат", "mybossdaddy": "мойбосспапочка", "gear5": "гир5", "grandbluedreaming": "грандвстречиснами", "bloodplus": "кровьплюс", "bloodplusanime": "кровьплюсаниме", "bloodcanime": "кровавоеаниме", "bloodc": "кровь", "talesofdemonsandgods": "сказкиодаровисущихибогов", "goreanime": "гореаниме", "animegirls": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "воробьиxплохие", "splatteranime": "сплэттераниме", "splatter": "плеск", "risingoftheshieldhero": "восстаниещитощитгероя", "somalianime": "сомалийскоеаниме", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "съедокатусим", "animeyuri": "анимеюри", "animeespaña": "анимеиспания", "animeciudadreal": "анимесьюдадреал", "murim": "мурим", "netjuunosusume": "нетюносуэми", "childrenofthewhales": "детиочковкитов", "liarliar": "л<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "суперкампеоны", "animeidols": "анимеидолы", "isekaiwasmartphone": "исекайбылумнымсмартфоном", "midorinohibi": "мидо<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>би", "magicalgirls": "магические_девчонки", "callofthenight": "звонокночи", "bakuganbrawler": "бакугандрака", "bakuganbrawlers": "бакуга́нбро́улеры", "natsuki": "натсуки", "mahoushoujo": "магическаядевочка", "shadowgarden": "теневойсад", "tsubasachronicle": "цубасахроники", "findermanga": "найдимангу", "princessjellyfish": "принцессащупальца", "kuragehime": "курэдзимэ", "paradisekiss": "поцелуйвраю", "kurochan": "куручан", "revuestarlight": "переосмислениенастоящемзвезде", "animeverse": "анимеоблако", "persocoms": "персокомы", "omniscientreadersview": "взглядвсезнающегочитателя", "animecat": "анимекот", "animerecommendations": "рекомендациипоаниме", "openinganime": "анимеоткрытие", "shinichirowatanabe": "шиничироватанабэ", "uzumaki": "узумаки", "myteenromanticcomedy": "мояподростковаяромантическаямелодрама", "evangelion": "евангелион", "gundam": "гандамы", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гандамсы", "voltesv": "вальтесв", "giantrobots": "гигантскиеромбы", "neongenesisevangelion": "неонгенысевангелион", "codegeass": "кодгяс", "mobilefighterggundam": "мобильныйбоецггундом", "neonevangelion": "неоневагелион", "mobilesuitgundam": "мобайлсьютгандан", "mech": "меч", "eurekaseven": "еврикасемь", "eureka7": "эврика7", "thebigoanime": "бигоданиме", "bleach": "блич", "deathnote": "смертельнаятетрадь", "cowboybebop": "кавалерибипоп", "jjba": "джджба", "jojosbizarreadventure": "диковинныеприключенияджоджо", "fullmetalalchemist": "полныйметаллалхимик", "ghiaccio": "лед", "jojobizarreadventures": "джоджобизарныеприключения", "kamuiyato": "камуюято", "militaryanime": "военнаяаниме", "greenranger": "зеленыйрейнджер", "jimmykudo": "д<PERSON>и<PERSON>и<PERSON>ид<PERSON>у", "tokyorev": "токиорев", "zorro": "зорро", "leonscottkennedy": "леонскотткеннеди", "korosensei": "коросенсей", "starfox": "старфокс", "ultraman": "ультрамэн", "salondelmanga": "салонделманги", "lupinthe3rd": "лунпинтретий", "animecity": "анимегород", "animetamil": "аниметамил", "jojoanime": "джоджоаниме", "naruto": "наруто", "narutoshippuden": "нарутошиппуден", "onepiece": "одинкусок", "animeonepiece": "анимеопиc", "dbz": "дб<PERSON>", "dragonball": "драгонбол", "yugioh": "югиох", "digimon": "дигимоны", "digimonadventure": "дигимонадветура", "hxh": "hxх", "highschooldxd": "старшеклассникидд", "goku": "гоку", "broly": "броули", "shonenanime": "шоненаниме", "bokunoheroacademia": "моегерояакадемия", "jujustukaitsen": "жужустукайтсен", "drstone": "докторкамень", "kimetsunoyaiba": "кимецунояиба", "shonenjump": "шоненджамп", "otaka": "отакa", "hunterxhunter": "хантерксхантер", "mha": "мха", "demonslayer": "убийцадемонов", "hinokamikagurademonsl": "хинокамикагурадемонсл", "attackontitan": "ат<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "ереньувлекает", "myheroacademia": "моегеройакадемия", "boruto": "боруто", "rwby": "руби", "dandadan": "да<PERSON><PERSON>а<PERSON><PERSON>н", "tomodachigame": "томодачитагейм", "akatsuki": "акацуки", "surveycorps": "сurveyкорпус", "onepieceanime": "анимеодинкусок", "attaquedestitans": "атакатитановых", "theonepieceisreal": "одинкусочекреален", "revengers": "ревенджеры", "mobpsycho": "мобпсиха", "aonoexorcist": "аяноексорцист", "joyboyeffect": "эффектрадостногопарня", "digimonstory": "диджимонсказка", "digimontamers": "диджимонтамеры", "superjail": "суперкутузка", "metalocalypse": "металокаипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "ватамотэ", "uramichioniisan": "урамитиони<PERSON><PERSON>н", "uruseiyatsura": "урусейяцура", "gintama": "гинтама", "ranma": "ранма", "doraemon": "дораэмон", "gto": "гто", "ouranhostclub": "нашклубхостов", "flawlesswebtoon": "безупречныйвебтун", "kemonofriends": "кэмоновдрузьях", "utanoprincesama": "утаноприинцесама", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юук<PERSON>ю<PERSON>на", "nichijou": "ничиджо", "yurucamp": "юрюкэмп", "nonnonbiyori": "ноннонбиёри", "flyingwitch": "летучаяведьма", "wotakoi": "вотакаи", "konanime": "конаниме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "простотак", "horimiya": "хоримия", "allsaintsstreet": "всехсвятыхулица", "recuentosdelavida": "пересказжизни"}