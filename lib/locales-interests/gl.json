{"2048": "2048", "mbti": "mbti", "enneagram": "enneagrama", "astrology": "astroloxía", "cognitivefunctions": "funciónscognitivas", "psychology": "psicoloxía", "philosophy": "filosofía", "history": "historia", "physics": "física", "science": "ciencia", "culture": "cultura", "languages": "idiomas", "technology": "tecnoloxía", "memes": "memes", "mbtimemes": "memesde<PERSON><PERSON>", "astrologymemes": "astroloxíadememes", "enneagrammemes": "memesdeenneagrama", "showerthoughts": "reflexiónsvarias", "funny": "diversión", "videos": "vídeos", "gadgets": "dispositivos", "politics": "política", "relationshipadvice": "consellosderelación", "lifeadvice": "consellosparaavida", "crypto": "cripto", "news": "noticias", "worldnews": "noticiasdomundo", "archaeology": "arqueoloxía", "learning": "aprender", "debates": "debates", "conspiracytheories": "teoriasconspiratorias", "universe": "universo", "meditation": "meditación", "mythology": "mitoloxía", "art": "arte", "crafts": "artesanía", "dance": "danza", "design": "deseño", "makeup": "ma<PERSON><PERSON><PERSON>", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "moda", "singing": "cantar", "writing": "escribir", "photography": "fotografía", "cosplay": "cosplay", "painting": "pintura", "drawing": "debuxar", "books": "libros", "movies": "filmes", "poetry": "poesía", "television": "televisión", "filmmaking": "cine", "animation": "animación", "anime": "anime", "scifi": "cienciaficción", "fantasy": "fantasía", "documentaries": "documentais", "mystery": "misterio", "comedy": "comedia", "crime": "crimes", "drama": "teatro", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "terror", "romance": "romance", "realitytv": "realities", "action": "acción", "music": "música", "blues": "blues", "classical": "clásica", "country": "country", "desi": "desi", "edm": "edm", "electronic": "electrónica", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latina", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "músicarnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tecno", "travel": "viaxar", "concerts": "concertos", "festivals": "festivais", "museums": "museos", "standup": "comedia", "theater": "teatro", "outdoors": "airelibre", "gardening": "xardinería", "partying": "festas", "gaming": "xogos", "boardgames": "xogosdemesa", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "comida", "baking": "repostaría", "cooking": "cociña", "vegetarian": "vexe<PERSON>o", "vegan": "vegano", "birds": "paxaros", "cats": "gatos", "dogs": "cans", "fish": "peixes", "animals": "animais", "blacklivesmatter": "blacklivesmatter", "environmentalism": "medioambiente", "feminism": "feminismo", "humanrights": "dereit<PERSON><PERSON>s", "lgbtqally": "aliadolgbt", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "aliadotrans", "volunteering": "voluntariado", "sports": "deportes", "badminton": "badminton", "baseball": "beisbol", "basketball": "baloncesto", "boxing": "boxeo", "cricket": "cr<PERSON><PERSON>", "cycling": "ciclismo", "fitness": "fitness", "football": "fútbol", "golf": "golf", "gym": "ximnasio", "gymnastics": "ximnasia", "hockey": "<PERSON><PERSON><PERSON>", "martialarts": "artesmarcia<PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "correr", "skateboarding": "skate", "skiing": "esquí", "snowboarding": "snowboard", "surfing": "surf", "swimming": "natación", "tennis": "tenis", "volleyball": "voleibol", "weightlifting": "pesas", "yoga": "ioga", "scubadiving": "mergullo", "hiking": "sendeirismo", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "acuario", "pisces": "pisces", "aries": "aries", "taurus": "tauro", "gemini": "<PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "escorpión", "sagittarius": "saxitario", "shortterm": "cortoprazo", "casual": "informal", "longtermrelationship": "rela<PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "solt<PERSON>", "polyamory": "poliamor", "enm": "solt<PERSON>", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbiana", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "idadedragón", "assassinscreed": "asesinosdacrede", "saintsrow": "santoscamiños", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "gardi<PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "questdosreis", "soulreaver": "recolectordealmas", "suikoden": "su<PERSON><PERSON>", "subverse": "subversa", "legendofspyro": "lendariospyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildeguerras", "openworld": "mund<PERSON><PERSON>", "heroesofthestorm": "heroisdotormenta", "cytus": "cytus", "soulslike": "almassemellantes", "dungeoncrawling": "explorando<PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribosdemidgard", "planescape": "planosdeescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simsinmersivos", "okage": "okage", "juegoderol": "roloso", "witcher": "mago", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "almaoscura", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "caída", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "valoresanciáns", "modding": "modding", "charactercreation": "creacióndepersonaxes", "immersive": "immersivo", "falloutnewvegas": "caídanevadavegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "fantasíafinal", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivaciónmórbida", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "molloxadoapolaamor", "otomegames": "xogosotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirosemascarada", "dimension20": "dimensió20", "gaslands": "gaslands", "pathfinder": "cami<PERSON><PERSON>", "pathfinder2ndedition": "camiñante2edición", "shadowrun": "saltopantasma", "bloodontheclocktower": "sangreonsincrodo", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitarrabo", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "unhasolatoma", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "sobrepatriarca", "yourturntodie": "oitusparadear", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtexto", "genshin": "gemsin", "eso": "iso", "diablo2": "diabló2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demons<PERSON>mas", "mu": "mu", "falloutshelter": "refuxiodacaída", "gurps": "gurps", "darkestdungeon": "mazmorrasmaisescurecidas", "eclipsephase": "faseeclipse", "disgaea": "disgaea", "outerworlds": "mundosafóra", "arpg": "arpg", "crpg": "rpgsdecrónicas", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloinmortal", "dynastywarriors": "guerrerosdadinastía", "skullgirls": "esqueletaxas", "nightcity": "noitecidade", "hogwartslegacy": "herdadegalego", "madnesscombat": "lo<PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "aliñamentoagullado2", "neverwinter": "nuncainverno", "road96": "camiño96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "reinosesquecidos", "dragonlance": "lanzadron", "arenaofvalor": "arenadevalor", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "adivisión2", "lineage2": "lineage2", "digimonworld": "mund<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverso", "fracturedthrones": "tronosfracturados", "horizonforbiddenwest": "horizonteprohibidooeste", "twewy": "twewy", "shadowpunk": "sombrapunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "misteriohogwarts", "deltagreen": "deltaverde", "diablo": "diabo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "xust<PERSON>", "lastepoch": "últimaepoca", "starfinder": "encontradordeestrelas", "goldensun": "soldourado", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "lâminasnasombra", "twilight2000": "crepúsculo2000", "sandevistan": "sandevistan", "cyberpunk": "ciberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkvermello", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordecaído", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "terrasmalas", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diablodevivir", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divindade", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "blues<PERSON>un<PERSON><PERSON><PERSON><PERSON>", "adventurequest": "aventuraquest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "xogosderole", "roleplayinggames": "xogosderol", "finalfantasy9": "finalfantasy9", "sunhaven": "solpazo", "talesofsymphonia": "contosdesimfonía", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "sacredeinferno", "chainedechoes": "cadeadoecos", "darksoul": "almadoiro", "soulslikes": "almassemellantes", "othercide": "outracide", "mountandblade": "montaixogo", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "cronotranscendencia", "pillarsofeternity": "pilasdeeternidade", "palladiumrpg": "palladiumrpg", "rifts": "fendas", "tibia": "tibia", "thedivision": "adivisión", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "lendaofragón", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "oapocalipselobo", "aveyond": "aveyond", "littlewood": "pequenowood", "childrenofmorta": "nenosdemor<PERSON>", "engineheart": "motorcoraçao", "fable3": "fable3", "fablethelostchapter": "fábulaocapítuloperdido", "hiveswap": "hiveswap", "rollenspiel": "rold<PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneterno", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "revivaloldschool", "finalfantasy12": "fantasíafinal12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mundosalvaxes", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "reinadoacorazón1", "ff9": "ff9", "kingdomheart2": "reinosoñas2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "xogosrpg", "kingdomhearts": "reinohearts", "kingdomheart3": "reinosdakora3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "recolección", "gloomhaven": "gloomhaven", "wildhearts": "cousaswild", "bastion": "bastión", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "céusdearcadia", "shadowhearts": "coraçõesnasombra", "nierreplicant": "nierreplicante", "gnosia": "gnosia", "pennyblood": "sangue<PERSON>ny", "breathoffire4": "respiraciondefogo4", "mother3": "maio3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "outroeden", "roleplaygames": "xogosderol", "roleplaygame": "xogoderol", "fabulaultima": "fabulault<PERSON>", "witchsheart": "corazóndebruxa", "harrypottergame": "x<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "camiñante2e", "vampirilamasquerade": "vampirilamascarada", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonageorixes", "chronocross": "cronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstrosdecaçantomundo", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "foroderpg", "shadowheartscovenant": "covenantdosheartshadow", "bladesoul": "almaserrada", "baldursgate3": "baldursgate3", "kingdomcome": "reina<PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "omundoacabacontigo", "dragalialost": "dragalialost", "elderscroll": "anteriorrol", "dyinglight2": "morrenluz2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "herex<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "fororpg", "golarion": "golarion", "earthmagic": "maxiaterra", "blackbook": "libronegro", "skychildrenoflight": "nenosdoceo", "gryrpg": "gry<PERSON><PERSON>", "sacredgoldedition": "edicióndourasagrada", "castlecrashers": "castelocrasher", "gothicgame": "xogogótico", "scarletnexus": "nexuscarmesí", "ghostwiretokyo": "fantasmapontetokyo", "fallout2d20": "fallout2d20", "gamingrpg": "videoxogosrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "cidadeneboa", "indierpg": "indierpg", "pointandclick": "apuntaefaiclic", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisible", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postciberpunk", "deathroadtocanada": "morteirouteacanadá", "palladium": "palladio", "knightjdr": "cabaleirojdr", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "<PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacía", "persona5": "persona5", "ghostoftsushima": "fantasmadetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monstronacimento", "nier": "nier", "dothack": "fagohack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "xogosnonarios", "tacticalrpg": "rpgtáctico", "mahoyo": "mahoyo", "animegames": "xogosdeanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "comedor<PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON>ata", "princessconnect": "princessconnect", "hexenzirkel": "circulodebruxas", "cristales": "cristais", "vcs": "vcs", "pes": "peso", "pocketsage": "pocketmestre", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindio", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esporte", "mlg": "mlg", "leagueofdreamers": "ligaofdeseos", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "so<PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "ciberdeporte", "crazyraccoon": "raccoraro", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitivo", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "mediavida", "left4dead": "deixadospormortos", "left4dead2": "esquerdoparaosmortos2", "valve": "vál<PERSON>la", "portal": "portal", "teamfortress2": "equipoagofortaleza2", "everlastingsummer": "veráninmemoria", "goatsimulator": "simuladordecabras", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "liberdadeplanetaria", "transformice": "transformice", "justshapesandbeats": "soamenteformaserympulsos", "battlefield4": "campodebatalla4", "nightinthewoods": "noiteenlos<PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riscodechuvia2", "metroidvanias": "metroidvanias", "overcooked": "pasadodecocción", "interplanetary": "interplanetario", "helltaker": "helltaker", "inscryption": "inscrición", "7d2d": "7d2d", "deadcells": "célulasmortas", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortalezaenana", "foxhole": "foxhole", "stray": "estray", "battlefield": "campodebatall<PERSON>", "battlefield1": "campodeguerra1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "mira", "blackdesert": "desertonegro", "tabletopsimulator": "simuladordetaboleiros", "partyhard": "festaaotodo", "hardspaceshipbreaker": "dificilespacetrinc<PERSON>", "hades": "hades", "gunsmith": "armoeiro", "okami": "<PERSON>ami", "trappedwithjester": "atrapadoscunjester", "dinkum": "dinkum", "predecessor": "predecesor", "rainworld": "mundodochu<PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "coloniasim", "noita": "noita", "dawnofwar": "amane<PERSON><PERSON><PERSON><PERSON>", "minionmasters": "mestresdemiñóns", "grimdawn": "amaneceroscuro", "darkanddarker": "escuroandmáisescuro", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "traballadoradesalma", "datingsims": "simulad<PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "escapeocubo", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novacidade", "citiesskylines": "pais<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconpesado", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiavirtual", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bibliotecaderuina", "l4d2": "l4d2", "thenonarygames": "osxogosnonarios", "omegastrikers": "omegastrikers", "wayfinder": "caminante", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "pat<PERSON><PERSON><PERSON><PERSON><PERSON>oapa<PERSON>", "battlebit": "batallabite", "ultimatechickenhorse": "ulti<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "dialcidade", "smileforme": "<PERSON><PERSON><PERSON><PERSON>", "catnight": "noitegato", "supermeatboy": "supermeatboy", "tinnybunny": "ollopequeño", "cozygrove": "cozygrove", "doom": "finmáxino", "callofduty": "chama<PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "chamadadeberroww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "bacalao", "borderlands": "terrasdefronteira", "pubg": "pubg", "callofdutyzombies": "chama<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "fugida", "farcrygames": "xogosfarcry", "paladins": "palad<PERSON><PERSON>", "earthdefenseforce": "forzadadefensadaterra", "huntshowdown": "cazaenfrentamento", "ghostrecon": "fantasmarecon", "grandtheftauto5": "grandtheftauto5", "warz": "wars", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "uniaosasquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurrecciónearexente", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "asesino3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "mort<PERSON>lazament<PERSON>", "b4b": "b4b", "codwarzone": "codzonadeguerra", "callofdutywarzone": "chamadaaodeberguerrazona", "codzombies": "codzombies", "mirrorsedge": "bord<PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisions2", "killzone": "zonademorte", "helghan": "hel<PERSON>", "coldwarzombies": "fr<PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "asubidaacecombat", "crosscode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "francotiradorelite", "modernwarfare": "guerramoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "borderlands", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>cap<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnaxedeprimal", "worldofwarships": "mundodosbarcosdeguerra", "back4blood": "devolta4sangue", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "matador", "masseffect": "masseffect", "systemshock": "sistemachoque", "valkyriachronicles": "crónicasdevalquiria", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cavestory", "doometernal": "doomedeternal", "centuryageofashes": "centuriaidadecinzas", "farcry4": "farcry4", "gearsofwar": "engrenaxesdelaguerra", "mwo": "mwo", "division2": "división2", "tythetasmaniantiger": "titheta<PERSON><PERSON><PERSON>", "generationzero": "xeraciónzero", "enterthegungeon": "entrenafogar", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "guerramoderna2", "blackops1": "blackops1", "sausageman": "sa<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "dolorphanta<PERSON>", "warface": "guerraxente", "crossfire": "tiroteo", "atomicheart": "coraçãoatómico", "blackops3": "blackops3", "vampiresurvivors": "vampirosobreviventes", "callofdutybatleroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "liberdade", "battlegrounds": "terreodeloita", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearfilhosdaliñaza", "juegosfps": "xogosfps", "convertstrike": "convertemos", "warzone2": "zonadeguerra2", "shatterline": "liñafracturada", "blackopszombies": "blackopszombies", "bloodymess": "desastreabsoluto", "republiccommando": "comandorepublicano", "elitedangerous": "elite<PERSON><PERSON><PERSON>", "soldat": "soldado", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "equipo", "destiny1": "destino1", "gamingfps": "xogosfps", "redfall": "caídaenroxo", "pubggirl": "pubggirl", "worldoftanksblitz": "mundotanquesblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "alistado", "farlight": "luzdestañoite", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "núcleodeblindaxe", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "pagrado2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgucraína", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubg<PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "xabóncod", "ghostcod": "espíritucódigo", "csplay": "csplay", "unrealtournament": "torneorealista", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "fronteiras2", "counterstrike": "contrataque", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "terremotistas", "halo3": "halo3", "halo": "halo", "killingfloor": "chocand<PERSON><PERSON><PERSON>", "destiny2": "destino2", "exoprimal": "exoprimal", "splintercell": "cellasplinter", "neonwhite": "neonbranco", "remnant": "resto", "azurelane": "azurelane", "worldofwar": "mundodague<PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "retornoal", "halo4": "halo4", "haloreach": "holaal<PERSON><PERSON>", "shadowman": "homensombra", "quake2": "quake2", "microvolts": "microvolts", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "campodebatalla3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "marde<PERSON><PERSON><PERSON><PERSON>", "rust": "óxido", "conqueronline": "conquistaronline", "dauntless": "indomable", "warships": "barcosdeguerra", "dayofdragons": "d<PERSON>dosdrag<PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "lendasderuneterra", "pso2": "pso2", "myster": "misterio", "phantasystaronline2": "fantasystaronline2", "maidenless": "senn<PERSON>va", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "riscar", "agario": "agario", "secondlife": "segundavid<PERSON>", "aion": "aion", "toweroffantasy": "torredefantasía", "netplay": "xogoenrede", "everquest": "buscadoraventuras", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "cabaleiroenlínia", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "lancelode<PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguín", "lotro": "lotro", "wakfu": "wakfu", "scum": "escuma", "newworld": "novomundo", "blackdesertonline": "desertonegroenliña", "multiplayer": "multixogos", "pirate101": "pirata101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "batalladeestarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclásico", "worldofwarcraft": "mundodewarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cinzasdecreación", "riotmmo": "<PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "noitespi<PERSON><PERSON>", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragónsprofeta", "grymmo": "grymmo", "warmane": "caloroso", "multijugador": "multixogador", "angelsonline": "anxosonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversoonline", "growtopia": "crecementopia", "starwarsoldrepublic": "starwarsrepúblicaantiga", "grandfantasia": "grandfantasía", "blueprotocol": "protocoloblue", "perfectworld": "mundoideal", "riseonline": "elevaonline", "corepunk": "corepunk", "adventurequestworlds": "aventuraquest<PERSON><PERSON><PERSON>", "flyforfun": "voaaporfuntime", "animaljam": "animaljam", "kingdomofloathing": "reinododescontento", "cityofheroes": "cidadedosherois", "mortalkombat": "mortalkombat", "streetfighter": "luchadorderúa", "hollowknight": "cavernasdoespectro", "metalgearsolid": "metalgearsolid", "forhonor": "porhonor", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtufighter", "streetsofrage": "ruasdefraga", "mkdeadlyalliance": "mkalianzamortáábrica", "nomoreheroes": "no<PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "okingdosloitadores", "likeadragon": "comoundragón", "retrofightinggames": "xogosdepelexaantigos", "blasphemous": "blasfemo", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "guerradasmonstros", "jogosdeluta": "xogos<PERSON><PERSON>", "cyberbots": "ciberbots", "armoredwarriors": "guerrerosblindados", "finalfight": "loitafinal", "poweredgear": "equipopoderoso", "beatemup": "bat<PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "luchasdevideojogos", "killerinstinct": "<PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "fantasman", "chivalry2": "cavalaloxía2", "demonssouls": "almasdemonios", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelhollowknight", "hollowknightsilksong": "hollowknightcanciónseda", "silksonghornet": "silksonghive", "silksonggame": "silksonggame", "silksongnews": "novidadesdesilksong", "silksong": "silksong", "undernight": "sobreacompañante", "typelumina": "tipolumina", "evolutiontournament": "torneoevolución", "evomoment": "evomomento", "lollipopchainsaw": "lollipopcadenas", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "contosdebreseria", "bloodborne": "sangueen<PERSON>te", "horizon": "horizonte", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "ranchoslime", "crashbandicoot": "crashbandicoot", "bloodbourne": "sanguebourn", "uncharted": "sen<PERSON><PERSON>", "horizonzerodawn": "horizontezerodende", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "ultimodosnosos", "infamous": "infame", "playstationbuddies": "amigosplaystation", "ps1": "ps1", "oddworld": "mundoestraño", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persoa4", "hellletloose": "liberao<PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "companyrebeldes", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "trove", "detroitbecomehuman": "detroitconvertirhumano", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "ataaoitobá", "touristtrophy": "trofeoturista", "lspdfr": "lspdfr", "shadowofthecolossus": "sombradocoloso", "crashteamracing": "crashteamracing", "fivepd": "cincopd", "tekken7": "tekken7", "devilmaycry": "diaboquedequitar", "devilmaycry3": "diablepodechorar3", "devilmaycry5": "diablomodechora5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "almaespada", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "cazahomens", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "ultimoguardián", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "animaldecelebración", "warharmmer40k": "warhammmer40k", "fightnightchampion": "no<PERSON><PERSON><PERSON>", "psychonauts": "psiconauts", "mhw": "mhw", "princeofpersia": "príncipedepersia", "theelderscrollsskyrim": "osvellosroldosskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "osanosenosrollos", "gxbox": "gxbox", "battlefront": "frontebata<PERSON>", "dontstarvetogether": "nonaestredesconxuntos", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "estrelaround", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "flipadordecasas", "americanmcgeesalice": "aliceamericandemcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligadosreinos", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telebasura", "skycotl": "ceo<PERSON><PERSON>", "erica": "erica", "ancestory": "antepasados", "cuphead": "cuphead", "littlemisfortune": "pequenadesgracia", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstrebromance", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultolacordeira", "duckgame": "xogodaspatacas", "thestanleyparable": "thestanleyparable", "towerunite": "torreunida", "occulto": "oculto", "longdrive": "longdrive", "satisfactory": "satisfactorio", "pluviophile": "pluviófilo", "underearth": "subterra", "assettocorsa": "assettocorsa", "geometrydash": "geometriadash", "kerbal": "kerbal", "kerbalspaceprogram": "programadeespazokerbal", "kenshi": "kenshi", "spiritfarer": "espíritomoeiro", "darkdome": "<PERSON><PERSON><PERSON>", "pizzatower": "torredapizza", "indiegame": "xogodeindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "xogo", "rockpaperscissors": "papelpedrascortés", "trampoline": "trampolín", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "atrever", "scavengerhunt": "cazapan<PERSON>las", "yardgames": "xogosdepatio", "pickanumber": "elixeunnúmero", "trueorfalse": "verda<PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "xogosacougo", "datinggames": "xogosdedate", "freegame": "<PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "xogosdebeber", "sodoku": "sodoku", "juegos": "xogos", "mahjong": "mahjong", "jeux": "jogos", "simulationgames": "xogosdesimulación", "wordgames": "xogosdepalabras", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "xogosdepalabras", "letsplayagame": "xogue<PERSON>un<PERSON><PERSON>", "boredgames": "xogosaboredo", "oyun": "ou", "interactivegames": "xogosinteractivos", "amtgard": "amtgard", "staringcontests": "concurs<PERSON><PERSON><PERSON><PERSON>", "spiele": "xogar", "giochi": "xogos", "geoguessr": "geoguessr", "iphonegames": "xogosdoiphone", "boogames": "boox<PERSON>s", "cranegame": "xogodegrúa", "hideandseek": "agochoesconde", "hopscotch": "pipas", "arcadegames": "xogosdearcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON>ogo<PERSON>", "classicgame": "xogoclásico", "mindgames": "xogosdemente", "guessthelyric": "adivinaasletras", "galagames": "galajogos", "romancegame": "xogoderomance", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "trabalenguas", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "xogosdearcade", "tabletopgames": "xogosdetaboleiro", "metroidvania": "metroidvania", "games90": "xogos90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "xogosdecarreiras", "ets2": "ets2", "realvsfake": "realvsgalego", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "xogoenliña", "onlinegames": "xogosonline", "jogosonline": "xogosonline", "writtenroleplay": "roles<PERSON>rito", "playaballgame": "x<PERSON><PERSON><PERSON><PERSON>", "pictionary": "dibujaeamatiranza", "coopgames": "xogoscooperativos", "jenga": "xenga", "wiigames": "wiixogos", "highscore": "máximapuntuación", "jeuxderôles": "xogosder<PERSON>s", "burgergames": "xogosdehamburguesa", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmweditiónnegra", "jeuconcour": "xogocompetición", "tcgplayer": "tcgplayer", "juegodepreguntas": "xogodepreguntas", "gioco": "xogo", "managementgame": "xogodexestión", "hiddenobjectgame": "xogoobxectosocultos", "roolipelit": "ruli<PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "xogodef1", "citybuilder": "construtoradecidades", "drdriving": "drdriving", "juegosarcade": "xogosarcade", "memorygames": "xogosdememoria", "vulkan": "vulkan", "actiongames": "xogosdeacción", "blowgames": "sopogames", "pinballmachines": "máquinasdepinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "sofácoop", "perguntados": "preguntados", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "xogosimessage", "idlegames": "xogosdeocio", "fillintheblank": "completaelespazo", "jeuxpc": "xogospc", "rétrogaming": "rétrogaming", "logicgames": "xogosdeloxica", "japangame": "xapongame", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "xogosdecelebridade", "exitgames": "xogosdeescape", "5vs5": "5vs5", "rolgame": "rolx<PERSON>", "dashiegames": "dashiegames", "gameandkill": "xogarandrematar", "traditionalgames": "xogostradicionais", "kniffel": "kniffel", "gamefps": "xogosfps", "textbasedgames": "xogosbaseadosentexto", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "xogodeladrón", "lawngames": "xogosdegrama", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "futbolin", "tischfußball": "fútboldemesa", "spieleabende": "noitesdeblox", "jeuxforum": "forodexogos", "casualgames": "xogoscasual", "fléchettes": "flech<PERSON>s", "escapegames": "escapegames", "thiefgameseries": "xogosdladroina", "cranegames": "xogosdegrúa", "játék": "xogo", "bordfodbold": "fodbord", "jogosorte": "xogosorte", "mage": "mago", "cargames": "cargames", "onlineplay": "xogoonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "noitesdegames", "pursebingos": "pursebingos", "randomizer": "aleatorizador", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "xogospc", "socialdeductiongames": "xogosdeduciónsocial", "dominos": "dominos", "domino": "domino", "isometricgames": "xogosisométricos", "goodoldgames": "buenosxogos", "truthanddare": "verda<PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "buscadadeobxectos", "jeuxvirtuel": "xogosvirtuais", "romhack": "romhack", "f2pgamer": "gamerf2p", "free2play": "grat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "xogofantástico", "gryonline": "gryonline", "driftgame": "xogodrift", "gamesotomes": "<PERSON>ogo<PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesyxogos", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "qualquercoaconmotor", "everywheregame": "xogodotodo", "swordandsorcery": "espadaeencantamento", "goodgamegiving": "boagameagoação", "jugamos": "xogamos", "lab8games": "lab8xogos", "labzerogames": "labzerox<PERSON><PERSON>", "grykomputerowe": "videoxogos", "virgogami": "virgogami", "gogame": "jogoparaíso", "jeuxderythmes": "xogosderitmos", "minaturegames": "xogosdeminatura", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "autocuidadoexogos", "gamemodding": "gameremodificación", "crimegames": "xogosdecrimen", "dobbelspellen": "doblespelen", "spelletjes": "xogar", "spacenerf": "spacenerf", "charades": "charadas", "singleplayer": "xogoindividual", "coopgame": "xogoencoop", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "xogo<PERSON><PERSON><PERSON>pal", "kingdiscord": "reid<PERSON><PERSON>", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "toka", "onitama": "onitama", "pandemiclegacy": "herdanzapandémica", "camelup": "dromedarioarriba", "monopolygame": "monopolygame", "brettspiele": "xogosdemesa", "bordspellen": "xogosdeandar", "boardgame": "xogodemesa", "sällskapspel": "xogosdecompagnía", "planszowe": "xogosdemesa", "risiko": "risiko", "permainanpapan": "xogopainel", "zombicide": "zombicide", "tabletop": "tabol<PERSON>", "baduk": "baduk", "bloodbowl": "bowladesangue", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "conecta4", "heroquest": "<PERSON><PERSON><PERSON>e", "giochidatavolo": "xogosdamesa", "farkle": "farkle", "carrom": "carrom", "tablegames": "xogosdetaberna", "dicegames": "x<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "xogo<PERSON>ab<PERSON><PERSON>", "jocuridesocietate": "xogarconosocial", "deskgames": "xogosdeescritorio", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "encontrocosmico", "creationludique": "creaciónludica", "tabletoproleplay": "rolen<PERSON><PERSON>", "cardboardgames": "xogosdecartón", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "xogosdeintercambio", "infinitythegame": "infinidadeoxogo", "kingdomdeath": "reinosdeath", "yahtzee": "<PERSON><PERSON><PERSON>", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "xogodemesa", "planszówki": "xogosdemesa", "rednecklife": "vidared<PERSON>", "boardom": "aburrimento", "applestoapples": "maceirasacoreas", "jeudesociété": "xogoensociedade", "gameboard": "tablerodexogo", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdesociétés": "xogosdemesa", "twilightimperium": "imperioefectosdebano", "horseopoly": "cabaloopolo", "deckbuilding": "constru<PERSON><PERSON>cks", "mansionsofmadness": "mansiónsdemadurez", "gomoku": "g<PERSON><PERSON><PERSON>", "giochidatavola": "xogosdemesa", "shadowsofbrimstone": "sombrasdebrimstone", "kingoftokyo": "reidetokio", "warcaby": "warcaby", "táblajátékok": "táblajuegos", "battleship": "bandeirante", "tickettoride": "billeteparacabalgar", "deskovehry": "escritoriosfesteiros", "catán": "catan", "subbuteo": "subbuteo", "jeuxdeplateau": "xogosdemesa", "stolníhry": "xogosdemesa", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "xogosdesociedade", "gesellschaftsspiele": "xogosdesociedade", "starwarslegion": "legionestrelas", "gochess": "go<PERSON>gar", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonadeguerra", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identidadev", "theisle": "aillada", "thelastofus": "<PERSON><PERSON><PERSON><PERSON>den<PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "perso<PERSON><PERSON><PERSON>", "callofcthulhu": "chama<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyyle<PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "entrenós", "eco": "eco", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafters", "daysgone": "díaspasados", "fobia": "fobia", "witchit": "maliaíxo", "pathologic": "patolóxico", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "elalongonoso", "ark": "arco", "grounded": "<PERSON><PERSON><PERSON>", "stateofdecay2": "estadoedecadencia2", "vrising": "vrising", "madfather": "paiengai", "dontstarve": "nonpasarfroito", "eternalreturn": "retornoeterno", "pathoftitans": "camiñodostitáns", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "oeviledenoomedo", "realrac": "realrac", "thebackrooms": "lasferramentas", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "bloquecontos", "thequarry": "aquarry", "tlou": "tlou", "dyinglight": "luzmor<PERSON>", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "somospoucosfelices", "riseofempires": "sen<PERSON><PERSON><PERSON><PERSON>pires", "stateofsurvivalgame": "estadodesobrevivir", "vintagestory": "historiavintage", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "respira", "alisa": "alisa", "westlendsurvival": "survivencialdooeste", "beastsofbermuda": "bestasdebermuda", "frostpunk": "frostpunk", "darkwood": "made<PERSON><PERSON><PERSON>", "survivalhorror": "sobrevivirhorror", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "trenvacío", "lifeaftergame": "vidasdespoisdojogo", "survivalgames": "xogosdesupervivencia", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "estaguerramía", "scpfoundation": "scpfundación", "greenproject": "proxectoverde", "kuon": "kuon", "cryoffear": "chorarporunexpected", "raft": "balsa", "rdo": "rdo", "greenhell": "infernogrande", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nai", "littlenightmares2": "pequenospesadelos2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "sonsdosbosques", "rustvideogame": "rust<PERSON><PERSON><PERSON>", "outlasttrials": "sobrevivirprobas", "alienisolation": "ilusiónalieníxena", "undawn": "nondía", "7day2die": "7días2morrer", "sunlesssea": "marsensol", "sopravvivenza": "sobrevivencia", "propnight": "propnight", "deadisland2": "illamorta2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampiro", "deathverse": "<PERSON><PERSON><PERSON>", "cataclysmdarkdays": "cataclismosdia<PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "medoefame", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "vidadespois", "ageofdarkness": "eradaescuridade", "clocktower3": "torredehoras3", "aloneinthedark": "sónoescuro", "medievaldynasty": "dinast<PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "proyectonimbusgame", "eternights": "eternoxes", "craftopia": "craftopia", "theoutlasttrials": "osultimosensaios", "bunker": "bunker", "worlddomination": "dominiomundial", "rocketleague": "ligadefoguetes", "tft": "tft", "officioassassinorum": "oficioassasinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "amorwarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warmaster40kmareescuro", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicar", "ilovesororitas": "queroasororitas", "ilovevindicare": "ilovevindicare", "iloveassasinorum": "adoroassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "g<PERSON><PERSON><PERSON><PERSON>", "ageofempires": "eraempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeridadeofsigmar", "civilizationv": "civilizaciónv", "ittakestwo": "falan<PERSON><PERSON><PERSON><PERSON>", "wingspan": "envergadura", "terraformingmars": "terraformand<PERSON><PERSON>", "heroesofmightandmagic": "heroesdebrawnaymaxia", "btd6": "btd6", "supremecommander": "comandantesupremo", "ageofmythology": "idadedamitoloxía", "args": "args", "rime": "rimar", "planetzoo": "<PERSON><PERSON>ó", "outpost2": "outpost2", "banished": "expulsado", "caesar3": "caesar3", "redalert": "alertaver<PERSON><PERSON>", "civilization6": "civilización6", "warcraft2": "warcraft2", "commandandconquer": "comandareconquistar", "warcraft3": "warcraft3", "eternalwar": "guerraeternal", "strategygames": "xogosdestratexias", "anno2070": "ano2070", "civilizationgame": "xogocivilización", "civilization4": "civilización4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "esporas", "totalwar": "totalguerra", "travian": "travian", "forts": "fortes", "goodcompany": "boacompanía", "civ": "civ", "homeworld": "mundos<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "máisrapidoquealuz", "forthekings": "polareis", "realtimestrategy": "estratexiarealtime", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "reinoduascoroas", "eu4": "eu4", "vainglory": "vanagloria", "ww40k": "ww40k", "godhood": "divindade", "anno": "anwow", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "clasedeálgebradavoestádeputa", "plagueinc": "plagueinc", "theorycraft": "teoríaartística", "mesbg": "mesbg", "civilization3": "civilización3", "4inarow": "4enrenglón", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "guerre<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "idadeempire2", "disciples2": "discípulos2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "xogosdestratexias", "stratejioyunları": "stratexiaxogo", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "idaded<PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosaurorey", "worldconquest": "conquistamundial", "heartsofiron4": "corazónsdeferro4", "companyofheroes": "companñadeheroes", "battleforwesnoth": "batallaporwesnoth", "aoe3": "aoe3", "forgeofempires": "forxaempire", "warhammerkillteam": "warmasterequipoasasinatos", "goosegooseduck": "g<PERSON><PERSON><PERSON><PERSON>", "phobies": "fobias", "phobiesgame": "phobiesgame", "gamingclashroyale": "clashroyalegamers", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "turnobase", "bomberman": "bomberman", "ageofempires4": "eraempresas4", "civilization5": "civilización5", "victoria2": "vitoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "artesaníamáxica", "starwarsempireatwar": "imperionasguerrasestar", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estratexia", "popfulmail": "popfulmail", "shiningforce": "forzabrillante", "masterduel": "dueloamestre", "dysonsphereprogram": "programadysonsfera", "transporttycoon": "transportetirano", "unrailed": "sentramizade", "magicarena": "arenamagica", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapatormenta", "uplandkingdoms": "reinosmontes", "galaxylife": "vidagalaxia", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "destrúeosspire", "battlecats": "gatosdebatalla", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "necesidadeporrapidez", "needforspeedcarbon": "necesitasespidocarbono", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "<PERSON><PERSON><PERSON>", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "sobreviver", "deadbydaylight": "mortospolasombra", "alicemadnessreturns": "voltaalicialocura", "darkhorseanthology": "antoloxíadearcabaloescuro", "phasmophobia": "fasmofobia", "fivenightsatfreddys": "cinconoitesenfreddy", "saiko": "saiko", "fatalframe": "fotofatal", "littlenightmares": "pe<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "mort<PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nacasa", "deadisland": "illamorta", "litlemissfortune": "pequenaecomalicia", "projectzero": "proyectocero", "horory": "horory", "jogosterror": "xogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "oláveciño2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "xogosdeterror", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartascontraahumanidade", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenomes", "dixit": "dixit", "bicyclecards": "bicicletacards", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "lendariodorunetera", "solitaire": "solitario", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uno": "unha", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "forxaclave", "cardtricks": "trucosdecartas", "playingcards": "cartasdejogo", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "correporinternet", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartasdecomercio", "pokemoncards": "cartaspo<PERSON><PERSON>", "fleshandbloodtcg": "carneiesanguetcg", "sportscards": "cartasdeporte", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "picas", "warcry": "gritodeguerra", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "reidoscorazós", "truco": "truco", "loteria": "lotaría", "hanafuda": "hana<PERSON>da", "theresistance": "teresistencia", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "cartasdeyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "duelos<PERSON><PERSON>gio<PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yug<PERSON>hxogo", "darkmagician": "<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "xogodecartas", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcomandante", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "xogosdecartas", "mtgjudge": "xuezdemtg", "juegosdecartas": "xogosdecartas", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "buscadeplanosmtg", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "xogodecartas", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespirits": "batallaspirits", "battlespiritssaga": "sagadaspiritosdebatalla", "jogodecartas": "xogodecartas", "žolíky": "xolíki", "facecard": "tarxetadevida", "cardfight": "luch<PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "constru<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "maxicartas", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "sombreadiverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicorniosinestables", "cyberse": "ciberse", "classicarcadegames": "xogosarcadeclásicos", "osu": "osú", "gitadora": "gitadora", "dancegames": "x<PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "venresnoitafunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proximirai", "projectdiva": "proyectodiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON>", "clonehero": "clon<PERSON><PERSON>", "justdance": "xustodanza", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockaosmortos", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "xogadorderitmo", "stepmania": "stepmania", "highscorerythmgames": "xogosderitmosaltoscoring", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "sonidovoltex", "rhythmheaven": "ceoritmo", "hypmic": "hypmic", "adanceoffireandice": "avanzoenfogoerio", "auditiononline": "audiciónonline", "itgmania": "itgmanía", "juegosderitmo": "xogosderitmo", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ritmodoctor", "cubing": "cubando", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "xogosdepuzles", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "puzzasdeloxica", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "enigmas", "rubikscube": "cubodosrubik", "crossword": "crucigrama", "motscroisés": "motscroisés", "krzyżówki": "crucigramas", "nonogram": "nonogram", "bookworm": "bichodel<PERSON><PERSON>", "jigsawpuzzles": "puzles<PERSON><PERSON><PERSON>es", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "enigma", "riddles": "enigma", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "dentro", "angrybirds": "páxarosenfurecidos", "escapesimulator": "escapexogo", "minesweeper": "desactivador", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "sudoku", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "puzzlesport", "escaperoomgames": "xogosdeescape", "escapegame": "escapegame", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgame", "wordsearch": "buscadepalabras", "enigmistica": "enigmística", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "contosdeenigmas", "fishdom": "fishdom", "theimpossiblequiz": "oquizimposible", "candycrush": "candycrush", "littlebigplanet": "pequenosgrandesplanetas", "match3puzzle": "puzzle3match", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "cubicorubik", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "oprincípiodastal<PERSON>", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "enigmaaí", "tycoongames": "xogosdeaxente", "cubosderubik": "cubosdebóo", "cruciverba": "cruciver<PERSON>", "ciphers": "cifras", "rätselwörter": "frasesenigmáticas", "buscaminas": "buscamiñas", "puzzlesolving": "resolverpuzzle", "turnipboy": "naboneno", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "ning<PERSON><PERSON>", "guessing": "suposición", "nonograms": "nonogramas", "kostkirubika": "kostkirubika", "crypticcrosswords": "crucigramascrípticos", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "búscaromisterios", "catcrime": "delitosgato", "quebracabeça": "quebracabezas", "hlavolamy": "r<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "oúltim<PERSON><PERSON>", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "canciónerrante", "carto": "<PERSON><PERSON>", "untitledgoosegame": "xogoindica<PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "trix<PERSON>", "tinykin": "tinykin", "rubikovakostka": "rub<PERSON><PERSON><PERSON>", "speedcube": "speedcube", "pieces": "peci<PERSON>s", "portalgame": "portalxogo", "bilmece": "bilmeces", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "cubor<PERSON>k", "indovinelli": "indi<PERSON><PERSON>s", "cubomagico": "cubomágico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopolio", "futurefight": "lutaaliña<PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "lobosolitario", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "estrelasensemble", "asphalt9": "asfalto9", "mlb": "mlb", "cookierunkingdom": "reinadocookierun", "alchemystars": "alquimistastelas", "stateofsurvival": "estadodesobrevivencia", "mycity": "minacidade", "arknights": "arknights", "colorfulstage": "escenacolorida", "bloonstowerdefense": "defensadastorresbloon", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "destinoordendegrande", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "corrermaxestático", "fireemblemheroes": "heroesde<PERSON>gos", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "xogosdemóbil", "kingschoice": "escolladekings", "guardiantales": "guardiantales", "petrolhead": "aficionadoaoasociación", "tacticool": "tacticool", "cookierun": "cocinarun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "fóradotema", "craftsman": "artesán", "supersus": "supersus", "slowdrive": "caminolento", "headsup": "aviso", "wordfeud": "wordfeud", "bedwars": "guerradascamas", "freefire": "firelibre", "mobilegaming": "xogosdámbulo", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "estratexiasdepelexa", "clashofclans": "conflitodeclans", "pjsekai": "pjsekai", "mysticmessenger": "misticomensaxeiro", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonáutica", "maplestory": "maplestory", "albion": "albión", "hayday": "teudía", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON>and<PERSON>dget", "ml": "ml", "bangdream": "bangdream", "clashofclan": "conflitodenosclans", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "princesadentempo", "beatstar": "beatstar", "dragonmanialegend": "dragónmaniacolección", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "amorinocan<PERSON>", "androidgames": "xogosandroid", "criminalcase": "casocriminal", "summonerswar": "guerreirosdosummoners", "cookingmadness": "locuracociñando", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaenxeos", "lordsmobile": "lordsmobile", "tinybirdgarden": "xardínpequeno", "gachalife": "gachalife", "neuralcloud": "nuvemneural", "mysingingmonsters": "minhossaltingmonstros", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "arquivazul", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "espexioverso", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "palpalo", "mpl": "mpl", "coinmaster": "monedamestre", "punishinggrayraven": "puns<PERSON><PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "cidadedecorrendo", "juegodemovil": "xogodemoible", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimética", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "montañarusaboo", "grandchase": "grandepersecución", "bombmebrasil": "bombemebrasil", "ldoe": "ldoe", "legendonline": "legend<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindustría", "callofdragons": "chamadadosdrag<PERSON>s", "shiningnikki": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "camiñononporonde", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "derbidedemolición3", "wordswithfriends2": "palabrasconamigos2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "contopurrfecto", "showbyrock": "showbyrock", "ladypopular": "muller<PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "recollidacidade", "perfectworldmobile": "mundoidealmobil", "empiresandpuzzles": "imperiosyxogos", "empirespuzzles": "empirespuzzles", "dragoncity": "ciudaddedragón", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilegl", "fanny": "fanny", "littlenightmare": "peque<PERSON><PERSON>o", "aethergazer": "aethergazer", "mudrunner": "mudarunner", "tearsofthemis": "lágrimasdosmitos", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmóbil", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "eve<PERSON>oitas", "jogocelular": "xogomóbil", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmóbil", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "o<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "chicasfronteira", "jurassicworldalive": "jurassicworldvivo", "soulseeker": "buscadoresoul", "gettingoverit": "superandoisto", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "xogosmobiles", "legendofneverland": "lendariodaterraeibertade", "pubglite": "pubglite", "gamemobilelegends": "xogomobilelegends", "timeraiders": "timit<PERSON>s", "gamingmobile": "xogomóbil<PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "búsqueda", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "mundoobscuro", "travellerttrpg": "viaxeirotrpg", "2300ad": "2300dc", "larp": "larp", "romanceclub": "clubderomance", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemongalegodelmisterio", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonconfrontación", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entaí", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON>leta", "pokemonpurpura": "pokemonpúrpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "equipoasleyroket", "furret": "furrito", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monstrosdebolsillo", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonpel<PERSON>s", "teamystic": "equipomístico", "pokeball": "pokébola", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "p<PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "mansdeferro", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "mest<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "ni<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "cazadorbrillante", "ajedrez": "<PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "escacs", "schaken": "desactivar", "skak": "skak", "ajedres": "xaq<PERSON><PERSON>", "chessgirls": "rapazasdoaqueche", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "mund<PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "x<PERSON><PERSON>", "japanesechess": "xaponés", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "axedecanada", "fide": "b<PERSON>o", "xadrezverbal": "xadrezverbal", "openings": "aperturas", "rook": "peón", "chesscom": "ajedrezcom", "calabozosydragones": "calabozosydragóns", "dungeonsanddragon": "mazmorrasydragóns", "dungeonmaster": "me<PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsesdrag<PERSON><PERSON>", "oxventure": "oxventuras", "darksun": "sorresubido", "thelegendofvoxmachina": "lalegendadevoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "darkmoor", "minecraftchampionship": "campionatodeminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "sueñosmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "chama<PERSON><PERSON>a", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodificado", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "entreterras", "minecraftdungeons": "minecraftdungóns", "minecraftcity": "cidademinecraft", "pcgamer": "pcgamer", "jeuxvideo": "xogosdevideo", "gambit": "gambito", "gamers": "gamers", "levelup": "subeunnivel", "gamermobile": "gamermóbil", "gameover": "xogado", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "j<PERSON><PERSON>", "pcgames": "xogosdepc", "casualgaming": "xogocasual", "gamingsetup": "configuracióndegaming", "pcmasterrace": "pcmasterrace", "pcgame": "xogosdepc", "gamerboy": "rapazgamer", "vrgaming": "vrxogos", "drdisrespect": "drdisrespect", "4kgaming": "4kgamers", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "jugadordeconsola", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "xogosonline", "semigamer": "semigamer", "gamergirls": "gamergirls", "gamermoms": "mamásgamer", "gamerguy": "gamerchico", "gamewatcher": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gamador", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerchicas", "otoge": "otoge", "dedsafio": "desafío", "teamtryhard": "equipoesforzado", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "buscas", "alax": "alax", "avgn": "avgn", "oldgamer": "gamervello", "cozygaming": "xogoacougar", "gamelpay": "gamelpay", "juegosdepc": "xogosdepc", "dsswitch": "dsswitch", "competitivegaming": "xogocompetitivo", "minecraftnewjersey": "minecraftnuevajersei", "faker": "falsificado", "pc4gamers": "pc4gamers", "gamingff": "<PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "xogo<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "xogopcp", "girlsgamer": "rapazasgamer", "fnfmods": "fnfmods", "dailyquest": "buscadadiaria", "gamegirl": "xogadora", "chicasgamer": "chicasgamer", "gamesetup": "configuradogame", "overpowered": "superpoderoso", "socialgamer": "xogadorsocial", "gamejam": "xogofragas", "proplayer": "proplayer", "roleplayer": "roleplayer", "myteam": "<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "repúblicadegamers", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "amigosgamer", "butuhcewekgamers": "necesitamoschicasgamers", "christiangamer": "gamercristián", "gamernerd": "gamerfriki", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamerdescontraído", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "oyunver", "gamertag": "nick<PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "videoxogador", "wspólnegranie": "xogarxuntos", "mortdog": "mortdog", "playstationgamer": "gamerdeplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamerasaludable", "gtracing": "gtracing", "notebookgamer": "cadernogamer", "protogen": "prot<PERSON><PERSON><PERSON>", "womangamer": "mullerxogador<PERSON>", "obviouslyimagamer": "obviamenteunicgamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forax<PERSON>", "humanfallflat": "humáncaenplano", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zerofuga", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomúsica", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonico", "fallguys": "caerchicos", "switch": "cambia", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "lendadezelda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "anacoalegría", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "máscarasmaioras", "mariokartmaster": "mestremariokar<PERSON>", "wii": "wii", "aceattorney": "fiscalenxeite", "ssbm": "ssbm", "skychildrenofthelight": "nenosdelaluznoceo", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simuladoresdeandar", "nintendogames": "xogosnintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragónbusqueda", "harvestmoon": "lúaenxeira", "mariobros": "mario<PERSON>s", "runefactory": "factoríaderuna", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "alentoaoértico", "myfriendpedro": "meupedro", "legendsofzelda": "lend<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51x<PERSON>s", "earthbound": "<PERSON><PERSON><PERSON>", "tales": "contos", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animal<PERSON><PERSON><PERSON><PERSON>", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendogalicia", "tloz": "tloz", "trianglestrategy": "estratexiadet<PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "castaña<PERSON>unmaldiacon<PERSON>e", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerrerosdehy<PERSON>le", "mariopartysuperstars": "mariopartysuperstar", "marioandsonic": "marioysonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "redcanidos", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "t<PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "ligadelendaseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "rind<PERSON><PERSON><PERSON>", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadasaslendarias", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "jogodefortnite", "gamingfortnite": "xuegosfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideoxogos", "scaryvideogames": "videoxogosdeasusto", "videogamemaker": "creadoravideoxogos", "megamanzero": "megamanzero", "videogame": "videoxogo", "videosgame": "videoxogos", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "vixilancia", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "mago101", "battleblocktheater": "battleblockteatro", "arcades": "arcadas", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simuladordefarming", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "xogosdesandbox", "videogamelore": "videoxogosmoito", "rollerdrome": "<PERSON><PERSON><PERSON>", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "pais<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandeasaltomobilístico", "deadspace": "espazomorto", "amordoce": "amordoce", "videogiochi": "videoxogos", "theoldrepublic": "aantigarepública", "videospiele": "videoxogos", "touhouproject": "pro<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamcast": "soñogalego", "adventuregames": "xogosdeaventura", "wolfenstein": "wolfenstein", "actionadventure": "aventuraacción", "storyofseasons": "historiaestacións", "retrogames": "retrogames", "retroarcade": "retroarcade", "vintagecomputing": "computaciónvintage", "retrogaming": "retrogaming", "vintagegaming": "videoxogosretro", "playdate": "datadexogo", "commanderkeen": "comandanteafín", "bugsnax": "buzsnax", "injustice2": "inxustiza2", "shadowthehedgehog": "sombraohedgehog", "rayman": "rayman", "skygame": "xogosdoceu", "zenlife": "vidazenspíritu", "beatmaniaiidx": "beatmaniaiidx", "steep": "empinado", "mystgames": "myst<PERSON><PERSON><PERSON>", "blockchaingaming": "xogoenblockchain", "medievil": "medievil", "consolegaming": "consolegaming", "konsolen": "konsolen", "outrun": "outrun", "bloomingpanic": "florecendoapánico", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "xogodeterror", "monstergirlquest": "monstergirlquest", "supergiant": "supergigante", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "simuladoresdeagricultura", "juegosviejos": "<PERSON>ogosvie<PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ficcióninteractiva", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "novelavisual", "visualnovels": "novelasvisuais", "rgg": "rgg", "shadowolf": "sombras<PERSON><PERSON>", "tcrghost": "tcrfantasma", "payday": "díadepago", "chatherine": "catherine", "twilightprincess": "princesadoanochecer", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "bandeirón", "aestheticgames": "xogosestéticos", "novelavisual": "novelavisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolucióndossopladoresdefolas", "wiiu": "wiiu", "leveldesign": "deseñodenivel", "starrail": "starrail", "keyblade": "espadachave", "aplaguetale": "apl<PERSON>gue<PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "novelasvisuais", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videoxogos", "videogamedates": "citasvideoxogo", "mycandylove": "<PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "simplementeporque3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "retornoadixestión", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "mansiónmaníaca", "crashracing": "carrerasdestope", "3dplatformers": "plataformas3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "gamingvintage", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "xogosdehistoria", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "sondodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "<PERSON><PERSON><PERSON>", "offmortisghost": "fóraloboo", "tinybunny": "coelquiñopequeno", "retroarch": "retroarch", "powerup": "impúlsate", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgráficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "deserto", "powerwashsim": "simuladorpoderlimpeza", "coralisland": "illacoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "outromundo", "metaquest": "metaquest", "animewarrios2": "animeguerrreiros2", "footballfusion": "fusiónfútbol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalretorcido", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "montañasdeserio", "simulator": "simulador", "symulatory": "simuladores", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gryvideogrls", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "marabillosoenli<PERSON>", "skylander": "skylander", "boyfrienddungeon": "mozoencalcetíns", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "caosurbano", "heavenlybodies": "corposcelest<PERSON><PERSON>", "seum": "súa", "partyvideogames": "videoxogosdefiesta", "graveyardkeeper": "coidadaremosantos", "spaceflightsimulator": "simuladorvoadorespacial", "legacyofkain": "herdodekain", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "comidaevideojogos", "oyunvideoları": "videoxogos", "thewolfamongus": "elpoñerentreosotros", "truckingsimulator": "simuladordecamións", "horizonworlds": "horizonmundos", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "lendasyvideoxogos", "oldschoolvideogames": "videoxogosoldschool", "racingsimulator": "simuladordecarreras", "beemov": "bee<PERSON>v", "agentsofmayhem": "axentesdemayhem", "songpop": "canciónpop", "famitsu": "famitsu", "gatesofolympus": "portasdoolimp", "monsterhunternow": "monstrocaçadoragora", "rebelstar": "estrelarebelde", "indievideogaming": "indievideoxogos", "indiegaming": "indiegaming", "indievideogames": "indievideoxogos", "indievideogame": "videoxogoindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "fortale<PERSON>boo", "unbeatable": "imbatible", "projectl": "proxectol", "futureclubgames": "futuroclubxogos", "mugman": "<PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "cienciadapertura", "backlog": "atraso", "gamebacklog": "retrouso", "gamingbacklog": "pendentesdevideoxogos", "personnagejeuxvidéos": "personaxexogosvideoxogos", "achievementhunter": "buscandoconquistas", "cityskylines": "paisaxesdecidade", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "perrotravieso", "beastlord": "bestial<PERSON>", "juegosretro": "xogosretro", "kentuckyroutezero": "routakentuckyzero", "oriandtheblindforest": "oriayolabozos<PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "videoxogosost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "querokofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berseker", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON>", "darkerthanblack": "máisescuroqueoimbra", "animescaling": "animescaling", "animewithplot": "animecontramaña", "pesci": "peixe", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "anime90s", "darklord": "lordedaso<PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "tempaddrstone1", "rapanime": "rapanime", "chargemanken": "cargamanceiros", "animecover": "cubertaanime", "thevisionofescaflowne": "lavisióndeescaflowne", "slayers": "destruidores", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "anima<PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "p<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "fuerzafogo", "moriartythepatriot": "moriartyelpatri<PERSON>", "futurediary": "diariofuturo", "fairytail": "contosdefadas", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "feitoenabismo", "parasyte": "parásito", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodíadasereas", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangadehorror", "romancemangas": "romanceman<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragónserventa", "blacklagoon": "lagoonnegra", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "xeniostribe", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "zugo", "bungostraydogs": "perrosenbanco", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "unhaíndicemágico", "sao": "sao", "blackclover": "trébolnegro", "tokyoghoul": "tokyoghoul", "onepunchman": "unpunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8ainfinito", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "espíasfamilia", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioridade", "angelsofdeath": "anxosamorte", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosímico", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesdeportes", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagadotanyaaicamalvada", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "boni<PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "oboiyeobestia", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "butlernegro", "towerofgod": "to<PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "comocuidarunhamá", "fullmoonwosagashite": "llunacheawosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "monoéterrorífico", "martialpeak": "cumedemartial", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "chicadealtapuntuación", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstre<PERSON>", "kanae": "kanae", "yone": "b<PERSON><PERSON><PERSON>", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amitis", "sailorsaturn": "ma<PERSON>heirosat<PERSON><PERSON><PERSON>", "dio": "dío", "sailorpluto": "marineroplutón", "aloy": "aloy", "runa": "runa", "oldanime": "animevello", "chainsawman": "homedepoda", "bungoustraydogs": "bungoustraydogs", "jogo": "xogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "butlernegro", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "cestaf<PERSON>ta", "devilmancrybaby": "diablomanchora", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "tanxalaesperanza", "monstermanga": "monstermanga", "yourlieinapril": "tuli<PERSON><PERSON><PERSON>", "buggytheclown": "buggyoelpayaso", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "pris<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "cemiterywonderland", "bannafish": "<PERSON><PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerracomida", "cardcaptorsakura": "capturador<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "lin<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "míteoeternidade", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "periodoaazul", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "borrado", "bluelock": "bluelock", "goblinslayer": "matago<PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "cavaleirovampiro", "mugi": "mugi", "blueexorcist": "exor<PERSON><PERSON><PERSON><PERSON>", "slamdunk": "sestalón", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "escribindo", "spyfamily": "familiaespía", "airgear": "airegear", "magicalgirl": "rapazamágica", "thesevendeadlysins": "estesetepecadosmortais", "prisonschool": "escoladeprisón", "thegodofhighschool": "odeusdoinstitutoalto", "kissxsis": "bi<PERSON><PERSON><PERSON>", "grandblue": "granazul", "mydressupdarling": "minhaacompanhante", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universoanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "romanceencomic", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demoncazadorataespada", "bloodlad": "<PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "punchdefogo", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "magiacereza", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "institutodasmortas", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "pr<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "au<PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "desfilemortal", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animexaponés", "animespace": "espazodeanime", "girlsundpanzer": "chicasundpanzer", "akb0048": "akb0048", "hopeanuoli": "esperoquevivas", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "rat<PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "rapazadepexegos", "cavalieridellozodiaco": "cabaleirosdelozodíaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "corazónmanga", "deliciousindungeon": "deliciosoenlademnación", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "divertimentoboo", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "saltaalaoficina", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialist<PERSON><PERSON>", "overgeared": "sobraeng<PERSON>ado", "toriko": "<PERSON><PERSON>o", "ravemaster": "m<PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atellerdocapuchón", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolión", "kamen": "kamen", "mangaislife": "mangaisvida", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "oscabaleirosdozodíaco", "animeshojo": "animeshojo", "reverseharem": "reversah<PERSON>m", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "granprofesoronizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldado", "mybossdaddy": "meubossapapá", "gear5": "gear5", "grandbluedreaming": "soñandocongrandblue", "bloodplus": "sangreplus", "bloodplusanime": "sang<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "sangrecanime", "bloodc": "<PERSON>ue<PERSON>", "talesofdemonsandgods": "contosdedemoniosedeuses", "goreanime": "goreanime", "animegirls": "animechicas", "sharingan": "compartirolook", "crowsxworst": "corvosxpeor", "splatteranime": "animeestoupido", "splatter": "sal<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "ascensodohéroedocombate", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animegalicia", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netxuñosusume", "childrenofthewhales": "nenosdasbal<PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampións", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiconsmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "nenasmáxicas", "callofthenight": "chama<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "brawlerdebakugan", "bakuganbrawlers": "brawlersdabakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "xardínsombra", "tsubasachronicle": "tsubasachronicle", "findermanga": "atopermanga", "princessjellyfish": "princesamedusa", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "bicoenparaiso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revistaraugardente", "animeverse": "animeuniverso", "persocoms": "persocoms", "omniscientreadersview": "vistaomniscientedelectores", "animecat": "animegato", "animerecommendations": "recomendaciónsanime", "openinganime": "aperturadeanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "micateenromántica", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotsgigantes", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "móbilfighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "blanco", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventurashorriblesdejojo", "fullmetalalchemist": "alquimistademetalcompleto", "ghiaccio": "xeo", "jojobizarreadventures": "aventurasdejojobizarre", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitar", "greenranger": "rangerverde", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "raposo", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animecidade", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonaventura", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "destrutoradedemonios", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ataqueosxigantes", "erenyeager": "erenyeager", "myheroacademia": "meuhéroeacadémico", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "amigosxogo", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corpodeen<PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "oonepiecereal", "revengers": "revengadores", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcista", "joyboyeffect": "efectoboyalegría", "digimonstory": "historiadodigimon", "digimontamers": "diximontamers", "superjail": "superxail", "metalocalypse": "metalocalipse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "nosoanhostclub", "flawlesswebtoon": "webtoonsenfeito", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bababababa", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "meigaenvoo", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "sóporque", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "todiñosantosstreet", "recuentosdelavida": "recontosdavida"}