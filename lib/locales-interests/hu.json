{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "asztrológia", "cognitivefunctions": "kognitívfunkciók", "psychology": "pszichológia", "philosophy": "filozófia", "history": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "physics": "fizika", "science": "tudomány", "culture": "kult<PERSON>ra", "languages": "nyelvek", "technology": "technológia", "memes": "m<PERSON>me<PERSON>", "mbtimemes": "mbtimémek", "astrologymemes": "asztrológiamémek", "enneagrammemes": "enneagrammémek", "showerthoughts": "váratlangondolatok", "funny": "vicces", "videos": "videók", "gadgets": "kütyük", "politics": "politika", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "életvitelitanácsok", "crypto": "kripto", "news": "<PERSON><PERSON><PERSON>", "worldnews": "vil<PERSON>ghírek", "archaeology": "ré<PERSON><PERSON><PERSON>", "learning": "<PERSON><PERSON><PERSON>", "debates": "viták", "conspiracytheories": "összeesküvéselméletek", "universe": "univerzum", "meditation": "meditáció", "mythology": "mitológia", "art": "művészet", "crafts": "barkácsolás", "dance": "t<PERSON>c", "design": "design", "makeup": "sminkelés", "beauty": "szépségápolás", "fashion": "divat", "singing": "éneklés", "writing": "írás", "photography": "fényképezés", "cosplay": "cosplay", "painting": "festészet", "drawing": "raj<PERSON><PERSON><PERSON>", "books": "könyvek", "movies": "filmek", "poetry": "költészet", "television": "televízió", "filmmaking": "filmkészítés", "animation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumentumfilmek", "mystery": "misztika", "comedy": "vígjáték", "crime": "krimi", "drama": "d<PERSON><PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romanti<PERSON>", "realitytv": "realitytv", "action": "<PERSON><PERSON><PERSON><PERSON>", "music": "zene", "blues": "blues", "classical": "klasszikus", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronikus", "folk": "népzene", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "<PERSON><PERSON><PERSON>", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "utazás", "concerts": "koncertek", "festivals": "fesztiválok", "museums": "múzeumok", "standup": "standup", "theater": "színház", "outdoors": "<PERSON><PERSON><PERSON><PERSON>", "gardening": "kertészkedés", "partying": "partizás", "gaming": "gaming", "boardgames": "társasjátékok", "dungeonsanddragons": "dungeonsanddragons", "chess": "sakk", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pok<PERSON><PERSON>", "food": "étkezés", "baking": "<PERSON><PERSON><PERSON><PERSON>", "cooking": "főzés", "vegetarian": "ve<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "vegán", "birds": "<PERSON>ara<PERSON>", "cats": "<PERSON><PERSON><PERSON>", "dogs": "kut<PERSON>ák", "fish": "halak", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "blacklivesmatter", "environmentalism": "környezetvédelem", "feminism": "feminizmus", "humanrights": "emberijogok", "lgbtqally": "lgbtqszövetséges", "stopasianhate": "tégyazázsiaigyűlöletellen", "transally": "transzszövetséges", "volunteering": "önkéntesség", "sports": "sportok", "badminton": "tollaslabda", "baseball": "baseball", "basketball": "kosárlabda", "boxing": "boksz", "cricket": "krikett", "cycling": "kerékpározás", "fitness": "<PERSON><PERSON><PERSON>", "football": "labdarúgás", "golf": "golf", "gym": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gymnastics": "torna", "hockey": "j<PERSON>g<PERSON><PERSON>", "martialarts": "harcművészetek", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "futás", "skateboarding": "gördeszkázás", "skiing": "<PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snowboardozás", "surfing": "szörfözés", "swimming": "úszás", "tennis": "tenisz", "volleyball": "rö<PERSON><PERSON><PERSON><PERSON>", "weightlifting": "s<PERSON><PERSON><PERSON><PERSON>", "yoga": "jóga", "scubadiving": "búv<PERSON><PERSON><PERSON><PERSON>", "hiking": "túrázás", "capricorn": "bak", "aquarius": "vízöntő", "pisces": "halak", "aries": "kos", "taurus": "bika", "gemini": "ikrek", "cancer": "<PERSON><PERSON>", "leo": "oroszlán", "virgo": "szűz", "libra": "<PERSON><PERSON><PERSON><PERSON>", "scorpio": "<PERSON><PERSON><PERSON><PERSON>", "sagittarius": "n<PERSON><PERSON>", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "laza", "longtermrelationship": "hosszútávúkapcsolat", "single": "s<PERSON><PERSON>", "polyamory": "többszerelem", "enm": "nyitottkapcsolat", "lgbt": "lmbt", "lgbtq": "lgbtq", "gay": "meleg", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "vöröshalálmegváltás2", "dragonage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsrow": "szentváros", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "őrkutya", "dislyte": "dislyte", "rougelikes": "roguelikeok", "kingsquest": "királyikaland", "soulreaver": "lélekfosztogató", "suikoden": "su<PERSON><PERSON>", "subverse": "subversz", "legendofspyro": "spyrolegenda", "rouguelikes": "roguelikeok", "syberia": "szibéria", "rdr2": "rdr2", "spyrothedragon": "spyrodragonszál", "dragonsdogma": "sárkányoktanítása", "sunsetoverdrive": "naplementefelhozatal", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "tűzjelemajni", "yokaiwatch": "yokaitésztájl", "rocksteady": "stabilzene", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildháborúk", "openworld": "nyitottvilág", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "lélekutánzó", "dungeoncrawling": "<PERSON><PERSON>j<PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "midgardtribok", "planescape": "tervpálya", "lordsoftherealm2": "realmuraurak2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "színvilág", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersívszimulációk", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "megcsonkítva", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "kipurcanás", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "idősebbtekercsek", "modding": "módosítás", "charactercreation": "karakterkészítés", "immersive": "magávalragadó", "falloutnewvegas": "főhősújváros", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyöregiskola", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotiváció", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "szerelmesbólogató", "otomegames": "o<PERSON><PERSON>á<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaidő", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampírokmaszkabálja", "dimension20": "dimenzió20", "gaslands": "gázföldek", "pathfinder": "utatkereső", "pathfinder2ndedition": "küldetéskereső2kiadás", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "vérazórán", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "szeretleknikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgkészítő", "osrs": "osrs", "overlord": "főnök", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgijesztő", "elderscrollsonline": "idősebbtekercsekonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgszoveg", "genshin": "genshin", "eso": "kaja", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "<PERSON>ed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "legsötétebbdungeon", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "külsővilágok", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinasztiaháborúk", "skullgirls": "koponyalányok", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogsmeadeörökség", "madnesscombat": "őrületcsata", "jaggedalliance2": "recésszövetség2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "út96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikeok", "gothamknights": "gothamlovagok", "forgottenrealms": "elfeledettvilágok", "dragonlance": "sárkányosbajnok", "arenaofvalor": "valorarénája", "ffxv": "ffxv", "ornarpg": "onarpg", "toontown": "toontown", "childoflight": "fénygyerek", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonvilág", "monsterrancher": "monstertanyás", "ecopunk": "ökolázadó", "vermintide2": "verminhibernálás2", "xeno": "xeno", "vulcanverse": "vulkánverzum", "fracturedthrones": "törötttrónok", "horizonforbiddenwest": "horizonttiltottnyugat", "twewy": "twewy", "shadowpunk": "árnypunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hoggartmystery", "deltagreen": "zöldrészvétel", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "csapás", "lastepoch": "utolsóepok", "starfinder": "csillagkeres<PERSON>", "goldensun": "aranynap", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "pengéköttöttesötétségben", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "kyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkvörös", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "elesettrend", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "gonoszvilágok", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "ördögi_túlélő", "oldschoolrunescape": "öregiskolásrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "istenség", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "régivilágbúbánat", "adventurequest": "kalandkereső", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "szerepjátékok", "roleplayinggames": "szerepjátékok", "finalfantasy9": "finalfantasy9", "sunhaven": "napfényváros", "talesofsymphonia": "symphoniahistóriák", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON>", "myfarog": "azénfarogom", "sacredunderworld": "sacrálthálózat", "chainedechoes": "láncahelyek", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "lélekhasonlók", "othercide": "m<PERSON><PERSON>e", "mountandblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "időutazó", "pillarsofeternity": "örökkévalóságpillérei", "palladiumrpg": "palladiumrpg", "rifts": "hasadékok", "tibia": "tibia", "thedivision": "amegosztás", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "sárkánylegendája", "xenobladechronicles2": "xenobladekrónikák2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "farkasemberekapo<PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "kiserdő", "childrenofmorta": "mortalgyerekek", "engineheart": "motorszív", "fable3": "fable3", "fablethelostchapter": "mesealostfejezet", "hiveswap": "hiveswap", "rollenspiel": "s<PERSON>ep<PERSON><PERSON><PERSON>k", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON>", "ff15": "ff15", "starfield": "csillagmező", "oldschoolrevival": "régiiskolaújraalapítása", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savagevilágok", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "sötétségbörtön", "juegosrpg": "rpgjátékok", "kingdomhearts": "királyságszívek", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klánmalkávian", "harvestella": "betakarítás", "gloomhaven": "gloomhaven", "wildhearts": "vadszívek", "bastion": "bastion", "drakarochdemoner": "drakarochdémonok", "skiesofarcadia": "arcadiaegei", "shadowhearts": "árn<PERSON>ékosszívek", "nierreplicant": "nierreplika", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "tűzlégzés4", "mother3": "anya3", "cyberpunk2020": "kyberpunk2020", "falloutbos": "falloutbos", "anothereden": "mégegyeden", "roleplaygames": "játékoszközösödés", "roleplaygame": "s<PERSON>ep<PERSON><PERSON><PERSON>k", "fabulaultima": "fabulault<PERSON>", "witchsheart": "banyaosziv", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "útkeresőrpg", "pathfinder2e": "ösvénykereső2e", "vampirilamasquerade": "vampírláma<PERSON>l", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "sárkánykoreredet", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "vadászroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "szörnyvadászbirodalom", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgfórum", "shadowheartscovenant": "shadowheartskövetkeztetése", "bladesoul": "lélekpenge", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "avilágveledvégződik", "dragalialost": "dragalialost", "elderscroll": "idősebbtekercs", "dyinglight2": "halófény2", "finalfantasytactics": "végsőfantáziataktikák", "grandia": "grandia", "darkheresy": "sötéterej<PERSON>ség", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "földimágia", "blackbook": "feketekönyv", "skychildrenoflight": "égigyerekekvilága", "gryrpg": "gryrpg", "sacredgoldedition": "szentaranykiadás", "castlecrashers": "várrobbantók", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "szellemhálótokió", "fallout2d20": "fallout2d20", "gamingrpg": "játékosrpg", "prophunt": "prophunt", "starrails": "csillag<PERSON>ala<PERSON>", "cityofmist": "városköd", "indierpg": "indierpg", "pointandclick": "pontéskattints", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "oszthatatlan", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "posztkiberpunk", "deathroadtocanada": "halálutakannániához", "palladium": "palládium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "szörnyvadász", "fireemblem": "tűzjelek", "genshinimpact": "gensihtáska", "geosupremancy": "geoszuprémácia", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "szörnyvadászmászás", "nier": "nier", "dothack": "dothack", "ys": "s<PERSON>bi", "souleater": "lélekfaló", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonaryjátékok", "tacticalrpg": "taktikairpg", "mahoyo": "mahoyo", "animegames": "animejátékok", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "örökson<PERSON><PERSON>", "princessconnect": "hercegnőkapcsolat", "hexenzirkel": "hexenzirkel", "cristales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vcs": "vcs", "pes": "pesz", "pocketsage": "zsebguru", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindiai", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efooball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "álomliga", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "álomhack", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "kibersport", "crazyraccoon": "őrültmosómedve", "test1test": "teszt1teszt", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantverseny", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portál2", "halflife": "f<PERSON>lidő", "left4dead": "b<PERSON><PERSON><PERSON><PERSON>", "left4dead2": "halottvagyok2", "valve": "szeleptest", "portal": "portál", "teamfortress2": "csapatvár2", "everlastingsummer": "örökkényár", "goatsimulator": "goatsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "szabadságbolygó", "transformice": "transformice", "justshapesandbeats": "csakformákésütemek", "battlefield4": "harctér4", "nightinthewoods": "éjszakaazerdőben", "halflife2": "félidő2", "hacknslash": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riskofrain2": "esőzkockázat2", "metroidvanias": "metroidvaniák", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "pokolivivő", "inscryption": "inscryption", "7d2d": "7n2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "törpvárral", "foxhole": "rókalyuk", "stray": "<PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "csatavonal1", "swtor": "swtor", "fallout2": "bukott2", "uboat": "ubot", "eyeb": "szemedbe", "blackdesert": "feketeerdő", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "partyzsák", "hardspaceshipbreaker": "keményűrhajóvágó", "hades": "<PERSON><PERSON><PERSON>", "gunsmith": "fegyvermester", "okami": "<PERSON>ami", "trappedwithjester": "csapdábanajászrajongóval", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON>", "rainworld": "esővilág", "cavesofqud": "qudcsepp<PERSON><PERSON>", "colonysim": "kolóniaszim", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minion<PERSON><PERSON><PERSON>", "grimdawn": "sötétszilánk", "darkanddarker": "sötétésmélyebben", "motox": "motox", "blackmesa": "feketeszint", "soulworker": "lélekmunkás", "datingsims": "randisimek", "yaga": "yaga", "cubeescape": "kockamenekülés", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "városihorizontok", "defconheavy": "defconheavy", "kenopsia": "kenopszia", "virtualkenopsia": "virtuáliskenopszia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "könyvtárosruina", "l4d2": "l4d2", "thenonarygames": "nonaryjátékok", "omegastrikers": "omegastrájkolók", "wayfinder": "<PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "nyugisműanyagkacsa", "battlebit": "<PERSON><PERSON><PERSON><PERSON>", "ultimatechickenhorse": "ultimátumcsirkelóhere", "dialtown": "hívóváros", "smileforme": "mosolygjálértem", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "s<PERSON><PERSON>húsifjú", "tinnybunny": "tinnybunny", "cozygrove": "cozygrove", "doom": "doom", "callofduty": "hadihívás", "callofdutyww2": "callofdutyww2", "rainbow6": "szivárvány6", "apexlegends": "apexlegends", "cod": "kód", "borderlands": "<PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutizombik", "apex": "csúcs", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "messzeordog", "farcrygames": "farcryjátékok", "paladins": "paladins", "earthdefenseforce": "földvédelmiérősség", "huntshowdown": "vadászatbumm", "ghostrecon": "fantomszerződés", "grandtheftauto5": "gtav", "warz": "warsz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "csatlakozzakárhoz", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "lázadáshomokvihar", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "fölszámoló3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON>lszakad<PERSON>", "b4b": "b4b", "codwarzone": "codhábor<PERSON><PERSON><PERSON><PERSON>", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombik", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "osztályok2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>ón<PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "coldwarzombik", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "aszfaltbajnok", "crosscode": "keresztdomain", "goldeneye007": "goldeneye007", "blackops2": "feketeoperációk2", "sniperelite": "mesterlövészelite", "modernwarfare": "modernháború", "neonabyss": "neonmélység", "planetside2": "planetside2", "mechwarrior": "mechharcos", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "meneküléstarkovból", "metalslug": "fémcsúszka", "primalcarnage": "primalcarnage", "worldofwarships": "hajóscsatákvilága", "back4blood": "vissza4vérért", "warframe": "háborúkeret", "rainbow6siege": "szivárvány6ostrom", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "tömeghatás", "systemshock": "rendszersokk", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "barlangtörténet", "doometernal": "doometernal", "centuryageofashes": "évszázadporéve", "farcry4": "farcry4", "gearsofwar": "háborúsfelszerelés", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "nulladik<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "lépjbealattjáróba", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernháború2", "blackops1": "feketehadművelet1", "sausageman": "kolbászguru", "ratchetandclank": "ratchetésclank", "chexquest": "chexquest", "thephantompain": "aphantomszenvedés", "warface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossfire": "kereszttűz", "atomicheart": "atomszív", "blackops3": "feketeakció3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON>za<PERSON>ság", "battlegrounds": "csataterek", "frag": "frag", "tinytina": "k<PERSON><PERSON>na", "gamepubg": "játékpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearlibertásfiúk", "juegosfps": "fpsjátékok", "convertstrike": "konvertáljle", "warzone2": "háborúzóna2", "shatterline": "széttörésvonal", "blackopszombies": "feketeakciózombik", "bloodymess": "vérfertőzés", "republiccommando": "republickommandó", "elitedangerous": "elitetartalmas", "soldat": "katona", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "gyülekezet", "destiny1": "sors1", "gamingfps": "játékfps", "redfall": "vörösesés", "pubggirl": "pubggirl", "worldoftanksblitz": "tankokvilága", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON>", "farlight": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "fénycsóva84", "splatoon3": "splatoon3", "armoredcore": "pán<PERSON><PERSON>zottmag", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "kicsitina_csodavilágai", "halo2": "halo2", "payday2": "fizetésnap2", "cs16": "cs16", "pubgindonesia": "pubgindonézia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgrománia", "empyrion": "empyrion", "pubgczech": "pubgcseh", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "valóságotlan<PERSON>ny", "callofdutydmz": "callofdutydmz", "gamingcodm": "játékokcodm", "borderlands2": "borderlands2", "counterstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "pisztolypofon", "callofdutymw2": "callofdutymw2", "quakechampions": "földrengésbajnokok", "halo3": "halo3", "halo": "<PERSON><PERSON><PERSON><PERSON>", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "repedéssejtek", "neonwhite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remnant": "maradvány", "azurelane": "azurelane", "worldofwar": "háborúkvilága", "gunvolt": "<PERSON><PERSON><PERSON><PERSON>", "returnal": "visszatérő", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "árn<PERSON>mber", "quake2": "rázkódás2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "vöröshalott", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "harctér3", "lostark": "elveszettark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "tengeritulajdonosok", "rust": "rozsda", "conqueronline": "h<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "dauntless": "vakmerő", "warships": "hadihajók", "dayofdragons": "sárkányoknapja", "warthunder": "warthunder", "flightrising": "repülésfelemelkedés", "recroom": "recroom", "legendsofruneterra": "runeterralegendák", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "majdn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "tankokvilága", "crossout": "kihúzás", "agario": "agario", "secondlife": "másod<PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "tornyofantázia", "netplay": "netjáték", "everquest": "örökkeresés", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunikrösszvilág", "reddeadonline": "vöröshaláldolgozó", "superanimalroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "lovagonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "isaackötődése", "dragonageinquisition": "sárkánykoraé<PERSON>", "codevein": "<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "feketesivatagonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "kalóz101", "honorofkings": "királyokmegbecsülése", "fivem": "fivem", "starwarsbattlefront": "starwarscsatamező", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "csillagháborúkharcfront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "póniáland", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasszikus", "worldofwarcraft": "worldofwarcraft", "warcraft": "<PERSON><PERSON><PERSON><PERSON>", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "hamvakelet", "riotmmo": "lázadásmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "bosszú", "albiononline": "albiononline", "bladeandsoul": "élszíváséslélek", "evony": "evony", "dragonsprophet": "sárkányokprófétája", "grymmo": "grymmo", "warmane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "angyalokonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverzsolo", "growtopia": "növénytopia", "starwarsoldrepublic": "starwarsrégiköztársaság", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "kékprotokoll", "perfectworld": "tökéletesvilág", "riseonline": "emelkedjfelonline", "corepunk": "maghűség", "adventurequestworlds": "kalandozósquestvilágok", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "aundorors<PERSON><PERSON><PERSON>", "cityofheroes": "hősökvárosa", "mortalkombat": "mortalcombat", "streetfighter": "utcaiharcos", "hollowknight": "üreslovag", "metalgearsolid": "metalgearsolid", "forhonor": "tiszteletért", "tekken": "tekken", "guiltygear": "guiltieszerzés", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "utcaharcos6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "lélekháború", "brawlhalla": "brawlhalla", "virtuafighter": "virtuálisharcos", "streetsofrage": "utcaikbőltanulva", "mkdeadlyalliance": "mkhaláliszövetség", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "aharcokkirálya", "likeadragon": "kedjúgymintadragón", "retrofightinggames": "retróküzdősjátékok", "blasphemous": "istenkáromlás", "rivalsofaether": "rivalsofether", "persona4arena": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelvscapcom": "marvelvscapcom", "supersmash": "szu<PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "szörnyháború", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "kiberrobotok", "armoredwarriors": "páncéltörőharcosok", "finalfight": "végsőharc", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "harcihgames", "killerinstinct": "gyilkosösztön", "kingoffigthers": "királyokharcosa", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "lovagias2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightfolytatás", "hollowknightsilksong": "üreslovagokhercegnő", "silksonghornet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksonggame": "silksongjáték", "silksongnews": "silksonghírek", "silksong": "<PERSON><PERSON><PERSON><PERSON>", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolúciós<PERSON>", "evomoment": "evomoment", "lollipopchainsaw": "nyalókahuoptatő", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "berseriatörténetek", "bloodborne": "vérszomjas", "horizon": "horizont", "pathofexile": "<PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "felfedezetlen", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "utolsóink", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationbarátok", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "nyuszik", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecég", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "<PERSON><PERSON><PERSON>", "rimworld": "peremvilág", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turistatrófea", "lspdfr": "lspdfr", "shadowofthecolossus": "colosszusokárnyé<PERSON>", "crashteamracing": "crashteamracing", "fivepd": "ötpd", "tekken7": "tekken7", "devilmaycry": "ördögöknélkönnyebben", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "szamurájharcosok", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "lélekpenge", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "férfivadászat", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "utolsóőrző", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "b<PERSON>z<PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warharmmer40k", "fightnightchampion": "harciestélybajnok", "psychonauts": "pszichonauták", "mhw": "mhw", "princeofpersia": "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "elderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "azidőidók", "gxbox": "gxbox", "battlefront": "csatamező", "dontstarvetogether": "neéhezzükmegegyütt", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "amerikaisalice", "xboxs": "xboxok", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "királyságokligája", "fable2": "mesemondás2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "égifoglalás", "erica": "erica", "ancestory": "ősök", "cuphead": "csés<PERSON><PERSON>", "littlemisfortune": "kicsimiszerencsétlenség", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "szörn<PERSON>b<PERSON><PERSON>", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motozás", "outerwilds": "külsővadon", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "lamb<PERSON><PERSON><PERSON><PERSON>", "duckgame": "kacsa<PERSON>j<PERSON><PERSON>k", "thestanleyparable": "thestanleyparable", "towerunite": "toronyegyesület", "occulto": "okkult", "longdrive": "hosszúautózás", "satisfactory": "megfelelő", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrialepes", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "lélekkísérő", "darkdome": "<PERSON><PERSON><PERSON>t<PERSON><PERSON><PERSON>", "pizzatower": "pizzatorony", "indiegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "golfit", "truthordare": "igazságvagymerészség", "game": "<PERSON><PERSON><PERSON><PERSON>", "rockpaperscissors": "kőpapírolló", "trampoline": "trambulin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "merj", "scavengerhunt": "kincs<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "kertijátékok", "pickanumber": "válasszszámot", "trueorfalse": "igazvagyham<PERSON>", "beerpong": "<PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "kockababó", "cosygames": "kényelmijátékok", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "ingyenesjáték", "drinkinggames": "ivósjátékok", "sodoku": "sodoku", "juegos": "játékok", "mahjong": "majang", "jeux": "játékok", "simulationgames": "szimulációsjáté<PERSON>k", "wordgames": "szójátékok", "jeuxdemots": "szójátékok", "juegosdepalabras": "szójátékok", "letsplayagame": "já<PERSON><PERSON>dő", "boredgames": "unalmasjatek", "oyun": "<PERSON><PERSON><PERSON><PERSON>", "interactivegames": "interaktívjátékok", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "játékok", "giochi": "játékok", "geoguessr": "geoguessr", "iphonegames": "iphonejátékok", "boogames": "booj<PERSON>téko<PERSON>", "cranegame": "darukaland", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "gépmászkózás", "yakuzagames": "yakuzajátékok", "classicgame": "klasszikusjáték", "mindgames": "elmejátékok", "guessthelyric": "kitaláljadaszöveget", "galagames": "galajátékok", "romancegame": "rom<PERSON><PERSON>j<PERSON><PERSON>k", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "nyelvtörők", "4xgames": "4xjátékok", "gamefi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "játékkonzolok", "tabletopgames": "asztalijátékok", "metroidvania": "metroidvania", "games90": "játékok90", "idareyou": "kihívásnak", "mozaa": "mozaa", "fumitouedagames": "fumitouedajátékok", "racinggames": "versenyjátékok", "ets2": "ets2", "realvsfake": "igaziellenszínház", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "onlinejátékok", "jogosonline": "jogosonline", "writtenroleplay": "írottszerepjáték", "playaballgame": "<PERSON><PERSON><PERSON><PERSON>aballaggal", "pictionary": "pictionary", "coopgames": "kooperatívjátékok", "jenga": "jenga", "wiigames": "wiijátékok", "highscore": "csucspontegység", "jeuxderôles": "szerepjátékok", "burgergames": "burgerszórakozások", "kidsgames": "gyerekgames", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwfeketeverzió", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "<PERSON><PERSON><PERSON><PERSON>", "managementgame": "menedzsmentszínesjáték", "hiddenobjectgame": "elrejtettobjektumjáték", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1játék", "citybuilder": "városépítő", "drdriving": "driving<PERSON><PERSON>g<PERSON><PERSON>", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "emlékjátékok", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "fújójátékgarancia", "pinballmachines": "flippergépek", "oldgames": "régijátékok", "couchcoop": "kanapékoppintás", "perguntados": "kérdezzfelelek", "gameo": "<PERSON><PERSON><PERSON><PERSON>", "lasergame": "lasergame", "imessagegames": "imessagejátékok", "idlegames": "lazajátékok", "fillintheblank": "töltsdkiabelohelyet", "jeuxpc": "pcjátékok", "rétrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logicgames": "logikaijátékok", "japangame": "japanga<PERSON>", "rizzupgame": "rizzfeljáték", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "celebj<PERSON><PERSON><PERSON>", "exitgames": "kijáratigames", "5vs5": "5v5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "hagyományosjátékok", "kniffel": "kniffel", "gamefps": "jatekfps", "textbasedgames": "szövegesjátékok", "gryparagrafowe": "gryparagrafok", "fantacalcio": "fantaszigeti", "retrospel": "retrospél", "thiefgame": "tolvajj<PERSON>ték", "lawngames": "gyepjátékok", "fliperama": "flipert<PERSON><PERSON>", "heroclix": "heroclix", "tablesoccer": "asztalifoci", "tischfußball": "asztalifoci", "spieleabende": "játékkörök", "jeuxforum": "játékfórum", "casualgames": "látványosjatekok", "fléchettes": "nyílvesszők", "escapegames": "sza<PERSON><PERSON><PERSON><PERSON><PERSON>ák", "thiefgameseries": "tolvajjátékokszéria", "cranegames": "darusjáté<PERSON>k", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodból", "jogosorte": "jogosorte", "mage": "varázslat", "cargames": "autósjátékok", "onlineplay": "onlinejáték", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON>k<PERSON>ok", "pursebingos": "táskabingók", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "pcjátékok", "socialdeductiongames": "társadalmiérvelősjátékok", "dominos": "dominók", "domino": "domino", "isometricgames": "izometrikusjátékok", "goodoldgames": "jóöregjátékok", "truthanddare": "igazságvagymerészség", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "kincskeresővadászatok", "jeuxvirtuel": "virtuálisjáté<PERSON>k", "romhack": "romhack", "f2pgamer": "f2pjátékos", "free2play": "ing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesésjátékok", "mushroomoasis": "gombavilág", "anythingwithanengine": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "kardésvará<PERSON><PERSON><PERSON>", "goodgamegiving": "jójátékadományozás", "jugamos": "játszunk", "lab8games": "lab8játékok", "labzerogames": "labzerogames", "grykomputerowe": "gamergépek", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "ritmusjátékok", "minaturegames": "miniatűrjátékok", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "önszeretetjáték", "gamemodding": "játékmódosítás", "crimegames": "bűnözősjátékok", "dobbelspellen": "<PERSON>bblej<PERSON><PERSON><PERSON><PERSON>", "spelletjes": "játékválaszték", "spacenerf": "<PERSON><PERSON><PERSON>", "charades": "szó<PERSON>rak<PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "kooperatívjáték", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "főj<PERSON><PERSON>k", "kingdiscord": "királydiscord", "scrabble": "betűzős", "schach": "sakk", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "pandémiaörökség", "camelup": "tevefutam", "monopolygame": "monopolytotó", "brettspiele": "táblajátékok", "bordspellen": "bordjátékok", "boardgame": "táblajáték", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "társasjátékozás", "risiko": "riszkó", "permainanpapan": "táblajáték", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "vérkör", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "szenet", "goboardgame": "go<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "összekötöttnégy", "heroquest": "hőskereső", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "asztalijátékok", "dicegames": "kockajátékok", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "asztalijátékok", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kozmikustalálkozó", "creationludique": "alkotásélmény", "tabletoproleplay": "asztalifogalmazás", "cardboardgames": "kartonjatekok", "eldritchhorror": "felháborítóizgalom", "switchboardgames": "switchboardjátékok", "infinitythegame": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l", "yahtzee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "buli", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "társasjátékok", "rednecklife": "vörösnyakúélet", "boardom": "unalmam", "applestoapples": "almátólalm<PERSON>ig", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "társasjátékok", "twilightimperium": "alkonybirodalom", "horseopoly": "<PERSON><PERSON><PERSON><PERSON>", "deckbuilding": "paklitalakítás", "mansionsofmadness": "őrültkastélyok", "gomoku": "gomoku", "giochidatavola": "asztalijátékok", "shadowsofbrimstone": "brimstoneárnyai", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "táblajátékok", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "jeg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "asztalijátékok", "catán": "kacs<PERSON><PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "asztalijátékok", "xiángqi": "sakk", "jeuxsociete": "társasjátékozás", "gesellschaftsspiele": "társasjátékok", "starwarslegion": "csillaghábor<PERSON>ó", "gochess": "gocsakk", "weiqi": "weiqi", "jeuxdesocietes": "társasjátékok", "terraria": "terria", "dsmp": "dsmp", "warzone": "<PERSON>á<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arktúlélőkfejlődése", "dayz": "napok", "identityv": "identitásv", "theisle": "sziget", "thelastofus": "azu<PERSON>lsóelőttibúzónk", "nomanssky": "ninc<PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "cthulhuhívása", "bendyandtheinkmachine": "bendyésatintagé<PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "minketbeppelek", "eco": "öko", "monkeyisland": "majmokszigete", "valheim": "valheim", "planetcrafter": "bolygóépítő", "daysgone": "eltűntnapok", "fobia": "fóbia", "witchit": "boszorkányozz", "pathologic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zomboid": "zombik", "northgard": "északváros", "7dtd": "7dtd", "thelongdark": "ahosszúsötétség", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "őr<PERSON>ltapa", "dontstarve": "neéhezz", "eternalreturn": "örökkéntvisszatér", "pathoftitans": "titánokösvénye", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "bennünkevilágfekete", "realrac": "igazirac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "blokktörténet", "thequarry": "akőbánya", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "a<PERSON><PERSON>ő<PERSON>ottj<PERSON>", "wehappyfew": "boldoganvagyunk", "riseofempires": "birodalmakfelemelkedése", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "vintagestory", "arksurvival": "arktúlélés", "barotrauma": "barotrauma", "breathedge": "légzés", "alisa": "alisa", "westlendsurvival": "nyugatiéletbentúlélni", "beastsofbermuda": "bermudai_szörnyetegek", "frostpunk": "fagypunk", "darkwood": "sötétfa", "survivalhorror": "túlélőszörnyek", "residentevil": "residentevil", "residentevil2": "rezidentevil2", "residentevil4": "rezidentevil4", "residentevil3": "rezidentevil3", "voidtrain": "üresvonat", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "túlélőjátékok", "sillenthill": "csendestomb", "thiswarofmine": "ezhabozam", "scpfoundation": "scpfoundation", "greenproject": "zöldprojekt", "kuon": "kuon", "cryoffear": "félelemn<PERSON><PERSON>l", "raft": "csónak", "rdo": "rdo", "greenhell": "zöldpokol", "residentevil5": "rezidensevil5", "deadpoly": "halottpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nagyi", "littlenightmares2": "kisnightmares2", "signalis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "túlélnitúl<PERSON><PERSON>ő<PERSON>r<PERSON><PERSON>", "alienisolation": "idegenelszigetelés", "undawn": "undawn", "7day2die": "7nap2halálozni", "sunlesssea": "napsütésmentetenger", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propnight": "<PERSON><PERSON><PERSON>", "deadisland2": "halottsziget2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvámpír", "deathverse": "hal<PERSON><PERSON>rzum", "cataclysmdarkdays": "cataclysmikusfeketeerők", "soma": "soma", "fearandhunger": "félelemé<PERSON>ő", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "életután", "ageofdarkness": "sötétségkora", "clocktower3": "óratorony3", "aloneinthedark": "egyedülasötétben", "medievaldynasty": "középkoridinasztia", "projectnimbusgame": "projectnimbusjátékmenet", "eternights": "örökké<PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunker", "worlddomination": "világuralom", "rocketleague": "rak<PERSON><PERSON><PERSON><PERSON>", "tft": "tft", "officioassassinorum": "hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "törpégyilkos", "warhammer40kcrush": "warhammer40<PERSON><PERSON><PERSON><PERSON>", "wh40": "wh40", "warhammer40klove": "warhammer40kszerelem", "warhammer40klore": "warhammer40kmesék", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "megváltoztatás", "ilovesororitas": "szeretemasororitasokat", "ilovevindicare": "imádomvindicare", "iloveassasinorum": "imádomassasinorum", "templovenenum": "templovenénem", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40ezer", "tetris": "tetris", "lioden": "lioden", "ageofempires": "birodalomkora", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammersigmarkora", "civilizationv": "civiliz<PERSON><PERSON>óv", "ittakestwo": "ittakestwo", "wingspan": "szárnyfesztávolság", "terraformingmars": "marsalakít<PERSON>", "heroesofmightandmagic": "hősökerej<PERSON>", "btd6": "btd6", "supremecommander": "főparancsnok", "ageofmythology": "mitológia_kora", "args": "k<PERSON>szönet", "rime": "rime", "planetzoo": "b<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "kihely2", "banished": "elűzve", "caesar3": "caesar3", "redalert": "vészjelzés", "civilization6": "civilizáció6", "warcraft2": "warcraft2", "commandandconquer": "parancsoljés<PERSON>j", "warcraft3": "warcraft3", "eternalwar": "örökháború", "strategygames": "stratégiaijátékok", "anno2070": "anno2070", "civilizationgame": "civilizációjáték", "civilization4": "civilizáció4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spóra", "totalwar": "totálisháború", "travian": "travian", "forts": "fortok", "goodcompany": "<PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "otthonvilág", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "királyoknak", "realtimestrategy": "igaziidőstratégia", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "királysággyarokธ์", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "istenség", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davevidámapolinomklassz", "plagueinc": "plagueinc", "theorycraft": "elméletgyártás", "mesbg": "mesbg", "civilization3": "civilizáció3", "4inarow": "4egyből", "crusaderkings3": "kereszteslovagok3", "heroes3": "hősök3", "advancewars": "harci_előretörés", "ageofempires2": "korokharca2", "disciples2": "tanítványok2", "plantsvszombies": "növényekvszombik", "giochidistrategia": "játékstratégia", "stratejioyunları": "stratégiaijátékok", "europauniversalis4": "európauniverzális4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "csodákkora", "dinosaurking": "dínóherceg", "worldconquest": "világhódítás", "heartsofiron4": "ironheart4", "companyofheroes": "hősökcsapata", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "birodalomépítők", "warhammerkillteam": "<PERSON><PERSON><PERSON>samerögyek", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "<PERSON><PERSON><PERSON><PERSON>", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "külsősíkon", "turnbased": "fordulóalapú", "bomberman": "bomberman", "ageofempires4": "korabelibirodalmak4", "civilization5": "civilizáció5", "victoria2": "viktória2", "crusaderkings": "kereszténykirályok", "cultris2": "cultris2", "spellcraft": "varázslatok", "starwarsempireatwar": "csillagokháborúja_birodalomháborúban", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stratégia", "popfulmail": "popfulmail", "shiningforce": "ragyogóerő", "masterduel": "mesterbunyó", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "szállítmányozóvezér", "unrailed": "n<PERSON><PERSON>ás<PERSON>tt", "magicarena": "varázs<PERSON>ata", "wolvesville": "farkas<PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "tervéljölvíszításra", "uplandkingdoms": "felvidékkirályságok", "galaxylife": "galaxélet", "wolvesvilleonline": "farkasvárosonline", "slaythespire": "verjékkéspírt", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "sims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "szükségedvanatempóra", "needforspeedcarbon": "szükségedvanavitessecarbonra", "realracing3": "igaziverseny3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "halottanavilágban", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON>at<PERSON><PERSON>", "darkhorseanthology": "sötétlovagantológia", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "ötéjszakásfreddysszórakozás", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "kiscselesek", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "halottsziget", "litlemissfortune": "kicsimisszerencsétlenség", "projectzero": "projektzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "helloszomsz<PERSON>d", "helloneighbor2": "helloszomszéd2", "gamingdbd": "gamingsdbd", "thecatlady": "macskaszereto", "jeuxhorreur": "horrorjátékok", "horrorgaming": "b<PERSON>zongatójáté<PERSON>k", "magicthegathering": "varázslattalgyűjtés", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kártyákazemberiségellen", "cribbage": "cribbage", "minnesotamtg": "minnesotamgt", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kódnevek", "dixit": "dixit", "bicyclecards": "bicikliszettek", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "runeteralegendája", "solitaire": "szoliter", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "tuzkő", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kulcsalakítás", "cardtricks": "kártyatrükkök", "playingcards": "játékkártyák", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kereskedőkártyák", "pokemoncards": "pokemonkártyák", "fleshandbloodtcg": "húsésvértcg", "sportscards": "sportkártyák", "cardfightvanguard": "kártyacsatafeldőzár", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcry": "háborúkiáltás", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON>zív<PERSON><PERSON><PERSON><PERSON>", "truco": "truko", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkártyák", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohjáték", "darkmagician": "sötétvarázsló", "blueeyeswhitedragon": "kékeszöldszeműfehérsárkány", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kártyajáték", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgparancsnok", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kártyajátékok", "mtgjudge": "mtgítélet", "juegosdecartas": "kártyajátékok", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconparancsnok", "kartenspiel": "kártyajáték", "carteado": "kártyázás", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespiritek", "battlespiritssaga": "csataszellemsaga", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "boozsóka", "facecard": "arcoká<PERSON>ya", "cardfight": "kártyacsata", "biriba": "biriba", "deckbuilders": "kártyakészítők", "marvelchampions": "marvel<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "varázskártyák", "yugiohmasterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "árnyékvilág", "skipbo": "skip<PERSON>", "unstableunicorns": "instabilisunikornisok", "cyberse": "cyberse", "classicarcadegames": "klasszikusjátéktermijátékok", "osu": "osu", "gitadora": "gitadora", "dancegames": "táncjátékok", "fridaynightfunkin": "péntekiestifunk", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "klónhős", "justdance": "csakdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockolddőket", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "ritmus<PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "magaspontritmusjatekok", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "<PERSON><PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritmusmenyország", "hypmic": "<PERSON><PERSON><PERSON>", "adanceoffireandice": "tűzésszéjáték", "auditiononline": "onlineaudíció", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ritmusjátékok", "cryptofthenecrodancer": "kriptoazélőhalottbálja", "rhythmdoctor": "ritmusdoktor", "cubing": "kockázás", "wordle": "wordle", "teniz": "tenisz", "puzzlegames": "puzzlejáté<PERSON><PERSON>", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logikairejtvények", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "agyfarkasok", "rubikscube": "rubiksgubanc", "crossword": "keresztrejtvény", "motscroisés": "keresztrejtvény", "krzyżówki": "keresztrejtvények", "nonogram": "nonogram", "bookworm": "könyvmoly", "jigsawpuzzles": "kirakósok", "indovinello": "rejtv<PERSON><PERSON>", "riddle": "rejtv<PERSON><PERSON>", "riddles": "rejtvények", "rompecabezas": "fejtörő", "tekateki": "tekateki", "inside": "bent", "angrybirds": "dühösmadarak", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "aknakereső", "puzzleanddragons": "puzzleésdragok", "crosswordpuzzles": "keresztrejtvények", "kurushi": "k<PERSON>hi", "gardenscapesgame": "kertekszépítősjáték", "puzzlesport": "fejtörősport", "escaperoomgames": "szabadulószobajátékok", "escapegame": "menekülőjáték", "3dpuzzle": "3d_puzzle", "homescapesgame": "homescapesjáték", "wordsearch": "szókereső", "enigmistica": "rejtv<PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "rejtv<PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "alehetetlenkvíz", "candycrush": "cukorkazás", "littlebigplanet": "kicsinagyvilág", "match3puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "k<PERSON>", "rubikcube": "rubiksgömb", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "otthongyönyörűségek", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "vállalkozósjátékok", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "keresztrejtvény", "ciphers": "titkosírások", "rätselwörter": "rejtvényszavak", "buscaminas": "búgócsiga", "puzzlesolving": "rejtvénymegoldás", "turnipboy": "retkófiú", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "senkik", "guessing": "tippelés", "nonograms": "nonogramok", "kostkirubika": "kostkirubika", "crypticcrosswords": "titkoskeresztszavak", "syberia2": "syberia2", "puzzlehunt": "rejtvényvadás<PERSON>t", "puzzlehunts": "rejtvényvadás<PERSON>t", "catcrime": "macskabűnözés", "quebracabeça": "fejtörő", "hlavolamy": "fejtörők", "poptropica": "poptropica", "thelastcampfire": "azutolsóedzőtűz", "autodefinidos": "öndefináltak", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "karton", "untitledgoosegame": "címnélküliénekgémjáték", "cassetête": "kassza", "limbo": "limbo", "rubiks": "<PERSON><PERSON><PERSON><PERSON>", "maze": "j<PERSON>rdabej<PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "portáljáték", "bilmece": "boo<PERSON><PERSON><PERSON><PERSON>", "puzzelen": "buborékhúzás", "picross": "p<PERSON><PERSON><PERSON>", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "csavartcsodaország", "monopoly": "monopólium", "futurefight": "jövőhar<PERSON>", "mobilelegends": "mobillegendák", "brawlstars": "brawlstars", "brawlstar": "brawl<PERSON>t<PERSON><PERSON>", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "egyesültcsillagok", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "sütime<PERSON><PERSON><PERSON>", "alchemystars": "alchemystars", "stateofsurvival": "túlélésállapota", "mycity": "városom", "arknights": "arknightok", "colorfulstage": "színespálya", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "sorsnagyrend", "hyperfront": "hiperfront", "knightrun": "lovagfutás", "fireemblemheroes": "tűzjelekhősök", "honkaiimpact": "honkaiimpact", "soccerbattle": "futballcsata", "a3": "a3", "phonegames": "telefonjátékok", "kingschoice": "királyiválasztás", "guardiantales": "örökségmesék", "petrolhead": "autóbolond", "tacticool": "<PERSON>kt<PERSON><PERSON><PERSON><PERSON>", "cookierun": "cookiefutam", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "kintvagyok", "craftsman": "kreatívművész", "supersus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "lassúvezetés", "headsup": "figyelmeztető", "wordfeud": "szócsata", "bedwars": "ágyháborúk", "freefire": "freefire", "mobilegaming": "mobil<PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "állatkertkeresztezés", "bgmi": "bgmi", "teamfighttactics": "csapatküzdelemstratégia", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "misztikusüzenet", "callofdutymobile": "callofdutymobile", "thearcana": "azark<PERSON><PERSON>", "8ballpool": "8<PERSON>bda<PERSON><PERSON>", "emergencyhq": "sürgőshq", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "fűműhely", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "rázásokésfitymákolás", "ml": "ml", "bangdream": "bangdream", "clashofclan": "klánokharca", "starstableonline": "csillagstableonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "időhercegnő", "beatstar": "beatstar", "dragonmanialegend": "sárkányemberlegend", "hanabi": "tüzijáték", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "zsebnyiromán<PERSON>", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "b<PERSON><PERSON>ü<PERSON>", "summonerswar": "szumonaacuterokhar<PERSON>", "cookingmadness": "főzőszenvedély", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "angyalokligája", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralfelhő", "mysingingmonsters": "enimo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "kékarchívum", "raidshadowlegends": "raidshadowlegends", "warrobots": "h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "tükörvilág", "pou": "pou", "warwings": "háborús<PERSON><PERSON><PERSON>", "fifamobile": "fifamobil", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "ellen<PERSON><PERSON>", "apexlegendmobile": "apexlegendmobil", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "érmeuszító", "punishinggrayraven": "büntetőszürkevarázsló", "petpals": "petbará<PERSON>", "gameofsultans": "sultánokjátéka", "arenabreakout": "arenabreakout", "wolfy": "<PERSON><PERSON><PERSON>", "runcitygame": "runcityjáték", "juegodemovil": "mobil<PERSON><PERSON><PERSON><PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "mi<PERSON><PERSON>", "blackdesertmobile": "feketeörvénymobil", "rollercoastertycoon": "hullozmester", "grandchase": "nagyvadászat", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdragons": "sárkányokhívása", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "árnyharc3", "limbuscompany": "limbuscompany", "demolitionderby3": "bontogatóbajnokság3", "wordswithfriends2": "barátokkaljátszva2", "soulknight": "léleklovag", "purrfecttale": "cicatörténet", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lollmobil", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "tökéletesvilágmobile", "empiresandpuzzles": "birodalmakésrejtvények", "empirespuzzles": "empirespuzzles", "dragoncity": "sárkányváros", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "kisdémonálom", "aethergazer": "aethernashogató", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "szemelvissza", "eversoul": "mindenslélek", "gunbound": "gunbound", "gamingmlbb": "játékmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombikalandorok", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobillegendaütötteütötte", "gachaclub": "gachaklub", "v4": "v4", "cookingmama": "főzőcuki", "cabalmobile": "cabalmobil", "streetfighterduel": "utcaiharcospárbaj", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "lányokfrontvonal", "jurassicworldalive": "jurassicworldéletben", "soulseeker": "lélekszóval", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "holdteaás", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "j<PERSON><PERSON><PERSON>", "legendofneverland": "sohaországlegendája", "pubglite": "pubglite", "gamemobilelegends": "játékmobillendületek", "timeraiders": "időlovagok", "gamingmobile": "mobil<PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "k<PERSON>ldetés", "giochidiruolo": "posz<PERSON>j<PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "sötétségvilága", "travellerttrpg": "utazótrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "románcklub", "d20": "d20", "pokemongames": "pokemonjátékok", "pokemonmysterydungeon": "pokémonrej<PERSON><PERSON>ér", "pokemonlegendsarceus": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pok<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "pokémonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemon<PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatot": "b<PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokémiviolet", "pokemonpurpura": "pok<PERSON><PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "zsebszörnyek", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonp<PERSON><PERSON>", "teamystic": "csapatmágikus", "pokeball": "pokéball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "csillámpokémon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "v<PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psziduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmester", "pokémonsleep": "pokémonalvás", "kidsandpokemon": "gyerekekéspokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "fényvadász", "ajedrez": "sakk", "catur": "<PERSON>ur", "xadrez": "sakk", "scacchi": "sakk", "schaken": "sakk", "skak": "skak", "ajedres": "sakkbuli", "chessgirls": "sakkcsajok", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "világvillanás", "jeudéchecs": "játékd<PERSON>ntő", "japanesechess": "japánsakkozás", "chinesechess": "kínaisakk", "chesscanada": "sakkkanada", "fide": "fide", "xadrezverbal": "xadrezbes<PERSON><PERSON><PERSON><PERSON><PERSON>", "openings": "megnyitók", "rook": "rook", "chesscom": "sakkozáscom", "calabozosydragones": "kalózokéssárkányok", "dungeonsanddragon": "dungeonésdragon", "dungeonmaster": "ka<PERSON>atames<PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjunsetdragons", "oxventure": "oxventura", "darksun": "sötétnap", "thelegendofvoxmachina": "a_voxmachina_legenda", "doungenoanddragons": "döngetechnóésdragok", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftbajnokság", "minecrafthive": "minecrafthaver", "minecraftbedrock": "minecraftalap", "dreamsmp": "álomsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodok", "mcc": "mcc", "candleflame": "gyer<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "kiegészítők", "mcpeaddons": "mcpekie<PERSON><PERSON><PERSON><PERSON>", "skyblock": "égblokk", "minecraftpocket": "minecraftzsebpénz", "minecraft360": "minecraft360", "moddedminecraft": "moddoltminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "köztesföldek", "minecraftdungeons": "minecraftpince", "minecraftcity": "minecraftváros", "pcgamer": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "gamerpals", "levelup": "<PERSON><PERSON><PERSON><PERSON>kedés", "gamermobile": "gamermobil", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "gamer", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcjátékok", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "játékbeállítás", "pcmasterrace": "pcmasterrace", "pcgame": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "gamerfiú", "vrgaming": "vrjátékok", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerek", "gameplays": "játékmenetek", "consoleplayer": "konsoljá<PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "epikusgamerek", "onlinegaming": "onlinejátékok", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "gamergirls", "gamermoms": "gamermamák", "gamerguy": "gamerguy", "gamewatcher": "j<PERSON><PERSON>kfigyel<PERSON>", "gameur": "gamertárs", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerscsajok", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "csapatigyekezet", "mallugaming": "mallugaming", "pawgers": "pawsik", "quests": "küldetések", "alax": "alax", "avgn": "átlagnak", "oldgamer": "öreg<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "kényelmesjáték", "gamelpay": "gamelpay", "juegosdepc": "pcjátékok", "dsswitch": "dsswitch", "competitivegaming": "versengőjátékozás", "minecraftnewjersey": "minecraftújjersey", "faker": "baba", "pc4gamers": "pc4gamerek", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "heteroszexuálisjátékok", "gamepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "lányokjátékosok", "fnfmods": "fnfmodok", "dailyquest": "nap<PERSON>t", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "gamerlányok", "gamesetup": "játéks<PERSON>tt", "overpowered": "túlságosanerős", "socialgamer": "társa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "c<PERSON><PERSON><PERSON>", "republicofgamers": "játékosokköztársasága", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON>bar<PERSON>", "butuhcewekgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "casualgamer", "89squad": "89csapat", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemerek", "oyunizlemek": "játéknézés", "gamertag": "gamercím", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "vide<PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "közösjáték", "mortdog": "mort<PERSON><PERSON>a", "playstationgamer": "playstationjá<PERSON><PERSON>", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "egészségesgamer", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "nő<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "nyilvánvalóhogyjátékosvagyok", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "gyűjtögető", "humanfallflat": "humaneséslapos", "supernintendo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzsika", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonikus", "fallguys": "esésfiúk", "switch": "váltás", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "zeldalegendája", "splatoon": "splat<PERSON>", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "ügyvédmester", "ssbm": "ssbm", "skychildrenofthelight": "égigyermekeiavilágosságnak", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "sétálószimulátorok", "nintendogames": "nintendosjátékok", "thelegendofzelda": "thelegendofzelda", "dragonquest": "sárkánykeresés", "harvestmoon": "szüretihold", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "avadonlehel<PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "zelda_legends", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON><PERSON><PERSON><PERSON>", "earthbound": "földhözragadt", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "állatkeresztezés", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "szupermá<PERSON>bros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendocsaj", "tloz": "tloz", "trianglestrategy": "háromszögstratégia", "supermariomaker": "szupermarioépítő", "xenobladechronicles3": "xenobladekronikák3", "supermario64": "supermario64", "conkersbadfurday": "makkokrossznap", "nintendos": "nintendók", "new3ds": "új3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyruleharcosok", "mariopartysuperstars": "mariopartyszupersztárok", "marioandsonic": "mario<PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogok", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "<PERSON><PERSON><PERSON><PERSON>", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "vörösragadozók", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "<PERSON><PERSON><PERSON>", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "v<PERSON><PERSON><PERSON>ák", "adcarry": "reklámszállító", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsmagyarország", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "<PERSON><PERSON><PERSON>", "shaco": "shaco", "ligadaslegendas": "legendák<PERSON><PERSON>gg<PERSON><PERSON>ü<PERSON>", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "zedfő", "hexgates": "hexkapuk", "hextech": "hextech", "fortnitegame": "fortni<PERSON>j<PERSON><PERSON>", "gamingfortnite": "fortni<PERSON>j<PERSON><PERSON>", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideojátékok", "scaryvideogames": "ijesztővideójátékok", "videogamemaker": "videójátékfejlesztő", "megamanzero": "megamanzero", "videogame": "videójáték", "videosgame": "videójáték", "professorlayton": "profess<PERSON><PERSON><PERSON>", "overwatch": "<PERSON><PERSON><PERSON><PERSON>", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "varázsló101", "battleblocktheater": "battleblockszinhaz", "arcades": "<PERSON><PERSON><PERSON>ktermek", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "földművelésisimul<PERSON><PERSON>", "robloxchile": "robloxchile_hu", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxnémetors<PERSON>g", "robloxdeutsch": "robloxnémet", "erlc": "erlc", "sanboxgames": "sanboxjátékok", "videogamelore": "videójátékmesék", "rollerdrome": "rollerdrome", "parasiteeve": "parazitázz", "gamecube": "játékdoboz", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON>világ", "starcitizen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "videójátékok", "theoldrepublic": "azöregköztársaság", "videospiele": "játékok", "touhouproject": "touhouproject", "dreamcast": "álomdoboz", "adventuregames": "kalandjátékok", "wolfenstein": "wolfenstein", "actionadventure": "akciókaland", "storyofseasons": "évszakokmeséje", "retrogames": "retrójátékok", "retroarcade": "<PERSON>tr<PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "vintagecomputing", "retrogaming": "retrózás", "vintagegaming": "régiiskolásjátékok", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "igazságtalanság2", "shadowthehedgehog": "shadowäśzenek", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "meredek", "mystgames": "mystjáté<PERSON>k", "blockchaingaming": "blokkláncjátékok", "medievil": "középkori", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konszol", "outrun": "megelőzni", "bloomingpanic": "bloomingpánik", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gamingszörnyek", "monstergirlquest": "monstergirlkaland", "supergiant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightv<PERSON>lgy", "farmingsims": "farmingszimik", "juegosviejos": "régiőrültjatekok", "bethesda": "bethesda", "jackboxgames": "jackboxjátékok", "interactivefiction": "interaktívirodalom", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "azutolsóesély2", "amantesamentes": "szerelmesmentes", "visualnovel": "vizu<PERSON><PERSON><PERSON><PERSON>", "visualnovels": "vizu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rggozz", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrszellem", "payday": "fizetésinapot", "chatherine": "katka", "twilightprincess": "alkonyhercegnő", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "aesthetic<PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "novelaavisual", "thecrew2": "acsapat2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "istenkezű", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "szinttervezés", "starrail": "csillagpálya", "keyblade": "k<PERSON><PERSON>kard", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafnéha", "novelasvisuales": "vizu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "játékok", "videogamedates": "videójátéktalálkozók", "mycandylove": "cukromszerelmem", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "csakazértis3", "hulkgames": "hulkgames", "batmangames": "batmanjáté<PERSON><PERSON>", "returnofreckoning": "visszatérésaszámonkéréshez", "gamstergaming": "gamstergaming", "dayofthetantacle": "tántorítnap", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "balesztracing", "3dplatformers": "3dplatformerek", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "régiiskolásgaming", "hellblade": "hellblade", "storygames": "történetjátékok", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "hangdodger", "beyondtwosouls": "túlinkékélek", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "felpörge<PERSON>s", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafikaiutazások", "quickflash": "gyorsvillanás", "fzero": "fzero", "gachagaming": "gachagépes", "retroarcades": "retroarcádok", "f123": "f123", "wasteland": "pusztaság", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "korallsziget", "syberia3": "szibéria3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxgyümölcs", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animeharcosok2", "footballfusion": "focif<PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON>ztronaut<PERSON>", "legomarvel": "legokomikszuperhős", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "csavartfém", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "szégyenhalom", "simulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symulatory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedrunner": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epikusx", "superrobottaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "samésmax", "grywideo": "<PERSON><PERSON><PERSON><PERSON>", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "csodavilagonline", "skylander": "égimá<PERSON>", "boyfrienddungeon": "bar<PERSON>tfi<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownújraírás", "simracing": "<PERSON>zi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "sim<PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "men<PERSON><PERSON><PERSON><PERSON>_testek", "seum": "seum", "partyvideogames": "bulizósjátékok", "graveyardkeeper": "temetőgondnok", "spaceflightsimulator": "űrhajóz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "kainöröksége", "hackandslash": "hackeljésésvágás", "foodandvideogames": "kajákésvideójátékok", "oyunvideoları": "j<PERSON><PERSON>kvideók", "thewolfamongus": "afarkasközöttünk", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "j<PERSON><PERSON><PERSON>k<PERSON>z", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>d<PERSON>", "oldschoolvideogames": "régiiskolásjátékok", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "akáoszügynökei", "songpop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "olümposztúrák", "monsterhunternow": "szörnyvadászmá", "rebelstar": "lázadócsillag", "indievideogaming": "függetlenvideójátékok", "indiegaming": "függetlenjátékok", "indievideogames": "indievideókjátékok", "indievideogame": "függetlenvideójáték", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "pókemberazinsomniacban", "bufffortress": "erődfitness", "unbeatable": "verhe<PERSON>tlen", "projectl": "projektl", "futureclubgames": "jövőklubjátékok", "mugman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "szuperóriásjátékok", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "nyílástudomány", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "<PERSON><PERSON><PERSON>klist<PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "karakterjátékok", "achievementhunter": "morzsavadász", "cityskylines": "városilátképek", "supermonkeyball": "s<PERSON><PERSON>majomlabda", "deponia": "deponia", "naughtydog": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "retrojátékok", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriésakakötetterdő", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezerváldbelépődopamint", "staxel": "staxel", "videogameost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "sárkányszinkron", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "imádomkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "őrj<PERSON>ngő", "baki": "baki", "sailormoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintseiya": "seiyakaland", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "kezdődőd", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "szomorúanime", "darkerthanblack": "sötétebbmintafekete", "animescaling": "animefokozás", "animewithplot": "animeplotos", "pesci": "pesci", "retroanime": "retr<PERSON><PERSON><PERSON>", "animes": "<PERSON><PERSON><PERSON><PERSON>", "supersentai": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstone1évad", "rapanime": "rapanime", "chargemanken": "töltőférfi", "animecover": "animeborító", "thevisionofescaflowne": "escaflownevízió", "slayers": "ölők", "tokyomajin": "tokyomajin", "anime90s": "anime90esévek", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "wchezkötöttanánakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "tűzharc", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "mese", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "para<PERSON>ta", "punpun": "puncsopom", "shingekinokyojin": "shingekinokyojin", "mushishi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastars": "<PERSON><PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sellőmelódia", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "<PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "sárkán<PERSON>án<PERSON>", "blacklagoon": "feketedömping", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "zsenikinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "bizonyosvarázslatosmutató", "sao": "sao", "blackclover": "feketelóhere", "tokyoghoul": "tokyoghoul", "onepunchman": "együtthúzásman", "hetalia": "<PERSON><PERSON><PERSON><PERSON>", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "roppanósroll", "aot": "aot", "sk8theinfinity": "sk8azvégtelen", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "k<PERSON><PERSON><PERSON>al<PERSON><PERSON>", "rezero": "rezero", "swordartonline": "kardművészetonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriorit<PERSON>", "angelsofdeath": "halálangyalok", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnoszimptom", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermusume": "szörnylány", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanimék", "sukasuka": "s<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "angyalszív", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagafalvaazördögтисztes", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "szépséggyógyító", "theboyandthebeast": "fiúésszörny", "fistofthenorthstar": "északicsillagöklödi", "mazinger": "mazinger", "blackbuttler": "feketefiú", "towerofgod": "istenitorony", "elfenlied": "elfenlíra", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "szolgamamp", "howtokeepamummy": "hogytartsukfennamumit", "fullmoonwosagashite": "teliholdwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "cukiésijesztő", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "csa<PERSON>las<PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "szörnylány", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "szkaramucs", "amiti": "amitit", "sailorsaturn": "hajósokaszaturn", "dio": "<PERSON><PERSON>", "sailorpluto": "tengeri_plútó", "aloy": "aloy", "runa": "runa", "oldanime": "regiani<PERSON>", "chainsawman": "láncfűrészember", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "fek<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "klaymór", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "gyümölcsfakadam", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "fiatalf<PERSON><PERSON><PERSON>tt", "lovelive": "éljenakerelem", "sakuracardcaptor": "sakurakártyavadász", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "azígértsohaország", "monstermanga": "szörnymanga", "yourlieinapril": "hazugságáprilisban", "buggytheclown": "buggybojoc", "bokunohero": "bokunohero", "seraphoftheend": "végítéletangyilkosa", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "halottemberország", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "ételháborúk", "cardcaptorsakura": "kártyavadászsakura", "stolas": "stolas", "devilsline": "ördögvonal", "toyoureternity": "teörökkévalódban", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "kékperiodus", "griffithberserk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "titkosalliance", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "eltüntetve", "bluelock": "k<PERSON>k<PERSON><PERSON><PERSON>", "goblinslayer": "go<PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detektívconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasztgremory", "shojobeat": "shojobeat", "vampireknight": "vérszívólovag", "mugi": "mugi", "blueexorcist": "kékördöglött", "slamdunk": "zsákolás", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "böngészett", "spyfamily": "k<PERSON><PERSON><PERSON>al<PERSON><PERSON>", "airgear": "légifelszerelés", "magicalgirl": "varázslólány", "thesevendeadlysins": "hetvenhalalosbűn", "prisonschool": "börtöniskola", "thegodofhighschool": "aközépiskolaistene", "kissxsis": "csók<PERSON><PERSON>", "grandblue": "nagykék", "mydressupdarling": "öltöztessbájtalálkom", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverz<PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hosszúpálca", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "romantikusmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromán<PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "dé<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "vérfiú", "goodbyeeri": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "tűzpofon", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "csillagokösszeérnek", "romanceanime": "romantikusanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cseresznyevarázs", "housekinokuni": "házikínókuña", "recordragnarok": "ragnárokfelvétel", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "élőhalottiskola", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbszuper", "princeoftennis": "teniszherceg", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "gyilkososztályterem", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "lán<PERSON><PERSON>nd<PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqtartó", "indieanime": "függ<PERSON><PERSON><PERSON><PERSON>", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "nyúljapánanimáció", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "baracklány", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklub", "dragonquestdai": "sárkánykalanddai", "heartofmanga": "mangaszív", "deliciousindungeon": "finomadungeonban", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokrekordja", "funamusea": "funamúzeum", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "mangaani<PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "túltuningolt", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravefőnök", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "boszorkánykalapműhely", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaazélet", "dropsofgod": "istencseppek", "loscaballerosdelzodia": "zodiárosfiúk", "animeshojo": "animeshojo", "reverseharem": "fordított_hárem", "saintsaeya": "szentsaeya", "greatteacheronizuka": "nagyszerőtanáronizuka", "gridman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kokorone": "koko<PERSON>", "soldato": "katona", "mybossdaddy": "főnökapukám", "gear5": "gear5", "grandbluedreaming": "nagykéklét", "bloodplus": "vérplusz", "bloodplusanime": "vérplúszanime", "bloodcanime": "véranime", "bloodc": "vérc", "talesofdemonsandgods": "démonokésistenekmeséi", "goreanime": "goreanime", "animegirls": "animecsajok", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON>okxlegrosszabb", "splatteranime": "fr<PERSON><PERSON><PERSON><PERSON>", "splatter": "fr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "apajzsoshősfelemelkedése", "somalianime": "szo<PERSON><PERSON><PERSON>me", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeispanyolország", "animeciudadreal": "animeciudadreal", "murim": "muri", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "bálnákgyerekei", "liarliar": "hazughazug", "supercampeones": "szuperbajnokok", "animeidols": "animeidolok", "isekaiwasmartphone": "isekai2mobilról", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "varázslányok", "callofthenight": "éjszakaívás", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "mahou<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "hercegnő<PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradicsomcsók", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuecsillagfénye", "animeverse": "animeuniverzum", "persocoms": "persocomok", "omniscientreadersview": "omniscensolvashuview", "animecat": "animecica", "animerecommendations": "animeajánlók", "openinganime": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "tinimagyarromantikuskomédia", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamok", "voltesv": "voltesv", "giantrobots": "óriásrobotok", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilefighterggundam": "mobilharcosggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilsuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON>lnapló", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojószörnykaland", "fullmetalalchemist": "teljesfémbűvész", "ghiaccio": "j<PERSON>g", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "milit<PERSON><PERSON><PERSON><PERSON>", "greenranger": "zöldharcos", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "csillagróka", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "anime<PERSON><PERSON><PERSON>", "animetamil": "animetamiltam", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeonepiece": "animeegytszd", "dbz": "dbz", "dragonball": "sárkánygömb", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonkaland", "hxh": "hxh", "highschooldxd": "gimnáziumdxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "drkő", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "titánokharca", "erenyeager": "er<PERSON>zomjas", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "felmé<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "támadjadestitans", "theonepieceisreal": "aonepieceigaz", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobps<PERSON>cho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "örömhúzóeffektus", "digimonstory": "digimonstory", "digimontamers": "digimontamerek", "superjail": "szuperbörtön", "metalocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "hibátlanwebtoon", "kemonofriends": "kemonobandák", "utanoprincesama": "utanóprincesama", "animecom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bobobobobobobo": "bobbobobobobobo", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON>sak<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "mindszentutca", "recuentosdelavida": "életmódkihívások"}