{"2048": "2048", "mbti": "mbti", "enneagram": "enneagrammid", "astrology": "astroloogia", "cognitivefunctions": "kognitiivsedfunktsioonid", "psychology": "psühholoogia", "philosophy": "filosoofia", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "füüsika", "science": "teadus", "culture": "kultuur", "languages": "keeled", "technology": "tehnoloogia", "memes": "meemid", "mbtimemes": "m<PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "astroloogiam<PERSON><PERSON>d", "enneagrammemes": "enneagrammimeemid", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "videod", "gadgets": "vidinad", "politics": "poliitika", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON>uanded", "lifeadvice": "eluta<PERSON><PERSON>", "crypto": "krüpto", "news": "uudised", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "arheoloogia", "learning": "<PERSON><PERSON><PERSON><PERSON>", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "konspiratsiooniteooriad", "universe": "universum", "meditation": "meditatioon", "mythology": "mütoloogia", "art": "kunst", "crafts": "meisterdamine", "dance": "tants<PERSON>ine", "design": "disain", "makeup": "meikimine", "beauty": "ilu", "fashion": "mood", "singing": "laulmine", "writing": "kirjutamine", "photography": "fotograafia", "cosplay": "cosplay", "painting": "maalimine", "drawing": "joonistamine", "books": "<PERSON><PERSON><PERSON><PERSON>", "movies": "filmid", "poetry": "luule", "television": "televisioon", "filmmaking": "filmitegemine", "animation": "animatisioon", "anime": "anime", "scifi": "scifi", "fantasy": "fan<PERSON><PERSON>a", "documentaries": "dokumentaalfilmid", "mystery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comedy": "komöödia", "crime": "krimi", "drama": "d<PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "d<PERSON><PERSON>", "horror": "<PERSON><PERSON><PERSON>", "romance": "romantika", "realitytv": "realitytv", "action": "<PERSON><PERSON><PERSON><PERSON>", "music": "muusika", "blues": "bluus", "classical": "klassika", "country": "kantri", "desi": "desi", "edm": "edm", "electronic": "elektrooniline", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "<PERSON><PERSON><PERSON><PERSON>", "kpop": "kpop", "latin": "ladina", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "<PERSON><PERSON><PERSON>", "reggae": "reggae", "rock": "rokk", "techno": "tekno", "travel": "reisimine", "concerts": "kontserdid", "festivals": "festivalid", "museums": "muuseumid", "standup": "standup", "theater": "teater", "outdoors": "<PERSON><PERSON>", "gardening": "<PERSON><PERSON><PERSON>", "partying": "pidutsemine", "gaming": "mäng<PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "male", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "toit", "baking": "k<PERSON><PERSON><PERSON>", "cooking": "toiduvalmistamine", "vegetarian": "taimetoit", "vegan": "veganlus", "birds": "linn<PERSON>", "cats": "kassid", "dogs": "koerad", "fish": "kalad", "animals": "loomad", "blacklivesmatter": "must<PERSON><PERSON>dloevad", "environmentalism": "keskkonnakaitse", "feminism": "feminism", "humanrights": "inimõigused", "lgbtqally": "lgbtqsõber", "stopasianhate": "lõpetadaasiaatidevihkamine", "transally": "transsõber", "volunteering": "vabatahtliktöö", "sports": "sport", "badminton": "sulgpall", "baseball": "pes<PERSON><PERSON>", "basketball": "korvpall", "boxing": "poks", "cricket": "kriket", "cycling": "rattasõit", "fitness": "fitness", "football": "j<PERSON><PERSON><PERSON><PERSON>", "golf": "golf", "gym": "jõusaal", "gymnastics": "gümnastika", "hockey": "hoki", "martialarts": "võitluskunstid", "netball": "netball", "pilates": "pilaates", "pingpong": "la<PERSON><PERSON><PERSON>", "running": "jook<PERSON>ine", "skateboarding": "rulatamine", "skiing": "suusatamine", "snowboarding": "lumelauasõit", "surfing": "surfamine", "swimming": "ujumine", "tennis": "tennis", "volleyball": "võrkpall", "weightlifting": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "jooga", "scubadiving": "sukeldumine", "hiking": "<PERSON><PERSON><PERSON>", "capricorn": "kaljukits", "aquarius": "<PERSON><PERSON><PERSON>", "pisces": "kalad", "aries": "<PERSON><PERSON><PERSON><PERSON>", "taurus": "sõnn", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "vähk", "leo": "l<PERSON><PERSON>", "virgo": "ne<PERSON>i", "libra": "ka<PERSON><PERSON>", "scorpio": "skorpion", "sagittarius": "ambur", "shortterm": "l<PERSON><PERSON><PERSON><PERSON>", "casual": "vabameelne", "longtermrelationship": "p<PERSON><PERSON><PERSON><PERSON>e", "single": "üksik", "polyamory": "polü<PERSON><PERSON>", "enm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "les<PERSON>", "bisexual": "biseksuaalne", "pansexual": "panseksua<PERSON>", "asexual": "aseksuaalne", "reddeadredemption2": "punanesurnutustagastus2", "dragonage": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "mürsuklubiline", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "valvekoerad", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kuningasteekond", "soulreaver": "<PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "spyrolegend", "rouguelikes": "pah<PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "päikeseloojangupöördemäng", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "tule<PERSON><PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildisõjad", "openworld": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "stormikangelad", "cytus": "cytus", "soulslike": "hinges<PERSON><PERSON><PERSON>", "dungeoncrawling": "vangikongikraapimine", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "pla<PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "lordid<PERSON>ningriigis2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "värvivool", "medabots": "medabotid", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersiivsimud", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "võlur", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "tumedadhinged", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modding": "modimine", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "lõppfantasiaklassika", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "lõppfantasia", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbiidmotiveerimine", "finalfantasyvii": "lõplikfantaasiavii", "ff8": "ff8", "otome": "otome", "suckerforlove": "armastuseafäär", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "aegadeocariin", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiiridepeo", "dimension20": "mõõtmed20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "<PERSON><PERSON><PERSON>", "pathfinder2ndedition": "teedots2ndväljaanne", "shadowrun": "varjuretk", "bloodontheclocktower": "verikella<PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "lõppfantaasia8", "ffxvi": "ffxvi", "lovenikki": "armast<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitatsioonikihutamine", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "üheshots", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON>", "yourturntodie": "sinuvaeg<PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "vanadskrollidonline", "reka": "reka", "honkai": "honkai", "marauders": "röövlid", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "t<PERSON><PERSON><PERSON>sõdakotor", "demonsouls": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "pimedadaedik", "eclipsephase": "eclipsefaas", "disgaea": "disgaea", "outerworlds": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "krpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "kolbnaised", "nightcity": "ö<PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "meeleheitemäng", "jaggedalliance2": "hõredaloodustik2", "neverwinter": "kunagitalvemets", "road96": "tee96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "rogueliked", "gothamknights": "<PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "unustatudkuningriigid", "dragonlance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "valoritarind", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "valgus<PERSON>s", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonmaailm", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "katkisedtroonid", "horizonforbiddenwest": "horizonkeelatudlääne", "twewy": "twewy", "shadowpunk": "varjupunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartimüsteerium", "deltagreen": "deltaroheline", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastepoch": "<PERSON><PERSON><PERSON><PERSON>", "starfinder": "t<PERSON>hetead<PERSON>", "goldensun": "kuldnepäike", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "hämardumine2000", "sandevistan": "sandevistan", "cyberpunk": "kyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "küberpunkpunane", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "lange<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "kuradiellujääja", "oldschoolrunescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "jumalus", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON>madb<PERSON><PERSON>", "adventurequest": "adventurequest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "lõppfantasia9", "sunhaven": "päikesekodu", "talesofsymphonia": "sümfooniajutud", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "lõppfantasia13", "daggerfall": "daggerfall", "torncity": "tornilinn", "myfarog": "müü<PERSON>oog", "sacredunderworld": "pühapäevamaailm", "chainedechoes": "ketijahäälivad", "darksoul": "tume<PERSON><PERSON><PERSON>l", "soulslikes": "soulsliked", "othercide": "teisediotsused", "mountandblade": "mägedesjaokeaniääres", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "ajakäivitaja", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "jao<PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "dragoontütrelegendi", "xenobladechronicles2": "xenobladekroonikad2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "väikemets", "childrenofmorta": "morta_lapsed", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "lugu3", "fablethelostchapter": "fablekaotatudpeatükk", "hiveswap": "mesi<PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenigavene", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "tähevälja", "oldschoolrevival": "vanaanekoolipöördumine", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kuningriigikute1", "ff9": "ff9", "kingdomheart2": "kuningriigikardi2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "rpgmängud", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "kuningriigikardi3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klannalkavian", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "metsikuajad", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "arcadia<PERSON>evad", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplikaant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON>i", "breathoffire4": "tulehingamine4", "mother3": "ema3", "cyberpunk2020": "kyberpunk2020", "falloutbos": "fallaoutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "nõiakatseheart", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "teejuhikrpg", "pathfinder2e": "teejuhiks2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "boo", "spelljammer": "tähekruiisiruum", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kronorists", "cocttrpg": "cocttrpg", "huntroyale": "jahtkuningriik", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstrijahimeemaailm", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgfoorum", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "terashing", "baldursgate3": "baldursgate3", "kingdomcome": "kuning<PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "maailmlõpebsinuga", "dragalialost": "dragalialost", "elderscroll": "vanakäsk", "dyinglight2": "suretulek2", "finalfantasytactics": "finalfantasytaktika", "grandia": "grandiia", "darkheresy": "t<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitanid", "forumrpg": "foorumrpg", "golarion": "golarion", "earthmagic": "ma<PERSON>me", "blackbook": "<PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "taevapeeled<PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "pühagoldiversioon", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gootiikmäng", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "mängurpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "punktjaoklikk", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pal<PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "vabapool", "epic7": "epic7", "ff7evercrisis": "ff7igavuskriis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postküberpunk", "deathroadtocanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "rüütelklh", "monsterhunter": "loomadejahtija", "fireemblem": "tule<PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremantsus", "persona5": "persona5", "ghostoftsushima": "tsushimaghost", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "koletisjahtmeenegise", "nier": "nier", "dothack": "hakime", "ys": "ys", "souleater": "hingesööja", "fatestaynight": "rasvavääribööd", "etrianodyssey": "etrianodyssey", "nonarygames": "mitme<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "taktikalinergp", "mahoyo": "mahooyo", "animegames": "animegamid", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "igavikunesonaat", "princessconnect": "printsessikontakt", "hexenzirkel": "hexenzirkel", "cristales": "krist<PERSON><PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "taskusage", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "eport", "mlg": "mlg", "leagueofdreamers": "unistajateliiga", "fifa14": "fifa14", "midlaner": "midlanevaataja", "efootball": "efootball", "dreamhack": "unistustehäkk", "gaimin": "gaimin", "overwatchleague": "overwatchliiga", "cybersport": "kybersport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eracing": "eracing", "brasilgameshow": "brasilimängunäitus", "valorantcompetitive": "valorantkonkurents", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "poolelu", "left4dead": "left4dead", "left4dead2": "vasak4surnud2", "valve": "valve", "portal": "portaal", "teamfortress2": "meeskonnakindel2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "kitsesimulaator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "vabadusplaneet", "transformice": "transformice", "justshapesandbeats": "lihtsaltkujundidjalood", "battlefield4": "lahedussõda4", "nightinthewoods": "ö<PERSON><PERSON><PERSON><PERSON>", "halflife2": "poolelu2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskivihmast2", "metroidvanias": "metroidvanias", "overcooked": "üleküpsetatud", "interplanetary": "interplaneetiline", "helltaker": "purgatoor", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON>", "foxhole": "kitselõks", "stray": "kõrvaline", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "lauavõitlus1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesert": "must<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "lauamängusimulaator", "partyhard": "pid<PERSON><PERSON>ovalt", "hardspaceshipbreaker": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "kinnijäänudjesteriga", "dinkum": "dinkum", "predecessor": "eelkäija", "rainworld": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "qud<PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolooniamäng", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionimeistrid", "grimdawn": "karmhommik", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "<PERSON><PERSON><PERSON>", "soulworker": "hingetööline", "datingsims": "kohtingumängud", "yaga": "yaga", "cubeescape": "kuubipõgenemine", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "uuslinn", "citiesskylines": "linn<PERSON><PERSON><PERSON>", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtuaalkenopsia", "snowrunner": "lumetõ<PERSON><PERSON>", "libraryofruina": "r<PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "mittevalimismängud", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "rahulikplastnuustik", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "naerataeminule", "catnight": "<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "superlihapoisike", "tinnybunny": "tinnyjänku", "cozygrove": "mugavafarm", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "vikerkaar6", "apexlegends": "apexlegends", "cod": "liblikas", "borderlands": "piirima<PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombid", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "maapäästeteam", "huntshowdown": "jahtshowdown", "ghostrecon": "vaimurekonnad", "grandtheftauto5": "grandtheftauto5", "warz": "sõda", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "liitugegrupiga", "echovr": "echovr", "discoelysium": "discokosmose", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "surmalingus", "b4b": "b4b", "codwarzone": "codsõjavöönd", "callofdutywarzone": "callofdutywarzone", "codzombies": "koodizombid", "mirrorsedge": "peegli<PERSON><PERSON><PERSON><PERSON>", "divisions2": "jaotused2", "killzone": "tapmisala", "helghan": "hel<PERSON>", "coldwarzombies": "külmasõdazombid", "metro2033": "metro2033", "metalgear": "metallikangur", "acecombat": "<PERSON><PERSON><PERSON><PERSON>", "crosscode": "r<PERSON><PERSON><PERSON>", "goldeneye007": "kuldsilmsilma007", "blackops2": "mustategevus2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "neonkupliku", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "piirima<PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalsulg", "primalcarnage": "primalcarnage", "worldofwarships": "vägedeilm", "back4blood": "back4blood", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "tapper", "masseffect": "massiivseteefekt", "systemshock": "süsteemishokk", "valkyriachronicles": "valkyriaajaloos", "specopstheline": "specopstheline", "killingfloor2": "tapmispõrand2", "cavestory": "koo<PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "sõdadehammastega", "mwo": "mwo", "division2": "jaotamine2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generatsioonnull", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "mustategevus1", "sausageman": "vors<PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "phantoomival<PERSON>", "warface": "sõjak<PERSON>jud", "crossfire": "r<PERSON><PERSON><PERSON>", "atomicheart": "aatomi<PERSON><PERSON><PERSON><PERSON>", "blackops3": "mustategevus3", "vampiresurvivors": "vampirijääjad", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "vabadus", "battlegrounds": "lahinguväljad", "frag": "frag", "tinytina": "tinatina", "gamepubg": "mängupubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metallikangelastepoegadeva<PERSON><PERSON>", "juegosfps": "fpsmängud", "convertstrike": "konverteerištrike", "warzone2": "sõjaväe2", "shatterline": "shatterline", "blackopszombies": "mustasaldajadzombid", "bloodymess": "veretuksilood", "republiccommando": "vabariigiü<PERSON>us", "elitedangerous": "eliitsaaletav", "soldat": "soldat", "groundbranch": "ma<PERSON><PERSON>", "squad": "tiim", "destiny1": "saatus1", "gamingfps": "mängufps", "redfall": "punakukkumine", "pubggirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldoftanksblitz": "maailmatankidblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "lii<PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "soomustatudtuum", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "palgapäev2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromaania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "seebikood", "ghostcod": "kummituskaart", "csplay": "csm<PERSON>ng", "unrealtournament": "ebarealneturn<PERSON>r", "callofdutydmz": "callofdutydmz", "gamingcodm": "mängimiskoodm", "borderlands2": "piiriland2", "counterstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakeh<PERSON><PERSON>d", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "saatus2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonwhite", "remnant": "jäägid", "azurelane": "azurelane", "worldofwar": "sõdademaailm", "gunvolt": "relvavolt", "returnal": "tagasi", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "mi<PERSON><PERSON><PERSON><PERSON>", "reddead": "pu<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "lahinguväli3", "lostark": "lostark", "guildwars2": "gildisõjad2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "mereröövlid", "rust": "rooste", "conqueronline": "vallutaonline", "dauntless": "kartmatu", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "runeterraarstid", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "fantasiatähtiüksused2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "tankideilm", "crossout": "üleskandmine", "agario": "agario", "secondlife": "teiseluelu", "aion": "aion", "toweroffantasy": "fantaasiatorn", "netplay": "netimäng", "everquest": "igavesesoojad", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "punaverelineonline", "superanimalroyale": "superloomingureaalne", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "rüütelonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "isaacisidumine", "dragonageinquisition": "dragonageinquisition", "codevein": "koodiviin", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubipingviin", "lotro": "lotro", "wakfu": "wakfu", "scum": "soppa", "newworld": "uusmaailm", "blackdesertonline": "mustakõrbeonline", "multiplayer": "mängimekaaskoos", "pirate101": "piraat101", "honorofkings": "k<PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "tähesõdadelaingumäng", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "tähesõdadelahinguväli2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponylinn", "3dchat": "3d<PERSON><PERSON><PERSON>", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassika", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neokujud", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "loomiseäashesofcreation", "riotmmo": "mässumäng", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "d<PERSON>akonitep<PERSON>h<PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "mitmikmäng", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversetonline", "growtopia": "growtopia", "starwarsoldrepublic": "tähesõdadekohalikkonföederatsioon", "grandfantasia": "suurfantaasia", "blueprotocol": "sinineprot<PERSON>ll", "perfectworld": "täiuslikmaailm", "riseonline": "tõusvõrgus", "corepunk": "tuumapunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "lenn<PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsolid", "forhonor": "austuseeest", "tekken": "tekken", "guiltygear": "süüdlause", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "tänavamõrvar6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuaalvõitleja", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkõhtutöölisliit", "nomoreheroes": "rohkemkangelasi", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "võitluskuningas", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrovahtlemismängud", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "rivaali<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "ülihämmastav", "mugen": "mugen", "warofthemonsters": "monstritesõda", "jogosdeluta": "võitlusmängud", "cyberbots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "rakend<PERSON>ang<PERSON><PERSON>", "finalfight": "lõppvõitlus", "poweredgear": "võimsustarvikud", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "võitlusmängud", "killerinstinct": "tapjainstinkt", "kingoffigthers": "kuningasvõitlejad", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "rüütellikkus2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "süüdivvõitlus", "hollowknightsequel": "hallowknightideepeat", "hollowknightsilksong": "tühiööritusesii<PERSON>laul", "silksonghornet": "silksonghornet", "silksonggame": "silksongmäng", "silksongnews": "silksongiuudised", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutsiooniturniir", "evomoment": "evomoment", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "veretukste", "horizon": "horisont", "pathofexile": "exiilitee", "slimerancher": "slimerancher", "crashbandicoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodbourne": "veretavpõrgu", "uncharted": "avastam<PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "viimanemeid", "infamous": "kütkestav", "playstationbuddies": "playstationikaaslased", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbidid", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "aisom<PERSON><PERSON><PERSON><PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "sõdadejumal", "gris": "gris", "trove": "aare", "detroitbecomehuman": "detroitmuutubinimeseks", "beatsaber": "beats<PERSON>r", "rimworld": "rimimaailm", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turismitrofee", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivepd": "viiepd", "tekken7": "tekken7", "devilmaycry": "devil<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "kuraditulebkinutma3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samura<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "viimaneigatsus", "soulblade": "<PERSON>nge<PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "varjuhinged2leping", "pcsx2": "pcsx2", "lastguardian": "viimsepere", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "m<PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "pid<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "võitlusõhtumeistrid", "psychonauts": "psühhonautide", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "vanadeskrolldskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "vanadeskered", "gxbox": "gxboks", "battlefront": "la<PERSON><PERSON>d", "dontstarvetogether": "ärälöödtogether", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "amerikamehealice", "xboxs": "xboxid", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "kuningriikideliit", "fable2": "muinasjutt2", "xboxgamepass": "xboxmängupass", "undertale": "undertale", "trashtv": "prügitelevisioon", "skycotl": "taevakotka", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "väikesepahandused", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "koletiseprom", "projectzomboid": "projektizomboid", "ddlc": "ddlc", "motos": "motoelu", "outerwilds": "väljametsad", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "parttamm", "thestanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "towerunite": "tornikohtumine", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "pikkdrive", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviofiil", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geo<PERSON><PERSON><PERSON><PERSON><PERSON>", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "tumedakuppel", "pizzatower": "pizzatorn", "indiegame": "indiemäng", "itchio": "itchio", "golfit": "golfit", "truthordare": "tõdevõiülesanne", "game": "mäng", "rockpaperscissors": "kividpaberkäärid", "trampoline": "trampoliin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "väljakutse", "scavengerhunt": "aaretejooks", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "valiinumbrid", "trueorfalse": "tõdevõivale", "beerpong": "beerpong", "dicegoblin": "täringukääruline", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "kohtingumängud", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "m<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "m<PERSON><PERSON><PERSON>", "simulationgames": "simulat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "mängimevälja", "boredgames": "igavadmängud", "oyun": "mäng", "interactivegames": "interaktiivsedmängud", "amtgard": "amtgard", "staringcontests": "silmadeturniirid", "spiele": "m<PERSON><PERSON>i", "giochi": "m<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphonemängud", "boogames": "<PERSON><PERSON><PERSON><PERSON>", "cranegame": "kra<PERSON><PERSON>äng", "hideandseek": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "kivike<PERSON>", "arcadegames": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klassikamäng", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON>", "galagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "romantikamäng", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xmängud", "gamefi": "mängufü", "jeuxdarcades": "tuli<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "mängud90", "idareyou": "<PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "võidusõid<PERSON>ängud", "ets2": "ets2", "realvsfake": "reaalvsvale", "playgames": "mängimemänge", "gameonline": "mänguonline", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "mängudonline", "writtenroleplay": "kirjutamisrollimäng", "playaballgame": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "piktogrammid", "coopgames": "koostöömängud", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON><PERSON>", "highscore": "kõrgepunktid", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgerimä<PERSON>ud", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwmustmustredition", "jeuconcour": "mänguv<PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "küsimusmäng", "gioco": "mäng", "managementgame": "juhtimismäng", "hiddenobjectgame": "peidetudobjektimäng", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1mäng", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "arcade<PERSON>ä<PERSON><PERSON>", "memorygames": "mängumälu", "vulkan": "vulkan", "actiongames": "actionmängud", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "pist<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON>", "couchcoop": "diivanikoostöö", "perguntados": "küsitud", "gameo": "mä<PERSON>", "lasergame": "lasermäng", "imessagegames": "imessagemängud", "idlegames": "m<PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "täidaauk", "jeuxpc": "mängudpc", "rétrogaming": "retrogaming", "logicgames": "loog<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "jaapangame", "rizzupgame": "rizzupmäng", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "ku<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "väljumismängud", "5vs5": "5vs5", "rolgame": "r<PERSON><PERSON>ä<PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "traditsioonilisedmängud", "kniffel": "kniffel", "gamefps": "mängufps", "textbasedgames": "tekstilpõhinevadmängud", "gryparagrafowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrospel": "retroseiklused", "thiefgame": "varast<PERSON>äng", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfussball", "spieleabende": "mäng<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "mängufoorum", "casualgames": "lõbusadmängud", "fléchettes": "noolteviskamine", "escapegames": "põgene<PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "varasgameseeria", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "mäng", "bordfodbold": "bordfodbold", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "automängud", "onlineplay": "onlinemängud", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "mäng<PERSON><PERSON><PERSON><PERSON>", "pursebingos": "rahakotibingos", "randomizer": "juh<PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "mängudpc", "socialdeductiongames": "sotsiaalsedpettus<PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "isomeetrilisedmängud", "goodoldgames": "headsedamängud", "truthanddare": "tõdejajulge", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "mängudvirtuaalses", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "vaba2mängida", "fantasygame": "fantaasiamäng", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "mängudokohale", "halotvseriesandgames": "halotvseriaalidjakulad", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "kõikmillelonmootor", "everywheregame": "igaljääkmäng", "swordandsorcery": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "heamängandmine", "jugamos": "mängime", "lab8games": "lab8mängud", "labzerogames": "labzeromängud", "grykomputerowe": "mäng<PERSON><PERSON>olid", "virgogami": "virgogami", "gogame": "go<PERSON>na", "jeuxderythmes": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "minat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "rõduläbivedrustüüp4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON>", "gamemodding": "mä<PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "dobbelspellen", "spelletjes": "m<PERSON><PERSON><PERSON>", "spacenerf": "spacenärf", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "üksikamäng", "coopgame": "koosmäng", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "p<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON>äng", "onitama": "onitama", "pandemiclegacy": "pandeemiajäätmed", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolymäng", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON>ä<PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombiscide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "verevaal", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "<PERSON><PERSON><PERSON><PERSON>", "heroquest": "kange<PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "giocid<PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "kujulood", "yatzy": "jat<PERSON>", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON>ä<PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "kõvavõitlus", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmilinekohtumine", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "<PERSON><PERSON><PERSON>ä<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "vahetamänge", "infinitythegame": "igavesusetmäng", "kingdomdeath": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "jahtsi", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "<PERSON><PERSON><PERSON><PERSON>", "applestoapples": "õunadõunadega", "jeudesociété": "mängup<PERSON><PERSON>", "gameboard": "m<PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "hämaraimpeerium", "horseopoly": "hobusteopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "hullumeelsusemõisad", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "brimstoneivarjud", "kingoftokyo": "tokyokuningas", "warcaby": "warkabid", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "laevahävitaja", "tickettoride": "piletiteekonnale", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "šahh", "jeuxsociete": "mängudesotsiaal", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON>ä<PERSON><PERSON>", "starwarslegion": "tähesõduregioon", "gochess": "mä<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON>ä<PERSON><PERSON>", "terraria": "terriaaria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "<PERSON>rk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "identiteetv", "theisle": "sa<PERSON><PERSON>", "thelastofus": "vii<PERSON><PERSON>eist", "nomanssky": "meesninatähtedele", "subnautica": "subnautica", "tombraider": "ha<PERSON>jandja", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eht", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "ökoloobujad", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "foobia", "witchit": "nõiditseda", "pathologic": "patoloogiline", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "lagunemiseolek2", "vrising": "vrising", "madfather": "k<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "ärähäälte", "eternalreturn": "igalepöördumine", "pathoftitans": "titaanidetee", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "kurju<PERSON><PERSON>", "realrac": "realtihk", "thebackrooms": "tagakambrites", "backrooms": "tagatoad", "empiressmp": "empiresmp", "blockstory": "plokknurgad", "thequarry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadmäng", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "imp<PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "ellujäämismäng", "vintagestory": "vintagelugu", "arksurvival": "arkeliitumine", "barotrauma": "barotrauma", "breathedge": "hingetõmme", "alisa": "alisa", "westlendsurvival": "läänelainetegiselvumine", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkwood": "tumemetss", "survivalhorror": "ellujääminehorror", "residentevil": "elukohafı<PERSON>rী<PERSON>", "residentevil2": "elupahad2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "elupärastmängu", "survivalgames": "ellujää<PERSON><PERSON><PERSON><PERSON>", "sillenthill": "vaikseltmägedes", "thiswarofmine": "seeõ<PERSON>on<PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "rohelineprojekt", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON>", "raft": "raft", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "<PERSON><PERSON><PERSON>", "littlenightmares2": "väikesedhirmud2", "signalis": "signalis", "amandatheadventurer": "amatöörseiklus", "sonsoftheforest": "met<PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rustmäng", "outlasttrials": "ü<PERSON>lamistest<PERSON>", "alienisolation": "alienisolatsioon", "undawn": "undawn", "7day2die": "7päeva2surma", "sunlesssea": "päikesevabameri", "sopravvivenza": "ellujäämine", "propnight": "<PERSON><PERSON><PERSON><PERSON>", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "surmaversum", "cataclysmdarkdays": "katak<PERSON><PERSON><PERSON><PERSON><PERSON>d", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "elupeale", "ageofdarkness": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "kellatorn3", "aloneinthedark": "üksiämahä<PERSON>s", "medievaldynasty": "keskaegnedünastia", "projectnimbusgame": "projektinimbuspäng", "eternights": "igavikunood", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rake<PERSON><PERSON><PERSON>", "tft": "tft", "officioassassinorum": "ametihävitajad", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "kääbikutehävitaja", "warhammer40kcrush": "warhammer<PERSON><PERSON><PERSON><PERSON>", "wh40": "wh40", "warhammer40klove": "warhammer<PERSON><PERSON><PERSON><PERSON>", "warhammer40klore": "warhammer40kloor", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kminukoi", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "armastansororitas", "ilovevindicare": "maarmastanvindicare", "iloveassasinorum": "maarmastanassassinorum", "templovenenum": "templovenenum", "templocallidus": "temlocallidus", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "mõrvariametid", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "aegadeimpeeriumite", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeragemagsigma", "civilizationv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "kaksitakest", "wingspan": "wingspan", "terraformingmars": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "võimekusejõududejavõlu", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "mütoloogiaajastu", "args": "args", "rime": "rime", "planetzoo": "planeetloomaaed", "outpost2": "väljak2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "tsivilisatsioon6", "warcraft2": "warcraft2", "commandandconquer": "käskigejavallutage", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON>", "strategygames": "strate<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "tsivilisat<PERSON><PERSON><PERSON>äng", "civilization4": "tsivilisatsioon4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "kogusõda", "travian": "travian", "forts": "forts", "goodcompany": "headselts", "civ": "tsivi", "homeworld": "kodumaailm", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "kiiremkuistvalgust", "forthekings": "kuningateheaks", "realtimestrategy": "reaalajastrateegia", "starctaft": "starctaft", "sidmeierscivilization": "sid<PERSON>erssivilisatsioon", "kingdomtwocrowns": "kuningriikkakskrooni", "eu4": "eu4", "vainglory": "p<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesfunalkoolialgebra", "plagueinc": "pla<PERSON><PERSON><PERSON>", "theorycraft": "teooriakunst", "mesbg": "mesbg", "civilization3": "tsivilisatsioon3", "4inarow": "4<PERSON><PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "ristisõdijakuninga3", "heroes3": "kangelased3", "advancewars": "advancewars", "ageofempires2": "impeeriateaeg2", "disciples2": "õpilased2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "giochidistrategia", "stratejioyunları": "strate<PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON>", "dinosaurking": "dinosauruskuninga<PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "raudsüdamet4", "companyofheroes": "kange<PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "impeer<PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "foobiad", "phobiesgame": "foobiadmäng", "gamingclashroyale": "mängudclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "välismaailm", "turnbased": "vaikesekäiguga", "bomberman": "pommit<PERSON>", "ageofempires4": "ageofempires4", "civilization5": "tsivilisatsioon5", "victoria2": "victoria2", "crusaderkings": "ristisõdi<PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "loitsukunst", "starwarsempireatwar": "tähesõdadekeevõitlus", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strateegia", "popfulmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "meisterduell", "dysonsphereprogram": "dysonsfääriprogramm", "transporttycoon": "transporttükk", "unrailed": "raildeeritud", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "plaaniseebehapnik", "uplandkingdoms": "uplandkuningriigid", "galaxylife": "<PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wäänelinn", "slaythespire": "slaythespire", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "kiirusenälg", "needforspeedcarbon": "kiirusvajabkarbongi", "realracing3": "tõelinevõidusõit3", "trackmania": "trackmania", "grandtourismo": "suurepäraneautotuur", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "kaotames4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "surmagaüleöö", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "tumedahobuseantoloogia", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "viisöödöödfreddys", "saiko": "saiko", "fatalframe": "surmara<PERSON>", "littlenightmares": "väikasedöövused", "deadrising": "<PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "k<PERSON>olek", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "pisitütarõnnetahtmine", "projectzero": "proje<PERSON><PERSON><PERSON>", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "tervetuttav", "helloneighbor2": "tere_naaber2", "gamingdbd": "mänguddbd", "thecatlady": "ka<PERSON><PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kaardidvastuinimlikkusevastu", "cribbage": "kribijä", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "k<PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "võtmehaardumine", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netijooksja", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kauplemiskaardid", "pokemoncards": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "lihaskondverkaardid", "sportscards": "spor<PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "kaardilahingvanguard", "duellinks": "duellinks", "spades": "kaabakad", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "südamekuningas", "truco": "truco", "loteria": "lotto", "hanafuda": "hana<PERSON>da", "theresistance": "vastupanuliik<PERSON>ne", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON><PERSON><PERSON>ll", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "tumedamagi", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandör", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgkohtunik", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconkomandör", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "lahinguhingejärelugu", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "<PERSON><PERSON><PERSON><PERSON>", "cardfight": "kaardivõitlus", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "ebastabiilsedüunikornid", "cyberse": "kyberse", "classicarcadegames": "klassikaarcademängud", "osu": "o<PERSON>u", "gitadora": "gitadora", "dancegames": "tan<PERSON><PERSON>ä<PERSON><PERSON>", "fridaynightfunkin": "reedeöölõbutsemine", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektim<PERSON>i", "projectdiva": "projektidiiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "klonikangel", "justdance": "l<PERSON><PERSON>al<PERSON>ntsi", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockdeadi", "chunithm": "chunithm", "idolmaster": "idolimeister", "dancecentral": "tan<PERSON><PERSON><PERSON>", "rhythmgamer": "rütmimängija", "stepmania": "stepmania", "highscorerythmgames": "kõrgepunkthärrythmimängud", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "tantsuletkniestjajääst", "auditiononline": "auditiononline", "itgmania": "itgmaania", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "rütmidoktor", "cubing": "kuubistamine", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "spota", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "loog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "ristisõnad", "motscroisés": "motscroisés", "krzyżówki": "ristisõnad", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "m<PERSON><PERSON><PERSON>", "riddle": "puzzle", "riddles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "puzzle", "tekateki": "tekateki", "inside": "sees", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "põgenemis模拟ator", "minesweeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzzlejakedraakonid", "crosswordpuzzles": "ristisõnad", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "põgenemistoamängud", "escapegame": "põgenemismäng", "3dpuzzle": "3dmosaiik", "homescapesgame": "koduseiklusedmäng", "wordsearch": "sõnadeotsing", "enigmistica": "enigmistika", "kulaworld": "kula<PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "riddletales", "fishdom": "kalam<PERSON><PERSON>m", "theimpossiblequiz": "võimatuquiz", "candycrush": "kondiitrihäving", "littlebigplanet": "väikehiiglamaailm", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "veider", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "kuubikrubik", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "k<PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "ärataxittööle", "tycoongames": "tykoongamess", "cubosderubik": "rubik<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "ristisõnad", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "rätseleid", "buscaminas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "mõistatuselahendamine", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "keegi", "guessing": "arvamine", "nonograms": "nonogrammid", "kostkirubika": "kostkirubika", "crypticcrosswords": "krüptilisedristsõnad", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "peamurdmised", "poptropica": "poptropica", "thelastcampfire": "viimasedlõkkeõhtud", "autodefinidos": "autoemotsioonid", "picopark": "picopark", "wandersong": "rändusaastal", "carto": "kaart", "untitledgoosegame": "pealkirjatahanemäng", "cassetête": "kassetid", "limbo": "limbo", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rub<PERSON><PERSON><PERSON><PERSON>", "speedcube": "kiiruskuubik", "pieces": "tükid", "portalgame": "portaalimäng", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "pildi<PERSON><PERSON><PERSON><PERSON>", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kuubikmagic", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "väärastunudimeemaailm", "monopoly": "monopol", "futurefight": "tulevikuvõitlus", "mobilelegends": "mobilelegendid", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "üksikhunt", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "koosstaared", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "alchemystars", "stateofsurvival": "ellujäämistase", "mycity": "<PERSON><PERSON>n", "arknights": "arknights", "colorfulstage": "värvikastseen", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hyperfront": "hüperfront", "knightrun": "ritterjooks", "fireemblemheroes": "tulemärgikangelesed", "honkaiimpact": "honkaiimpact", "soccerbattle": "j<PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "telefonimängud", "kingschoice": "kuningavalik", "guardiantales": "kaitsetlood", "petrolhead": "bensiinipeade", "tacticool": "taktikahästi", "cookierun": "k<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "väljasilmast", "craftsman": "meist<PERSON><PERSON>", "supersus": "ülihelde", "slowdrive": "aeglanej<PERSON>ud<PERSON>", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "freefire", "mobilegaming": "mobiilimängud", "lilysgarden": "lily<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "tiimivõitlusstrateegiad", "clashofclans": "klaanidekokkulepe", "pjsekai": "pjsekai", "mysticmessenger": "müstilinesõnumitooja", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8pallibassein", "emergencyhq": "hädaolukordhq", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangunhavingfun", "clashofclan": "klanidevõitlus", "starstableonline": "tähtehaavalonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "aeg<PERSON>ess", "beatstar": "beatstar", "dragonmanialegend": "draakonimeeslegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "kriminaal<PERSON><PERSON><PERSON>", "summonerswar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON>israev", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ingliteliiga", "lordsmobile": "loridismobile", "tinybirdgarden": "väikepingviinibotaanika", "gachalife": "gachalife", "neuralcloud": "neuraalpilv", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "sininearhiiv", "raidshadowlegends": "raidshadowlegends", "warrobots": "sõjakotkad", "mirrorverse": "peegeluniversum", "pou": "pou", "warwings": "sõjasiivad", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>kamp<PERSON>", "gameofsultans": "sultanitemäng", "arenabreakout": "arenalähevälja", "wolfy": "hunt<PERSON>", "runcitygame": "runcitygame", "juegodemovil": "mängudmobiles", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "mustakõrbemobiilne", "rollercoastertycoon": "raketipark", "grandchase": "<PERSON><PERSON><PERSON><PERSON>", "bombmebrasil": "pommiemnabrasiil", "ldoe": "ldoe", "legendonline": "legendonlines", "otomegame": "otomeg<PERSON>", "mindustry": "meelesektori", "callofdragons": "lohekuningas", "shiningnikki": "sädelevnik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "varjunõitlus3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitsioonidemonstratsioon3", "wordswithfriends2": "sõnadõpradega2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "täiusliklugu", "showbyrock": "näitamekivi", "ladypopular": "ladypopular", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "korista<PERSON><PERSON><PERSON>", "perfectworldmobile": "täiuslikmaailmmobiil", "empiresandpuzzles": "impeeridjaenigu<PERSON>", "empirespuzzles": "empiremõistatused", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "gartictelefon", "battlegroundmobileind": "battlegroundmobileind", "fanny": "nunnu", "littlenightmare": "väikesedööd", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "igaves<PERSON>ls", "gunbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "mängiminemlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "<PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobiilimüütbangbang", "gachaclub": "gachaklubi", "v4": "v4", "cookingmama": "kokakannad", "cabalmobile": "cabalmobiil", "streetfighterduel": "tänavavõitlejaduell", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "mängiminebgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassi<PERSON><PERSON><PERSON><PERSON>", "soulseeker": "hingesoolane", "gettingoverit": "ülesaad", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "kuuajalugu", "carxdriftracingonline": "autodriftivõistlusinternetis", "jogosmobile": "mängudmobitel", "legendofneverland": "igavestiisamaa", "pubglite": "pubglite", "gamemobilelegends": "mängumobillegendid", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "mängudtelefonil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "otsing", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "pimeduseilm", "travellerttrpg": "reisijatettrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "romantikaklubi", "d20": "d20", "pokemongames": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemoniomamysteeriume", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonk<PERSON><PERSON>", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>e", "pokemongo": "pokemongo", "pokemonshowdown": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "entai": "entai", "hypno": "<PERSON><PERSON><PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "poke<PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "tii<PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "taskukotikoletised", "nuzlocke": "nuzlocke", "pokemonplush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "tiimystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemoonromihäkk", "pubgmobile": "pubgmobile", "litten": "<PERSON><PERSON><PERSON>", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psydaki", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmeistrik챔피언", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "lapsed<PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "säravjahtija", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xadrez": "malet", "scacchi": "<PERSON><PERSON><PERSON><PERSON>", "schaken": "<PERSON><PERSON><PERSON>", "skak": "skak", "ajedres": "<PERSON><PERSON><PERSON><PERSON>", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudécheks", "japanesechess": "jaapanitaktika", "chinesechess": "<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "malet<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "a<PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "<PERSON><PERSON><PERSON><PERSON>", "calabozosydragones": "ka<PERSON>jasebudeja<PERSON>tagede", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "lossejakäbid", "oxventure": "oxventure", "darksun": "pimedapäike", "thelegendofvoxmachina": "voximachinalegend", "doungenoanddragons": "mängimejaala<PERSON>", "darkmoor": "<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftimeistrivõistlus", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "unistustesmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftimoodud", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "lisad", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "taevalaht", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moditudminecrafter", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "v<PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftikindlused", "minecraftcity": "minecraftilinn", "pcgamer": "pcmängija", "jeuxvideo": "m<PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "mängijad", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermobiil", "gameover": "mänglõpp", "gg": "gg", "pcgaming": "pcmäng<PERSON><PERSON>", "gamen": "gamen", "oyunoynamak": "mängime", "pcgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgaming": "vabasajamängimine", "gamingsetup": "mängusisustus", "pcmasterrace": "pcmeistrivõistlus", "pcgame": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "vrmängimine", "drdisrespect": "drdisrespect", "4kgaming": "4<PERSON><PERSON>", "gamerbr": "gamerbr", "gameplays": "m<PERSON><PERSON><PERSON>", "consoleplayer": "konsolimängija", "boxi": "boxi", "pro": "pro", "epicgamers": "epicmängijad", "onlinegaming": "onlinegaming", "semigamer": "pool<PERSON>äng<PERSON>", "gamergirls": "gamertütred", "gamermoms": "gamermammad", "gamerguy": "mängurtüüp", "gamewatcher": "mänguvalvur", "gameur": "m<PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamersõbrad", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "tii<PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "missioonid", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "kod<PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dsswitch", "competitivegaming": "konkurentsimängud", "minecraftnewjersey": "minecraftuusjärsey", "faker": "vale", "pc4gamers": "pc4mängijad", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "heteroseksuaalnehäkkimine", "gamepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "tüdrukumängija", "fnfmods": "fnfmodid", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "mäng<PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "mänguritädid", "gamesetup": "mä<PERSON><PERSON><PERSON><PERSON>", "overpowered": "ületav", "socialgamer": "sotsiaalnemängur", "gamejam": "mängumaraton", "proplayer": "proplayer", "roleplayer": "rollimängija", "myteam": "meena<PERSON><PERSON>", "republicofgamers": "mängijatevabariik", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "kolmekordnelegend", "gamerbuddies": "mängusõbrad", "butuhcewekgamers": "agaöeldakuratgamerid", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "mängurilja", "nerdgamer": "ne<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "ägedamängija", "89squad": "89seltskond", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "mängudevaatamine", "gamertag": "mängijanimi", "lanparty": "<PERSON><PERSON><PERSON>", "videogamer": "videomängija", "wspólnegranie": "ühisemängimine", "mortdog": "mort<PERSON><PERSON>", "playstationgamer": "playstationimängija", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "tervislikmängur", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "wanaismängija", "obviouslyimagamer": "ilmseltolemängur", "mario": "mario", "papermario": "p<PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "korjaja", "humanfallflat": "inimesevabaltmaha", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nullpõgenemine", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuusika", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "vaheta", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "zeldaajalugu", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "võitjadvindikaator", "ssbm": "ssbm", "skychildrenofthelight": "taeval<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "ahah<PERSON>k", "tearsofthekingdom": "kuningapisarad", "walkingsimulators": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "nintendomängud", "thelegendofzelda": "zeldalegend", "dragonquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON>", "mariobros": "mariovennad", "runefactory": "<PERSON><PERSON><PERSON>", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "muusõberpedro", "legendsofzelda": "zeldalegendid", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51mängu", "earthbound": "<PERSON><PERSON><PERSON><PERSON>", "tales": "lood", "raymanlegends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "luigismansion": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "loomadeületamine", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "kolmnurgastrateegia", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kasteki<PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "uus3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstarid", "marioandsonic": "mariojas<PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "juumina", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "punas<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wild<PERSON>ee", "lolph": "lolph", "leagueoflegend": "legendideliit", "tốcchiến": "võitlus", "gragas": "gragas", "leagueoflegendswild": "legendideliigametsik", "adcarry": "adcarry", "lolzinho": "lolzike", "leagueoflegendsespaña": "leagueoflegendshispaña", "aatrox": "aatrox", "euw": "miks", "leagueoflegendseuw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "legendidegaü<PERSON>atud", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "zedpeamine", "hexgates": "hexväravad", "hextech": "hextech", "fortnitegame": "fortnitemäng", "gamingfortnite": "mänginfortnite", "fortnitebr": "<PERSON><PERSON><PERSON>e", "retrovideogames": "retrovideomängud", "scaryvideogames": "hirmu<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "mänguarendaja", "megamanzero": "megamanzero", "videogame": "videomäng", "videosgame": "videomäng<PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "ülevaade", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "võlur101", "battleblocktheater": "battleblockteater", "arcades": "arcadeid", "acnh": "acnh", "puffpals": "puff<PERSON><PERSON><PERSON>d", "farmingsimulator": "talusimulaator", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sanboxmängud", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamscape": "unistusteilm", "starcitizen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderesimulator": "yanderesimulator", "grandtheftauto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "sur<PERSON><PERSON><PERSON>", "amordoce": "armastust<PERSON><PERSON>", "videogiochi": "videomäng<PERSON>", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON>", "videospiele": "videomäng<PERSON>", "touhouproject": "touhouprojekt", "dreamcast": "unistustevisioon", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retromängud", "retroarcade": "retroarcade", "vintagecomputing": "vintagearvutamine", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "mä<PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "komandöörkeen", "bugsnax": "bugsnax", "injustice2": "ebaõiglus2", "shadowthehedgehog": "var<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "<PERSON>evamäng", "zenlife": "zeneluo", "beatmaniaiidx": "beatmaniaiidx", "steep": "kuid<PERSON>", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "medieval", "consolegaming": "konsolimängud", "konsolen": "konsolen", "outrun": "ületama", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "mänguh<PERSON><PERSON><PERSON>", "monstergirlquest": "monstertüdrukuteots", "supergiant": "superhiiglaslik", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "farmingsims", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxmängud", "interactivefiction": "interaktiivnefiktsioon", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "meieviimasedmehed2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON>", "visualnovel": "vis<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "visua<PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "ka<PERSON>in", "twilightprincess": "hämaraeelnepriincess", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "liivakast", "aestheticgames": "esteetilised<PERSON><PERSON><PERSON><PERSON>", "novelavisual": "<PERSON><PERSON><PERSON><PERSON>", "thecrew2": "meeskond2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retromängud", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "le<PERSON><PERSON><PERSON>jar<PERSON>lut<PERSON>on", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyblade": "võtmeodoober", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafs<PERSON><PERSON><PERSON>", "novelasvisuales": "visua<PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videomäng<PERSON>", "videogamedates": "videomängudate", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulk<PERSON>ä<PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "maniac<PERSON><PERSON>", "crashracing": "crashracing", "3dplatformers": "3dplatvormimängud", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON>ä<PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "lug<PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "ülekahehinged", "gameuse": "mäng<PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "väikejänku", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "kiirevälk", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcaded", "f123": "f123", "wasteland": "wasteland", "powerwashsim": "powerwashsim", "coralisland": "k<PERSON><PERSON>aar<PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxviljad", "anotherworld": "teiselemaailmal<PERSON>", "metaquest": "metaotsing", "animewarrios2": "animekangelased2", "footballfusion": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "<PERSON><PERSON><PERSON><PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "keerdmetall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulaator", "symulatory": "simulaatorid", "speedrunner": "kiirkorjaja", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "imedeulkond온라인", "skylander": "taevastrider", "boyfrienddungeon": "poisssõbrakäik", "toontownrewritten": "toontowniüleskirjutamine", "simracing": "simracing", "simrace": "simvõidusõit", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "taevasedke<PERSON>", "seum": "seum", "partyvideogames": "piduvideomä<PERSON>ud", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "kosmoselennusimulaator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "toitjavideomängud", "oyunvideoları": "mänguvideod", "thewolfamongus": "huntidehulkadehulgas", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "kätetöömäng", "leyendasyvideojuegos": "mängulegendid", "oldschoolvideogames": "vanaškoorvideomängud", "racingsimulator": "võidusõidsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "mässajateagendid", "songpop": "laulupopp", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterjahtijadnüüd", "rebelstar": "mässajadtäht", "indievideogaming": "indievideomängud", "indiegaming": "indiegaming", "indievideogames": "indievideomängud", "indievideogame": "indievideomäng", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "buff<PERSON><PERSON>", "unbeatable": "ületamatu", "projectl": "projektl", "futureclubgames": "tulevikuklubimängud", "mugman": "mugimees", "insomniacgames": "unetuksimääratudmängud", "supergiantgames": "supergiant<PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestemäng", "aperturescience": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "tagasivaatamine", "gamebacklog": "mängudejärjekord", "gamingbacklog": "mängudejärjekord", "personnagejeuxvidéos": "mä<PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "saav<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "linn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "superahvikuipp", "deponia": "deponia", "naughtydog": "kutsuõelane", "beastlord": "beastlord", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandpimedamets", "alanwake": "alanwake", "stanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "<PERSON>m<PERSON>ng<PERSON><PERSON>", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "armastankofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "bersek", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "esimened", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "kurblaneanime", "darkerthanblack": "tumedamkuimust", "animescaling": "<PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animekoosideega", "pesci": "kalad", "retroanime": "retroanime", "animes": "animed", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON>aan<PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON>", "animecover": "animekaas", "thevisionofescaflowne": "escaflownevisioon", "slayers": "tapperid", "tokyomajin": "tokyomajin", "anime90s": "anime90ndad", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "tualetiso<PERSON>lung<PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON><PERSON>", "futurediary": "tulevikupäevik", "fairytail": "muin<PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "tehtudügust", "parasyte": "parasiit", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "kõhedamanga", "romancemangas": "romantikamangad", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonmaia", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bongostraykoerad", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoughul", "onepunchman": "ükslöökmees", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "krõbevoog", "aot": "aot", "sk8theinfinity": "sk8lõpmatus", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spiooniperekond", "rezero": "rezero", "swordartonline": "mõõgakunstvõrgus", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioriteet", "angelsofdeath": "suredeadangelid", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hüpnosümptoom", "goldenkamuy": "kuldnekamuy", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "spordian<PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "ing<PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaianime": "iseanime", "sagaoftanyatheevil": "sagaoftanyaitheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "ilusravim", "theboyandthebeast": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "mustpüha", "towerofgod": "jumalatempel", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "t<PERSON><PERSON><PERSON><PERSON><PERSON>osaagashi<PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "lõbusjaõudne", "martialpeak": "võitluspunkt", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "kõrgepunktetüdruk", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "ka<PERSON><PERSON>u", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "minuga", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korraga", "vanny": "<PERSON><PERSON>", "vegeta": "vegeta", "goromi": "go<PERSON><PERSON>", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "meremehedsaturn", "dio": "dio", "sailorpluto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON>", "chainsawman": "kett<PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "ka<PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangavaba", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakurakaardivõtja", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "lubatudigamaa", "monstermanga": "monstermanga", "yourlieinapril": "sinu<PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggykloun", "bokunohero": "<PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "sügavmerevan<PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannakalad", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "kaardikütidsakura", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "siniaeg", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "kustutatud", "bluelock": "bluelock", "goblinslayer": "kobold<PERSON>or", "detectiveconan": "dete<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampiiritond", "mugi": "mugi", "blueexorcist": "sinineeksortsist", "slamdunk": "slamdukk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "p<PERSON><PERSON><PERSON>", "spyfamily": "spiooniperiood", "airgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "needseitsmeohtlikkuvõtet", "prisonschool": "vangikool", "thegodofhighschool": "kõrgkoolijumal", "kissxsis": "suudleksõde", "grandblue": "suursinine", "mydressupdarling": "minuelukostüümisõber", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniversum", "swordartonlineabridge": "mõõgakunstonlineutral", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "romantilinemangaportaal", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animearmastus", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentiina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonilööjaokstootsavale", "bloodlad": "verd<PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "tulelöök", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "tähedreatsumine", "romanceanime": "roman<PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "kirsivõlu", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "salvestaragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "kõrgkoolidevaalpaat", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tennis<PERSON><PERSON>ts", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "mõrvaklass", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "surmatants", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "jaa<PERSON>ian<PERSON>", "animespace": "animeala", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "lootusnaine", "animedub": "animesünk", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "<PERSON><PERSON><PERSON>", "ratman": "rottimees", "haremanime": "jänesemeedia", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "zodiaagikavalrid", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinlolliklubi", "dragonquestdai": "lohetugedai", "heartofmanga": "mangaüdamik", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON>he<PERSON>emoe", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "õpetusiutliigaraske", "overgeared": "ülevarustatud", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "nõidk<PERSON><PERSON><PERSON><PERSON>jee", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "jumalatepiiskad", "loscaballerosdelzodia": "zodiar<PERSON><PERSON><PERSON><PERSON>", "animeshojo": "animeshojo", "reverseharem": "tagasiharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "suurepäevaneõpetajaonizuka", "gridman": "võrgumees", "kokorone": "koko<PERSON>", "soldato": "s<PERSON><PERSON><PERSON>", "mybossdaddy": "minuülemisissid", "gear5": "varustus5", "grandbluedreaming": "suurehalluneelumine", "bloodplus": "verepreemium", "bloodplusanime": "vereplussanime", "bloodcanime": "veresaade", "bloodc": "veric", "talesofdemonsandgods": "demonite<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "animetüdrukud", "sharingan": "jagamine", "crowsxworst": "konstruktsioonikõigehalvem", "splatteranime": "splatternime", "splatter": "tilgutamine", "risingoftheshieldhero": "kilbikangelasek<PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "<PERSON><PERSON><PERSON>", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON>", "liarliar": "valevale", "supercampeones": "supercampeones", "animeidols": "animeidolid", "isekaiwasmartphone": "isekaioliägedagaäpid", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "ööhääl", "bakuganbrawler": "bakuganjä<PERSON><PERSON>", "bakuganbrawlers": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "leiamanga", "princessjellyfish": "printsessigeelika", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradi<PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeuniversum", "persocoms": "persokoomid", "omniscientreadersview": "omniscientreadersiudad", "animecat": "animekass", "animerecommendations": "animesoovitused", "openinganime": "a<PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minunoorteromantilinekomöödia", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamid", "voltesv": "voltesv", "giantrobots": "hiig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobiilivõitlejaggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobiilsklassigundam", "mech": "mech", "eurekaseven": "eurekaseitsme", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "valgendaja", "deathnote": "surmavale", "cowboybebop": "kauboibebop", "jjba": "jjba", "jojosbizarreadventure": "jojosveidratulemine", "fullmetalalchemist": "täismetallivõlur", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "sõjaväeanime", "greenranger": "rohelinelöök", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "tähtrebane", "ultraman": "ultramees", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeküla", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "<PERSON><PERSON><PERSON>", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "keskkooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "minageeroakadeemia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonilööja", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "rünnakupealhiiud", "erenyeager": "er<PERSON><PERSON><PERSON>", "myheroacademia": "minuheldusakadeemia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "to<PERSON>da<PERSON><PERSON>äng", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "küsitluskomando", "onepieceanime": "onepieceanime", "attaquedestitans": "ründurkanitäni", "theonepieceisreal": "ükstükikondonreal", "revengers": "kättemaksjad", "mobpsycho": "mobipsühho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "rõõmupoissmõju", "digimonstory": "digimonlugu", "digimontamers": "digimontamers", "superjail": "supervangla", "metalocalypse": "metallakadu", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "jook<PERSON>", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "täiuslikwebtoon", "kemonofriends": "kemonosõbrad", "utanoprincesama": "utanoprincesama", "animecom": "animekommunity", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "lihtsaltnii", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "kõikpühadetänav", "recuentosdelavida": "elulood"}