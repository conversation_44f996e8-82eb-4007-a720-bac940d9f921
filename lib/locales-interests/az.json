{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologiya", "cognitivefunctions": "koqnitivfunksiyalar", "psychology": "psixologiya", "philosophy": "fəlsəfə", "history": "tarix", "physics": "fizika", "science": "elm", "culture": "mədəniyyət", "languages": "dillər", "technology": "texnologiya", "memes": "memlər", "mbtimemes": "mbtime<PERSON>lər", "astrologymemes": "astrolojimemlər", "enneagrammemes": "ennegrammemlər", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "mə<PERSON>ə<PERSON>", "videos": "videolar", "gadgets": "qad<PERSON><PERSON>ər", "politics": "<PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "münasibətməsl<PERSON>həti", "lifeadvice": "həyat<PERSON>ə<PERSON>l<PERSON>həti", "crypto": "kripto", "news": "xəbərlər", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "arxeologi<PERSON>", "learning": "öyrənmək", "debates": "debatlar", "conspiracytheories": "konspirasiyanəzəriyy<PERSON><PERSON><PERSON>ri", "universe": "kainat", "meditation": "meditasiya", "mythology": "mifologiya", "art": "incəsənət", "crafts": "sənətkarlıq", "dance": "rəqs", "design": "<PERSON><PERSON><PERSON>", "makeup": "ma<PERSON><PERSON><PERSON>", "beauty": "g<PERSON><PERSON><PERSON><PERSON>k", "fashion": "moda", "singing": "oxumaq", "writing": "yazı", "photography": "fotoqrafiya", "cosplay": "kosplay", "painting": "rəsm", "drawing": "rəsm", "books": "kit<PERSON><PERSON>", "movies": "<PERSON>l<PERSON>r", "poetry": "poeziya", "television": "televiziya", "filmmaking": "filmçəkmək", "animation": "animasiya", "anime": "anime", "scifi": "scifi", "fantasy": "fantastika", "documentaries": "sənədlifilmlər", "mystery": "sirr", "comedy": "komediya", "crime": "cinayət", "drama": "dram", "bollywood": "boll<PERSON>ud", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "də<PERSON><PERSON>ət", "romance": "romantika", "realitytv": "realititv", "action": "hərəkət", "music": "musiqi", "blues": "bluz", "classical": "klassik", "country": "kontri", "desi": "desi", "edm": "edm", "electronic": "elektron", "folk": "xalqmahnıları", "funk": "funkmusiqisu", "hiphop": "hiphop", "house": "ev", "indie": "indi", "jazz": "caz", "kpop": "kpop", "latin": "latın", "metal": "metal", "pop": "pop", "punk": "pank", "rnb": "rnb", "rap": "rep", "reggae": "reggi", "rock": "rok", "techno": "texno", "travel": "səyahət", "concerts": "konsertlər", "festivals": "festivallar", "museums": "m<PERSON><PERSON><PERSON><PERSON>", "standup": "standap", "theater": "teatr", "outdoors": "açıqhavada", "gardening": "ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partying": "şənliktəşkiletmək", "gaming": "oyun", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "zindanlarv<PERSON><PERSON><PERSON>alar", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnayt", "leagueoflegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraft": "maynk<PERSON>", "pokemon": "pokemon", "food": "qida", "baking": "çörəkçilik", "cooking": "yeməkbişirmək", "vegetarian": "vegetarian", "vegan": "веган", "birds": "qu<PERSON><PERSON>", "cats": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dogs": "itlər", "fish": "balıq", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "qaralarınhəyatıməsələsi", "environmentalism": "ətrafmühitinmü<PERSON>", "feminism": "feminizm", "humanrights": "insanhüquqları", "lgbtqally": "lgbt<PERSON><PERSON>", "stopasianhate": "dayand<PERSON>rma<PERSON>", "transally": "translı", "volunteering": "könüllülük", "sports": "i̇dmanlar", "badminton": "badminton", "baseball": "beysbol", "basketball": "basketbol", "boxing": "boks", "cricket": "kriket", "cycling": "velosipedsürmə", "fitness": "fitnes", "football": "futbol", "golf": "golf", "gym": "idmanzalı", "gymnastics": "gimnast<PERSON>", "hockey": "xokkey", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netbol", "pilates": "pilates", "pingpong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "running": "qaçış", "skateboarding": "skeytbordinq", "skiing": "xizə<PERSON><PERSON><PERSON>ə", "snowboarding": "snouboardasürüşmək", "surfing": "<PERSON><PERSON><PERSON><PERSON>", "swimming": "üzgüçülük", "tennis": "tenis", "volleyball": "voleybol", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoqa", "scubadiving": "akvalanqiləsualtıüzmə", "hiking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capricorn": "oğlaq", "aquarius": "dolça", "pisces": "b<PERSON><PERSON><PERSON><PERSON>", "aries": "qoç", "taurus": "b<PERSON><PERSON><PERSON>", "gemini": "əkizlər", "cancer": "xərçəng", "leo": "şir", "virgo": "qızbürcü", "libra": "tərə<PERSON>", "scorpio": "əqrəb", "sagittarius": "oxatan", "shortterm": "qısam̈üdd̈ətli", "casual": "g<PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "uzunmüddətlimünasibət", "single": "tək", "polyamory": "çoxsevənlik", "enm": "qeyrimonaqamik", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gey", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "qırmızıölümödül2", "dragonage": "dragonyaş", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "nəzarətçilər", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kralınmacarası", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiya", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "qartallar", "syberia": "siberiya", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "daşlarındogması", "sunsetoverdrive": "günəşinbatmaəhvalatı", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "budağaççalışmaları", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloiknfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "açıqdünya", "heroesofthestorm": "stormunqahramanları", "cytus": "cytus", "soulslike": "ruh<PERSON>i", "dungeoncrawling": "zindanqalxma", "jetsetradio": "jetsetradio", "tribesofmidgard": "midgardtribləri", "planescape": "planlaşdırmayolu", "lordsoftherealm2": "dünyanınlordları2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "rəng<PERSON>za", "medabots": "medabotlar", "lodsoftherealm2": "realm2dəçoxluşeylər", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "da<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l<PERSON><PERSON>", "okage": "okage", "juegoderol": "r<PERSON><PERSON><PERSON>", "witcher": "cadug<PERSON>r", "dishonored": "tə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "qaranlıqcanlar", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fə<PERSON>ət", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "yaşlıdönmələr", "modding": "modding", "charactercreation": "karakteryaradılması", "immersive": "dalmış", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyköhnəməktəb", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantaziya", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbiddəstək", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "sev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "otomeoyun<PERSON>", "stardew": "ulduzçayğı", "stardewvalley": "stardewvalley", "ocarinaoftime": "zamanınocarına", "yiikrpg": "yıxrpg", "vampirethemasquerade": "zombilərlinktəkrarla", "dimension20": "ölçü20", "gaslands": "gazdiyarları", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "yolçusu2cinəşr", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "qansaatında", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "sevgili_nikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "düzlüyürüş", "rpg": "rpg", "dota2": "dota2", "xenoblade": "ksen<PERSON><PERSON>", "oneshot": "birşansınız", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "şef", "yourturntodie": "səninçixışınıdır", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "yaşlıdövrsonlayn", "reka": "rəkə", "honkai": "honkai", "marauders": "fırıldaqçılar", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgmətn", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "ul<PERSON><PERSON><PERSON>mü<PERSON>b<PERSON><PERSON><PERSON>rik<PERSON>r", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "tənh<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "qaranlıqzindançı", "eclipsephase": "günəştutulmasıfazasısı", "disgaea": "disgaea", "outerworlds": "dünyanınkənarları", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "isaa<PERSON>ınbağlanması", "diabloimmortal": "diabloimmortal", "dynastywarriors": "din<PERSON><PERSON>q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "sümüklüqızlar", "nightcity": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "madnesscombat": "də<PERSON>likmübarizə", "jaggedalliance2": "sarımçıqbirlik2", "neverwinter": "heçvaxtqış", "road96": "yol96", "vtmb": "vtmb", "chimeraland": "çimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikl<PERSON>r", "gothamknights": "gothamknights", "forgottenrealms": "unutulmuş<PERSON>yar<PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "işığ<PERSON>nuşağı", "aq3d": "aq3d", "mogeko": "moq<PERSON>o", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonal<PERSON><PERSON>", "monsterrancher": "mon<PERSON>rfer<PERSON>", "ecopunk": "ekopank", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanversiyası", "fracturedthrones": "qırıqtaxtlar", "horizonforbiddenwest": "horizontəhlükəsizşərq", "twewy": "twewy", "shadowpunk": "kölgəpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "yaşılpartiya", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smit", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "qızılgünəş", "divinityoriginalsin": "ilahiqəbahat", "bladesinthedark": "qaranlıq<PERSON><PERSON><PERSON><PERSON>", "twilight2000": "günəşbatımı2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cibersurətliqırmızı", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "düşmüşsifariş", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "şerlişəhərlər", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "köhnəsinifrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "köhnədünyaüzüntüsü", "adventurequest": "macəraşamı", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rolputoyunları", "roleplayinggames": "rolikanoyunları", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "symphoniyah<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "errorfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "məni<PERSON><PERSON>ogum", "sacredunderworld": "sıradankənaraltındakıdünya", "chainedechoes": "zənc<PERSON>liəkslər", "darksoul": "qaraürək", "soulslikes": "ruhdoğmaları", "othercide": "dig<PERSON>rc<PERSON><PERSON><PERSON>t", "mountandblade": "dağvəqamçı", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "zamanidarğacı", "pillarsofeternity": "davamlılığınpill<PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "hissə", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "dragoon<PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octopathsərg<PERSON><PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfapokalipsdə", "aveyond": "aveyond", "littlewood": "kiçikmeşə", "childrenofmorta": "mortauşaqları", "engineheart": "<PERSON>q<PERSON>lb", "fable3": "nağıl3", "fablethelostchapter": "nağılitkinbölüm", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "ədəbdədavamlı", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "köhnə<PERSON><PERSON><PERSON><PERSON><PERSON>dirçəlişi", "finalfantasy12": "sonfantazi12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "vəhşi_dünya", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "krallıqürəyi1", "ff9": "ff9", "kingdomheart2": "krallıqürəyi2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "rpgoyunları", "kingdomhearts": "krallıqüçəkl<PERSON>ri", "kingdomheart3": "krallıqürək3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "təbə<PERSON>üm", "gloomhaven": "gloomhaven", "wildhearts": "çılgınürəklər", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "arcadiyanınsəması", "shadowhearts": "kölgəürəklər", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyqan", "breathoffire4": "odunnəfəsi4", "mother3": "ana3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "çöküşbos", "anothereden": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "rolifantaziyalar", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "yoltapandrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "qur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chronocross": "xronokross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "canavaraxtarıbdünyası", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "kölgəürəyiittifaqı", "bladesoul": "bıçaqsoul", "baldursgate3": "baldursgate3", "kingdomcome": "krallığıngəlişi", "awplanet": "awplanet", "theworldendswithyou": "dünyakiminləbitir", "dragalialost": "dragalialost", "elderscroll": "yaşlısözlük", "dyinglight2": "ölənişıq2", "finalfantasytactics": "sonfantaziataktikası", "grandia": "grandia", "darkheresy": "qaramdoktrina", "shoptitans": "şoptitanları", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "yersehirbazlığı", "blackbook": "qara<PERSON><PERSON><PERSON>", "skychildrenoflight": "göyuwşağlarınıışıq", "gryrpg": "gryrpg", "sacredgoldedition": "müqəddəsqızılversiyası", "castlecrashers": "qalaçıranlar", "gothicgame": "<PERSON><PERSON>yunu", "scarletnexus": "qırmızınexus", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "oyunrpg", "prophunt": "prophunt", "starrails": "starrailzz", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "müstəqilrpg", "pointandclick": "noktasıvəklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "b<PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON>", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7həmişək<PERSON>", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "ölümyoluculuğukanada", "palladium": "palladium", "knightjdr": "cəngavər<PERSON>dr", "monsterhunter": "monstrovuruq", "fireemblem": "qızılnəşr", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremantlıq", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "canavarsalayışıyüksəlişi", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "ruhyeyən", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktikirpg", "mahoyo": "mahoyo", "animegames": "animeoyunları", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "yeməkbazarı", "diluc": "diluc", "venti": "venti", "eternalsonata": "əbədimelodiya", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "krist<PERSON><PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "cibdəkimüdrik", "valorant": "valorant<PERSON><PERSON><PERSON>i", "valorante": "valorante", "valorantindian": "valoranthindistan", "dota": "dota", "madden": "madde", "cdl": "cdl", "efootbal": "elektrofutbol", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "xəyallarınliqası", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "efootball", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaymin", "overwatchleague": "overwatchliga", "cybersport": "cybersport", "crazyraccoon": "də<PERSON>rəşkara<PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantmüsabiqə", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "yarımh<PERSON><PERSON>t", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valve", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "əhəngyayılsısummer", "goatsimulator": "çəpkökləri模拟ator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "azadlıqplaneti", "transformice": "transformice", "justshapesandbeats": "sadə<PERSON><PERSON>şəkillərvəritmlər", "battlefield4": "battlefield4", "nightinthewoods": "gecəmeşədə", "halflife2": "yarımömür2", "hacknslash": "hacknslash", "deeprockgalactic": "dərinəsaltnöqtə", "riskofrain2": "yağışriski2", "metroidvanias": "metroidvanias", "overcooked": "çoxbişmiş", "interplanetary": "planetlərarası", "helltaker": "cəhənnəmsatan", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "ölühü<PERSON>r<PERSON>lər", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON>", "foxhole": "tülküdehlizi", "stray": "itirilmish", "battlefield": "batalyon", "battlefield1": "battleground1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "gözbüroğası", "blackdesert": "qaraçöl", "tabletopsimulator": "masaüstüsimulyator", "partyhard": "qız<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "çətinkosmosgəmisiqıran", "hades": "hades", "gunsmith": "silahustası", "okami": "<PERSON>ami", "trappedwithjester": "jesterləqapanmış", "dinkum": "dinkum", "predecessor": "istiqa<PERSON>ə<PERSON><PERSON><PERSON>", "rainworld": "yağışdünya", "cavesofqud": "qudmağaraları", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "müharibəninşafağı", "minionmasters": "minionmasters", "grimdawn": "qaranlıqşafaq", "darkanddarker": "qaranlıqv<PERSON><PERSON>aqaranlıq", "motox": "motox", "blackmesa": "qaraçay", "soulworker": "ruhişçisi", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kubqaçış", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "ye<PERSON><PERSON><PERSON>hər", "citiesskylines": "şə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uqu", "defconheavy": "defconheavy", "kenopsia": "kenopsiya", "virtualkenopsia": "virtualkenopsiya", "snowrunner": "qarqaçan", "libraryofruina": "ruinadakıkitabxana", "l4d2": "l4d2", "thenonarygames": "nonaryoyunlar", "omegastrikers": "omegastrikerlar", "wayfinder": "yolgöstə<PERSON>ən", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "sakitplastikördek", "battlebit": "battlebit", "ultimatechickenhorse": "ultimavəkləquş", "dialtown": "dial<PERSON><PERSON><PERSON>ər", "smileforme": "gülümsəmənimüçün", "catnight": "pisiyaxtası", "supermeatboy": "superşirnikoğul", "tinnybunny": "tınıqlıqanad", "cozygrove": "cozygrove", "doom": "qəza", "callofduty": "görevədəvət", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kod", "borderlands": "sər<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombilər", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "u<PERSON>n<PERSON>üddətli", "farcrygames": "farcryoyunları", "paladins": "pala<PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "<PERSON>ı<PERSON><PERSON><PERSON><PERSON>", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "qoşulböyükqrupa", "echovr": "echovr", "discoelysium": "diskoylisyum", "insurgencysandstorm": "üsyanqumul<PERSON><PERSON>ə<PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ölümqurmaq", "b4b": "b4b", "codwarzone": "codzonası", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombiləri", "mirrorsedge": "güzg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "bölmələr2", "killzone": "öldürməzone", "helghan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coldwarzombies": "soyuq<PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "çarpazkod", "goldeneye007": "qızılgöz007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "müasirm<PERSON><PERSON>bə", "neonabyss": "neonabyss", "planetside2": "planetvarsi2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "sərhə<PERSON><PERSON>lər", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "tarkovdanqaçış", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "döyüşgəmilərdünyası", "back4blood": "back4blood", "warframe": "savaşçılar", "rainbow6siege": "rainbow6çatışması", "xcom": "xcom", "hitman": "qatil", "masseffect": "<PERSON><PERSON><PERSON>ə<PERSON>", "systemshock": "sistemşok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cavestory", "doometernal": "doometernaл", "centuryageofashes": "əsrtozlarınınyaşı", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "bölmə2", "tythetasmaniantiger": "tytasmaniyap<PERSON>ləngi", "generationzero": "nəsilzero", "enterthegungeon": "də<PERSON><PERSON><PERSON><PERSON>r", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "müasirşəhid2", "blackops1": "qarasöhbət1", "sausageman": "sosiskaadam", "ratchetandclank": "ratchetveclank", "chexquest": "chexquest", "thephantompain": "şəffafacıacıdazagərdimuğam", "warface": "müharibəüz<PERSON>", "crossfire": "çətindöyüş", "atomicheart": "atomikürək", "blackops3": "qaraemeliyat3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lar", "callofdutybatleroyale": "callofdutybatlroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "azadlıq", "battlegrounds": "batalyonlar", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "oyunpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalseksonlarazadliq", "juegosfps": "fpsoyunları", "convertstrike": "dönüşümvuruşu", "warzone2": "müharibəzona2", "shatterline": "şəkilqırıcı", "blackopszombies": "qaraəməliyyatlarzombiləri", "bloodymess": "qanlıqarışıqlıq", "republiccommando": "republickomanda", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "yeraltışöbə", "squad": "<PERSON><PERSON>ar", "destiny1": "tale1", "gamingfps": "oyunfps", "redfall": "qırm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ə", "pubggirl": "pubggirl", "worldoftanksblitz": "tanklaraləmiblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "qoşulmuş", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "maşın2", "cs16": "cs16", "pubgindonesia": "pubgindoneziya", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromanya", "empyrion": "empiryon", "pubgczech": "pubgçeşka", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "boobağları", "csplay": "csplay", "unrealtournament": "gerçəkdəyilturnir", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "sərhədlar2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "tü<PERSON>əngyerləşdirmək", "callofdutymw2": "callofdutymw2", "quakechampions": "zə<PERSON><PERSON><PERSON><PERSON><PERSON>çempionları", "halo3": "halo3", "halo": "halo", "killingfloor": "qə<PERSON><PERSON><PERSON><PERSON>yi", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "spli̇nterci̇ll", "neonwhite": "neonwhite", "remnant": "qalıq", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ş", "gunvolt": "günvolt", "returnal": "qayıdış", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "kölgəadam", "quake2": "zəlzələ2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "qırmızıölüm", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battleground3", "lostark": "itirark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "dünyaqəhr<PERSON><PERSON>ları", "rust": "rəngsaz", "conqueronline": "qələbəonline", "dauntless": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "gəmilər", "dayofdragons": "cıngıllarıngünü", "warthunder": "warthunder", "flightrising": "uçuşqalxması", "recroom": "oyunxanasi", "legendsofruneterra": "runeçölünün<PERSON>fs<PERSON>ə<PERSON>", "pso2": "pso2", "myster": "<PERSON>eriya", "phantasystaronline2": "fantasystaronline2", "maidenless": "qızsız", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "çəkdiqıra", "agario": "agario", "secondlife": "ikincihayat", "aion": "aion", "toweroffantasy": "fonyadayı", "netplay": "şə<PERSON>ək<PERSON>oyunu", "everquest": "daimisual", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossdünyası", "reddeadonline": "qırmızıölümonlayn", "superanimalroyale": "superheyvanroyalı", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "şövalyənət", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "isaa<PERSON>ınbağlanması", "dragonageinquisition": "dragonageinquisition", "codevein": "kodqanı", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "zibil", "newworld": "yeni_dünya", "blackdesertonline": "qaraçölonline", "multiplayer": "çoxoyunçu", "pirate101": "pirat101", "honorofkings": "şöhrətkralları", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>si<PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "ulduzdöyüşümfron2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3ds<PERSON><PERSON>", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassik", "worldofwarcraft": "döyüşdünyası", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "<PERSON><PERSON><PERSON><PERSON>r", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "yarad<PERSON>l<PERSON>şınc<PERSON><PERSON>lə<PERSON>", "riotmmo": "dözümbağışla", "silkroad": "ipekyolu", "spiralknights": "spiralknightlar", "mulegend": "mu<PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "intiqam", "albiononline": "albiononline", "bladeandsoul": "bıçaqvəruh", "evony": "evony", "dragonsprophet": "dragonları<PERSON>ləşəti", "grymmo": "grymmo", "warmane": "istiolmaq", "multijugador": "müxtəlifoyunçu", "angelsonline": "mə<PERSON><PERSON><PERSON><PERSON><PERSON>n", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseneonline", "growtopia": "growtopia", "starwarsoldrepublic": "uğurlularistisnası", "grandfantasia": "böyükfantaziya", "blueprotocol": "blueprotokol", "perfectworld": "mükəmməldünya", "riseonline": "onlaynqalx", "corepunk": "corepunk", "adventurequestworlds": "macəraçılardünyası", "flyforfun": "dahaeynikinüçün", "animaljam": "<PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "bəd<PERSON><PERSON><PERSON>ınkrallığı", "cityofheroes": "qə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "küçəsavaşçısı", "hollowknight": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsolid", "forhonor": "ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgg", "streetfighter6": "kstreetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "ruhçöyücü", "brawlhalla": "brawlhalla", "virtuafighter": "virtuasavaşçı", "streetsofrage": "rəqibk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkölümcülittifaq", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "döyüşçülərinşahı", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightinggames", "blasphemous": "blasfemik", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmasher", "mugen": "mugen", "warofthemonsters": "canavarlarınmü<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "mübarizəoyunları", "cyberbots": "kiberobotlar", "armoredwarriors": "şəffafdöyüşçülər", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "güclüavadanlıqlar", "beatemup": "döyüşdüzdün", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "döyüşoyunları", "killerinstinct": "qatilinstinkti", "kingoffigthers": "döyüşkralı", "ghostrunner": "ghostrunner", "chivalry2": "nizami2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "cəzaüçünmübarizə", "hollowknightsequel": "hollowknightdavamı", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongoyunu", "silksongnews": "silksongmələkləri", "silksong": "silksong", "undernight": "gecəaltı", "typelumina": "tipelumina", "evolutiontournament": "inkişaftturniri", "evomoment": "evomoment", "lollipopchainsaw": "lolli<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "be<PERSON><PERSON>nınhekay<PERSON>l<PERSON><PERSON>", "bloodborne": "qanaxma", "horizon": "horizont", "pathofexile": "exiledə<PERSON><PERSON>", "slimerancher": "sümükçüferması", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "kəşfedilməmiş", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "sonuncularımız", "infamous": "mə<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationdostları", "ps1": "ps1", "oddworld": "tə<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON>əm", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "cə<PERSON>ənnəmaçılır", "gta4": "gta4", "gta": "gta", "roguecompany": "qanunsuzşirkət", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "döyüşünxudası", "gris": "griş", "trove": "xazina", "detroitbecomehuman": "detroithumanolur", "beatsaber": "<PERSON><PERSON><PERSON><PERSON>", "rimworld": "rimdünyası", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "sü<PERSON>h<PERSON>d<PERSON>k", "touristtrophy": "turi<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "nəhəngl<PERSON>rinkölgəsi", "crashteamracing": "qəzaqruppoyunu", "fivepd": "beşpd", "tekken7": "tekken7", "devilmaycry": "şeytanıxıdır", "devilmaycry3": "devilmaycry3", "devilmaycry5": "şeytanqoymıram5", "ufc4": "ufc4", "playingstation": "ps5", "samuraiwarriors": "samuraydöy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "soulblade", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2beyannamə", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "oyunpass", "armello": "armello", "partyanimal": "bayramcanlı", "warharmmer40k": "döyüşhəşməti40k", "fightnightchampion": "döyüşgecəqəhrəmanı", "psychonauts": "psixonautlar", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "yaşlılarçevrimiskyrim", "pantarhei": "pantarei", "theelderscrolls": "yaşlıscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "birlik<PERSON><PERSON>ac<PERSON>lma", "ori": "ori", "spelunky": "spəlunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "ul<PERSON>zlardankənar", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "evdeyişdirici", "americanmcgeesalice": "amerikamcgeesalice", "xboxs": "xboxlar", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseriyası", "r6xbox": "r6xbox", "leagueofkingdoms": "krallıqlarliga", "fable2": "fable2", "xboxgamepass": "xboxoyunpassı", "undertale": "undertale", "trashtv": "tullantitv", "skycotl": "göyçaylı", "erica": "erica", "ancestory": "nəsil", "cuphead": "cuphead", "littlemisfortune": "kiçikbəxtsizlik", "sallyface": "sa<PERSON><PERSON><PERSON>", "franbow": "franboz", "monsterprom": "canavarbalayı", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "kənard<PERSON><PERSON>lar", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "lambkultu", "duckgame": "ördekoyunu", "thestanleyparable": "stan<PERSON>nin<PERSON><PERSON><PERSON>ə<PERSON>", "towerunite": "towerunite", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "uzunşoferlik", "satisfactory": "məqbul", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometridənşətəyı", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "ruhxeyardarı", "darkdome": "qaraşəffaf", "pizzatower": "pizzatower", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "doğruduryacəsarət", "game": "oyun", "rockpaperscissors": "daşqağıtç<PERSON><PERSON>n", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "cə<PERSON><PERSON>t", "scavengerhunt": "çölaxtarışı", "yardgames": "yazoyunları", "pickanumber": "rəqə<PERSON>ç", "trueorfalse": "do<PERSON><PERSON>uryaşdüzdür", "beerpong": "beerponq", "dicegoblin": "zaratmayısevənlər", "cosygames": "cosygames", "datinggames": "tanışlıqoyunları", "freegame": "pulsuz<PERSON><PERSON>", "drinkinggames": "içkiyarışları", "sodoku": "sodoku", "juegos": "oyunlar", "mahjong": "mahjong", "jeux": "oyunlar", "simulationgames": "simulyasiyaoyunları", "wordgames": "sözoyunları", "jeuxdemots": "sözoyunları", "juegosdepalabras": "sözoyunları", "letsplayagame": "gedəkoynayaq", "boredgames": "bored<PERSON><PERSON>", "oyun": "oyun", "interactivegames": "interaktivoyunlar", "amtgard": "amtgard", "staringcontests": "başqa<PERSON>ırmaoyunları", "spiele": "spil", "giochi": "oyunlar", "geoguessr": "geoguessr", "iphonegames": "iphoneoyunları", "boogames": "boooyunları", "cranegame": "kranoyunu", "hideandseek": "gizlivəaxtar", "hopscotch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "arcadeoyunları", "yakuzagames": "yakuzaoyunları", "classicgame": "klassikoyun", "mindgames": "zehnoyunları", "guessthelyric": "sözləritap", "galagames": "galacoyunlar", "romancegame": "romantikoyun", "yanderegames": "yander<PERSON><PERSON>ları", "tonguetwisters": "dilçalışmaları", "4xgames": "4xoyunlar", "gamefi": "gamefi", "jeuxdarcades": "arcadeoyunları", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "oyunlar90", "idareyou": "səniidarəediram", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "yarışoyunları", "ets2": "ets2", "realvsfake": "gerçəkvsyalan", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "oyunlayn", "onlinegames": "onlayngames", "jogosonline": "onlaynoyunlar", "writtenroleplay": "yazılıroloynama", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgames": "kooperativoyunlar", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON>üksəkbal", "jeuxderôles": "r<PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgeroyunları", "kidsgames": "uşaqlararoyunları", "skeeball": "squeeball", "nfsmwblackedition": "nfsmwqaranoğlu", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "sor<PERSON><PERSON><PERSON>ışması", "gioco": "oyun", "managementgame": "idarəetməoyunu", "hiddenobjectgame": "gizliobyektoyunu", "roolipelit": "rolipelit", "formula1game": "formula1oyunu", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "arcadesoyğunları", "memorygames": "yaddaşoyunları", "vulkan": "vulkan", "actiongames": "fədalıoyunlar", "blowgames": "tə<PERSON><PERSON><PERSON><PERSON>ı", "pinballmachines": "bilyardmaşınları", "oldgames": "köhnoyunlar", "couchcoop": "<PERSON><PERSON><PERSON><PERSON>", "perguntados": "soruşduğumuzu", "gameo": "<PERSON><PERSON><PERSON>", "lasergame": "lasergame", "imessagegames": "imessageoyunları", "idlegames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "kompoyunları", "rétrogaming": "retrogaming", "logicgames": "məntiqoyunları", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "metrolayndırma", "jeuxdecelebrite": "məşhurlarınoyunları", "exitgames": "çiğnəsidəqurtulmaoyunu", "5vs5": "5vs5", "rolgame": "rolo<PERSON>", "dashiegames": "dashiegames", "gameandkill": "oynavə<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "ənənəviloyunlar", "kniffel": "kniffel", "gamefps": "oyunfps", "textbasedgames": "mətnəsa<PERSON>yunlar", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "qə<PERSON><PERSON><PERSON>yun", "lawngames": "çimoyunları", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "ma<PERSON><PERSON><PERSON>", "tischfußball": "futbolmasası", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "bazaoyunlar", "casualgames": "rahatoyunlar", "fléchettes": "darts", "escapegames": "qaçışoyunları", "thiefgameseries": "oğruoyunlarıseriyası", "cranegames": "quşoyunları", "játék": "oyun", "bordfodbold": "bordfutbol", "jogosorte": "oyunbir<PERSON><PERSON><PERSON>", "mage": "dostluq", "cargames": "maşınoyunları", "onlineplay": "<PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "çantaçarpanlar", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "oyunlardapc", "socialdeductiongames": "sosialtəhliloyunları", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrikoyunlar", "goodoldgames": "yaxşıköhnəoyunlar", "truthanddare": "həqiqətvəcəsarət", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "tapanqoymaoyunları", "jeuxvirtuel": "<PERSON><PERSON>un<PERSON>", "romhack": "romhacker", "f2pgamer": "f2pgamer", "free2play": "puls<PERSON><PERSON>na", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "oyunlarqozuqa", "halotvseriesandgames": "halotvseriyalarvəoyunlar", "mushroomoasis": "göbələkdənqənaat", "anythingwithanengine": "motoruolanhərşey", "everywheregame": "hə<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "qılınc<PERSON><PERSON><PERSON>hr", "goodgamegiving": "yaxşimübarizəveririk", "jugamos": "baxırıq", "lab8games": "lab8oyunları", "labzerogames": "labzerogames", "grykomputerowe": "kompüteroyunları", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "ritm<PERSON><PERSON><PERSON><PERSON>u", "minaturegames": "miniatüroyunlar", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "özünəsevənoyun", "gamemodding": "oyunmodifikasiyası", "crimegames": "cinayətoyunları", "dobbelspellen": "dobbelspellen", "spelletjes": "oyunlar", "spacenerf": "kos<PERSON><PERSON><PERSON>", "charades": "<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "təkoyunçu", "coopgame": "koop<PERSON><PERSON><PERSON><PERSON>", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "kraldiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON>", "shogi": "<PERSON><PERSON><PERSON>", "dandd": "dandd", "catan": "katan", "ludo": "ludo", "backgammon": "damka", "onitama": "onitama", "pandemiclegacy": "pandemiyairs", "camelup": "dromedarsürüşü", "monopolygame": "monopolyaoyunu", "brettspiele": "bretoyunlar", "bordspellen": "bords<PERSON>z<PERSON><PERSON><PERSON>", "boardgame": "oyun", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "planzovə", "risiko": "risko", "permainanpapan": "oyunpalitrası", "zombicide": "zombicid", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "qan<PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "sənət", "goboardgame": "gooyunoynamaq", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "qəhrəmanovu", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "karrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "zaroyunları", "yatzy": "yatzy", "parchis": "par<PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "cəmiyyətdəoy<PERSON>lar", "deskgames": "masaoyunları", "alpharius": "alfarius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelböhranprotokolu", "cosmicencounter": "kozmikqarş<PERSON>laşma", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "masaüstürolodüşünmə", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "qaranlıqhəyəcan", "switchboardgames": "switchboardoyunları", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "krallığınölümü", "yahtzee": "yahtzee", "chutesandladders": "şəlalələrvəmənbərilər", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "üstümasalar", "planszówki": "oyunlar", "rednecklife": "qırmızıboyunhayatı", "boardom": "boşboğazlıq", "applestoapples": "almadanalma", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "oyuntaxtası", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "cəmiyyətoyunları", "twilightimperium": "alatoranimperiyası", "horseopoly": "atıopoly", "deckbuilding": "karttoparlama", "mansionsofmadness": "də<PERSON><PERSON><PERSON><PERSON>rdənmal<PERSON>lər", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "kölgələrdənqalayır", "kingoftokyo": "tokyonunkralı", "warcaby": "şəhidlərinhörmətı", "táblajátékok": "taşoyunlar", "battleship": "<PERSON><PERSON><PERSON><PERSON>şg<PERSON><PERSON><PERSON>", "tickettoride": "biletəminir", "deskovehry": "masaoyunları", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "oyunplakası", "stolníhry": "stoln<PERSON><PERSON>lar", "xiángqi": "<PERSON><PERSON><PERSON>", "jeuxsociete": "ictimaiy_game", "gesellschaftsspiele": "cəmiyyətoyunları", "starwarslegion": "uğurduzlarlegionu", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "so<PERSON><PERSON><PERSON><PERSON>", "terraria": "terrarıya", "dsmp": "dsmp", "warzone": "müharibəzonası", "arksurvivalevolved": "arkbirlikdünyası", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "kimliyik", "theisle": "ada", "thelastofus": "sonun<PERSON><PERSON>", "nomanssky": "nobodysgalaxy", "subnautica": "subnautica", "tombraider": "qə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "cthulhununzəngi", "bendyandtheinkmachine": "bendyvədəftərmaşını", "conanexiles": "conanexiles", "eft": "eft", "amongus": "bizinarxamızda", "eco": "eko", "monkeyisland": "çimpanzerlərsahili", "valheim": "valheim", "planetcrafter": "planetqurucusu", "daysgone": "günlərkeçdi", "fobia": "fobiya", "witchit": "witchedin", "pathologic": "<PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "uzunqaranlıq", "ark": "ark", "grounded": "yerdə", "stateofdecay2": "çürüməhaləti2", "vrising": "vrising", "madfather": "madanaçlıq", "dontstarve": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "əbədiqayıdış", "pathoftitans": "titanlarınyolu", "frictionalgames": "frictionaloyunlar", "hexen": "hexen", "theevilwithin": "daxild<PERSON><PERSON>b<PERSON>lanın", "realrac": "g<PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "geriotaqlar", "empiressmp": "imperiyamehzengar", "blockstory": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "<PERSON>rier", "tlou": "tlou", "dyinglight": "ölməşişıq", "thewalkingdeadgame": "y<PERSON>r<PERSON><PERSON>ənöl<PERSON>lə<PERSON>u", "wehappyfew": "bizxöşükfew", "riseofempires": "imperiyalarınş<PERSON><PERSON>əsi", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "vintagestory": "köhnəhekayə", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathegə", "alisa": "alisa", "westlendsurvival": "qərbqalıqsürmə", "beastsofbermuda": "bermudanıncanavarsları", "frostpunk": "frostpunk", "darkwood": "qarameşə", "survivalhorror": "survivalhor<PERSON>r", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "rezyidentşeytan3", "voidtrain": "b<PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "oyundansonrah<PERSON>t", "survivalgames": "davametadışmalar", "sillenthill": "səssizdağ", "thiswarofmine": "bumə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfundamental", "greenproject": "yaş<PERSON>llayihə", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "raft", "rdo": "rdo", "greenhell": "yaş<PERSON>l<PERSON><PERSON>h<PERSON>t", "residentevil5": "residentevil5", "deadpoly": "ölüpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nənə", "littlenightmares2": "kiçikkabuslar2", "signalis": "signalis", "amandatheadventurer": "mandatheadventurer", "sonsoftheforest": "meşəsö<PERSON>l<PERSON><PERSON>", "rustvideogame": "rustvide<PERSON><PERSON>u", "outlasttrials": "dava<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "xariciizolyasiya", "undawn": "undawn", "7day2die": "7gün2öl", "sunlesssea": "günəşsizdəniz", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propnight": "propnoktası", "deadisland2": "ölüada2", "ikemensengoku": "ikenmensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "ölümversiyası", "cataclysmdarkdays": "kataklizmdarkgünlər", "soma": "soma", "fearandhunger": "qor<PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "həyatondansonra", "ageofdarkness": "qaranlıqdövr", "clocktower3": "saatevi3", "aloneinthedark": "qaranlıqdatək", "medievaldynasty": "ortaçagirəyi", "projectnimbusgame": "projectnimbusoyunu", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "outlastsınaqları", "bunker": "bunker", "worlddomination": "d<PERSON><PERSON>nıqaplama", "rocketleague": "raketliq", "tft": "tft", "officioassassinorum": "ofisdöyü<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dwarfdarpağı", "warhammer40kcrush": "warhammer40ksev<PERSON>i", "wh40": "wh40", "warhammer40klove": "warhammer40ksevgi", "warhammer40klore": "warhammer40kafka", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kqaranlıqdalğa", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindika<PERSON>", "ilovesororitas": "mənşororitalarısevirəm", "ilovevindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iloveassasinorum": "assasinorumsevirəm", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "qə<PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "i̇mperiyalardövrü", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "mədəniyyətv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "qanadaçısı", "terraformingmars": "marsıyeniləşdirmək", "heroesofmightandmagic": "güclüvesih<PERSON><PERSON>", "btd6": "btd6", "supremecommander": "supremekomandir", "ageofmythology": "məfku<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "təzad", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "yolasalındı", "caesar3": "caesar3", "redalert": "qırmızıxəbərdarlıq", "civilization6": "sivilizasiya6", "warcraft2": "warcraft2", "commandandconquer": "əmrvervəqələbə", "warcraft3": "warcraft3", "eternalwar": "əbədi<PERSON><PERSON>bə", "strategygames": "strate<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "mədəni<PERSON><PERSON><PERSON><PERSON>yunu", "civilization4": "sivilizasiya4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "spore", "totalwar": "ümumimüharibə", "travian": "travian", "forts": "forts", "goodcompany": "yaxşışirkət", "civ": "civ", "homeworld": "evdünyası", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "işıqdandahasürətli", "forthekings": "kralılarüçün", "realtimestrategy": "gerçektimoyunstrateji", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierinmedeniyyə<PERSON>", "kingdomtwocrowns": "ikiöyünkrallığı", "eu4": "avropalılarüçün", "vainglory": "böyüklənmə", "ww40k": "ww40k", "godhood": "ilahisərv<PERSON>t", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "dave<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ə<PERSON>i", "plagueinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "teoriyabazarı", "mesbg": "mesbg", "civilization3": "mədəniyyət3", "4inarow": "4dördünüstə", "crusaderkings3": "crusaderkings3", "heroes3": "qəhrəmanlar3", "advancewars": "irə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "döngəimpariyası2", "disciples2": "tərbiyəçilər2", "plantsvszombies": "bitkilərvszombilər", "giochidistrategia": "giochidistrategiya", "stratejioyunları": "stratejiolmaları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "möcüz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozavrşahı", "worldconquest": "dünyanıfəthet", "heartsofiron4": "dəmirdənqəlblər4", "companyofheroes": "qə<PERSON>ə<PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "battleforwesnoth": "wesnothüçündöyüş", "aoe3": "aoe3", "forgeofempires": "imperiyalarınqaynarxanası", "warhammerkillteam": "warhammerölümqrupu", "goosegooseduck": "goosegooseduck", "phobies": "fob<PERSON>lar", "phobiesgame": "fob<PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "xarijdüzülüş", "turnbased": "növbəəsasında", "bomberman": "bomberman", "ageofempires4": "imperiyalarınyaşı4", "civilization5": "mədəniyyət5", "victoria2": "victoria2", "crusaderkings": "şövalyələrinkrallığı", "cultris2": "cultris2", "spellcraft": "sehirbazanlıq", "starwarsempireatwar": "ulduzmüharibələriimperiyamüharibədə", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strateji", "popfulmail": "popfulmail", "shiningforce": "parlaqqüvvə", "masterduel": "ustadduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "nəqliyyatböyükləri", "unrailed": "yoldançıkmış", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "qurdlar<PERSON><PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "planlayırıqqaçmaq<PERSON>ə<PERSON>ən", "uplandkingdoms": "uplaşdö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "galaxylife", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "battlecats", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "sürətəeşarət", "needforspeedcarbon": "sürətəeynişeycarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "böyükturizm", "gt7": "gt7", "simsfreeplay": "simsplayaz", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "itirmis4", "fnaf": "fnaf", "outlast": "davaqol", "deadbydaylight": "ölükləriqorxudan", "alicemadnessreturns": "alicemadnessqayıdır", "darkhorseanthology": "qaraatlıantologiya", "phasmophobia": "phan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "beşgecəfreddidə", "saiko": "saiko", "fatalframe": "fəna<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "kiçikkabuslar", "deadrising": "ölülərinqaldırılması", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "evdə_qalan", "deadisland": "ölüada", "litlemissfortune": "kiçikqızbədduası", "projectzero": "proyektzero", "horory": "horror", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "salamsozun2", "gamingdbd": "oyunverilənl<PERSON>ri", "thecatlady": "pisiqqadın", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "də<PERSON><PERSON><PERSON><PERSON>yunları", "magicthegathering": "mağicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "insanlığaqar<PERSON><PERSON><PERSON>ar", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodadları", "dixit": "dixit", "bicyclecards": "biskletkartları", "lor": "lor", "euchre": "yuker", "thegwent": "thegwent", "legendofrunetera": "runeteranınlegendi", "solitaire": "təkoyun", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "şafşah", "keyforge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardtricks": "karttriklər", "playingcards": "oyunkartları", "marvelsnap": "marvelsnap", "ginrummy": "gingirdağı", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "ticarətkartları", "pokemoncards": "pokemonkartları", "fleshandbloodtcg": "cəmdəkəziqtcg", "sportscards": "idmankartları", "cardfightvanguard": "kartdöyüşvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "savaşçığlığı", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "ürəyinpadşası", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "mü<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkartları", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "<PERSON><PERSON><PERSON>", "yugiohgame": "yugiohoyunu", "darkmagician": "qaranlıq魔法çı", "blueeyeswhitedragon": "mavigözlüağdragon", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kartoy<PERSON>u", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandiri", "cotorro": "katırtwo", "jeuxdecartes": "kartoyunları", "mtgjudge": "mtgjurisi", "juegosdecartas": "kartoyunları", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplansqovuşma", "mtgpreconcommander": "mtgpreconkomandiri", "kartenspiel": "kartopusu", "carteado": "kartatmaq", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespirits": "döyüşruhları", "battlespiritssaga": "döyüşruhuşagası", "jogodecartas": "kartoy<PERSON>u", "žolíky": "žolíky", "facecard": "üzkartı", "cardfight": "kartdöyüşü", "biriba": "biriba", "deckbuilders": "kartşünaslar", "marvelchampions": "marvelçempionları", "magiccartas": "majiçartas", "yugiohmasterduel": "yugiohustadmaqarası", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "stabilsəhərlər", "cyberse": "cyberse", "classicarcadegames": "klassikarcadeoyunları", "osu": "osu", "gitadora": "gitadoram", "dancegames": "rə<PERSON><PERSON>yunları", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projeydiva", "djmax": "djmax", "guitarhero": "gitaraqəhrəmanı", "clonehero": "klonquhero", "justdance": "<PERSON><PERSON><PERSON><PERSON><PERSON>na", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "ölülərialtqaçır", "chunithm": "çunithm", "idolmaster": "idolmaster", "dancecentral": "rə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "rit<PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "<PERSON>ü<PERSON>əltskoreythmgamlar", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritm<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "atəşvəbuzdanreaksiyası", "auditiononline": "onlaynseçim", "itgmania": "itgmaniası", "juegosderitmo": "ritmoyunları", "cryptofthenecrodancer": "kriptodansöyünənqəbrçilər", "rhythmdoctor": "ritmhə<PERSON>i", "cubing": "kublaşdırma", "wordle": "wordle", "teniz": "də<PERSON>z", "puzzlegames": "puzzleoyunları", "spotit": "tapıbgör", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "məntiqbulmacaları", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "zəkagücləndirənlər", "rubikscube": "rubikscube", "crossword": "krossvord", "motscroisés": "motscroisés", "krzyżówki": "krossvordlar", "nonogram": "nonogram", "bookworm": "kitabsever", "jigsawpuzzles": "jigsawpuzzles", "indovinello": "gizlişifrə", "riddle": "tapmaca", "riddles": "tapmacalar", "rompecabezas": "bulmacalar", "tekateki": "tekateki", "inside": "içə<PERSON><PERSON>", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "qaçışsimulyatoru", "minesweeper": "minesweeper", "puzzleanddragons": "təhqiqatvədraqlılar", "crosswordpuzzles": "cümləbulmacaları", "kurushi": "k<PERSON>hi", "gardenscapesgame": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "bulmacasport", "escaperoomgames": "qaçışoyunları", "escapegame": "qaçışoyunu", "3dpuzzle": "3dbulmacalar", "homescapesgame": "evg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordsearch": "söztapmaca", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "bə<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "imkansızsorğu", "candycrush": "şirinkövrəklər", "littlebigplanet": "kiçikböyükplanet", "match3puzzle": "match3bulmacası", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "qəribə", "rubikcube": "rubikcube", "cuborubik": "kubikdəgizlənmək", "yapboz": "yapboz", "thetalosprinciple": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "tənqidet", "tycoongames": "tycoongames", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "krossvord", "ciphers": "ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "tapmacawordları", "buscaminas": "bucaminas", "puzzlesolving": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "tunepoğlan", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "tahminetmək", "nonograms": "nonogramlar", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptikkrossvordlar", "syberia2": "syberia2", "puzzlehunt": "tapmacasaçan", "puzzlehunts": "tapmacayarışları", "catcrime": "pişik<PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "qırıqbaş", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "sonoduncampfire", "autodefinidos": "özünütəyinedənlər", "picopark": "picopark", "wandersong": "wandersong", "carto": "carto", "untitledgoosegame": "adı<PERSON><PERSON>q<PERSON>ulmamıştəyyarəoyunu", "cassetête": "kasetçətə", "limbo": "limbo", "rubiks": "rubiks", "maze": "dünyamız", "tinykin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "rubik<PERSON><PERSON><PERSON>", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "<PERSON><PERSON><PERSON><PERSON>", "bilmece": "bilm<PERSON>ce", "puzzelen": "puzzelen", "picross": "pik<PERSON>", "rubixcube": "rubixkub", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobil", "codm": "codm", "twistedwonderland": "<PERSON>ük<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "monopoly": "monopoliya", "futurefight": "gələ<PERSON><PERSON>yindöyüşü", "mobilelegends": "mobillegendlər", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "təkcəqurd", "gacha": "gacha", "wr": "wərr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensembleulduzları", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "bişiribıqralığı", "alchemystars": "alchemystars", "stateofsurvival": "ağırşərait", "mycity": "mə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknightlar", "colorfulstage": "rəng<PERSON>əhnə", "bloonstowerdefense": "bloonstonkaqoruma", "btd": "btd", "clashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON>", "knightrun": "cəngavərqaçış", "fireemblemheroes": "odqəhrəmanları", "honkaiimpact": "honkaiimpact", "soccerbattle": "futboldöyüşü", "a3": "a3", "phonegames": "telefonoyunları", "kingschoice": "kralınseçimi", "guardiantales": "qəhrə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikcool", "cookierun": "cookieqayıq", "pixeldungeon": "pixeldungeon", "arcaea": "arcea", "outoftheloop": "kənardaqalan", "craftsman": "ustac", "supersus": "supersus", "slowdrive": "yavaşsürüş", "headsup": "baş<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "çarpışmaoyunları", "freefire": "freefire", "mobilegaming": "mobiloyunlar", "lilysgarden": "lilysgahı", "farmville2": "farmville2", "animalcrossing": "heyvankeç<PERSON><PERSON>ə<PERSON>", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "klashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mistikmesajçı", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8topbilyard", "emergencyhq": "təciliqtəşkilatı", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "heyday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "titrəvə<PERSON><PERSON>ş", "ml": "ml", "bangdream": "bangdream", "clashofclan": "klanlararasımübarizə", "starstableonline": "ul<PERSON><PERSON><PERSON>əm", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "vaxtprin<PERSON>i", "beatstar": "beatstar", "dragonmanialegend": "dragonadamlegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON>", "androidgames": "androidoyunları", "criminalcase": "cinayətişi", "summonerswar": "dəvə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "yeməkçiliyəhəyəcan", "dokkan": "dokkən", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "mələklərinliqası", "lordsmobile": "lordsmobile", "tinybirdgarden": "kiçikquşbağı", "gachalife": "gachalife", "neuralcloud": "<PERSON><PERSON><PERSON><PERSON>", "mysingingmonsters": "mənimoxuyancanavarlarım", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "mavilayihə", "raidshadowlegends": "raidshadowlegends", "warrobots": "müharibərobotları", "mirrorverse": "güz<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "müharibəqanadları", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyağdır", "apexlegendmobile": "apexlegendmobil", "ingress": "<PERSON><PERSON><PERSON>", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "pulustası", "punishinggrayraven": "cəzaqaragüvən", "petpals": "hey<PERSON>_dostlar", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "qurdlu", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ışoyunu", "juegodemovil": "mobiloyun", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "təqlid", "blackdesertmobile": "qaradənizmobil", "rollercoastertycoon": "rəngarəngdəhəyət", "grandchase": "büyükmü<PERSON>bə", "bombmebrasil": "bomb<PERSON>ən<PERSON><PERSON>l", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "callofdragons", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "kölgəbdövüşü3", "limbuscompany": "limbusfirması", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "dostlarlasözlər2", "soulknight": "ruhşövalyesi", "purrfecttale": "purrfekthekayə", "showbyrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "qızlarpopulyar", "lolmobile": "lolmobildə", "harvesttown": "təd<PERSON>torpaq", "perfectworldmobile": "mükəmmelaləmlərmobil", "empiresandpuzzles": "impariyalarveküplər", "empirespuzzles": "empirespuzzles", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilenişində", "fanny": "sərbəst", "littlenightmare": "kiçikkabus", "aethergazer": "aethergözləyən", "mudrunner": "mudrunner", "tearsofthemis": "g<PERSON><PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "hə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t", "gunbound": "günbağlı", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknights", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiqaçqınları", "eveechoes": "<PERSON><PERSON><PERSON>", "jogocelular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobiləjendbangbang", "gachaclub": "gachaklub<PERSON>", "v4": "v4", "cookingmama": "bişirməananası", "cabalmobile": "cabalmobil", "streetfighterduel": "küçəgöyüçüsüduel", "lesecretdhenri": "lassescretsdhenri", "gamingbgmi": "gamingbgmi", "girlsfrontline": "qızlaröncüsü", "jurassicworldalive": "jurassikdünyaayaqdaşması", "soulseeker": "ruhaxtaran", "gettingoverit": "ö<PERSON>d<PERSON>sindəngəlmək", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobiloyunlar", "legendofneverland": "heçbirölkəninlegendası", "pubglite": "pubglite", "gamemobilelegends": "gamermobillegendlər", "timeraiders": "timeraiders", "gamingmobile": "mobiloyunlar", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "axtarış", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgedməsi", "worldofdarkness": "qaranlıqdünya", "travellerttrpg": "səyahətçittrpg", "2300ad": "2300il", "larp": "larp", "romanceclub": "romanceklubu", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémonkristal", "pokemonanime": "pokémonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonqırmızı", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonbirlik", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemontutqun", "pokemonpurpura": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "komandaroket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonqucaq", "teamystic": "teamystic", "pokeball": "pokebola", "charmander": "<PERSON><PERSON><PERSON>", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "<PERSON><PERSON><PERSON>ü<PERSON>", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "salam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokeemonustası", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "uşaqlarvəh<PERSON>ş<PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "çarızar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON>", "catur": "<PERSON><PERSON><PERSON>", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON><PERSON>", "schaken": "şəkilçəkmək", "skak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedres": "<PERSON><PERSON><PERSON>", "chessgirls": "şahmatqızları", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rma", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "çinşahmatı", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "şahmatdanşif<PERSON>", "openings": "açılışlar", "rook": "bibi", "chesscom": "şahmatcom", "calabozosydragones": "kalavozlarvədrakonlar", "dungeonsanddragon": "zindanlarvətəslər", "dungeonmaster": "zindanustası", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventura", "darksun": "qaragünəş", "thelegendofvoxmachina": "voxmachinanınlegendası", "doungenoanddragons": "dounqenoanddragons", "darkmoor": "qaranl<PERSON>q<PERSON><PERSON>", "minecraftchampionship": "minecraftçempionluğu", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftyatağı", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodları", "mcc": "mcc", "candleflame": "şamodununistisi", "fru": "fru", "addons": "<PERSON><PERSON>ələr", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "göyblokk", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "arasındaolanlar", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftşəhəri", "pcgamer": "pcoyunçu", "jeuxvideo": "<PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "oyunçular", "levelup": "səviyyəniqaldır", "gamermobile": "gamermobil", "gameover": "oyunbitdi", "gg": "gg", "pcgaming": "kompüteroyunu", "gamen": "<PERSON><PERSON><PERSON>ın", "oyunoynamak": "oyunoynamaq", "pcgames": "kompüteroyunları", "casualgaming": "rahatoyunlar", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "kompy<PERSON><PERSON><PERSON><PERSON>", "gamerboy": "gameroğlan", "vrgaming": "vroyunları", "drdisrespect": "drdisrespect", "4kgaming": "4koyunçuluq", "gamerbr": "gamerbür", "gameplays": "oyunlar", "consoleplayer": "konsoloyuncusu", "boxi": "boxi", "pro": "pe<PERSON><PERSON><PERSON>", "epicgamers": "epikoyunçular", "onlinegaming": "onlaynoyunlar", "semigamer": "yarımoyunçu", "gamergirls": "oyunqızları", "gamermoms": "<PERSON><PERSON><PERSON><PERSON>", "gamerguy": "oyunçuyoğlan", "gamewatcher": "oyunbaxan", "gameur": "oyunçu", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "qızgamerlər", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "komandacalisir", "mallugaming": "malluoyunları", "pawgers": "pawgers", "quests": "<PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "köhnəoyunçular", "cozygaming": "cozygaming", "gamelpay": "gamelpay", "juegosdepc": "kompüteroyunları", "dsswitch": "dsswitch", "competitivegaming": "rəqabətliolimpiya", "minecraftnewjersey": "minecraftyeniçika", "faker": "feykər", "pc4gamers": "komp4gamerlər", "gamingff": "oyunçular", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "oyunpc", "girlsgamer": "qızgamer", "fnfmods": "fnfmodları", "dailyquest": "gündəliktapşırıq", "gamegirl": "oyunqızı", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "oyunquruluşu", "overpowered": "güclülaşdırılmış", "socialgamer": "sosialgamer", "gamejam": "oyunjamı", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "komandam", "republicofgamers": "gamersdöv<PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "triplelegend", "gamerbuddies": "oyunçuyoldaşlar", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "oyunçuygeek", "nerdgamer": "nerdoyunçu", "afk": "afk", "andregamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgamer": "rahatoyunçusu", "89squad": "89squad", "inicaramainnyagimana": "inicaraçınneyagimana", "insec": "baxış", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "oyuncuq<PERSON>bi", "lanparty": "<PERSON><PERSON><PERSON><PERSON>", "videogamer": "videogamer", "wspólnegranie": "birlikdələrkən", "mortdog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationgamer": "playstationoyuncusu", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "sağlamgamer", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "prototip", "womangamer": "qızoyunçusu", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "toplayıcı", "humanfallflat": "insanq<PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "sıfıraçıq", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusiqi", "sonicthehedgehog": "sonikdovşanı", "sonic": "sonik", "fallguys": "payızoğlanları", "switch": "dəyi<PERSON><PERSON>k", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "göyvalilərişəfqədəsində", "tomodachilife": "tomodachihəyatı", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "krallığıngözyaşları", "walkingsimulators": "gəzinti_simulyatorları", "nintendogames": "nintendogames", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "nəsnəovusu", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "seleste", "breathofthewild": "vəhşinəfəs", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktay", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "üçbucaqstrategiyası", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kestanelərpisişgünü", "nintendos": "nintendos", "new3ds": "yeni3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hry<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartyulduzları", "marioandsonic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendoglar", "thezelda": "zelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "yumimayım", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cbkxd", "leagueoflegendslas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "urgot": "urgot", "zyra": "zyra", "redcanids": "qırmızıcanavarlar", "vanillalol": "vanillalol", "wildriftph": "wildriftaz", "lolph": "gülürəmph", "leagueoflegend": "legend<PERSON><PERSON><PERSON>liyası", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leykoflegendswild", "adcarry": "reklamdaşıyır", "lolzinho": "lolçü", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "gülüşdür<PERSON>r", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "şako", "ligadaslegendas": "legendalarlabağlı", "gaminglol": "oyunlarhaşir", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexqapıları", "hextech": "hektek", "fortnitegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingfortnite": "fortniteoyunları", "fortnitebr": "fortniteaz", "retrovideogames": "retrooyun<PERSON>", "scaryvideogames": "dəhşətvideolar", "videogamemaker": "oyunistehsalçısı", "megamanzero": "megamanzero", "videogame": "videoyun", "videosgame": "<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "cadu101", "battleblocktheater": "battleblockteatr", "arcades": "oyunxanalar", "acnh": "acnh", "puffpals": "puffdostları", "farmingsimulator": "kəndtəsərrüfatısimulyatoru", "robloxchile": "robloxçili", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxalemania", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sandboxoyunları", "videogamelore": "oyunlardünyası", "rollerdrome": "rollerdrom", "parasiteeve": "parazitpərəstişkarı", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "xəyaldünyası", "starcitizen": "ul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ni", "yanderesimulator": "yanderesimulator", "grandtheftauto": "<PERSON><PERSON><PERSON>ü<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "ölüboşluq", "amordoce": "mohabbətşirniyyatı", "videogiochi": "videogiochi", "theoldrepublic": "köhnərespublikası", "videospiele": "videomüharibə", "touhouproject": "touhouproject", "dreamcast": "xəyalcast", "adventuregames": "məmacoyunları", "wolfenstein": "wolfenstein", "actionadventure": "hərəkətma<PERSON>əra", "storyofseasons": "mövsümdəheka<PERSON>lər", "retrogames": "retrogames", "retroarcade": "retroarcade", "vintagecomputing": "nostaljikompüterlər", "retrogaming": "retro<PERSON>un", "vintagegaming": "<PERSON><PERSON><PERSON>əoyunlar", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "komandirəngüclü", "bugsnax": "bugsnax", "injustice2": "haqsızlıq2", "shadowthehedgehog": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "göyoyunu", "zenlife": "zenhəyatı", "beatmaniaiidx": "beatmaniaiidx", "steep": "dərin", "mystgames": "mystgames", "blockchaingaming": "blokçeynoyunları", "medievil": "ortaəsr", "consolegaming": "konsoloyunları", "konsolen": "konsolen", "outrun": "qaçışağaçsan", "bloomingpanic": "çiçəklənənpanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "oyunxoru", "monstergirlquest": "canavargözə<PERSON><PERSON>lunda", "supergiant": "supergigant", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "<PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON>əoyunlar", "bethesda": "bethesda", "jackboxgames": "jackboxoyunları", "interactivefiction": "interaktivnəsr", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "sevgililərəgözyaşı", "visualnovel": "vizualroman", "visualnovels": "vizualromanlar", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "maaşgü<PERSON><PERSON>", "chatherine": "katirinə", "twilightprincess": "alac<PERSON>bal<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "estetikoyunlar", "novelavisual": "yenişə<PERSON>l", "thecrew2": "komanda2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retro<PERSON>un", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "il<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "səviyyədizaynı", "starrail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyblade": "ključpadı", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizualromance", "robloxbrasil": "robloxazərbaycan", "pacman": "pacman", "gameretro": "retrooyun<PERSON>", "videojuejos": "videooyun<PERSON>", "videogamedates": "videooyunqatılmaları", "mycandylove": "candyloveum", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "həmişəbəxt", "justcause3": "sadəcəsəbəb3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "hesablaşmanınqayıdışı", "gamstergaming": "gamstergaming", "dayofthetantacle": "tentaklənmənin_günü", "maniacmansion": "maniacmansion", "crashracing": "çarpışmaqaçış", "3dplatformers": "3dplatformerlər", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "köhnəməktəboyunları", "hellblade": "cəhənnəmqılıncı", "storygames": "hekayəoyunları", "bioware": "bioware", "residentevil6": "rezidentbad6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "beyondikidoğul", "gameuse": "oyunistifadə", "offmortisghost": "ölüghostunqurtarılması", "tinybunny": "kiçikquzu", "retroarch": "retroarch", "powerup": "g<PERSON><PERSON><PERSON><PERSON>n", "katanazero": "katana0", "famicom": "famikom", "aventurasgraficas": "adventurasiqra<PERSON><PERSON>", "quickflash": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ffaf", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcadlar", "f123": "f123", "wasteland": "d<PERSON>ş<PERSON><PERSON><PERSON>", "powerwashsim": "gü<PERSON><PERSON><PERSON><PERSON>yan<PERSON><PERSON><PERSON>", "coralisland": "korallıada", "syberia3": "syberia3", "grymmorpg": "grimmmorpg", "bloxfruit": "bloxfruit", "anotherworld": "başqa_dünya", "metaquest": "<PERSON><PERSON><PERSON><PERSON>t", "animewarrios2": "animesavaşçıları2", "footballfusion": "futbolbirliyi", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "leykomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "qarmaqarışıqmetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "utancxanası", "simulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symulatory": "simulyatorlar", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>çışı", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "şahlandworld", "skylander": "göydaşçı", "boyfrienddungeon": "oğlanzarğını", "toontownrewritten": "toontownyenidənyazılıb", "simracing": "simracing", "simrace": "simyarış", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı", "heavenlybodies": "göylərəqaldıranbədənlər", "seum": "seum", "partyvideogames": "partiyavideoyunları", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "kosmonavtlari̇çinsimulyator", "legacyofkain": "kaininmirası", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "yemə<PERSON><PERSON><PERSON><PERSON>unlar", "oyunvideoları": "oyunvideo", "thewolfamongus": "şirilərbirimizlə", "truckingsimulator": "yükdaşımasimulasyonu", "horizonworlds": "horizonworlds", "handygame": "üstgüzgüoyunu", "leyendasyvideojuegos": "videooyunlarınlegendası", "oldschoolvideogames": "köhnəməktəbvideooyunları", "racingsimulator": "yarışsimulyatoru", "beemov": "bee<PERSON>v", "agentsofmayhem": "tə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "<PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "olimpınqapıları", "monsterhunternow": "dəhşətovçulartamindeyik", "rebelstar": "üsyançıuşağı", "indievideogaming": "indievideoyuğruları", "indiegaming": "indiegaming", "indievideogames": "indievideoyouduqları", "indievideogame": "indievideoyunlar", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "buffqalaq", "unbeatable": "müqavimətsiz", "projectl": "lay<PERSON><PERSON>", "futureclubgames": "gələcəkkluboyunları", "mugman": "kubokadam", "insomniacgames": "y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "superbooyanoyunları", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "açıqelm_sayəsində", "backlog": "qəzəş", "gamebacklog": "oyunqalıq", "gamingbacklog": "oyunlarbazası", "personnagejeuxvidéos": "şəxsiyyətvideooyunlar", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermaymuntopu", "deponia": "deponia", "naughtydog": "pısqırıqit", "beastlord": "<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "retrooyun<PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dopaminrezervatoriyası", "staxel": "staxel", "videogameost": "videoyer<PERSON>əsəsi", "dragonsync": "dragonzinklər", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pclənc", "berserk": "<PERSON>ı<PERSON><PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "ilkd", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "kədə<PERSON>ianime", "darkerthanblack": "qara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animeölçmə", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "pəs<PERSON><PERSON><PERSON>r", "retroanime": "retroanime", "animes": "anime", "supersentai": "superdostlar", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "qaralarxası", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "şarjəmen", "animecover": "animeüstlük", "thevisionofescaflowne": "eskaflowneninvisionu", "slayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90lar", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "bananafish", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "tualetəbağlıhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "odquma", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "gə<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "abyssdəisteh<PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mush<PERSON><PERSON><PERSON>", "beastars": "canavarlar", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "dənizqızımelodiyası", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romansmangaları", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonmaid", "blacklagoon": "qaraçaylıgöl", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "zəkaşirkəti", "shamanking": "şamanqinq", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "cjujutsu", "yurionice": "y<PERSON>ha<PERSON><PERSON>ında", "acertainmagicalindex": "birnağılgöstə<PERSON>i", "sao": "sao", "blackclover": "qaraşahlandır", "tokyoghoul": "tokyoghoul", "onepunchman": "birvu<PERSON><PERSON>", "hetalia": "<PERSON><PERSON><PERSON>", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8sonsuzluq", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsofdeath": "ö<PERSON><PERSON><PERSON><PERSON>l<PERSON>kləri", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosimzik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "idmananime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "mələklərbatareyası", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyadərrak", "shounenanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "gözəldavalı", "theboyandthebeast": "oğlanvəd<PERSON>şət", "fistofthenorthstar": "şimalınuşağı", "mazinger": "mazinger", "blackbuttler": "qaraşeytan", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "çibi", "servamp": "servamp", "howtokeepamummy": "mummyüsağtutmağıbilmək", "fullmoonwosagashite": "doluaybulsusaga<PERSON><PERSON>", "shugochara": "şugochara", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "şirinv<PERSON>q<PERSON><PERSON><PERSON>", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "böyüknəticəqız", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "<PERSON><PERSON><PERSON>", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "canavarqız", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "mə<PERSON><PERSON>u", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "görü<PERSON>ü", "luci": "luci", "reigen": "reigen", "scaramouche": "<PERSON><PERSON><PERSON><PERSON>", "amiti": "amiti", "sailorsaturn": "dəmirç<PERSON>ərsaturn<PERSON>", "dio": "dio", "sailorpluto": "dənizçipluto", "aloy": "aloy", "runa": "runa", "oldanime": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainsawman": "<PERSON>əncirzəh<PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "oyna", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "qaraicarqədim", "ergoproxy": "ergoproxy", "claymore": "k<PERSON><PERSON>", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "meyvəkörf<PERSON>si", "devilmancrybaby": "şeytanadamqışqırır", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "senin", "lovelive": "sevgi<PERSON><PERSON><PERSON>şamaq", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "mənimbaşqatransformatorum", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "və<PERSON><PERSON><PERSON>mayanölkə", "monstermanga": "canavarjurnal", "yourlieinapril": "səninapreldekiyalanın", "buggytheclown": "böcəkgülkəndi", "bokunohero": "bokunohero", "seraphoftheend": "sonunserafı", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "dərin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "jojolion": "jojo<PERSON>", "deadmanwonderland": "ölüadamlıcənnət", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "yeməkdö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "kartoyunçusakura", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "sənəəbədiyyat", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "mavi_dönəm", "griffithberserk": "gri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinigami": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretalliance": "giz<PERSON>ittifaq", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "silməmiş", "bluelock": "mavi<PERSON><PERSON>", "goblinslayer": "goblinqıran", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "şiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riaskremor", "shojobeat": "shojobeat", "vampireknight": "vampircavabı", "mugi": "mugi", "blueexorcist": "maviəksor<PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "casus<PERSON><PERSON><PERSON>", "airgear": "havaqayğı", "magicalgirl": "magiqq<PERSON>z", "thesevendeadlysins": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "məktə<PERSON><PERSON><PERSON><PERSON>haqqı<PERSON>allaha", "kissxsis": "öpüşvəsis", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animealəmi", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoşizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ö<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>", "romancemanga": "romansmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromans", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinalı", "lolicon": "lolikon", "demonslayertothesword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>p<PERSON><PERSON>", "bloodlad": "qanlıdost", "goodbyeeri": "vidaelvida", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "ul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r", "romanceanime": "romansanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "albalimagic", "housekinokuni": "e<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "ölümcülmə<PERSON>əb", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "qatilodr<PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "ölümkarvanı", "shokugekinosouma": "şokugekinosouma", "japaneseanime": "yapo<PERSON><PERSON>", "animespace": "animealemi", "girlsundpanzer": "qızlarundpanzer", "akb0048": "akb0048", "hopeanuoli": "umid<PERSON>la", "animedub": "animealtında", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON>", "indieanime": "müstəqilanimas<PERSON>", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gündəm0", "animescifi": "animeelmimp3", "ratman": "şişmanadam", "haremanime": "harem<PERSON><PERSON>m", "kochikame": "ko<PERSON><PERSON><PERSON>", "nekoboy": "nekooğlan", "gashbell": "gashbell", "peachgirl": "şaftalıqız", "cavalieridellozodiaco": "qədimzodiakçılari", "mechamusume": "maçamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklubu", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "zindandağ<PERSON>əlis", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "əyləncəlitəhsil", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "şuumatsunovalkyrie", "tutorialistoohard": "tutorialçox<PERSON><PERSON><PERSON>dir", "overgeared": "üstünavadanlıq", "toriko": "<PERSON><PERSON>o", "ravemaster": "raveustası", "kkondae": "kkondae", "chobits": "çobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON>ı<PERSON>", "dropsofgod": "tanrıdamdamlar", "loscaballerosdelzodia": "zodiakıncavalieri", "animeshojo": "animeshojo", "reverseharem": "qarşıtharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "böyükmüəllimonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "böyükmaviarzu", "bloodplus": "qanartıplus", "bloodplusanime": "qanani<PERSON>", "bloodcanime": "qanani<PERSON>", "bloodc": "qanş<PERSON><PERSON><PERSON>qi", "talesofdemonsandgods": "iblisvətanrılarhekayələri", "goreanime": "animeyoxdur", "animegirls": "animeqızlar", "sharingan": "bölüşdükcə", "crowsxworst": "qaraquşlarxə<PERSON>ə<PERSON>ər", "splatteranime": "sp<PERSON><PERSON><PERSON><PERSON>", "splatter": "splaşdır", "risingoftheshieldhero": "kalkanqanqəhrəmanınyüksəlişi", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "yeyməkdayanmışam", "animeyuri": "animeyuri", "animeespaña": "animeispaniya", "animeciudadreal": "animeciudadreal", "murim": "<PERSON><PERSON><PERSON><PERSON>", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "balıqlar<PERSON>nuşaqları", "liarliar": "yalançıyalançı", "supercampeones": "superçempionlar", "animeidols": "animeidollları", "isekaiwasmartphone": "isekaiyaçıqsmartfondu", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "müq<PERSON>dd<PERSON>sq<PERSON>zlar", "callofthenight": "gecəylədəyik", "bakuganbrawler": "bakuganbrawlçı", "bakuganbrawlers": "bakugandö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "kölgəbağçası", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "qızcığazjellyfish", "kuragehime": "k<PERSON><PERSON><PERSON><PERSON>", "paradisekiss": "cənnətöpüşü", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animealəm", "persocoms": "persocoms", "omniscientreadersview": "bilgilireadervisi", "animecat": "animepisi", "animerecommendations": "animetövsi<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "açılışanime", "shinichirowatanabe": "şini<PERSON>rovatānabe", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mənimgənclikromantikkomediyam", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "böyükrobotlar", "neongenesisevangelion": "neonjenesisəvangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobildöyüşçüsüggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilkostyumboyundum", "mech": "mech", "eurekaseven": "eureka7", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "soda", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "buz", "jojobizarreadventures": "jojobizaraekspedisiyaları", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "hə<PERSON><PERSON><PERSON>", "greenranger": "yaşılşövalye", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animeazərbaycanca", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoşip<PERSON>den", "onepiece": "birparça", "animeonepiece": "animebirparça", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonmacəra", "hxh": "hxh", "highschooldxd": "ortaməktəbdxd", "goku": "goku", "broly": "broly", "shonenanime": "şonenanime", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "şonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "titanlaraqarşıhücum", "erenyeager": "yeməyiçmək", "myheroacademia": "mücərrədg<PERSON>hr<PERSON><PERSON>ə<PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "anketkollektiv", "onepieceanime": "onepieceanime", "attaquedestitans": "ataquedestitans", "theonepieceisreal": "birparçahəqiqətdir", "revengers": "intiqamçılar", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "sevinçoglanıeffekti", "digimonstory": "digimonheka<PERSON>ə<PERSON>", "digimontamers": "dijimontamers", "superjail": "superzin<PERSON>", "metalocalypse": "metalocalypse", "shinchan": "şinçan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "bizimyanhostclub", "flawlesswebtoon": "mükəmməlwebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "uçancadıqlı", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "sadəcəəvəzinə", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "həyatınhesabatları"}