{"2048": "2048", "mbti": "mbti", "enneagram": "enneagrama", "astrology": "astrologia", "cognitivefunctions": "funcionscognitives", "psychology": "psicologia", "philosophy": "filosofia", "history": "història", "physics": "física", "science": "ciència", "culture": "cultura", "languages": "idiomes", "technology": "tecnologia", "memes": "mems", "mbtimemes": "memsmbti", "astrologymemes": "memsastrologia", "enneagrammemes": "memsenneagrama", "showerthoughts": "pensamentsaladutxa", "funny": "divertit", "videos": "vídeos", "gadgets": "ginys", "politics": "política", "relationshipadvice": "consellrelacions", "lifeadvice": "consellsvida", "crypto": "cripto", "news": "notícies", "worldnews": "notíciesdelmón", "archaeology": "arqueologia", "learning": "aprendre", "debates": "debats", "conspiracytheories": "teoriesconspiració", "universe": "univers", "meditation": "meditació", "mythology": "mitologia", "art": "art", "crafts": "artesania", "dance": "ball", "design": "disseny", "makeup": "maqui<PERSON><PERSON><PERSON>", "beauty": "bellesa", "fashion": "moda", "singing": "cant", "writing": "escriptura", "photography": "fotografia", "cosplay": "cosplay", "painting": "pintura", "drawing": "dibuix", "books": "llibres", "movies": "pell<PERSON><PERSON>s", "poetry": "poesia", "television": "televisió", "filmmaking": "cine", "animation": "animaci<PERSON>", "anime": "anime", "scifi": "ciènciaficció", "fantasy": "fantasia", "documentaries": "documentals", "mystery": "misteri", "comedy": "comèdia", "crime": "crim", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "terror", "romance": "romanç", "realitytv": "realitytv", "action": "acció", "music": "música", "blues": "blues", "classical": "clàssica", "country": "country", "desi": "desi", "edm": "edm", "electronic": "electrònica", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "llatina", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tecno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "concerts", "festivals": "festivals", "museums": "museus", "standup": "monòlegs", "theater": "teatre", "outdoors": "airelliure", "gardening": "jardineria", "partying": "festes", "gaming": "videojocs", "boardgames": "jocs<PERSON><PERSON><PERSON>", "dungeonsanddragons": "drac<PERSON><PERSON><PERSON><PERSON>", "chess": "escacs", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "gastronomia", "baking": "rebosteria", "cooking": "cuina", "vegetarian": "vegetarià", "vegan": "vegà", "birds": "ocells", "cats": "gats", "dogs": "gossos", "fish": "peix", "animals": "animals", "blacklivesmatter": "blacklivesmatter", "environmentalism": "mediambient", "feminism": "feminisme", "humanrights": "dretshumans", "lgbtqally": "prolgtbiq", "stopasianhate": "estopodiasiàtics", "transally": "protrans", "volunteering": "voluntariat", "sports": "esports", "badminton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseball": "beisbol", "basketball": "b<PERSON><PERSON><PERSON>", "boxing": "boxa", "cricket": "criquet", "cycling": "ciclisme", "fitness": "fitness", "football": "futbol", "golf": "golf", "gym": "gimnàs", "gymnastics": "gimnàstica", "hockey": "hoquei", "martialarts": "artsmarcials", "netball": "netbol", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON><PERSON>", "skateboarding": "patinatgedemonopatí", "skiing": "esquí", "snowboarding": "surfdeneu", "surfing": "surf", "swimming": "na<PERSON><PERSON>ó", "tennis": "tennis", "volleyball": "voleibol", "weightlifting": "halterofília", "yoga": "ioga", "scubadiving": "submarinisme", "hiking": "senderisme", "capricorn": "capricorn", "aquarius": "aquari", "pisces": "peixos", "aries": "àries", "taurus": "taure", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON><PERSON>", "leo": "lleó", "virgo": "verge", "libra": "balança", "scorpio": "escorpió", "sagittarius": "sagitari", "shortterm": "<PERSON><PERSON><PERSON><PERSON>", "casual": "informal", "longtermrelationship": "rela<PERSON><PERSON><PERSON><PERSON>", "single": "solter", "polyamory": "poliamor", "enm": "relaciónomonògama", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gai", "lesbian": "lesbiana", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "santsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "cansdeguàrdia", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "questsdelreis", "soulreaver": "recollecciódsoul", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiu", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "postasoleil", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinit", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "mónobert", "heroesofthestorm": "heroisdeltempest", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "explorandmasmòrbits", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribusdemidgard", "planescape": "plaboo", "lordsoftherealm2": "lordsdelreialm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsdellarealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "deshonorat", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "caiguda", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "creaciodepersonatges", "immersive": "immersiu", "falloutnewvegas": "falloutnovavegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "fantasifinal", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivaciómorbosa", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "aficionatdelamor", "otomegames": "joc<PERSON><PERSON>e", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirelamasquerada", "dimension20": "dimensio20", "gaslands": "gaslands", "pathfinder": "cercadordecamins", "pathfinder2ndedition": "pathfinder2naedicio", "shadowrun": "sombrejo<PERSON>", "bloodontheclocktower": "sangalrellotge", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravetatemp<PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "unsolt", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "sobir<PERSON>", "yourturntodie": "elteutornpermorir", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "a<PERSON><PERSON>", "diablo2": "diablo2", "diablo2lod": "diable2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "dimoniosoul", "mu": "mu", "falloutshelter": "refugiapocalíptic", "gurps": "gurps", "darkestdungeon": "dungeonmésfosc", "eclipsephase": "faseeclíptica", "disgaea": "disgaea", "outerworlds": "mónsex<PERSON>s", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diableimmortal", "dynastywarriors": "guerre<PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "nitciutat", "hogwartslegacy": "legadodehogwarts", "madnesscombat": "combatmadness", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "camí96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "regnesoblidats", "dragonlance": "draconallança", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "ne<PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanvers", "fracturedthrones": "tronesttrencats", "horizonforbiddenwest": "horitzontprohibitoest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "misterihogwarts", "deltagreen": "deltaverde", "diablo": "diable", "diablo3": "diable3", "diablo4": "diable4", "smite": "smite", "lastepoch": "darrerepoca", "starfinder": "trobadordestrelles", "goldensun": "soldaurora", "divinityoriginalsin": "divinitatoriginalsin", "bladesinthedark": "fullesennotombra", "twilight2000": "crepuscle2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkvermell", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "terresmalèfiques", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diablosurvivor", "oldschoolrunescape": "runescapeantiquat", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinitat", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "bluesdelloldworld", "adventurequest": "aventuraventura", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "jocsderol", "roleplayinggames": "jocsderol", "finalfantasy9": "finalfantasy9", "sunhaven": "solhaven", "talesofsymphonia": "contesdesimfonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "<PERSON><PERSON><PERSON>", "sacredunderworld": "sacredunderworld", "chainedechoes": "ecosenxarxats", "darksoul": "ànimafosca", "soulslikes": "soulslikes", "othercide": "altreside", "mountandblade": "muntanyaiespasa", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "cronotrigger", "pillarsofeternity": "pilarsdaleternitat", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "eldivisió", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "fillets<PERSON><PERSON><PERSON>", "engineheart": "motorcor", "fable3": "fable3", "fablethelostchapter": "fableel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenetern", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "campestel", "oldschoolrevival": "revivaloldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mónsal<PERSON><PERSON>", "diabloiv": "diableiv", "pve": "pve", "kingdomheart1": "regnatdelcors1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "ma<PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "jocsrpg", "kingdomhearts": "regnecor", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "colli<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "corssalvatges", "bastion": "bastió", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "celsdarcadia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "bufdofoc4", "mother3": "mare3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "jocsderole", "roleplaygame": "jocderol", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "camí2e", "vampirilamasquerade": "vampirilatempesta", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonageorígens", "chronocross": "cronocross", "cocttrpg": "cocttrpg", "huntroyale": "caçaroial", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "caçamonstresmón", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "covenantdelcorombra", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "reinoqueve", "awplanet": "awplanet", "theworldendswithyou": "elmundacabaambtu", "dragalialost": "dragalialost", "elderscroll": "scrollsancians", "dyinglight2": "morintluz2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "heretgig<PERSON>t", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magiaterrestre", "blackbook": "llibrenegre", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "ediciódor_sagrada", "castlecrashers": "castellxocadors", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "jocsderol", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "ciutatdelboira", "indierpg": "indierpg", "pointandclick": "puntiferra", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisible", "freeside": "llibreside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "mortpercanadà", "palladium": "palladium", "knightjdr": "cavallerjdr", "monsterhunter": "caçamonstres", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremàcia", "persona5": "persona5", "ghostoftsushima": "fantasmetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "caçadoramonstresalçades", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "joc<PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtàctic", "mahoyo": "mahoyo", "animegames": "joc<PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeternal", "princessconnect": "princessconnect", "hexenzirkel": "círculdelasbruixes", "cristales": "cristalls", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketsage", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valorantindià", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "lligadesomniadors", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "lligaoverwatch", "cybersport": "cibersport", "crazyraccoon": "r<PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "ecurses", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitiu", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "vàlvula", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "estiuetern", "goatsimulator": "simuladordecabres", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "llibertatplanet", "transformice": "transformice", "justshapesandbeats": "nomésformesipulsacions", "battlefield4": "campdebatalla4", "nightinthewoods": "nitsalboscos", "halflife2": "halflife2", "hacknslash": "tallaixorra", "deeprockgalactic": "minadelrofcosmic", "riskofrain2": "riscdepluja2", "metroidvanias": "metroidvanias", "overcooked": "sobrecuinat", "interplanetary": "interplanetari", "helltaker": "<PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscripció", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortalesadelsnans", "foxhole": "foxhole", "stray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield": "campdebatalla", "battlefield1": "campodebatalla1", "swtor": "swtor", "fallout2": "caigudament2", "uboat": "uboat", "eyeb": "ullb", "blackdesert": "desertnegre", "tabletopsimulator": "simuladordetablers", "partyhard": "festaambenergia", "hardspaceshipbreaker": "duroscombradesnaves", "hades": "hades", "gunsmith": "armesmith", "okami": "<PERSON>ami", "trappedwithjester": "atrapatambjester", "dinkum": "dinkum", "predecessor": "predecessor", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "coves<PERSON><PERSON><PERSON>", "colonysim": "colonysim", "noita": "<PERSON><PERSON><PERSON>", "dawnofwar": "albadelaguerra", "minionmasters": "minionmasters", "grimdawn": "albaherida", "darkanddarker": "foscimésfosc", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "treball<PERSON>adesoul", "datingsims": "jocsdequotes", "yaga": "yaga", "cubeescape": "escapealcub", "hifirush": "hifirush", "svencoop": "svencoopca", "newcity": "novacitat", "citiesskylines": "siluetadesciutats", "defconheavy": "defconpesat", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiavirtual", "snowrunner": "nevatrunner", "libraryofruina": "bibliotecaderuina", "l4d2": "l4d2", "thenonarygames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "cercadordecamins", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "patodeplàsticcalmat", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "so<PERSON><PERSON><PERSON>per<PERSON>", "catnight": "nitsdegat", "supermeatboy": "supermeatboy", "tinnybunny": "conyetiny", "cozygrove": "cozygrove", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "codi", "borderlands": "terresfrontereres", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "jocsfar<PERSON>ry", "paladins": "paladins", "earthdefenseforce": "forçadefensadelaterra", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "granrobatoriauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "uneixtealquip", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgenciadapolsandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "mortconectant", "b4b": "b4b", "codwarzone": "codguerraworld", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzomb<PERSON>", "mirrorsedge": "mirrorsedge", "divisions2": "divisions2", "killzone": "zonamortal", "helghan": "hel<PERSON>", "coldwarzombies": "guerrafreddazombis", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "codiatravessat", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "guerramoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "terraesbor<PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>cap<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnatgeprimal", "worldofwarships": "mundodebucles", "back4blood": "tornarem4sang", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "contractor", "masseffect": "masseffect", "systemshock": "xocdelsistema", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killsfloor2", "cavestory": "cavestory", "doometernal": "doometernal", "centuryageofashes": "segleageidescendres", "farcry4": "farcry4", "gearsofwar": "rodesdeguerrers", "mwo": "mwo", "division2": "divisió2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generaciózero", "enterthegungeon": "entrathegun<PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "guerremoderna2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "eldolorfantom", "warface": "guerrafacial", "crossfire": "foccreuat", "atomicheart": "<PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampirsobrevivents", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "llibertat", "battlegrounds": "camposdebatalla", "frag": "fraga", "tinytina": "<PERSON><PERSON>", "gamepubg": "jocpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearfillsdellibertat", "juegosfps": "jocsfps", "convertstrike": "converteixladestraquejada", "warzone2": "warzone2", "shatterline": "trencalínies", "blackopszombies": "zombisblackops", "bloodymess": "merda<PERSON>ta", "republiccommando": "republiccommando", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldat", "groundbranch": "terratot", "squad": "colla", "destiny1": "destí1", "gamingfps": "jocfps", "redfall": "caiguda<PERSON>l<PERSON>", "pubggirl": "pubggirl", "worldoftanksblitz": "munddelsbotsblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "enrolat", "farlight": "llumdelfoc", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "la<PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "paguem2", "cs16": "cs16", "pubgindonesia": "pubgindonèsia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgtxec", "titanfall2": "titanfall2", "soapcod": "sabócod", "ghostcod": "fantasmatcod", "csplay": "csplay", "unrealtournament": "tournamentirreals", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "contraatac", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "campionsdelterremot", "halo3": "halo3", "halo": "halo", "killingfloor": "terraassassina", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "cèllula<PERSON>plinter", "neonwhite": "neonwhite", "remnant": "restes", "azurelane": "a<PERSON><PERSON><PERSON>", "worldofwar": "mónsdeguerrers", "gunvolt": "gunvolt", "returnal": "retornal", "halo4": "halo4", "haloreach": "holaal<PERSON><PERSON>", "shadowman": "ombrehome", "quake2": "terrabruta2", "microvolts": "microvolts", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "campodebatalla3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "maridelslladres", "rust": "òxid", "conqueronline": "conquereonline", "dauntless": "valent", "warships": "vaixellsdeguerra", "dayofdragons": "diadelosdragons", "warthunder": "warthunder", "flightrising": "alçadelsvols", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "fantasyestelonline2", "maidenless": "sensenoia", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "munddetancs", "crossout": "tret<PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "segonavida", "aion": "aion", "toweroffantasy": "torredefantasi<PERSON>", "netplay": "jocnet", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "còdigevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguí", "lotro": "lotro", "wakfu": "wakfu", "scum": "escòria", "newworld": "nuevmón", "blackdesertonline": "blackdesertonline", "multiplayer": "multijugador", "pirate101": "pirata101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "batalladesestrelles", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "xat3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "mónwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cendresdecreació", "riotmmo": "riotmmo", "silkroad": "camídelased", "spiralknights": "espiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "venjança", "albiononline": "albiononline", "bladeandsoul": "espasaesmà", "evony": "evony", "dragonsprophet": "dragonsprophet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "<PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversonline", "growtopia": "creixotopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "granfantasia", "blueprotocol": "protocolblau", "perfectworld": "mónperfecte", "riseonline": "pujasonline", "corepunk": "corepunk", "adventurequestworlds": "aventuracercamonde", "flyforfun": "volaparafestacar", "animaljam": "animaljam", "kingdomofloathing": "regnidelsentiments", "cityofheroes": "ciu<PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "cavallbuit", "metalgearsolid": "metalgearsolid", "forhonor": "perhonor", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtufighter", "streetsofrage": "carrersdelafuriosa", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "elkingdelslluitadors", "likeadragon": "comunicorn", "retrofightinggames": "jocsdefitnesretro", "blasphemous": "blasfem", "rivalsofaether": "rivalsdelaether", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "guerradelmonstres", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "ciberbots", "armoredwarriors": "guerrersblin<PERSON><PERSON>", "finalfight": "lluitafinal", "poweredgear": "equipamentpotent", "beatemup": "pallassada", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "jocsdeforça", "killerinstinct": "instintassassí", "kingoffigthers": "reidelslluitadors", "ghostrunner": "fantasmat", "chivalry2": "cavallerositat2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelhollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongjo<PERSON>", "silksongnews": "notíciesdelsilksong", "silksong": "silksong", "undernight": "debaixdelainit", "typelumina": "tipuslumina", "evolutiontournament": "torneigevolució", "evomoment": "evomoment", "lollipopchainsaw": "piruletesmortals", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "històriesdeberseria", "bloodborne": "<PERSON><PERSON><PERSON><PERSON>", "horizon": "<PERSON><PERSON><PERSON>", "pathofexile": "camídelexili", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "sangbentornat", "uncharted": "desconegut", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "infamous", "playstationbuddies": "amicsdeplaystation", "ps1": "ps1", "oddworld": "m<PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "queesdeslligui", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "trove", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "finsaldema<PERSON><PERSON>", "touristtrophy": "tropitourist", "lspdfr": "lspdfr", "shadowofthecolossus": "lombredecolossus", "crashteamracing": "crashteamracing", "fivepd": "cincpd", "tekken7": "tekken7", "devilmaycry": "diablenoplaç", "devilmaycry3": "diabloppotplorar3", "devilmaycry5": "diablotpuged<PERSON><PERSON>", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "guerre<PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "lultimguardia", "soulblade": "llanternestela", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "caçamanesc", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "lultimguardia", "xboxone": "xboxone", "forza": "força", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "nitedelcombatchampió", "psychonauts": "psiconautes", "mhw": "mhw", "princeofpersia": "príncexdelsperits", "theelderscrollsskyrim": "lescrollsdelveteraneskyyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elsgranscrolls", "gxbox": "gxboc", "battlefront": "frontdebatalla", "dontstarvetogether": "nòvagennoustarvetogether", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "estellimitat", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "flipadoradecases", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "lligadelregnes", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telebasura", "skycotl": "celcotl", "erica": "erica", "ancestory": "ancestors", "cuphead": "cuphead", "littlemisfortune": "petitamalasort", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstreprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "extremerespesques", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultdelvell", "duckgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "lestanleparàbola", "towerunite": "torreunida", "occulto": "oculto", "longdrive": "llargviatge", "satisfactory": "satisfactori", "pluviophile": "pluviòfil", "underearth": "sotaterra", "assettocorsa": "assettocorsa", "geometrydash": "geometridash", "kerbal": "kerbal", "kerbalspaceprogram": "programaespecialkerbal", "kenshi": "kenshi", "spiritfarer": "espíritallibertador", "darkdome": "domefosc", "pizzatower": "pizzatower", "indiegame": "jocindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "veritatodret", "game": "joc", "rockpaperscissors": "pedrapaper<PERSON><PERSON><PERSON>", "trampoline": "trampolí", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "repte", "scavengerhunt": "caceradetresors", "yardgames": "jocsdeljard<PERSON>", "pickanumber": "tria<PERSON><PERSON>", "trueorfalse": "veritatofals", "beerpong": "beerpong", "dicegoblin": "tiradaus", "cosygames": "jocsacollonits", "datinggames": "jocsdecites", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "jocsdebeguda", "sodoku": "sudoku", "juegos": "jocs", "mahjong": "mahjong", "jeux": "jocs", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "jocsdepar<PERSON><PERSON>", "jeuxdemots": "jocdemots", "juegosdepalabras": "jocsdepar<PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "jocsdepaletes", "oyun": "joc", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "concurs<PERSON><PERSON><PERSON>", "spiele": "juga", "giochi": "jocs", "geoguessr": "geoguessr", "iphonegames": "jocsiphone", "boogames": "boojo<PERSON>", "cranegame": "joc<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "amagaribuscar", "hopscotch": "xarranca", "arcadegames": "jocsdarcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "jocclàssic", "mindgames": "jocsdamentals", "guessthelyric": "endevinae<PERSON><PERSON>", "galagames": "galajocs", "romancegame": "jocderomance", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "trencaclosques", "4xgames": "4xjocs", "gamefi": "gamefi", "jeuxdarcades": "jocsdarcade", "tabletopgames": "jocs<PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "jocs90", "idareyou": "etreclamo", "mozaa": "mozaa", "fumitouedagames": "fumitouedajocs", "racinggames": "jocsderàpidament", "ets2": "ets2", "realvsfake": "realvsfals", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "jocsonline", "onlinegames": "j<PERSON><PERSON><PERSON><PERSON>", "jogosonline": "jocsonline", "writtenroleplay": "rolescrit", "playaballgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "j<PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiijocs", "highscore": "puntuaciómáxima", "jeuxderôles": "jocsder<PERSON>", "burgergames": "jocsdehamburguesa", "kidsgames": "jocsinfantils", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "jocdpreguntes", "gioco": "joc", "managementgame": "jocdemanagement", "hiddenobjectgame": "jocdobjectesamagats", "roolipelit": "jocsdemobils", "formula1game": "jocdeformula1", "citybuilder": "constructoradeciutats", "drdriving": "drivingdriving", "juegosarcade": "jocsarcade", "memorygames": "jocsdememòria", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "jocsdesoplar", "pinballmachines": "maquinesdepinball", "oldgames": "jocsantics", "couchcoop": "<PERSON><PERSON><PERSON><PERSON>", "perguntados": "preguntats", "gameo": "juga<PERSON>", "lasergame": "joc<PERSON><PERSON>", "imessagegames": "jocsimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "ompleltabloc", "jeuxpc": "jocspc", "rétrogaming": "rétrogaming", "logicgames": "jocsdelògica", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jocsdecelebritats", "exitgames": "jocsdeescapada", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "jocimata", "traditionalgames": "jocstradicionals", "kniffel": "kniffel", "gamefps": "jocfps", "textbasedgames": "jocsdetext", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "joc<PERSON><PERSON><PERSON>", "lawngames": "jocsdelgramat", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "futbolí", "tischfußball": "futbolin", "spieleabende": "nitsdespiele", "jeuxforum": "<PERSON><PERSON><PERSON><PERSON>", "casualgames": "joc<PERSON><PERSON><PERSON>", "fléchettes": "dards", "escapegames": "escapegames", "thiefgameseries": "jocsdelatrampa", "cranegames": "jocscranegames", "játék": "joc", "bordfodbold": "bordfutbol", "jogosorte": "jogosorte", "mage": "màgic", "cargames": "jocscargol", "onlineplay": "joc<PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "nitsdegames", "pursebingos": "pursebingos", "randomizer": "aleatoritzador", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "jocspc", "socialdeductiongames": "jocssocietatsdeducció", "dominos": "dominos", "domino": "domino", "isometricgames": "jocsisomè<PERSON>s", "goodoldgames": "bonsjocsantics", "truthanddare": "veritatidesafiament", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "caçadepis<PERSON>", "jeuxvirtuel": "jocsvirtuals", "romhack": "romhack", "f2pgamer": "jugadordef2p", "free2play": "grat<PERSON><PERSON><PERSON>", "fantasygame": "jocdefantasia", "gryonline": "gryonline", "driftgame": "jocdedrifting", "gamesotomes": "j<PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesijocs", "mushroomoasis": "oasisdebolets", "anythingwithanengine": "qualsevolcosaconmotor", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "espasaimàgia", "goodgamegiving": "bon<PERSON><PERSON><PERSON><PERSON>", "jugamos": "f<PERSON>jo<PERSON>", "lab8games": "lab8jocs", "labzerogames": "joc<PERSON>b<PERSON>o", "grykomputerowe": "grycomptadores", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "jocsderitmes", "minaturegames": "jocsdeminatura", "ridgeracertype4": "tipus4ridgeracer", "selflovegaming": "amorpropiigaming", "gamemodding": "modificaciójocs", "crimegames": "jocsdecriminalitat", "dobbelspellen": "dobbelspellen", "spelletjes": "jocs", "spacenerf": "spacenerf", "charades": "xara<PERSON>", "singleplayer": "jugadorindividual", "coopgame": "joccooperatiu", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "reid<PERSON><PERSON>", "scrabble": "scrabble", "schach": "escac", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "gammonatr<PERSON>", "onitama": "onitama", "pandemiclegacy": "llegatpandèmia", "camelup": "camelup", "monopolygame": "monopolygame", "brettspiele": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "jocs<PERSON><PERSON><PERSON>", "boardgame": "joc<PERSON><PERSON><PERSON>", "sällskapspel": "jocdesociació", "planszowe": "jocs<PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "jocdesofà", "zombicide": "zombicide", "tabletop": "taula", "baduk": "baduk", "bloodbowl": "bowldeveu", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "connectfour", "heroquest": "heroequest", "giochidatavolo": "jocsdet<PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "jocs<PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "jogodetaulers", "jocuridesocietate": "jocuridesocietat", "deskgames": "jocsdeskdoficina", "alpharius": "alpharius", "masaoyunları": "ma<PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "encountercosmic", "creationludique": "creaciólúdica", "tabletoproleplay": "r<PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "jocsdeplans", "infinitythegame": "infinitaeljo<PERSON>", "kingdomdeath": "regni<PERSON><PERSON><PERSON>", "yahtzee": "yatzy", "chutesandladders": "xocsyescaleres", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "jocdemesa", "planszówki": "jocs<PERSON><PERSON><PERSON>", "rednecklife": "vidadecampesina", "boardom": "avorriment", "applestoapples": "melaamesa", "jeudesociété": "jocdesocietat", "gameboard": "taulerdejugament", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jocs<PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "ca<PERSON>lopoli", "deckbuilding": "construcciódelesformacions", "mansionsofmadness": "mansionsdelabogeria", "gomoku": "gomoku", "giochidatavola": "jocs<PERSON><PERSON><PERSON>", "shadowsofbrimstone": "ombresdelbrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON>", "battleship": "batalldelvaixell", "tickettoride": "entradaparapujar", "deskovehry": "escriptoriaventures", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "jocs<PERSON><PERSON><PERSON>", "stolníhry": "jocs<PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jocssocietat", "gesellschaftsspiele": "jocsdecessió", "starwarslegion": "legióstarwars", "gochess": "jugaescacs", "weiqi": "weiqi", "jeuxdesocietes": "jocsdesocietat", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonadeguerra", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identitatv", "theisle": "lilla", "thelastofus": "leúltimdenosaltres", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "crida<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyijoainegra", "conanexiles": "conanexiles", "eft": "eft", "amongus": "entrenosaltres", "eco": "eco", "monkeyisland": "illadelsmicos", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "diespassats", "fobia": "fobia", "witchit": "brui<PERSON>t", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "lelondark", "ark": "<PERSON><PERSON><PERSON><PERSON>", "grounded": "conectat", "stateofdecay2": "estatdedecadència2", "vrising": "vrising", "madfather": "pad<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "nodis<PERSON>", "eternalreturn": "retornetern", "pathoftitans": "camídelstitans", "frictionalgames": "jocsfrictionals", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "leshabitacionsdelrere", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "blocdetalls", "thequarry": "laped<PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "somfelicitàtagens", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "estatdsupervivència", "vintagestory": "històriaventatge", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathedege", "alisa": "alisa", "westlendsurvival": "survivènciawestend", "beastsofbermuda": "bestiesdelbermu<PERSON>", "frostpunk": "frostpunk", "darkwood": "fustnegre", "survivalhorror": "sobrevivintalterror", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "trenbuit", "lifeaftergame": "vidaaprèselgame", "survivalgames": "jocssurvivència", "sillenthill": "sil<PERSON><PERSON>alcolm", "thiswarofmine": "aquestaverdadameva", "scpfoundation": "sc<PERSON><PERSON><PERSON><PERSON>", "greenproject": "projecteverd", "kuon": "kuon", "cryoffear": "ploraambpor", "raft": "ràft", "rdo": "rdo", "greenhell": "infernverd", "residentevil5": "residentevil5", "deadpoly": "mortpolí", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "àvia", "littlenightmares2": "petitsensortits2", "signalis": "signalis", "amandatheadventurer": "amandatenexploradora", "sonsoftheforest": "fillsdelbosc", "rustvideogame": "rust<PERSON><PERSON>videogame", "outlasttrials": "sobreviurealsassaj<PERSON>", "alienisolation": "isolamentalienígena", "undawn": "undawn", "7day2die": "7diespermorir", "sunlesssea": "marsensesol", "sopravvivenza": "sopravivència", "propnight": "propnight", "deadisland2": "illamorta2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "mortvers", "cataclysmdarkdays": "cataclismeperillososdies", "soma": "soma", "fearandhunger": "porifam", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "vidadesprés", "ageofdarkness": "edatdelestenebres", "clocktower3": "to<PERSON><PERSON><PERSON>", "aloneinthedark": "<PERSON>ls<PERSON><PERSON><PERSON>", "medievaldynasty": "dinastiam<PERSON><PERSON><PERSON>", "projectnimbusgame": "projectnimbusjoc", "eternights": "eternits", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "dominiomundial", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "matadorsdanans", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "amorwarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdesgastoscuro", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "mencantenlessororites", "ilovevindicare": "mencantaelvindicare", "iloveassasinorum": "mencantenassassinorum", "templovenenum": "templovenenum", "templocallidus": "templa<PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "edatdelsemperors", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeredatgesigmar", "civilizationv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "esnecessiten2", "wingspan": "<PERSON>p<PERSON><PERSON><PERSON>", "terraformingmars": "terraformantmarte", "heroesofmightandmagic": "heroesdemitijamagia", "btd6": "btd6", "supremecommander": "comandantsuprem", "ageofmythology": "edatdelmitologia", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "esborrat", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON>", "civilization6": "civilització6", "warcraft2": "warcraft2", "commandandconquer": "comandaiconquista", "warcraft3": "wow3", "eternalwar": "guerre<PERSON><PERSON>", "strategygames": "jocsdestrategia", "anno2070": "any2070", "civilizationgame": "jocdecivilitzacions", "civilization4": "civilització4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "espores", "totalwar": "guerratotal", "travian": "travian", "forts": "forts", "goodcompany": "bonacompanyia", "civ": "civ", "homeworld": "mónmamà", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "mésràpidquealalum", "forthekings": "pelskings", "realtimestrategy": "estratègiaenrealitat", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "regnatdos<PERSON><PERSON><PERSON>", "eu4": "jo4", "vainglory": "<PERSON>ag<PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "divinitat", "anno": "anot", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "clasedalgebraambdave", "plagueinc": "plagueinc", "theorycraft": "teoriaartística", "mesbg": "mesbg", "civilization3": "civilitzacio3", "4inarow": "4<PERSON><PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "warsdavanç", "ageofempires2": "edatdelsemperors2", "disciples2": "disciples2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "jocsdestratègia", "stratejioyunları": "jocsdestratejia", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "edatdel<PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "conquestamundial", "heartsofiron4": "corsdferro4", "companyofheroes": "companydherois", "battleforwesnoth": "batallaporelswenoth", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansosgansocisne", "phobies": "fobies", "phobiesgame": "jocdefobies", "gamingclashroyale": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "exteriorplane", "turnbased": "pertorna", "bomberman": "bomberman", "ageofempires4": "edatdelsimperis4", "civilization5": "civilització5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "màgiaortogràfica", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estratègia", "popfulmail": "popfulmail", "shiningforce": "forçalluminosa", "masterduel": "màsterduel", "dysonsphereprogram": "programadysonsfera", "transporttycoon": "transporttycoon", "unrailed": "desrenyats", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetormenta", "uplandkingdoms": "regnesdelmuntanya", "galaxylife": "galaxylife", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "gatsdebatalla", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "necessitodevelocitat", "needforspeedcarbon": "necessitodevellcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "sobreviu", "deadbydaylight": "mortperllumdia", "alicemadnessreturns": "retornaalicebogeria", "darkhorseanthology": "antologiaoscura", "phasmophobia": "fasmofòbia", "fivenightsatfreddys": "cincnitsalfreddys", "saiko": "saiko", "fatalframe": "quadratfatal", "littlenightmares": "petitsmalsnits", "deadrising": "mortencantament", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "aventurersdesdecasa", "deadisland": "illamorta", "litlemissfortune": "petitmisssort", "projectzero": "projectzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "hellonaïve", "helloneighbor2": "helloneighbor2", "gamingdbd": "gamingdbd", "thecatlady": "lacatalanacat", "jeuxhorreur": "jocsdeporra", "horrorgaming": "joc<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartescontraelhumanisme", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenoms", "dixit": "dixit", "bicyclecards": "targetabicicletes", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legend<PERSON><PERSON><PERSON>", "solitaire": "solitari", "poker": "pòquer", "hearthstone": "hearthstone", "uno": "unoo", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "c<PERSON><PERSON><PERSON><PERSON>", "cardtricks": "trucsdecar<PERSON>", "playingcards": "cartesdejugat", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "targetesdecomerç", "pokemoncards": "cardsdepokemon", "fleshandbloodtcg": "carneixidangotcg", "sportscards": "targetesdesportives", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "piques", "warcry": "cridadelaverdat", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "reidecors", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "laresistència", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "cartesdeyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "dueldemagi", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "magsfosc", "blueeyeswhitedragon": "ul<PERSON><PERSON>usdragoblanc", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "jutgemtg", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "persecucióplansmtg", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "cartejat", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "espiritualsbatalletes", "battlespiritssaga": "batallesdelsesperitsaga", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "joli<PERSON>", "facecard": "targetaface", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "constructorsdemaces", "marvelchampions": "marvelchampions", "magiccartas": "magiccartes", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornsinstables", "cyberse": "ciberse", "classicarcadegames": "jocsarcadeclàssics", "osu": "osu", "gitadora": "gitadora", "dancegames": "joc<PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "divendresnitenfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "ritmegamer", "stepmania": "stepmania", "highscorerythmgames": "jocsderitmaaltscore", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritmecelestial", "hypmic": "hypmic", "adanceoffireandice": "avançamentdefocice", "auditiononline": "audicionsenlinia", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "jocsderitme", "cryptofthenecrodancer": "cryptodelnecròfags", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON>", "cubing": "cubisme", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "jocsdetrencaclosques", "spotit": "trobat", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "puzleslògics", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "trencaclosques", "rubikscube": "cubrubik", "crossword": "creuades", "motscroisés": "motscreuats", "krzyżówki": "creuades", "nonogram": "nonogram", "bookworm": "ratolídebiblioteca", "jigsawpuzzles": "trencaclosques", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "endevinal<PERSON>", "rompecabezas": "trencaclosques", "tekateki": "tekateki", "inside": "dins", "angrybirds": "ocellsenfadats", "escapesimulator": "escapesimulator", "minesweeper": "netejaterres", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "trencaclosques", "kurushi": "k<PERSON>hi", "gardenscapesgame": "jardinscapesgame", "puzzlesport": "puzzlesport", "escaperoomgames": "jocsdeescaperoom", "escapegame": "jocdescapada", "3dpuzzle": "trencaclosques3d", "homescapesgame": "jochomescapes", "wordsearch": "cercalletres", "enigmistica": "enigmistica", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "contes<PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "elquizimpossible", "candycrush": "candycrush", "littlebigplanet": "pet<PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "puzzle3match", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "cubrubik", "cuborubik": "cobacub", "yapboz": "yapboz", "thetalosprinciple": "letalsprincipi", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "endevinam", "tycoongames": "jocsdegranempresari", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "xifres", "rätselwörter": "paraulesenigma", "buscaminas": "buscamines", "puzzlesolving": "resoluciódepuzzles", "turnipboy": "na<PERSON><PERSON><PERSON>", "adivinanzashot": "adiv<PERSON><PERSON><PERSON><PERSON>", "nobodies": "ning<PERSON>", "guessing": "endevinant", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "creuadescryptiques", "syberia2": "syberia2", "puzzlehunt": "caçacaps", "puzzlehunts": "caçadoresdepuzzles", "catcrime": "gatdelict<PERSON>", "quebracabeça": "trencaclosques", "hlavolamy": "trencaclosques", "poptropica": "poptropica", "thelastcampfire": "lultimfocdelcamp", "autodefinidos": "autodefinits", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "jocdelànecuntitulat", "cassetête": "casse<PERSON><PERSON>", "limbo": "limbo", "rubiks": "rubiks", "maze": "laberint", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "trossos", "portalgame": "jocportal", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "cubertrubik", "indovinelli": "endevinal<PERSON>", "cubomagico": "cubomàgic", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "maravellator<PERSON>boo", "monopoly": "monopoli", "futurefight": "lluitafutura", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestrelles", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "reidelreinodegaletes", "alchemystars": "alchemystars", "stateofsurvival": "estatdesupervivència", "mycity": "meumunici<PERSON>", "arknights": "arknights", "colorfulstage": "escenaricolorit", "bloonstowerdefense": "defensadebloons", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "destinordre<PERSON>nd", "hyperfront": "hiperfront", "knightrun": "correrambcavallers", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "lligadefutbol", "a3": "a3", "phonegames": "jocsdelmòbil", "kingschoice": "elecciódelrei", "guardiantales": "guardianimals", "petrolhead": "apassionatdelpetroli", "tacticool": "tacticool", "cookierun": "cookerun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "foradelbucle", "craftsman": "artesà", "supersus": "supersus", "slowdrive": "conducciólent", "headsup": "estigueatent", "wordfeud": "wordfeud", "bedwars": "guerresdelllit", "freefire": "freefire", "mobilegaming": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "xocdelslocals", "pjsekai": "pjsekai", "mysticmessenger": "missatgermístic", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergènciahq", "enstars": "estrelles", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albió", "hayday": "festadelcamp", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tremollesifidgeta", "ml": "ml", "bangdream": "bangdream", "clashofclan": "xocdelsclans", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "tempsprincesa", "beatstar": "beatstar", "dragonmanialegend": "legend<PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "<PERSON><PERSON><PERSON>", "pocketlove": "amoralbutxaca", "androidgames": "jocsandroid", "criminalcase": "cascriminal", "summonerswar": "summonerswar", "cookingmadness": "cuinamania", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "jarditdelpetitpasseró", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "elsmeusmonstrescantants", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "mi<PERSON><PERSON>", "pou": "pou", "warwings": "alesdeguerrera", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendsmòbil", "ingress": "entrada", "slugitout": "finsalfinal", "mpl": "mpl", "coinmaster": "monedacapità", "punishinggrayraven": "castigant<PERSON><PERSON><PERSON>", "petpals": "amicsdelmascota", "gameofsultans": "joc<PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "jocdelruncity", "juegodemovil": "jocdemòbil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimicria", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "granpers<PERSON>uc<PERSON>ó", "bombmebrasil": "bombemebrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mentindustria", "callofdragons": "cridalddragons", "shiningnikki": "brilla<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "camíadinsensemeta", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "paraulesambamics2", "soulknight": "ànimknight", "purrfecttale": "contespurrfecte", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "colli<PERSON><PERSON><PERSON>", "perfectworldmobile": "mónperfectmobile", "empiresandpuzzles": "empresesipuzzles", "empirespuzzles": "empirepuzzles", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "petitnor<PERSON>mmera", "aethergazer": "aethergazer", "mudrunner": "mudirunner", "tearsofthemis": "<PERSON>à<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "jocsmlbb", "dbdmobile": "dbdmòbil", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "ecoboo", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "cuinantmama", "cabalmobile": "cabalmobile", "streetfighterduel": "lluitadoralcarrerduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "noiesenfrontal", "jurassicworldalive": "jurassicworldviva", "soulseeker": "<PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "superantho", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "legendadeneverland", "pubglite": "pubglite", "gamemobilelegends": "jocmobilelegends", "timeraiders": "caçadorsdetemps", "gamingmobile": "joc<PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "les<PERSON><PERSON><PERSON><PERSON>all", "dnd": "dnd", "quest": "quest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "mónfosc", "travellerttrpg": "viatgertrpg", "2300ad": "2300dc", "larp": "larp", "romanceclub": "clubderomances", "d20": "d20", "pokemongames": "jocspok<PERSON>mon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonvermell", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "xateja", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON>leta", "pokemonpurpura": "pokemonpúrpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "equiprocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monstresdelbutxaca", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonpandarresistant", "teamystic": "teammístic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pok<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "mansferro", "kabutops": "kabutops", "psyduck": "psydoc", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "mest<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "escacs", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "escacs", "schaken": "schaken", "skak": "skak", "ajedres": "escacs", "chessgirls": "donesdescacs", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jugadessolutions", "japanesechess": "escacsjaponesos", "chinesechess": "escacsxinesos", "chesscanada": "escacscanadà", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "oferiments", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabossoyd<PERSON><PERSON>", "dungeonsanddragon": "masmorresidragons", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "dungeonsetdragons", "oxventure": "oxventurat", "darksun": "solnegre", "thelegendofvoxmachina": "llegendadevoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "ma<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftchampionship", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "somnismp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "flamdecera", "fru": "fru", "addons": "addons", "mcpeaddons": "addonsmcpe", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodificat", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "entrelands", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "ciutatminecraft", "pcgamer": "pcgamer", "jeuxvideo": "jocsdevideo", "gambit": "gambit", "gamers": "gammers", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermòbil", "gameover": "jocacabat", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "jugarju<PERSON>", "pcgames": "jocsdepc", "casualgaming": "joc<PERSON><PERSON><PERSON>", "gamingsetup": "confi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "jocpc", "gamerboy": "noigamer", "vrgaming": "jocsvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "jugadordeconsoles", "boxi": "boxi", "pro": "pro", "epicgamers": "gamersépics", "onlinegaming": "juga<PERSON><PERSON>", "semigamer": "semijugador", "gamergirls": "gamerchicks", "gamermoms": "mamesgamer", "gamerguy": "gamerchico", "gamewatcher": "observa<PERSON><PERSON><PERSON>", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerchicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "equipintensiu", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "missions", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerveterà", "cozygaming": "jocconfort", "gamelpay": "gamelpay", "juegosdepc": "jocsdepc", "dsswitch": "dsswitch", "competitivegaming": "gamingcompetitiu", "minecraftnewjersey": "minecraftnovayork", "faker": "fals", "pc4gamers": "pc4gamers", "gamingff": "gammingff", "yatoro": "jauma", "heterosexualgaming": "gamingheterosexual", "gamepc": "jocpc", "girlsgamer": "noiesgamers", "fnfmods": "fnfmods", "dailyquest": "missiodiària", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "gamerchicas", "gamesetup": "configu<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON>", "socialgamer": "gamerensocials", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "jugadorderols", "myteam": "elmeuteam", "republicofgamers": "repúblicadelsgamers", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "triplelegend", "gamerbuddies": "amicsgamer", "butuhcewekgamers": "necessitemnoiesgamers", "christiangamer": "gamercristià", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamercasual", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemerers", "oyunizlemek": "j<PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "videojugador", "wspólnegranie": "jugue<PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "jugadordepaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamerasaludable", "gtracing": "gtracing", "notebookgamer": "gamerdelletra", "protogen": "protogen", "womangamer": "donesju<PERSON><PERSON>", "obviouslyimagamer": "òbviaments<PERSON>juger", "mario": "màriostyle", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forager", "humanfallflat": "humansocarrat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON>", "switch": "canvi", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "mascared<PERSON><PERSON>es", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "advocat", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "llàgrimesdelregne", "walkingsimulators": "simuladorsdecaminar", "nintendogames": "jocsnin<PERSON>o", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "lluna_de_recollida", "mariobros": "mario<PERSON>s", "runefactory": "fabrikadel<PERSON><PERSON><PERSON>", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "respira<PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "elmeufriendpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51jocs", "earthbound": "te<PERSON><PERSON><PERSON><PERSON>", "tales": "contes", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "esclatultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "trianglestrategy", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "nova3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerresdhyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioiesonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendsles", "urgot": "urgot", "zyra": "zyra", "redcanids": "canidsroig", "vanillalol": "vanillalol", "wildriftph": "wildriftcat", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "lolzim", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "eww", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "lligadesalegendes", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "jocfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideojocs", "scaryvideogames": "videogamesesgarrifosos", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videjo<PERSON>", "videosgame": "vide<PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "sobrevigília", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "teatrecombatblock", "arcades": "arcades", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simuladoragricola", "robloxchile": "robloxxile", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxcatalunya", "robloxdeutsch": "robloxcat", "erlc": "erlc", "sanboxgames": "jocsansandbox", "videogamelore": "videogamelore", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "pais<PERSON><PERSON><PERSON><PERSON>", "starcitizen": "estelcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "granrobatoriauto", "deadspace": "espaimort", "amordoce": "amor12", "videogiochi": "videojocs", "theoldrepublic": "lanticrepublica", "videospiele": "videojocs", "touhouproject": "touhouproject", "dreamcast": "somnisenvivint", "adventuregames": "jocsdaventura", "wolfenstein": "wolfenstein", "actionadventure": "aventuraactiva", "storyofseasons": "històriesdestacions", "retrogames": "retrogames", "retroarcade": "retroarcada", "vintagecomputing": "computaciovintage", "retrogaming": "retrogaming", "vintagegaming": "jocsdevintage", "playdate": "citadejoc", "commanderkeen": "comandant<PERSON>t", "bugsnax": "bugsnaix", "injustice2": "injustícia2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "jocdel<PERSON>", "zenlife": "vidazen", "beatmaniaiidx": "beatmaniaiidx", "steep": "empinat", "mystgames": "jocs<PERSON>", "blockchaingaming": "jocsdelblockchain", "medievil": "medieval", "consolegaming": "jocsdeconsola", "konsolen": "konsolen", "outrun": "escapar", "bloomingpanic": "pànicflorint", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "jocsdepor", "monstergirlquest": "monstergirlquest", "supergiant": "supergiga", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "simuladors<PERSON>farming", "juegosviejos": "jocsantics", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ficcióinteractiva", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantsamentes", "visualnovel": "novellvisual", "visualnovels": "novellesvisuals", "rgg": "rgg", "shadowolf": "ombraull", "tcrghost": "tcrfantasma", "payday": "diadepaguets", "chatherine": "catherine", "twilightprincess": "<PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "caixaregals", "aestheticgames": "jocsestètics", "novelavisual": "novellavisual", "thecrew2": "lacrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamentem", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revoluciódelbufador", "wiiu": "wiiu", "leveldesign": "dissenydenivel", "starrail": "starrail", "keyblade": "clau<PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsovint", "novelasvisuales": "novellesvisuals", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videojocs", "videogamedates": "datesdevideo<PERSON><PERSON>", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "joc<PERSON>lk", "batmangames": "j<PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "retorndeljutge", "gamstergaming": "gamerstargaming", "dayofthetantacle": "diadeltentacle", "maniacmansion": "mansiómaníaca", "crashracing": "carrerescrash", "3dplatformers": "plataformes3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "jocsantiquats", "hellblade": "diabledecapçalera", "storygames": "jocsdestòria", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "mésenllàdestwàls", "gameuse": "jugueu", "offmortisghost": "offmortisghost", "tinybunny": "conilletpetit", "retroarch": "retroarch", "powerup": "potenciator", "katanazero": "katanazero", "famicom": "famicon", "aventurasgraficas": "aventuresgràfiques", "quickflash": "ràpidllamp", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "terra<PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "illacoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "unaltremon", "metaquest": "metaquest", "animewarrios2": "animeguerrers2", "footballfusion": "fusiófutbol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metaltort", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "muntanyadevergonya", "simulator": "simulador", "symulatory": "simulatori", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "mascotalantí", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simraces", "pvp": "pvp", "urbanchaos": "caosurbà", "heavenlybodies": "cosesdivines", "seum": "seum", "partyvideogames": "jocsdevideoenfesta", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "simuladorvuelosespacials", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "tallaextripa", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "jocsdevideo", "thewolfamongus": "elwolfentrenosaltres", "truckingsimulator": "simuladorcamions", "horizonworlds": "horizonworlds", "handygame": "jocpràctic", "leyendasyvideojuegos": "llegendesivalsvideojocs", "oldschoolvideogames": "videojocsantiquats", "racingsimulator": "simuladorracing", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentsdelcaos", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "portesdolim<PERSON>", "monsterhunternow": "caçademonstresja", "rebelstar": "rebelstar", "indievideogaming": "indievideojocs", "indiegaming": "indiegaming", "indievideogames": "indievideojocs", "indievideogame": "indievidejoc", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON>", "unbeatable": "incomparable", "projectl": "projectl", "futureclubgames": "futursjocsclub", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "ciènciaapertures", "backlog": "retard", "gamebacklog": "jocatrasat", "gamingbacklog": "jocspend<PERSON>", "personnagejeuxvidéos": "personatgejocsvídeo", "achievementhunter": "caçadorassoliments", "cityskylines": "paisatgesdelciutat", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "gaterobedient", "beastlord": "bestialsenyor", "juegosretro": "jocs<PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriialbosccegament", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoridopamina", "staxel": "staxel", "videogameost": "videogamesost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "iniciald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesad", "darkerthanblack": "mésfoscqueelnegre", "animescaling": "animescaling", "animewithplot": "animeambhistòria", "pesci": "peixet", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "anime90s", "darklord": "senyordelavorant", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veureanime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonetemporada1", "rapanime": "rapanime", "chargemanken": "carregamankenc", "animecover": "animeportada", "thevisionofescaflowne": "laviusdel<PERSON><PERSON>ne", "slayers": "matadorets", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "p<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartyelpatri<PERSON>", "futurediary": "diarifutur", "fairytail": "contesdefades", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "fetalfons", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sireniamelodia", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "carnaval", "dragonmaid": "dracomaid", "blacklagoon": "llacnegre", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "gossosdestraia", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "uníndexmàgicpertothom", "sao": "sao", "blackclover": "trévolnegro", "tokyoghoul": "tokyoghoul", "onepunchman": "unpunxoman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8lainfinat", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "espionatgefamilia", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioritatwonderegg", "angelsofdeath": "àngelsdelamort", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosímic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesports", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "saga<PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "elneniialbestia", "fistofthenorthstar": "puny<PERSON><PERSON><PERSON>d", "mazinger": "mazinger", "blackbuttler": "butlernegre", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "comman<PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON>wosagas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "monoicreepy", "martialpeak": "picomarcial", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "chicadelrècord", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revenantfae", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reiboo", "scaramouche": "scaramouche", "amiti": "amics", "sailorsaturn": "marinersatur", "dio": "dio", "sailorpluto": "marinerplut<PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "animeantiquat", "chainsawman": "cadeça<PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "negrebutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "cistelldefruites", "devilmancrybaby": "diablehomeplora", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "viuam<PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "elpromesnuncaaterra", "monstermanga": "monstermanga", "yourlieinapril": "tuli<PERSON><PERSON><PERSON>", "buggytheclown": "buggyel<PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "seraphdelfinal", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "presonerdelprofundmar", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mónmortdelhome", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "joc<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerresdelsaliments", "cardcaptorsakura": "captura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "liniadediables", "toyoureternity": "totevaeternitat", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "períodeblau", "griffithberserk": "g<PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "aliançasecreta", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "esborrat", "bluelock": "bluelock", "goblinslayer": "matagoblins", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "cavallervampir", "mugi": "mugi", "blueexorcist": "exor<PERSON><PERSON><PERSON><PERSON>", "slamdunk": "clavada", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "f<PERSON><PERSON><PERSON><PERSON>", "airgear": "airegear", "magicalgirl": "noiamàgica", "thesevendeadlysins": "aquestessetepecatsmortals", "prisonschool": "escoladepresoners", "thegodofhighschool": "elgodedelcollegihaut", "kissxsis": "pet<PERSON><PERSON><PERSON>", "grandblue": "granblau", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "mortvivamentafortunat", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "sanglletge", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "flamaapuñal", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "kinkinman", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "màgicdic<PERSON><PERSON>", "housekinokuni": "casaquinosfi", "recordragnarok": "registreragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "institutdelmort", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "pr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "classeassassina", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "des<PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponès", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "esperancaudí", "animedub": "animedubtinc", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "noiabonica", "cavalieridellozodiaco": "cavalersdelzodiaq", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "corfemanga", "deliciousindungeon": "deliciostadungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "registrederagnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialmassadif<PERSON><PERSON><PERSON>", "overgeared": "sobrecarregat", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierbarret<PERSON>abates", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunoleó", "kamen": "kamen", "mangaislife": "mangaislife", "dropsofgod": "gotesdellibertat", "loscaballerosdelzodia": "lacaballersdelzodíac", "animeshojo": "animeshojo", "reverseharem": "reversharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "granprofessoronizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "elmeubossdad", "gear5": "gear5", "grandbluedreaming": "somnigrandblue", "bloodplus": "<PERSON><PERSON><PERSON>", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "sangcanime", "bloodc": "sangc", "talesofdemonsandgods": "contesdemonisigods", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "cargolsxpitjor", "splatteranime": "esclatanime", "splatter": "esclat", "risingoftheshieldhero": "llevantdelheroiambescut", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespanya", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeons", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiambsmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "noiesmàgiques", "callofthenight": "<PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "brawlersbakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "jardin<PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevers", "persocoms": "persocoms", "omniscientreadersview": "vistaomniscentdell<PERSON>", "animecat": "gatuanime", "animerecommendations": "recomanacionsanime", "openinganime": "oberturadeanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minaromantiqueteen", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotgigants", "neongenesisevangelion": "neon<PERSON><PERSON><PERSON><PERSON><PERSON>", "codegeass": "codegeass", "mobilefighterggundam": "mobilfighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "blancament", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventurajojoesbizarre", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "gelato", "jojobizarreadventures": "aventuresjojobizarre", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitar", "greenranger": "verd<PERSON>er", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "estrellafox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeciutat", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonaventura", "hxh": "hxh", "highschooldxd": "institutdxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "matardemons", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ataqontaltitans", "erenyeager": "erenyeager", "myheroacademia": "miheroiakademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corpsdenquestes", "onepieceanime": "onepieceanime", "attaquedestitans": "atacdestitans", "theonepieceisreal": "elonepieceésreal", "revengers": "venjadors", "mobpsycho": "mobpsico", "aonoexorcist": "aonoexorcista", "joyboyeffect": "efectjoyboy", "digimonstory": "his<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "superpresó", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "nuestroanhostclub", "flawlesswebtoon": "webtoonimpecable", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesa", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "bruixaflair<PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "nomésper<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "totsantsstreet", "recuentosdelavida": "recontesdelavida"}