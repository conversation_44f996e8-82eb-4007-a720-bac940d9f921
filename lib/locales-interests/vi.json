{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "tâmlý<PERSON>ọc", "philosophy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "<PERSON><PERSON><PERSON><PERSON>", "science": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "culture": "vănhoá", "languages": "ngônng<PERSON>", "technology": "côngnghệ", "memes": "meme", "mbtimemes": "mbtimeme", "astrologymemes": "memechiêmtinh", "enneagrammemes": "memeenneagram", "showerthoughts": "showerthought", "funny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "video", "gadgets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "chín<PERSON>rị", "relationshipadvice": "lờikhuyênch<PERSON>", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "tint<PERSON><PERSON>", "worldnews": "tint<PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "khảocổ<PERSON>", "learning": "<PERSON><PERSON><PERSON>", "debates": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crafts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dance": "<PERSON><PERSON>ả<PERSON>", "design": "thiếtkế", "makeup": "makeup", "beauty": "làmđẹp", "fashion": "th<PERSON><PERSON><PERSON>", "singing": "<PERSON><PERSON><PERSON>", "writing": "v<PERSON><PERSON><PERSON>", "photography": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosplay": "cosplay", "painting": "hộihoạ", "drawing": "vẽ", "books": "<PERSON><PERSON><PERSON>", "movies": "phim", "poetry": "thơ", "television": "tivi", "filmmaking": "<PERSON><PERSON><PERSON><PERSON>", "animation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "viễntưởng", "fantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentaries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "<PERSON><PERSON><PERSON><PERSON>", "comedy": "p<PERSON><PERSON><PERSON>i", "crime": "trinhthám", "drama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "kinhd<PERSON>", "romance": "lãngmạn", "realitytv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ế", "action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music": "âmnhạc", "blues": "blues", "classical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "đ<PERSON>ngq<PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "folk": "dânca", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latinh", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "<PERSON><PERSON><PERSON><PERSON>", "concerts": "concert", "festivals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "museums": "b<PERSON><PERSON><PERSON><PERSON>", "standup": "hàiđộcthoại", "theater": "nhàhát", "outdoors": "ngoàitrời", "gardening": "làmvườn", "partying": "ti<PERSON><PERSON><PERSON><PERSON>", "gaming": "chơigame", "boardgames": "boardgame", "dungeonsanddragons": "dungeonsanddragons", "chess": "c<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "<PERSON><PERSON><PERSON><PERSON>", "baking": "nướng<PERSON>án<PERSON>", "cooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON>", "vegan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birds": "chim", "cats": "mèo", "dogs": "chó", "fish": "cá", "animals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "blacklivesmatter", "environmentalism": "b<PERSON><PERSON>ệ<PERSON>ôit<PERSON><PERSON><PERSON><PERSON>", "feminism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "quy<PERSON>nlgbt", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "tìnhnguyện", "sports": "thểthao", "badminton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseball": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "basketball": "b<PERSON><PERSON><PERSON><PERSON>", "boxing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cricket": "b<PERSON><PERSON><PERSON><PERSON>", "cycling": "đạpxe", "fitness": "thể<PERSON><PERSON><PERSON>", "football": "bóng<PERSON><PERSON>", "golf": "gôn", "gym": "gym", "gymnastics": "thể<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hockey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "bónglư<PERSON><PERSON>", "pilates": "pilates", "pingpong": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "running": "ch<PERSON>y", "skateboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "surfing": "lư<PERSON><PERSON>ón<PERSON>", "swimming": "b<PERSON><PERSON>", "tennis": "tennis", "volleyball": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weightlifting": "cửtạ", "yoga": "yoga", "scubadiving": "đilặn", "hiking": "<PERSON>ib<PERSON>", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "cung<PERSON><PERSON><PERSON><PERSON><PERSON>", "pisces": "<PERSON>ngư", "aries": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "taurus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gemini": "songtử", "cancer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leo": "s<PERSON><PERSON><PERSON>", "virgo": "x<PERSON><PERSON><PERSON>", "libra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scorpio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sagittarius": "nhânmã", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "khôngràngbuộc", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "độ<PERSON><PERSON>ân", "polyamory": "<PERSON><PERSON><PERSON><PERSON>", "enm": "tinhyêunhi<PERSON>ười", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbian", "bisexual": "lưỡngtính", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "vôtính", "reddeadredemption2": "reddeadredemption2", "dragonage": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsrow": "<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "huynhgiabao", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "linhh<PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "hoànghônq<PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "litrpg": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "haloinfinite": "haloinfinite", "guildwars": "<PERSON>ộ<PERSON>ếnbang<PERSON>i", "openworld": "thếgiớimở", "heroesofthestorm": "cactheraongstorm", "cytus": "cytus", "soulslike": "l<PERSON><PERSON><PERSON>", "dungeoncrawling": "khámphángụ<PERSON>ối", "jetsetradio": "jetsetradio", "tribesofmidgard": "bộlạccủami<PERSON>gard", "planescape": "kếhoạchvượtkhỏi", "lordsoftherealm2": "daitrihuynh2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "môphỏngthúvị", "okage": "okage", "juegoderol": "juegoderol", "witcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dishonored": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "thachthuc3", "fallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "eldercrolls", "modding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasycuold", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "thichyeulovemotcachkhongthechiemduc", "otomegames": "gameotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinathờigian", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "ngườikhámphá", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "bóngch<PERSON>y", "bloodontheclocktower": "máuvàotháp<PERSON>ồ", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "hấpdẫn", "rpg": "gamenhậpvai", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "mộ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "hồnquỷ", "mu": "boo", "falloutshelter": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "g<PERSON><PERSON>", "darkestdungeon": "<PERSON>ầmngầmtố<PERSON>ă<PERSON>", "eclipsephase": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "triề<PERSON><PERSON><PERSON>", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwart<PERSON><PERSON><PERSON><PERSON>", "madnesscombat": "cuồngnhịpcombat", "jaggedalliance2": "liênminhnhấpnhô2", "neverwinter": "neverwinter", "road96": "đường96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "th<PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "đ<PERSON><PERSON>_trẻ_c<PERSON><PERSON><PERSON><PERSON>_sáng", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "dòngdõi2", "digimonworld": "<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "chiếcghếbịvỡ", "horizonforbiddenwest": "horizoncấmphươngtây", "twewy": "twewy", "shadowpunk": "b<PERSON><PERSON><PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hognichhuy<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastepoch": "kỷnguyêncuối", "starfinder": "tìmngôisa<PERSON>", "goldensun": "mặttrờivàng", "divinityoriginalsin": "tộilỗinguyênkhai", "bladesinthedark": "lưỡidaotrongbóngtối", "twilight2000": "hoànghôn2000", "sandevistan": "sandevistan", "cyberpunk": "thếgiớicybepunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkđỏ", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "lệnhr<PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "maquỷsốngsót", "oldschoolrunescape": "runes<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "nỗibuồnthếgiớicổ", "adventurequest": "hànhtrìnhkhámphá", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "tr<PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "truyềnsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "<PERSON>ónh<PERSON><PERSON>", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON>", "chainedechoes": "tiếngvan<PERSON><PERSON>", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "nh<PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "núivàkiếm", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "cột_tr<PERSON>_c<PERSON><PERSON>_v<PERSON><PERSON>_c<PERSON>u", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "s<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "khoangduong8cong", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "sóihoangđ<PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "concuade<PERSON>ta", "engineheart": "tráitimđộngcơ", "fable3": "huyềnthoại3", "fablethelostchapter": "c<PERSON><PERSON><PERSON>ện<PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "nhậpvai", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "hồiđọccuối90", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "vươngquoctráitim1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "tròchơirpg", "kingdomhearts": "vươngq<PERSON><PERSON><PERSON>ó<PERSON>", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "<PERSON><PERSON><PERSON><PERSON>", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "tráitimcuồngdại", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "bầutrờiarcadia", "shadowhearts": "tráitimb<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "h<PERSON><PERSON>_thở_của_lửa4", "mother3": "mẹ3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "khacnhauedn", "roleplaygames": "tr<PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "tr<PERSON><PERSON>ơivai", "fabulaultima": "fabulault<PERSON>", "witchsheart": "tráitimp<PERSON>ù<PERSON>ủy", "harrypottergame": "gameharrypotter", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "conduongphuot2e", "vampirilamasquerade": "booh<PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "chơiboo", "spelljammer": "<PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronox", "cocttrpg": "cocttrpg", "huntroyale": "truye<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "<PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "bladesoul": "linhhaithanhblade", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "th<PERSON>gi<PERSON>tt<PERSON>ú<PERSON>withyou", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "chiếnlượccu<PERSON><PERSON>", "grandia": "grandia", "darkheresy": "bángh<PERSON>tốibí", "shoptitans": "shoptitans", "forumrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "bocdanhcongtrongtrociel", "gryrpg": "gryrpg", "sacredgoldedition": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gamegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamerpg", "prophunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrails": "starrails", "cityofmist": "th<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "nhấpvàclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "khôngthểchiasẻ", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "hậucyberpunk", "deathroadtocanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "kho<PERSON>i_unjdr", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "sănquáivậtbùng<PERSON>ổ", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "nu<PERSON><PERSON><PERSON>hhồn", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "tròchơi9", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "gameanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "ngườigoodeat", "diluc": "diluc", "venti": "venti", "eternalsonata": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "côntheprin<PERSON>", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vcs": "vcs", "pes": "bạnbé", "pocketsage": "pocketsage", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cdl": "cdl", "efootbal": "bong<PERSON>le<PERSON>", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "li<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "giảiđấuoverwatch", "cybersport": "cybersport", "crazyraccoon": "rac<PERSON><PERSON><PERSON><PERSON>", "test1test": "kiểmtrathử1", "fc24": "fc24", "riotgames": "riotgames", "eracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorant<PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantvn", "csgo": "csgo", "tf2": "tf2", "portal2": "cổng2", "halflife": "nửađ<PERSON><PERSON>", "left4dead": "bỏđisống", "left4dead2": "left4dead2", "valve": "van", "portal": "c<PERSON>ng", "teamfortress2": "độipháo2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "k<PERSON>inguyencuaboo", "transformice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justshapesandbeats": "chỉhìnhdạngvànhịpđiệu", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "chặtchém", "deeprockgalactic": "deprockgalactic", "riskofrain2": "rủiromưa2", "metroidvanias": "metroidvania", "overcooked": "nấuqu<PERSON><PERSON>ín", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7ngày2đêm", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "<PERSON><PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "chiếntrường1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "nhìntheo", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "môphỏngbànchơi", "partyhard": "quẩyđẫm", "hardspaceshipbreaker": "ngắtkhókhăn", "hades": "hades", "gunsmith": "th<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "bịnhốtvớijester", "dinkum": "dinkum", "predecessor": "ngườitiề<PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "colonygame", "noita": "noita", "dawnofwar": "khởiđầusựchiếntranht", "minionmasters": "chiếnhakỳminion", "grimdawn": "th<PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "lin<PERSON>g", "datingsims": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "thoátkhỏihìnhkhối", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "horizoncacthanhphohiendaị", "defconheavy": "defconnặng", "kenopsia": "kenopsia", "virtualkenopsia": "ảoảnhkenopsia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "tr<PERSON><PERSON>ơib<PERSON>", "omegastrikers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wayfinder": "ngườih<PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "vịtnh<PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "gàcuốicùngngựa", "dialtown": "dialtown", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "si<PERSON><PERSON>_thịt_boy", "tinnybunny": "tinnybunny", "cozygrove": "cozygrove", "doom": "<PERSON><PERSON><PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "biêngiới", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "xảhiện", "farcrygames": "gamefarcry", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "cuộckhángchiến<PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "codchiếntranh", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "r<PERSON>ae<PERSON>g<PERSON>", "divisions2": "divisions2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "chiếntranhlạnhvớizombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "mắtvang007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>đ<PERSON>", "neonabyss": "neonabyss", "planetside2": "hànhtinh2", "mechwarrior": "mechwarrior", "boarderlands": "biêngiới", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "thoátkhỏitarkov", "metalslug": "kimslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON>", "back4blood": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "s<PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "biểudayavalkyria", "specopstheline": "đặcnhiệmthedia", "killingfloor2": "sátthủtầng2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "thếkỷthếgiớitrotàn", "farcry4": "farcry4", "gearsofwar": "cogsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythúyênnhânchóliêugianghoang", "generationzero": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "vàovukhi", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "chiếntranhhiệnđại2", "blackops1": "blackops1", "sausageman": "ngườihotdog", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "sống<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "t<PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "b<PERSON><PERSON><PERSON>", "tinytina": "<PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "concaubangtudo", "juegosfps": "gamebắnsúng", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "chiếntrường2", "shatterline": "vạchgãy", "blackopszombies": "zombiesblackops", "bloodymess": "bẩnthỉu", "republiccommando": "độikỵbinhcộnghòa", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "<PERSON><PERSON><PERSON><PERSON>", "destiny1": "địnhmệnh1", "gamingfps": "gamefps", "redfall": "đỏrơi", "pubggirl": "côg<PERSON><PERSON><PERSON><PERSON>", "worldoftanksblitz": "th<PERSON>gi<PERSON>blitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "ánhsángxa", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "vùng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "ngàylĩnhlương2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "xàphồngcod", "ghostcod": "ma<PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "giảitr<PERSON><PERSON><PERSON><PERSON>thể<PERSON>ổ<PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamecodm", "borderlands2": "biêngiới2", "counterstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "bắnthẳng", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "t<PERSON><PERSON>ong", "neonwhite": "neontrắng", "remnant": "<PERSON><PERSON><PERSON><PERSON>", "azurelane": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwar": "<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "súngvolt", "returnal": "trởvề", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "ngườ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "rungthong2", "microvolts": "microvolt", "reddead": "đỏchết", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "trậnchiến3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rỉsét", "conqueronline": "chinhphuconline", "dauntless": "kh<PERSON>_chịu", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON>à<PERSON><PERSON><PERSON><PERSON>", "warthunder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flightrising": "<PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "<PERSON>uy<PERSON>nt<PERSON>ạ<PERSON>une<PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "khôngcóngườiyêu", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "gạchxóa", "agario": "agario", "secondlife": "cuocsongthu2", "aion": "aion", "toweroffantasy": "toweoffantasy", "netplay": "netplay", "everquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "sự<PERSON>àngbuộ<PERSON>", "dragonageinquisition": "<PERSON><PERSON><PERSON>nthalongkiểmtra", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newworld": "th<PERSON>giớ<PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "pirate101": "cướpbiển101", "honorofkings": "vinhquangcuaquocdandau", "fivem": "fivem", "starwarsbattlefront": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmn", "pokemmo": "pokemmo", "ponytown": "thị<PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wow<PERSON><PERSON><PERSON><PERSON>", "worldofwarcraft": "thếgiớicủakhảihoặc", "warcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wotlk": "bảnmởrộngwotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "troccuaxatan", "riotmmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "kỵsỹxoắn", "mulegend": "mulegend", "startrekonline": "<PERSON><PERSON>á<PERSON><PERSON><PERSON><PERSON><PERSON>ụ", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "lưỡibvàlinhhồn", "evony": "evony", "dragonsprophet": "<PERSON>i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "gry<PERSON><PERSON>", "warmane": "warmane", "multijugador": "<PERSON><PERSON><PERSON><PERSON>", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "chillon", "dcuniverseonline": "v<PERSON><PERSON><PERSON><PERSON>", "growtopia": "growtopia", "starwarsoldrepublic": "c<PERSON><PERSON><PERSON><PERSON>_c<PERSON><PERSON><PERSON>n", "grandfantasia": "fantas<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "th<PERSON><PERSON>", "riseonline": "tangtruoctrenmang", "corepunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequestworlds": "cuộcđiệnhànhtrongthếgiới", "flyforfun": "<PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "vươngquốcvôvọng", "cityofheroes": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "brawlhalla", "virtuafighter": "đấutrưởng", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkđồng<PERSON><PERSON><PERSON>", "nomoreheroes": "không<PERSON><PERSON>nan<PERSON>ng", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "nhưconrồng", "retrofightinggames": "<PERSON>đ<PERSON><PERSON><PERSON><PERSON>ổ", "blasphemous": "b<PERSON><PERSON><PERSON>", "rivalsofaether": "kẻthùcủakhíáo", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "robotcyber", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "dungcucungcap", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "vuafigthers", "ghostrunner": "machay", "chivalry2": "khiếmkhuyết2", "demonssouls": "linhdemon", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "tinnhansilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "giảithểthaoevolution", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "chuyendongberseria", "bloodborne": "máuđẻn", "horizon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "chưakhámphá", "horizonzerodawn": "horizonzerođỉnh", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "cuoicungcuachúngta", "infamous": "nổitiếngđen", "playstationbuddies": "bạnbèplaystation", "ps1": "ps1", "oddworld": "th<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "chuo<PERSON><PERSON><PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "tango<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "thantuongchientranh", "gris": "xám", "trove": "trove", "detroitbecomehuman": "detroittrởthànhconngười", "beatsaber": "beats<PERSON>r", "rimworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ồ", "crashteamracing": "đọchsốcracing", "fivepd": "nămpd", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "chơiplaystation", "samuraiwarriors": "chiếnbinhsamurai", "psvr2": "psvr2", "thelastguardian": "ngườ<PERSON><PERSON><PERSON>", "soulblade": "linhmach<PERSON>ồ<PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "ngườicanhcuối", "xboxone": "xboxone", "forza": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "nhàkhámphátâmhồn", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "elderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbó", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "khong<PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "trượtbăng3", "houseflipper": "làmbiếnhouse", "americanmcgeesalice": "alicecủaamericanmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vư<PERSON>ng", "fable2": "huyenthoait2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "truyentranhchonkien", "skycotl": "bob<PERSON><PERSON>o", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "hẹnhòquáivật", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "<PERSON>e<PERSON><PERSON><PERSON>", "outerwilds": "k<PERSON><PERSON>mp<PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "an<PERSON><PERSON>", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "gamevịt", "thestanleyparable": "thestanleyparable", "towerunite": "thápgặpgỡ", "occulto": "<PERSON><PERSON><PERSON><PERSON>", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "ngườithíchmưa", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "ch<PERSON>ơngtrìnhkhonggiankerbal", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "vòmđ<PERSON>", "pizzatower": "tháppizza", "indiegame": "gameindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "sựthậthoặctháchthức", "game": "tr<PERSON><PERSON><PERSON><PERSON>", "rockpaperscissors": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hulahoop": "vònghula", "dare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "tròchơiởsân", "pickanumber": "chọnmộtsố", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "qu<PERSON><PERSON><PERSON>", "cosygames": "gameấmáp", "datinggames": "tròchơidate", "freegame": "gamefree", "drinkinggames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "tr<PERSON><PERSON><PERSON><PERSON>", "mahjong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeux": "game", "simulationgames": "gamegiả<PERSON>ập", "wordgames": "tr<PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "chudes<PERSON>ng", "letsplayagame": "choi<PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON>", "oyun": "<PERSON>nyun", "interactivegames": "tròchơiinteractív", "amtgard": "amtgard", "staringcontests": "cuộcchiếnhìnhstare", "spiele": "ch<PERSON><PERSON>", "giochi": "tr<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "gameiphone", "boogames": "boogames", "cranegame": "tròchơiđưađỏ", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "nhảycóc", "arcadegames": "tròchơiarcade", "yakuzagames": "gameyakuzav", "classicgame": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "đ<PERSON><PERSON><PERSON>", "galagames": "gamegalaxy", "romancegame": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>nh<PERSON>ê<PERSON>", "yanderegames": "gameyandere", "tonguetwisters": "vặnlưỡi", "4xgames": "4xgame", "gamefi": "gamefi", "jeuxdarcades": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "gamebàn", "metroidvania": "metroidvania", "games90": "game90", "idareyou": "mìnhth<PERSON><PERSON>bạn", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "thậtvảgiả", "playgames": "chơigame", "gameonline": "gameonline", "onlinegames": "gameonline", "jogosonline": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "chơi1tròchơi", "pictionary": "chơivẽchữ", "coopgames": "gamecoh", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "ch<PERSON><PERSON>_rôles", "burgergames": "tròchơiburger", "kidsgames": "tròchơitrẻem", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "tròchơihỏiđáp", "gioco": "ch<PERSON><PERSON>", "managementgame": "tr<PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "tr<PERSON><PERSON>ơiđ<PERSON>ể", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "gameformula1", "citybuilder": "xânhàcity", "drdriving": "drdriving", "juegosarcade": "trochoiarcade", "memorygames": "tr<PERSON><PERSON>ơiho<PERSON>", "vulkan": "vulkan", "actiongames": "gamehànhđộng", "blowgames": "thổi_game", "pinballmachines": "máymáyb<PERSON>g", "oldgames": "gamecũ", "couchcoop": "<PERSON><PERSON><PERSON><PERSON>", "perguntados": "hỏiđáp", "gameo": "gameo", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "gameimessage", "idlegames": "gamekhonglamgiau", "fillintheblank": "điềnvàochỗtrống", "jeuxpc": "gamepc", "rétrogaming": "ch<PERSON>i_game_c<PERSON><PERSON><PERSON>n", "logicgames": "tròchơilogic", "japangame": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "lướtsóngtàu", "jeuxdecelebrite": "tr<PERSON><PERSON><PERSON><PERSON>", "exitgames": "tho<PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "gamechudangtexte", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "trộmgame", "lawngames": "tròchơingoàitrời", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "biaboo", "spieleabende": "<PERSON><PERSON>ich<PERSON><PERSON>", "jeuxforum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgames": "gamecasual", "fléchettes": "b<PERSON><PERSON><PERSON>", "escapegames": "tròchơiđà<PERSON>á<PERSON>", "thiefgameseries": "trộmcungseries", "cranegames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "ch<PERSON><PERSON>", "bordfodbold": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosorte": "tráicatutra", "mage": "ma", "cargames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "chơionline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "đạigame", "pursebingos": "víbingo", "randomizer": "ngẫu_nhên", "msx": "msx", "anagrammi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamespc": "gamepc", "socialdeductiongames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "gamecổ<PERSON><PERSON>", "truthanddare": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "să<PERSON><PERSON><PERSON>khobạc", "jeuxvirtuel": "tr<PERSON><PERSON><PERSON>i<PERSON><PERSON>", "romhack": "hackrom", "f2pgamer": "gamechinhthuc", "free2play": "miễn_ph<PERSON>_để_ch<PERSON>i", "fantasygame": "gamehuyềnbí", "gryonline": "gryonline", "driftgame": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "gamesotomes", "halotvseriesandgames": "halotvseriesandgames", "mushroomoasis": "tiếngiấ<PERSON>lâm", "anythingwithanengine": "mọig<PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "trongtungngocgame", "swordandsorcery": "k<PERSON>ế<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "choi<PERSON><PERSON>", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8tr<PERSON><PERSON><PERSON><PERSON>", "labzerogames": "labzerogames", "grykomputerowe": "gamecomputer", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "loại4<PERSON><PERSON>", "selflovegaming": "yêuchínhmìnhgaming", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "tr<PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "dobbelspellen", "spelletjes": "tr<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "k<PERSON><PERSON><PERSON><PERSON>", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "choi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "tr<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "vuadiscord", "scrabble": "chơiscrabble", "schach": "cờv<PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "cờv<PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "dis<PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolygame", "brettspiele": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "tr<PERSON><PERSON>ơib<PERSON>", "boardgame": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "tr<PERSON><PERSON><PERSON><PERSON>", "planszowe": "gameboard", "risiko": "<PERSON><PERSON><PERSON>", "permainanpapan": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "b<PERSON><PERSON><PERSON><PERSON>", "baduk": "cờv<PERSON><PERSON>", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "cờv<PERSON><PERSON>", "senet": "bạn_b<PERSON>_senet", "goboardgame": "goboardgame", "connectfour": "kếtnối4", "heroquest": "anhhùngkhámphá", "giochidatavolo": "gióchidatavolo", "farkle": "farkle", "carrom": "biangoc", "tablegames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON>", "deskgames": "tròchơivănphòng", "alpharius": "alpharius", "masaoyunları": "masao", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "cuộcnémcosmic", "creationludique": "sangtaoludique", "tabletoproleplay": "ch<PERSON><PERSON>_bàn", "cardboardgames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "chogameđổichỗ", "infinitythegame": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "vươngqu<PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "tr<PERSON><PERSON><PERSON><PERSON>", "rednecklife": "cuộcsốngnôngdân", "boardom": "b<PERSON><PERSON><PERSON><PERSON>", "applestoapples": "táixoántáixoán", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "tròchơiấnphím", "twilightimperium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horseopoly": "ngựaopoly", "deckbuilding": "x<PERSON><PERSON>_dự<PERSON>_bàn", "mansionsofmadness": "<PERSON>ệ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "giờchidatavola", "shadowsofbrimstone": "b<PERSON><PERSON><PERSON><PERSON>", "kingoftokyo": "vuatokyo", "warcaby": "warcaby", "táblajátékok": "tr<PERSON><PERSON>ơib<PERSON>", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "b<PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "ch<PERSON><PERSON>_bàn", "xiángqi": "cờv<PERSON><PERSON>", "jeuxsociete": "tr<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "tr<PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "li<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gochess": "gochess", "weiqi": "cờv<PERSON><PERSON>", "jeuxdesocietes": "tr<PERSON><PERSON><PERSON><PERSON>", "terraria": "terraría", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "tungdayz", "identityv": "nhậndạngv", "theisle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelastofus": "cuoicungcuachung<PERSON>", "nomanssky": "khongcocach", "subnautica": "subnautica", "tombraider": "kẻsănlùngmộ", "callofcthulhu": "g<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "giữachún<PERSON><PERSON>", "eco": "<PERSON><PERSON><PERSON><PERSON>i", "monkeyisland": "hònđảokhỉ", "valheim": "valheim", "planetcrafter": "nhàthiênvăn", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "sợhãi", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "boonorthgard", "7dtd": "7dtd", "thelongdark": "chond<PERSON><PERSON><PERSON>oi", "ark": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "tinhtrangthoitan2", "vrising": "vrising", "madfather": "madfather", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "trởlạivôhạn", "pathoftitans": "<PERSON><PERSON><PERSON>ờngti<PERSON><PERSON>", "frictionalgames": "gamekh<PERSON>khăn", "hexen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "ácquỷbêntrong", "realrac": "th<PERSON><PERSON><PERSON>", "thebackrooms": "nhữngphòngbịbỏquên", "backrooms": "phonglag", "empiressmp": "đếchthíchsmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "gamechdeadwalking", "wehappyfew": "chúngtớvẫnvui", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "trangtha<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ổ", "arksurvival": "sinhtồntrongark", "barotrauma": "barotrauma", "breathedge": "hítthở", "alisa": "alisa", "westlendsurvival": "sốngsótmi<PERSON>", "beastsofbermuda": "quá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkwood": "gỗđen", "survivalhorror": "sợchếtsốngsót", "residentevil": "sinhetoilien", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "xehuỷn", "lifeaftergame": "cuocsongsaugame", "survivalgames": "tr<PERSON><PERSON>ơ<PERSON>nh<PERSON>ồ<PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "<PERSON><PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "bàgià", "littlenightmares2": "cơnácmộngnhỏ2", "signalis": "signalis", "amandatheadventurer": "amandathediadventure", "sonsoftheforest": "concu<PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "gamevideobium", "outlasttrials": "tháchthứcbềnbỉ", "alienisolation": "bẹmtáchbiệt", "undawn": "không<PERSON><PERSON><PERSON>min<PERSON>", "7day2die": "7ngày2chết", "sunlesssea": "biểnkhông<PERSON><PERSON>", "sopravvivenza": "<PERSON><PERSON><PERSON>n", "propnight": "propnight", "deadisland2": "hònđảochết2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "ngàytốithếkỷ", "soma": "soma", "fearandhunger": "sợhãivàđói", "stalkercieńczarnobyla": "theođ<PERSON><PERSON>ścziarnobyla", "lifeafter": "cuocsongsaunay", "ageofdarkness": "thờiđộcta", "clocktower3": "thápđồnghồ3", "aloneinthedark": "mộtmìnhtrongbóngtối", "medievaldynasty": "triề<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ổ", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunker", "worlddomination": "th<PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "s<PERSON><PERSON><PERSON>ủ<PERSON>ínhtrị", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "giếtchócnhỏ", "warhammer40kcrush": "yêuthíchwarhammer40k", "wh40": "wh40", "warhammer40klove": "tinhyeuanhwarhammer40k", "warhammer40klore": "truyềnhayout40k", "warhammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "ilovevindicare", "iloveassasinorum": "iloveassasinorum", "templovenenum": "tempuồngn<PERSON>num", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "nhiệmvụs<PERSON>ủ", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammer<PERSON><PERSON><PERSON>", "civilizationv": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "c<PERSON>những<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "cánhr<PERSON>", "terraformingmars": "biếnthànhsaohỏ", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ứcm<PERSON>nhv<PERSON>", "btd6": "btd6", "supremecommander": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "thếgiớimythology", "args": "args", "rime": "rime", "planetzoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "trạm2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "cảnhbáođỏ", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "chỉhuyvàchinhphục", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "gamechiếnlượ<PERSON>", "anno2070": "anno2070", "civilizationgame": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>minh", "civilization4": "nềnvănhóa4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "nấm", "totalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "pháo", "goodcompany": "côngtytuyệtvời", "civ": "civ", "homeworld": "quênhà", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>v<PERSON><PERSON><PERSON>", "realtimestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomtwocrowns": "vươngquốc2vươngmiện", "eu4": "eu4", "vainglory": "vẻvang", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "lớp<PERSON><PERSON><PERSON><PERSON>", "plagueinc": "plagueinc", "theorycraft": "thuy<PERSON><PERSON>o", "mesbg": "mesbg", "civilization3": "vănminh3", "4inarow": "4trong1", "crusaderkings3": "crusaderkings3", "heroes3": "anhhùng3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "thếgiớihuyếnbinh2", "disciples2": "nguoitheodoi2", "plantsvszombies": "caytrongchongzombie", "giochidistrategia": "gióchidiệntrận", "stratejioyunları": "tr<PERSON><PERSON>ơib<PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "th<PERSON>giớ<PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "chinhphuchauworld", "heartsofiron4": "tráitimsắt4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "trang<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "lòđếchế帝国", "warhammerkillteam": "warhammer<PERSON><PERSON><PERSON>", "goosegooseduck": "mòngmòngdịchbeo", "phobies": "phobies", "phobiesgame": "phobiesgame", "gamingclashroyale": "gameclashroyale", "adeptusmechanicus": "ngư<PERSON><PERSON><PERSON><PERSON>", "outerplane": "khoanggio", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "thếgiớih<PERSON>ềnbí4", "civilization5": "vănminh5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popfulmail": "popfulmail", "shiningforce": "sangbong", "masterduel": "<PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "chươngtrìnhdysonsphere", "transporttycoon": "<PERSON><PERSON><PERSON><PERSON>giaothông", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wullvesville", "ooblets": "ooblets", "planescapetorment": "kế<PERSON><PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "vươngquốcupland", "galaxylife": "cuộc_sống_galaxy", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "đuaxethật3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lờisim4", "fnaf": "fnaf", "outlast": "s<PERSON><PERSON><PERSON>ó<PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "điênrồcủaalicetrởlại", "darkhorseanthology": "tuy<PERSON>tmư<PERSON><PERSON><PERSON>", "phasmophobia": "nỗi_sợ_hồn_ma", "fivenightsatfreddys": "5đ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "khungchếtngười", "littlenightmares": "cơnácmộng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "sốnglại", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "ởnhà", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "tiếcmisfortune", "projectzero": "dựánkhôngprojectzero", "horory": "horror", "jogosterror": "nhímkhủngbố", "helloneighbor": "chàobàconhàngxóm", "helloneighbor2": "chaobanhangxom2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "tròchơikinhdị", "horrorgaming": "gamekinhdi", "magicthegathering": "ma<PERSON>ang<PERSON><PERSON>", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "bocardschongnhanloai", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "tendia", "dixit": "nóixong", "bicyclecards": "bicyclecards", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON>nt<PERSON>ọairune<PERSON>", "solitaire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poker": "poker", "hearthstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "khóachìa", "cardtricks": "mànhuyềnthoạicard", "playingcards": "b<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "ngườich<PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "thẻgiaodịch", "pokemoncards": "thẻpokemon", "fleshandbloodtcg": "thattamchongtcg", "sportscards": "thẻthểthao", "cardfightvanguard": "đấuthẻvanguard", "duellinks": "duellinks", "spades": "bichchì", "warcry": "chiếnhônbay", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "vịvuacủatráitim", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "khángcự", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "thẻyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "duelyugioh", "yugiohocg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dueldisk": "dueldisk", "yugiohgame": "tr<PERSON><PERSON><PERSON>i<PERSON>gio<PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "rồngtrắngm<PERSON>nh", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "chơiboo", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "tròchơiặtứng", "mtgjudge": "mtgjudge", "juegosdecartas": "gamebài", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "truytimmttg", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "tr<PERSON><PERSON>ơib<PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "chơiboo", "battlespirits": "tr<PERSON><PERSON><PERSON>ếns<PERSON><PERSON>", "battlespiritssaga": "t<PERSON><PERSON><PERSON><PERSON><PERSON>hl<PERSON><PERSON>nblaga", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "boožolíky", "facecard": "thẻmặt", "cardfight": "đấuthẻ", "biriba": "biriba", "deckbuilders": "ngườ<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "thẻmathuật", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "boo<PERSON><PERSON><PERSON>", "unstableunicorns": "nhímcókhókhăn", "cyberse": "cyberse", "classicarcadegames": "gamearcadecổđ<PERSON>ển", "osu": "osu", "gitadora": "gitadora", "dancegames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "thứ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "d<PERSON><PERSON>", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "nhảythoi", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dancecentral": "vũtrườngcentral", "rhythmgamer": "game<PERSON><PERSON><PERSON><PERSON><PERSON>u", "stepmania": "stepmania", "highscorerythmgames": "tròchơirhythmđ<PERSON>", "pkxd": "pkxd", "sidem": "b<PERSON><PERSON><PERSON><PERSON>", "ongeki": "ongg<PERSON>i", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "nhảycủ<PERSON><PERSON>", "auditiononline": "thimundongonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "tròchơipuzzlegames", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "khucrubik", "crossword": "<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "<PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "<PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "c<PERSON><PERSON><PERSON>nhghé<PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "b<PERSON><PERSON><PERSON>", "angrybirds": "chimcuatucuong", "escapesimulator": "tráiluvy", "minesweeper": "mìn", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "trangtraithoatkhoivui", "escapegame": "tr<PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "tròchơi3d", "homescapesgame": "gamehomescapes", "wordsearch": "tìm<PERSON><PERSON>", "enigmistica": "<PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "t<PERSON>yệnhuy<PERSON>bí", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlebigplanet": "planetnhỏlớn", "match3puzzle": "hashtagtrậnđấu3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kỳquặc", "rubikcube": "kh<PERSON>ir<PERSON>k", "cuborubik": "kh<PERSON>ir<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "cảnhnhà", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "kh<PERSON>ir<PERSON>k", "cruciverba": "<PERSON><PERSON><PERSON><PERSON>", "ciphers": "mãhó<PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "giảiquyếtpuzzle", "turnipboy": "cuc<PERSON><PERSON>oy", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "khônga<PERSON>", "guessing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonograms": "khóangón", "kostkirubika": "kostkirubika", "crypticcrosswords": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "t<PERSON>ytì<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "tộiácmèo", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "lửatrạ<PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "truyệngansóca", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrinthe", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "t<PERSON>cđộcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "gameportal", "bilmece": "<PERSON><PERSON><PERSON><PERSON>", "puzzelen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "thếgiớimêhoặc", "monopoly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurefight": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "sóctômb<PERSON>", "gacha": "gacha", "wr": "wáo", "fgo": "fgo", "bitlife": "cuộcđ<PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "nhómngôisao", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "vươngq<PERSON><PERSON><PERSON><PERSON>", "alchemystars": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "sânkhấusặcsỡ", "bloonstowerdefense": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "vậnmệnhlệnhđặthàng", "hyperfront": "siêuđỉnh", "knightrun": "cuocchayknight", "fireemblemheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "lựachọnvua", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "tacticool", "cookierun": "chạckhêcookie", "pixeldungeon": "hồphẳngpixel", "arcaea": "arcaea", "outoftheloop": "khôngbiếtgì", "craftsman": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supersus": "cực<PERSON><PERSON>_ngờ", "slowdrive": "lambslow", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "vùngchuyểnđộngmáythú", "bgmi": "bgmi", "teamfighttactics": "ch<PERSON>ếnta<PERSON><PERSON>ú", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "lắcđungvàchơi", "ml": "ml", "bangdream": "bangdream", "clashofclan": "cuộchiềnc<PERSON>", "starstableonline": "ngoisaoonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "côngchúathờigian", "beatstar": "beatstar", "dragonmanialegend": "huy<PERSON><PERSON><PERSON>ại_dragonmanialegend", "hanabi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disneymirrorverse": "gươngph<PERSON><PERSON><PERSON>", "pocketlove": "tìnhyêubỏtúi", "androidgames": "gameandroid", "criminalcase": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "vườnchimnhỏ", "gachalife": "gachalife", "neuralcloud": "đámđámnão", "mysingingmonsters": "qu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "robotcuatao", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "ch<PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "vàocổng", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "trừngphạtcơnôm", "petpals": "b<PERSON>nt<PERSON><PERSON>", "gameofsultans": "vươngquốcvịvu<PERSON>", "arenabreakout": "thoátkhỏiđấutrường", "wolfy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runcitygame": "chơiruncity", "juegodemovil": "juegodemovil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "nhại", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "<PERSON><PERSON><PERSON>", "grandchase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bombmebrasil": "bombeemebrasil", "ldoe": "ldoe", "legendonline": "h<PERSON><PERSON><PERSON><PERSON>on<PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkisáng<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealme", "shadowfight3": "shadowfight3", "limbuscompany": "côngtylimbus", "demolitionderby3": "đảmsát3", "wordswithfriends2": "từvớinhữngngườibạn2", "soulknight": "linhhồnkỵsĩ", "purrfecttale": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "ladyđẹp", "lolmobile": "lolmobile", "harvesttown": "th<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "thếgiớihòanhtuyệtnhỏ", "empiresandpuzzles": "vươngquố<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "empirespuzzles", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilevn", "fanny": "fanny", "littlenightmare": "cơnácmộngnhỏ", "aethergazer": "ngườiquansátbầutrời", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "nướcmắtcủahọcviện", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamemlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "thuyềntrưởngxácsống", "eveechoes": "<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "nữhoàng<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "đấuvớihẻm", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamebgmi", "girlsfrontline": "trieunguyengirl", "jurassicworldalive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulseeker": "nhữngtínhtaih<PERSON>n", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "gamechơimobile", "legendofneverland": "huyenthoaineverland", "pubglite": "pubglite", "gamemobilelegends": "game_di_dong_legends", "timeraiders": "timeraiders", "gamingmobile": "gameonmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "battlemewcats", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "gamechudac", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceclub": "c<PERSON>ulạ<PERSON>bộtìnhyê<PERSON>", "d20": "d20", "pokemongames": "trangtraipokémon", "pokemonmysterydungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonđỏ", "pokemongo": "pokemongo", "pokemonshowdown": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "nhânviênpokemon", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "độiroket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "quáivậtbỏtúi", "nuzlocke": "nuzlocke", "pokemonplush": "g<PERSON><PERSON><PERSON><PERSON>", "teamystic": "teamystic", "pokeball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charmander": "charmander", "pokemonromhack": "hack<PERSON><PERSON><PERSON><PERSON>", "pubgmobile": "pubgmobile", "litten": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinypokemon": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "tayt<PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbryon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "ngủpok<PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "b<PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "trẻemvàpokemon", "pokemonsnap": "chụppokémon", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "sănđồđẹp", "ajedrez": "cờv<PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "c<PERSON><PERSON><PERSON>", "scacchi": "c<PERSON><PERSON><PERSON>", "schaken": "rung<PERSON><PERSON><PERSON>", "skak": "skak", "ajedres": "c<PERSON><PERSON><PERSON>", "chessgirls": "c<PERSON>g<PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON>", "jeudéchecs": "đảokí<PERSON>cờ", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "c<PERSON><PERSON><PERSON>", "chesscanada": "cờcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "mởród", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "ngườidẫnđường", "tiamat": "tiamat", "donjonsetdragons": "hảmbáthóidragons", "oxventure": "oxventure", "darksun": "mặttrờiđen", "thelegendofvoxmachina": "h<PERSON>ennhandevoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "darkmoor", "minecraftchampionship": "minecraftchampionship", "minecrafthive": "hợpquảngchơiminecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "giấcmơsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsminecraft", "mcc": "mcc", "candleflame": "ngọn<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "addon", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "b<PERSON><PERSON><PERSON>", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftđộlại", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "giữ<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "hầmngucminecraft", "minecraftcity": "thànhphốminecraft", "pcgamer": "gamepc", "jeuxvideo": "tr<PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "gamethủ", "levelup": "levelup", "gamermobile": "gameonmobile", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "gameonthepc", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON>", "pcgames": "gamepc", "casualgaming": "gamehaiv<PERSON>ả<PERSON>", "gamingsetup": "b<PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "đẳngcấppc", "pcgame": "gamepc", "gamerboy": "caubegame", "vrgaming": "gamevr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicgamers": "gamekhủng", "onlinegaming": "chơigameonline", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "côgáigame", "gamermoms": "mẹgame", "gamerguy": "chàngtraigame", "gamewatcher": "gamewatcher", "gameur": "gamethủ", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerchicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "nhómchămchỉ", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "nhiemvu", "alax": "alax", "avgn": "avgn", "oldgamer": "gamegià", "cozygaming": "gamephongthuy", "gamelpay": "gamelpay", "juegosdepc": "gamepc", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "giảmạo", "pc4gamers": "pc4gamethủ", "gamingff": "gamekhongnghi", "yatoro": "yatoro", "heterosexualgaming": "gameđồng<PERSON>ính", "gamepc": "gamepc", "girlsgamer": "gamecongái", "fnfmods": "fnfmods", "dailyquest": "hànhtrìnhhằngngày", "gamegirl": "côgáichơigame", "chicasgamer": "côgáigame", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "qu<PERSON>mạnh", "socialgamer": "gameonlinevừađiênvừaxã<PERSON>ội", "gamejam": "gamejam", "proplayer": "gamecuhot", "roleplayer": "nhânvậttronggame", "myteam": "teamcủami", "republicofgamers": "cộngđ<PERSON>ng<PERSON>", "aorus": "aorus", "cougargaming": "côgiàgaming", "triplelegend": "huyềnthoạibậct<PERSON>ầy", "gamerbuddies": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "cầncewekgamers", "christiangamer": "gamethán<PERSON>", "gamernerd": "gamecuong", "nerdgamer": "gamecuatui", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamethủbìnhdân", "89squad": "<PERSON><PERSON><PERSON><PERSON>", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "xemgame", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "gamethủ", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "gamethủplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamechobannangsứckhỏe", "gtracing": "gtracing", "notebookgamer": "gamevớnotebook", "protogen": "protogen", "womangamer": "nữ<PERSON><PERSON><PERSON>", "obviouslyimagamer": "rõràngtôilàgamer", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "th<PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "ngườirơiphẳng", "supernintendo": "siêunintendо", "nintendo64": "nintendo64", "zeroescape": "khôngcóđ<PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nhạcnintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "đ<PERSON>i", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majarasmask", "mariokartmaster": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "nướcmắtvươngquốc", "walkingsimulators": "môphỏngđiđểchơi", "nintendogames": "gamenintendo", "thelegendofzelda": "huy<PERSON><PERSON><PERSON>", "dragonquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "hơithởcủathiênnhiên", "myfriendpedro": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "huy<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "marikart", "kirby": "kirby", "51games": "51<PERSON>ò<PERSON><PERSON><PERSON>", "earthbound": "dễdàngtrởvề", "tales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "crossingthúcưng", "taikonotatsujin": "đ<PERSON><PERSON><PERSON>ng<PERSON><PERSON>", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "siêumario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "mariovàsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildrift": "rừnghoangdã", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "li<PERSON><PERSON><PERSON><PERSON>uy<PERSON>nthoạila", "urgot": "urgot", "zyra": "zyra", "redcanids": "chóđỏ", "vanillalol": "vanillalol", "wildriftph": "wildriftvn", "lolph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adcarry": "quangcao", "lolzinho": "cườichếtmb理", "leagueoflegendsespaña": "leagueoflegendsviệtnam", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "gắnlálegends", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "chơifortnite", "fortnitebr": "fortnitevn", "retrovideogames": "gamexưa", "scaryvideogames": "videogamekinhdị", "videogamemaker": "ngườitạogame", "megamanzero": "megamanzero", "videogame": "tr<PERSON><PERSON><PERSON><PERSON>", "videosgame": "videogame", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "tranchien", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "phapthu101", "battleblocktheater": "battleblocktheater", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "môphỏngnôngtrại", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxviệtnam", "robloxdeutsch": "r<PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "tròchơisandbox", "videogamelore": "tr<PERSON><PERSON><PERSON>i<PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasitêve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "khônggiấcmơ", "starcitizen": "ngôisaocôngdân", "yanderesimulator": "yanderesimulator", "grandtheftauto": "cướpgiaotran<PERSON>", "deadspace": "<PERSON><PERSON><PERSON><PERSON>g", "amordoce": "amordoce", "videogiochi": "gamengoàitrời", "theoldrepublic": "c<PERSON>ng<PERSON>ò<PERSON><PERSON>", "videospiele": "tr<PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "t<PERSON><PERSON><PERSON><PERSON><PERSON>ơ", "adventuregames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON>ngphi<PERSON><PERSON>ư<PERSON>", "storyofseasons": "c<PERSON><PERSON>_ch<PERSON><PERSON>n_c<PERSON><PERSON>_mùa", "retrogames": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "arcaderetro", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "ch<PERSON>i_game_c<PERSON><PERSON><PERSON>n", "vintagegaming": "gamecổ<PERSON><PERSON>", "playdate": "buổihẹnchơi", "commanderkeen": "lãnhđạokhônngoan", "bugsnax": "bgn<PERSON>ắ<PERSON>", "injustice2": "bấtcông2", "shadowthehedgehog": "bóng<PERSON><PERSON><PERSON>ố<PERSON>", "rayman": "rayman", "skygame": "skygame", "zenlife": "cuộcsốngzen", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON>", "mystgames": "mystgames", "blockchaingaming": "gameblockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON>", "consolegaming": "gameconsole", "konsolen": "konsolen", "outrun": "chayvoittoc", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "cuộchành<PERSON><PERSON>nhcôgáiquáiv<PERSON>t", "supergiant": "khổnglồ", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "gamenôngtrại", "juegosviejos": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "hưongdẫntrongtròchơi", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "cuốicùngcủachúngta2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "truyencv", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrma", "payday": "ngàylãnh<PERSON>ư<PERSON>", "chatherine": "catherine", "twilightprincess": "côngchúah<PERSON>àng<PERSON>ôn", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "bãi1", "aestheticgames": "tr<PERSON><PERSON><PERSON>", "novelavisual": "novelavisual", "thecrew2": "nhóm2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "cuộccáchmạngmáythổilá", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "chìakhoá", "aplaguetale": "plaguetale", "fnafsometimes": "fnafthinhthoảng", "novelasvisuales": "tiểuthuyếttrựcquan", "robloxbrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "gamecổ<PERSON><PERSON>", "videojuejos": "gamehay", "videogamedates": "hẹnhòchơi_game", "mycandylove": "tìnhyêuquaykẹo", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "gamehulk", "batmangames": "batmangames", "returnofreckoning": "trởlạicủasátsanh", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON>", "crashracing": "đuaxengã", "3dplatformers": "tròchơi3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "gamecũ", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "t<PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "gameuse", "offmortisghost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybunny": "bun<PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "t<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "cuộcphihành<PERSON>", "quickflash": "nhanhchóng", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "qu<PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "mộtthếgi<PERSON>", "metaquest": "metaquest", "animewarrios2": "animechiếnbinh2", "footballfusion": "kếtnốibóngđá", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "kimloạiquaycuồng", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "môphỏng", "symulatory": "môphỏng", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "xứsởthầntiênonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownviếtlại", "simracing": "<PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "hỗnloạnthànhphố", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "gameparty", "graveyardkeeper": "<PERSON>ư<PERSON><PERSON>", "spaceflightsimulator": "môphỏngchuyênbay", "legacyofkain": "di_san_cua_kain", "hackandslash": "chặtchém", "foodandvideogames": "đồănvàgame", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "bầychógiữach<PERSON>", "truckingsimulator": "môphỏngxetải", "horizonworlds": "horizonworlds", "handygame": "tròchơiđỉnh", "leyendasyvideojuegos": "<PERSON>uy<PERSON><PERSON><PERSON>ạ<PERSON>", "oldschoolvideogames": "gamecổ<PERSON><PERSON>", "racingsimulator": "môphỏngđuaรถ", "beemov": "bee<PERSON>v", "agentsofmayhem": "nhânvậtcủanỗisupport", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "c<PERSON>ngtrờiolym<PERSON><PERSON>", "monsterhunternow": "sănquá<PERSON>ậ<PERSON>", "rebelstar": "<PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "gameindie", "indiegaming": "gameindie", "indievideogames": "tròchơindie", "indievideogame": "gameindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "ngư<PERSON><PERSON><PERSON>ệninsomnia<PERSON>", "bufffortress": "pháopháo", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "<PERSON><PERSON><PERSON><PERSON>", "futureclubgames": "tranglậpnha1tươnglai", "mugman": "ngư<PERSON><PERSON><PERSON>", "insomniacgames": "choi<PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "khoahocmangaperture", "backlog": "danh_sách_chờ", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "nhânvậtgames", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON>", "beastlord": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "gamecổ<PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "nhạcgame", "dragonsync": "rồngkếtnối", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "đuapcr", "berserk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "thuy<PERSON>uo<PERSON>", "saintseiya": "th<PERSON>nt<PERSON><PERSON>ei<PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animebuồn", "darkerthanblack": "t<PERSON><PERSON>_<PERSON><PERSON>n_cả_đen", "animescaling": "phimhoạthìnhtăng<PERSON>ường", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "cá", "retroanime": "animecổđiển", "animes": "anime", "supersentai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "ch<PERSON><PERSON>_tối", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "đỉnhhơn", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonemùa1", "rapanime": "rapanime", "chargemanken": "sạcnam", "animecover": "animecover", "thevisionofescaflowne": "tầmnhìncủaesca<PERSON>ne", "slayers": "kẻsátthủ", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "lực_l<PERSON><PERSON><PERSON>_lửa", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "nhậtký<PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "nàng<PERSON>ê<PERSON><PERSON>", "kamisamakiss": "h<PERSON>nvang", "blmanga": "blmanga", "horrormanga": "truye<PERSON><PERSON>", "romancemangas": "truyenmangaotoc", "karneval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bongostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "chỉsốmathuậtnhấtđịnh", "sao": "sao", "blackclover": "clover<PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "thang<PERSON>tung", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rezero": "rezero", "swordartonline": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "quy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstercongchua", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "animeboy", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "đẹpnhưthuốc", "theboyandthebeast": "cuavangvadiu", "fistofthenorthstar": "võđàib<PERSON>phươ<PERSON>", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "cáchgiữmẹbênmình", "fullmoonwosagashite": "trongtrangbongsak<PERSON>uthang", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "đỉnhcaovõthuật", "bakihanma": "b<PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "côg<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "ma<PERSON><PERSON><PERSON>", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "bạnb<PERSON>", "sailorsaturn": "thuythuatan", "dio": "<PERSON><PERSON><PERSON>", "sailorpluto": "thuythuypluto", "aloy": "aloy", "runa": "runa", "oldanime": "animecũ", "chainsawman": "ngườichặtcây", "bungoustraydogs": "bungoustraydogs", "jogo": "ch<PERSON><PERSON>", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyobăng<PERSON><PERSON>ng", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "quỷnhantrem", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "trai_trai", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "bocongchua_sakura", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "quái_v<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "dảodằntrongthángtư", "buggytheclown": "bong<PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "tùnhântàibiểnsâu", "jojolion": "jojo<PERSON>", "deadmanwonderland": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannacá", "sukuna": "<PERSON>kuna", "darwinsgame": "trangcho<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "tráitimpandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "thẻbắtmasakura", "stolas": "stolas", "devilsline": "da<PERSON><PERSON>", "toyoureternity": "đểđếnvĩnhhằng", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "thưởihằnblue", "griffithberserk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "vợcủamahoutsukai", "yuki": "yuki", "erased": "xóa", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "exor<PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "khinhkh<PERSON>", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "tr<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "thangkhoaho<PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "my<PERSON><PERSON><PERSON>ling", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undeadunluck": "bấttửkhóđỡ", "romancemanga": "truyềntranhromance", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "b<PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "tạmbiệt", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "khungtrờiđồngthời", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "maicherry", "housekinokuni": "nhàkinokuni", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "tr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsi<PERSON><PERSON>", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animenhật", "animespace": "khônggiananime", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "hyvọnga<PERSON>oli", "animedub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animanga": "animevàmanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animeviễntưởng", "ratman": "chuotman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "nekotrai", "gashbell": "gashbell", "peachgirl": "cô_gái_đào", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "c<PERSON><PERSON>ạ<PERSON>bộchóbitchyarichin", "dragonquestdai": "dragonquestdai", "heartofmanga": "tráitimcủamanga", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "kylucragnarok", "funamusea": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "hướngdẫnkhóquá", "overgeared": "overgear", "toriko": "<PERSON><PERSON>o", "ravemaster": "ma<PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatnuvolion", "kamen": "kamen", "mangaislife": "mangaistcuocdoi", "dropsofgod": "<PERSON><PERSON>ọ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "giaosudangyeuonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "l<PERSON><PERSON>", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "giấcmơgrandblue", "bloodplus": "<PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "máuhuyếtvớianime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "animeđẫmmáu", "animegirls": "côgáianime", "sharingan": "chia_sẻ_an", "crowsxworst": "crowsxghẻlạnh", "splatteranime": "vẽanime", "splatter": "v<PERSON>ng<PERSON>g", "risingoftheshieldhero": "sựtrỗidậycủaanhhùnghield", "somalianime": "animesomali", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animetâybannha", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "concuatamca", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeidols": "thần_t<PERSON><PERSON><PERSON>_anime", "isekaiwasmartphone": "isekaiđãcósmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "goin<PERSON><PERSON><PERSON>đêm", "bakuganbrawler": "brawlbakugan", "bakuganbrawlers": "brawlersbakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "bongcay", "tsubasachronicle": "tsubasachronicle", "findermanga": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "cáquànglạc", "kuragehime": "côbéthuỷtinh", "paradisekiss": "nụhônt<PERSON>ê<PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "persocom", "omniscientreadersview": "gócnhìntritue", "animecat": "anime_mèo", "animerecommendations": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "moannime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "hàitìnhtuổiteen", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotkhổnglồ", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ệ<PERSON>ủaj<PERSON>jo", "fullmetalalchemist": "al<PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "cuốchànhtrìnhjojobizarre", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "ranger<PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "saocáo", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "th<PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "rồngball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "cuộcphiê<PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "trunghocdxd", "goku": "goku", "broly": "broly", "shonenanime": "animeboy", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "nhàmha", "demonslayer": "thợsănquỷ", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "tấncôngvàkhổnglồ", "erenyeager": "gan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tr<PERSON><PERSON>ơib<PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "tấncôngcáctitans", "theonepieceisreal": "one<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "kẻbáovệ", "mobpsycho": "mobpsycho", "aonoexorcist": "exor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>àngtra<PERSON>ph<PERSON>", "digimonstory": "c<PERSON><PERSON>_chuyện_digimon", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gto": "gto", "ouranhostclub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flawlesswebtoon": "webtoonhoànhảo", "kemonofriends": "bạnth<PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nhật_ký_hằng_ngày", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>飞", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "chỉvìvậy", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "phodasaints", "recuentosdelavida": "nhậ<PERSON><PERSON>đời"}