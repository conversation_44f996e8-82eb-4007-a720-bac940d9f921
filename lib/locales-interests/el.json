{"2048": "2048", "mbti": "mbti", "enneagram": "εννεάγραμμα", "astrology": "αστρολογία", "cognitivefunctions": "γνωστικέςλειτουργίες", "psychology": "ψυχολογία", "philosophy": "φιλοσοφία", "history": "ιστορία", "physics": "φυσική", "science": "επιστήμη", "culture": "πολιτισμ<PERSON>ς", "languages": "γλώσσες", "technology": "τεχνολογία", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "αστρολογ<PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "εννεάγραμμαmemes", "showerthoughts": "σκέψειςστοντουζ", "funny": "αστεία", "videos": "βίντεο", "gadgets": "γκάτζετ", "politics": "πολιτική", "relationshipadvice": "συμβουλέςσχέσεων", "lifeadvice": "συμβουλέςζωής", "crypto": "κρύπτο", "news": "νέα", "worldnews": "παγκόσμιεςειδήσεις", "archaeology": "αρχαιολογία", "learning": "εκμάθηση", "debates": "συζητήσεις", "conspiracytheories": "θεωρίεςσυνομωσίας", "universe": "σύμπαν", "meditation": "διαλογισμός", "mythology": "μυθολογία", "art": "τέχνη", "crafts": "χειροτεχνίες", "dance": "χορ<PERSON>ς", "design": "σχέδιο", "makeup": "μακιγι<PERSON>ζ", "beauty": "ομορφιά", "fashion": "μόδα", "singing": "τραγούδι", "writing": "γράψιμο", "photography": "φωτογραφία", "cosplay": "cosplay", "painting": "ζωγραφική", "drawing": "ζωγραφική", "books": "βιβλία", "movies": "ταινίες", "poetry": "ποίηση", "television": "τηλεόραση", "filmmaking": "κινηματογράφηση", "animation": "κινούμενασχέδια", "anime": "anime", "scifi": "επιστημονικήφαντασία", "fantasy": "φαντασία", "documentaries": "ντοκιμαντέρ", "mystery": "μυστήριο", "comedy": "κωμωδίες", "crime": "έγκλημα", "drama": "δράμα", "bollywood": "μπόλιγουντ", "kdrama": "kδράμα", "horror": "τρόμος", "romance": "αγάπη", "realitytv": "τηλεόρασηριάλιτι", "action": "δράση", "music": "μουσική", "blues": "μπλουζ", "classical": "κλασικήμουσική", "country": "κάντρι", "desi": "desi", "edm": "ηλεκτρονικήμουσική", "electronic": "ηλεκτρονική", "folk": "λαϊκήμουσική", "funk": "φανκ", "hiphop": "χιπχοπ", "house": "house", "indie": "indie", "jazz": "τζαζ", "kpop": "kποπ", "latin": "λάτιν", "metal": "μέταλ", "pop": "ποπ", "punk": "πανκ", "rnb": "rnb", "rap": "ραπ", "reggae": "ρέγκε", "rock": "ρ<PERSON><PERSON>", "techno": "τέκνο", "travel": "ταξίδια", "concerts": "συναυλίες", "festivals": "φεστιβάλ", "museums": "μουσεία", "standup": "στανταπ", "theater": "θέατρο", "outdoors": "εξοχή", "gardening": "κηπουρική", "partying": "πάρτι", "gaming": "παιχνίδια", "boardgames": "επιτραπέζια", "dungeonsanddragons": "dungeonsanddragons", "chess": "σκάκι", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "πόκεμον", "food": "φαγητό", "baking": "ζαχαροπλαστική", "cooking": "μαγειρική", "vegetarian": "χορτο<PERSON><PERSON><PERSON>ος", "vegan": "vegan", "birds": "πτηνά", "cats": "γάτες", "dogs": "σκύλοι", "fish": "ψάρια", "animals": "ζώα", "blacklivesmatter": "blacklivesmatter", "environmentalism": "περιβαλλοντισμός", "feminism": "φεμινισμός", "humanrights": "ανθρώπιναδικαιώματα", "lgbtqally": "lgbtqσύμμαχος", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "τρανςσύμμαχος", "volunteering": "εθελοντισμός", "sports": "αθλήματα", "badminton": "μπάντμιντον", "baseball": "μπέιζμπολ", "basketball": "μπάσκετ", "boxing": "πυγμαχία", "cricket": "κρίκετ", "cycling": "ποδηλασία", "fitness": "fitness", "football": "ποδόσφαιρο", "golf": "γκολφ", "gym": "γυμναστήριο", "gymnastics": "ενόργανη", "hockey": "χόκεϊ", "martialarts": "πολεμικέςτέχνες", "netball": "νέτμπολ", "pilates": "πιλάτες", "pingpong": "πινγκπονγκ", "running": "τρέξιμο", "skateboarding": "σκέιτμπορντ", "skiing": "σκι", "snowboarding": "σνόουμπορντ", "surfing": "σερφ", "swimming": "κολύμβηση", "tennis": "τένις", "volleyball": "βόλεϊ", "weightlifting": "άρσηβαρών", "yoga": "γιόγκα", "scubadiving": "κατάδυση", "hiking": "πεζοπορία", "capricorn": "αιγόκερως", "aquarius": "υδρο<PERSON><PERSON><PERSON>", "pisces": "ιχθύς", "aries": "κριός", "taurus": "ταύρος", "gemini": "δίδυμοι", "cancer": "καρ<PERSON><PERSON>νος", "leo": "λέων", "virgo": "παρθένος", "libra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scorpio": "σκορπιός", "sagittarius": "τοξότης", "shortterm": "βραχυπρόθεσμα", "casual": "χαλαρή", "longtermrelationship": "μακροχρόνιασχέση", "single": "μονος", "polyamory": "πολυσυντροφικότητα", "enm": "ανοιχτέςσχέσεις", "lgbt": "λγβτ", "lgbtq": "λγβτκ", "gay": "ομοφυλόφιλος", "lesbian": "λεσβία", "bisexual": "αμφιφυλόφιλος", "pansexual": "πανσεξουαλικ<PERSON>ς", "asexual": "ασέξουαλ", "reddeadredemption2": "κόκκινοθανατ<PERSON>επανόρθωση2", "dragonage": "δράκοςτουαιώνα", "assassinscreed": "assassinscreed", "saintsrow": "άγιοιδρόμοι", "danganronpa": "ντανγκανρόπα", "deltarune": "δμεταμόρφωση", "watchdogs": "ρεπόρτερδημοσίου", "dislyte": "dislyte", "rougelikes": "ρουγ<PERSON><PERSON><PERSON><PERSON>κς", "kingsquest": "βασιλικήαναζήτηση", "soulreaver": "ψυχοληστής", "suikoden": "σουικοδεν", "subverse": "υποσυνείδητο", "legendofspyro": "θρύλοςτουσπάιρο", "rouguelikes": "ρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "syberia": "συβερία", "rdr2": "rdr2", "spyrothedragon": "σπυροτοδράκο", "dragonsdogma": "δράκωνεςδόγμα", "sunsetoverdrive": "ηλιοβασίλεμαoverdrive", "arkham": "άρκαμ", "deusex": "θεόςεξ", "fireemblemfates": "fireemblemfates", "yokaiwatch": "γιωκάιγουότς", "rocksteady": "rocksteady", "litrpg": "λιτρπγ", "haloinfinite": "χαλοϊνφίνιτε", "guildwars": "συνομοσπονδίεςπολέμου", "openworld": "ανοιχτ<PERSON>ς<PERSON>όσμος", "heroesofthestorm": "ήρωεςτηςκαταιγίδας", "cytus": "κυτус", "soulslike": "ψυχές<PERSON>αντούρνες", "dungeoncrawling": "καταδ<PERSON>ω<PERSON><PERSON>σεκάταση", "jetsetradio": "jetsetradio", "tribesofmidgard": "φυλές<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "σχεδιασμ<PERSON>ςτουκόσμου", "lordsoftherealm2": "lordsτουβασιλείου2", "baldursgate": "μπάλντου<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ", "colorvore": "χρωμοβόρο", "medabots": "μενταμπότς", "lodsoftherealm2": "παράξενοιτουβασιλείου2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "εμβυθιστικάσυμπόσια", "okage": "οκάγκε", "juegoderol": "παιχνίδιρόλων", "witcher": "μάγος", "dishonored": "άτιμος", "eldenring": "elden<PERSON>", "darksouls": "σκοτεινέςψυχές", "kotor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wynncraft": "γουιν<PERSON><PERSON><PERSON><PERSON>τ", "witcher3": "witcher3", "fallout": "καταστροφή", "fallout3": "φούλιν3", "fallout4": "fallout4", "skyrim": "σκάιριμ", "elderscrolls": "παλιέςκύλινδροι", "modding": "modding", "charactercreation": "δημιουργ<PERSON>αχ<PERSON>ρακτήρων", "immersive": "απορροφητικό", "falloutnewvegas": "falloutnewvegas", "bioshock": "μπιο<PERSON><PERSON><PERSON><PERSON>", "omori": "ομόρι", "finalfantasyoldschool": "τελικήφαντασίαπαλιόσχολείο", "ffvii": "ffvii", "ff6": "φφ6", "finalfantasy": "τελικήφαντασία", "finalfantasy14": "τελικήφαντασία14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "τελικοφαντασιαματόγια", "lalafell": "λαλα<PERSON><PERSON>λ", "dissidia": "δισιδία", "finalfantasy7": "τελικόςφαντασίας7", "ff7": "ff7", "morbidmotivation": "μακάβριαπαρακίνηση", "finalfantasyvii": "τελικόςφάντασι7", "ff8": "ff8", "otome": "ογκόμες", "suckerforlove": "κολλημ<PERSON>νοςμετηναγάπη", "otomegames": "οτομέπαιχνίδια", "stardew": "σταρντιου", "stardewvalley": "stardewvalley", "ocarinaoftime": "ογκαρ<PERSON>να<PERSON>ουχρόνου", "yiikrpg": "γιικρπγ", "vampirethemasquerade": "βρυκόλακες_στο_μάσκα_βεεμπορν", "dimension20": "διάσταση20", "gaslands": "gaslands", "pathfinder": "καθοδηγητής", "pathfinder2ndedition": "παρουσίαση2ηςέκδοσης", "shadowrun": "σκιώδηςτρέξιμο", "bloodontheclocktower": "αίμαστορολ<PERSON>ι", "finalfantasy15": "τελικόφαίνεσθαι15", "finalfantasy11": "τελευταίοφαίνταζι11", "finalfantasy8": "τελικόςφαντασίας8", "ffxvi": "ffxvi", "lovenikki": "αγάπηνίκι", "drakengard": "ντράκεγκαρντ", "gravityrush": "βαρύτητακαταιγίδα", "rpg": "rpg", "dota2": "dota2", "xenoblade": "ξενόμπλαιντ", "oneshot": "μίαφορά", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "υπερκυρίαρχος", "yourturntodie": "ήρθεησειράσουναπεθάνεις", "persona3": "προσωπικότητα3", "rpghorror": "rpgτρομάρα", "elderscrollsonline": "elderscrollsonline", "reka": "ρέκα", "honkai": "χόνκαϊ", "marauders": "μαραuderες", "shinmegamitensei": "σινμεγκαμιτενσέι", "epicseven": "επίκτητος7", "rpgtext": "rpgκείμενο", "genshin": "γενσιν", "eso": "έσο", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "μόροουιντ", "starwarskotor": "σταργου<PERSON><PERSON><PERSON>κωτορ", "demonsouls": "δαίμονεςψυχές", "mu": "μου", "falloutshelter": "καταφύγιοκαταστροφής", "gurps": "γκουρπς", "darkestdungeon": "σκοτεινόςχάσματος", "eclipsephase": "φασηεκλειψης", "disgaea": "ντισγάια", "outerworlds": "εξωτερικ<PERSON>ίκόσμοι", "arpg": "αrpg", "crpg": "κρπγ", "bindingofisaac": "bindingofisaac", "diabloimmortal": "διάβολ<PERSON><PERSON><PERSON>άνατος", "dynastywarriors": "πολεμιστέςτηςδυναστείας", "skullgirls": "κρανιακάκορίτσια", "nightcity": "νύχταπόλη", "hogwartslegacy": "κληρονο<PERSON>ιάτουχόγκουαρτς", "madnesscombat": "παραφρο<PERSON>ύνημάχης", "jaggedalliance2": "μαχενιτόαλλιένς2", "neverwinter": "ποτέχειμώνα", "road96": "road96", "vtmb": "vtmb", "chimeraland": "χιμε<PERSON><PERSON>αντ", "homm3": "χμμ3", "fe3h": "fe3h", "roguelikes": "ρογκούλακες", "gothamknights": "γκόθαμνάιτς", "forgottenrealms": "ξεχασμέναβασίλεια", "dragonlance": "δράκονταςμάχες", "arenaofvalor": "αρένατης<PERSON><PERSON><PERSON>ας", "ffxv": "ffxv", "ornarpg": "ορνάρπγ", "toontown": "πόλητουοράματος", "childoflight": "παιδίτουφωτός", "aq3d": "αq3d", "mogeko": "μογέκο", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "ντίντζιμονκόσμος", "monsterrancher": "τερατο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "ecopunk": "οικολογι<PERSON><PERSON>ςπόνκι", "vermintide2": "vermintide2", "xeno": "ξένο", "vulcanverse": "βολκανβερς", "fracturedthrones": "σπασμέναθρόνα", "horizonforbiddenwest": "ορίζονταςαπαγορευμένουδυτικού", "twewy": "twewy", "shadowpunk": "σκιάκι", "finalfantasyxv": "τελικήφαντασίαxv", "everoasis": "έβεροάσις", "hogwartmystery": "hogwartmystery", "deltagreen": "deltagreen", "diablo": "διάβολος", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "χτυπιέται", "lastepoch": "τελευταίαεποχή", "starfinder": "αστροανακριτής", "goldensun": "χρυσ<PERSON><PERSON><PERSON><PERSON><PERSON>ος", "divinityoriginalsin": "θεϊκή_αμαρτία", "bladesinthedark": "μαχα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>κοτάδι", "twilight2000": "δυτική2000", "sandevistan": "σάντεβισταν", "cyberpunk": "κυβερν<PERSON>πάνκ", "cyberpunk2077": "κυβερνοπάνκ2077", "cyberpunkred": "κυβερνοπανκκόκκινο", "dragonballxenoverse2": "δράκοςμπάλξενόβερσε2", "fallenorder": "καταρ<PERSON><PERSON><PERSON><PERSON><PERSON>ταξίδι", "finalfantasyxii": "τελικήφαντασία12", "evillands": "κακέςχώρα", "genshinimact": "genshinimact", "aethyr": "αιθ<PERSON><PERSON>", "devilsurvivor": "διάβολοςεπιβίωσης", "oldschoolrunescape": "παλιόσχολπορτάλιση", "finalfantasy10": "τελικήφαντασία10", "anime5e": "anime5e", "divinity": "θεότητα", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "παλιέςγειτονιές", "adventurequest": "περιπέτειααναζήτησης", "dagorhir": "νταγκ<PERSON><PERSON><PERSON><PERSON>ρ", "roleplayingames": "παιχνίδιαρολό", "roleplayinggames": "παιχνίδιαρόλων", "finalfantasy9": "τελικήφαντασία9", "sunhaven": "ήλιοςμ<PERSON>ζ<PERSON>", "talesofsymphonia": "ιστορίεςσυμφωνίας", "honkaistarrail": "χόνκαϊστάρραιλ", "wolong": "γουλον<PERSON>κ", "finalfantasy13": "τελικόςφαντασία13", "daggerfall": "μαχαίριτουβραχίονα", "torncity": "σκισμένηπόλη", "myfarog": "μουφαρ<PERSON><PERSON>κ", "sacredunderworld": "ιερόςκοσμος", "chainedechoes": "αλυσ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν", "darksoul": "σκοτεινηψυχη", "soulslikes": "ψυχέςόμοιες", "othercide": "άλλοκτονία", "mountandblade": "βουνόκαιμαχαίρι", "inazumaeleven": "ινάζουμαέλεβεν", "acvalhalla": "ακβαλχάλα", "chronotrigger": "χρονικόσυγκλονιστής", "pillarsofeternity": "στήλεςτης<PERSON><PERSON>ωνιότητας", "palladiumrpg": "παλλαντιούρπγ", "rifts": "σχίσματα", "tibia": "τιβία", "thedivision": "ηδιαίρεση", "hellocharlotte": "γειασ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ", "legendofdragoon": "θρύλοςτουδράκου", "xenobladechronicles2": "ξενομπλέιντχρόνοι2", "vampirolamascarada": "βαμπιρολαμασκαρανα", "octopathtraveler": "οκτώδρομοςταξιδιώτης", "afkarena": "αφκαρένα", "werewolftheapocalypse": "λύκοςτ<PERSON>αποκαλυπτικο", "aveyond": "άβυδος", "littlewood": "μικρούλα", "childrenofmorta": "παιδιάτουμόρτα", "engineheart": "κινητή<PERSON><PERSON><PERSON>κ<PERSON><PERSON><PERSON>ι<PERSON>ς", "fable3": "θρύλος3", "fablethelostchapter": "μύθοςτο<PERSON>αμένοκεφάλαιο", "hiveswap": "hiveswap", "rollenspiel": "ρολ<PERSON><PERSON><PERSON>ιλ", "harpg": "χάρπγ", "baldursgates": "μπαλντουρςγκεϊτς", "edeneternal": "ηεδέναιώνια", "finalfantasy16": "τελικήφαντασία16", "andyandleyley": "άντυκαιλεϊλέι", "ff15": "ff15", "starfield": "αστερόπεδιο", "oldschoolrevival": "παλιάσχολικόεπιστρέφει", "finalfantasy12": "τελικήφαντασία12", "ff12": "φφ12", "morkborg": "μορκμπόργκ", "savageworlds": "άγρι<PERSON><PERSON><PERSON><PERSON><PERSON>ος", "diabloiv": "ντιάμπλο4", "pve": "pve", "kingdomheart1": "βασίλειοκαρδιά1", "ff9": "ff9", "kingdomheart2": "βασίλειοκαρδιάς2", "darknessdungeon": "σκοτεινόςθάλαμος", "juegosrpg": "rpgπαιχνίδια", "kingdomhearts": "βασίλ<PERSON>ι<PERSON>κ<PERSON>ρδιών", "kingdomheart3": "βασίλειοκαρδιάς3", "finalfantasy6": "τελευτα<PERSON>αφαντασία6", "ffvi": "φφβι", "clanmalkavian": "κλανμαλκαβιαν", "harvestella": "θεριστέλλα", "gloomhaven": "γκλουμχέιβεν", "wildhearts": "άγριεςκαρδιές", "bastion": "μπάστιον", "drakarochdemoner": "δρακάρχοδαιμόνια", "skiesofarcadia": "ουρανοίτουαρκαδία", "shadowhearts": "σκιακαρδιές", "nierreplicant": "νιερρεπλικάντ", "gnosia": "γνωσία", "pennyblood": "λεφτάαίμα", "breathoffire4": "φλόγααναπνοής4", "mother3": "μαμά3", "cyberpunk2020": "κυβερνοπανκ2020", "falloutbos": "falloutbos", "anothereden": "άλληεδένη", "roleplaygames": "παιχνίδιαρόλων", "roleplaygame": "παιχνίδιρόλων", "fabulaultima": "φανταστικατελευταία", "witchsheart": "καρδιέςτηςμάγισσας", "harrypottergame": "παίχνιδιχάριπότερ", "pathfinderrpg": "παθοχάρτηςrpg", "pathfinder2e": "παθφάιντερ2ε", "vampirilamasquerade": "βαμπιρομασκέρα", "dračák": "δράκος", "spelljammer": "μαγικάταξίδια", "dragonageorigins": "δρακογεννημένηαρχή", "chronocross": "χρονικ<PERSON><PERSON><PERSON><PERSON>υρ<PERSON>ς", "cocttrpg": "cocttrpg", "huntroyale": "κυνηγοίτουβασιλείου", "albertodyssey": "αλμπέρτο<PERSON>αρ<PERSON>ταξη", "monsterhunterworld": "τέρας<PERSON>υνηγ<PERSON>ςκόσμος", "bg3": "bg3", "xenogear": "ξενόγκαρ", "temtem": "τεμτεμ", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "ψυχήblade", "baldursgate3": "baldursgate3", "kingdomcome": "βασίλειοέρχεται", "awplanet": "awplanet", "theworldendswithyou": "οκόσμοςτέλεινεισένασένασένα", "dragalialost": "dragalialost", "elderscroll": "παλιόφυλλο", "dyinglight2": "dyinglight2", "finalfantasytactics": "τελικόςφαντα<PERSON><PERSON>α<PERSON><PERSON>ςτακτικής", "grandia": "grandia", "darkheresy": "σκοτεινήςαίρεσης", "shoptitans": "shoptitans", "forumrpg": "φόρουμrpg", "golarion": "γκολάριον", "earthmagic": "μαγείατηςγης", "blackbook": "μαύροβιβλίο", "skychildrenoflight": "ουρανοίπαιδιάτουφωτός", "gryrpg": "γκρυρπγ", "sacredgoldedition": "ιερός<PERSON><PERSON>υ<PERSON><PERSON>ςέκδοση", "castlecrashers": "κάστροκατα<PERSON>ρίπτες", "gothicgame": "γοτθικόπαιχνίδι", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "φάντασμασύνδεσητόκιο", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "προφάντ", "starrails": "αστέρες<PERSON>τρα<PERSON><PERSON>ς", "cityofmist": "πόλητουανέμου", "indierpg": "ανεξάρτητοrpg", "pointandclick": "κλίκκαιβρείτε", "emilyisawaytoo": "εμιλιέφυγεκιόλας", "emilyisaway": "ηemilyφεύγει", "indivisible": "αδιάσπαστος", "freeside": "ελεύθερηπλευρά", "epic7": "επικ7", "ff7evercrisis": "ff7παντακρίση", "xenogears": "ξενόγκειρς", "megamitensei": "μεγαμίτενσέι", "symbaroum": "συμπαρούμ", "postcyberpunk": "μετακυβερνοπανκ", "deathroadtocanada": "θά<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νδρόμοπροςτον<PERSON>αναδά", "palladium": "παλλάδιον", "knightjdr": "ιππότης<PERSON>dr", "monsterhunter": "κυνηγόςτέρατων", "fireemblem": "φωτιά_έμβλημα", "genshinimpact": "genshinimpact", "geosupremancy": "γεωαsupremancy", "persona5": "περσόνα5", "ghostoftsushima": "φαντάσμα<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "σεκιρό", "monsterhunterrise": "κηνυγ<PERSON>ς<PERSON><PERSON><PERSON><PERSON><PERSON>ώνανάταση", "nier": "νιέρ", "dothack": "dothack", "ys": "ψ", "souleater": "ψυχοφάγοι", "fatestaynight": "fatestaynight", "etrianodyssey": "ετεριανοδυσσέας", "nonarygames": "μηναιρεωνπαιχνιδια", "tacticalrpg": "τακτικήrpg", "mahoyo": "μαχόυο", "animegames": "ανιμεπαιχνίδια", "damganronpa": "ντάμγκανρονπά", "granbluefantasy": "γρανμπλουφαンタζυ", "godeater": "φαγόχορτος", "diluc": "ντίλουκ", "venti": "βεντί", "eternalsonata": "αιώνιασονάτα", "princessconnect": "πριγκιποσυνδεσμος", "hexenzirkel": "μάγισσες", "cristales": "κρυστάλλινες", "vcs": "vcs", "pes": "πες", "pocketsage": "τσέπησοφία", "valorant": "βαλοραντ", "valorante": "βαλοράντε", "valorantindian": "valorantindia", "dota": "ντότα", "madden": "μάντεν", "cdl": "κνλ", "efootbal": "eποδόσφαιρο", "nba2k": "nba2k", "egames": "ηλεκτρονικάπαιχνίδια", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "ηλεκτρονικά_σπορ", "mlg": "mlg", "leagueofdreamers": "συνομοσπονδίαονείρων", "fifa14": "fifa14", "midlaner": "μιντλ<PERSON><PERSON><PERSON><PERSON>ρ", "efootball": "eποδόσφαιρο", "dreamhack": "όνειροχάκι", "gaimin": "γκέιμιν", "overwatchleague": "αλγόριθμοςανταγωνισμού", "cybersport": "κυβερνοαθλητισμός", "crazyraccoon": "τρελέννυφίδι", "test1test": "δοκιμή1δοκιμή", "fc24": "fc24", "riotgames": "riotgames", "eracing": "ερεικασιγ", "brasilgameshow": "μπρα<PERSON><PERSON><PERSON><PERSON><PERSON>έιμσχοου", "valorantcompetitive": "αγωνιστι<PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantgr", "csgo": "csgo", "tf2": "tf2", "portal2": "πύλη2", "halflife": "μισήζωή", "left4dead": "αριστερ<PERSON><PERSON><PERSON><PERSON>α<PERSON><PERSON><PERSON>ρ<PERSON>ς", "left4dead2": "left4dead2", "valve": "βαλβίδα", "portal": "πύλη", "teamfortress2": "ομάδακαταφυγίου2", "everlastingsummer": "αιώνιοκαλοκαίρι", "goatsimulator": "προσομοιωγίδας", "garrysmod": "γκαρύςμοτ", "freedomplanet": "ελευθερίαπλανήτη", "transformice": "μεταμόρφωση", "justshapesandbeats": "μόνοσχήματακαιχτύποι", "battlefield4": "μάχη4", "nightinthewoods": "βράδιστοδ<PERSON><PERSON>ος", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "βαθιάπετριάγαλακτικής", "riskofrain2": "ρίσκοβροχής2", "metroidvanias": "μετροϊντβάνιας", "overcooked": "καμμένος", "interplanetary": "διαγαλαξιακό", "helltaker": "κατακτητήςτηςκόλασης", "inscryption": "αποκρυπτογράφηση", "7d2d": "7d2d", "deadcells": "νεκράκύτταρα", "nierautomata": "νίεραυτομάτα", "gmod": "gmod", "dwarffortress": "κάστρον<PERSON>νων", "foxhole": "φωλ<PERSON>ς", "stray": "αδέσποτος", "battlefield": "μάχη", "battlefield1": "μάχη1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "υποβρύχιο", "eyeb": "μάτι", "blackdesert": "μαύρηέρημος", "tabletopsimulator": "παιχνίδιασετραπέζι", "partyhard": "γλεντάμεσφοβερά", "hardspaceshipbreaker": "σκληρόςδιαστημοναυ<PERSON><PERSON>ς", "hades": "χάθες", "gunsmith": "οικοδόμοςόπλων", "okami": "ωκαμί", "trappedwithjester": "παγιδευμένοιμετοντ<PERSON><PERSON>στερ", "dinkum": "μπαμπούληδες", "predecessor": "προγον<PERSON>ς", "rainworld": "βροχήκοσμος", "cavesofqud": "σπήλαιατουκουτ", "colonysim": "κολων<PERSON><PERSON><PERSON><PERSON><PERSON>им", "noita": "νοίτα", "dawnofwar": "αυγήτουπολέμου", "minionmasters": "μάστερςτουμίνιον", "grimdawn": "γκριμντάουν", "darkanddarker": "σκοτεινότερ<PERSON>καισκοτεινότερο", "motox": "μοτ<PERSON><PERSON>", "blackmesa": "μαύρηγέννηση", "soulworker": "ψυχολειτου<PERSON>γ<PERSON>ς", "datingsims": "παιχνίδια_ραντεβού", "yaga": "γιάγα", "cubeescape": "kubasdiso", "hifirush": "hiφ<PERSON>ρ<PERSON>ς", "svencoop": "σβενκόοπ", "newcity": "νέαπόλη", "citiesskylines": "πολιτείεςοραμάτων", "defconheavy": "defconheavy", "kenopsia": "κενοψία", "virtualkenopsia": "εικονικήκενοψία", "snowrunner": "χιονοδρομιστής", "libraryofruina": "βιβλιοθήκητουθανάτου", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "ωμεγαστράικερς", "wayfinder": "καθοδηγητής", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "ήρεμηπλαστικήπάπια", "battlebit": "battlebit", "ultimatechickenhorse": "απόλυτοκ<PERSON>τόπουλοάλογο", "dialtown": "χοροταξίδι", "smileforme": "χαμογέλασεμου", "catnight": "νύχταγατών", "supermeatboy": "σούπερκρέαςμπόι", "tinnybunny": "μικρός_λαγούλης", "cozygrove": "ζεστανούβουνου", "doom": "καταδίκη", "callofduty": "καλέστετονπόλεμο", "callofdutyww2": "callofdutyww2", "rainbow6": "ουράνιοτόξο6", "apexlegends": "apexlegends", "cod": "κωδ", "borderlands": "ακραὶσυνοριακ<PERSON>ς", "pubg": "παμπγ", "callofdutyzombies": "καλέσματωννεκρώνζωντανών", "apex": "απαίνω", "r6siege": "r6siege", "megamanx": "μεγάμανχ", "touhou": "τουχώ", "farcry": "μακριάαπόκραυγες", "farcrygames": "farcrygames", "paladins": "παλάδινοι", "earthdefenseforce": "στρατόςάμυναςγιατηγη", "huntshowdown": "κυνήγιμαχης", "ghostrecon": "φαντοταξη", "grandtheftauto5": "γκραντθεφταυτο5", "warz": "πόλεμος", "sierra117": "σιέρα117", "dayzstandalone": "dayzstandalone", "ultrakill": "υπερσκότωμα", "joinsquad": "έρχεσαιστημπάντα", "echovr": "εχόβρ", "discoelysium": "δισκοελιζιουμ", "insurgencysandstorm": "επανάστα<PERSON>ηκαταιγίδα", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "μαξπαίν", "hitman3": "χτυπητής3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codzanzo", "callofdutywarzone": "κωδικ<PERSON>ςπολέμουζώνης", "codzombies": "κωδζόμπις", "mirrorsedge": "κατοπτή<PERSON><PERSON>κ<PERSON><PERSON>ς", "divisions2": "διαιρέσεις2", "killzone": "ζώνηθανάτου", "helghan": "χελγκαν", "coldwarzombies": "κρυογέφυραζόμπι", "metro2033": "μετρό2033", "metalgear": "μεταλγ<PERSON>ιρ", "acecombat": "αστεράτημα", "crosscode": "σταυροσύνδεση", "goldeneye007": "χρυσόμάτι007", "blackops2": "μαύρεςεπιχειρήσεις2", "sniperelite": "ελίτ_σνάιπερ", "modernwarfare": "σύγχρονος<PERSON>όλεμος", "neonabyss": "νέον_άβυσσος", "planetside2": "planetside2", "mechwarrior": "μαχητήςτουμηχανισμού", "boarderlands": "όρια", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "φύγεαπότουτάρκοφ", "metalslug": "μεταλλοδρομός", "primalcarnage": "πρωτόγονοςφ<PERSON><PERSON>ος", "worldofwarships": "κόσμοςτουπολέμουкораблі", "back4blood": "πισω4αίμα", "warframe": "πολεμικόπλαίσιο", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "εκτελεστής", "masseffect": "μασσεφέκτ", "systemshock": "συστημικόχτύπημα", "valkyriachronicles": "βαλκυρίαχρονικά", "specopstheline": "ειδικέςεπιχειρήσειςτηγραμμή", "killingfloor2": "killingfloor2", "cavestory": "ιστορίαγιγαντιστάθμου", "doometernal": "καταδίκηαιώνια", "centuryageofashes": "αιώναςτηςστάχτης", "farcry4": "farcry4", "gearsofwar": "γρανάζιατουπολέμου", "mwo": "μωω", "division2": "διαίρεση2", "tythetasmaniantiger": "τυθέταςμαντιανόςτίγρης", "generationzero": "γενιάμηδέν", "enterthegungeon": "μπεςστηνοπήγαινα", "jakanddaxter": "τζακκ<PERSON><PERSON>ντ<PERSON>ξτερ", "modernwarfare2": "μοντέρνοςπόλεμος2", "blackops1": "μαύρεςεπιχειρήσεις1", "sausageman": "λουκανικοανθρωπος", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "τοφανατοπόνου", "warface": "πολεμικήμάσκα", "crossfire": "σταυρός_άνεσης", "atomicheart": "ατομικήκαρδιά", "blackops3": "μαύρεςεπιχειρήσεις3", "vampiresurvivors": "βαμπιροεπιβιωτές", "callofdutybatleroyale": "callofdutyμπατλρόγιαλ", "moorhuhn": "μο0ρυχούν", "freedoom": "ελευθερία", "battlegrounds": "μάχες", "frag": "frag", "tinytina": "μικρήτίνα", "gamepubg": "παίξεpubg", "necromunda": "νεκρομούντα", "metalgearsonsoflibert": "μετάλγκ<PERSON>ρσονςοφλιμπερτ", "juegosfps": "παιχνίδιαfps", "convertstrike": "μετατροπήχτυπήματος", "warzone2": "πόλεμος2", "shatterline": "σπάσετηγραμμή", "blackopszombies": "μαύρεςεπιχειρήσεις<PERSON><PERSON><PERSON><PERSON><PERSON>νώννεκρών", "bloodymess": "αιματοχυσία", "republiccommando": "πολιτοφύλακας", "elitedangerous": "ελίτεπικίνδυνος", "soldat": "στρατιώτης", "groundbranch": "έδα<PERSON>ος<PERSON><PERSON><PERSON><PERSON>ος", "squad": "παρέα", "destiny1": "μοίρα1", "gamingfps": "gamingfps", "redfall": "κόκκινηπτώσει", "pubggirl": "παίκτριαpubg", "worldoftanksblitz": "κόσμοςτων<PERSON>ρμάτωνμπλίτζ", "callofdutyblackops": "callofdutyblackops", "enlisted": "στρατολογημένοι", "farlight": "φάροφως", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "φωτεινός84", "splatoon3": "σπλατούνα3", "armoredcore": "θωρακισμένοςπυρήνας", "pavlovvr": "παυλόβρ", "xdefiant": "xdefiant", "tinytinaswonderlands": "μικρέςχώρατουτίνα", "halo2": "χάλο2", "payday2": "πληρωμή2", "cs16": "cs16", "pubgindonesia": "pubgινδονησία", "pubgukraine": "pubgουκρανία", "pubgeu": "μπουμγεu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgρομανία", "empyrion": "εμπύριον", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "τιτανόπτωσεις2", "soapcod": "σαπούνικωδικός", "ghostcod": "φαντ<PERSON>κώδικας", "csplay": "csplay", "unrealtournament": "αλιευτικ<PERSON>ςδιαγωνισμός", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "αντεπίθεση", "cs2": "cs2", "pistolwhip": "πιστολιέρα", "callofdutymw2": "callofdutymw2", "quakechampions": "σεισμικοίήρωες", "halo3": "χάλο3", "halo": "χάλο", "killingfloor": "σκοτώνοντάςτοπάτωμα", "destiny2": "destiny2", "exoprimal": "εξωπραγματικ<PERSON>ς", "splintercell": "σπλιτέρκελ", "neonwhite": "νέονλευκό", "remnant": "υπόλοιπο", "azurelane": "αζούρλέιν", "worldofwar": "κόσμοςτουπολέμου", "gunvolt": "γιουνβολτ", "returnal": "επιστροφή", "halo4": "χάλο4", "haloreach": "χαλορίχτης", "shadowman": "σκιάρης", "quake2": "σεισμός2", "microvolts": "μικροβολτς", "reddead": "reddead", "standoff2": "standoff2", "harekat": "χαρακ<PERSON>τες", "battlefield3": "μαχητικά3", "lostark": "χαμένος<PERSON><PERSON><PERSON>", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "ελσόρντ", "seaofthieves": "θάλα<PERSON><PERSON><PERSON><PERSON>ω<PERSON><PERSON><PERSON>εφτών", "rust": "σκουριά", "conqueronline": "κατακτάμεδιαδικτυακά", "dauntless": "ατρόμητοι", "warships": "πολεμικάπλοία", "dayofdragons": "μέρατωνδράκων", "warthunder": "πολεμικήκαταιγίδα", "flightrising": "έκρηξηπτήσης", "recroom": "recroom", "legendsofruneterra": "θρύλοιτουruneterra", "pso2": "ψο2", "myster": "μυστήριo", "phantasystaronline2": "φάντα<PERSON>ιςτάρονλάιν2", "maidenless": "χωρίςκορίτσι", "ninokuni": "νινοκούνι", "worldoftanks": "κόσμοςτουντουφεκιού", "crossout": "διαγράφω", "agario": "αγάριο", "secondlife": "δεύτερηζωή", "aion": "αίων", "toweroffantasy": "πύργος<PERSON>αντα<PERSON>ίας", "netplay": "δικτυακόπαιχνίδι", "everquest": "πορείασεων", "metin2": "μετιν2", "gtaonline": "gtaonline", "ninokunicrossworld": "νινοκουνικροσγουorld", "reddeadonline": "κόκκινο_νεκρό_διαδικτυακό", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ραγναρόκονλαΐν", "knightonline": "ιππότηςonline", "gw2": "gw2", "tboi": "τμποϊ", "thebindingofisaac": "τοδέσιμοτο<PERSON><PERSON><PERSON><PERSON><PERSON>κ", "dragonageinquisition": "dragonageinquisition", "codevein": "κωδικοφλέβες", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "κλαμππιγκουίνους", "lotro": "λότρο", "wakfu": "γουακφου", "scum": "σκουπίδι", "newworld": "νέοςκόσμος", "blackdesertonline": "μαύρηέρημοςδιαδικτυακά", "multiplayer": "πολυσυμμετοχικό", "pirate101": "πειρατής101", "honorofkings": "τιμήτωνβασιλέων", "fivem": "φάιβεν", "starwarsbattlefront": "πολέμοςτων<PERSON>στρωνμάχημπροστά", "karmaland": "καρμαλάντ", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "φιγκρος", "mmo": "mmo", "pokemmo": "ποκεμμo", "ponytown": "πονητόουν", "3dchat": "3dσυνομιλία", "nostale": "νοσταλγία", "tauriwow": "ταυρήwow", "wowclassic": "wowκλασικά", "worldofwarcraft": "κόσμοςτουπολέμου", "warcraft": "πόλεμοςτε<PERSON>νών", "wotlk": "wotlk", "runescape": "runescape", "neopets": "νέοπες", "moba": "μόμπα", "habbo": "χαμπο", "archeage": "αρχαίγεια", "toramonline": "τοραμονλαιν", "mabinogi": "μαμπινόγκι", "ashesofcreation": "στάχτεςδημιουργίας", "riotmmo": "ριάλιτυ<PERSON>o", "silkroad": "μεταξωτόςδρόμος", "spiralknights": "σπειροίιππότες", "mulegend": "μυθικόςσυνδυασμός", "startrekonline": "starfleetonline", "vindictus": "βιντίκτους", "albiononline": "αλμπιον온라인", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "δράκονταςπροφήτης", "grymmo": "γκριμμο", "warmane": "ζεστόαέρα", "multijugador": "πολυπαίκτης", "angelsonline": "αγγέλ<PERSON><PERSON><PERSON><PERSON>", "lunia": "λουνία", "luniaz": "λουν<PERSON><PERSON><PERSON>", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "πόλεμοιτων<PERSON>στωνπαλιά", "grandfantasia": "μεγάλεςφαντασίες", "blueprotocol": "μπλέπρωτοκόλλο", "perfectworld": "τέλειος<PERSON><PERSON><PERSON>μος", "riseonline": "ανεβαίνουμεonline", "corepunk": "κορπ<PERSON>ύνκ", "adventurequestworlds": "περιπέτειεςστοκοσμο", "flyforfun": "πετάωγιαδιασκέδαση", "animaljam": "ζωαγαμώσταγμα", "kingdomofloathing": "βασίλειοτουμαρτυρίου", "cityofheroes": "πολιτείατωνηρώων", "mortalkombat": "μορταλκομπατ", "streetfighter": "στριτφάιτερ", "hollowknight": "κενόςιππότης", "metalgearsolid": "metalgearsolid", "forhonor": "γιατιμητή", "tekken": "τέκεν", "guiltygear": "γκ<PERSON>λ<PERSON>ιγ<PERSON><PERSON>ρ", "xenoverse2": "ξενάσφαιρα2", "fgc": "fgc", "streetfighter6": "στριτφάιτερ6", "multiversus": "πολυσυνάντησες", "smashbrosultimate": "σμάςμπροουζούλτιμετ", "soulcalibur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "μπραουλχάλα", "virtuafighter": "virtuafighter", "streetsofrage": "δρόμοιτουράγκε", "mkdeadlyalliance": "mkνεκρωτικησυμμαχια", "nomoreheroes": "κανένας<PERSON><PERSON><PERSON><PERSON>ςπαραπάνω", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "οβασιλιάςτωνμαχών", "likeadragon": "σανδρ<PERSON><PERSON>ος", "retrofightinggames": "ρετρόμαχεςπαιχνίδια", "blasphemous": "βλάσφημος", "rivalsofaether": "αντίπαλοιτουαιθέρα", "persona4arena": "persona4arena", "marvelvscapcom": "μαρβελvsκαπκομ", "supersmash": "σούπερσμάς", "mugen": "μυγένη", "warofthemonsters": "πόλεμοςτωντεράτων", "jogosdeluta": "παιχνίδιαμάχης", "cyberbots": "κυβερνορομπότ", "armoredwarriors": "θωρακισμένοιπολεμιστές", "finalfight": "τελικήμάχη", "poweredgear": "δύναμηάτακες", "beatemup": "χτυπάτετους", "blazblue": "μπλέιζμπλου", "mortalkombat9": "μortalkombat9", "fightgames": "παίξτομαζί", "killerinstinct": "δολοφονικ<PERSON>ένστικτο", "kingoffigthers": "βασιλιάςτουςμάχης", "ghostrunner": "φαντοδρομέας", "chivalry2": "ιπποτισμός2", "demonssouls": "δαίμονεςψυχές", "blazbluecrosstag": "μπλέιζμπλούκροςτάγκ", "blazbluextagbattle": "μπλέιζμπλουέξταγκμπάτλ", "blazbluextag": "μπλεζμπλουεξταγκ", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "συνέχειατουhollowknight", "hollowknightsilksong": "κενός<PERSON><PERSON><PERSON><PERSON>τηςσιλκσονγκ", "silksonghornet": "σιλκσόνγκχόρνετ", "silksonggame": "παίχνιδισιλκόνγκ", "silksongnews": "νέαsilksong", "silksong": "μεταξοπαιχνίδι", "undernight": "υποβραδυά", "typelumina": "τυπολουμίνα", "evolutiontournament": "τουρνουάεξέλιξης", "evomoment": "ευχαριστηστιγμή", "lollipopchainsaw": "καραμελομαχαίρι", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "ιστορίεςτο<PERSON><PERSON>eria", "bloodborne": "αίμαδενδενέχει", "horizon": "ορίζοντας", "pathofexile": "μονοπάτιτουεξορισμού", "slimerancher": "slimerancher", "crashbandicoot": "κράσμπαντικουτ", "bloodbourne": "bloodbourne", "uncharted": "ανεξερεύνητος", "horizonzerodawn": "ορίζονταςμηδένφως", "ps4": "ps4", "ps5": "ps5", "spyro": "σπήρο", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "διαβόητο", "playstationbuddies": "playstationφιλαράκια", "ps1": "ps1", "oddworld": "παράξ<PERSON>νοςκόσμος", "playstation5": "playstation5", "slycooper": "σλάικουπερ", "psp": "ψιψιψι", "rabbids": "ρακούνες", "splitgate": "splitgate", "persona4": "προσωπικότητα4", "hellletloose": "αγγέλοιαναρχίας", "gta4": "gta4", "gta": "γκτα", "roguecompany": "αντάρτικηεταιρεία", "aisomniumfiles": "aisomniumfiles", "gta5": "γκτα5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "θεόςτουπολέμου", "gris": "γκρίζο", "trove": "θησαυρ<PERSON>ς", "detroitbecomehuman": "detroitγίνεανθρώπων", "beatsaber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rimworld": "κόσμος_του_rim", "stellaris": "στελλαρεισ", "ps3": "ps3", "untildawn": "μέχριτοξημέρωμα", "touristtrophy": "τουριστικόβραβείο", "lspdfr": "λσπνφρ", "shadowofthecolossus": "σκιάτουκολοσσού", "crashteamracing": "crashteamracing", "fivepd": "πέντεpd", "tekken7": "τεκκεν7", "devilmaycry": "διάβολοςμπορείνακλάψει", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "παιχνιδοσυστημα", "samuraiwarriors": "σαμουράιπολεμιστές", "psvr2": "psvr2", "thelastguardian": "οτελευτ<PERSON><PERSON><PERSON><PERSON><PERSON>λακας", "soulblade": "ψυχοξιφίας", "gta5rp": "gta5rp", "gtav": "γκταv", "playstation3": "playstation3", "manhunt": "κυνήγιάντρα", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2συμμαχία", "pcsx2": "pcsx2", "lastguardian": "τελευτα<PERSON><PERSON><PERSON><PERSON>λακας", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "αρμέλο", "partyanimal": "ζώοντουπάρτι", "warharmmer40k": "πολέμοςχάμερ40κ", "fightnightchampion": "νύχταμαχώνchampion", "psychonauts": "ψυχαναύτες", "mhw": "mhw", "princeofpersia": "πρίγκιπαςτηςπερσίας", "theelderscrollsskyrim": "τοelderscrollsskyrim", "pantarhei": "πάνταρεί", "theelderscrolls": "ταηλικιωμέναπεριοδικά", "gxbox": "gxbox", "battlefront": "μάχη_front", "dontstarvetogether": "μηνπεινάτεμαζί", "ori": "όρι", "spelunky": "σπελουνκί", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "αστέρισενούμενοι", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "σκέιτ3", "houseflipper": "ανακα<PERSON>νισησπιτιού", "americanmcgeesalice": "αμερικανικήαλισόντουμπου", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "λήγκατουβασιλείων", "fable2": "παραμύθι2", "xboxgamepass": "xboxgamepass", "undertale": "υπογειοι", "trashtv": "σκουπίδιατηλεόραση", "skycotl": "skycotl", "erica": "έρικα", "ancestory": "προγονικήκληρονομιά", "cuphead": "cuphead", "littlemisfortune": "μικρέςκακοτυχίες", "sallyface": "sallyface", "franbow": "φρανμποου", "monsterprom": "τερατοπροαγωγή", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "μοτοσικλέτες", "outerwilds": "εξωτερικ<PERSON>ίκόσμοι", "pbbg": "pbbg", "anshi": "άνσι", "cultofthelamb": "δογματουαρνιού", "duckgame": "πάπιαπα<PERSON>χνιδο", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "απόκρυφος", "longdrive": "μακρινήοδήγηση", "satisfactory": "ικανοποιητικό", "pluviophile": "βροχόφιλος", "underearth": "υποχθόνια", "assettocorsa": "assettocorsa", "geometrydash": "γεωμετρ<PERSON>α<PERSON><PERSON><PERSON>ς", "kerbal": "κέρμπαλ", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "κένσι", "spiritfarer": "υποστηρικτής", "darkdome": "σκοτειν<PERSON>ςθόλος", "pizzatower": "πίτσαμπαθ<PERSON>ρ", "indiegame": "ανεξάρτητοπαιχνίδι", "itchio": "itchio", "golfit": "γκόλφιτ", "truthordare": "αλήθειαήτολμήσεις", "game": "παιχνίδι", "rockpaperscissors": "ροκψαππ<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>ρας", "trampoline": "τραμπολίνο", "hulahoop": "χορευτικόσωλήνα", "dare": "τόλμησέτο", "scavengerhunt": "κυνήγιθησαυρού", "yardgames": "παιχνίδιαστηναυλή", "pickanumber": "διάλεξεέναναριθμό", "trueorfalse": "αληθέςήψευδές", "beerpong": "μπυρομπάλα", "dicegoblin": "ζαριγομπλίν", "cosygames": "άνεταπαιχνίδια", "datinggames": "παιχνίδιαραντεβού", "freegame": "δωρεάνπαιχνίδι", "drinkinggames": "παιχνίδιααλκοόλ", "sodoku": "σοδοκού", "juegos": "παιχνίδια", "mahjong": "μαχόνγκ", "jeux": "παιχνίδια", "simulationgames": "παιχνίδιαπροσομοίωσης", "wordgames": "παιχνίδιαλέξεων", "jeuxdemots": "παιχνίδιαλέξεων", "juegosdepalabras": "παιχνίδιαλέξεων", "letsplayagame": "παίζουμεέναπαιχνίδι", "boredgames": "παιχνίδιαβαρεμάρας", "oyun": "ογιουν", "interactivegames": "διαδραστικάπαιχνίδια", "amtgard": "αμτ<PERSON><PERSON>αρντ", "staringcontests": "διαγωνισμοίκοιτάγματος", "spiele": "παίζουμε", "giochi": "παιχνίδια", "geoguessr": "γεωγνώστης", "iphonegames": "παιχνίδιαiphone", "boogames": "μπουγκέιμς", "cranegame": "παιχνίδιμεγερανούς", "hideandseek": "κρυφτό", "hopscotch": "κουτσό", "arcadegames": "παιχνίδιααρκαδικά", "yakuzagames": "γιακο<PERSON>ζαγκειμενα", "classicgame": "κλασσικόπαιχνίδι", "mindgames": "παιχνίδιανου", "guessthelyric": "μαντεύωτουςστίχους", "galagames": "γαλαγκέιμς", "romancegame": "ρομαντικόπαιχνίδι", "yanderegames": "γιαντερέγκιμες", "tonguetwisters": "γλωσσοδέτες", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "παιχνίδιααίθουσας", "tabletopgames": "επιτραπέζιαπαιχνίδια", "metroidvania": "μετροΐνβανια", "games90": "παιχνίδια90", "idareyou": "τολμάωσένα", "mozaa": "μοζαα", "fumitouedagames": "φουμετα<PERSON><PERSON>αγώνες", "racinggames": "αγωνιστικάπαιχνίδια", "ets2": "ets2", "realvsfake": "αληθινόvsψεύτικο", "playgames": "παίζουμεπαιχνίδια", "gameonline": "παίχν<PERSON><PERSON><PERSON><PERSON>", "onlinegames": "διαδικτυακάπαιχνίδια", "jogosonline": "παίχνιδιαonline", "writtenroleplay": "γραπτόπαιχνίδι", "playaballgame": "παίζωμπάλα", "pictionary": "πινακοθήκη", "coopgames": "παιχνίδιασυνεργασίας", "jenga": "τζένγκα", "wiigames": "wiigames", "highscore": "υψηλότεροσκ<PERSON>ρ", "jeuxderôles": "παιχνίδιαρόλων", "burgergames": "μπεργκεργκέιμς", "kidsgames": "παιδικάπαιχνίδια", "skeeball": "σκιμπωλ", "nfsmwblackedition": "nfsmwμαύρηέκδοση", "jeuconcour": "παίξεδιαγωνισμό", "tcgplayer": "tcgplayer", "juegodepreguntas": "παιχνίδιερωτήσεων", "gioco": "παιχνίδι", "managementgame": "παιχνίδιδιαχείρισης", "hiddenobjectgame": "κρυφάαντικείμενα", "roolipelit": "ρουλιπ<PERSON>λ<PERSON>τ", "formula1game": "παιχνίδιformula1", "citybuilder": "πολεοδομικ<PERSON>ς", "drdriving": "drdriving", "juegosarcade": "παιχνίδιαarcade", "memorygames": "παιχνίδιαμνήμης", "vulkan": "βούρκαν", "actiongames": "παιχνίδιαδράσης", "blowgames": "blowgames", "pinballmachines": "μηχανέςπιμπάμ", "oldgames": "παλιάπαιχνίδια", "couchcoop": "κανα<PERSON>εδο<PERSON>υνεργασία", "perguntados": "περτάντα<PERSON>ος", "gameo": "γκαίμο", "lasergame": "λαζ<PERSON><PERSON><PERSON><PERSON><PERSON>ιμ", "imessagegames": "παιχνίδιαimessage", "idlegames": "παίχνιδιααδράνειας", "fillintheblank": "συμπλήρωσε_το_κενό", "jeuxpc": "παίξεpc", "rétrogaming": "παλιέςαναμνήσεις", "logicgames": "λογοπαίγνια", "japangame": "ιαπωνικόπαιχνίδι", "rizzupgame": "rizzupgame", "subwaysurf": "υπόγειος<PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "jeuxdecelebrite": "παιχνίδιαδιάσημων", "exitgames": "έξοδοςπαιχνίδια", "5vs5": "5vs5", "rolgame": "ρολ<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "παίξεκ<PERSON><PERSON><PERSON>κότωσέτους", "traditionalgames": "παραδο<PERSON><PERSON><PERSON>κάπαιχνίδια", "kniffel": "ντάμα", "gamefps": "παιχνίδιfps", "textbasedgames": "παιχνίδιαμεκείμενο", "gryparagrafowe": "γραπτάγκριση", "fantacalcio": "φανταστικ<PERSON>ποδόσφαιρο", "retrospel": "ρετρο<PERSON><PERSON><PERSON>χνιδο", "thiefgame": "κλεφτοπαιχνίδι", "lawngames": "παιχνίδιαστηνανάσσα", "fliperama": "φλιπεράμα", "heroclix": "ήρω<PERSON><PERSON><PERSON><PERSON>", "tablesoccer": "πιγκπογκ", "tischfußball": "πιγκάλες", "spieleabende": "βραδιέςπαιχνιδιών", "jeuxforum": "jeuxforum", "casualgames": "χαλαράπαιχνίδια", "fléchettes": "βελάκια", "escapegames": "παιχνίδιαδιαφυγής", "thiefgameseries": "κλέφτηπαίγνιση", "cranegames": "παιχνίδιακράνου", "játék": "παίξε", "bordfodbold": "μπόρντφουντμπόλ", "jogosorte": "παιχνιδιαστηντύχη", "mage": "μάγισσα", "cargames": "παιχνίδια<PERSON>υτοκινήτων", "onlineplay": "διαδικτυακόπαιχνίδι", "mölkky": "μόλκυ", "gamenights": "βραδιέςπαίχνιδιών", "pursebingos": "τσάντεςμπίνγκος", "randomizer": "τυχαίατισίτα", "msx": "msx", "anagrammi": "αγαν<PERSON><PERSON><PERSON><PERSON>", "gamespc": "παιχνίδιαpc", "socialdeductiongames": "κοινωνικάπαιχνίδιαμαθήσεως", "dominos": "ντόμινός", "domino": "ντόμινο", "isometricgames": "ισομετρικάπαιχνίδια", "goodoldgames": "καλοπαλιάτικαπαιχνιδια", "truthanddare": "αλήθειακ<PERSON>ιτολμάς", "mahjongriichi": "μαχόνγκριχί", "scavengerhunts": "σαφάριθησαυρού", "jeuxvirtuel": "εικονικάπαιχνίδια", "romhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f2pgamer": "f2pgamer", "free2play": "ελεύθεροναπαίζεις", "fantasygame": "φανταστικοπαιχνιδι", "gryonline": "gryonline", "driftgame": "παιχνίδικαταδύσεων", "gamesotomes": "παίξετομουσική", "halotvseriesandgames": "χαλάκιασειρέςκαιπαιχνίδια", "mushroomoasis": "μανιταροπαράδεισος", "anythingwithanengine": "όλαμεκινητήρα", "everywheregame": "παντούπα<PERSON>χνιδο", "swordandsorcery": "σπάθεςκαιμαγείες", "goodgamegiving": "καλόπαιχνίδιδώρο", "jugamos": "παίζουμε", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "γκρικομπιούτερ", "virgogami": "παρθένομάνα", "gogame": "παίξετοπαιχνίδι", "jeuxderythmes": "παιχνίδιαρυθμού", "minaturegames": "μινιατούρες", "ridgeracertype4": "τυπος4του<PERSON>racer", "selflovegaming": "αυτοαγ<PERSON><PERSON>ηκαιπαιχνίδια", "gamemodding": "παιχνιδοτροποποίηση", "crimegames": "παιχνίδιαεγκλήματος", "dobbelspellen": "διπλάπαιχνίδια", "spelletjes": "παιχνίδια", "spacenerf": "διαστημικήχτύπημα", "charades": "χαράδες", "singleplayer": "μονάχ<PERSON><PERSON><PERSON><PERSON><PERSON>της", "coopgame": "παίξεμαζί", "gamed": "παίζουμε", "forzahorizon": "forzahorizon", "nexus": "νέξους", "geforcenow": "γεφόρτσενοου", "maingame": "κύριονέο", "kingdiscord": "βασιλιάςdiscord", "scrabble": "σκραμπλ", "schach": "σκάκι", "shogi": "σόγκι", "dandd": "δανδ", "catan": "κατάν", "ludo": "λούδο", "backgammon": "πιγγάλινγκ", "onitama": "ονιτάμα", "pandemiclegacy": "κληρονομιάπανδημίας", "camelup": "καμήλαπάνω", "monopolygame": "μονόπολη", "brettspiele": "επιτραπέζια", "bordspellen": "επιτραπέζιαπαιχνιδάκια", "boardgame": "επιτραπ<PERSON>ζιο", "sällskapspel": "επιτραπέζιαπαιχνίδια", "planszowe": "παιχνίδιατραπέζιου", "risiko": "ρίσ<PERSON><PERSON>", "permainanpapan": "παιχνίδιαμαζί", "zombicide": "ζομποκτονία", "tabletop": "τσακωτό", "baduk": "μπ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodbowl": "αίμακαιμπάλα", "cluedo": "κλούδο", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "σενετ", "goboardgame": "γκομπoardgame", "connectfour": "συνδέσετέσσερα", "heroquest": "ήρωαςκαταδίωξη", "giochidatavolo": "παιχνίδιαστοτραπέζι", "farkle": "φαρκολο", "carrom": "καρόμ", "tablegames": "επιτραπέζια", "dicegames": "παιχνίδιαζ<PERSON><PERSON>ιών", "yatzy": "γιάτζι", "parchis": "παρτίδα", "jogodetabuleiro": "τζόγος<PERSON>τοταβλίο", "jocuridesocietate": "παιχνίδιαμετηνκοινωνία", "deskgames": "παιχνίδιαγραφείου", "alpharius": "αλφαριος", "masaoyunları": "μασαογιουνάρια", "marvelcrisisprotocol": "μαρβελκρίσιςπροτόκολο", "cosmicencounter": "κοσμικήσυνάντηση", "creationludique": "δημιουργικήδιασκέδαση", "tabletoproleplay": "παιχνίδια_ρόλων_σε_τραπέζι", "cardboardgames": "χαρτονένικαπαιχνίδια", "eldritchhorror": "παράξενοςφόβος", "switchboardgames": "διαγωνιστικάπαιχνίδια", "infinitythegame": "απειροτοπαιχνίδι", "kingdomdeath": "βασίλειοτουθανάτου", "yahtzee": "γιάτζι", "chutesandladders": "σκαλοπάτια<PERSON>αιτσουλήθρες", "társas": "boo", "juegodemesa": "παιχνίδιτουςτραπέζιου", "planszówki": "παιχνίδιαζ<PERSON>ρ<PERSON>ς", "rednecklife": "κόβω_μπυραρίες", "boardom": "βαρεμάρα", "applestoapples": "μήλασεμήλα", "jeudesociété": "παιχνίδια_τραπεζιού", "gameboard": "παιχνιδόφυλλο", "dominó": "ντόμινο", "kalah": "καλά", "crokinole": "κρο<PERSON>ιν<PERSON>λ", "jeuxdesociétés": "παιχνίδιατραπεζιού", "twilightimperium": "λυκόφωςαυτοκρατορία", "horseopoly": "ιππιάδα", "deckbuilding": "κατασκ<PERSON><PERSON><PERSON>καταστρωμάτων", "mansionsofmadness": "μανιτέρτωνπαράνοιας", "gomoku": "γκομόκου", "giochidatavola": "παιχνίδιαστοτραπέζι", "shadowsofbrimstone": "σκιάτουβρύου", "kingoftokyo": "βασιλιάςτουτόκιο", "warcaby": "πολεμοι", "táblajátékok": "παιχνίδιατραπέζιου", "battleship": "μάχηπλοίο", "tickettoride": "εισιτήριογιαβόλτα", "deskovehry": "γραφε<PERSON>αγ<PERSON>άνγκστερ", "catán": "κατάν", "subbuteo": "σαμπουτεό", "jeuxdeplateau": "επιτραπέζιαπαιχνίδια", "stolníhry": "επιτραπέζιαπαιχνίδια", "xiángqi": "σιάνγκτσι", "jeuxsociete": "παιχνίδιαμαζί", "gesellschaftsspiele": "κοινωνικάπαιχνίδια", "starwarslegion": "σταργουορσλεγιόν", "gochess": "παίξεσκάκι", "weiqi": "γουίτσι", "jeuxdesocietes": "παιχνίδιατηςπαρέας", "terraria": "τεράρια", "dsmp": "dsmp", "warzone": "πεδίομάχης", "arksurvivalevolved": "arksurvivalevolved", "dayz": "μέρες", "identityv": "ταυτότηταv", "theisle": "τονη<PERSON><PERSON>", "thelastofus": "τελευτα<PERSON>οςμας", "nomanssky": "κανέν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν<PERSON>ς", "subnautica": "υποβρύχια", "tombraider": "ταφοφύλακας", "callofcthulhu": "κλήσητου_cthulhu", "bendyandtheinkmachine": "bendyκαιημπινιδα", "conanexiles": "conanexiles", "eft": "eft", "amongus": "μεταξύμας", "eco": "οικολογικό", "monkeyisland": "νησίτωνπιθήκων", "valheim": "βάλχαϊμ", "planetcrafter": "πλανητοδημιουργ<PERSON>ς", "daysgone": "μέρεςπουπέρασαν", "fobia": "φοβία", "witchit": "μαγεψτο", "pathologic": "παθολογικό", "zomboid": "ζομποειδές", "northgard": "βόρειαφύλαξη", "7dtd": "7dtd", "thelongdark": "τομακρύσκοτάδι", "ark": "αρκ", "grounded": "άβαρφος", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "τρελήμαμά", "dontstarve": "μηνπεινάς", "eternalreturn": "αιώνιαεπιστροφή", "pathoftitans": "μονοπάτιτουτίτανες", "frictionalgames": "frictionalgames", "hexen": "μάγισσες", "theevilwithin": "τοκακόμέσαμας", "realrac": "αληθινόςχ<PERSON>ρ<PERSON><PERSON>ας", "thebackrooms": "ταπίσωδωμάτια", "backrooms": "πίσωδωμάτια", "empiressmp": "ενεσθεντζουμπούρλεσ", "blockstory": "μπλοκιστορία", "thequarry": "τολατομίο", "tlou": "τλού", "dyinglight": "σβήνειτοφως", "thewalkingdeadgame": "τοπαιχν<PERSON>διτουνεκρ<PERSON><PERSON><PERSON>ντανού", "wehappyfew": "εμείςοιχαρούμενοιfew", "riseofempires": "άνοδοςτων<PERSON>υτοκρατοριών", "stateofsurvivalgame": "κατάστασηεπιβίωσηςgame", "vintagestory": "παλιέςιστορίες", "arksurvival": "arkεπιβίωση", "barotrauma": "βαροτραύμα", "breathedge": "breathedge", "alisa": "αλίσα", "westlendsurvival": "δυτικήεπιβίωση", "beastsofbermuda": "θηρίατουμπερμούδα", "frostpunk": "παγετώνας", "darkwood": "σκοτεινόδρυς", "survivalhorror": "επιβίωσηκαιτρομαγηση", "residentevil": "κατοικιδαιμόνια", "residentevil2": "άρρωστοςκακός2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "κενότραίνο", "lifeaftergame": "ζωήμετάτοπαιχνίδι", "survivalgames": "παιχνίδιαεπιβίωσης", "sillenthill": "σιωπηλήκοιλάδα", "thiswarofmine": "αυτοςο<PERSON><PERSON><PERSON>εμοςμου", "scpfoundation": "scpfoundation", "greenproject": "πράσινοέργο", "kuon": "κουόν", "cryoffear": "κλάψεαπόφόβο", "raft": "βαρκάδα", "rdo": "rdo", "greenhell": "πράσινηκολάση", "residentevil5": "residentevil5", "deadpoly": "νεκρόπολυ", "residentevil8": "κατοικ<PERSON>ατουκακού8", "onironauta": "ονειροναύτης", "granny": "γιαγιά", "littlenightmares2": "μικρέςεφιάλτες2", "signalis": "signalis", "amandatheadventurer": "αμάνταηπεριπετειώδης", "sonsoftheforest": "γιοιτουδάσους", "rustvideogame": "rustβιντεοπαιχνίδι", "outlasttrials": "υπερβα<PERSON>νωτιςδοκιμασίες", "alienisolation": "απομόνωσηεξωγήινων", "undawn": "αθέατος", "7day2die": "7μέρεςγ<PERSON>αν<PERSON>πεθάνεις", "sunlesssea": "άνεμοςτηςθάλασσας", "sopravvivenza": "επιβίωση", "propnight": "προπόνηνηυατική", "deadisland2": "νεκρόνησος2", "ikemensengoku": "ικανοίπολεμιστές", "ikemenvampire": "ιγκεμένβαμπιρ", "deathverse": "θάνατοςverse", "cataclysmdarkdays": "κατακλυσμιαίεςσκοτεινέςμέρες", "soma": "σώμα", "fearandhunger": "φόβοςκαιπείνα", "stalkercieńczarnobyla": "κρυφάμηνυμα", "lifeafter": "ζωήμετά", "ageofdarkness": "εποχήτουσκότους", "clocktower3": "ρολόιτουπύργου3", "aloneinthedark": "μόν<PERSON><PERSON><PERSON><PERSON><PERSON>κοτάδι", "medievaldynasty": "μεσαιωνικήδυναστεία", "projectnimbusgame": "projectnimbusgame", "eternights": "αιωνιές", "craftopia": "χάπτοπια", "theoutlasttrials": "theoutlasttrials", "bunker": "καταφύγιο", "worlddomination": "παγκόσμιακατάληψη", "rocketleague": "ροκέτλιγκ", "tft": "tft", "officioassassinorum": "δολοφόνοιγραφείου", "necron": "νέκρον", "wfrp": "wfrp", "dwarfslayer": "νανοσκοτώστρια", "warhammer40kcrush": "warhammer40kκαψούρα", "wh40": "wh40", "warhammer40klove": "πόλεμοςχάμερ40kαγάπη", "warhammer40klore": "warhammer40kιστορίες", "warhammer": "γου<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "warhammer30k": "πολεμιστές30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "τεμπλοκουλέξους", "vindicare": "εκδίκηση", "ilovesororitas": "αγαπάω<PERSON>ις<PERSON><PERSON>ρότες", "ilovevindicare": "αγαπώτηνvindicare", "iloveassasinorum": "αγαπώassasinorum", "templovenenum": "τεμπλοβενένου", "templocallidus": "τεμπελοκάλλιδος", "templomaerorus": "τεμπλόμαέρους", "templovanus": "τεμπλοβάνους", "oficioasesinorum": "δουλειέςδολοφόνων", "tarkov": "τάρκοβ", "40k": "40k", "tetris": "τετρίς", "lioden": "λιοντάρι", "ageofempires": "εποχήτων<PERSON>υτοκρατοριών", "aoe2": "αοe2", "hoi4": "hoi4", "warhammerageofsigmar": "πόλεμοςτουαιώνασίγμα", "civilizationv": "πολιτισμ<PERSON>ςv", "ittakestwo": "χρειά<PERSON><PERSON>νταιδύο", "wingspan": "άνοιγμαφ<PERSON><PERSON>ρών", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "ήρωεςτηςδύναμηςκαιμαγείας", "btd6": "btd6", "supremecommander": "φιρμάρχης", "ageofmythology": "εποχήτουμύθου", "args": "άργς", "rime": "ρίμα", "planetzoo": "πλανητόκηπος", "outpost2": "προχωρημένος2", "banished": "εξοστρακισμένος", "caesar3": "καίσαρ3", "redalert": "κόκκινοσυναγερμό", "civilization6": "πολιτισμός6", "warcraft2": "warcraft2", "commandandconquer": "διοίκησεκαικατακτησε", "warcraft3": "warcraft3", "eternalwar": "αιώνι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ος", "strategygames": "στρατηγικάπαιχνίδια", "anno2070": "anno2070", "civilizationgame": "πολιτισμόςπαίχνιδο", "civilization4": "πολιτισμός4", "factorio": "φακτοριο", "dungeondraft": "μπαζοκατασκευή", "spore": "σπόρος", "totalwar": "ολικόςπόλεμος", "travian": "τραβιαν", "forts": "φορτς", "goodcompany": "καλήπαρέα", "civ": "πολίτης", "homeworld": "τόποςμου", "heidentum": "εγώ_είμαι_υποστηρικτής", "aoe4": "aoe4", "hnefatafl": "χνεφατάφλ", "fasterthanlight": "ταχύτερααπότοφως", "forthekings": "γιατουςβασιλιάδες", "realtimestrategy": "πραγματικήστρατηγική", "starctaft": "starctaft", "sidmeierscivilization": "σιντμάιερςπολιτισμός", "kingdomtwocrowns": "βασίλειοδυοκορώνεις", "eu4": "ευ4", "vainglory": "ματαιοδοξία", "ww40k": "ww40k", "godhood": "θεϊκήτάξη", "anno": "άνοιγμα", "battletech": "μάχητεχνολογία", "malifaux": "μαλιφ<PERSON>ξ", "w40k": "w40k", "hattrick": "χαττρικ", "davesfunalgebraclass": "μαθηματης<PERSON><PERSON>α<PERSON>κεδαστικηςαλγεβραςτουδαβιδ", "plagueinc": "πληγ<PERSON><PERSON>νκ", "theorycraft": "θεωρητικήκατασκευή", "mesbg": "μερσμπγ", "civilization3": "πολιτισμός3", "4inarow": "4σειρές", "crusaderkings3": "σταυροφόροι3", "heroes3": "ήρωες3", "advancewars": "προχωρημένοιπόλεμοι", "ageofempires2": "εποχήτωναυτοκρατοριών2", "disciples2": "μαθητές2", "plantsvszombies": "φυτάεναντίονζόμπι", "giochidistrategia": "στρατηγικάπαιχνίδια", "stratejioyunları": "στρατηγικάπαιχνίδια", "europauniversalis4": "ευρωπαιοθνητια4", "warhammervermintide2": "πολεμιστέςβερμιντίδη2", "ageofwonders": "εποχήτωνθαυμάτων", "dinosaurking": "βασιλιάςτωνδεινοσαύρων", "worldconquest": "παγκόσμιακατάκτηση", "heartsofiron4": "καρδιές<PERSON>πόσίδερο4", "companyofheroes": "εταιρείαηρώων", "battleforwesnoth": "μάχη<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "σφυριξετωναυτοκρατοριων", "warhammerkillteam": "πολεμοι_χτμ", "goosegooseduck": "goosegooseduck", "phobies": "φοβίες", "phobiesgame": "φοβίεςγκέιμ", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "επάρκειαμηχανιστή", "outerplane": "εξωτερικόαεροπλάνο", "turnbased": "στροφογενές", "bomberman": "μπόμπερμαν", "ageofempires4": "ageofempires4", "civilization5": "πολιτισμός5", "victoria2": "βικτώρια2", "crusaderkings": "σταυροφόροιμπαμπάδες", "cultris2": "cultris2", "spellcraft": "ξόρκια", "starwarsempireatwar": "πόλεμοιάστρωνεmpireatwar", "pikmin4": "πικμιν4", "anno1800": "anno1800", "estratégia": "στρατηγική", "popfulmail": "ποπφουλμέιλ", "shiningforce": "λαμπερήδύναμη", "masterduel": "μαστερντουέλ", "dysonsphereprogram": "πρόγραμμαdysonsphere", "transporttycoon": "μεταφορέςτυχανίας", "unrailed": "χωρίςγραμμές", "magicarena": "μαγικοαρένα", "wolvesville": "βουβόκυκνοι", "ooblets": "ooblets", "planescapetorment": "σχέδιοδιαφυγήςαπόβασανιστήρια", "uplandkingdoms": "υψηλάβασίλεια", "galaxylife": "γαλαξιακήζωή", "wolvesvilleonline": "λυκόχωριonline", "slaythespire": "σκοτώνωτηνάκρη", "battlecats": "μάχεςγάτες", "sims3": "sims3", "sims4": "σπς4", "thesims4": "thesims4", "thesims": "τασίμς", "simcity": "σιμσιτι", "simcity2000": "σιμσιτυ2000", "sims2": "σims2", "iracing": "iracing", "granturismo": "γραφτηρισμό", "needforspeed": "χρειάζομαιταχύτητα", "needforspeedcarbon": "χρειά<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ύτητακαρμπον", "realracing3": "πραγματικ<PERSON>ςαγώνας3", "trackmania": "τρυπάγωνας", "grandtourismo": "μεγαλότουρισμα", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "τσ4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "χιπσιμς4", "fnaf": "φναφ", "outlast": "κρατάωρούδι", "deadbydaylight": "νεκρόςμέραμεσημέρι", "alicemadnessreturns": "επιστροφήστηντρέλατηςαλ<PERSON>κης", "darkhorseanthology": "σκοτεινόςιππότηςανθολογία", "phasmophobia": "φασματοφοβία", "fivenightsatfreddys": "πέντεν<PERSON>χτες<PERSON>τ<PERSON><PERSON>dy", "saiko": "σάικο", "fatalframe": "μπρικαπωλή", "littlenightmares": "μικροίεφιάλτες", "deadrising": "νεκροάνοδος", "ladydimitrescu": "κυρίαδημητρίσκου", "homebound": "εντόςσπιτιού", "deadisland": "νεκρονησί", "litlemissfortune": "μικρήκακοτυχία", "projectzero": "projectzero", "horory": "τρόμος", "jogosterror": "jogosterror", "helloneighbor": "γειάσουγειτόνε", "helloneighbor2": "γειασουγειτονά2", "gamingdbd": "gamingdbd", "thecatlady": "ηγατούλα", "jeuxhorreur": "παιχνίδιατρόμου", "horrorgaming": "τρομακτικάπαιχνίδια", "magicthegathering": "μαγικά<PERSON><PERSON><PERSON>υνάντηση", "mtg": "μαγικά", "tcg": "tcg", "cardsagainsthumanity": "κάρτε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τηνανθρωπότητα", "cribbage": "κριμ<PERSON><PERSON><PERSON>", "minnesotamtg": "μιννεσοταμιτγ", "edh": "εδω", "monte": "μοντέλο", "pinochle": "πινοκλ", "codenames": "κωδικώνυμα", "dixit": "δικέπτης", "bicyclecards": "ποδήλα<PERSON><PERSON><PERSON><PERSON><PERSON>τες", "lor": "λορ", "euchre": "ευχέριο", "thegwent": "ταg<PERSON>t", "legendofrunetera": "θρύλοςτο<PERSON><PERSON><PERSON>a", "solitaire": "σολίτερ", "poker": "πόκερ", "hearthstone": "καρδιάς<PERSON><PERSON><PERSON><PERSON>ος", "uno": "ούνο", "schafkopf": "σχα<PERSON><PERSON><PERSON><PERSON>", "keyforge": "κλειδοκατασκευή", "cardtricks": "μαγικάμεκάρτες", "playingcards": "παίζοντας<PERSON><PERSON>ρτες", "marvelsnap": "μαρβελσναπ", "ginrummy": "γιναράκι", "netrunner": "δικτυακ<PERSON>ςδρομέας", "gwent": "γκόεντ", "metazoo": "μετα<PERSON>oo", "tradingcards": "κάρτεςδιαπραγμάτευσης", "pokemoncards": "χαρτι<PERSON>_πόκεμον", "fleshandbloodtcg": "σάρκακαιαίμαtcg", "sportscards": "κάρτε<PERSON><PERSON><PERSON><PERSON>ητικών", "cardfightvanguard": "καρτομαχίαβάνγκαρντ", "duellinks": "duellinks", "spades": "σπαθιά", "warcry": "πολέμιοςκραυγή", "digimontcg": "ντιτζιμόντcg", "toukenranbu": "τουκεναμπού", "kingofhearts": "βασιλιάςτωνκαρδιών", "truco": "τρούκο", "loteria": "λοταρία", "hanafuda": "χαναφούδα", "theresistance": "ηαντίσταση", "transformerstcg": "transformerstcg", "doppelkopf": "ντόππελκοπ", "yugiohcards": "γιούγκιοκάρτες", "yugiohtcg": "γιούγκιοτcg", "yugiohduel": "γιουγιόντζα", "yugiohocg": "γιγιοχογ", "dueldisk": "ντουέλντισκ", "yugiohgame": "γιγιοχγκεϊμ", "darkmagician": "σκοτεινόςμάγος", "blueeyeswhitedragon": "μπλεμάτια<PERSON><PERSON>υκ<PERSON>ςδράκος", "yugiohgoat": "γιουγιόχγκόατ", "briscas": "μπρίσκας", "juegocartas": "παιχνίδι_καρτών", "burraco": "μπουράκο", "rummy": "ράμυ", "grawkarty": "γκραουκάρτυ", "dobble": "ντόμπλ", "mtgcommander": "μάγοι_διοικητής", "cotorro": "κοντόρ<PERSON>", "jeuxdecartes": "παιχνίδιατσάρτης", "mtgjudge": "κριτήςmtg", "juegosdecartas": "παιχνίδιακ<PERSON><PERSON>τών", "duelyst": "ντουέλιστ", "mtgplanschase": "μτγπλανσχέσεις", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "παιχνίδικ<PERSON><PERSON>τών", "carteado": "καρτεάντο", "sueca": "σουέκα", "beloteonline": "μπελόταonline", "karcianki": "καρτσιάνκι", "battlespirits": "μάχεςπνευμάτων", "battlespiritssaga": "μάχητωνπνευμάτων", "jogodecartas": "χαρτο<PERSON><PERSON><PERSON>γνιο", "žolíky": "τζολίκια", "facecard": "πρόσωποκαρτα", "cardfight": "μάχηκ<PERSON><PERSON>τ<PERSON>ν", "biriba": "μπιρίμπα", "deckbuilders": "κατασκ<PERSON><PERSON><PERSON><PERSON>τέςδεκτς", "marvelchampions": "μαρβελομάχοι", "magiccartas": "μαγικέςκάρτες", "yugiohmasterduel": "γιούγιοχ<PERSON>άστερντούελ", "shadowverse": "σκιαματοσύνθεση", "skipbo": "σκιπμπο", "unstableunicorns": "ασταθήμονόκεροι", "cyberse": "κυβερνοσε", "classicarcadegames": "κλασικάατομικάπαιχνίδια", "osu": "όσου", "gitadora": "γινταδόρα", "dancegames": "χορευτικ<PERSON>παιχνίδια", "fridaynightfunkin": "παρασκευοβραδινόφυνκιν", "fnf": "fnf", "proseka": "προσεκα", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "κιθαρίσταςμάγος", "clonehero": "κλώνενργίτης", "justdance": "χορέψτεαπλά", "hatsunemiku": "χάτσουνεμιούκου", "prosekai": "προσεκάι", "rocksmith": "ροκσμιθ", "idolish7": "ιντάλις7", "rockthedead": "rockthedead", "chunithm": "χούνιθμ", "idolmaster": "αϊνταλμάστερ", "dancecentral": "χορευτικ<PERSON>κεντρο", "rhythmgamer": "ρυθμιστήςρυθμού", "stepmania": "βημαμανία", "highscorerythmgames": "παιχνίδιαρυθμούυψηλώνβαθμών", "pkxd": "pkxd", "sidem": "σάιντμ", "ongeki": "ονγκεκί", "soundvoltex": "soundvoltex", "rhythmheaven": "ρυθμόςπαράδεισος", "hypmic": "υπομικ", "adanceoffireandice": "χορόςμεφωτιάκιπάγο", "auditiononline": "ακρόασηonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "παιχνίδιαρυθμού", "cryptofthenecrodancer": "κρυπτοαποκοιμιστής", "rhythmdoctor": "ρυθμόςια<PERSON>ρ<PERSON>ς", "cubing": "κύβοι", "wordle": "ουρνταλ", "teniz": "τένιζ", "puzzlegames": "παίχνιδιαπαζλ", "spotit": "βρεςτο", "rummikub": "ρουμικιούμπ", "blockdoku": "μπλοκντόκου", "logicpuzzles": "λογι<PERSON><PERSON><PERSON><PERSON><PERSON>λ", "sudoku": "σουδoku", "rubik": "ρου<PERSON><PERSON><PERSON><PERSON>", "brainteasers": "γρίφοι", "rubikscube": "ρουμπικςκιουβ", "crossword": "σταυρόλεξο", "motscroisés": "κινητέςσταυροφορίες", "krzyżówki": "σταυρολέξα", "nonogram": "νονογράμμα", "bookworm": "βιβλιοφάγος", "jigsawpuzzles": "παζλ", "indovinello": "γρίφος", "riddle": "γρίφος", "riddles": "γρίφοι", "rompecabezas": "παζλ", "tekateki": "τεκατεκι", "inside": "μέσα", "angrybirds": "θυμωμέναπουλιά", "escapesimulator": "παιχνίδιδιαφυγής", "minesweeper": "ναρκοπέδιο", "puzzleanddragons": "παζλκαιδράκοι", "crosswordpuzzles": "σταυρόλεξα", "kurushi": "κουρου<PERSON><PERSON>", "gardenscapesgame": "κῆποιορκοί", "puzzlesport": "παζλσπορ", "escaperoomgames": "παίχνιδαδωμάτιωνδιαφυγής", "escapegame": "παίξεκαιφύγε", "3dpuzzle": "3dπαζλ", "homescapesgame": "homescapesgame", "wordsearch": "αναζήτησηλέξεων", "enigmistica": "αινιγματιστική", "kulaworld": "κουλασυμπαν", "myst": "myst", "riddletales": "αταξιδιαзагадки", "fishdom": "fishdom", "theimpossiblequiz": "τοα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "καραμελοθλίψη", "littlebigplanet": "μικρόμεγαλοκόσμο", "match3puzzle": "παζλ3σετ", "huniepop": "χυνίεποπ", "katamaridamacy": "καταμαρίδαματσυ", "kwirky": "κουλτσίτσα", "rubikcube": "κύβοςτουρούμπικ", "cuborubik": "κουμπορούμπικ", "yapboz": "για<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "θεσταλοςprinciple", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "κουβερτ", "riddleme": "γρίφομε", "tycoongames": "παιχνίδιατιτάνες", "cubosderubik": "κυβοςτουρουμπικ", "cruciverba": "σταυρόλεξα", "ciphers": "κωδικοί", "rätselwörter": "γρίφοι", "buscaminas": "εξερευνητής", "puzzlesolving": "λύσιμοπαζλ", "turnipboy": "turnipboy", "adivinanzashot": "αποκαλυψήshot", "nobodies": "κανένας", "guessing": "μμμ", "nonograms": "νονογράμματα", "kostkirubika": "kostkirubika", "crypticcrosswords": "κρυπτικ<PERSON>ςσταυρόλεξα", "syberia2": "συβερία2", "puzzlehunt": "κυνήγι<PERSON>α<PERSON>λ", "puzzlehunts": "κυνήγι<PERSON>α<PERSON>λ", "catcrime": "γάταεγκλημα", "quebracabeça": "σπαζοκεφαλιά", "hlavolamy": "χάλαλο", "poptropica": "ποπτροπ<PERSON>κα", "thelastcampfire": "τοτελευτ<PERSON><PERSON>οκάμπινγκ", "autodefinidos": "αυτόορίζομαι", "picopark": "πικοπ<PERSON><PERSON>κ", "wandersong": "περιπλανώμενοςτραγούδι", "carto": "καρτο", "untitledgoosegame": "ατίτλοςγουσγκεϊμ", "cassetête": "κασέτα", "limbo": "λίμπο", "rubiks": "ρουμπίκς", "maze": "λαβύρινθος", "tinykin": "μικροάμπελο", "rubikovakostka": "ρουμπικουκάστκα", "speedcube": "ταχύβολα", "pieces": "κομμάτια", "portalgame": "πορτά<PERSON><PERSON><PERSON><PERSON><PERSON>μ", "bilmece": "μπιλέμτ<PERSON>έ", "puzzelen": "παζλ", "picross": "πι크ρός", "rubixcube": "ρουμπίκσκιουβ", "indovinelli": "γρίφοι", "cubomagico": "μαγικός<PERSON>ύβος", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "στριφογυριστόπαράδοξο", "monopoly": "μονόπολη", "futurefight": "μαχέστυομέλλον", "mobilelegends": "κινητοίθρύλοι", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "κοκ", "lonewolf": "μον<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>κος", "gacha": "γκάτσα", "wr": "ρβ", "fgo": "φγό", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "φφ", "ensemblestars": "αστέρε<PERSON><PERSON>mble", "asphalt9": "άσφαλτος9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>ος", "alchemystars": "αλχημιστάρς", "stateofsurvival": "κατάστασηεπιβίωσης", "mycity": "ηπόλημου", "arknights": "αρκναιτς", "colorfulstage": "χρωματιστήσκηνή", "bloonstowerdefense": "μπλουστά<PERSON>υερντεφένς", "btd": "btδ", "clashroyale": "κλασχρογιάλ", "angela": "αντζελα", "dokkanbattle": "ντοκκάνμπατλ", "fategrandorder": "μοίραμεγάληπαραγγελία", "hyperfront": "υπερπροσθιο", "knightrun": "ιπποδρομία", "fireemblemheroes": "ήρωες_της_φωτιάς", "honkaiimpact": "honkaiimpact", "soccerbattle": "ποδοσφαιρομάχη", "a3": "α3", "phonegames": "τηλέφωναπαιχνίδια", "kingschoice": "βασιλικήεπιλογή", "guardiantales": "γονίτ<PERSON>α<PERSON>ηςιστορίας", "petrolhead": "γκάζι", "tacticool": "τακτικουλ", "cookierun": "cookieαθλό", "pixeldungeon": "πιξελντάνγκιον", "arcaea": "αρκάια", "outoftheloop": "εκτόςκύκλου", "craftsman": "μάστερτεχνίτης", "supersus": "σούπερψεύτης", "slowdrive": "αργοδήγηση", "headsup": "γνώρισετο", "wordfeud": "wordfeud", "bedwars": "κρεβατοπόλεμοι", "freefire": "freefire", "mobilegaming": "κινητάπαιχνίδια", "lilysgarden": "κηπάκιτηςλίλι", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "μάχεςομάδας", "clashofclans": "μάχητωνφυλών", "pjsekai": "pjsekai", "mysticmessenger": "μυστικ<PERSON><PERSON><PERSON>γελιαφορος", "callofdutymobile": "callofdutymobile", "thearcana": "οαρκάνα", "8ballpool": "8ballpool", "emergencyhq": "καταστ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τακτηςανάγκης", "enstars": "ενσταρς", "randonautica": "ραντονάουτικα", "maplestory": "maplestory", "albion": "αλμπιόν", "hayday": "χρόνιαμας", "onmyoji": "ονιότζι", "azurlane": "αζούρλέιν", "shakesandfidget": "κουνάκιακαιγρυλίσματα", "ml": "μηλ", "bangdream": "μπανγκντριμ", "clashofclan": "clashofclan", "starstableonline": "αστέριοσταβλόνλαϊν", "dragonraja": "δράκοςραγιά", "timeprincess": "χρονιαπριγκίπισσα", "beatstar": "μπιτ<PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "δράκος_μυθικός_θρύλος", "hanabi": "χανάμπι", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "τσέπηαγάπη", "androidgames": "παιχνίδι<PERSON><PERSON><PERSON>", "criminalcase": "εγκληματικήυπόθεση", "summonerswar": "summonerswar", "cookingmadness": "κουζινομανούρα", "dokkan": "ντόκκαν", "aov": "αov", "triviacrack": "τριβιακράκ", "leagueofangels": "λέσχητωναγγέλων", "lordsmobile": "lordsmobile", "tinybirdgarden": "μικρόςκηποςπουργούντας", "gachalife": "γκατσαλαϊφ", "neuralcloud": "νευροσύννεφο", "mysingingmonsters": "τατεmonsterαγαπώ", "nekoatsume": "νεκοατσούμε", "bluearchive": "μπλεαρχείο", "raidshadowlegends": "raidshadowlegends", "warrobots": "πολεμικορομπότ", "mirrorverse": "καθρεφτόκοσμος", "pou": "πού", "warwings": "πόλεμοςφτερούγες", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "φάεχρόνο", "antiyoy": "αντίγιου", "apexlegendmobile": "apexlegendmobile", "ingress": "εισβολή", "slugitout": "παλέψτο", "mpl": "μπλ", "coinmaster": "νομισματομάστερ", "punishinggrayraven": "τιμωρητικ<PERSON>ςγκριχάροντας", "petpals": "φίλοικατοικίδια", "gameofsultans": "παιχνίδιτουσουλτάν", "arenabreakout": "αρέναδιαφυγή", "wolfy": "λύκος", "runcitygame": "παιχνίδιτρέξιμο", "juegodemovil": "παιχνίδικινητού", "avakinlife": "avakinlife", "kogama": "κογάμα", "mimicry": "μίμηση", "blackdesertmobile": "μαύρηέρημοςκινητό", "rollercoastertycoon": "τρελήλύση", "grandchase": "μεγάλοκυνήγι", "bombmebrasil": "βομβαρδιστεμεβραζιλία", "ldoe": "ldoe", "legendonline": "θρύλοςonline", "otomegame": "οτομ<PERSON>ιγκεμ", "mindustry": "μιντάστρι", "callofdragons": "κλήσηδράκων", "shiningnikki": "λαμπερήνικκι", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "πατώσεκανέναν", "sealm": "σφράγισετο", "shadowfight3": "σκιάμαχη3", "limbuscompany": "limbuscompany", "demolitionderby3": "κατεδα<PERSON><PERSON>στικόντερμπι3", "wordswithfriends2": "λέξειςμεφίλους2", "soulknight": "ψυχοιππότες", "purrfecttale": "τέλειαιστορία", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "χαχανίζωκινητό", "harvesttown": "συλλογήχωριού", "perfectworldmobile": "τέλειος<PERSON><PERSON>σμοςmobile", "empiresandpuzzles": "αυτοκρατορ<PERSON>εςκαιπαζλ", "empirespuzzles": "empirepuzzles", "dragoncity": "δράκοπόλη", "garticphone": "γκαρτικφόνια", "battlegroundmobileind": "battlegroundmobileind", "fanny": "φάνι", "littlenightmare": "μικρόςεφιάλτης", "aethergazer": "αιθέραςκοίταγμα", "mudrunner": "mudrunner", "tearsofthemis": "δάκρυατωνημίσοδων", "eversoul": "αθάνατηψυχή", "gunbound": "γκάνμπαουντ", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "αρκνάιτ", "pristontale": "πριστοντάλε", "zombiecastaways": "ζομπιοαποικιστές", "eveechoes": "eveechoes", "jogocelular": "γιόγκακι", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "ζούμπα", "mobilelegendbangbang": "κινητόθρύλοςμπαμμπαμ", "gachaclub": "γκάχακλαμπ", "v4": "β4", "cookingmama": "μαμάτηςκ<PERSON>υ<PERSON><PERSON>νας", "cabalmobile": "καμπαλμόμπιλ", "streetfighterduel": "μαχητέςτουδρόμου", "lesecretdhenri": "λεσσεκρέτνενρί", "gamingbgmi": "gamingleague", "girlsfrontline": "κορίτσιαμέτωπο", "jurassicworldalive": "παρώνστην<PERSON>αγκόσμιαταξινόμηση", "soulseeker": "αναζητητήςψυχής", "gettingoverit": "ξεπερνώνταςτο", "openttd": "ανοιχτόttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ιστορίεςτουmooncha", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "κινητάπαιχνίδια", "legendofneverland": "θησαυρόςτηςποτέποτας", "pubglite": "pubglite", "gamemobilelegends": "παίξεmobilelegends", "timeraiders": "χρόνοκυνηγοί", "gamingmobile": "κινητόπαιχνίδι", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "μάχεςγάτων", "dnd": "dnd", "quest": "αποστολή", "giochidiruolo": "γιωχιδιρουλο", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "κόσμοςτ<PERSON>υσκοτους", "travellerttrpg": "ταξιδευτήςttrpg", "2300ad": "2300μχ", "larp": "λαρπ", "romanceclub": "ρομαντι<PERSON><PERSON><PERSON><PERSON>ύλλογος", "d20": "δ20", "pokemongames": "παιχ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "ποκεμονμυστηριοδωμάτιο", "pokemonlegendsarceus": "ποκεμονθρύλοιαρκεούς", "pokemoncrystal": "ποκεμονκρυσταλλος", "pokemonanime": "ποκεμονανιμε", "pokémongo": "ποκεμονγκο", "pokemonred": "ποκεμόνκόκκινο", "pokemongo": "ποκεμόνγκο", "pokemonshowdown": "ποκεμονμάχη", "pokemonranger": "ποκεμον<PERSON><PERSON><PERSON>ν<PERSON><PERSON><PERSON>ρ", "lipeep": "λιπίπ", "porygon": "ποριγκ<PERSON>ν", "pokemonunite": "ποκεμονενωμένοι", "entai": "ένταϊ", "hypno": "υπνο", "empoleon": "εμπολεόν", "arceus": "άρκειος", "mewtwo": "μewtwo", "paldea": "παλντέα", "pokemonscarlet": "ποκεμον<PERSON>κα<PERSON>λετ", "chatot": "τσιτσίκα", "pikachu": "πικατσού", "roxie": "ροξι", "pokemonviolet": "ποκεμονβιολετί", "pokemonpurpura": "ποκεμονμπουργκρυσο", "ashketchum": "άσκιτσεμ", "gengar": "γκενγκαρ", "natu": "ντου", "teamrocket": "ομαδαρουκετα", "furret": "φουρέτ", "magikarp": "μαγικάρπ", "mimikyu": "<PERSON><PERSON><PERSON><PERSON>", "snorlax": "σνορλ<PERSON>ξ", "pocketmonsters": "τσέπικητέρας", "nuzlocke": "νάζλοκε", "pokemonplush": "γονίτσεςπόκεμον", "teamystic": "ομάδαμυστική", "pokeball": "ποκεμόνμπάλα", "charmander": "χάρ<PERSON>αντερ", "pokemonromhack": "ποκεμονρομχακ", "pubgmobile": "pubgmobile", "litten": "λούκι", "shinypokemon": "λα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "μεσπίρτ", "pokémoni": "ποκεμονί", "ironhands": "σιδερένιαχέρια", "kabutops": "καμπούτοψ", "psyduck": "ψυχοπάπια", "umbreon": "ουμβρεόν", "pokevore": "ποκεβόρε", "ptcg": "ptcg", "piplup": "πιπλπ", "pokemonsleep": "ποκεμονύπνου", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "μάστερποκέμον", "pokémonsleep": "ποκεμονύπνος", "kidsandpokemon": "παιδιάκαιπόκεμον", "pokemonsnap": "ποκεμονσνάπ", "bulbasaur": "μπουλμπασά<PERSON>υρ", "lucario": "λουκαρίο", "charizar": "χαρίζω", "shinyhunter": "θαυμα<PERSON><PERSON><PERSON><PERSON><PERSON>ηγ<PERSON>ς", "ajedrez": "σκακι", "catur": "κατανοιώσουμε", "xadrez": "σκακι", "scacchi": "σκακιέρα", "schaken": "σκέιτεν", "skak": "σκακ", "ajedres": "σκακι", "chessgirls": "κορίτ<PERSON><PERSON><PERSON><PERSON>κά<PERSON>ι", "magnuscarlsen": "μάγκνουςκάρλσεν", "worldblitz": "παγκόσμιος<PERSON><PERSON>λ<PERSON>τζ", "jeudéchecs": "ζατρίκιο", "japanesechess": "ιαπωνικάσκάκι", "chinesechess": "κινέζικεςσκακιές", "chesscanada": "σκακιστικήκαναδ<PERSON>ς", "fide": "φίδε", "xadrezverbal": "σκακιστικ<PERSON>ςλόγος", "openings": "ανοίγματα", "rook": "<PERSON><PERSON><PERSON><PERSON>", "chesscom": "σκακιέρα", "calabozosydragones": "καλαβόζοι_και_δράκοι", "dungeonsanddragon": "μπουντρούμιακαιδράκοι", "dungeonmaster": "δράκ<PERSON>μάστερ", "tiamat": "τιαμάτ", "donjonsetdragons": "donjonsetdragons", "oxventure": "οξβεντούρα", "darksun": "σκοτεινός<PERSON>λιος", "thelegendofvoxmachina": "οθρύλοςτουvoxmachina", "doungenoanddragons": "ντουντ<PERSON><PERSON><PERSON>όκαιδράκοι", "darkmoor": "σκοτεινόςβάλτος", "minecraftchampionship": "minecraftπρωτάθλημα", "minecrafthive": "μυρμηγκοφωλιάminecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "όνειραsmp", "hermitcraft": "ειρηνικήχειρονομία", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "χάιπικσελ", "karmaland5": "κάρμαλαντ5", "minecraftmods": "minecraftmods", "mcc": "μαλάκες", "candleflame": "φλόγατηςστήλης", "fru": "φρου", "addons": "προσθετα", "mcpeaddons": "mcpeπρόσθετα", "skyblock": "ουρανομπλόκ", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "τροποποιημένοminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "ανάμεσασεχώραδες", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "μινκρά<PERSON>τπόλη", "pcgamer": "pcgamer", "jeuxvideo": "παιχνίδιαβίντεο", "gambit": "γκάμπιτ", "gamers": "παίκτες", "levelup": "ανέβασετ<PERSON>επίπεδο", "gamermobile": "γκάμερκινητό", "gameover": "τέλοςπαιχνιδιού", "gg": "γκ", "pcgaming": "pcgaming", "gamen": "παίζουμε", "oyunoynamak": "παίζουμεμπαλα", "pcgames": "παιχνίδιαpc", "casualgaming": "χαλαρόπαιχνίδι", "gamingsetup": "ρύθμισηπαίχνιδιών", "pcmasterrace": "pcmasterrace", "pcgame": "pcgame", "gamerboy": "gamerguy", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kγgaming", "gamerbr": "gamersgr", "gameplays": "παιχνίδια", "consoleplayer": "παίκτης<PERSON><PERSON><PERSON><PERSON><PERSON>λας", "boxi": "μπόξι", "pro": "προ", "epicgamers": "επικά<PERSON>α<PERSON>χτες", "onlinegaming": "διαδικτυακάπαιχνίδια", "semigamer": "ημιπαίκτης", "gamergirls": "παίκτριες", "gamermoms": "μαμάδεςπαίκτες", "gamerguy": "παίκτης", "gamewatcher": "παιχνιδόφατσα", "gameur": "γκέιμερ", "grypc": "γκρίπc", "rangugamer": "ρανγκ<PERSON>ύγκειμερ", "gamerschicas": "παίκτριες", "otoge": "ωτόγκε", "dedsafio": "δεδομένο", "teamtryhard": "ομάδαπροσπάθεια", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "αποστολές", "alax": "αλ<PERSON>ξ", "avgn": "avgνο", "oldgamer": "παλιόςπαίκτης", "cozygaming": "ζεστόgaming", "gamelpay": "gamelpay", "juegosdepc": "παιχνίδιαγιαυπολογιστή", "dsswitch": "dsswitch", "competitivegaming": "ανταγωνιστικόgaming", "minecraftnewjersey": "minecraftνέας_αρκάνσας", "faker": "ψεύτης", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "γιατόρο", "heterosexualgaming": "ετεροφυλοφιλικόπαιχνίδι", "gamepc": "παίξεpc", "girlsgamer": "κορίτσια<PERSON>α<PERSON>κτες", "fnfmods": "fnfmods", "dailyquest": "καθημερινήαποστολή", "gamegirl": "παιχνιδόκοριτσο", "chicasgamer": "κορίτ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "gamesetup": "ρυθμισήπαιχνιδιού", "overpowered": "πανίσ<PERSON>υ<PERSON>ος", "socialgamer": "κοινωνικ<PERSON>ςπαίκτης", "gamejam": "παιχνιδιάρικα", "proplayer": "προπαίκτης", "roleplayer": "ρολεϊπλέρ", "myteam": "ηομάδαμου", "republicofgamers": "δημοκρατίατωνπαιχνιδιών", "aorus": "αόρους", "cougargaming": "νταμπλγκάμινγκ", "triplelegend": "τριπλόςθρύλος", "gamerbuddies": "φίλοιτουγκαίμερ", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "χριστιαν<PERSON>ςπαίκτησ", "gamernerd": "γκάμερνερντ", "nerdgamer": "νερντγκέιμερ", "afk": "αφκ", "andregamer": "andregamer", "casualgamer": "χαλαρ<PERSON><PERSON><PERSON><PERSON><PERSON>κτης", "89squad": "89συντροφιά", "inicaramainnyagimana": "ινικαραμαίννυαγκιμάνα", "insec": "ανασφάλεια", "gemers": "γκέμερς", "oyunizlemek": "ουνιζλέμε", "gamertag": "παίκτης", "lanparty": "λανπάρη", "videogamer": "βιντεοπαίκτης", "wspólnegranie": "κοινόπαιγμα", "mortdog": "μορτν<PERSON><PERSON><PERSON>κ", "playstationgamer": "παίκτηςplaystation", "justinwong": "τζάστινγουόνγκ", "healthygamer": "υγιής<PERSON><PERSON><PERSON><PERSON>της", "gtracing": "gtracing", "notebookgamer": "σημειωματάριοπαίκτης", "protogen": "πρωτογενής", "womangamer": "γυναικείαπαίκτρια", "obviouslyimagamer": "φανεράείμαιπαίκτης", "mario": "μάριο", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "μαριόγκολφ", "samusaran": "σαμουσάραν", "forager": "μαζευτής", "humanfallflat": "ανθρωποπτώσηματα", "supernintendo": "σούπερνίντεντο", "nintendo64": "νιντεντό64", "zeroescape": "μηδενικήδιαφυγή", "waluigi": "γουλου<PERSON><PERSON><PERSON>ι", "nintendoswitch": "νιντεντοσουιτς", "nintendosw": "νιντεντοσγου", "nintendomusic": "νιντεντομουσική", "sonicthehedgehog": "σόνικοθεχάιδγκογκ", "sonic": "σόνικ", "fallguys": "φθινοπωρινάαγόρια", "switch": "αλλάζω", "zelda": "ζέλντα", "smashbros": "σμσκμπρος", "legendofzelda": "θρύλοςτουζέλντα", "splatoon": "σπλατούνα", "metroid": "μετρόιντ", "pikmin": "πιγμίν", "ringfit": "ringfit", "amiibo": "αμίμπο", "megaman": "μεγαμάν", "majorasmask": "μάσκατωνμάγια", "mariokartmaster": "μάστερτουμάριοκάρτ", "wii": "γου<PERSON>", "aceattorney": "άσ<PERSON><PERSON><PERSON>ικηγ<PERSON><PERSON>ος", "ssbm": "ssbm", "skychildrenofthelight": "ουρανίεσπερνέστωνφωτός", "tomodachilife": "τομοδάχιλάιφ", "ahatintime": "αγάπηστηνώρα", "tearsofthekingdom": "δάκρυατουβασιλιά", "walkingsimulators": "περιπατητικοίπροσομοιωτές", "nintendogames": "παιχνίδιανίτεντο", "thelegendofzelda": "τομύθοτουζέλντα", "dragonquest": "καταδ<PERSON>ωξηδράκων", "harvestmoon": "συγκομιδήφεγγαριού", "mariobros": "μαριόμπρος", "runefactory": "runefactory", "banjokazooie": "μπαντ<PERSON><PERSON><PERSON><PERSON><PERSON>ο<PERSON>ι", "celeste": "σελέστε", "breathofthewild": "ανάσατης<PERSON><PERSON><PERSON>ιαςφύσης", "myfriendpedro": "φίλοςμουοπέδρο", "legendsofzelda": "legendsofzelda", "donkeykong": "ντονκείκογκ", "mariokart": "μαριοκαρτ", "kirby": "κίρμπι", "51games": "51παιχνίδια", "earthbound": "γήινες", "tales": "παραμύθια", "raymanlegends": "raymanlegends", "luigismansion": "λουιγιέςμάνσιον", "animalcrosssing": "ζωάκιαστιγμές", "taikonotatsujin": "ταϊκονοτάτσουζιν", "nintendo3ds": "νιντεντό3ds", "supermariobros": "σούπερμαριόμπρος", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "σπάζωτελικό", "nintendochile": "νιντεντοχ<PERSON>λη", "tloz": "tloz", "trianglestrategy": "τριγωνικήστρατηγική", "supermariomaker": "σούπερμαριομάκερ", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "σούπερμαριο64", "conkersbadfurday": "conkersκακήμέρα", "nintendos": "νιντέντοσ", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "πολεμιστέςτουχάιρουλ", "mariopartysuperstars": "μαριόγιορτέςυπερσταρς", "marioandsonic": "μάριοκ<PERSON><PERSON>σ<PERSON>νικ", "banjotooie": "μπαντ<PERSON><PERSON><PERSON><PERSON><PERSON>ι", "nintendogs": "ντεντόγκς", "thezelda": "τοzel<PERSON>", "palia": "παλιά", "marioandluigi": "μαριοκαιλουιγι", "mariorpg": "μαριορπγ", "zeldabotw": "ζελνταμποτβ", "yuumimain": "γιουμμυμαίν", "wildrift": "wildrift", "riven": "ραγισμένος", "ahri": "αχρί", "illaoi": "ίλλαοι", "aram": "άραμ", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "χρειαζομαι", "zyra": "ζύρα", "redcanids": "κόκκινοικήνες", "vanillalol": "βαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "μάγισσεςwi<PERSON>rift", "lolph": "χαχαφ", "leagueoflegend": "ligaxtonlegend", "tốcchiến": "ταχύτηταῖν", "gragas": "γκράγκ<PERSON><PERSON>", "leagueoflegendswild": "συγκλονιστικο<PERSON>παίκτεςλοιγκας", "adcarry": "adcarry", "lolzinho": "χαχαδάκι", "leagueoflegendsespaña": "leagueoflegendsελλάδα", "aatrox": "ατρ<PERSON><PERSON>", "euw": "εουυ", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "καυλή", "samira": "σαμίρα", "akali": "ακαλ<PERSON>", "lunari": "λουναρι", "fnatic": "fnatic", "lollcs": "λολτσ", "akshan": "άκσαν", "milio": "μύλιο", "shaco": "σάκος", "ligadaslegendas": "λίγερεςθρύλοι", "gaminglol": "gamingχαχα", "nasus": "νασόυς", "teemo": "τεέμο", "zedmain": "ζεντμέιν", "hexgates": "εξπύλες", "hextech": "εξτεχ", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "φορτνάιτεμπρ", "retrovideogames": "παλιάβιντεοπαιχνίδια", "scaryvideogames": "τρομακτικ<PERSON>βιντεοπαιχνίδια", "videogamemaker": "κατασκ<PERSON><PERSON><PERSON><PERSON>τήςβιντεοπαιχνιδιών", "megamanzero": "μεγκαμανζερο", "videogame": "βιντεοπαιχνίδι", "videosgame": "βιντεοπαιχνίδια", "professorlayton": "καθηγητήςλέιτον", "overwatch": "υπερεπίβλεψη", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "μάγος101", "battleblocktheater": "μάχημπλοκθεάτρου", "arcades": "αρκαδίες", "acnh": "acnh", "puffpals": "φούφοιφίλοι", "farmingsimulator": "προσομοιωτήςγεωργίας", "robloxchile": "robloxχίλη", "roblox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutschland": "ρομπλοξγερμανία", "robloxdeutsch": "ρομπλόξελληνικά", "erlc": "ερλκ", "sanboxgames": "sandboxgames", "videogamelore": "βιντεοπαιχνίδια", "rollerdrome": "ρολ<PERSON><PERSON><PERSON><PERSON>ρ<PERSON><PERSON>", "parasiteeve": "παρασιτηφάγος", "gamecube": "γκέιμκιουμπ", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "ονειροτόπιο", "starcitizen": "starcitizen", "yanderesimulator": "γιαντερεσιμιούλα", "grandtheftauto": "μεγάληκλοπήαυτοκινήτου", "deadspace": "νεκρόςχώρος", "amordoce": "αμόρντοσε", "videogiochi": "βιντεοπαιχνίδια", "theoldrepublic": "ηπαλιάδημοκρατία", "videospiele": "βιντεοπαιχνίδια", "touhouproject": "τουχόουπρο<PERSON><PERSON><PERSON><PERSON>τ", "dreamcast": "ονειροκάστρα", "adventuregames": "παιχνίδιαπεριπέτειας", "wolfenstein": "wolfenstein", "actionadventure": "δράσηπεριπέτεια", "storyofseasons": "ιστορίατωνεποχών", "retrogames": "ρετρόπαιχνίδια", "retroarcade": "ρετρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ντ", "vintagecomputing": "παλιάυπολογιστική", "retrogaming": "ρετρόπαιχνίδια", "vintagegaming": "παλιοί_παίχτες", "playdate": "παιχνιδ<PERSON><PERSON><PERSON><PERSON><PERSON>ηρ", "commanderkeen": "διοικητήςκιν", "bugsnax": "μπαγ<PERSON><PERSON><PERSON><PERSON>ξ", "injustice2": "αδικία2", "shadowthehedgehog": "σκιέςοσύντροφος", "rayman": "ρέιμαν", "skygame": "ουρανίοςπαίχνιδι", "zenlife": "ζενζωή", "beatmaniaiidx": "beatmaniaiidx", "steep": "κοφίνι", "mystgames": "μυστηριώδηπαχνίδια", "blockchaingaming": "μπλοκχαίνγκιμγ", "medievil": "μεσαιωνικ<PERSON>ς", "consolegaming": "παιχνίδια<PERSON><PERSON>κονσόλα", "konsolen": "κονσόλες", "outrun": "ξεφεύγω", "bloomingpanic": "ανθισμένοάγχος", "tobyfox": "τόμπυφοξ", "hoyoverse": "χογιόβερς", "senrankagura": "σενράνκγκουρα", "gaminghorror": "παιχνιδιοτρόμος", "monstergirlquest": "τέραςκορίτσιαποστολή", "supergiant": "σούπεργιγάντας", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "farmsims", "juegosviejos": "παλιάπαιχνίδια", "bethesda": "μπεθεστάδα", "jackboxgames": "jackboxgames", "interactivefiction": "διαδραστικήφημολογία", "pso2ngs": "pso2ngs", "grimfandango": "γκριμφαντάγκο", "thelastofus2": "τοτελευταίομας2", "amantesamentes": "αμαρτωλ<PERSON><PERSON><PERSON>γ<PERSON>πες", "visualnovel": "οπτικομυθιστόρημα", "visualnovels": "οπτικέςνουβέλες", "rgg": "ργκγ", "shadowolf": "σκιαλύκος", "tcrghost": "tcrghost", "payday": "ημέραπληρωμής", "chatherine": "κατερίνα", "twilightprincess": "πριγκίπισσατηςδύσης", "jakandaxter": "τζακκ<PERSON><PERSON>ντ<PERSON>ξτερ", "sandbox": "τόποςπαιχνιδιού", "aestheticgames": "αισθητικάπαιχνίδια", "novelavisual": "νέοςοπτικός", "thecrew2": "ηπαρέα2", "alexkidd": "αλε<PERSON><PERSON>ιτ", "retrogame": "παλιόπαιχνίδι", "tonyhawkproskater": "τόνυχόρκερ", "smbz": "σμμπζ", "lamento": "θλίβομαι", "godhand": "θεόςχέρι", "leafblowerrevolution": "επανάσταση_με_φύλλα_αέρα", "wiiu": "ουιου", "leveldesign": "σχεδιασμόςεπιπέδου", "starrail": "αστερία", "keyblade": "κλειδοblade", "aplaguetale": "άσπρο<PERSON><PERSON>ν<PERSON>ζά<PERSON>ι", "fnafsometimes": "fnafsometimes", "novelasvisuales": "οπτικοδιηγήματα", "robloxbrasil": "ρομπλοξμπραζιλ", "pacman": "πακμαν", "gameretro": "παίξεπαλι", "videojuejos": "βιντεοπαιχνίδια", "videogamedates": "ραντεβούμεβιντεοπαιχνίδια", "mycandylove": "ηκαρδιάμου", "megaten": "μεγκατεν", "mortalkombat11": "mortalcombategreek11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "χιλκγείμz", "batmangames": "παιχνίδιαμπατμαν", "returnofreckoning": "επιστροφήτηςεκδίκησης", "gamstergaming": "gamstergaming", "dayofthetantacle": "ημέρατουτάντακλ", "maniacmansion": "μανιακοσπιτι", "crashracing": "κρασάρισμα", "3dplatformers": "3dπλατφόρμες", "nfsmw": "nfsmw", "kimigashine": "κιμιγκα<PERSON>άιν", "oldschoolgaming": "παλιέςγκέιμες", "hellblade": "κολασμένος", "storygames": "ιστορίεςπαιχνίδια", "bioware": "βιοχάραξη", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "beyondtwosouls", "gameuse": "παιχνιδιάρα", "offmortisghost": "εκτόςτουφάντασματουμόρτις", "tinybunny": "μικρ<PERSON>ς<PERSON><PERSON><PERSON><PERSON>ς", "retroarch": "ρετρόαρτς", "powerup": "ενεργοποίηση", "katanazero": "καταν<PERSON><PERSON><PERSON><PERSON><PERSON>", "famicom": "φαμικον", "aventurasgraficas": "γραφικέςπεριπέτειες", "quickflash": "γρήγορηλ<PERSON>μ<PERSON>η", "fzero": "φζερο", "gachagaming": "γκατ<PERSON>α<PERSON>κάμινγκ", "retroarcades": "ρετρόαρκέδες", "f123": "f123", "wasteland": "ερημιά", "powerwashsim": "powerwashsim", "coralisland": "κοραλλιονησί", "syberia3": "συβερία3", "grymmorpg": "γκριμμμορπγ", "bloxfruit": "μπλόξφρουτ", "anotherworld": "άλλ<PERSON><PERSON><PERSON>σμος", "metaquest": "μεταquest", "animewarrios2": "ανιμέπολεμιστές2", "footballfusion": "ποδόσφαιροσυγχωνία", "edithdlc": "edithdlc", "abzu": "αβζού", "astroneer": "αστρονόμος", "legomarvel": "λεγκομαρβελ", "wranduin": "γουρντούνιν", "twistedmetal": "στριμμένομέταλλο", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "σωρόςντροπής", "simulator": "προσομοιωτής", "symulatory": "συμπολικότητες", "speedrunner": "ταχύτητος", "epicx": "επικ<PERSON>ν", "superrobottaisen": "σούπερρο<PERSON><PERSON><PERSON>τταisen", "dcuo": "dcuo", "samandmax": "σαμαντμαξ", "grywideo": "gry<PERSON>o", "gaiaonline": "γαίαonline", "korkuoyunu": "κορκούογιουνα", "wonderlandonline": "χώραθαυμάτωνonline", "skylander": "skylander", "boyfrienddungeon": "μπόγκανοςκαταφύγιο", "toontownrewritten": "τοοντά<PERSON>υναναδημιουργήθηκε", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "αστικ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "heavenlybodies": "θεϊκάσώματα", "seum": "σεουμ", "partyvideogames": "πάρτιβιντεοπαιχνίδια", "graveyardkeeper": "νεκροταφείονταφυλάκας", "spaceflightsimulator": "προσομοιωτήςδιαστημικώνπτήσεων", "legacyofkain": "κληρονομιάτουκαίν", "hackandslash": "χτύπακαικόβεις", "foodandvideogames": "φαγητόκιβιντεοπαιχνίδια", "oyunvideoları": "οπαιχνιδομάρα", "thewolfamongus": "ολύκοςμεταξύμας", "truckingsimulator": "σομιτατοκαναταξίδια", "horizonworlds": "ορίζοντεςκόσμοι", "handygame": "χέριπαιχνίδι", "leyendasyvideojuegos": "θρυλοικαιβιντεοπαιχνιδια", "oldschoolvideogames": "παλιέςσχολικέςβιντεοπαιχνίδια", "racingsimulator": "προσομοιωτήςα<PERSON>νων", "beemov": "bee<PERSON>v", "agentsofmayhem": "πράκτορεςτηςκαταστροφής", "songpop": "τραγούδιφημισμένο", "famitsu": "φαμιτσού", "gatesofolympus": "πύλεςτουολύμπου", "monsterhunternow": "τέρας<PERSON>υνηγόςτώρα", "rebelstar": "επαναστάτεςαστέρες", "indievideogaming": "ανεξάρτητοβιντεοπαιχνίδι", "indiegaming": "ανεξάρτητοπαιχνίδι", "indievideogames": "ανεξάρτητ<PERSON><PERSON>ιντεοπαιχνίδια", "indievideogame": "ανεξάρτητοβιντεοπαιχνίδι", "chellfreeman": "τ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "μπάφαρτρεσ", "unbeatable": "αήττητος", "projectl": "projεctl", "futureclubgames": "μελλοντικέςπαραστάσειςπαιχνιδιού", "mugman": "λουλουδάς", "insomniacgames": "υπνοβάτες<PERSON>s", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "επιστήμητηςαπoρροής", "backlog": "αναμονή", "gamebacklog": "παιχνίδιασελίδα", "gamingbacklog": "παλαιέςεξερευνήσεις", "personnagejeuxvidéos": "χαρακτήρεςπαιχνιδιών", "achievementhunter": "κυνηγόςεπιτυχιών", "cityskylines": "αστικέςγραφ<PERSON>ς", "supermonkeyball": "σούπερπίθηκοςμπάλα", "deponia": "ντεπόνια", "naughtydog": "κακο<PERSON>κυλο", "beastlord": "θηρίοάρχης", "juegosretro": "αναμνήσειςπαιχνιδιών", "kentuckyroutezero": "κερκυρα5", "oriandtheblindforest": "ορικαιτοτυφλόδασος", "alanwake": "αλαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stanleyparable": "στάνλεϊπαραβολή", "reservatoriodedopamin": "ρεσερβατόριοδοπαμίνης", "staxel": "σμέρνα", "videogameost": "βιντεοπαιχνιδιουμουσική", "dragonsync": "δράκωνσυγχρονισμός", "vivapiñata": "βιβαπινατα", "ilovekofxv": "αγαπώkofxv", "arcanum": "αρκάνουμ", "neoy2k": "νεοy2k", "pcracing": "pcracing", "berserk": "μπερσερκ", "baki": "μπάκι", "sailormoon": "σεΐλอร์μουν", "saintseiya": "αγαπημένοιτροίνοπες", "inuyasha": "ινουγιάσα", "yuyuhakusho": "γιγυχακ<PERSON><PERSON>σο", "initiald": "αρχικήδεκάδα", "elhazard": "ελχά<PERSON><PERSON><PERSON>ντ", "dragonballz": "δράκοντας<PERSON>πάλαζ", "sadanime": "λυπημένανιμε", "darkerthanblack": "σκοτεινότεροαπόμαύρο", "animescaling": "ανιμεσκαλίng", "animewithplot": "animesmeploth", "pesci": "πες_σι", "retroanime": "ρετροανιμέ", "animes": "ανιμέ", "supersentai": "υπερσεντάι", "samuraichamploo": "σαμουράιχαμπλού", "madoka": "μαδόκα", "higurashi": "χιγκουρ<PERSON><PERSON>ι", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "σκοτεινός<PERSON><PERSON>χοντας", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "μάστερπονγκι", "samuraix": "σαμουρ<PERSON>ιx", "dbgt": "dbgt", "veranime": "βερανιμέ", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "λουπινίιι", "drstoneseason1": "drstoneseason1", "rapanime": "ραπαναμέ", "chargemanken": "φορτιστήσυναισθηματικού", "animecover": "ανιμεκάλυψη", "thevisionofescaflowne": "τοόραματ<PERSON>υescaflowne", "slayers": "σφάχτες", "tokyomajin": "τοκιόματζιν", "anime90s": "ανιμε90ς", "animcharlotte": "ανιμ<PERSON><PERSON><PERSON><PERSON><PERSON>τ", "gantz": "γκάν<PERSON><PERSON>", "shoujo": "σόυτζο", "bananafish": "μπαναφις", "jujutsukaisen": "ζουτζούτσουκαϊσεν", "jjk": "jjk", "haikyu": "χαϊκιού", "toiletboundhanakokun": "τουαλετοδεσποτης<PERSON><PERSON>ν<PERSON>κ<PERSON>κου", "bnha": "bnha", "hellsing": "χελσίνγκ", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "βανίτας", "fireforce": "φωτιάδύναμη", "moriartythepatriot": "μοριάρτυοπατριώτης", "futurediary": "ημερολόγιομέλλοντος", "fairytail": "παραμυθένιο", "dorohedoro": "ντοροχεδόρο", "vinlandsaga": "βινλαντσάγκα", "madeinabyss": "φτιαγμένοστηνάβυσσο", "parasyte": "παράσιτο", "punpun": "πονπόν", "shingekinokyojin": "επίθεσητιτάνων", "mushishi": "μουσισί", "beastars": "μπίσταρς", "vanitasnocarte": "βουλευτικήκαρδιά", "mermaidmelody": "γοργονίκαμελωδία", "kamisamakiss": "καμισαμακ<PERSON>σσ", "blmanga": "μπλόγκμαγκα", "horrormanga": "τρομακτικάμανγκα", "romancemangas": "ρομαντικάμανγκα", "karneval": "καρναβάλι", "dragonmaid": "δράκονοικοκυρά", "blacklagoon": "μαύρηλαγκα", "kentaromiura": "κενταρομιούρα", "mobpsycho100": "μόμπψάικο100", "terraformars": "τεραφορμάρς", "geniusinc": "ιδιοφυΐαinc", "shamanking": "σαμακίνγκ", "kurokonobasket": "κουρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>σκετ", "jugo": "γιούγκο", "bungostraydogs": "μπαγκον<PERSON><PERSON><PERSON>νόσκυλα", "jujustukaisen": "τζούτζουτζαϊσεν", "jujutsu": "ζεντζούτσου", "yurionice": "γιούριον<PERSON>ισ", "acertainmagicalindex": "έναςμαγικοςδείκτης", "sao": "σάο", "blackclover": "μαύροτριφύλλι", "tokyoghoul": "τοκιογκούλ", "onepunchman": "έναχτύπημαάντρας", "hetalia": "εταλία", "kagerouproject": "καγκερού<PERSON>ρο<PERSON>ζε<PERSON>τ", "haikyuu": "χαϊκίου", "toaru": "τοαρού", "crunchyroll": "κράντσιρολ", "aot": "αοτ", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "σουριούστεν<PERSON><PERSON><PERSON><PERSON>γκ<PERSON>ρ", "spyxfamily": "κατασκοπείαxοικογένεια", "rezero": "ρεζ<PERSON>ρ<PERSON>", "swordartonline": "μαχαιροτεχνηδιαδικτυακά", "dororo": "ντορόρο", "wondereggpriority": "προτερ<PERSON><PERSON><PERSON><PERSON>ηταστηναυγόφουσκωμα", "angelsofdeath": "άγγελοιτουθανάτου", "kakeguri": "κακέγκουρι", "dragonballsuper": "δρακομπάλαυπερ", "hypnosismic": "υπνοσμισμίκ", "goldenkamuy": "χρυσήκαμούι", "monstermusume": "τερατομουςούμια", "konosuba": "κονόσουμπα", "aikatsu": "αϊκάτσου", "sportsanime": "αθλητικάανιμέ", "sukasuka": "σούκασούκα", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "αγγελοχτυπήματα", "isekaianime": "ισεκαιανιμε", "sagaoftanyatheevil": "σάγκατηςτίγρηςτηςκακ<PERSON>ας", "shounenanime": "σόουνενάιμε", "bandori": "μπαντόρι", "tanya": "τάνυα", "durarara": "ντουραράρα", "prettycure": "όμορφηθεραπεία", "theboyandthebeast": "τοαγόρικαιτοθηρίο", "fistofthenorthstar": "χέριτουβορρά", "mazinger": "μαζίν<PERSON><PERSON><PERSON>ρ", "blackbuttler": "μαύρος<PERSON><PERSON><PERSON>κλα", "towerofgod": "πύργοςτουθεού", "elfenlied": "ελέφαντχαρτ", "akunohana": "ακουνοχάνα", "chibi": "τσίμπι", "servamp": "σερβάμπ", "howtokeepamummy": "πώςνακρατήσωμιαμαμά", "fullmoonwosagashite": "πανσελήνειαμεμπου", "shugochara": "σουγκοχάρα", "tokyomewmew": "τοκυόμευμέυ", "gugurekokkurisan": "γκουγκουρεκοκουρίσσαν", "cuteandcreepy": "χαριτωμένοκαιτροματικό", "martialpeak": "πολεμιστήςκορυφή", "bakihanma": "μπακικανμά", "hiscoregirl": "κορίτ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "orochimaru": "οροτσιμάρου", "mierukochan": "μιερουκοτσαν", "dabi": "ντάμπι", "johnconstantine": "γιωρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ταντίνου", "astolfo": "αστόλφο", "revanantfae": "αναγενν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν<PERSON>ρντ", "shinji": "σιντζι", "zerotwo": "μηδενδύο", "inosuke": "ινοσούκε", "nezuko": "νεζούκο", "monstergirl": "τέρατοκ<PERSON>ρίτσι", "kanae": "κανάε", "yone": "γιόνι", "mitsuki": "μιτσούκι", "kakashi": "κακάσι", "lenore": "λενορ", "benimaru": "μπένιμαρου", "saitama": "σαϊτάμα", "sanji": "σαντζί", "bakugo": "μπακούγκο", "griffith": "γκρίφθ", "ririn": "ριριν", "korra": "κορρα", "vanny": "βάνυ", "vegeta": "βέτζετα", "goromi": "γορομί", "luci": "λούτσα", "reigen": "ρίγγεν", "scaramouche": "σκαραμούτς", "amiti": "αυθεντική", "sailorsaturn": "ναυτέςτουκρόνου", "dio": "ντίο", "sailorpluto": "ναυτικόςπλούτωνας", "aloy": "αλοϊ", "runa": "ρουνα", "oldanime": "παλιόανιμέ", "chainsawman": "αλυσοπρίοναςμαν", "bungoustraydogs": "μπανγκοουστρέιγκντογκς", "jogo": "παίξτε", "franziska": "φραντ<PERSON><PERSON>σ<PERSON>α", "nekomimi": "νέκομιμί", "inumimi": "ινουμίμι", "isekai": "ισεκάι", "tokyorevengers": "τοκιωρεβέντζερς", "blackbutler": "μαύρ<PERSON><PERSON><PERSON><PERSON><PERSON>της", "ergoproxy": "εργοπρόξυ", "claymore": "κλαϊμόρ", "loli": "λόλι", "horroranime": "τρομακτικάανιμέ", "fruitsbasket": "καρπο<PERSON><PERSON><PERSON><PERSON><PERSON>κούλα", "devilmancrybaby": "διαβολόανθρωποςκλάψας", "noragami": "νοράγκαμι", "mangalivre": "μάνγκαελεύθερα", "kuroshitsuji": "κουρου<PERSON><PERSON>τ<PERSON>ουτ<PERSON>ι", "seinen": "σέινεν", "lovelive": "αγάπαστηζη", "sakuracardcaptor": "σάκουρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ακτ<PERSON>ρ", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "οπόλεμοςτουσεραφ", "thepromisedneverland": "ηυποσχεμένηχώρα", "monstermanga": "τέρατομάγκα", "yourlieinapril": "τοψέμασουτοναπρίλη", "buggytheclown": "μπάγγυοκλόουν", "bokunohero": "μποκουνόηρωας", "seraphoftheend": "σεραφώναςτουτέλους", "trigun": "τρίγκα<PERSON>н", "cyborg009": "κυβερνο009", "magi": "μάγια", "deepseaprisoner": "βαθύθαλασσινόςφυλακισμένος", "jojolion": "τζοτζολιόν", "deadmanwonderland": "νεκρόςκόσμοςθαυμάτων", "bannafish": "μπανανά<PERSON>ι", "sukuna": "σουκούνα", "darwinsgame": "παιχνίδιτουδαρβίνου", "husbu": "χουσμπού", "sugurugeto": "σουγκούργετω", "leviackerman": "λεβιάκκερμαν", "sanzu": "σανζού", "sarazanmai": "σαραζανάι", "pandorahearts": "πανδώρα<PERSON><PERSON><PERSON>διές", "yoimiya": "γιόιμιγια", "foodwars": "μαχεςφαγητού", "cardcaptorsakura": "καρτώνκλέφτηςσακούρα", "stolas": "στόλας", "devilsline": "γραμμήτουδιαβόλου", "toyoureternity": "στηναιωνισήσου", "infpanime": "infπανίμε", "eleceed": "elec<PERSON>", "akamegakill": "ακαμε<PERSON>κακιλλ", "blueperiod": "μπλεπερίοδος", "griffithberserk": "γκρίφιθμπερσέρκ", "shinigami": "σινιγκάμι", "secretalliance": "μυστικήσυμμαχία", "mirainikki": "μιραεινίκι", "mahoutsukainoyome": "μαχοτσ<PERSON>ύκαινόυμε", "yuki": "γιούκι", "erased": "σβησμένο", "bluelock": "μπλεκλείδωμα", "goblinslayer": "σφαγέαςγκομπλίνων", "detectiveconan": "ντετέκτιβκόναν", "shiki": "σικί", "deku": "ντεκού", "akitoshinonome": "ακιτοσίνωνομε", "riasgremory": "ριασγκρεμόρυ", "shojobeat": "σότζομπιτ", "vampireknight": "βρικόλακεςιππότες", "mugi": "μούγκι", "blueexorcist": "μπλεεξορκιστής", "slamdunk": "σλαμντάνκ", "zatchbell": "ζατςμπελ", "mashle": "μασχλέ", "scryed": "σκληρότερος", "spyfamily": "κατασκοπικήοικογένεια", "airgear": "αεροφόρεμα", "magicalgirl": "μαγικήκορίτσι", "thesevendeadlysins": "αυτέςο<PERSON><PERSON>πτάθανάσιμεςαμαρτίες", "prisonschool": "σχολείοστιςφυλακές", "thegodofhighschool": "οθεόςτουλυκείου", "kissxsis": "φιλάκιααδερφούλη", "grandblue": "μεγάλομπλε", "mydressupdarling": "πανέμορφηντυνομίες", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "ρο<PERSON><PERSON><PERSON>μ<PERSON>ιντεν", "animeuniverse": "ανεβα<PERSON>ιστοδιάστημα", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "χιλιάδεςουρανούδες", "dragonballgt": "dragonballgt", "bocchitherock": "μπότζιθεροκ", "kakegurui": "κακεγκουρούι", "mobpyscho100": "mobpyscho100", "hajimenoippo": "χατζιμενοϊππο", "undeadunluck": "αθάνατηατυχία", "romancemanga": "ρομαντικάμανγκα", "blmanhwa": "blμανχάγουα", "kimetsunoyaba": "κιμετσούνογιάμπα", "kohai": "κοχάι", "animeromance": "ανιμερομαντς", "senpai": "σενπάι", "blmanhwas": "μπλμανχουά", "animeargentina": "animeαργεντινή", "lolicon": "λολικον", "demonslayertothesword": "δαίμον<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>πότηλογχίδα", "bloodlad": "bloodlad", "goodbyeeri": "αντί<PERSON><PERSON><PERSON><PERSON>", "firepunch": "φωτιάγροθιά", "adioseri": "αδειασερυ", "tatsukifujimoto": "τατσούκιφουτζίμοτο", "kinnikuman": "κιinn<PERSON><PERSON>", "mushokutensei": "μουσοκουτενσέι", "shoujoai": "σιωμποϊ", "starsalign": "τααστέριαευθυγραμμίζονται", "romanceanime": "ρομαντικόανιμέ", "tsundere": "τσούντερε", "yandere": "γιαντέρε", "mahoushoujomadoka": "μαχοσούτζομαδόκα", "kenganashura": "κενγκανασούρα", "saointegralfactor": "σαοολοκληρωτικ<PERSON>ςπαράγοντας", "cherrymagic": "κερασσομαγεία", "housekinokuni": "σπιτικινόκουνα", "recordragnarok": "καταγρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "καληνύχταμπου", "meliodas": "μελιόδας", "fudanshi": "φουτάνσι", "retromanga": "ρετρομάγκα", "highschoolofthedead": "λυκείοτωννεκρών", "germantechno": "γερμαντεχνο", "oshinoko": "οσινκό", "ansatsukyoushitsu": "αναστ<PERSON><PERSON><PERSON><PERSON>κιουσίντου", "vindlandsaga": "βιντλαντσαγκα", "mangaka": "μανγκακά", "dbsuper": "ντάμπσούπερ", "princeoftennis": "πρίγκιπαςτουτένις", "tonikawa": "τονικάβα", "esdeath": "εσντεθ", "dokurachan": "ντοκουράχαν", "bjalex": "μπ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assassinclassroom": "μαθήματαδολοφόνων", "animemanga": "ανιμέμανγκα", "bakuman": "μπακούμαν", "deathparade": "παρέλα<PERSON><PERSON>θανάτου", "shokugekinosouma": "σοκουγκεκινόσουμα", "japaneseanime": "ιαπωνικόανιμέ", "animespace": "χ<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "κορίτ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "ακμπ0048", "hopeanuoli": "ελπίδαστηνκαρδιά", "animedub": "ανεβασμένοanime", "animanga": "ανιμανγκα", "tsurune": "τσουρούνε", "uqholder": "uqholder", "indieanime": "ανεξάρτητοανιμέ", "bungoustray": "μπουνγκ<PERSON><PERSON>υστράι", "dagashikashi": "νταγκα<PERSON>ικασί", "gundam0": "γκαντάμ0", "animescifi": "ανιμεεπιστημονικήςφαντασίας", "ratman": "ποντικοάνθρωπος", "haremanime": "χαρούμεν<PERSON><PERSON><PERSON>", "kochikame": "κοτσικαμέ", "nekoboy": "νέκοςαγόρι", "gashbell": "κλαρμπελ", "peachgirl": "ρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρίτ<PERSON>ι", "cavalieridellozodiaco": "καβαλιερεσλουδιου", "mechamusume": "μεχαμουσούμε", "nijigasaki": "νιτζιγ<PERSON>α<PERSON><PERSON>κι", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "δράκοςεξ<PERSON>ρεύνησηντάι", "heartofmanga": "καρδιάτουμάνγκα", "deliciousindungeon": "νοστιμιάστουκάτωμα", "manhviyaoi": "μανχβιαοϊ", "recordofragnarok": "βιβλίοτουράγκναροκ", "funamusea": "διασκέδασημαγεία", "hiranotokagiura": "ηρηνατοκαγιούρα", "mangaanime": "μανγκαανιμε", "bochitherock": "μπόχινθεροκ", "kamisamahajimemashita": "καμισάμαχαζ<PERSON>μεμασταν", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "σούματσουνοβάλκυριε", "tutorialistoohard": "τομάθημαείναιπολύδύσκολο", "overgeared": "στραπατσαρισμένος", "toriko": "τορίκο", "ravemaster": "ραβέμαστρο", "kkondae": "κκο<PERSON>ντ<PERSON>ι", "chobits": "χομπίτς", "witchhatatelier": "μάγισσακαπέλοατελιέ", "lansizhui": "λάνσιζουι", "sangatsunolion": "σανγκατσουνολιόν", "kamen": "καμμένος", "mangaislife": "μανγκάείναιζωή", "dropsofgod": "σταγονίτσεςτουθεού", "loscaballerosdelzodia": "οιιππότεςτουζωδιακού", "animeshojo": "ανιμε<PERSON><PERSON><PERSON><PERSON>ο", "reverseharem": "αντίστροφοχαρέμι", "saintsaeya": "αγίοιμάχοι", "greatteacheronizuka": "σπουδ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>οςονιζούκα", "gridman": "γρίδμαν", "kokorone": "κοκορόνε", "soldato": "στρατιώτης", "mybossdaddy": "οπατεραςμου", "gear5": "gear5", "grandbluedreaming": "μεγάλομπλεόνειρο", "bloodplus": "bloodplus", "bloodplusanime": "αίμα_και_anime", "bloodcanime": "αίμ<PERSON><PERSON><PERSON>", "bloodc": "αίμαc", "talesofdemonsandgods": "ιστορίεςδαίμωνκαιθεών", "goreanime": "γκόρεανιμέ", "animegirls": "κορίτσιαανιμέ", "sharingan": "<PERSON><PERSON>", "crowsxworst": "κοράκιαxχειρότερα", "splatteranime": "σταμπαρισμένοανιμέ", "splatter": "ξεφλουδίζω", "risingoftheshieldhero": "άνοδοςτουήρωατουασπίδα", "somalianime": "σομαλάνιμε", "riodejaneiroanime": "ριουντεϊανέιροανιμέ", "slimedattaken": "slimedattaken", "animeyuri": "ανιμεγιούρι", "animeespaña": "animeεσπανία", "animeciudadreal": "animeσιudadreal", "murim": "μυρίμ", "netjuunosusume": "καλύτερηπροτεινόμενη", "childrenofthewhales": "παιδιάτωνφάλαινων", "liarliar": "ψεύτηςψεύτης", "supercampeones": "σούπερκαμπιόνες", "animeidols": "ανιμεείδωλα", "isekaiwasmartphone": "ισεκάιμετηλέφωνο", "midorinohibi": "μιντορινόχιμπι", "magicalgirls": "μαγικέςκοπέλες", "callofthenight": "κάλεσματηςν<PERSON>χτας", "bakuganbrawler": "μπακουγκανμπράουλερ", "bakuganbrawlers": "μπακουγκάνμπράουλερς", "natsuki": "νατσούκι", "mahoushoujo": "μαχου<PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "σκιάκκηπος", "tsubasachronicle": "τσούμπασαχρονικό", "findermanga": "βρεςμανγκα", "princessjellyfish": "πριγκιπισσαμέδουσα", "kuragehime": "κουραγέημα", "paradisekiss": "φιλιάστοπαράδεισο", "kurochan": "κουν<PERSON>λά<PERSON>ι", "revuestarlight": "αναβιβαστεράϊτ", "animeverse": "ανιμεκοσμος", "persocoms": "προσωπικοίυπολογιστές", "omniscientreadersview": "παντο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νώστεςοπτική", "animecat": "ανιμεγάτα", "animerecommendations": "συστάσειςanime", "openinganime": "άνοιγμαanime", "shinichirowatanabe": "σινίτσιροουαταναμπέ", "uzumaki": "ουζουμάκι", "myteenromanticcomedy": "μυθιστορήματαεφηβικήςαγάπης", "evangelion": "ευαγγέλιο", "gundam": "γκαντ<PERSON>μ", "macross": "μακροσ", "gundams": "γκαντάμς", "voltesv": "βολτε<PERSON>v", "giantrobots": "γιγάντιοιρομπότ", "neongenesisevangelion": "νέονγέννησηευαγγέλιο", "codegeass": "codegeass", "mobilefighterggundam": "κινητόςμαχητήςggundam", "neonevangelion": "νεοευαγγέλιο", "mobilesuitgundam": "κινητ<PERSON><PERSON>uitgundam", "mech": "μηχ", "eurekaseven": "eurekaseven", "eureka7": "ευρέκα7", "thebigoanime": "τομπ<PERSON>γ<PERSON><PERSON><PERSON><PERSON>", "bleach": "αποχρωματισμός", "deathnote": "σημειωμαθανατου", "cowboybebop": "καουμπόιμπεμπόπ", "jjba": "jjba", "jojosbizarreadventure": "μπουμμπουκιαεξωτικηπεριπετεια", "fullmetalalchemist": "ολόχρυσοςαλχημιστής", "ghiaccio": "πάγος", "jojobizarreadventures": "αταξίες_του_μπουμπούκου", "kamuiyato": "καμουϊγιάτο", "militaryanime": "στρατιωτικόάνιμε", "greenranger": "πράσιν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρ", "jimmykudo": "τζιμικούδο", "tokyorev": "τοκυρεβ", "zorro": "ζόρο", "leonscottkennedy": "λεονσκοτκερνεντύ", "korosensei": "κορ<PERSON><PERSON><PERSON><PERSON><PERSON>ι", "starfox": "αστέρινον<PERSON><PERSON><PERSON><PERSON>ρ", "ultraman": "ουλτραμάν", "salondelmanga": "σαλόνιτουμάνγκα", "lupinthe3rd": "λουπινθ3rd", "animecity": "ανιμεπόλη", "animetamil": "ανιμεταμίλ", "jojoanime": "τζοτζοανιμέ", "naruto": "ναρούτο", "narutoshippuden": "ναρουτοσιπουντεν", "onepiece": "ένακομμάτι", "animeonepiece": "αanimonepiece", "dbz": "ντμπζ", "dragonball": "δραγωνπάλα", "yugioh": "γιγιοχ", "digimon": "ντιγκιμόν", "digimonadventure": "ντάιτζιμονπεριπέτεια", "hxh": "χχχ", "highschooldxd": "λυκείονdxd", "goku": "γκόκου", "broly": "μπρόλι", "shonenanime": "σαπωνομάναime", "bokunoheroacademia": "μπόκουνοχίροακαδέμια", "jujustukaitsen": "γιουγιο<PERSON>ουκείς", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "κιμετσουνογιαίμπα", "shonenjump": "σόνετζαμπ", "otaka": "οτάκα", "hunterxhunter": "κυνήγικυνήγι", "mha": "μχα", "demonslayer": "δαίμοναςσφαγέας", "hinokamikagurademonsl": "ηνωκαμικαγκούραδεμόνσλ", "attackontitan": "επίθεσηστ<PERSON><PERSON><PERSON>s", "erenyeager": "erene<PERSON>", "myheroacademia": "μανθάνοντάςηρωές", "boruto": "μπορούτο", "rwby": "rwby", "dandadan": "νταντανταν", "tomodachigame": "τομοντάτ<PERSON>ιγκέιμ", "akatsuki": "ακατσ<PERSON><PERSON><PERSON>ι", "surveycorps": "ομάδαέρευνας", "onepieceanime": "onepieceanime", "attaquedestitans": "επιθέσειςτωντιτάνων", "theonepieceisreal": "τοonepieceείναιαληθινό", "revengers": "εκδικητές", "mobpsycho": "μάγισσα", "aonoexorcist": "aonoexorcist", "joyboyeffect": "επίδρα<PERSON>ητ<PERSON>υjoyboy", "digimonstory": "ιστορίεςντίτζιμον", "digimontamers": "ψηφιακοίμάγοι", "superjail": "superjail", "metalocalypse": "μεταλοκαλύψατε", "shinchan": "σίντσαν", "watamote": "watamote", "uramichioniisan": "ουραμιχιόνισαν", "uruseiyatsura": "ουρουσέιγιατσουρα", "gintama": "γιντάμα", "ranma": "ρανμά", "doraemon": "ντόραεμον", "gto": "γκοτσο", "ouranhostclub": "ουρανόςclub", "flawlesswebtoon": "τέλειοwebtoon", "kemonofriends": "κονέμενομικοι", "utanoprincesama": "ουτανόπριγκίπισαμα", "animecom": "ανιμεκοινοτἰα", "bobobobobobobo": "μπαμπαμπαμπαμπα", "yuukiyuuna": "γιουκίγιουνά", "nichijou": "νιχιτζου", "yurucamp": "γιουρούκαμπ", "nonnonbiyori": "νονονμπιόρι", "flyingwitch": "πετάχτρα", "wotakoi": "wotakoi", "konanime": "κονάμι", "clannad": "κλάννατ", "justbecause": "απλάέτσι", "horimiya": "χοριμία", "allsaintsstreet": "όλοιοιάγιοιδρόμος", "recuentosdelavida": "αναπολησειςτηςζωης"}