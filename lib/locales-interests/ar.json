{"2048": "2048", "mbti": "mbti", "enneagram": "انيغرام", "astrology": "علمالتنجيم", "cognitivefunctions": "الوظائف_المعرفية", "psychology": "علم_النفس", "philosophy": "الفلسفة", "history": "التاريخ", "physics": "الفيزياء", "science": "العلوم", "culture": "الثقافة", "languages": "اللغات", "technology": "التكنولوجيا", "memes": "الميمز", "mbtimemes": "م<PERSON><PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "ميمز_علم_التنجيم", "enneagrammemes": "ميمز_الانيغرام", "showerthoughts": "أفكار_الاستحمام", "funny": "مض<PERSON><PERSON>ة", "videos": "فيديوهات", "gadgets": "الأدوات", "politics": "السياسة", "relationshipadvice": "نصائح_العلاقات", "lifeadvice": "نصائح_الحياة", "crypto": "العملات_الرقمية", "news": "<PERSON><PERSON><PERSON><PERSON>ر", "worldnews": "اخبار_العالم", "archaeology": "علم_الآثار", "learning": "التعلم", "debates": "النقاشات", "conspiracytheories": "نظريات_المؤامرة", "universe": "الكون", "meditation": "التأمل", "mythology": "الميثولوجيا", "art": "الفن", "crafts": "الحرف", "dance": "الرقص", "design": "التصميم", "makeup": "ماكياج", "beauty": "الجمال", "fashion": "الموضة", "singing": "الغناء", "writing": "الكتابة", "photography": "التصوير_الفوتوغرافي", "cosplay": "التنكر", "painting": "الرسم", "drawing": "الرسم_الانشائي", "books": "الكتب", "movies": "الأفلام", "poetry": "الشعر", "television": "التلفاز", "filmmaking": "صناعة_الأفلام", "animation": "الرسوم_المتحركة", "anime": "أنمي", "scifi": "الخيال_العلمي", "fantasy": "الخيال", "documentaries": "الأفلام_الوثائقية", "mystery": "الغموض", "comedy": "فكاهة", "crime": "الجريمة", "drama": "دراما", "bollywood": "بوليوود", "kdrama": "الدراما_الكورية", "horror": "رعب", "romance": "رومانسي", "realitytv": "تلفزيون_الواقع", "action": "أكشن", "music": "الموسيقى", "blues": "البلوز", "classical": "كلاسيكي", "country": "كانتري", "desi": "ديسي", "edm": "موسيقى_إلكترونية_راقصة", "electronic": "إلكترونية", "folk": "فولك", "funk": "فانك", "hiphop": "هيب_هوب", "house": "هاوس", "indie": "إندي", "jazz": "الجاز", "kpop": "بوب_كوري", "latin": "اللاتينية", "metal": "ميتال", "pop": "البوب", "punk": "بانك", "rnb": "آر_أند_بي", "rap": "الراب", "reggae": "الريغي", "rock": "الروك", "techno": "التكنولوجيا", "travel": "السفر", "concerts": "حفلات_موسيقية", "festivals": "مهرجانات", "museums": "المتاحف", "standup": "الارتجال_الكوميدي", "theater": "مسر<PERSON>", "outdoors": "في_الهواء_الطلق", "gardening": "البستنة", "partying": "الحفلات", "gaming": "الألعاب", "boardgames": "ألعاب_اللوح", "dungeonsanddragons": "دانجنز_آند_دراغونز", "chess": "شطرنج", "fortnite": "فورتنايت", "leagueoflegends": "ليغ_اوف_لجندز", "starcraft": "ستاركرافت", "minecraft": "ماين_كرافت", "pokemon": "بوكيمون", "food": "الطعام", "baking": "الخ<PERSON>ز", "cooking": "الطبخ", "vegetarian": "نباتي", "vegan": "نباتي", "birds": "الطيور", "cats": "القطط", "dogs": "الكلاب", "fish": "سمك", "animals": "الحيوانات", "blacklivesmatter": "حياة_السود_مهمة", "environmentalism": "حماية_البيئة", "feminism": "الحركة_النسائية", "humanrights": "حقوق_الانسان", "lgbtqally": "حليفlgbtq", "stopasianhate": "وقف_كره_الآسيوين", "transally": "التحول", "volunteering": "التطوع", "sports": "الرياضات", "badminton": "تنس_الريشة", "baseball": "بيسبول", "basketball": "كرةسلة", "boxing": "ملاكمة", "cricket": "كريكيت", "cycling": "ركوب_الدراجات", "fitness": "اللياقة_البدنية", "football": "كرة_القدم", "golf": "جو<PERSON><PERSON>", "gym": "نادي_رياضي", "gymnastics": "الجمباز", "hockey": "الهوكي", "martialarts": "فنون_القتال", "netball": "كرة_الشبكة", "pilates": "بيلاتس", "pingpong": "بينج_بونج", "running": "جاري_التشغيل", "skateboarding": "التزلج", "skiing": "التزحلق", "snowboarding": "التزلج_على_الجليد", "surfing": "ركوب_الأمواج", "swimming": "سباحة", "tennis": "تنس", "volleyball": "الكرة_الطائرة", "weightlifting": "رفع_الاثقال", "yoga": "اليوجا", "scubadiving": "الغوص", "hiking": "رياضة_المشى", "capricorn": "برج_الجدي", "aquarius": "برج_الدلو", "pisces": "برج_الحوت", "aries": "برج_الحمل", "taurus": "برج_الثور", "gemini": "برج_الجوزاء", "cancer": "برج_سرطان", "leo": "برج_الأسد", "virgo": "برج_العذراء", "libra": "برج_الميزان", "scorpio": "برج_العقرب", "sagittarius": "برج_القوس", "shortterm": "قصيرالأجل", "casual": "عفوي", "longtermrelationship": "علاقة_جادة", "single": "عزابي", "polyamory": "تعددالعلاقات", "enm": "علاقة_مفتوحة", "lgbt": "لجبت", "lgbtq": "الجيبيتيكيو", "gay": "مثليالجنس", "lesbian": "سحاقية", "bisexual": "ثنائيالجنس", "pansexual": "بانسكسوال", "asexual": "لاجنسي", "reddeadredemption2": "ريدديدريديمبشن2", "dragonage": "دراغونإيدج", "assassinscreed": "أساسنز_كريد", "saintsrow": "ساينتسرو", "danganronpa": "دانغانرونبا", "deltarune": "ديلترون", "watchdogs": "مراقبون", "dislyte": "ديزلايت", "rougelikes": "روجلاي<PERSON>ز", "kingsquest": "ملك_المغامرة", "soulreaver": "نازع_الأرواح", "suikoden": "سويدكن", "subverse": "سابفيرس", "legendofspyro": "أسطورةسبايرو", "rouguelikes": "روغللايكس", "syberia": "سيبيريا", "rdr2": "رودر2", "spyrothedragon": "سبايروتدراجون", "dragonsdogma": "كلب_التنين", "sunsetoverdrive": "غروب_الشمس_المشوق", "arkham": "أركهام", "deusex": "ديوسكس", "fireemblemfates": "فايرإيمبلمفيتس", "yokaiwatch": "يوكايووتش", "rocksteady": "روكستيدي", "litrpg": "لعبة_الكتب", "haloinfinite": "هالوإنفينت", "guildwars": "حرو<PERSON>_الفرق", "openworld": "عالم_مفتوح", "heroesofthestorm": "أبطال_العاصفة", "cytus": "سايتس", "soulslike": "سوللايك", "dungeoncrawling": "استكشافالزنزانات", "jetsetradio": "جيست_راديو", "tribesofmidgard": "قبائلميتغارد", "planescape": "خطة_السفر", "lordsoftherealm2": "أسياد_المملكة2", "baldursgate": "بوابةبالدور", "colorvore": "كولورفور", "medabots": "ميدابوتس", "lodsoftherealm2": "كثيرمنالعالم2", "patfofexile": "باتفوفإكسيل", "immersivesims": "محاكيات_غمرية", "okage": "أوكاج", "juegoderol": "لعبة_الأدوار", "witcher": "ويتشر", "dishonored": "مُهان", "eldenring": "إلدنرينغ", "darksouls": "داركسولز", "kotor": "كوتور", "wynncraft": "وينكرافت", "witcher3": "ويتشر3", "fallout": "السقوط", "fallout3": "فولآوت3", "fallout4": "فالآوت4", "skyrim": "سكيريم", "elderscrolls": "أسطورة_القدم", "modding": "تعديل", "charactercreation": "إنشاء_شخصيات", "immersive": "ممتع", "falloutnewvegas": "فولاوتنيوفيغاس", "bioshock": "بايوشوك", "omori": "أوموري", "finalfantasyoldschool": "فfinalfantasyمدرسة_قديمة", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "فاinalfantasy", "finalfantasy14": "فاinalfantasy14", "finalfantasyxiv": "فاينالفانتسي14", "ff14": "اف14", "ffxiv": "ffxiv", "ff13": "افف13", "finalfantasymatoya": "فاينل_fantasy_ماتويّا", "lalafell": "للاфель", "dissidia": "ديسيديا", "finalfantasy7": "فاينالفانتسي7", "ff7": "اف7", "morbidmotivation": "تحفيز_مظلم", "finalfantasyvii": "فاينلفانتسي7", "ff8": "افف8", "otome": "أوتومي", "suckerforlove": "مد<PERSON><PERSON><PERSON><PERSON>", "otomegames": "ألعابأوتومي", "stardew": "ستارديو", "stardewvalley": "ستارديوفالي", "ocarinaoftime": "عوالم_الزمن", "yiikrpg": "ييكرpg", "vampirethemasquerade": "فامبايرذاماسكراد", "dimension20": "بعد20", "gaslands": "أراضي_الغاز", "pathfinder": "باثفايندر", "pathfinder2ndedition": "مستكشف_الطبعة_الثانية", "shadowrun": "ظل_الركض", "bloodontheclocktower": "دم_على_برج_الساعة", "finalfantasy15": "فإذاًفاينال_فانتازي_15", "finalfantasy11": "فاinalfantasy11", "finalfantasy8": "فاينالفانتاسي8", "ffxvi": "فيفا١٦", "lovenikki": "حبنيكي", "drakengard": "دراكينغارد", "gravityrush": "جرavityrush", "rpg": "أربيجي", "dota2": "دوتا2", "xenoblade": "زينوبلايد", "oneshot": "لقطةوحدة", "rpgmaker": "هاشتاجصانع_الألعاب", "osrs": "أوإسآرإس", "overlord": "الزعيم", "yourturntodie": "دوركتموت", "persona3": "برزون3", "rpghorror": "رعب_الـrpg", "elderscrollsonline": "إلدسكانسلون", "reka": "ريكا", "honkai": "هونكاي", "marauders": "المغيرون", "shinmegamitensei": "شينميغامي_تنسي", "epicseven": "إيبيك_seven", "rpgtext": "نص_الأربيجي", "genshin": "جنشن", "eso": "يلا", "diablo2": "ديابلو2", "diablo2lod": "ديابلو2لود", "morrowind": "موروند", "starwarskotor": "ستار_وارز_كوتور", "demonsouls": "أرواحالشياطين", "mu": "مو", "falloutshelter": "ملجأ_السقوط", "gurps": "غوربس", "darkestdungeon": "زنزانةالظلام", "eclipsephase": "مرحلة_الكسوف", "disgaea": "ديزغايا", "outerworlds": "عالم_خارجي", "arpg": "أرpg", "crpg": "أربيجي", "bindingofisaac": "ربط_إيزاك", "diabloimmortal": "ديابلوخالد", "dynastywarriors": "محاربين_السلالة", "skullgirls": "سكريلمات", "nightcity": "ليلة_المدينة", "hogwartslegacy": "إرث_هوغوورتس", "madnesscombat": "مجنون_القتال", "jaggedalliance2": "تحالف_م76", "neverwinter": "نيويروينتر", "road96": "طريق96", "vtmb": "فتمب", "chimeraland": "أرض_الخيال", "homm3": "هُمّ_3", "fe3h": "فِيهِم", "roguelikes": "روجيليك", "gothamknights": "جknightsغوثام", "forgottenrealms": "عالم_المنسيات", "dragonlance": "أسطوانة_التنين", "arenaofvalor": "ساحةالبطل", "ffxv": "فيفاكسفنشفت", "ornarpg": "أورناربيجي", "toontown": "تونتاون", "childoflight": "طفل_النور", "aq3d": "آك3دي", "mogeko": "موغيكم", "thedivision2": "الفرقة2", "lineage2": "لايناج2", "digimonworld": "عالم_ديجيمون", "monsterrancher": "مزرعةالوحوش", "ecopunk": "إيكوبانك", "vermintide2": "فيرمينتايد2", "xeno": "زاوي", "vulcanverse": "عالم_فولكان", "fracturedthrones": "كرسي_الملك_المكسور", "horizonforbiddenwest": "أفق_الممنوع_الغربي", "twewy": "تويواي", "shadowpunk": "ظلبانك", "finalfantasyxv": "فاinalfantasyxv", "everoasis": "يفيروأسيس", "hogwartmystery": "لغز_هوجورتس", "deltagreen": "دلتاجرين", "diablo": "ديابلو", "diablo3": "ديابلو3", "diablo4": "ديابلو4", "smite": "سمت", "lastepoch": "العصر_الأخير", "starfinder": "مكتشفالنجوم", "goldensun": "الشمس_الذهبية", "divinityoriginalsin": "خطايا_الأصلية_للإلهية", "bladesinthedark": "شفايف_في_الظلام", "twilight2000": "توأم2000", "sandevistan": "سانديفيستيان", "cyberpunk": "سايبربانك", "cyberpunk2077": "سايبربانك2077", "cyberpunkred": "سايبربانكريد", "dragonballxenoverse2": "دراغونبولزينوفيرس2", "fallenorder": "أمر_ساقط", "finalfantasyxii": "فاinal<PERSON>", "evillands": "أراضي_شريرة", "genshinimact": "جينشين_إيمكت", "aethyr": "أيثير", "devilsurvivor": "مُنجو_الشياطين", "oldschoolrunescape": "مدرسة_قديمة_رانسكيب", "finalfantasy10": "فاinalfantasy10", "anime5e": "أنمي5e", "divinity": "قداسة", "pf2": "بيف2", "farmrpg": "فارمرpg", "oldworldblues": "أزرق_العالم_القديم", "adventurequest": "هاشتاغمغامرة_البحث", "dagorhir": "داغورهير", "roleplayingames": "ألعاب_تقليدالشخصيات", "roleplayinggames": "ألعاب_تمثيل_الأدوار", "finalfantasy9": "فاينال_فانتاسي9", "sunhaven": "جنة_الشمس", "talesofsymphonia": "حكايات_سيمفونية", "honkaistarrail": "هونكايفيسترايل", "wolong": "ولونغ", "finalfantasy13": "فاينل_فانتسي13", "daggerfall": "داغرفال", "torncity": "مدينة_ممزقة", "myfarog": "مايفرغ", "sacredunderworld": "العالم_السري", "chainedechoes": "صدى_مربوط", "darksoul": "روح_ظلامية", "soulslikes": "ألعاب_سولز", "othercide": "أثير_الغموض", "mountandblade": "mountandblade", "inazumaeleven": "إنازوماإليفن", "acvalhalla": "أسياد_المقدّس", "chronotrigger": "كرونوترغر", "pillarsofeternity": "أعمدةالخلود", "palladiumrpg": "هاشتاجفانتازياالرعب", "rifts": "شقوق", "tibia": "تيبيا", "thedivision": "الانقسام", "hellocharlotte": "هلاشارلوت", "legendofdragoon": "أسطورةالتنين", "xenobladechronicles2": "زينوبلايدكرونيكلز2", "vampirolamascarada": "فامبيرولا_ماسكرادا", "octopathtraveler": "رحلة_ثماني_طرق", "afkarena": "أفكارنا", "werewolftheapocalypse": "ذئب_الهبوط", "aveyond": "أ<PERSON><PERSON><PERSON>وند", "littlewood": "ليتلود", "childrenofmorta": "أطفال_مورتا", "engineheart": "قلب_المكينة", "fable3": "حكاية3", "fablethelostchapter": "قصة_الفصل_المفقود", "hiveswap": "هايفسواب", "rollenspiel": "لعبة_الأدوار", "harpg": "ها<PERSON><PERSON><PERSON>", "baldursgates": "بوابة_بالدور", "edeneternal": "جنةأبدية", "finalfantasy16": "فاينال_فانتسي16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "فف15", "starfield": "نجوم_المجال", "oldschoolrevival": "عودة_المدرسة_القديمة", "finalfantasy12": "فاينلفانتسي12", "ff12": "فف12", "morkborg": "موكبورغ", "savageworlds": "عالم_الشقاوة", "diabloiv": "ديابلو4", "pve": "بيفي", "kingdomheart1": "مملكة_القلب1", "ff9": "فيدف9", "kingdomheart2": "مملكة_القلب2", "darknessdungeon": "زنزانةالظلام", "juegosrpg": "ألعابrpg", "kingdomhearts": "مملكة_القلوب", "kingdomheart3": "مملكة_القلب٣", "finalfantasy6": "فاينال_فانتسي_6", "ffvi": "فففي", "clanmalkavian": "عشيرةمالكاويان", "harvestella": "حصادلا", "gloomhaven": "غلامهافن", "wildhearts": "قلوب_مجنونة", "bastion": "قلعة", "drakarochdemoner": "دراكاروخديمونر", "skiesofarcadia": "سماءأركاديا", "shadowhearts": "قلوب_ظلال", "nierreplicant": "نييرريبيليكانت", "gnosia": "جنوستيا", "pennyblood": "دم_البنس", "breathoffire4": "فاير_بريث_٤", "mother3": "أم3", "cyberpunk2020": "سايبربانك2020", "falloutbos": "سقوط_بوس", "anothereden": "أناديرك", "roleplaygames": "ألعاب_التمثيل", "roleplaygame": "لعبة_تمثيل", "fabulaultima": "فابولاوتيما", "witchsheart": "قلب_الساحرة", "harrypottergame": "لعبة_هاري_بوتر", "pathfinderrpg": "بلاكباثفايندرآربيجي", "pathfinder2e": "باثفايندر2e", "vampirilamasquerade": "ماسكارا_مصاصي_الدماء", "dračák": "دراكاك", "spelljammer": "سبلجامر", "dragonageorigins": "دراغونإيجأوريجنز", "chronocross": "كروونوكروس", "cocttrpg": "كوكتيلآربيجي", "huntroyale": "هنترويال", "albertodyssey": "أوديسا_ألبرتو", "monsterhunterworld": "عالم_صائد_الوحوش", "bg3": "بغ3", "xenogear": "زينوجير", "temtem": "تيمتيم", "rpgforum": "منتدى_الألعاب_التمثيلية", "shadowheartscovenant": "نقابة_قلوب_الظل", "bladesoul": "بلادسول", "baldursgate3": "بالدرزغيت3", "kingdomcome": "مملكة_القدوم", "awplanet": "عالم_بوه", "theworldendswithyou": "تنتهي_العالم_معك", "dragalialost": "دراغاليا_المفقودة", "elderscroll": "تقدم_الكبار", "dyinglight2": "داينجلايت2", "finalfantasytactics": "فاينل_فانتاسي_تاكتكس", "grandia": "جرانديا", "darkheresy": "هرطقة_سوداء", "shoptitans": "متجري_الأبطال", "forumrpg": "منتدىrpg", "golarion": "غولاريون", "earthmagic": "سحرالأرض", "blackbook": "كُتابأسود", "skychildrenoflight": "أطفال_السماء_ضياء", "gryrpg": "جريربغ", "sacredgoldedition": "نسخة_الذهب_المقدسة", "castlecrashers": "كاسلكراشرز", "gothicgame": "هاشتاج_قصة_غوثيك", "scarletnexus": "ارتباط_قرمزي", "ghostwiretokyo": "هاشتاجغوستوايرطوكيو", "fallout2d20": "فولآوت2دي20", "gamingrpg": "ألعاب_آربيجي", "prophunt": "صائد_الأسرار", "starrails": "ستاررايلز", "cityofmist": "مدينةالضباب", "indierpg": "رول_العب_المستقلة", "pointandclick": "نقطةوانقر", "emilyisawaytoo": "إميلي_بعيدة_زيادة", "emilyisaway": "إميلي_راحت", "indivisible": "لانتجزأ", "freeside": "فريسايد", "epic7": "إبيك7", "ff7evercrisis": "أزمةff7للابد", "xenogears": "زينوجيرس", "megamitensei": "ميغاميتنسي", "symbaroum": "سمباروم", "postcyberpunk": "مابعد_السايبر", "deathroadtocanada": "طريقالموتلكندا", "palladium": "بالاديوم", "knightjdr": "فارس_جيد_نرد", "monsterhunter": "صائد_الوحوش", "fireemblem": "شعار_النار", "genshinimpact": "هاشتاججينشينإمباكت", "geosupremancy": "هيمنة_الجغرافيا", "persona5": "برسونا5", "ghostoftsushima": "شبح_تسوشيما", "sekiro": "سيكيرو", "monsterhunterrise": "هاشتاجصائد_الوحوش_رايز", "nier": "نير", "dothack": "دوذهاك", "ys": "يس", "souleater": "آكلالأرواح", "fatestaynight": "فاتمكنالليل", "etrianodyssey": "مغامرة_إيتريانو", "nonarygames": "ألعاب_نوناري", "tacticalrpg": "rpgاستراتيجي", "mahoyo": "ماهويو", "animegames": "ألعاب_الأنمي", "damganronpa": "دامغانرونبا", "granbluefantasy": "جرانبلوفانتسي", "godeater": "آكلة_الآلهة", "diluc": "ديلوق", "venti": "فينتي", "eternalsonata": "سوناتاالأبدي", "princessconnect": "اتصالي_الأميرة", "hexenzirkel": "دائرة_الساحرات", "cristales": "كريستالز", "vcs": "في_سي_اس", "pes": "ببسي", "pocketsage": "بوكيتساج", "valorant": "فالورانت", "valorante": "فالورانت", "valorantindian": "فالورانت_هندي", "dota": "دوتا", "madden": "مادن", "cdl": "سيديإل", "efootbal": "كرة_قدم_الكترونية", "nba2k": "أنبيإي2كي", "egames": "<PERSON><PERSON>_جيمز", "fifa23": "فيفا23", "wwe2k": "ووي2كي", "esport": "الرياضات_الإلكترونية", "mlg": "م<PERSON><PERSON>", "leagueofdreamers": "هاشتاج_رابطة_الحالمين", "fifa14": "فيفا14", "midlaner": "ميد<PERSON><PERSON><PERSON><PERSON>ر", "efootball": "إي_فوتبول", "dreamhack": "حلم_هاك", "gaimin": "غايمين", "overwatchleague": "دوري_أوفرواتش", "cybersport": "رياضة_الكترونية", "crazyraccoon": "راكون_مجنون", "test1test": "تست1تست", "fc24": "إفسي24", "riotgames": "هاشتاجرايوت_جيمز", "eracing": "إيراسينغ", "brasilgameshow": "برازيل_لعبة_عرض", "valorantcompetitive": "فالنترانت_التنافسي", "t3arena": "تعيش_تفاعل", "valorantbr": "فالورنت_برازيل", "csgo": "سياسغو", "tf2": "تي_اف_2", "portal2": "بوابة2", "halflife": "هافلايف", "left4dead": "ماتوا_وهم_يحتفلوا", "left4dead2": "ماتت_ولقيناها2", "valve": "صمام", "portal": "بوابة", "teamfortress2": "فريق_الحصن_2", "everlastingsummer": "صيف_سرمدي", "goatsimulator": "محاكاة_الماعز", "garrysmod": "جاريسمود", "freedomplanet": "كوكب_الحرية", "transformice": "ترانسفورمايس", "justshapesandbeats": "بسأشكالوضربات", "battlefield4": "ساحة_المعركة4", "nightinthewoods": "ليل_في_الغابة", "halflife2": "نصف_الحياة2", "hacknslash": "هاك_ونسلش", "deeprockgalactic": "ديبروكجالاكتيك", "riskofrain2": "خطرالمطر2", "metroidvanias": "ميترويدفنيا", "overcooked": "مسلوقزيادة", "interplanetary": "بينالكواكب", "helltaker": "هَيلتاكر", "inscryption": "إنسكرپشن", "7d2d": "٧د٢د", "deadcells": "خلايا_ميتة", "nierautomata": "نييرأوتوماتا", "gmod": "جي_مود", "dwarffortress": "حصنالأقزام", "foxhole": "فوركسهول", "stray": "تائها", "battlefield": "ساحة_المعركة", "battlefield1": "ساحة_المعركة1", "swtor": "سواتور", "fallout2": "فالوت2", "uboat": "يوبوت", "eyeb": "عيوني", "blackdesert": "الصحراء_السوداء", "tabletopsimulator": "محاكاة_طاولة", "partyhard": "حفلة_بجنون", "hardspaceshipbreaker": "كسر_سفن_فضاء_صعبة", "hades": "هايد<PERSON>", "gunsmith": "صانع_أسلحة", "okami": "أوكامي", "trappedwithjester": "محبوسينمعالمهرج", "dinkum": "دينكوم", "predecessor": "السابِق", "rainworld": "عالم_المطر", "cavesofqud": "كهوفقد", "colonysim": "محاكاة_الكولونيز", "noita": "نوطة", "dawnofwar": "فجر_الحرب", "minionmasters": "سادة_الخبز", "grimdawn": "جرمداون", "darkanddarker": "داكنوأغمق", "motox": "موتوكس", "blackmesa": "بلاكميسا", "soulworker": "عامل_الروح", "datingsims": "هاشتاجمحاكي_المواعدة", "yaga": "ياغا", "cubeescape": "هروب_الكيوب", "hifirush": "هاشتاجهيڤيراش", "svencoop": "سفينكووب", "newcity": "مدينة_جديدة", "citiesskylines": "أف<PERSON>_المدن", "defconheavy": "ديفكون_هيفي", "kenopsia": "كينوبسيا", "virtualkenopsia": "الكنوبسيا_الافتراضية", "snowrunner": "سنورانر", "libraryofruina": "مكتبة_الخراب", "l4d2": "لعبةboo2", "thenonarygames": "ألعاب_ثنوناري", "omegastrikers": "أوميغاسترايكرز", "wayfinder": "هاشتاجوايفندر", "kenabridgeofspirits": "كنابجسرالأرواح", "placidplasticduck": "بطةبلاستيكيةهادئة", "battlebit": "بتلبيت", "ultimatechickenhorse": "الدجاجةالأخيرةالخاسر", "dialtown": "مدينة_الإتصالات", "smileforme": "ابتسملي", "catnight": "ليلة_القطط", "supermeatboy": "سوبرميتبوي", "tinnybunny": "تينيبانّي", "cozygrove": "هاشتاجكوzyغروف", "doom": "دووم", "callofduty": "هاشتاجنداء_الواجب", "callofdutyww2": "نداء_الواجب_2", "rainbow6": "رينبو6", "apexlegends": "أساطير_الذروة", "cod": "كود", "borderlands": "حدودالأرض", "pubg": "ببجي", "callofdutyzombies": "هاشتاج_نداء_الواجب_زومبي", "apex": "أيبيكس", "r6siege": "حرب_الأخوة", "megamanx": "ميغامانإكس", "touhou": "توهُو", "farcry": "فاركراي", "farcrygames": "ألعاب_فاركراي", "paladins": "بالادينز", "earthdefenseforce": "قوةالدفاععنالأرض", "huntshowdown": "صراع_المطاردة", "ghostrecon": "شب<PERSON>_تحرير", "grandtheftauto5": "جراندثفتأوتو5", "warz": "وارز", "sierra117": "سييرا117", "dayzstandalone": "هاشتاجدايزستاندالون", "ultrakill": "ألتراكيل", "joinsquad": "انضم_إلى_الفرقة", "echovr": "echoفر", "discoelysium": "ديبكوإليسيوم", "insurgencysandstorm": "عاصفة_تمرد", "farcry3": "فاركراي3", "hotlinemiami": "هاشتاج_هوتلاين_ميامي", "maxpayne": "ماكسباين", "hitman3": "هتمن3", "r6s": "را6س", "rainbowsixsiege": "راينبو6ساج", "deathstranding": "موت_العبور", "b4b": "ب4ب", "codwarzone": "كودوارزون", "callofdutywarzone": "نداء_الواجب_منطقة_الحرب", "codzombies": "كودزومبيز", "mirrorsedge": "حافة_المرايا", "divisions2": "تقسيمات2", "killzone": "زونالموت", "helghan": "ه<PERSON><PERSON><PERSON>", "coldwarzombies": "حربالبردالزومبي", "metro2033": "مترو2033", "metalgear": "ميتالكير", "acecombat": "قتال_الطائرات", "crosscode": "ر<PERSON><PERSON>_عبر", "goldeneye007": "عین_الذهب007", "blackops2": "بلاكأوبس2", "sniperelite": "قناص_النخبة", "modernwarfare": "الحربالعصرية", "neonabyss": "نيون_أبيبس", "planetside2": "كوكب_2", "mechwarrior": "ميشوارير", "boarderlands": "أراضي_الحدود", "owerwatch": "أوفرواش", "rtype": "رايب", "dcsworld": "هاشتاغدي_سي_سورلد", "escapefromtarkov": "الهروب_من_تاركوف", "metalslug": "معدن_الحرب", "primalcarnage": "فوضى_الحيوانات", "worldofwarships": "عالمالسفنالحربية", "back4blood": "باك4بلود", "warframe": "وارفريم", "rainbow6siege": "راينبو6سج", "xcom": "إكسكوم", "hitman": "قاتل", "masseffect": "ماسإيفكت", "systemshock": "صدمة_النظام", "valkyriachronicles": "هاشتاجفالكييرياكرونيكلز", "specopstheline": "خط_العمليات_الخاصة", "killingfloor2": "قتال_الأرض2", "cavestory": "قصة_الكهف", "doometernal": "دووميتيرنال", "centuryageofashes": "قرن_عصر_الرماد", "farcry4": "فاركراي4", "gearsofwar": "أسلحة_الحرب", "mwo": "موا", "division2": "القسم2", "tythetasmaniantiger": "تيثا_النمر_التسماني", "generationzero": "جيل_الصفر", "enterthegungeon": "ادخل_الزنزانة", "jakanddaxter": "جاكانداكستر", "modernwarfare2": "حرب_عصرية2", "blackops1": "بلاكأوبس1", "sausageman": "رجل_السجق", "ratchetandclank": "راتشيتاندكلانك", "chexquest": "تشكسكويست", "thephantompain": "ألم_وهمي", "warface": "وجه_الحرب", "crossfire": "نيران_مستعرضة", "atomicheart": "قلب_نووي", "blackops3": "بلاكأوبس3", "vampiresurvivors": "نجاة_مصاصي_الدماء", "callofdutybatleroyale": "نداءالواجبباتلرويال", "moorhuhn": "مورهون", "freedoom": "حرية", "battlegrounds": "ميدان_المعركة", "frag": "فراغ", "tinytina": "تينيتينا", "gamepubg": "هاشتاج_لعبة_بابجي", "necromunda": "نيكروموندا", "metalgearsonsoflibert": "أبناءالحريةالمعدنية", "juegosfps": "ألعا<PERSON>_إطلاق_النار", "convertstrike": "تحويل_الضربة", "warzone2": "حرب_زون2", "shatterline": "شاترلاين", "blackopszombies": "بلاكأوبسزومبيز", "bloodymess": "فوضى_دموية", "republiccommando": "جمهورية_الكوماندو", "elitedangerous": "إيليتدانجرس", "soldat": "جن<PERSON>د", "groundbranch": "فرع_الأرض", "squad": "عصابة", "destiny1": "destiny1", "gamingfps": "ألعا<PERSON>_إطلاق_النيران", "redfall": "ريدفال", "pubggirl": "بنتببجي", "worldoftanksblitz": "عالم_دبابات_بليتز", "callofdutyblackops": "كولأوفديوتيبلاكأوبس", "enlisted": "مُجنّد", "farlight": "فلايت", "farcry5": "فايركراي5", "farcry6": "فاركراي6", "farlight84": "فارلايت84", "splatoon3": "سبلتون3", "armoredcore": "نواة_مدرعة", "pavlovvr": "بافلوفڤر", "xdefiant": "إكسديفiant", "tinytinaswonderlands": "عجائب_تيناتي_الصغيرة", "halo2": "هالو2", "payday2": "بايداي2", "cs16": "سيإس16", "pubgindonesia": "ببجيإندونيسيا", "pubgukraine": "ببجي_أوكرانيا", "pubgeu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgczsk": "ببجي_سي_زيك", "wotblitz": "ووتبلتس", "pubgromania": "ببجي_رومانيا", "empyrion": "إمبيريون", "pubgczech": "ببجيتشيك", "titanfall2": "هاشتاجتايتنفول2", "soapcod": "صابونكود", "ghostcod": "هاشتاجغاستكود", "csplay": "csplay", "unrealtournament": "بطولة_أسطورية", "callofdutydmz": "نداء_الواجب_dmz", "gamingcodm": "جيمينجكودم", "borderlands2": "حدود_الأراضي2", "counterstrike": "كounterstrike", "cs2": "سي_اس_2", "pistolwhip": "صفع_بالمسدس", "callofdutymw2": "نداء_الواجب_موار_2", "quakechampions": "أبطال_الزلزلة", "halo3": "هالو3", "halo": "مرحبا", "killingfloor": "قتل_الأرض", "destiny2": "مصير2", "exoprimal": "إكزوبرايمال", "splintercell": "خلية_تشرذم", "neonwhite": "نيونوايت", "remnant": "بقايا", "azurelane": "أزورلاين", "worldofwar": "عالم_الحرب", "gunvolt": "غونفولت", "returnal": "العودة", "halo4": "هالو4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "ظلالرجل", "quake2": "زلزلة2", "microvolts": "ميكروفولتس", "reddead": "ريد<PERSON>يد", "standoff2": "ستاندأوف2", "harekat": "حركة", "battlefield3": "ساحة_القتال3", "lostark": "لوستارك", "guildwars2": "حروب_النقابات2", "fallout76": "فاولآوت76", "elsword": "إلسورد", "seaofthieves": "بحراللصوص", "rust": "صدة", "conqueronline": "كن_الملك", "dauntless": "غيرمُرَتَعِب", "warships": "سفن_الحرب", "dayofdragons": "يوم_التنانين", "warthunder": "حرب_الرعد", "flightrising": "صعود_الرحلات", "recroom": "ريقرووم", "legendsofruneterra": "أساطيررونيتيرا", "pso2": "بسو2", "myster": "غا<PERSON>ض", "phantasystaronline2": "نجوم_الخيال_عبر_الإنترنت2", "maidenless": "بلاعروس", "ninokuni": "نينوكوني", "worldoftanks": "عالم_الدبابات", "crossout": "محو", "agario": "أجاريו", "secondlife": "حياة_ثانية", "aion": "أيّون", "toweroffantasy": "برج_الخيال", "netplay": "نتلاي", "everquest": "مهمة_الأبد", "metin2": "ميتنج2", "gtaonline": "جيتيإيه_أونلاين", "ninokunicrossworld": "نينوكونيكروسوورلد", "reddeadonline": "ريدديدأونلاين", "superanimalroyale": "سوبرأنيمالرويال", "ragnarokonline": "راجناروكأونلاين", "knightonline": "فارس_أونلاين", "gw2": "gw2", "tboi": "تبوئي", "thebindingofisaac": "رابطة_إيزاك", "dragonageinquisition": "عصر_التنين_التحقيق", "codevein": "كودفين", "eveonline": "إيفأونلاين", "clubpenguin": "نادي_البطاريق", "lotro": "لوترو", "wakfu": "واكفو", "scum": "قمامة", "newworld": "عالم_جديد", "blackdesertonline": "صحراء_سوداء_أونلاين", "multiplayer": "تعدداللاعبين", "pirate101": "قرصان101", "honorofkings": "تكريم_الملوك", "fivem": "فيفم", "starwarsbattlefront": "نجوم_الحرب_ساحة_المعركة", "karmaland": "كارمالاند", "ssbu": "إسإسبييو", "starwarsbattlefront2": "حربالنجوم_باتلفront2", "phigros": "فيغروس", "mmo": "ممو", "pokemmo": "بوكيممو", "ponytown": "مدينة_البوني", "3dchat": "دردشة3d", "nostale": "نوستال", "tauriwow": "توريواو", "wowclassic": "واو_كلاسيك", "worldofwarcraft": "عالمووركرافت", "warcraft": "وركرافت", "wotlk": "واتلكلاس", "runescape": "رانسكيب", "neopets": "نيوبتس", "moba": "موبَا", "habbo": "هابّو", "archeage": "أركايج", "toramonline": "تورايمأونلاين", "mabinogi": "مابينوجي", "ashesofcreation": "رماد_الإبداع", "riotmmo": "موجة_الفوضى", "silkroad": "طريق_الحرير", "spiralknights": "فرسان_الدوامة", "mulegend": "أسطورةميو", "startrekonline": "ستار_تريك_أونلاين", "vindictus": "فينديكتس", "albiononline": "ألباينأونلاين", "bladeandsoul": "شفرة_وكsoul", "evony": "إيفوني", "dragonsprophet": "نبع_التنانين", "grymmo": "جرّيمو", "warmane": "ورمانة", "multijugador": "مُتَعَدِّد_اللاعبين", "angelsonline": "ملاكعلىالإنترنت", "lunia": "لونية", "luniaz": "لونياذ", "idleon": "إيديلون", "dcuniverseonline": "عالم_دي_سي_أونلاين", "growtopia": "غروتوبيا", "starwarsoldrepublic": "ستارورزالجمهوريةالقديمة", "grandfantasia": "جراندفنتازيا", "blueprotocol": "بروتوكول_الأزرق", "perfectworld": "عالم_مثالي", "riseonline": "ارتق_أونلاين", "corepunk": "كوربانك", "adventurequestworlds": "مغامرات_عالم_الاستكشاف", "flyforfun": "اطلق_مرحك", "animaljam": "حيوانات_جام", "kingdomofloathing": "مملكة_الكره", "cityofheroes": "مدينةالأبطال", "mortalkombat": "مortalcombat", "streetfighter": "مقاتلالشارع", "hollowknight": "هاشتاج_هولوكايت", "metalgearsolid": "معدن_الجرس_صلب", "forhonor": "منأجلالشرف", "tekken": "تيكن", "guiltygear": "قاتل_الجرح", "xenoverse2": "زينوفرس2", "fgc": "فريق_بوا", "streetfighter6": "ستريت_fايتير6", "multiversus": "مولتيفيرسوس", "smashbrosultimate": "سمشبروازالتيميت", "soulcalibur": "سولكاليفور", "brawlhalla": "بروهللا", "virtuafighter": "مقاتل_افتراضي", "streetsofrage": "شوارع_راغي", "mkdeadlyalliance": "تحالف_الخطر_المدمر", "nomoreheroes": "لا_أبطال_بعد", "mhr": "مهر", "mortalkombat12": "مورتالكومبات12", "thekingoffighters": "ملكالمقاتلين", "likeadragon": "مثل_التنين", "retrofightinggames": "ألعاب_القتال_القديمة", "blasphemous": "كافر", "rivalsofaether": "أعداء_الأثير", "persona4arena": "شخصية4الحلبة", "marvelvscapcom": "مارفلvsكابكوم", "supersmash": "سوبرسمش", "mugen": "موغن", "warofthemonsters": "حربالوحوش", "jogosdeluta": "ألعاب_قتالية", "cyberbots": "هاشتاجسايبربوتس", "armoredwarriors": "محاربين_مدرعين", "finalfight": "المعركة_النهائية", "poweredgear": "معداتالطاقة", "beatemup": "بوضربه", "blazblue": "بلازبلو", "mortalkombat9": "مورتال_كُمْبَات9", "fightgames": "ألعا<PERSON>_قتال", "killerinstinct": "غرائز_القتل", "kingoffigthers": "ملك_المقاتلين", "ghostrunner": "عداء_الأشباح", "chivalry2": "فروسية2", "demonssouls": "أرواحالشياطين", "blazbluecrosstag": "بلازبلوكرسترغمة", "blazbluextagbattle": "بلازبلوتكتاجباتل", "blazbluextag": "بلازبلوإكستاغ", "guiltygearstrive": "غيلتيجيرسترايف", "hollowknightsequel": "تسلسل_هالونايت", "hollowknightsilksong": "هولونايتسيلكسونغ", "silksonghornet": "سيليكسونغهورنت", "silksonggame": "لعبة_سيلكسانغ", "silksongnews": "أخبار_سيلكسونغ", "silksong": "سيلكسونغ", "undernight": "أندرنايت", "typelumina": "تايبلوماينا", "evolutiontournament": "بطولة_التطور", "evomoment": "لحظة_إيفو", "lollipopchainsaw": "عصا_الحلوى_المتأرجحة", "dragonballfighterz": "دراغونبولفايتزرز", "talesofberseria": "قصص_بيرسيريا", "bloodborne": "دموي", "horizon": "<PERSON><PERSON><PERSON>", "pathofexile": "طريق_المنفى", "slimerancher": "سلايمرانشر", "crashbandicoot": "كراش_بانديكوت", "bloodbourne": "بلودبورن", "uncharted": "غيرمستكشف", "horizonzerodawn": "هارزنزيروdawn", "ps4": "بلايستيشن4", "ps5": "بلايستيشن5", "spyro": "سبايرو", "playstationplus": "بلايستيشنبلس", "lastofus": "آخرنا", "infamous": "سيء_السمعة", "playstationbuddies": "أصدقاء_بلايستيشن", "ps1": "بي_اس_1", "oddworld": "عالم_غريب", "playstation5": "بلايستيشن5", "slycooper": "سلاي_كوبر", "psp": "بيإسبي", "rabbids": "ر<PERSON><PERSON><PERSON><PERSON><PERSON>", "splitgate": "سبيليتغيت", "persona4": "شخصية4", "hellletloose": "هلاّتلاوز", "gta4": "جيتيإيه4", "gta": "قراند_ثفت_أوتو", "roguecompany": "شركة_متمردة", "aisomniumfiles": "ملفات_أيسومنيوم", "gta5": "جيتيإيه5", "gtasanandreas": "جيتيأيسانأندرياس", "godofwar": "إلهالحرب", "gris": "غريس", "trove": "كنز", "detroitbecomehuman": "ديترويت_تصبح_إنسانًا", "beatsaber": "بيتسابر", "rimworld": "عالم_ريم", "stellaris": "ستيلاريس", "ps3": "بلايستيشن3", "untildawn": "حتىالفجر", "touristtrophy": "هاشتاج_تروفي_السياحة", "lspdfr": "لعبة_الأمن_المدني", "shadowofthecolossus": "ظلالعملاق", "crashteamracing": "فريق_سباقات_الكراش", "fivepd": "خمسةpd", "tekken7": "تيكن7", "devilmaycry": "الشيطان_قد_يصرخ", "devilmaycry3": "شيطان_قد_يبكي_3", "devilmaycry5": "ديابلومايكراي5", "ufc4": "يوفيسي4", "playingstation": "بلايستيشن", "samuraiwarriors": "محاربيالساموراي", "psvr2": "بيإسفيآر2", "thelastguardian": "الحارس_الأخير", "soulblade": "شفرة_الروح", "gta5rp": "جيتيأي5آرپي", "gtav": "جيتيإيه٥", "playstation3": "بلايستيشن3", "manhunt": "مطاردةرجال", "gtavicecity": "جي_تي_أيه_فايس_سيتي", "wwe2k23": "دبليو_دبليو_اي_2ك23", "shadowhearts2covenant": "ظل_القلوب_2_العهد", "pcsx2": "بيسيكس2", "lastguardian": "الحارس_الأخير", "xboxone": "اكسبوكسون", "forza": "فورزا", "cd": "سيدي", "gamepass": "جيمباس", "armello": "أرميلو", "partyanimal": "حيوانات_الحفلات", "warharmmer40k": "وارهاممر40ك", "fightnightchampion": "ليلة_المقاتل_بطل", "psychonauts": "سايكونوتس", "mhw": "مهوّس", "princeofpersia": "أمير_فارسي", "theelderscrollsskyrim": "قصة_القدامى_سكايريم", "pantarhei": "بانتارهاي", "theelderscrolls": "تختيم_أسطورة_الشيوخ", "gxbox": "جيكسبوك", "battlefront": "جبهةالمعركة", "dontstarvetogether": "لاتجوعون_مع_بُو", "ori": "أوري", "spelunky": "سبيلانكي", "xbox1": "اكسبوكس1", "xbox360": "إكس_بوكس_360", "starbound": "نحوالنجوم", "xboxonex": "إكس_بوكس_ون_x", "forzahorizon5": "فورزا_هورايزن5", "skate3": "سكات3", "houseflipper": "هاشتاج_تغيير_المنازل", "americanmcgeesalice": "الأمريكية_ماغي_أليس", "xboxs": "هاشتاجإكس_بوكس", "xboxseriesx": "إكس_بوكس_سيريز_x", "xboxseries": "هاشتاجاكسبوكسسيريز", "r6xbox": "آر6إكسبوكس", "leagueofkingdoms": "مملكة_البطولات", "fable2": "فابل2", "xboxgamepass": "باقة_ألعاب_إكس_بوكس", "undertale": "أندرتيل", "trashtv": "تلفزيونقمامة", "skycotl": "سماء_كوتل", "erica": "إيريكا", "ancestory": "أجدادنا", "cuphead": "كُوبْهَيد", "littlemisfortune": "مساكيني", "sallyface": "سالي_فيس", "franbow": "فرانبوا", "monsterprom": "مونستر_بروم", "projectzomboid": "بروجكتزومبויד", "ddlc": "ديديألسي", "motos": "موتوس", "outerwilds": "خارجي_البراري", "pbbg": "بيبيجي", "anshi": "انشي", "cultofthelamb": "cult_حمل", "duckgame": "لعبة_البطة", "thestanleyparable": "قصة_ستانلي", "towerunite": "برج_الاتحاد", "occulto": "خ<PERSON>ي", "longdrive": "طريق_طويل", "satisfactory": "مرتاح", "pluviophile": "عاشق_المطر", "underearth": "تحت_الأرض", "assettocorsa": "أستيتوكورسا", "geometrydash": "داش_الهندسة", "kerbal": "كيربال", "kerbalspaceprogram": "برنامج_فضاء_كيربل", "kenshi": "كنشي", "spiritfarer": "روح_المرافق", "darkdome": "قبة_الظلام", "pizzatower": "برج_البيتزا", "indiegame": "لعبة_مستقلة", "itchio": "إتشيهو", "golfit": "جولفيت", "truthordare": "صدق_أو_تحدي", "game": "لعبة", "rockpaperscissors": "حجرورقةمقص", "trampoline": "ترامبولين", "hulahoop": "هولاهوب", "dare": "تحدي", "scavengerhunt": "صيد_الكنوز", "yardgames": "ألعاب_الفناء", "pickanumber": "اختاررقم", "trueorfalse": "حقيقيولاكذب", "beerpong": "كرةالجعة", "dicegoblin": "نردغوبلن", "cosygames": "ألعاب_مريحة", "datinggames": "ألعاب_المواعدة", "freegame": "لعبة_مجانية", "drinkinggames": "ألعاب_شرب", "sodoku": "سدكو", "juegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjong": "ماجونغ", "jeux": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulationgames": "ألعاب_محاكاة", "wordgames": "ألعاب_الكلمات", "jeuxdemots": "ألعاب_الكلمات", "juegosdepalabras": "ألعاب_كلمات", "letsplayagame": "يللانلعبلعبة", "boredgames": "ألعاب_مملة", "oyun": "أويون", "interactivegames": "ألعاب_تفاعلية", "amtgard": "أم<PERSON>غارد", "staringcontests": "مسابقات_تحديق", "spiele": "لعب", "giochi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geoguessr": "جيوغيسر", "iphonegames": "ألعاب_آيفون", "boogames": "بوياتالألعاب", "cranegame": "لعبة_الجراندي", "hideandseek": "اختبئوابحث", "hopscotch": "تيلة", "arcadegames": "ألعاب_الأركيد", "yakuzagames": "هاشتاجيوكوزاجيمز", "classicgame": "لعبة_كلاسيكية", "mindgames": "ألعاب_العقل", "guessthelyric": "تخمين_الكلمات", "galagames": "ألعا<PERSON>_غالا", "romancegame": "لعبة_الرومانسية", "yanderegames": "ألعا<PERSON>_ياندره", "tonguetwisters": "تحدياتاللسان", "4xgames": "ألعاب4x", "gamefi": "جيمفاي", "jeuxdarcades": "ألعاب_الأركيد", "tabletopgames": "ألعاب_المائدة", "metroidvania": "ميترويدفانيا", "games90": "ألعاب90", "idareyou": "أتحداك", "mozaa": "موزا", "fumitouedagames": "فوميتويداغيمز", "racinggames": "ألعاب_السباقات", "ets2": "ets2", "realvsfake": "الحقيقي_ضد_المزيف", "playgames": "ألعاب_العب", "gameonline": "لعبة_أونلاين", "onlinegames": "ألعاب_أونلاين", "jogosonline": "ألعاب_أونلاين", "writtenroleplay": "كتابة_دور_لعب", "playaballgame": "لعب_لعبة_الكرة", "pictionary": "بيكشنري", "coopgames": "ألعاب_تعاونية", "jenga": "جينغا", "wiigames": "وييغيمز", "highscore": "نقطة_عالية", "jeuxderôles": "ألعاب_الأدوار", "burgergames": "ألعاب_البرغر", "kidsgames": "ألعاب_الأطفال", "skeeball": "سكايبول", "nfsmwblackedition": "نفسموهبلاكإيديشن", "jeuconcour": "لعبةالمسابقة", "tcgplayer": "تجارة_بطاقات", "juegodepreguntas": "لعبةالأسئلة", "gioco": "لعبة", "managementgame": "مباراة_الإدارة", "hiddenobjectgame": "لعبة_الكائنات_المخفيّة", "roolipelit": "لعب_الروحي", "formula1game": "لعبة_فورمولا1", "citybuilder": "مبني_المدينة", "drdriving": "درايفنج", "juegosarcade": "ألعا<PERSON>_أركيد", "memorygames": "ألعاب_الذاكرة", "vulkan": "فولكان", "actiongames": "ألعاب_أكشن", "blowgames": "ألعاب_بلوو", "pinballmachines": "ماكينات_البينبول", "oldgames": "ألعاب_قديمة", "couchcoop": "كوشكوب", "perguntados": "أسئلة", "gameo": "جيمو", "lasergame": "لعبة_الليزر", "imessagegames": "ألعاب_إيميسيج", "idlegames": "ألعاب_تسلية", "fillintheblank": "املأ_الفراغ", "jeuxpc": "ألعاب_الكمبيوتر", "rétrogaming": "ريتوغايمينغ", "logicgames": "ألعاب_منطق", "japangame": "لعبة_اليابان", "rizzupgame": "لعبة_المهارة", "subwaysurf": "سوبوايسورف", "jeuxdecelebrite": "ألعاب_المشاهير", "exitgames": "ألعاب_الخروج", "5vs5": "٥vs٥", "rolgame": "لعبة_الروبلي", "dashiegames": "داشيوألعاب", "gameandkill": "لعب_وقتل", "traditionalgames": "ألعاب_تقليدية", "kniffel": "كنيفل", "gamefps": "لعبةfps", "textbasedgames": "ألعاب_نصية", "gryparagrafowe": "جراباراڤي", "fantacalcio": "فانتكالطيو", "retrospel": "ريتروسبل", "thiefgame": "لعبة_اللص", "lawngames": "لعب_عالجرس", "fliperama": "فليبراما", "heroclix": "هيروكلكس", "tablesoccer": "كرة_الطاولة", "tischfußball": "تيشفوسبال", "spieleabende": "ليالي_الألعاب", "jeuxforum": "هاشتاغألعابفورم", "casualgames": "ألعاب_كاجوال", "fléchettes": "سهام", "escapegames": "ألعاب_الهروب", "thiefgameseries": "سلسلة_ألعاب_ثيف", "cranegames": "ألعاب_الجراندي", "játék": "لعبة", "bordfodbold": "بوردفوتبول", "jogosorte": "لعبة_الحظ", "mage": "ماجي", "cargames": "ألعاب_السيارات", "onlineplay": "لعب_أونلاين", "mölkky": "موكلي", "gamenights": "ليالي_الألعاب", "pursebingos": "بغوز_الشنطة", "randomizer": "هاشتاج_عشوائي", "msx": "مسك", "anagrammi": "أنغرامي", "gamespc": "ألعاب_بيسي", "socialdeductiongames": "ألعاب_الاستنتاج_الاجتماعي", "dominos": "دومينوز", "domino": "دومينو", "isometricgames": "ألعاب_متساوية", "goodoldgames": "ألعاب_الأيام_الخير", "truthanddare": "الحقيقة_والتحدي", "mahjongriichi": "ماجونغرييتشي", "scavengerhunts": "صيد_الكنوز", "jeuxvirtuel": "ألعاب_افتراضية", "romhack": "رومهاك", "f2pgamer": "لاعبين_عالسريع", "free2play": "مجاناً_للعب", "fantasygame": "لعبة_الخيال", "gryonline": "جرى_اونلاين", "driftgame": "لعبة_الدريفت", "gamesotomes": "ألعا<PERSON>_إلى_القلوب", "halotvseriesandgames": "هالو_مسلسلات_وألعاب", "mushroomoasis": "مشروم_واحة", "anythingwithanengine": "أيشيءبيهمحرك", "everywheregame": "لعبة_في_كل_مكان", "swordandsorcery": "سيفوسحر", "goodgamegiving": "لعبة_كويسة_للتبرع", "jugamos": "نلعب", "lab8games": "لعب8ألعاب", "labzerogames": "لابزيرو_جيمز", "grykomputerowe": "ألعاب_كمبيوتر", "virgogami": "فيرغوجامي", "gogame": "العبوا", "jeuxderythmes": "ألعاب_الإيقاع", "minaturegames": "ألعاب_مصغرة", "ridgeracertype4": "رايدجرائسرنوع4", "selflovegaming": "حب_الذات_لعبة", "gamemodding": "تعديل_الألعاب", "crimegames": "ألعاب_جرائم", "dobbelspellen": "دوبلسبيلن", "spelletjes": "لعبات", "spacenerf": "سبايسنيرف", "charades": "تحدي_تخمين", "singleplayer": "لاعب_وحيد", "coopgame": "لعبة_تعاون", "gamed": "لعبت", "forzahorizon": "فورزا_هورايزن", "nexus": "نكسس", "geforcenow": "اجهز_الآن", "maingame": "اللعبةالرئيسية", "kingdiscord": "ملكديستورد", "scrabble": "سكرابل", "schach": "شخاخ", "shogi": "شوجي", "dandd": "د<PERSON><PERSON><PERSON>", "catan": "كاتان", "ludo": "لودو", "backgammon": "طاولة_زهر", "onitama": "أونيتاما", "pandemiclegacy": "إرث_الجائحة", "camelup": "جمل_فوق", "monopolygame": "لعبة_المونوبولي", "brettspiele": "ألعاب_طاولة", "bordspellen": "ألعاب_الطاولة", "boardgame": "لعبة_لوحية", "sällskapspel": "لعبة_مسلية", "planszowe": "بلاينززوو", "risiko": "ريسكو", "permainanpapan": "ألعاب_الطاولة", "zombicide": "زومبيسايد", "tabletop": "لعبة_الطاولة", "baduk": "بادوك", "bloodbowl": "كرة_الدم", "cluedo": "كلودو", "xiangqi": "شطرنجصيني", "senet": "سينيت", "goboardgame": "بوبوردجيم", "connectfour": "تواصل_أربعة", "heroquest": "هيروكوست", "giochidatavolo": "ألعاب_طاولة", "farkle": "فاركلي", "carrom": "كاروم", "tablegames": "ألعاب_الطاولة", "dicegames": "ألعا<PERSON>_النرد", "yatzy": "يعتزي", "parchis": "بارتشيس", "jogodetabuleiro": "جوجو_دتابوليرو", "jocuridesocietate": "لعبة_الناس", "deskgames": "ألعاب_المكتب", "alpharius": "الفارياس", "masaoyunları": "مساويونlar", "marvelcrisisprotocol": "بروتوكول_أزمة_مارفل", "cosmicencounter": "مواجهة_كونية", "creationludique": "إبداع_لذيذ", "tabletoproleplay": "لعبة_طاولة", "cardboardgames": "ألعاب_كرتونية", "eldritchhorror": "رعب_غريب", "switchboardgames": "ألعاب_السويتش", "infinitythegame": "إنفينيتي_ذا_جيم", "kingdomdeath": "مملكةالموت", "yahtzee": "يااتسي", "chutesandladders": "مراجيحوسلالم", "társas": "تارساس", "juegodemesa": "لعبة_المائدة", "planszówki": "خطط_بلاحدود", "rednecklife": "حياة_البوратين", "boardom": "ملل", "applestoapples": "تفاحلتفاح", "jeudesociété": "هاشتاجلعبة_مجتمعية", "gameboard": "لوحة_اللعبة", "dominó": "دومينو", "kalah": "كلاها", "crokinole": "كروكينول", "jeuxdesociétés": "ألعاب_الطاولة", "twilightimperium": "تويلايتإمبريوم", "horseopoly": "حصانوبولي", "deckbuilding": "بناء_المجموعات", "mansionsofmadness": "قصور_الجنون", "gomoku": "جوموكو", "giochidatavola": "ألعاب_عالم_المائدة", "shadowsofbrimstone": "ظلال_الجحيم", "kingoftokyo": "ملكطوكيو", "warcaby": "واركابي", "táblajátékok": "ألعاب_الألواح", "battleship": "باخرةالمعركة", "tickettoride": "تذكرةللركوب", "deskovehry": "ديسكوويهري", "catán": "كاتان", "subbuteo": "سبوتيو", "jeuxdeplateau": "ألعاب_اللوح", "stolníhry": "ألعاب_المائدة", "xiángqi": "شغف_الشطرنج", "jeuxsociete": "ألعاب_الطاولة", "gesellschaftsspiele": "ألعاب_مجتمعية", "starwarslegion": "فيلمالحربالنجوم", "gochess": "ايلعب_شساشة", "weiqi": "ويكي", "jeuxdesocietes": "ألعاب_مجتمعية", "terraria": "تيرايريا", "dsmp": "ديإسإمبي", "warzone": "منطقة_الحرب", "arksurvivalevolved": "ارك_البقاء_على_قيد_الحياة", "dayz": "ديز", "identityv": "هوية_في", "theisle": "الجزيرة", "thelastofus": "آخرنا", "nomanssky": "لاسماءفيالسماء", "subnautica": "سابنوتيكا", "tombraider": "تومبرايدر", "callofcthulhu": "نداءكتولهو", "bendyandtheinkmachine": "بنديوالآلةالحبرية", "conanexiles": "كونان_منفى", "eft": "تحدي_boo", "amongus": "بيننا", "eco": "إيكو", "monkeyisland": "جزيرةالمينكي", "valheim": "فالهايم", "planetcrafter": "كوكب_مبدع", "daysgone": "أيامgone", "fobia": "فوبيا", "witchit": "ويتشإت", "pathologic": "مريض", "zomboid": "زومبوايد", "northgard": "نورثغارد", "7dtd": "٧ديتيدي", "thelongdark": "الظلامالطويل", "ark": "ارك", "grounded": "مؤسّسين", "stateofdecay2": "حالة_الانحلال2", "vrising": "فوق_الواقع", "madfather": "أب_مجنون", "dontstarve": "ما_تجوع", "eternalreturn": "عودةأبدية", "pathoftitans": "طريق_العمالقة", "frictionalgames": "ألعب_فريكنال游戏", "hexen": "ه<PERSON><PERSON>ن", "theevilwithin": "الشر_في_الداخل", "realrac": "رايد_الريال", "thebackrooms": "الغرف_الخلفية", "backrooms": "غرف_خلفية", "empiressmp": "إمبراطورية_سمب", "blockstory": "قصة_بلوك", "thequarry": "المقلع", "tlou": "تلعب", "dyinglight": "ضوءالموت", "thewalkingdeadgame": "لعبة_الماشيين_الأموات", "wehappyfew": "نحن_سعداء_قليلًا", "riseofempires": "صعود_الإمبراطوريات", "stateofsurvivalgame": "حالة_بقاء_لعبة", "vintagestory": "قصة_عتيقة", "arksurvival": "بقاء_الآرك", "barotrauma": "باروتروما", "breathedge": "بريذج", "alisa": "أليسا", "westlendsurvival": "غرب_بقاء", "beastsofbermuda": "وحوشبرمودا", "frostpunk": "فروستبانك", "darkwood": "خشب_داكن", "survivalhorror": "نجاةرعب", "residentevil": "شرالمقيمين", "residentevil2": "ساكنالشر2", "residentevil4": "رايزيدنتإيفل4", "residentevil3": "رايزدإيفل3", "voidtrain": "قطار_العدم", "lifeaftergame": "الحياةبعداللعبة", "survivalgames": "ألعاب_البقاء", "sillenthill": "هيل_السكون", "thiswarofmine": "هذهالحربلـmine", "scpfoundation": "مؤسسة_scp", "greenproject": "مشروع_الأخضر", "kuon": "كون", "cryoffear": "ابكِ_من_الخوف", "raft": "طوف", "rdo": "ردو", "greenhell": "الجحيم_الأخضر", "residentevil5": "رايزيدنتإيفل5", "deadpoly": "ديادبولي", "residentevil8": "رازديفل8", "onironauta": "أونييوناوتا", "granny": "دادة", "littlenightmares2": "كابوس_صغير_2", "signalis": "سيجناليس", "amandatheadventurer": "أماندا_المستكشفة", "sonsoftheforest": "أبناء_الغابة", "rustvideogame": "لعبة_راست", "outlasttrials": "تجارب_التحمل", "alienisolation": "عزل_غريب", "undawn": "غيرمعدن", "7day2die": "سبعةأيامتموت", "sunlesssea": "بحر_بدون_شمس", "sopravvivenza": "بقاء", "propnight": "ليلة_الألعاب", "deadisland2": "جزيرة_الموت2", "ikemensengoku": "إيكيمنسنغوكو", "ikemenvampire": "اكره_مصاصين_الدماء", "deathverse": "عالم_الموت", "cataclysmdarkdays": "أيام_ظلام_الكوارث", "soma": "سومة", "fearandhunger": "رعبوجوع", "stalkercieńczarnobyla": "ملاحق_في_الظل", "lifeafter": "الحياةبعد", "ageofdarkness": "عصرالظلام", "clocktower3": "برجالساعة3", "aloneinthedark": "وحدكفيالظلام", "medievaldynasty": "عصر_الوسطى", "projectnimbusgame": "مشروع_نيبموس_لعبة", "eternights": "إيتيرنايتس", "craftopia": "كرافتوبيا", "theoutlasttrials": "تجارب_ذو_النجاة", "bunker": "بنكَر", "worlddomination": "سيطرة_عالمية", "rocketleague": "هاشتاج_روكيت_ليغ", "tft": "تفت", "officioassassinorum": "مغتالين_المكاتب", "necron": "نيكرون", "wfrp": "وافرp", "dwarfslayer": "قاتلالأقزام", "warhammer40kcrush": "حب_وارهامر40k", "wh40": "wh40", "warhammer40klove": "حبوارهامر٤٠ك", "warhammer40klore": "ورمهر40كليجطة", "warhammer": "وارهاممر", "warhammer30k": "حرب_المطرقة30k", "warhammer40k": "وارهاممر40ك", "warhammer40kdarktide": "وارهاممر40كداركتايد", "totalwarhammer3": "توتالوارهمر3", "temploculexus": "تمبلوكلكسوس", "vindicare": "فينديكير", "ilovesororitas": "أنا_عاشق_سوريتاس", "ilovevindicare": "أ<PERSON><PERSON>vndicare", "iloveassasinorum": "أحب_أساسينورم", "templovenenum": "تمبلوفينيم", "templocallidus": "تمبلوكاليدوس", "templomaerorus": "تمبلوميرورس", "templovanus": "تمبلوفانوس", "oficioasesinorum": "مهنة_القاتلين", "tarkov": "تاركوف", "40k": "٤٠ألف", "tetris": "تتريس", "lioden": "ليودين", "ageofempires": "عصرالإمبراطوريات", "aoe2": "إيه_أو_إي_2", "hoi4": "هوي4", "warhammerageofsigmar": "حرب_هامر_عصر_سيغمار", "civilizationv": "ح<PERSON><PERSON><PERSON><PERSON>v", "ittakestwo": "يتطلباثنين", "wingspan": "جن<PERSON><PERSON>_الطير", "terraformingmars": "تحويل_المريخ", "heroesofmightandmagic": "أبطالالقوةوالسحر", "btd6": "بتي_دي6", "supremecommander": "القائدالمطلق", "ageofmythology": "عصر_الأساطير", "args": "<PERSON>ر<PERSON><PERSON>", "rime": "ريمي", "planetzoo": "كوكب_حديقة_الحيوانات", "outpost2": "بوابة2", "banished": "مطرود", "caesar3": "قيصر3", "redalert": "إنذار_أحمر", "civilization6": "civilization6", "warcraft2": "ووركرافت2", "commandandconquer": "أوامروانتصارات", "warcraft3": "هاشتاجووركرافت3", "eternalwar": "الحربالأبدية", "strategygames": "ألعاب_استراتيجية", "anno2070": "أنو2070", "civilizationgame": "لعبة_الحضارة", "civilization4": "حضارة4", "factorio": "فاكتوريوا", "dungeondraft": "دنجندرافت", "spore": "سبور", "totalwar": "حرب_شاملة", "travian": "ترافيان", "forts": "حصون", "goodcompany": "رفقة_جميلة", "civ": "مدني", "homeworld": "عالم_الدار", "heidentum": "الهيدنتوم", "aoe4": "أوي٤", "hnefatafl": "هنيفاتافل", "fasterthanlight": "أسرعمنالضوء", "forthekings": "لملوك", "realtimestrategy": "استراتيجيةالوقتالحقيقي", "starctaft": "ستاركتافت", "sidmeierscivilization": "هاشتاجسيدمايرز_цивилизация", "kingdomtwocrowns": "مملكةتاجين", "eu4": "أوروبا4", "vainglory": "غرور", "ww40k": "ويكي40ك", "godhood": "الألوهية", "anno": "أنا_بوقتي", "battletech": "باتلتك", "malifaux": "ماليفو", "w40k": "دبليو40ك", "hattrick": "هاتريك", "davesfunalgebraclass": "درس_الجبر_الممتع_مع_ديف", "plagueinc": "بلاقيإنك", "theorycraft": "نظرية_التخيل", "mesbg": "مسابقة", "civilization3": "حضارة3", "4inarow": "٤فيصفواحد", "crusaderkings3": "فُتُوحَات_الملوك3", "heroes3": "أبطال3", "advancewars": "حرب_التقدم", "ageofempires2": "عصرالإمبراطوريات2", "disciples2": "تلاميذ2", "plantsvszombies": "نباتاتvsزومبيز", "giochidistrategia": "ألعاب_استراتيجية", "stratejioyunları": "استراتيجية_ألعاب", "europauniversalis4": "يوروبيونيفرساليس4", "warhammervermintide2": "وارهامر_فيرمينتايد2", "ageofwonders": "عصرالعجائب", "dinosaurking": "ديناصوركينج", "worldconquest": "غزو_العالم", "heartsofiron4": "قلوب_حديدية4", "companyofheroes": "رفاق_الأبطال", "battleforwesnoth": "معركة_ويزناث", "aoe3": "آوي3", "forgeofempires": "صهرالإمبراطوريات", "warhammerkillteam": "وارهيمركيلتيم", "goosegooseduck": "بطةبطتوز", "phobies": "فوبيا", "phobiesgame": "لعبة_فوبيا", "gamingclashroyale": "معركة_الألعاب_رويال", "adeptusmechanicus": "أديبتسميكانيكوس", "outerplane": "أوتربلاين", "turnbased": "دور_عندك", "bomberman": "بومبرمان", "ageofempires4": "عصرالإمبراطوريات4", "civilization5": "حضارة5", "victoria2": "فيكتوريا2", "crusaderkings": "كروسيدركينغز", "cultris2": "كولتريز2", "spellcraft": "فن_السحر", "starwarsempireatwar": "حرب_نجوم_إمبراطورية_في_حرب", "pikmin4": "بيكمن4", "anno1800": "أنو1800", "estratégia": "استراتيجية", "popfulmail": "بوبفولميل", "shiningforce": "قوة_اللمعان", "masterduel": "ماسترديويل", "dysonsphereprogram": "برنامج_ديزون_سفير", "transporttycoon": "رجل_النقل", "unrailed": "غيرمتروك", "magicarena": "ساحة_السحر", "wolvesville": "وولفزفيل", "ooblets": "أوبليتس", "planescapetorment": "طلعات_مجنونة", "uplandkingdoms": "ممال<PERSON>_الجبال", "galaxylife": "حياة_المجرة", "wolvesvilleonline": "ووولفزفيلأونلاين", "slaythespire": "أسقطواالعمود", "battlecats": "معركة_القطط", "sims3": "سيمس3", "sims4": "سيمز4", "thesims4": "ذا_سيمز4", "thesims": "ذا_سيمز", "simcity": "مدينة_المحاكاة", "simcity2000": "سيمسيتي2000", "sims2": "سيمس2", "iracing": "سباق_الخيال", "granturismo": "غرانتوريزمو", "needforspeed": "هوس_السرعة", "needforspeedcarbon": "بحاجة_لسرعة_كربون", "realracing3": "منافسة_الحقيقية3", "trackmania": "تراكمانيا", "grandtourismo": "جراندتوريسمو", "gt7": "جيتي7", "simsfreeplay": "سيمز_فري_بلاي", "ts4": "تس4", "thesims2": "ذا_سيمز2", "thesims3": "ذا_سيمز3", "thesims1": "ذا_سيمز1", "lossims4": "خسارةsims4", "fnaf": "أنا_نايم_بكن_في_fnaf", "outlast": "تفوّق", "deadbydaylight": "ديدبايدايلايت", "alicemadnessreturns": "عودة_جنون_أليس", "darkhorseanthology": "أنتولوجيا_الحصان_الأسود", "phasmophobia": "رهاب_الأشباح", "fivenightsatfreddys": "خمسةلياليعندفريديز", "saiko": "سايكو", "fatalframe": "إطار_قاتل", "littlenightmares": "كابوس_صغير", "deadrising": "انتفاضة_الأموات", "ladydimitrescu": "ليدي_ديميتريسكу", "homebound": "داخل_البيت", "deadisland": "جزيرة_الموت", "litlemissfortune": "مسكينة_بسيطة", "projectzero": "مشروعzero", "horory": "هوروري", "jogosterror": "خوف_جوجو", "helloneighbor": "مرحبا_جار", "helloneighbor2": "مرحبا_جار2", "gamingdbd": "قائمة_ألعاب_boo", "thecatlady": "ستّ_القطط", "jeuxhorreur": "ألعاب_رعب", "horrorgaming": "ألعابرعب", "magicthegathering": "ماجيك_ذا_غاثرينغ", "mtg": "ماجد", "tcg": "تسلق_السقف", "cardsagainsthumanity": "بطاقاتضدالإنسانية", "cribbage": "كريبيبج", "minnesotamtg": "مينيسوتا_مترجة", "edh": "ادھ", "monte": "مونتي", "pinochle": "بينيشيل", "codenames": "كودنيمز", "dixit": "ديكست", "bicyclecards": "بطاقات_الدراجات", "lor": "لور", "euchre": "يوكير", "thegwent": "ذاغوينت", "legendofrunetera": "أسطورة_رونيتيرا", "solitaire": "بوسوليتير", "poker": "بوكَر", "hearthstone": "لعبة_المفاتيح", "uno": "أونو", "schafkopf": "شلافكوف", "keyforge": "مفت<PERSON><PERSON>_البوابات", "cardtricks": "خدع_الكوتشينة", "playingcards": "بطاقات_لعب", "marvelsnap": "مارفل_سناب", "ginrummy": "جينرامي", "netrunner": "نتراينر", "gwent": "غوينت", "metazoo": "ميتازو", "tradingcards": "كروت_مبادلة", "pokemoncards": "بطاقات_بوكيمون", "fleshandbloodtcg": "لحم_ودم_تي_سي_جي", "sportscards": "كروت_الرياضة", "cardfightvanguard": "كاردفايتفانغارد", "duellinks": "دويللينكس", "spades": "س<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcry": "صرخةالحرب", "digimontcg": "ديجيمونتسيجي", "toukenranbu": "توكينرانبو", "kingofhearts": "ملكالقلوب", "truco": "تركو", "loteria": "لوتيريا", "hanafuda": "هانافودا", "theresistance": "المقاومة", "transformerstcg": "ترانسفورمرزtcg", "doppelkopf": "دوبلكوف", "yugiohcards": "بطاقات_يوغييوه", "yugiohtcg": "يوغيهوtcg", "yugiohduel": "يوغيوديوال", "yugiohocg": "يوغييوسيجي", "dueldisk": "دويليست", "yugiohgame": "يوغيواهوغلعبة", "darkmagician": "ساحر_<PERSON><PERSON>ام", "blueeyeswhitedragon": "عيون_زرقاء_وتنين_أبيض", "yugiohgoat": "يوغيوهغوت", "briscas": "بريسكس", "juegocartas": "لعبة_البطاقات", "burraco": "بوراكو", "rummy": "رامي", "grawkarty": "جروكرتي", "dobble": "دوبل", "mtgcommander": "كوماندرفيامتيجي", "cotorro": "كوتورو", "jeuxdecartes": "ألعاب_البطاقات", "mtgjudge": "قاضي_بطولة", "juegosdecartas": "ألعاب_ورق", "duelyst": "ديوليست", "mtgplanschase": "مطاردة_خطط_المجتمعات", "mtgpreconcommander": "ملوك_العب_المسبق", "kartenspiel": "لعبةالكارتي", "carteado": "كارتيدو", "sueca": "سويكا", "beloteonline": "بلوتأونلاين", "karcianki": "كارتشينكي", "battlespirits": "أرواح_المعركة", "battlespiritssaga": "هوس_قتال_الأرواح", "jogodecartas": "لعبة_البطاقات", "žolíky": "زوليكي", "facecard": "بطاقة_الوجه", "cardfight": "قتال_البطاقات", "biriba": "بريبا", "deckbuilders": "بناةالدك", "marvelchampions": "أبطال_مارفل", "magiccartas": "بطاقات_سحرية", "yugiohmasterduel": "يوغي_أوه_ماستر_دويلي", "shadowverse": "ظل_العالم", "skipbo": "سكيببو", "unstableunicorns": "يونيكورنزغيرمستقرة", "cyberse": "سايبرس", "classicarcadegames": "ألعاب_الأركيد_الكلاسيكية", "osu": "أوسو", "gitadora": "جيتادورا", "dancegames": "ألعاب_الرقص", "fridaynightfunkin": "فانك_ليل_الجمعة", "fnf": "فانف", "proseka": "بروسيكا", "projectmirai": "مشروع_ميرايت", "projectdiva": "مشروعديوا", "djmax": "ديجيماكس", "guitarhero": "بطل_الجيتار", "clonehero": "كلونهيرو", "justdance": "بس_ارقص", "hatsunemiku": "هاتسونيميكو", "prosekai": "بروسيكاي", "rocksmith": "روكسميث", "idolish7": "ايدوليش7", "rockthedead": "اهزواالأموات", "chunithm": "تشونيذم", "idolmaster": "أيدولماستر", "dancecentral": "رقص_سنترال", "rhythmgamer": "لاعب_الإيقاع", "stepmania": "ستيبمانيا", "highscorerythmgames": "ألعابالإيقاعذاتالنقاطالعالية", "pkxd": "بكxd", "sidem": "سيديم", "ongeki": "أونغيكي", "soundvoltex": "ساوندفولتيكس", "rhythmheaven": "إيقاع_الجنة", "hypmic": "هاشتاجهيبميك", "adanceoffireandice": "رقصة_النار_والثلج", "auditiononline": "اختبار_عن_بعد", "itgmania": "إيتجمانيا", "juegosderitmo": "ألعاب_الإيقاع", "cryptofthenecrodancer": "كريبتو_ذي_نكرودانسر", "rhythmdoctor": "راعي_الإيقاع", "cubing": "تحديالمتاهة", "wordle": "وردل", "teniz": "تينز", "puzzlegames": "ألعاب_الألغاز", "spotit": "سبوتت", "rummikub": "راميكاوب", "blockdoku": "بلوكدوكو", "logicpuzzles": "ألغاز_منطقية", "sudoku": "سودوكو", "rubik": "روبيك", "brainteasers": "الألغاز", "rubikscube": "كعب_روبيك", "crossword": "كلمات_مقاطع", "motscroisés": "كلمات_متقاطعة", "krzyżówki": "كرازيwكي", "nonogram": "نونوجرام", "bookworm": "عشّاقالكتب", "jigsawpuzzles": "بازلقطعتين", "indovinello": "فزورة", "riddle": "فزورة", "riddles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "لعبة_الألغاز", "tekateki": "تيكاتي", "inside": "جوة", "angrybirds": "طيورغاضبة", "escapesimulator": "لعبة_الهروب", "minesweeper": "مناجم", "puzzleanddragons": "ألغازوتنانين", "crosswordpuzzles": "ألغاز_الكلمات", "kurushi": "كرشي", "gardenscapesgame": "حديقة_الألعاب", "puzzlesport": "ألعاب_الألغاز", "escaperoomgames": "ألعاب_الهروب", "escapegame": "لعبة_الهروب", "3dpuzzle": "بازل3دي", "homescapesgame": "هاشتاج_هومسكيبز", "wordsearch": "بحث_عن_الكلمات", "enigmistica": "بوز_ألعاب_الألغاز", "kulaworld": "كوكلورلد", "myst": "ميست", "riddletales": "ألغاز_بو", "fishdom": "فيشدوم", "theimpossiblequiz": "الاختبار_المستحيل", "candycrush": "كانديكراش", "littlebigplanet": "كوكب_الصغار_الكبير", "match3puzzle": "بادلت_الأحجار", "huniepop": "هونيبوب", "katamaridamacy": "كتاماريداماسي", "kwirky": "كويركي", "rubikcube": "مكعب_روبيك", "cuborubik": "كوبوروبك", "yapboz": "يابوز", "thetalosprinciple": "مبد<PERSON>_تالوس", "homescapes": "هومسكيبز", "puttputt": "بوتبوت", "qbert": "كيوبرت", "riddleme": "أحجيني", "tycoongames": "ألعاب_تيكون", "cubosderubik": "كيوبي_ديروبك", "cruciverba": "بوزناً", "ciphers": "شيفرات", "rätselwörter": "كلمات_الألغاز", "buscaminas": "بوسكاميناس", "puzzlesolving": "حل<PERSON><PERSON><PERSON>لغ<PERSON>ز", "turnipboy": "فتى_اللفت", "adivinanzashot": "تحدي_الألغاز", "nobodies": "ما<PERSON><PERSON><PERSON>", "guessing": "تخمين", "nonograms": "أحجية_الأرقام", "kostkirubika": "كوستكيربيكا", "crypticcrosswords": "الألغاز_الغامضة", "syberia2": "سيبريا2", "puzzlehunt": "مغامرةالألغاز", "puzzlehunts": "البحث_عن_الألغاز", "catcrime": "جريمة_القطط", "quebracabeça": "كسر_رأس", "hlavolamy": "هلافولامي", "poptropica": "بوبيترولكا", "thelastcampfire": "آخرمخيمالنار", "autodefinidos": "ذاتي_التعريف", "picopark": "بيكوبارك", "wandersong": "هاشت<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "كارتو", "untitledgoosegame": "لعبة_الإوز_من_غير_عنوان", "cassetête": "كاسيتيت", "limbo": "ليمبو", "rubiks": "روبكس", "maze": "مذهل", "tinykin": "تاينيكين", "rubikovakostka": "مكعب_روبيك", "speedcube": "سرعة_الكعب", "pieces": "قطع", "portalgame": "لعبة_البوابة", "bilmece": "بيلمزك", "puzzelen": "بوزلن", "picross": "بيكروس", "rubixcube": "مكعب_روبيكس", "indovinelli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "كوبوماجيكو", "mlbb": "مل<PERSON><PERSON>", "pubgm": "ببجي", "codmobile": "كودموبيل", "codm": "كودم", "twistedwonderland": "عالم_ملتوٍ", "monopoly": "مونوبولي", "futurefight": "مستقبل_القتال", "mobilelegends": "أساطير_الموبايل", "brawlstars": "نجوم_المعارك", "brawlstar": "بروالستار", "coc": "كوك", "lonewolf": "ذئب_وحيد", "gacha": "غاشا", "wr": "ور", "fgo": "فغو", "bitlife": "حياة_بسيطة", "pikminbloom": "بيكمينبلوم", "ff": "فف", "ensemblestars": "نجوم_الفرقة", "asphalt9": "أسفلت9", "mlb": "إمإلبي", "cookierunkingdom": "مملكة_كوكي_ران", "alchemystars": "نجوم_الكيمياء", "stateofsurvival": "حالة_البقاء", "mycity": "مدينتي", "arknights": "أركنايتس", "colorfulstage": "مسر<PERSON>_ملون", "bloonstowerdefense": "دفاع_برج_البالونات", "btd": "بتد", "clashroyale": "كلاشرويال", "angela": "أنجلاإ", "dokkanbattle": "دوكانباتل", "fategrandorder": "قدر_المهمة", "hyperfront": "هايبرفرونت", "knightrun": "ركض_الفرسان", "fireemblemheroes": "فايرإمبلمهيروز", "honkaiimpact": "هونكاي_امباكت", "soccerbattle": "معركة_كرة_القدم", "a3": "أ3", "phonegames": "ألعاب_الهاتف", "kingschoice": "اختيار_الملوك", "guardiantales": "حكايات_الحراس", "petrolhead": "مجنونالسيارات", "tacticool": "تكتيكول", "cookierun": "سباق_البسكوت", "pixeldungeon": "بكسلدانجن", "arcaea": "أركايا", "outoftheloop": "بعيدعنالسالفة", "craftsman": "حرفي", "supersus": "سوبرسوس", "slowdrive": "بطيئة_سياقة", "headsup": "هاشتاجانتبه", "wordfeud": "ووردفيد", "bedwars": "حروب_السرير", "freefire": "فريفاير", "mobilegaming": "ألعاب_الموبايل", "lilysgarden": "حديقة_ليلى", "farmville2": "فارمفيل2", "animalcrossing": "عبور_الحيوانات", "bgmi": "بيجي_ام_اي", "teamfighttactics": "تكتك_قتال_الفرق", "clashofclans": "حرب_العشائر", "pjsekai": "بيجي_سكاى", "mysticmessenger": "مستقبل_بغل", "callofdutymobile": "كود_الموبايل", "thearcana": "الأركانا", "8ballpool": "كرة_الثمانية", "emergencyhq": "مقر_الطوارئ", "enstars": "إنستارز", "randonautica": "رانودوتيكا", "maplestory": "مابلاستوري", "albion": "ألبا<PERSON>ن", "hayday": "هايداي", "onmyoji": "أونيوموجي", "azurlane": "أزورلاين", "shakesandfidget": "هزاتوفجوات", "ml": "مل", "bangdream": "بانغدريم", "clashofclan": "صراع_العشائر", "starstableonline": "ستارستابلأونلاين", "dragonraja": "تنينراجا", "timeprincess": "أميرة_الوقت", "beatstar": "بيستار", "dragonmanialegend": "أسطورة_رجل_التنين", "hanabi": "هاانابي", "disneymirrorverse": "مرآة_ديزني", "pocketlove": "<PERSON><PERSON><PERSON>جي<PERSON>ي", "androidgames": "ألعا<PERSON>_أندرويد", "criminalcase": "قضية_إجرامية", "summonerswar": "حرب_المستدعين", "cookingmadness": "جنون_الطبخ", "dokkan": "دوكان", "aov": "أو<PERSON>", "triviacrack": "هاشتاجتريفياكراك", "leagueofangels": "ليغأوفأنجلز", "lordsmobile": "لوردز_موبايل", "tinybirdgarden": "حديقة_طيور_بو", "gachalife": "gachalife", "neuralcloud": "السحاب_العصبي", "mysingingmonsters": "وحوشي_المغنية", "nekoatsume": "نيكوأطومي", "bluearchive": "أرشيف_بلو", "raidshadowlegends": "رايد<PERSON>ادو<PERSON>يجندز", "warrobots": "روبوتات_الحرب", "mirrorverse": "عالم_المرآة", "pou": "بـو", "warwings": "أجنحة_الحرب", "fifamobile": "فيفا_موبايل", "mobalegendbangbang": "موبايلغندبنجانغ", "evertale": "إيفرتيل", "futime": "وقت_الإنجاز", "antiyoy": "أنتي_يوي", "apexlegendmobile": "هاشتاجأبيكس_legends_موبايل", "ingress": "إينغرس", "slugitout": "بنقهرها", "mpl": "امبول", "coinmaster": "كوينيماستر", "punishinggrayraven": "عقاب_الغونزا", "petpals": "أصدقاء_الحيوانات", "gameofsultans": "لعبة_السلاطين", "arenabreakout": "كسر_السجن", "wolfy": "ذئبي", "runcitygame": "لعبة_ركض_المدينة", "juegodemovil": "لعبة_موبايل", "avakinlife": "هاشتاجأفاكينلايف", "kogama": "كوجاما", "mimicry": "تقليد", "blackdesertmobile": "صحراواسطورة", "rollercoastertycoon": "مغامرات_المدينة_الملاهي", "grandchase": "مطاردة_العظمة", "bombmebrasil": "بوبها_برازيل", "ldoe": "لد<PERSON>ز", "legendonline": "أسطورة_أونلاين", "otomegame": "ألعاب_الأوتومي", "mindustry": "مايندستري", "callofdragons": "نداءالتنانين", "shiningnikki": "نيكي_المتلألئة", "carxdriftracing2": "كاركسدريفتراسينغ2", "pathtonowhere": "طرحة_للاماكن_المجهولة", "sealm": "سيلم", "shadowfight3": "قتال_الظل3", "limbuscompany": "شركةليمبوس", "demolitionderby3": "دمار_الدرابي3", "wordswithfriends2": "كلمات_مع_الأصدقاء2", "soulknight": "فارس_الروح", "purrfecttale": "قصة_مبهرة", "showbyrock": "شوفبايروك", "ladypopular": "لابديتحولبوبولار", "lolmobile": "موبايل_لول", "harvesttown": "مدينةالحصاد", "perfectworldmobile": "عالم_مثالي_موبايل", "empiresandpuzzles": "إمبراطورياتوالألغاز", "empirespuzzles": "ألعاب_الذكاء_لإمباير", "dragoncity": "مدينة_التنين", "garticphone": "غارتيكفون", "battlegroundmobileind": "ساحة_المعركة_موبايل_الهند", "fanny": "فاني", "littlenightmare": "كابوس_صغير", "aethergazer": "ناظر_الآثير", "mudrunner": "متسابقون", "tearsofthemis": "دموعهميس", "eversoul": "إيفرسول", "gunbound": "جونباوند", "gamingmlbb": "جيمينجملبب", "dbdmobile": "ديبـيـديـمـوبـايل", "arknight": "أركنايت", "pristontale": "برايستون_تيل", "zombiecastaways": "زومبيكاستاويز", "eveechoes": "صدى_عشية", "jogocelular": "جوجوسلولي", "mariokarttour": "ماريكارت_تور", "zooba": "زوبة", "mobilelegendbangbang": "موبايلليجندبانغبانغ", "gachaclub": "غاشاكلوب", "v4": "فور", "cookingmama": "ماما_الطبخ", "cabalmobile": "كابال_موبايل", "streetfighterduel": "مواجهة_مقاتلي_الشارع", "lesecretdhenri": "سرهنري", "gamingbgmi": "ألعاب_بيجيإمآي", "girlsfrontline": "جبهة_الفتيات", "jurassicworldalive": "عالم_الديانات_حي", "soulseeker": "باحث_عن_الروح", "gettingoverit": "تجاوزها", "openttd": "<PERSON>و<PERSON>نttd", "onepiecebountyrush": "ونبيس_باونتي_راش", "moonchaistory": "قصص_مونتشا", "carxdriftracingonline": "سباقات_سياراتك_على_بو", "jogosmobile": "ألعاب_موبايل", "legendofneverland": "أسطورةنيفرلاند", "pubglite": "بوبجي_لايت", "gamemobilelegends": "هاشتاجلعبة_موبايل_ليجندز", "timeraiders": "مغامري_الزمن", "gamingmobile": "ألعاب_موبايل", "marvelstrikeforce": "مارفلسترايكفورس", "thebattlecats": "معركة_القطط", "dnd": "دنسينغ드راكونز", "quest": "مغامرة", "giochidiruolo": "لعب_السيرفر", "dnd5e": "ديإندي5إي", "rpgdemesa": "عالم_الأدوار_بمساء", "worldofdarkness": "عالمالظلام", "travellerttrpg": "مسافر_تي_تي_آر_بي_جي", "2300ad": "٢٣٠٠ميلادي", "larp": "لعبة_الأدوار", "romanceclub": "نادي_الرومانسية", "d20": "دي20", "pokemongames": "ألعاب_بوكيمون", "pokemonmysterydungeon": "بوكيمون_زنزانات_الأسرار", "pokemonlegendsarceus": "بوكيمون_أساطير_أركيوس", "pokemoncrystal": "بوكيمونكريستال", "pokemonanime": "بوكيمون_أنمي", "pokémongo": "بوكيمونجو", "pokemonred": "بوكيمون_أحمر", "pokemongo": "بوكيمونغو", "pokemonshowdown": "بوكيمون_مواجهة", "pokemonranger": "بوكيمون_رينجر", "lipeep": "ليبيپ", "porygon": "بوريغون", "pokemonunite": "بوكيمونيونايت", "entai": "انتاي", "hypno": "هيبنو", "empoleon": "إمبوليون", "arceus": "أركيوس", "mewtwo": "ميتوتو", "paldea": "بالديا", "pokemonscarlet": "بوكيمون_سكارليت", "chatot": "شاتوت", "pikachu": "بيكاتشو", "roxie": "روكسي", "pokemonviolet": "بوكيمونفايولت", "pokemonpurpura": "بوكيمون_بنفسجي", "ashketchum": "أشكيتشوم", "gengar": "غنجار", "natu": "ناتو", "teamrocket": "فريق_روكيت", "furret": "فرريت", "magikarp": "ماجيكارپ", "mimikyu": "ميميكيو", "snorlax": "سنورلاكس", "pocketmonsters": "وحوش_الجيب", "nuzlocke": "نوزلوك", "pokemonplush": "بوبومان_بلوش", "teamystic": "تيماستيك", "pokeball": "بوكيمونالكرة", "charmander": "شارمندر", "pokemonromhack": "هاشتاجبوكيمونرومهاك", "pubgmobile": "بوبجيموبايل", "litten": "ليتن", "shinypokemon": "بوكيموناللامع", "mesprit": "مِسْبَرت", "pokémoni": "بوكيموني", "ironhands": "يديحديدية", "kabutops": "كابوتوبس", "psyduck": "بايسيدك", "umbreon": "أمبرون", "pokevore": "بُوكفُور", "ptcg": "بيتيجي", "piplup": "بيبلوب", "pokemonsleep": "نومبوكيمون", "heyyoupikachu": "هيايوبيكاتشو", "pokémonmaster": "ماستر_بوكيمون", "pokémonsleep": "نوم_بوكيمون", "kidsandpokemon": "أطفالوبوكيمون", "pokemonsnap": "بوكيمون_سناب", "bulbasaur": "بولباسور", "lucario": "لوكاريو", "charizar": "شارزار", "shinyhunter": "صياد_اللمعان", "ajedrez": "شطرنج", "catur": "شطرنج", "xadrez": "شطرنج", "scacchi": "شطرنج", "schaken": "شaken", "skak": "سكاك", "ajedres": "شطرنج", "chessgirls": "فتيات_الشطرنج", "magnuscarlsen": "ماجنوسكارلسن", "worldblitz": "عالم_بليتس", "jeudéchecs": "شطرنج", "japanesechess": "شطرنج_ياباني", "chinesechess": "شطرنج_صيني", "chesscanada": "تشيسكندا", "fide": "فيدي", "xadrezverbal": "شطرنج_شفوي", "openings": "فرص", "rook": "روك", "chesscom": "تشيسكوم", "calabozosydragones": "زنزانات_وتنانين", "dungeonsanddragon": "زنزاناتوتنانين", "dungeonmaster": "مديرالزنزانات", "tiamat": "تيامات", "donjonsetdragons": "دنجونز_و_تنانين", "oxventure": "أوكسبينشر", "darksun": "شمس_الظلام", "thelegendofvoxmachina": "أسطورةفوكسمشينا", "doungenoanddragons": "دونجنووودراجونز", "darkmoor": "داركموور", "minecraftchampionship": "هاشتاج_بطولة_ماينكرافت", "minecrafthive": "هاشتاغمينكرافتهايف", "minecraftbedrock": "ماينكرافت_بدروك", "dreamsmp": "أحلام_سياج", "hermitcraft": "هارميتكرافت", "minecraftjava": "هاشتاجماينكرافت_جافا", "hypixelskyblock": "هاشتاج_هايبيكسل_سكايبلوك", "minetest": "ماينكرافت", "hypixel": "هاشتاجهيبكسل", "karmaland5": "كارمالاند5", "minecraftmods": "مودات_ماينكرافت", "mcc": "مكسيك", "candleflame": "لهيب_الشمعة", "fru": "فرو", "addons": "إضافات", "mcpeaddons": "إضافات_ماينكرافت", "skyblock": "سكايبلوك", "minecraftpocket": "ماينكرافت_بوكيت", "minecraft360": "ماينكرافت360", "moddedminecraft": "ماينكرافت_المعدلة", "minecraftps4": "هاشتاغماينكرافت_بيأس4", "minecraftpc": "ماينكرافت_بيسي", "betweenlands": "بينالأراضي", "minecraftdungeons": "زنزانات_ماينكرافت", "minecraftcity": "مدينةماينكرافت", "pcgamer": "لاعبي_الكمبيوتر", "jeuxvideo": "ألعاب_فيديو", "gambit": "قنبلة", "gamers": "لاعبين", "levelup": "ارفع_مستواك", "gamermobile": "لاعبين_موبايل", "gameover": "لعبة_انتهت", "gg": "جيجي", "pcgaming": "ألعاب_الكمبيوتر", "gamen": "ج<PERSON><PERSON>", "oyunoynamak": "أونويناماك", "pcgames": "ألعاب_الكمبيوتر", "casualgaming": "ألعاب_غير_رسمية", "gamingsetup": "إعداد_الألعاب", "pcmasterrace": "سباق_سادة_البيسي", "pcgame": "ألعاب_الكمبيوتر", "gamerboy": "غيمربوي", "vrgaming": "ألعاب_الواقع_الافتراضي", "drdisrespect": "دكتور_ديسريسبكت", "4kgaming": "٤كغيمنج", "gamerbr": "لاعبين_boo", "gameplays": "أسلوب_اللعب", "consoleplayer": "لاعب_كونسول", "boxi": "بوكسي", "pro": "برو", "epicgamers": "ملحمين_الألعاب", "onlinegaming": "ألعاب_أونلاين", "semigamer": "نصف_لاعب", "gamergirls": "بنات_الألعاب", "gamermoms": "أمهات_اللاعبيين", "gamerguy": "جيمر_راعي", "gamewatcher": "مراقب_الألعاب", "gameur": "غيمر", "grypc": "غريبيك", "rangugamer": "رانغوجيمر", "gamerschicas": "لاعبات_بوا", "otoge": "أوتوجي", "dedsafio": "ديتسافيو", "teamtryhard": "فريقالمجهود", "mallugaming": "ماللوجيمينج", "pawgers": "باوجرزم", "quests": "مهمات", "alax": "الكس", "avgn": "<PERSON><PERSON><PERSON><PERSON>", "oldgamer": "لاعبين_قدامى", "cozygaming": "ألعاب_مريحة", "gamelpay": "جيميلباي", "juegosdepc": "ألعاب_الكمبيوتر", "dsswitch": "فتح_الدياس", "competitivegaming": "الألعاب_التنافسية", "minecraftnewjersey": "ماينكرافت_نيوجيرسي", "faker": "ف<PERSON>ر", "pc4gamers": "بيسي4لاعبين", "gamingff": "لاعبينff", "yatoro": "ياتوروا", "heterosexualgaming": "الألعاب_الهيترو", "gamepc": "لعبة_بيسي", "girlsgamer": "لاعبات_بلايستيشن", "fnfmods": "مودات_فنيشنال_فريداي", "dailyquest": "مهمة_يومية", "gamegirl": "فتاة_الألعاب", "chicasgamer": "بنات_الألعاب", "gamesetup": "إعداد_الألعاب", "overpowered": "قويجدًا", "socialgamer": "لاعب_اجتماعي", "gamejam": "هاشتاج_لعبة", "proplayer": "بروبلاير", "roleplayer": "لاعبأدوار", "myteam": "فريقي", "republicofgamers": "جمهورية_اللاعبين", "aorus": "أوروس", "cougargaming": "لعبة_كوغر", "triplelegend": "أسطورةمضاعفة", "gamerbuddies": "أصدقاء_اللاعبين", "butuhcewekgamers": "بنتحتاجللبناتالجيمرز", "christiangamer": "لاعب_كريستياني", "gamernerd": "غشيم_جيمرز", "nerdgamer": "نيردجيمر", "afk": "<PERSON><PERSON><PERSON>", "andregamer": "اندري_جيمر", "casualgamer": "لاعب_كاجوال", "89squad": "89فريق", "inicaramainnyagimana": "إنكارامانيانيجمينا", "insec": "انظروا", "gemers": "ج<PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "أويونيزليكم", "gamertag": "اسم_اللاعب", "lanparty": "حفلة_ألعاب", "videogamer": "لاعب_ألعاب", "wspólnegranie": "لعب_مستدام", "mortdog": "مورتدوغ", "playstationgamer": "لاعب_بلايستيشن", "justinwong": "جاستن_وونغ", "healthygamer": "لاعب_صحي", "gtracing": "جيتراسينغ", "notebookgamer": "لاعب_دفتر", "protogen": "بروتوجين", "womangamer": "مسترجلة", "obviouslyimagamer": "واضح_أني_لاعب", "mario": "ماريو", "papermario": "بابرماريو", "mariogolf": "ماريوبولف", "samusaran": "ساموساران", "forager": "جامع_المكافآت", "humanfallflat": "سقوط_البشر", "supernintendo": "سوبرنينتندو", "nintendo64": "نينتندو64", "zeroescape": "زيروإسكيب", "waluigi": "واليجي", "nintendoswitch": "نينتندو_سويتش", "nintendosw": "نينتندو_sw", "nintendomusic": "هاشتاجنينتندوميوزك", "sonicthehedgehog": "سونيكتةالقنفذ", "sonic": "صوتي", "fallguys": "فولقايز", "switch": "تبديل", "zelda": "زيلدا", "smashbros": "سمشبروز", "legendofzelda": "أسطورة_زيلدا", "splatoon": "سبلتون", "metroid": "ميترويد", "pikmin": "بيكمن", "ringfit": "رينجفيت", "amiibo": "أميبو", "megaman": "ميغامان", "majorasmask": "ماجوراسماك", "mariokartmaster": "ماستركارتماريو", "wii": "وييي", "aceattorney": "محامي_أساسي", "ssbm": "إسإسبيأم", "skychildrenofthelight": "أطفال_السماء_في_نور", "tomodachilife": "حياة_الأصدقاء", "ahatintime": "أهتمام_بوقتي", "tearsofthekingdom": "دموع_المملكة", "walkingsimulators": "محاكيات_المشي", "nintendogames": "ألعاب_نينتندو", "thelegendofzelda": "أسطورة_زيلدا", "dragonquest": "مهمة_التنين", "harvestmoon": "قمر_الحصاد", "mariobros": "ماريوبروس", "runefactory": "مزرعة_الأرواح", "banjokazooie": "بنجوكازوي", "celeste": "سيلستي", "breathofthewild": "نسيم_البرية", "myfriendpedro": "صديقي_بيدرو", "legendsofzelda": "أساطيرزيلدا", "donkeykong": "دونكيكونغ", "mariokart": "ماريوكارت", "kirby": "كيربي", "51games": "51ألعاب", "earthbound": "مربوط_بالأرض", "tales": "حكايات", "raymanlegends": "رايمانlegends", "luigismansion": "قصر_لويجي", "animalcrosssing": "عبور_الحيوانات", "taikonotatsujin": "تايكو_نوتاتسوجين", "nintendo3ds": "نينتندو3دياس", "supermariobros": "سوبرماريوبروس", "mariomaker2": "ماريوميكير2", "boktai": "بوكتي", "smashultimate": "سماشالتيميت", "nintendochile": "نينتندو_شيلي", "tloz": "تلوظ", "trianglestrategy": "استراتيجية_المثلث", "supermariomaker": "صانع_ماريو_الخارق", "xenobladechronicles3": "زينو_بليد_كرونيكلز_3", "supermario64": "سوبرماريو64", "conkersbadfurday": "بونيكيرزكويسيوفيوم", "nintendos": "نينتندوز", "new3ds": "نيو3ديأس", "donkeykongcountry2": "دونكيكونغكنتري2", "hyrulewarriors": "محاربي_هايرول", "mariopartysuperstars": "أبطال_حفلات_ماريو", "marioandsonic": "ماريووسونيك", "banjotooie": "بنجوتوي", "nintendogs": "نينتندوغز", "thezelda": "زيلدا", "palia": "باليا", "marioandluigi": "ماريوولويجي", "mariorpg": "ماريوآربيجي", "zeldabotw": "زيلدا_بوته", "yuumimain": "يوميمين", "wildrift": "وايلدريفت", "riven": "ريفين", "ahri": "أهري", "illaoi": "إلاوي", "aram": "آرام", "cblol": "سيب_لول", "leagueoflegendslas": "ليغأوفليدجندزلاس", "urgot": "أورغوت", "zyra": "زايرا", "redcanids": "ريد<PERSON><PERSON>يدز", "vanillalol": "فانيل<PERSON><PERSON>l", "wildriftph": "وايلدريفتفيلبين", "lolph": "لولف", "leagueoflegend": "دوري_الأبطال", "tốcchiến": "توكشين", "gragas": "غراگاس", "leagueoflegendswild": "دوري_أساطير_البرية", "adcarry": "إعلانك", "lolzinho": "لولزينو", "leagueoflegendsespaña": "ليغ_أوف_ليجندز_إسبانيا", "aatrox": "أاتروكس", "euw": "بوه_مزعج", "leagueoflegendseuw": "ليغ_أوف_ليجندز_يو_أي_وي", "kayle": "كايلي", "samira": "سميرة", "akali": "أكالي", "lunari": "لونياري", "fnatic": "فنتك", "lollcs": "اضح<PERSON>_مع_بو", "akshan": "أكشان", "milio": "ميلو", "shaco": "شاكوا", "ligadaslegendas": "مرتبطةبالأساطير", "gaminglol": "جيمينغلول", "nasus": "ناسوس", "teemo": "تييمو", "zedmain": "زيدمين", "hexgates": "بوابة_هيكس", "hextech": "هيكستيك", "fortnitegame": "فورتنايت_لعبة", "gamingfortnite": "هاشتاجالألعابفورتنايت", "fortnitebr": "فورتنايتبري", "retrovideogames": "ألعاب_فيديو_قديمة", "scaryvideogames": "ألعابفيديومخيفة", "videogamemaker": "صانع_ألعاب_الفيديو", "megamanzero": "مغامرات_بو", "videogame": "ألعاب_فيديو", "videosgame": "ألعاب_فيديو", "professorlayton": "بروفيسورلايتون", "overwatch": "أوفرواتش", "ow2": "أو٢", "overwatch2": "أوفرواتش2", "wizard101": "وايزرد101", "battleblocktheater": "بندقية_معركة_المسرح", "arcades": "ألعاب_الصالات", "acnh": "أكنه", "puffpals": "أصدقاء_بوب", "farmingsimulator": "محاكي_الزراعة", "robloxchile": "روبلوكس_شيلي", "roblox": "روبلكس", "robloxdeutschland": "روبلوكس_ألمانيا", "robloxdeutsch": "روبلوكس_ألماني", "erlc": "ارلق", "sanboxgames": "ألعاب_سانبوكس", "videogamelore": "حب_ألعاب_الفيديو", "rollerdrome": "رولردروم", "parasiteeve": "باراسيتيف", "gamecube": "غيمكيوب", "starcraft2": "ستاركرافت2", "duskwood": "داكود", "dreamscape": "فضاء_الأحلام", "starcitizen": "مواطن_النجوم", "yanderesimulator": "محاكي_ياندره", "grandtheftauto": "جراندثفتأوتو", "deadspace": "مساحة_ميتة", "amordoce": "ح<PERSON>_حلو", "videogiochi": "ألعاب_فيديو", "theoldrepublic": "الجمهورية_القديمة", "videospiele": "ألعاب_فيديو", "touhouproject": "مشروع_توهو", "dreamcast": "درجوكاست", "adventuregames": "ألعاب_المغامرات", "wolfenstein": "وولفنسـتاين", "actionadventure": "مغامرة_فعل", "storyofseasons": "قصة_الفصول", "retrogames": "ألعاب_القديمة", "retroarcade": "أركيد_رترو", "vintagecomputing": "حاسو<PERSON>_عتيق", "retrogaming": "لعبة_الجيل_القديم", "vintagegaming": "ألعاب_قديمة", "playdate": "موعد<PERSON>عب", "commanderkeen": "كومانديركين", "bugsnax": "باستخدام_بوجنكس", "injustice2": "ظلم2", "shadowthehedgehog": "ظل_الراكون", "rayman": "رايمـان", "skygame": "لعبة_السماء", "zenlife": "حياة_زن", "beatmaniaiidx": "بيتمانيا_أي_دي_إكس", "steep": "مائل", "mystgames": "ألعاب_مايستيري", "blockchaingaming": "ألعاب_البلوكشين", "medievil": "ماديفايل", "consolegaming": "ألعاب_الكونسول", "konsolen": "كونسولن", "outrun": "هارب", "bloomingpanic": "ذعر_مزدهر", "tobyfox": "تويبي_فوكسي", "hoyoverse": "هويفرس", "senrankagura": "سنرانكاجورا", "gaminghorror": "رعب_الألعاب", "monstergirlquest": "مهمة_فتاة_الوحش", "supergiant": "سوبرجاينت", "disneydreamlightvalle": "ديزني_دريملايت_فالي", "farmingsims": "محاكاة_الزراعة", "juegosviejos": "ألعاب_قديمة", "bethesda": "بيثيسدا", "jackboxgames": "جاكبوكسجيمز", "interactivefiction": "خيال_تفاعلي", "pso2ngs": "بسو2نجس", "grimfandango": "غريمفاندانغو", "thelastofus2": "آخرنا2", "amantesamentes": "عشاق_العقول", "visualnovel": "رواية_مرئية", "visualnovels": "روايات_مرئية", "rgg": "رغا", "shadowolf": "ظلالذئب", "tcrghost": "شبح_تيسيآر", "payday": "يوم_الرواتب", "chatherine": "كاترين", "twilightprincess": "أميرةالغسق", "jakandaxter": "جاكةدكستر", "sandbox": "صندوق_الرمل", "aestheticgames": "ألعاب_جمالية", "novelavisual": "روايةبصرية", "thecrew2": "الطاقم2", "alexkidd": "أليكسيكيد", "retrogame": "لعبة_رجعية", "tonyhawkproskater": "تونيهوك_برو_سكاتر", "smbz": "سم<PERSON>ز", "lamento": "لومونتو", "godhand": "يد_الإله", "leafblowerrevolution": "ثورة_نفخ_الأوراق", "wiiu": "وييو", "leveldesign": "تصميم_المستويات", "starrail": "ستاررايل", "keyblade": "مفتاحالسيف", "aplaguetale": "أمزح_دايمًا", "fnafsometimes": "فنافأحيانًا", "novelasvisuales": "روايات_مرئية", "robloxbrasil": "روبلوكس_البرازيل", "pacman": "باكمان", "gameretro": "العبرجوع", "videojuejos": "ألعاب_فيديو", "videogamedates": "مواعيد_ألعاب_الفيديو", "mycandylove": "حبيبيسكر", "megaten": "ميغاعشرة", "mortalkombat11": "مورتالكومبات11", "everskies": "إيفرسكايس", "justcause3": "مجردسبب3", "hulkgames": "ألعاب_هالك", "batmangames": "باتمان_ألعاب", "returnofreckoning": "عودةالحسم", "gamstergaming": "جيمسترغامنج", "dayofthetantacle": "يوم_الأخطبوط", "maniacmansion": "هاشتاج_مانياك_مانشن", "crashracing": "سباق_تحطيم", "3dplatformers": "ألعاب_المغامرات_ثلاثية_الأبعاد", "nfsmw": "نفسي_موازي", "kimigashine": "كيميجاشين", "oldschoolgaming": "ألعاب_المدرسة_القديمة", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "ألعاب_قصص", "bioware": "بيووير", "residentevil6": "residentevil6", "soundodger": "ساوندودجر", "beyondtwosouls": "ماوراءروحين", "gameuse": "استعمال_الألعاب", "offmortisghost": "موت_بوجو", "tinybunny": "بنيّةالصغيرة", "retroarch": "ريتروارك", "powerup": "قوة_لزيادة_الطاقة", "katanazero": "كاتانازيرو", "famicom": "فاميكون", "aventurasgraficas": "مغامرات_جرافيكية", "quickflash": "فلاشسريع", "fzero": "إفزيرو", "gachagaming": "قاشا_جيمينغ", "retroarcades": "آركيدز_ريتروا", "f123": "f123", "wasteland": "أرض_الخراب", "powerwashsim": "محاكاة_غسيل_القوة", "coralisland": "جزيرة_كورال", "syberia3": "سيبيريا3", "grymmorpg": "جراموربج", "bloxfruit": "فاكهة_بlox", "anotherworld": "عالم_آخر", "metaquest": "ميتاقويست", "animewarrios2": "محاربي_الأنمي2", "footballfusion": "هاشتاجفوتبولفيوجن", "edithdlc": "إيدث_دي_ال_سي", "abzu": "عب<PERSON>و", "astroneer": "أستروينير", "legomarvel": "ليغو_مارفل", "wranduin": "وراندوين", "twistedmetal": "المعدن_المعوج", "beamngdrive": "بيمنجدرايف", "twdg": "تودغ", "pileofshame": "كومة_العار", "simulator": "محا<PERSON>ي", "symulatory": "متماثل", "speedrunner": "سرعةاللاعب", "epicx": "إيبيكس", "superrobottaisen": "سوبرروبوطيزن", "dcuo": "ديسييوأو", "samandmax": "ساماندمكس", "grywideo": "غريويديو", "gaiaonline": "غايا_أونلاين", "korkuoyunu": "لعبة_الرعب", "wonderlandonline": "أرضالعجائبأونلاين", "skylander": "سكايلاندير", "boyfrienddungeon": "زنزانةالحبيب", "toontownrewritten": "توونتاون_إعادة_كتابة", "simracing": "سباق_السيارات", "simrace": "سباق_سيارات", "pvp": "بفب", "urbanchaos": "فوضى_الحياة_المدنية", "heavenlybodies": "أجساد天使", "seum": "سيم", "partyvideogames": "ألعاب_فيديو_السهرات", "graveyardkeeper": "حارس_المقبرة", "spaceflightsimulator": "محاكي_رحلات_الفضاء", "legacyofkain": "إرثكين", "hackandslash": "هاك_و_سلاش", "foodandvideogames": "طعام_وألعاب_فيديو", "oyunvideoları": "ألعاب_فيديو", "thewolfamongus": "الذئب_بيننا", "truckingsimulator": "محاكي_الشاحنات", "horizonworlds": "عالم_هوريزون", "handygame": "العاب_يدوية", "leyendasyvideojuegos": "أساطير_ألعاب_الفيديو", "oldschoolvideogames": "ألعابالفيديوالقديمة", "racingsimulator": "محاكي_سباقات", "beemov": "بيموف", "agentsofmayhem": "عملاءالفوضى", "songpop": "سونغبوب", "famitsu": "فاميتسو", "gatesofolympus": "بوابة_الأوليمب", "monsterhunternow": "صيادة_الوحوش_الآن", "rebelstar": "نجم_الثورة", "indievideogaming": "ألعاب_الفيديو_المستقلة", "indiegaming": "ألعاب_مستقلة", "indievideogames": "ألعاب_فيديو_مستقلة", "indievideogame": "لعبة_فيديو_مستقلة", "chellfreeman": "تشيلفريمان", "spidermaninsomniac": "سبايدرمان_في_بوي", "bufffortress": "باففورتريس", "unbeatable": "لايُقهر", "projectl": "مشروعل", "futureclubgames": "هاشتاج_ألعاب_نادي_المستقبل", "mugman": "ماغمان", "insomniacgames": "ألعاب_الإنسومنياك", "supergiantgames": "ألعاب_سوبرجاينت", "henrystickman": "هنري_ستيكمان", "henrystickmin": "هنري_ستيكمين", "celestegame": "لعبة_سيليستي", "aperturescience": "علم_فتحات_الأبواب", "backlog": "الطلبات_pending", "gamebacklog": "قائمة_الألعاب", "gamingbacklog": "قائمة_الألعاب", "personnagejeuxvidéos": "شخصيات_ألعاب_فيديو", "achievementhunter": "صيادالإنجازات", "cityskylines": "أفق_المدينة", "supermonkeyball": "كرة_القرد_الخارقة", "deponia": "ديبونيا", "naughtydog": "كلب_شقي", "beastlord": "بيستلورد", "juegosretro": "ألعاب_قديمة", "kentuckyroutezero": "طريق_كنتاكي_زيرو", "oriandtheblindforest": "أوريوالغابةالعمياء", "alanwake": "ألان_ويك", "stanleyparable": "ستانلي_مباراة", "reservatoriodedopamin": "مخزن_الدوبامين", "staxel": "ستاكسيель", "videogameost": "هاشتاجنغمات_ألعاب_الفيديو", "dragonsync": "زايروسنكر", "vivapiñata": "فيفابيلاتا", "ilovekofxv": "أحبكوفإكسفي", "arcanum": "أركانوم", "neoy2k": "نيوميلينيوم", "pcracing": "سباقات_boo", "berserk": "جنوني", "baki": "باقي", "sailormoon": "سايلورمون", "saintseiya": "سانتسايا", "inuyasha": "إنيوشا", "yuyuhakusho": "يويوهاكوشو", "initiald": "اينشيتالدي", "elhazard": "الهازارد", "dragonballz": "دراغونبولزد", "sadanime": "أنمي_حزين", "darkerthanblack": "أغمق_من_الأسود", "animescaling": "هاشتاج_تنمية_الأنمي", "animewithplot": "أنمي_بحبكة", "pesci": "بِشَخشَة", "retroanime": "الأنيمي_الكلاسيكي", "animes": "أنيميات", "supersentai": "سوبرسينتاي", "samuraichamploo": "سامورايشامبلو", "madoka": "مادوكه", "higurashi": "هاشتاجهيغوراشي", "80sanime": "انمي80s", "90sanime": "انمي90s", "darklord": "داكن_الرب", "popeetheperformer": "بوبي_المنشد", "masterpogi": "ماستربوغي", "samuraix": "سمورايكس", "dbgt": "ديبجات", "veranime": "فيرانيم", "2000sanime": "أنمي_ألفين", "lupiniii": "لُبِيني", "drstoneseason1": "موسم_الدكتور_ستون_١", "rapanime": "رابانمي", "chargemanken": "شحن_الرجال", "animecover": "هاشتاج_أنمي_كفر", "thevisionofescaflowne": "رؤية_إسكا_فلوينة", "slayers": "سلاي<PERSON>ز", "tokyomajin": "توكيماجين", "anime90s": "أنمي90ات", "animcharlotte": "أنيمشارلوت", "gantz": "غنتز", "shoujo": "شوجو", "bananafish": "سمكةالموز", "jujutsukaisen": "جوجوتسوكايسن", "jjk": "جيجيكي", "haikyu": "هايكيو", "toiletboundhanakokun": "توبيخ_هانكو_تبع_الحمام", "bnha": "بنهاء", "hellsing": "هلسينغ", "skipbeatmanga": "اسكيببيتمانجا", "vanitas": "فانيتاس", "fireforce": "قوة_النار", "moriartythepatriot": "موريارتي_الوطن", "futurediary": "يوميات_المستقبل", "fairytail": "حكاية_خيالية", "dorohedoro": "دوروهدورو", "vinlandsaga": "أسطورة_فينلاند", "madeinabyss": "صنعفيهاوية", "parasyte": "بَراسيتي", "punpun": "بونبون", "shingekinokyojin": "هجومالعمالقة", "mushishi": "موشيشي", "beastars": "بيستارز", "vanitasnocarte": "فانيتاسنوكايت", "mermaidmelody": "لحن_حورية_البحر", "kamisamakiss": "كميسماكيس", "blmanga": "بلمانجا", "horrormanga": "مانغا_رعب", "romancemangas": "مانغا_الرومانسية", "karneval": "كرنفال", "dragonmaid": "دراغونميد", "blacklagoon": "البحرالأسود", "kentaromiura": "كنتاروميوارا", "mobpsycho100": "موبيسايكو100", "terraformars": "تيرافورمرز", "geniusinc": "جينيوسإنك", "shamanking": "هاشتاغالشامانكينغ", "kurokonobasket": "كوروكونوباسكت", "jugo": "جوجو", "bungostraydogs": "كلاب_الشوارع_بنجو", "jujustukaisen": "جوجوتسوكايسن", "jujutsu": "جوجوتسو", "yurionice": "يوريونيس", "acertainmagicalindex": "فهرس_سحري_معين", "sao": "ساو", "blackclover": "بلاككلوفر", "tokyoghoul": "توكيوقول", "onepunchman": "وانبانشمان", "hetalia": "هيتاليا", "kagerouproject": "كاجيرو_بروجيكت", "haikyuu": "هايكيو", "toaru": "توارُ", "crunchyroll": "كرانشيرول", "aot": "آوت", "sk8theinfinity": "سكيب_الانفينيتي", "siriusthejaeger": "سيريوس_ذا_جايغر", "spyxfamily": "سباوي_عائلة", "rezero": "ريزيدو", "swordartonline": "هاشتاجسوردآرتأونلاين", "dororo": "دورورو", "wondereggpriority": "بpriority_بيضة_العجائب", "angelsofdeath": "ملائكةالموت", "kakeguri": "كايكغوري", "dragonballsuper": "دراغونبولسوبر", "hypnosismic": "هايبرنزيل", "goldenkamuy": "غامونجولدن", "monstermusume": "مونسترميوسومي", "konosuba": "كونوسوبا", "aikatsu": "أيكاتسو", "sportsanime": "أنيمي_رياضة", "sukasuka": "سوكاسوكا", "arwinsgame": "لعبة_آروين", "angelbeats": "ضربات_الملائكة", "isekaianime": "انمي_ايسيكاي", "sagaoftanyatheevil": "ساقاوفانياثالشريرة", "shounenanime": "هاشتاغأنميشونين", "bandori": "باندوري", "tanya": "تانيا", "durarara": "دورارا", "prettycure": "علاج_الجمال", "theboyandthebeast": "الصبيوالوحش", "fistofthenorthstar": "قبضةالنجمالشمالي", "mazinger": "مازينغر", "blackbuttler": "بلاكباتلر", "towerofgod": "برج_الإله", "elfenlied": "إلفنليد", "akunohana": "آكونوهانا", "chibi": "تشيبى", "servamp": "سيرفامب", "howtokeepamummy": "كيفتحافظعلىأمك", "fullmoonwosagashite": "بكامل_مزالق_القمر", "shugochara": "شوجوكمبقى", "tokyomewmew": "توكيوميوميو", "gugurekokkurisan": "غوغوريكوكرسان", "cuteandcreepy": "مرعبولطيف", "martialpeak": "ذروة_الفنون_القتالية", "bakihanma": "باكيهانا", "hiscoregirl": "هايسكورجيرل", "orochimaru": "أوروتشيمارو", "mierukochan": "مييروكوشان", "dabi": "دابي", "johnconstantine": "جونكونستانتين", "astolfo": "أستولفو", "revanantfae": "ريفاننتفيي", "shinji": "شينجي", "zerotwo": "زيرو2", "inosuke": "إنوسوكه", "nezuko": "نيزوكو", "monstergirl": "فتاةالوحش", "kanae": "كناة", "yone": "يون", "mitsuki": "مي<PERSON><PERSON><PERSON>", "kakashi": "كاكاشي", "lenore": "لينور", "benimaru": "بينيامارو", "saitama": "سايتاما", "sanji": "سانجي", "bakugo": "باكوجو", "griffith": "غريفيث", "ririn": "ررين", "korra": "كورّاه", "vanny": "فاني", "vegeta": "فيجيتا", "goromi": "غورومي", "luci": "لُوسي", "reigen": "رايجن", "scaramouche": "سكاراموش", "amiti": "صديقتي", "sailorsaturn": "بحّارة_ساتورن", "dio": "ديو", "sailorpluto": "ساحرةبلوتو", "aloy": "الوي", "runa": "رونا", "oldanime": "أنمي_قديم", "chainsawman": "رجل_منشار", "bungoustraydogs": "بنجو_ستراي_دوغز", "jogo": "لعب", "franziska": "فرانزيسكا", "nekomimi": "نيكوميمي", "inumimi": "عيوني_يا_بوب", "isekai": "إيسيكاي", "tokyorevengers": "طوكيو_ريفنجيرز", "blackbutler": "السيرفرازالأسود", "ergoproxy": "إيرغوبروكسي", "claymore": "كلايمور", "loli": "لولي", "horroranime": "انميرعب", "fruitsbasket": "سلّة_الفواكه", "devilmancrybaby": "شيطانرجاليبكي", "noragami": "نوراغامي", "mangalivre": "مانغاليفر", "kuroshitsuji": "كوروشيتسوجي", "seinen": "سينين", "lovelive": "حب_حي", "sakuracardcaptor": "ساكورا_كارت_كابتور", "umibenoetranger": "أمي_بين_الأغراب", "owarinoseraph": "أوارينوسيراف", "thepromisedneverland": "الأرضالموعودة", "monstermanga": "هاشتاجمونسترمانغا", "yourlieinapril": "كذبتكفيأبريل", "buggytheclown": "باغي_الأرجواني", "bokunohero": "بطل_قصتي", "seraphoftheend": "سيرافنهاية", "trigun": "تريجون", "cyborg009": "سايبرغ009", "magi": "ماجي", "deepseaprisoner": "سجين_أعماق_البحر", "jojolion": "<PERSON><PERSON><PERSON><PERSON>ون", "deadmanwonderland": "ديادمانووندرلاند", "bannafish": "بنفش", "sukuna": "سكونا", "darwinsgame": "لعبة_داروين", "husbu": "هسبو", "sugurugeto": "سغوروجيتو", "leviackerman": "ليفي_أكيرمان", "sanzu": "سانزو", "sarazanmai": "سارازانماي", "pandorahearts": "قلوب_باندرورا", "yoimiya": "يويمييا", "foodwars": "حرو<PERSON>_الطعام", "cardcaptorsakura": "مكتشفي_البطاقات_ساكورا", "stolas": "ستولاس", "devilsline": "هاشتاجخطالشياطين", "toyoureternity": "إلىالأبدليك", "infpanime": "إنفبانيما", "eleceed": "إيليسييد", "akamegakill": "اكامي_ماقتله", "blueperiod": "فترة_الزرقة", "griffithberserk": "غريفيث_ميرث", "shinigami": "شينيغامي", "secretalliance": "التحالف_السري", "mirainikki": "ميرا_نيكي", "mahoutsukainoyome": "ماهوستكاينويومي", "yuki": "يوكي", "erased": "ممحاة", "bluelock": "بلولوك", "goblinslayer": "قاتل_الأقزام", "detectiveconan": "المحقق_كونان", "shiki": "شيكي", "deku": "ديكو", "akitoshinonome": "أكيتوشينونومي", "riasgremory": "ريا_غريمورى", "shojobeat": "شوجوبيات", "vampireknight": "فارس_مصاص_الدماء", "mugi": "موغي", "blueexorcist": "معذب_الأزرق", "slamdunk": "سلمدنك", "zatchbell": "زاتشبيل", "mashle": "مَشْلَة", "scryed": "سرايد", "spyfamily": "عائلة_الجاسوس", "airgear": "تجهيزات_الهواء", "magicalgirl": "فتاةسحرية", "thesevendeadlysins": "الخطاياالسابعةالقاتلة", "prisonschool": "مدرسة_السجن", "thegodofhighschool": "إله_المدرسة_الثانوية", "kissxsis": "قبلات_مع_الأخت", "grandblue": "جراندبلو", "mydressupdarling": "عروستي_الجميلة", "dgrayman": "ديغرايمن", "rozenmaiden": "روزنامين", "animeuniverse": "عالم_الأنمي", "swordartonlineabridge": "سوردأرتأونلاينأبريدج", "saoabridged": "ساو_مختصر", "hoshizora": "هوشيزوراء", "dragonballgt": "دراغونبولgt", "bocchitherock": "بوشيذاصطخّ", "kakegurui": "كاكيغوروي", "mobpyscho100": "موبسيكو100", "hajimenoippo": "خطوةبداية", "undeadunluck": "الأموات_غلط", "romancemanga": "مانغا_رومانسية", "blmanhwa": "بليمانhwa", "kimetsunoyaba": "كيميتسو_نو_يابا", "kohai": "كُهَاي", "animeromance": "رومانسيةأنمي", "senpai": "سينباي", "blmanhwas": "بلمنهواص", "animeargentina": "انمي_الأرجنتين", "lolicon": "لولكون", "demonslayertothesword": "قاتلة_الشياطين_إلى_السيف", "bloodlad": "بلودلاد", "goodbyeeri": "وداعاًيري", "firepunch": "صفعة_نارية", "adioseri": "وداعاًسيري", "tatsukifujimoto": "تاتسوكيfujimoto", "kinnikuman": "كنيكيمان", "mushokutensei": "موشوكوتينسي", "shoujoai": "شوجوآي", "starsalign": "تتaline_النجوم", "romanceanime": "رومانسية_أنمي", "tsundere": "تسوندره", "yandere": "ياندرى", "mahoushoujomadoka": "ماهوشوجومادوكه", "kenganashura": "كينغنأشورا", "saointegralfactor": "ساوإنستغرالفاكتور", "cherrymagic": "السحر_مارشملو", "housekinokuni": "بيبان_بيتكنوكوني", "recordragnarok": "سجل_راجناروك", "oyasumipunpun": "أوياسومايبايوتون", "meliodas": "ميليوداس", "fudanshi": "فودانشي", "retromanga": "ريترومانغا", "highschoolofthedead": "هايسكولأوفذاديد", "germantechno": "جيرمانتيكنو", "oshinoko": "أوشينوكو", "ansatsukyoushitsu": "انساتسوكيوشيتسو", "vindlandsaga": "فينلاندساگا", "mangaka": "مانجاكا", "dbsuper": "ديبي_سوبر", "princeoftennis": "أمير_التنس", "tonikawa": "تونيكاوا", "esdeath": "إسديث", "dokurachan": "دوكوراتشان", "bjalex": "بجالكسي", "assassinclassroom": "فصل_القتل", "animemanga": "أنيممanga", "bakuman": "باكومان", "deathparade": "موك<PERSON>_الموت", "shokugekinosouma": "شوكوجيكنو_سومة", "japaneseanime": "أنمي_ياباني", "animespace": "فضاء_الأنمي", "girlsundpanzer": "بنات_أند_بانزر", "akb0048": "اكب0048", "hopeanuoli": "نأمل_أن_تكونوا", "animedub": "دوب_أنمي", "animanga": "انيمونغا", "tsurune": "تسوروني", "uqholder": "حاملي_المفتاح", "indieanime": "أنمي_مستقل", "bungoustray": "بنجوستراي", "dagashikashi": "دغاشيكاشي", "gundam0": "غوندام0", "animescifi": "انمي_خيال_علمي", "ratman": "رجل_الجرذان", "haremanime": "هاريمانيم", "kochikame": "كوشيكايم", "nekoboy": "نيكوبوي", "gashbell": "<PERSON>ا<PERSON>بل", "peachgirl": "فتاة_الخوخ", "cavalieridellozodiaco": "فرسانالزودياك", "mechamusume": "ميتشاموسومي", "nijigasaki": "نيجيغاساكي", "yarichinbitchclub": "ياريتشينبيشكلوب", "dragonquestdai": "هاشتاجدراجونكويستداي", "heartofmanga": "قلب_المانغا", "deliciousindungeon": "لذيذ_في_المdungeon", "manhviyaoi": "منهفية_أوي", "recordofragnarok": "سجل_راجناروك", "funamusea": "فاهميةboo", "hiranotokagiura": "هيرانوتوكاجيورا", "mangaanime": "مانغاأنمي", "bochitherock": "بوغيثروك", "kamisamahajimemashita": "كاميسامةهاجيماشيتا", "skiptoloafer": "تخطي_التعلل", "shuumatsunovalkyrie": "شوماتسونوفالكييري", "tutorialistoohard": "دروسصعبةجداً", "overgeared": "بزيادةالمعدات", "toriko": "توريكو", "ravemaster": "رايفماستر", "kkondae": "كونداي", "chobits": "شوبتس", "witchhatatelier": "ورشة_قبعة_الساحرة", "lansizhui": "لانسزهوي", "sangatsunolion": "سانغتسونوليون", "kamen": "كامِن", "mangaislife": "مانغا_هي_الحياة", "dropsofgod": "قطرات_الله", "loscaballerosdelzodia": "فرسان_الأبراج", "animeshojo": "أنميشوجو", "reverseharem": "هاريم_عكسي", "saintsaeya": "سانتسايا", "greatteacheronizuka": "معلم_عظيم_أونيزكا", "gridman": "غريدمان", "kokorone": "كوكوروني", "soldato": "جندي", "mybossdaddy": "بابا_مديري", "gear5": "جير5", "grandbluedreaming": "حلم_الأزرق_الكبير", "bloodplus": "بلودبلس", "bloodplusanime": "دمأنمي", "bloodcanime": "دم_الرسوم_المتحركة", "bloodc": "دمك", "talesofdemonsandgods": "حكاياتالشياطينوالآلهة", "goreanime": "أنمي_دموي", "animegirls": "فتيات_الأنمي", "sharingan": "شارينغان", "crowsxworst": "غربان_أسوأ", "splatteranime": "أنمي_بقعة", "splatter": "بصقة", "risingoftheshieldhero": "صعود_بطل_الدرع", "somalianime": "أنمي_صومالي", "riodejaneiroanime": "ريو_دي_جانيرو_أنيم", "slimedattaken": "مطبخ_بثة", "animeyuri": "أنمييوري", "animeespaña": "أنيمي_إسبانيا", "animeciudadreal": "انيمي_سيوداد_ريال", "murim": "مُوريم", "netjuunosusume": "نتجوونوسسومي", "childrenofthewhales": "أطفالالحيتان", "liarliar": "كذابكذاب", "supercampeones": "سوبركامبيونز", "animeidols": "أصنام_الأنمي", "isekaiwasmartphone": "إيسكاي_كان_عبر_السمارتفون", "midorinohibi": "ميدورينوهبي", "magicalgirls": "فتيات_ساحرات", "callofthenight": "نداءالليل", "bakuganbrawler": "بروزر_باكوجان", "bakuganbrawlers": "باكوجانبروالرز", "natsuki": "ناتسuki", "mahoushoujo": "ماهوشوجو", "shadowgarden": "ظلالحديقة", "tsubasachronicle": "تسوبا_كرونيكل", "findermanga": "مانجا_فايندر", "princessjellyfish": "أميرةقناديلالجيلي", "kuragehime": "كوراكيهيمه", "paradisekiss": "قبلة_الجنة", "kurochan": "كوروشان", "revuestarlight": "مراجعةستارلايت", "animeverse": "عالم_الأنمي", "persocoms": "بِرسوكومز", "omniscientreadersview": "وجهةنظرالقارئالعارف", "animecat": "كات_الأنمي", "animerecommendations": "توصيات_انمي", "openinganime": "افتتاح_الأنمي", "shinichirowatanabe": "شينيشيروواتانابي", "uzumaki": "أزوماكي", "myteenromanticcomedy": "كوميديا_الرومانسية_في_سن_المراهقة", "evangelion": "إيفانجيون", "gundam": "غندام", "macross": "ماكروس", "gundams": "غن<PERSON><PERSON><PERSON>ز", "voltesv": "فولتيزفي", "giantrobots": "روبوتات_عملاقة", "neongenesisevangelion": "نيونجينيسيسإيفانجيليون", "codegeass": "كودجياس", "mobilefighterggundam": "موبيلفايتربوغاندم", "neonevangelion": "نيوغيفانجيليون", "mobilesuitgundam": "موبايلسوتغاندام", "mech": "مي<PERSON><PERSON>", "eurekaseven": "يوريكا7", "eureka7": "يوركَا7", "thebigoanime": "البغوأنمي", "bleach": "بليتش", "deathnote": "مذكرةالموت", "cowboybebop": "كوبويبيبوب", "jjba": "جيجيبياي", "jojosbizarreadventure": "مغامرة_جوجو_الغريبة", "fullmetalalchemist": "الخيال_اللامعدني", "ghiaccio": "ج<PERSON>ي<PERSON>", "jojobizarreadventures": "مغامراتبوجوباالغريبة", "kamuiyato": "كامويوت", "militaryanime": "أنمي_عسكري", "greenranger": "راعي_البيئة", "jimmykudo": "جيميكودو", "tokyorev": "طوكيو_ريف", "zorro": "زورّو", "leonscottkennedy": "ليونسكوتكينيدي", "korosensei": "كوروسنسي", "starfox": "ستارفوكس", "ultraman": "ألترامان", "salondelmanga": "صالونالديمانغا", "lupinthe3rd": "لوبين_الثالث", "animecity": "مدينةالأنمي", "animetamil": "أنيمي_تاميلي", "jojoanime": "جوجو_أنمي", "naruto": "ناروتو", "narutoshippuden": "ناروتو_شيبودن", "onepiece": "ونبيس", "animeonepiece": "مانجا_ون_بيس", "dbz": "ديبيزي", "dragonball": "دراغونبول", "yugioh": "يوغيأوه", "digimon": "ديجمون", "digimonadventure": "مغامرة_ديجيمون", "hxh": "هاه", "highschooldxd": "هايسكولديإكسدي", "goku": "غوكو", "broly": "برولي", "shonenanime": "أنمي_الشونن", "bokunoheroacademia": "بوكُنوهيروأكاديميا", "jujustukaitsen": "جوجستوكيتسن", "drstone": "دكتور_ستون", "kimetsunoyaiba": "كيمتسونويايبَا", "shonenjump": "شونينجامب", "otaka": "أوتاكا", "hunterxhunter": "هاشتاج_هانتر__هانتر", "mha": "مها", "demonslayer": "قاتل_الشياطين", "hinokamikagurademonsl": "هينوكاميكاكورا_شياطين", "attackontitan": "هجومالعمالقة", "erenyeager": "إريني_يغر", "myheroacademia": "أكاديمية_بطلي", "boruto": "بورتو", "rwby": "رواي", "dandadan": "دنددن", "tomodachigame": "توموداشي_غيم", "akatsuki": "أكاتسوكي", "surveycorps": "حملة_استطلاعات", "onepieceanime": "هاشتاجونبيسأنمي", "attaquedestitans": "هجومالعمالقة", "theonepieceisreal": "القطع_الوحدة_حقيقية", "revengers": "الثأرية", "mobpsycho": "موبسايكو", "aonoexorcist": "آونوإكسورسيست", "joyboyeffect": "تأثير_جوَيبوي", "digimonstory": "قصةديجمون", "digimontamers": "ديجيمونتميرز", "superjail": "سوبرجيل", "metalocalypse": "موتاكونوبا", "shinchan": "شينشان", "watamote": "واتاموتي", "uramichioniisan": "وراميشيونيسان", "uruseiyatsura": "أوروسياياتسورا", "gintama": "جينتاما", "ranma": "رانما", "doraemon": "دوريمون", "gto": "غيتو", "ouranhostclub": "نادي_الأوران_الضيافة", "flawlesswebtoon": "ويبتوونبلاعيوب", "kemonofriends": "كيمونوفريندز", "utanoprincesama": "أوتانوبريسنسا", "animecom": "انميكم", "bobobobobobobo": "بوبوبوبوبوبو", "yuukiyuuna": "يوكيونا", "nichijou": "نيتشيو", "yurucamp": "يوروكامب", "nonnonbiyori": "نونونبيوري", "flyingwitch": "ساحرة_طائرة", "wotakoi": "ووتاكوي", "konanime": "كوناڤيومي", "clannad": "كلاناد", "justbecause": "بسكده", "horimiya": "هوريميا", "allsaintsstreet": "شارعallsaints", "recuentosdelavida": "قصص_الحياة"}