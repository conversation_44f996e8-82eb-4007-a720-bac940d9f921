{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologija", "cognitivefunctions": "kognitivnefunkcije", "psychology": "psihologija", "philosophy": "filozofija", "history": "zgodovina", "physics": "fizika", "science": "znanost", "culture": "kultura", "languages": "jeziki", "technology": "tehnologija", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrološkimems", "enneagrammemes": "enneagrammemes", "showerthoughts": "zbiranjeidej", "funny": "s<PERSON>š<PERSON>", "videos": "videopos<PERSON><PERSON>", "gadgets": "gadgets", "politics": "politika", "relationshipadvice": "ljubezenskinas<PERSON><PERSON>", "lifeadvice": "življenjskinasveti", "crypto": "crypto", "news": "novice", "worldnews": "novice<PERSON><PERSON><PERSON><PERSON>", "archaeology": "arheologija", "learning": "učenje", "debates": "razprave", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "vesolje", "meditation": "meditacija", "mythology": "mitologija", "art": "umetnost", "crafts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dance": "ples", "design": "<PERSON><PERSON><PERSON>", "makeup": "makeup", "beauty": "lepota", "fashion": "moda", "singing": "petje", "writing": "pisanje", "photography": "fotografiranje", "cosplay": "cosplay", "painting": "slikarstvo", "drawing": "risanje", "books": "knjige", "movies": "filmi", "poetry": "poezija", "television": "televizija", "filmmaking": "filmskoustvarjanje", "animation": "animacija", "anime": "anime", "scifi": "znanstvenafantastika", "fantasy": "domišljija", "documentaries": "dokumentarci", "mystery": "skrivnost", "comedy": "komedija", "crime": "kriminalno", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "grozljivka", "romance": "romance", "realitytv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "ak<PERSON><PERSON>", "music": "glasba", "blues": "blues", "classical": "klasika", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronskaglasba", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indierock", "jazz": "jazz", "kpop": "kpop", "latin": "<PERSON><PERSON>š<PERSON><PERSON>", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "potovanje", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "festivali", "museums": "muzeji", "standup": "standup", "theater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outdoors": "aktivnostinaprostem", "gardening": "vrtnarjenje", "partying": "zabave", "gaming": "gaming", "boardgames": "družabneigre", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ah", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "hrana", "baking": "peka", "cooking": "kuh<PERSON><PERSON>", "vegetarian": "veget<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "veganskahrana", "birds": "ptice", "cats": "<PERSON><PERSON><PERSON>", "dogs": "psi", "fish": "riba", "animals": "živali", "blacklivesmatter": "blacklivesmatter", "environmentalism": "okoljevarstvo", "feminism": "feminizem", "humanrights": "človekovepravice", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "prostovoljstvo", "sports": "šport", "badminton": "badminton", "baseball": "bejzbol", "basketball": "<PERSON><PERSON><PERSON><PERSON>", "boxing": "boks", "cricket": "kriket", "cycling": "kolesarjenje", "fitness": "fitnes", "football": "nogomet", "golf": "golf", "gym": "vadba", "gymnastics": "gimnast<PERSON>", "hockey": "hokej", "martialarts": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "na<PERSON>z<PERSON><PERSON><PERSON>", "running": "tek", "skateboarding": "skateboarding", "skiing": "smučanje", "snowboarding": "snowboarding", "surfing": "<PERSON><PERSON><PERSON>", "swimming": "plavanje", "tennis": "tenis", "volleyball": "od<PERSON><PERSON><PERSON>", "weightlifting": "dvigovanjeuteži", "yoga": "joga", "scubadiving": "potapljanje", "hiking": "pohodništvo", "capricorn": "kozorog", "aquarius": "vodnar", "pisces": "riba", "aries": "oven", "taurus": "bik", "gemini": "dvojčka", "cancer": "rak", "leo": "lev", "virgo": "de<PERSON><PERSON>", "libra": "tehtnica", "scorpio": "škorpijon", "sagittarius": "strelec", "shortterm": "kratkastvar", "casual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "dolgotrajnazveza", "single": "samski", "polyamory": "poliamorijalubezenskazveza", "enm": "ethičnanemogamija", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gej", "lesbian": "lezbijka", "bisexual": "biseksualen", "pansexual": "panseksualen", "asexual": "aseksualen", "reddeadredemption2": "rdečnedeljnospravičilo2", "dragonage": "zmajevojedro", "assassinscreed": "ubijalčevkodex", "saintsrow": "sodnikimesta", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "opazovalci", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kraljevamisija", "soulreaver": "soulreaver", "suikoden": "su<PERSON><PERSON>", "subverse": "subverzija", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelike", "syberia": "sibir<PERSON>", "rdr2": "rdr2", "spyrothedragon": "spyrogedragonom", "dragonsdogma": "zmajevodo", "sunsetoverdrive": "sončnizahodfuzija", "arkham": "arkham", "deusex": "bogobjekta", "fireemblemfates": "ognjemetjausode", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "cezavajskevojne", "openworld": "od<PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "herojiv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "potapljanjevropah", "jetsetradio": "jetsetradio", "tribesofmidgard": "plemenasrednjegasveta", "planescape": "nacrtokrajine", "lordsoftherealm2": "gospodarji_kraljestva2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "barvnivrh", "medabots": "medabots", "lodsoftherealm2": "lodsofhrealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "potopnesims", "okage": "okage", "juegoderol": "igralnarol", "witcher": "čarodej", "dishonored": "osestranjeno", "eldenring": "eldenskoobročje", "darksouls": "temniduši", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "izpad", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "starazrna", "modding": "modding", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "pot<PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyšola", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidirazpoloženje", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "zaljubljenvljube", "otomegames": "otomeigre", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinačasa", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirjespremstvo", "dimension20": "dimenzija20", "gaslands": "gasilnice", "pathfinder": "iskalecpoti", "pathfinder2ndedition": "potnik2izdaja", "shadowrun": "sencenazad<PERSON><PERSON>", "bloodontheclocktower": "krvnauraunapred", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitevnapad", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "enkratnaakcija", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "tvojpotrdnikmori", "persona3": "persona3", "rpghorror": "rpggroza", "elderscrollsonline": "starekolesaonline", "reka": "reka", "honkai": "honkai", "marauders": "razbojniki", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgbesedilo", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "shelterzapad", "gurps": "gurps", "darkestdungeon": "najtemnejšaklet", "eclipsephase": "eclipsefazа", "disgaea": "disgaea", "outerworlds": "zunanjisvet<PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "družinskipokali", "skullgirls": "lobanjepunce", "nightcity": "nočnomesto", "hogwartslegacy": "hogs<PERSON><PERSON><PERSON>", "madnesscombat": "norijaoboroženeborbe", "jaggedalliance2": "nategnjenizavezništvo2", "neverwinter": "nikolivetruje", "road96": "cesta96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "pozabljenisveti", "dragonlance": "zmajevipon", "arenaofvalor": "arenausode", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "otroksvetlobe", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "razdelitev2", "lineage2": "lineage2", "digimonworld": "digimonsvet", "monsterrancher": "zverinar", "ecopunk": "ecopank", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanvesolje", "fracturedthrones": "zlomljenitroni", "horizonforbiddenwest": "horizonnapovedanizahod", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltazeleno", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "p<PERSON><PERSON><PERSON><PERSON>", "lastepoch": "zad<PERSON><PERSON><PERSON>hе", "starfinder": "iskaleczvezd", "goldensun": "zlatosonce", "divinityoriginalsin": "božanstvoizvirngreha", "bladesinthedark": "<PERSON>živ<PERSON>i", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "kiberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrdeče", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "padlired", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlozemlja", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "djavolovpreživetje", "oldschoolrunescape": "starasolač<PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božanskost", "pf2": "pf2", "farmrpg": "kmetijskorpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON>", "adventurequest": "<PERSON><PERSON><PERSON>š<PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "igralnolegende", "roleplayinggames": "igralneigre", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "zgodbeglasbe", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "moj<PERSON><PERSON>", "sacredunderworld": "sacredunderworld", "chainedechoes": "ve<PERSON>ž<PERSON><PERSON><PERSON><PERSON>", "darksoul": "temnodusa", "soulslikes": "du<PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "časovnik", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "raz<PERSON><PERSON>", "tibia": "tibija", "thedivision": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "volkodlakvapokalipsi", "aveyond": "aveyond", "littlewood": "maloplesnjak", "childrenofmorta": "otrocimorte", "engineheart": "motorčrke", "fable3": "bajka3", "fablethelostchapter": "pravljicakizgubljenopoglavje", "hiveswap": "hiveswap", "rollenspiel": "igraoblačjenja", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "edenve<PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "zvezdnopolje", "oldschoolrevival": "starišolskiopohod", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworlds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kraljestvo_srca1", "ff9": "ff9", "kingdomheart2": "kraljestvosrc2", "darknessdungeon": "temnizapor", "juegosrpg": "rpgigre", "kingdomhearts": "kraljestvotosrca", "kingdomheart3": "kraljestvosrca3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "malkavskiklani", "harvestella": "berba", "gloomhaven": "gloomhaven", "wildhearts": "divjasrca", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "nebozarcadije", "shadowhearts": "se<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosija", "pennyblood": "pennyblood", "breathoffire4": "sapaognja4", "mother3": "mama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "igralnevloge", "roleplaygame": "igreudajan<PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "čarovniconarava", "harrypottergame": "harry<PERSON><PERSON><PERSON>ra", "pathfinderrpg": "ojupotnikomrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirskamasquerada", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "čarobnoletenje", "dragonageorigins": "zmajevadomovina", "chronocross": "kronokriž", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "lo<PERSON>ci<PERSON>trovsvet", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "senčnasrcazaveza", "bladesoul": "<PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "krkraljestvokdoše", "awplanet": "awplanet", "theworldendswithyou": "svetsezaključišzabojem", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "umirajočesvetloba2", "finalfantasytactics": "finalfantasytaktike", "grandia": "grandia", "darkheresy": "temnaherezija", "shoptitans": "trgovinsketitani", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "zemeljskamagija", "blackbook": "črnookno", "skychildrenoflight": "nebes<PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "svetazlataprevleka", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gotskaigra", "scarletnexus": "škarletninexus", "ghostwiretokyo": "duhovnapovezavatokio", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "zvezdneželeznice", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "točkastiklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON>", "freeside": "prostoobrobje", "epic7": "epic7", "ff7evercrisis": "ff7zavednakriza", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "smrtnodaltekanadi", "palladium": "palaadium", "knightjdr": "vitezjdr", "monsterhunter": "lovcamonstrov", "fireemblem": "<PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacija", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "neunarnigames", "tacticalrpg": "taktičnirpg", "mahoyo": "mahoyo", "animegames": "animeigre", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "pojedecbogov", "diluc": "diluc", "venti": "venti", "eternalsonata": "večnisonata", "princessconnect": "princesconnect", "hexenzirkel": "čarovniškikrog", "cristales": "krist<PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "žepnamodrost", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindijski", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaupuščskih", "fifa14": "fifa14", "midlaner": "srednjekaljo<PERSON>", "efootball": "elektronskinogomet", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "kiberšport", "crazyraccoon": "nor<PERSON>a", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitivno", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "polživljenje", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "ekipnautrjena2", "everlastingsummer": "naj<PERSON><PERSON><PERSON>_poletna_sezona", "goatsimulator": "goatsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "svobodanplanet", "transformice": "transformice", "justshapesandbeats": "justshapesandbeats", "battlefield4": "bojišče4", "nightinthewoods": "nočvzelenjavah", "halflife2": "polživljenjej2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "tvegajadež2", "metroidvanias": "metroidvanije", "overcooked": "<PERSON><PERSON><PERSON><PERSON>", "interplanetary": "medplanetarno", "helltaker": "helltaker", "inscryption": "napovedovanje", "7d2d": "7d2d", "deadcells": "mrtvikcelice", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "škegajazbinadgradnja", "foxhole": "foxyhole", "stray": "<PERSON><PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "bojišče1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oko", "blackdesert": "črnavsakraj", "tabletopsimulator": "simulatorjenaladjah", "partyhard": "zabavajmose", "hardspaceshipbreaker": "trdoprostorskirazbijač", "hades": "hades", "gunsmith": "or<PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "ujetazjesterjem", "dinkum": "dinkum", "predecessor": "predhodnik", "rainworld": "dežnizdruženje", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "zoravojne", "minionmasters": "minionmasters", "grimdawn": "temnopovrisevanje", "darkanddarker": "temnejšinatemnejše", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "duš<PERSON><PERSON><PERSON>je", "datingsims": "zmenkarskesimulecije", "yaga": "yaga", "cubeescape": "kockaposvet", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novimesto", "citiesskylines": "mestneobzorja", "defconheavy": "<PERSON>con<PERSON><PERSON><PERSON>", "kenopsia": "kenopsija", "virtualkenopsia": "virtualkenopsija", "snowrunner": "snežnitek", "libraryofruina": "knjiznicaruina", "l4d2": "l4d2", "thenonarygames": "nenavadnигре", "omegastrikers": "omegastrikerji", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "mirnaplastičnar<PERSON>čka", "battlebit": "bitkavanje", "ultimatechickenhorse": "najboljšeavokadojajce", "dialtown": "klicnaselja", "smileforme": "nasmejmese", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermesnjak", "tinnybunny": "tinnybunny", "cozygrove": "udobnigaj", "doom": "družbenaapokalipsa", "callofduty": "klicdolžnosti", "callofdutyww2": "klicdolžnostiww2", "rainbow6": "rajbovn6", "apexlegends": "apexlegends", "cod": "koda", "borderlands": "meje", "pubg": "pubg", "callofdutyzombies": "klicodutyzombijev", "apex": "apeks", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "b<PERSON><PERSON><PERSON>", "farcrygames": "farcryigre", "paladins": "paladins", "earthdefenseforce": "zemeljskoodporvforces", "huntshowdown": "lovnatežav", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "pridružiseekipi", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "smrtnopovezovanje", "b4b": "b4b", "codwarzone": "codvojnazone", "callofdutywarzone": "klicdolgživevojna", "codzombies": "kliczombijev", "mirrorsedge": "zrcalnarob", "divisions2": "divizije2", "killzone": "ubijalskaodpravljalnica", "helghan": "hel<PERSON>", "coldwarzombies": "hladnofrontazombiji", "metro2033": "metrop2033", "metalgear": "metalgear", "acecombat": "aslet<PERSON><PERSON>", "crosscode": "prečkanjekode", "goldeneye007": "zlatioko007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "sodobnoboditeljstvo", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalnašorka", "primalcarnage": "primalcarnage", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "back4blood", "warframe": "<PERSON><PERSON><PERSON>vir", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "sistemskišok", "valkyriachronicles": "valkyrijinekronike", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "jamazgodba", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "kolesazavojna", "mwo": "mwo", "division2": "divizija2", "tythetasmaniantiger": "tytetasmanijskitiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "vstopivgungeon", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernavojna2", "blackops1": "blackops1", "sausageman": "mestnjak", "ratchetandclank": "ratchetinclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON>", "warface": "vojniobraz", "crossfire": "križnaognja", "atomicheart": "atomskosrce", "blackops3": "blackops3", "vampiresurvivors": "vampirjevpreživetje", "callofdutybatleroyale": "klicdolžnostiobratnoboj", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "svoboda", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frg", "tinytina": "malatina", "gamepubg": "igripubg", "necromunda": "nekrimunda", "metalgearsonsoflibert": "metalgearsonovsvobode", "juegosfps": "fpsigre", "convertstrike": "pretvor<PERSON>vu<PERSON><PERSON><PERSON><PERSON>", "warzone2": "vojna2", "shatterline": "razbitičrtni", "blackopszombies": "blackopszombies", "bloodymess": "krvavišpeh", "republiccommando": "republikakommando", "elitedangerous": "eliteogroženi", "soldat": "v<PERSON>k", "groundbranch": "zemeljskiodsek", "squad": "ekipa", "destiny1": "usoda1", "gamingfps": "gamingfps", "redfall": "rdečipad", "pubggirl": "pubgdekleta", "worldoftanksblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "klicoddolžnostičrnidaske", "enlisted": "vključeni", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "oklepnijedro", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytininačudežnašporta", "halo2": "halo2", "payday2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cs16": "cs16", "pubgindonesia": "pubgindonezija", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromunija", "empyrion": "empyrion", "pubgczech": "pubgčeska", "titanfall2": "titanfall2", "soapcod": "milozoom", "ghostcod": "duhovnišpeh", "csplay": "<PERSON><PERSON><PERSON><PERSON>", "unrealtournament": "nerealnemturnirju", "callofdutydmz": "klicdolžnostidmz", "gamingcodm": "gamingcodm", "borderlands2": "meje2", "counterstrike": "skupinskaudarjanje", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "klicdutymw2", "quakechampions": "potresnišampion", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonwhite": "neonwhite", "remnant": "<PERSON><PERSON><PERSON>", "azurelane": "<PERSON><PERSON><PERSON><PERSON>ž<PERSON>", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "vrnitev", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "sencečlovek", "quake2": "potres2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "rdečmrtev", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "bojiščeb3", "lostark": "izgubljenark", "guildwars2": "bojkadruštva2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "mor<PERSON><PERSON><PERSON>", "rust": "rja", "conqueronline": "osvojisplet", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "vojneladje", "dayofdragons": "dan_<PERSON><PERSON><PERSON>v", "warthunder": "warthunder", "flightrising": "letalskunki", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON>", "phantasystaronline2": "fantazijskizaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "<PERSON><PERSON>a", "aion": "aion", "toweroffantasy": "towerodfantazije", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "rdecdeadonline", "superanimalroyale": "superživalskaroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "vitezonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON>zaizaka", "dragonageinquisition": "dragonageinquisition", "codevein": "kodožilo", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "banda", "newworld": "noves<PERSON>", "blackdesertonline": "črnaskupnostonline", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "častkraljem", "fivem": "fivem", "starwarsbattlefront": "zvezdnevojevanjeboji", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "zvezdnevojnebitkafront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponitevmesto", "3dchat": "3dklepet", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasika", "worldofwarcraft": "svetvojnovojne", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "pepelstvarjenja", "riotmmo": "mmoupor", "silkroad": "svilnacesta", "spiralknights": "spiralknighti", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "albiononline": "albiononline", "bladeandsoul": "rezi<PERSON><PERSON><PERSON>š<PERSON>", "evony": "evony", "dragonsprophet": "zmajevskiprerok", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "večigralec", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "rasttopija", "starwarsoldrepublic": "zvezdnevojnesstararepublika", "grandfantasia": "velikafantazija", "blueprotocol": "modriprotokol", "perfectworld": "popolnsvet", "riseonline": "dvignionline", "corepunk": "corepunk", "adventurequestworlds": "pustolovščinasključno", "flyforfun": "letizazabavo", "animaljam": "živalskijam", "kingdomofloathing": "kraljestvoopozorjenja", "cityofheroes": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalnabitka", "streetfighter": "ulicnibojevnik", "hollowknight": "praznivalitez", "metalgearsolid": "metalgearsolid", "forhonor": "začast", "tekken": "tekken", "guiltygear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "ulicibojevnik6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "duhovačast", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "ulic<PERSON><PERSON>ž<PERSON>", "mkdeadlyalliance": "mksmrtonosnoalliance", "nomoreheroes": "nobenihjunakovveč", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "kotzmaj", "retrofightinggames": "retrofightingigre", "blasphemous": "blasfemično", "rivalsofaether": "rivalidezrak", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superslam", "mugen": "mugen", "warofthemonsters": "v<PERSON><PERSON><PERSON><PERSON><PERSON>in", "jogosdeluta": "igralnebitke", "cyberbots": "<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "oklepnibojevniki", "finalfight": "zadnjiboj", "poweredgear": "m<PERSON><PERSON><PERSON><PERSON>va", "beatemup": "<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "borilneigre", "killerinstinct": "ubijalskiinstinkt", "kingoffigthers": "kraljipogodb", "ghostrunner": "<PERSON><PERSON><PERSON>", "chivalry2": "viteštvo2", "demonssouls": "demonsoul", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "krivečpodsvoja", "hollowknightsequel": "hollowknightnadaljevanje", "hollowknightsilksong": "prazni<PERSON><PERSON><PERSON><PERSON><PERSON>", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongnovice", "silksong": "svilnapesem", "undernight": "podnočje", "typelumina": "tipolumina", "evolutiontournament": "turnir_evolucije", "evomoment": "evomoment", "lollipopchainsaw": "lollipopverižice", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "zgodbeofberseria", "bloodborne": "krvnapodlaga", "horizon": "horizont", "pathofexile": "potekizgnanstva", "slimerancher": "slimeč<PERSON><PERSON>č", "crashbandicoot": "crashbandicoot", "bloodbourne": "krvavamladina", "uncharted": "neizmerno", "horizonzerodawn": "horizonzerozora", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "zadnjiznas", "infamous": "slaven", "playstationbuddies": "playstationprijatelji", "ps1": "ps1", "oddworld": "norsezemske", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "peklenostjeodprta", "gta4": "gta4", "gta": "gta", "roguecompany": "upornikpodjetje", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "bogovsveta", "gris": "gris", "trove": "zakladnica", "detroitbecomehuman": "detroitpostanečlovek", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "dočimzora", "touristtrophy": "turističnatrofej", "lspdfr": "lspdfr", "shadowofthecolossus": "senc<PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "izpadnotranjegaavtomobilizma", "fivepd": "petpd", "tekken7": "tekken7", "devilmaycry": "hudičnejokal", "devilmaycry3": "hudičnejoče3", "devilmaycry5": "hudičnieče5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samurajevibor<PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON>ad<PERSON><PERSON><PERSON>", "soulblade": "dušnaorožja", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "možnaprestrezanje", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "senčnesrci2zaveza", "pcsx2": "pcsx2", "lastguardian": "zadnjičuvajitelj", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "žuralnažival", "warharmmer40k": "vojnowarhammer40k", "fightnightchampion": "bojnanocprvak", "psychonauts": "psihonauti", "mhw": "mhw", "princeofpersia": "princpersije", "theelderscrollsskyrim": "starijizapiskiskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "starirolniškeigre", "gxbox": "gxbox", "battlefront": "bojiščeborbe", "dontstarvetogether": "neumiraj<PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "zvezdnoobljubljen", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "prebivalnik", "americanmcgeesalice": "ameriškialicenmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakraljestvenstev", "fable2": "bajka2", "xboxgamepass": "xboxgamepass", "undertale": "podzemlje", "trashtv": "č<PERSON><PERSON><PERSON><PERSON>jetv", "skycotl": "neboješnica", "erica": "erica", "ancestory": "predniki", "cuphead": "cuphead", "littlemisfortune": "malanesreča", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "poštnimaturantska", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "zunanjivrstam", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultjagnje", "duckgame": "raščakanigra", "thestanleyparable": "stanlejevapripoved", "towerunite": "towerunite", "occulto": "okultno", "longdrive": "dolgavožnja", "satisfactory": "zadovoljivo", "pluviophile": "ljubiteljdežja", "underearth": "podzemlje", "assettocorsa": "assettocorsa", "geometrydash": "geometrijskiskok", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "duhovnemotnjenje", "darkdome": "temnohuba", "pizzatower": "<PERSON><PERSON><PERSON>", "indiegame": "neodvisnaigra", "itchio": "itchio", "golfit": "golfaj", "truthordare": "resnicaaliizziv", "game": "igra", "rockpaperscissors": "rockpa<PERSON><PERSON>š<PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "izziv", "scavengerhunt": "zakladnaigra", "yardgames": "igralnicedvorane", "pickanumber": "izberinštevilko", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "kockastišoma", "cosygames": "udobnejigames", "datinggames": "zmenkovneigre", "freegame": "brezplačnagra", "drinkinggames": "pijanskeigre", "sodoku": "sudoku", "juegos": "igre", "mahjong": "mahjong", "jeux": "igra", "simulationgames": "simulacijskeig<PERSON>", "wordgames": "besed<PERSON><PERSON>re", "jeuxdemots": "igramibesed", "juegosdepalabras": "igricebesed", "letsplayagame": "igravajmoigrico", "boredgames": "igralnost", "oyun": "igra", "interactivegames": "interaktivneigre", "amtgard": "amtgard", "staringcontests": "gledalskebitke", "spiele": "igramo", "giochi": "igra", "geoguessr": "geoguessr", "iphonegames": "iphoneigre", "boogames": "boogames", "cranegame": "igracranja", "hideandseek": "skritiiskanje", "hopscotch": "skakanje", "arcadegames": "arkadneigre", "yakuzagames": "yakuzagames", "classicgame": "klasičnaigra", "mindgames": "miselnesigre", "guessthelyric": "ugan<PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "igraromance", "yanderegames": "yander<PERSON><PERSON><PERSON>", "tonguetwisters": "jezičnezaplete", "4xgames": "4xigre", "gamefi": "gamefi", "jeuxdarcades": "igreizarcade", "tabletopgames": "namizneigre", "metroidvania": "metroidvania", "games90": "igre90", "idareyou": "izzivamte", "mozaa": "mozaa", "fumitouedagames": "fumiigames", "racinggames": "dirkalneigre", "ets2": "ets2", "realvsfake": "resničnoprotiponarejenemu", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "igravredneti", "onlinegames": "spletneigre", "jogosonline": "igrajonline", "writtenroleplay": "pisanjerolskihiger", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "sodelovalneigre", "jenga": "jenga", "wiigames": "wiigames", "highscore": "visoki<PERSON><PERSON>", "jeuxderôles": "igralnevloge", "burgergames": "burgerigre", "kidsgames": "<PERSON><PERSON>š<PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwcrnopodr<PERSON><PERSON>", "jeuconcour": "igralnatekmovanje", "tcgplayer": "tcgplayer", "juegodepreguntas": "igralnaprip<PERSON>ša<PERSON>", "gioco": "igra", "managementgame": "igraobv<PERSON><PERSON><PERSON>", "hiddenobjectgame": "skritavoznapovina", "roolipelit": "igralninezgodbe", "formula1game": "formula1igra", "citybuilder": "mestotvoritelj", "drdriving": "drdriving", "juegosarcade": "igrearka<PERSON>", "memorygames": "spominskeigre", "vulkan": "vulkan", "actiongames": "akcijskeigre", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "fliperji", "oldgames": "stareigre", "couchcoop": "<PERSON><PERSON><PERSON><PERSON>", "perguntados": "vprašanih", "gameo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lasergame": "laserskaigra", "imessagegames": "igemes", "idlegames": "igralneigre", "fillintheblank": "zapolni_praznino", "jeuxpc": "igranaračunalniku", "rétrogaming": "rétrogaming", "logicgames": "logičneigre", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "podzemnadeska", "jeuxdecelebrite": "igreznamenitnosti", "exitgames": "izhodneigre", "5vs5": "5na5", "rolgame": "roligra", "dashiegames": "dashiegames", "gameandkill": "igramoinšijem", "traditionalgames": "tradicionalneigre", "kniffel": "kniffel", "gamefps": "igrafps", "textbasedgames": "igralniteks<PERSON>", "gryparagrafowe": "gryparagrafove", "fantacalcio": "futbolfantazija", "retrospel": "retroigra", "thiefgame": "igralnacrapalec", "lawngames": "igricenatravniku", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stolninogomet", "tischfußball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spieleabende": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "prijateljskeigre", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "pobegigre", "thiefgameseries": "igralnizaslabičine", "cranegames": "žerjavigre", "játék": "igra", "bordfodbold": "b<PERSON>žičnifudbal", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "magi", "cargames": "avtogames", "onlineplay": "spletnoblanje", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "igraln<PERSON><PERSON><PERSON>", "pursebingos": "torbafarbinga", "randomizer": "naključnilež", "msx": "msx", "anagrammi": "anagrami", "gamespc": "igrepc", "socialdeductiongames": "družabneigrededukcije", "dominos": "dominos", "domino": "domino", "isometricgames": "izometričneigre", "goodoldgames": "dobri_stari_igre", "truthanddare": "resnicaizivljenje", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "virtualneigre", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "brezplačnozabavno", "fantasygame": "fantazijskagames", "gryonline": "igranie<PERSON>", "driftgame": "driftigra", "gamesotomes": "igralnajopem", "halotvseriesandgames": "halotvserijeigralnice", "mushroomoasis": "gobovaoaza", "anythingwithanengine": "vsezazmotorjem", "everywheregame": "igralnavsepovsod", "swordandsorcery": "mečinamagija", "goodgamegiving": "doberigraj<PERSON>", "jugamos": "igramo", "lab8games": "lab8igre", "labzerogames": "labzerogames", "grykomputerowe": "igricezaračunalnik", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniaturneigre", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "samospoštovanjeigranje", "gamemodding": "gamedodajanje", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "dobbelspellen", "spelletjes": "igra", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "ka<PERSON><PERSON>i", "singleplayer": "enoigralec", "coopgame": "kooperativnaigra", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "glavnazamet", "kingdiscord": "kraljdiscord", "scrabble": "scrabble", "schach": "<PERSON>ah", "shogi": "<PERSON><PERSON>i", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "dedščinapandemije", "camelup": "kamelagor", "monopolygame": "monopolygame", "brettspiele": "družabneigre", "bordspellen": "družabneigre", "boardgame": "d<PERSON><PERSON>abnaigra", "sällskapspel": "družabneigre", "planszowe": "planskave", "risiko": "risiko", "permainanpapan": "igrajmomizneigre", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "krvavokotliček", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "povezanjekvadrat", "heroquest": "heroquest", "giochidatavolo": "igralneizdelke", "farkle": "farkle", "carrom": "karom", "tablegames": "miz<PERSON><PERSON><PERSON>", "dicegames": "kockanjeigre", "yatzy": "jat<PERSON>", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "igrajmozabavo", "deskgames": "mizaigre", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvel<PERSON>risisp<PERSON><PERSON><PERSON>", "cosmicencounter": "kozmikspopad", "creationludique": "ustvarjalnarazvada", "tabletoproleplay": "namizneroliganje", "cardboardgames": "kartezaigre", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "igrajigra", "infinitythegame": "neskončnostigame", "kingdomdeath": "kraljevstvonamrti", "yahtzee": "yahtzee", "chutesandladders": "toboganiinlestevnice", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "družabnigeri", "planszówki": "planskice", "rednecklife": "životredneka", "boardom": "dolg<PERSON><PERSON>t", "applestoapples": "jabolkočjabolko", "jeudesociété": "žurzabavapodjetja", "gameboard": "igralnadeska", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "družabneigre", "twilightimperium": "twilightimperium", "horseopoly": "konjopoly", "deckbuilding": "<PERSON><PERSON><PERSON>_<PERSON>ov", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "gameday", "shadowsofbrimstone": "sencebrimstone", "kingoftokyo": "kraljtokya", "warcaby": "vojnaparti", "táblajátékok": "namizneigre", "battleship": "battleship", "tickettoride": "vstopnicazavožnjo", "deskovehry": "<PERSON><PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "igraln<PERSON><PERSON><PERSON>e", "stolníhry": "stolníhry", "xiángqi": "<PERSON>ah", "jeuxsociete": "družabneigre", "gesellschaftsspiele": "družabneigre", "starwarslegion": "zvezdnevojnelegija", "gochess": "<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "družabneigre", "terraria": "terarija", "dsmp": "dsmp", "warzone": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arkpreživetjerazvito", "dayz": "dni", "identityv": "identiteta", "theisle": "otok", "thelastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>š<PERSON>ganeb<PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "k<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "mednami", "eco": "eko", "monkeyisland": "opičjaotok", "valheim": "valheim", "planetcrafter": "planetkreator", "daysgone": "dneviinn<PERSON>ši", "fobia": "fobija", "witchit": "č<PERSON><PERSON><PERSON>", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "dolgtem<PERSON>", "ark": "ark", "grounded": "prizemljen", "stateofdecay2": "stanjepropadanja2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON>", "dontstarve": "ne<PERSON><PERSON><PERSON>", "eternalreturn": "večnivračanje", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "zlejevniznotrajnosti", "realrac": "pravirac", "thebackrooms": "zadnjeprostore", "backrooms": "zadnjiprostori", "empiressmp": "empiressmp", "blockstory": "blokzgodba", "thequarry": "kamnolom", "tlou": "tlou", "dyinglight": "umirajočasvetloba", "thewalkingdeadgame": "hojojivihmrtvecevigre", "wehappyfew": "srečniščasamoizbrani", "riseofempires": "vzponimparij", "stateofsurvivalgame": "stanjepreživetjaigrica", "vintagestory": "vintagezgodba", "arksurvival": "arkpreživetje", "barotrauma": "barotrauma", "breathedge": "breatheedge", "alisa": "alisa", "westlendsurvival": "preživetjewestlend", "beastsofbermuda": "zveriizbermude", "frostpunk": "zmrzlinaskupnost", "darkwood": "temnalesa", "survivalhorror": "<PERSON>živetvenohorror", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "rezidentnoevil3", "voidtrain": "brazgodba", "lifeaftergame": "življenjepotgames", "survivalgames": "preživetveneigre", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "tavojnamoja", "scpfoundation": "scpfundacija", "greenproject": "zelenprojekt", "kuon": "kuon", "cryoffear": "ob<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "splav", "rdo": "rdo", "greenhell": "zelenapeklenska", "residentevil5": "residentevil5", "deadpoly": "mrtvapoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "baba", "littlenightmares2": "majhnenočnemore2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "sinovigozda", "rustvideogame": "rustvideogame", "outlasttrials": "preskočitevizivij", "alienisolation": "tujaizolacija", "undawn": "<PERSON><PERSON><PERSON><PERSON>", "7day2die": "7dni2umret", "sunlesssea": "<PERSON><PERSON><PERSON>čnež<PERSON>i", "sopravvivenza": "preživetje", "propnight": "nočpopropu", "deadisland2": "mrtvotoka2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "smrtnavers", "cataclysmdarkdays": "kataklizmatemnidnevi", "soma": "soma", "fearandhunger": "strahihungry", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "življenjepotem", "ageofdarkness": "dobakrajnosti", "clocktower3": "urnatorre3", "aloneinthedark": "sama_v_temnosti", "medievaldynasty": "srednjeveškadinastija", "projectnimbusgame": "projektnimbusigra", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "izkušnjeiztrpljenja", "bunker": "bunker", "worlddomination": "svetovnodominiranje", "rocketleague": "rocketliga", "tft": "tft", "officioassassinorum": "uradnipredatorji", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kljubezen", "wh40": "wh40", "warhammer40klove": "warhammer40kljubezen", "warhammer40klore": "warhammer40klegenda", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "obožujemsororitas", "ilovevindicare": "ljubimvindicare", "iloveassasinorum": "obozujemassassinorum", "templovenenum": "templovenenu", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "dobaimperijev", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigrma", "civilizationv": "civilizacijav", "ittakestwo": "itakstadva", "wingspan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraformingmars": "terraformiranjemarsá", "heroesofmightandmagic": "junakimočiinmagije", "btd6": "btd6", "supremecommander": "supremekomandant", "ageofmythology": "doševitezamitologijo", "args": "argumenti", "rime": "<PERSON><PERSON>", "planetzoo": "planetzoo", "outpost2": "zunanjak2", "banished": "izgnan", "caesar3": "caesar3", "redalert": "rdečaopozorila", "civilization6": "civilizacija6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "večnatovojno", "strategygames": "strategijskegames", "anno2070": "anno2070", "civilizationgame": "civilizacijskaigra", "civilization4": "civilizacija4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "špore", "totalwar": "totalnavojna", "travian": "travian", "forts": "trdnjave", "goodcompany": "dobrapartija", "civ": "civ", "homeworld": "domovskezvezde", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "zazakralje", "realtimestrategy": "pravačasstrategija", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierjevacivilizacija", "kingdomtwocrowns": "kraljestvodvehkron", "eu4": "eu4", "vainglory": "<PERSON>valisavost", "ww40k": "ww40k", "godhood": "božanstvo", "anno": "noro", "battletech": "bitkatek", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesfuntokažnice", "plagueinc": "plagueinc", "theorycraft": "teorijskokreiranje", "mesbg": "mesbg", "civilization3": "civilizacija3", "4inarow": "4v<PERSON><PERSON>", "crusaderkings3": "križarskikralji3", "heroes3": "heroj3", "advancewars": "naprednevojne", "ageofempires2": "dozapadov2", "disciples2": "učenci2", "plantsvszombies": "rastlinevszombiji", "giochidistrategia": "strategskigeri", "stratejioyunları": "stratejioigra", "europauniversalis4": "evropauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozav<PERSON><PERSON>", "worldconquest": "osvajanjesveta", "heartsofiron4": "srciizželeza4", "companyofheroes": "družbaherojev", "battleforwesnoth": "bitkazawesnoth", "aoe3": "aoe3", "forgeofempires": "kovačevirimperije", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gosiportirajrac", "phobies": "fobije", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingklanjujepodobe", "adeptusmechanicus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outerplane": "zunanjaplane", "turnbased": "naizmenično", "bomberman": "bomberman", "ageofempires4": "dočasovimperij4", "civilization5": "civilizacija5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "kultrisi2", "spellcraft": "čarovnije", "starwarsempireatwar": "vojnezvezdimperijavojni", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategija", "popfulmail": "popfulmail", "shiningforce": "sijajocakraft", "masterduel": "mojsterdvoboj", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportnižupan", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "načrtujbežoteizmučenja", "uplandkingdoms": "planinskakraljestva", "galaxylife": "galaksživot", "wolvesvilleonline": "wovlesvilleonline", "slaythespire": "slaythespire", "battlecats": "bit<PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "sims4", "thesims": "simsi", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potrebujemhitrost", "needforspeedcarbon": "potrebujemetlakačcarbon", "realracing3": "pravigonjenje3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "sims3", "thesims1": "sims1", "lossims4": "izgubljenims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "mrtvilazuživanju", "alicemadnessreturns": "aliceponoreladobesednost", "darkhorseanthology": "temnifrajvotna", "phasmophobia": "fob<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "petnočivfreddysu", "saiko": "<PERSON><PERSON>o", "fatalframe": "usodenokadrovanje", "littlenightmares": "<PERSON><PERSON>č<PERSON><PERSON>ovi", "deadrising": "mrtvoplekščanje", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "domačeposlovanje", "deadisland": "mrtvotok", "litlemissfortune": "malabolaščina", "projectzero": "projektzero", "horory": "strašno", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "zdravososede2", "gamingdbd": "gamingdbd", "thecatlady": "mačkaženska", "jeuxhorreur": "strašneigre", "horrorgaming": "grozljivkigaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "karteprotičloveštvu", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodenames", "dixit": "dixit", "bicyclecards": "kolesarskartice", "lor": "lor", "euchre": "e<PERSON>re", "thegwent": "tegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "ključnapogodba", "cardtricks": "kartot<PERSON>i", "playingcards": "igralnekartice", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrežnik", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "trgovskekarte", "pokemoncards": "poke<PERSON><PERSON>te", "fleshandbloodtcg": "mesoinkrvotcg", "sportscards": "športnekartice", "cardfightvanguard": "kardnabitkavanguard", "duellinks": "duellinks", "spades": "karta", "warcry": "v<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kraljsrca", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "upor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohigra", "darkmagician": "temničar", "blueeyeswhitedragon": "modreo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "ig<PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "pokalica", "jeuxdecartes": "igralnekarte", "mtgjudge": "mtgrazsodnik", "juegosdecartas": "karteigra", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "karteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "bitjespiri<PERSON>", "battlespiritssaga": "bitjespiritovsgaga", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "facecard", "cardfight": "kartičnoborba", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "magicnekartice", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "senčnavesolje", "skipbo": "skip<PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "ciberžur", "classicarcadegames": "klasičnearcadigre", "osu": "osu", "gitadora": "gitadora", "dancegames": "plesnigreigre", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "prosecco", "projectmirai": "projektmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON>", "clonehero": "klonjunak", "justdance": "samo<PERSON>i", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockajmrtve", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "ples<PERSON><PERSON><PERSON>", "rhythmgamer": "ritmogamer", "stepmania": "stepmania", "highscorerythmgames": "visokoskorniritmiigre", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritmnonebesa", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "audicijanam<PERSON>ži", "itgmania": "itgmanija", "juegosderitmo": "ritmičneigračke", "cryptofthenecrodancer": "kriptozombidancer", "rhythmdoctor": "ritemzdravnik", "cubing": "kubljenje", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "igralnicezugledom", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "logičneuganke", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "uganke", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "križanka", "motscroisés": "motscroisés", "krzyżówki": "bavnekrižvke", "nonogram": "nonogram", "bookworm": "knjigožerec", "jigsawpuzzles": "sestavljanke", "indovinello": "uganka", "riddle": "uganka", "riddles": "uganke", "rompecabezas": "sestavljanka", "tekateki": "tekajotekateki", "inside": "znotraj", "angrybirds": "jez<PERSON><PERSON><PERSON>", "escapesimulator": "bežingersimulator", "minesweeper": "minesweeper", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "su<PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "igraodvrti", "puzzlesport": "ugankarstvo", "escaperoomgames": "igralnicezaizbe", "escapegame": "p<PERSON>ž<PERSON>ba<PERSON>", "3dpuzzle": "3duganka", "homescapesgame": "homescapesigra", "wordsearch": "iskalnikbesed", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "ugankastorije", "fishdom": "fishdom", "theimpossiblequiz": "neizvedljivkviz", "candycrush": "candycrush", "littlebigplanet": "malivelikplanet", "match3puzzle": "puzzle3zmatch", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "kubusrubik", "yapboz": "yapboz", "thetalosprinciple": "taletosprincip", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "kubusirubikovega", "cruciverba": "krizanka", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "ugankaslice", "buscaminas": "iskalnikmin", "puzzlesolving": "rešitevugank", "turnipboy": "<PERSON>po<PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "nobodies", "guessing": "ugibanje", "nonograms": "nonogrami", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptičneuganke", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "lovnauganke", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "razburljanje", "hlavolamy": "boo<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "zadnjokampfires", "autodefinidos": "av<PERSON><PERSON><PERSON>č<PERSON>", "picopark": "picopark", "wandersong": "potovanjupevka", "carto": "karto", "untitledgoosegame": "nimevnikuig<PERSON>jas<PERSON><PERSON><PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limbo": "limbo", "rubiks": "<PERSON><PERSON><PERSON>", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "rubikova_kostka", "speedcube": "hitrostnakocka", "pieces": "kosci", "portalgame": "portalnaigra", "bilmece": "bilmece", "puzzelen": "u<PERSON><PERSON>", "picross": "pikros", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "uganke", "cubomagico": "kubičnamagija", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "ukrivljenovseživetje", "monopoly": "monopoli", "futurefight": "pri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilnelegende", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "samski_volk", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "zvezdeensemble", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "alchemystars", "stateofsurvival": "stanjepreživetja", "mycity": "mojmestno", "arknights": "arknighti", "colorfulstage": "barvitostrand", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "usodnaveljavnost", "hyperfront": "hipersprememba", "knightrun": "viteztek", "fireemblemheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "nogometnaborba", "a3": "a3", "phonegames": "mobilneigre", "kingschoice": "kraljevaizbira", "guardiantales": "zgodbevaruha", "petrolhead": "motorist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktičnokul", "cookierun": "krušneigra", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "brez<PERSON><PERSON><PERSON><PERSON>lec", "craftsman": "obrtnik", "supersus": "supersus", "slowdrive": "počasnavoznja", "headsup": "p<PERSON>ra<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "prostoognje", "mobilegaming": "mobilnigaming", "lilysgarden": "lilysvrt", "farmville2": "farmville2", "animalcrossing": "živalskovožnje", "bgmi": "bgmi", "teamfighttactics": "boj<PERSON>amskebitke", "clashofclans": "bitkavzaklanih", "pjsekai": "pjsekai", "mysticmessenger": "mist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "klicdolžnostimobilno", "thearcana": "thearcana", "8ballpool": "8balldvornik", "emergencyhq": "nužnadružba", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "<PERSON><PERSON><PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "časovnaprincess", "beatstar": "beatstar", "dragonmanialegend": "zmajezmajallegenda", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ž<PERSON><PERSON><PERSON>", "androidgames": "androidigre", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "klic<PERSON>askrb", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaangelov", "lordsmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybirdgarden": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "<PERSON>vrons<PERSON><PERSON><PERSON>", "mysingingmonsters": "mojepevskepošasti", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "modarnarhiva", "raidshadowlegends": "raidshadowlegends", "warrobots": "vojnerobote", "mirrorverse": "ogledalnisvet", "pou": "pou", "warwings": "vojnamočí", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antižogice", "apexlegendmobile": "apexlegendsmobilno", "ingress": "vstop", "slugitout": "razpnizadeve", "mpl": "mpl", "coinmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "kaznovanjegrayraven", "petpals": "pasjepartnerji", "gameofsultans": "igrasultana", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "tekmovalnagradnjak", "juegodemovil": "igranjenamobilu", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "velikopolzovanje", "bombmebrasil": "bombimebrazil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "kliczmajev", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "potvnik<PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "senčnabitka3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "besedamivzfriends2", "soulknight": "dušurnite", "purrfecttale": "popolnzgodba", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobilno", "harvesttown": "oberjasimesto", "perfectworldmobile": "popolnsvetmobilni", "empiresandpuzzles": "imperi<PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "empirespuzzle", "dragoncity": "zmajograd", "garticphone": "garticphone", "battlegroundmobileind": "b<PERSON>jiščemobileind", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "solzezaumetnost", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiepotapljači", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilnelegendebangbang", "gachaclub": "gachaklub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "kabalmobil", "streetfighterduel": "uličniborciduel", "lesecretdhenri": "leskrivnostdhenrija", "gamingbgmi": "gamingbgmi", "girlsfrontline": "frontline<PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "iskalec<PERSON>š<PERSON>", "gettingoverit": "premagujem<PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "mooncha<PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilneigre", "legendofneverland": "legendaonikjeršnjega", "pubglite": "pubglite", "gamemobilelegends": "gamedomobilelegends", "timeraiders": "timeraiders", "gamingmobile": "mobilnoigrajenje", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON>", "giochidiruolo": "igralnevloge", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "potovalecttrpg", "2300ad": "2300ad", "larp": "ž<PERSON>lai<PERSON><PERSON><PERSON>", "romanceclub": "<PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokémonmysterydungeon", "pokemonlegendsarceus": "poké<PERSON>legends<PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokémonizaziv", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "<PERSON>š<PERSON><PERSON><PERSON>", "gengar": "gengar", "natu": "natu", "teamrocket": "s<PERSON>pinastrel<PERSON>v", "furret": "tacobobek", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "žepnišportniki", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonblaznički", "teamystic": "ekipamistična", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "<PERSON><PERSON><PERSON><PERSON>", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "železnahrčka", "kabutops": "kabutops", "psyduck": "psydak", "umbreon": "umreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "o<PERSON>kini<PERSON>kemoni", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON>ah", "catur": "<PERSON>ur", "xadrez": "<PERSON>ah", "scacchi": "<PERSON>ah", "schaken": "šakanje", "skak": "skak", "ajedres": "<PERSON>ah", "chessgirls": "šahovskedekleta", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "svetovnikrate", "jeudéchecs": "gav<PERSON><PERSON>", "japanesechess": "japonskashaha", "chinesechess": "kitajskishodovnik", "chesscanada": "šah<PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "odprtaвrta", "rook": "mladina", "chesscom": "šahcom", "calabozosydragones": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "podzemljiinzmaji", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventura", "darksun": "temnisonce", "thelegendofvoxmachina": "legendaovoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "te<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftšampionat", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "sanjskismp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodi", "mcc": "mcc", "candleflame": "plam<PERSON>vetil<PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "nebesnoblok", "minecraftpocket": "minecraftžep", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "me<PERSON><PERSON><PERSON>l<PERSON><PERSON>", "minecraftdungeons": "minecraftjame", "minecraftcity": "minecraftmesto", "pcgamer": "pcgamer", "jeuxvideo": "igralneigre", "gambit": "gambit", "gamers": "gamerji", "levelup": "<PERSON><PERSON><PERSON>ni<PERSON>", "gamermobile": "gamermobil", "gameover": "konecigre", "gg": "gg", "pcgaming": "računalniškigaming", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "računalniškeigre", "casualgaming": "casualgaming", "gamingsetup": "gamingpostavitev", "pcmasterrace": "pcmasterrace", "pcgame": "računalniškaigra", "gamerboy": "gamerfant", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kigranje", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "igralnaplatforma", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "spletnoplačanje", "semigamer": "polovigamer", "gamergirls": "gamergirls", "gamermoms": "gamermame", "gamerguy": "gamerguy", "gamewatcher": "igralnivosk", "gameur": "gamur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerskepunce", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "ekipaizkušnje", "mallugaming": "mallugaming", "pawgers": "<PERSON><PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "starigamer", "cozygaming": "udobnoigraње", "gamelpay": "gamelpay", "juegosdepc": "igricenapc", "dsswitch": "dsswitch", "competitivegaming": "konkurenčnaigra", "minecraftnewjersey": "minecraftnovjersey", "faker": "fakefriend", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksualnigaming", "gamepc": "igralnikračunalnik", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "dnevnapustolovščina", "gamegirl": "igralnadeklica", "chicasgamer": "gamerke", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "igralnjak", "proplayer": "proigralec", "roleplayer": "igralecrol", "myteam": "moteam", "republicofgamers": "republikaigralcev", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "trojnimiti", "gamerbuddies": "gamerprijatelji", "butuhcewekgamers": "butuhcewekgamerji", "christiangamer": "krščanskigamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamersl", "afk": "afk", "andregamer": "andregamer", "casualgamer": "prijetenigralec", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "nezadostno", "gemers": "gem<PERSON><PERSON>", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "videoigralec", "wspólnegranie": "skupnigramanje", "mortdog": "mortdog", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "waberica", "obviouslyimagamer": "<PERSON>vedasem<PERSON>r", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "<PERSON><PERSON><PERSON><PERSON>", "forager": "nabiralec", "humanfallflat": "človekpadeplosko", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "ničizbeža", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "<PERSON><PERSON><PERSON><PERSON>", "fallguys": "jesenskifantje", "switch": "<PERSON>k<PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatun", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorova<PERSON><PERSON>", "mariokartmaster": "ma<PERSON>kartma<PERSON>ro", "wii": "wii", "aceattorney": "aceodvetnik", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "solzekraljestva", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "nintendogames", "thelegendofzelda": "izjemnilegendizelde", "dragonquest": "zmajskaizlet", "harvestmoon": "letnjak", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mij<PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51iger", "earthbound": "zemeljsko", "tales": "zgodbe", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "živalikrižanje", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendoslovenskem", "tloz": "tloz", "trianglestrategy": "trianglenastrategija", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kestenivetlasano", "nintendos": "nintendos", "new3ds": "novi3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "bojevnik<PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperzvezde", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogi", "thezelda": "zelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cbhahaha", "leagueoflegendslas": "ligalegendovlas", "urgot": "urgot", "zyra": "zyra", "redcanids": "rdečikanidi", "vanillalol": "vanillalol", "wildriftph": "<PERSON><PERSON><PERSON>", "lolph": "lolph", "leagueoflegend": "ligalegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligalegendovwild", "adcarry": "adnosite", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsšpanija", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "ligalegendoveuw", "kayle": "kejla", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaleslegend", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexvrata", "hextech": "hextech", "fortnitegame": "fortniteigra", "gamingfortnite": "igrefortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retroigrevideovideo", "scaryvideogames": "strašneigralnice", "videogamemaker": "videogamemaker", "megamanzero": "megamanzero", "videogame": "videogame", "videosgame": "videoigrice", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "preteklost", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "bitkab<PERSON>kteater", "arcades": "arkade", "acnh": "acnh", "puffpals": "pu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "kmetijskisimulator", "robloxchile": "robloxslovenija", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxslovenija", "robloxdeutsch": "robloxslovenija", "erlc": "erlc", "sanboxgames": "sandboxigre", "videogamelore": "videogamestrasti", "rollerdrome": "rolerodrom", "parasiteeve": "paraziteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "sanjskapokrajina", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "velikoprostorimafija", "deadspace": "deadspace", "amordoce": "sladkaljubezen", "videogiochi": "videogames", "theoldrepublic": "stararepublika", "videospiele": "videoigra", "touhouproject": "touhouprojekt", "dreamcast": "sanjskiprenos", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "akcijskaavantura", "storyofseasons": "zgodbel<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retrogames", "retroarcade": "retroarkada", "vintagecomputing": "vintageračunalništvo", "retrogaming": "retrogaming", "vintagegaming": "retroigre", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "komandantkeen", "bugsnax": "bugsnakslove", "injustice2": "nepravičnost2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "neboigra", "zenlife": "zenlife", "beatmaniaiidx": "beatmaniaiidx", "steep": "strmo", "mystgames": "mystigre", "blockchaingaming": "blokchainigranje", "medievil": "srednjivek", "consolegaming": "konzolnoiganje", "konsolen": "konsolni", "outrun": "<PERSON><PERSON><PERSON>", "bloomingpanic": "bloomingpanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "iskanjepošastnihdeklet", "supergiant": "superduper", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "kmetijskisimulacije", "juegosviejos": "stareigre", "bethesda": "bethesda", "jackboxgames": "jackboxigre", "interactivefiction": "interaktivnafikcija", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "zadonjičas2", "amantesamentes": "ljubezenskeumetnosti", "visualnovel": "vizualnovela", "visualnovels": "vizualnepripovedi", "rgg": "rgg", "shadowolf": "sencavolk", "tcrghost": "tcrghost", "payday": "plačadana", "chatherine": "čatrin", "twilightprincess": "twilightprincess", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "pešč<PERSON>_plošča", "aestheticgames": "estetskeigre", "novelavisual": "novelavisual", "thecrew2": "ekipa2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogaming", "tonyhawkproskater": "tonyhuwakproskater", "smbz": "smbz", "lamento": "<PERSON><PERSON><PERSON>", "godhand": "božja_roka", "leafblowerrevolution": "revolu<PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "navigacijaigre", "starrail": "zvezdnavor", "keyblade": "ključnasablj", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizualnezgodbe", "robloxbrasil": "robloxslovenija", "pacman": "pacman", "gameretro": "narejenonostalgijo", "videojuejos": "videoigra", "videogamedates": "videogamedatumi", "mycandylove": "mojaskandysnežka", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "vrnitevodvetovanja", "gamstergaming": "gamerjiigrami", "dayofthetantacle": "dantanta<PERSON>", "maniacmansion": "maniacmansion", "crashracing": "zrušitvenoracing", "3dplatformers": "3dplatformerji", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "starašolaigre", "hellblade": "peklenjskameča", "storygames": "zgodbenegaming", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "igrajuporabo", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "povi<PERSON><PERSON>mo动力", "katanazero": "katanzero", "famicom": "famicom", "aventurasgraficas": "grafičneavanture", "quickflash": "hit<PERSON>žarek", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkade", "f123": "f123", "wasteland": "pustinja", "powerwashsim": "powerwashsim", "coralisland": "koralnotočje", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "drugisvet", "metaquest": "metaquest", "animewarrios2": "animeborci2", "footballfusion": "fuzijašporta", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomajstor", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "ukrivljenimetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "kupčeksramote", "simulator": "simulator", "symulatory": "simula<PERSON><PERSON>", "speedrunner": "hitriigralec", "epicx": "epikx", "superrobottaisen": "superrobotka", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gryvideo", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "čudnosedonline", "skylander": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boyfrienddungeon": "fantazijazmoža", "toontownrewritten": "toontownprekodirano", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "urbanikaos", "heavenlybodies": "nebeške_telesa", "seum": "seum", "partyvideogames": "zabavnenavideograhih", "graveyardkeeper": "skrbnikpokopališča", "spaceflightsimulator": "vesoljesimulator", "legacyofkain": "zaplegadokain", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "hranaivideoigre", "oyunvideoları": "igralnevideo", "thewolfamongus": "volkmednas", "truckingsimulator": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "ročneigre", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "starešolskeigre", "racingsimulator": "dirkaškisimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentimajem", "songpop": "pesmopop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympusa", "monsterhunternow": "lovecpošastizdaj", "rebelstar": "upornikzvezda", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "indievideogameščki", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "močnautrdbа", "unbeatable": "nepremagljiv", "projectl": "projektl", "futureclubgames": "igrekrožkigame", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "<PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "igriskladisče", "gamingbacklog": "ikigaming", "personnagejeuxvidéos": "likivideogame", "achievementhunter": "lovecizadovoljstvo", "cityskylines": "mestnepanorame", "supermonkeyball": "supermonkeyball", "deponia": "deponija", "naughtydog": "naughtydog", "beastlord": "zverinskigospodar", "juegosretro": "retroigra", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriinblindgomorje", "alanwake": "alanwake", "stanleyparable": "stanleyporabne", "reservatoriodedopamin": "rezervatoriodedopamin", "staxel": "staxel", "videogameost": "videoigraost", "dragonsync": "zmajevskisync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ljubimkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "morskaprincezna", "saintseiya": "svetnikomseiya", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "prvookretnik", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "žalostnanime", "darkerthanblack": "temnejšikotčrn", "animescaling": "animescaling", "animewithplot": "animezzgledom", "pesci": "pesci", "retroanime": "retroanime", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "temnigospod", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezona1", "rapanime": "rapanime", "chargemanken": "polnimonke", "animecover": "<PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "vizijescaflowne", "slayers": "ubijalci", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananapeša", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletnopovezanhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "ognjenaekipa", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "dnevnikprihodnosti", "fairytail": "pravljica", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "narejenovdolu", "parasyte": "parazitek", "punpun": "punpun", "shingekinokyojin": "napadnantitane", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "morskasirenažurka", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horormanga", "romancemangas": "romantičenmanga", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "zmajevahčička", "blacklagoon": "črnamočvara", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformarji", "geniusinc": "geniusinc", "shamanking": "šamankanje", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "pulančatabuhcije", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "nekačarobnaindeks", "sao": "sao", "blackclover": "črnenitka", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "<PERSON><PERSON><PERSON>", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "krvavizvijak", "aot": "aot", "sk8theinfinity": "sk8neskončnosti", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "mečnjakovspletu", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprednost", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldenkamuy": "zlatakamuy", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "športnianime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "le<PERSON>č<PERSON><PERSON><PERSON>", "theboyandthebeast": "fantekinzver", "fistofthenorthstar": "pestsevernegazela", "mazinger": "mazinger", "blackbuttler": "črnslužabnik", "towerofgod": "tornbogov", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "<PERSON><PERSON>", "servamp": "servamp", "howtokeepamummy": "kakoobdržatimamico", "fullmoonwosagashite": "fullmoonwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "lušnoinnerodno", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "najboljšadeklica", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "morn<PERSON><PERSON>iz<PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "mornarkapet", "aloy": "aloy", "runa": "runa", "oldanime": "starianime", "chainsawman": "možnatovečereza", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengersi", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "k<PERSON><PERSON>", "loli": "loli", "horroranime": "grozljivkanime", "fruitsbasket": "sadjevnjak", "devilmancrybaby": "<PERSON><PERSON>č<PERSON>jci<PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ljubilivzivljenje", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "ovarinoseraph", "thepromisedneverland": "obljubljenanikdardežela", "monstermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "tvojalžaaprilu", "buggytheclown": "buggyklovn", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "globokomorskiujetnik", "jojolion": "jojo<PERSON>", "deadmanwonderland": "božjačudežnica", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorinasrca", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "v<PERSON><PERSON><PERSON>ovrstah", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "vrstapekla", "toyoureternity": "tvojetvečnost", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "modra<PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "tajnozavezništvo", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "izbrisano", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detektivconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirskivitez", "mugi": "mugi", "blueexorcist": "modriizganjalec", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "vohunskadružina", "airgear": "zračneopreme", "magicalgirl": "magicnodelka", "thesevendeadlysins": "sedems<PERSON><PERSON><PERSON><PERSON>", "prisonschool": "zapornikšnola", "thegodofhighschool": "bogsrednjisekole", "kissxsis": "poljubxsestra", "grandblue": "velikmodri", "mydressupdarling": "moodamijesopoti", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "roženka", "animeuniverse": "animevesolje", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoobrezskrajšanja", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nedolgoživljenjepreživetje", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromanca", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinska", "lolicon": "<PERSON><PERSON><PERSON>", "demonslayertothesword": "demonslayer<PERSON><PERSON><PERSON>", "bloodlad": "krvotak", "goodbyeeri": "slovenčadovijeri", "firepunch": "og<PERSON><PERSON><PERSON>z<PERSON>v", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zvezdeseporavnavajo", "romanceanime": "romantičnaanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "češnjevamagija", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "zabeležiragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "srednjaskolamrtvih", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "učenjavesnice", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "poklonmrtvim", "shokugekinosouma": "šokugekinosouma", "japaneseanime": "japon<PERSON>anime", "animespace": "animesvet", "girlsundpanzer": "dekle<PERSON>und<PERSON>zer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "neodvisnianime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "podgana", "haremanime": "hareanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "breskevdeklica", "cavalieridellozodiaco": "cavalieri_zodiaka", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklub", "dragonquestdai": "zmajskanajdai", "heartofmanga": "srcemange", "deliciousindungeon": "slastnovdungeon", "manhviyaoi": "moškihojajoi", "recordofragnarok": "zapisoragnaroka", "funamusea": "zabavnamuzeja", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialitrep<PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "čarovnikovašalčkastudio", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaživljenje", "dropsofgod": "kapljicebožje", "loscaballerosdelzodia": "vitezizodstrani", "animeshojo": "animeshojo", "reverseharem": "obratnijaharem", "saintsaeya": "svetniščesaya", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "mojšefdodatek", "gear5": "gear5", "grandbluedreaming": "velikoplanblue", "bloodplus": "krvoplus", "bloodplusanime": "krvplusanime", "bloodcanime": "krvnoanime", "bloodc": "krvavo", "talesofdemonsandgods": "zgodbeofdemoniinbogovi", "goreanime": "goreanime", "animegirls": "animeskepunce", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vranexnajslabše", "splatteranime": "razlitianime", "splatter": "razlitje", "risingoftheshieldhero": "vzponščitezaščitnejunake", "somalianime": "so<PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedatopljen", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animešpanija", "animeciudadreal": "animeciudadreal", "murim": "umrem", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "otrokipremažerjev", "liarliar": "lažniveclažnivec", "supercampeones": "superšampioni", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiztelefončkom", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "čarobnepunce", "callofthenight": "klic<PERSON>či", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbojevniki", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "sencašenvrt", "tsubasachronicle": "tsubasachronicle", "findermanga": "najdemanga", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "poljub<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevesolje", "persocoms": "persocoms", "omniscientreadersview": "<PERSON>mnis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "animemačka", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "odprt<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mojanaindromantika", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundami", "voltesv": "voltesv", "giantrobots": "velikanzaroboti", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilnibojevnikggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilnaskupinagundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "belilo", "deathnote": "smrtnopismo", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojovzmagaavantura", "fullmetalalchemist": "polniometalnialkemičar", "ghiaccio": "led", "jojobizarreadventures": "jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "vojaškimanga", "greenranger": "<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "zvezdnilisjak", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animemesto", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "en<PERSON><PERSON><PERSON>", "animeonepiece": "animeeneposrečje", "dbz": "dbz", "dragonball": "zmajevažoga", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonavantura", "hxh": "hxh", "highschooldxd": "srednjašoldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroakademija", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON>ka<PERSON><PERSON><PERSON>l", "attackontitan": "napadnanit", "erenyeager": "tudizparajo", "myheroacademia": "mojaherojskaakademija", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "enopieceanime", "attaquedestitans": "napadenitenka", "theonepieceisreal": "theonepiecejespravilen", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mob<PERSON><PERSON>", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyefekt", "digimonstory": "digimonzgodba", "digimontamers": "digimontamers", "superjail": "superzapor", "metalocalypse": "metalokmalapsa", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "našgostilnasplet", "flawlesswebtoon": "brezhibenwebtoon", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utánoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "letalskačarovnica", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "prep<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "vsisvetnikiulica", "recuentosdelavida": "zapiskiživljenja"}