{"2048": "2048", "mbti": "mbti", "enneagram": "九型人格", "astrology": "星座", "cognitivefunctions": "认知功能", "psychology": "心理学", "philosophy": "哲学", "history": "历史", "physics": "物理", "science": "科学", "culture": "文化", "languages": "语言", "technology": "科技", "memes": "模因", "mbtimemes": "mbti模因", "astrologymemes": "星座模因", "enneagrammemes": "九型人格网络模因", "showerthoughts": "淋浴思考", "funny": "搞笑", "videos": "视频", "gadgets": "小玩意", "politics": "政治", "relationshipadvice": "关系建议", "lifeadvice": "人生忠告", "crypto": "加密货币", "news": "新闻", "worldnews": "世界新闻", "archaeology": "考古学", "learning": "学习", "debates": "辩论", "conspiracytheories": "阴谋论", "universe": "宇宙", "meditation": "冥想", "mythology": "神话学", "art": "艺术", "crafts": "工艺", "dance": "舞蹈", "design": "设计", "makeup": "化妆", "beauty": "美妆", "fashion": "时尚", "singing": "唱歌", "writing": "写作", "photography": "摄影", "cosplay": "角色扮演", "painting": "绘画", "drawing": "绘画", "books": "书籍", "movies": "电影", "poetry": "诗歌", "television": "电视", "filmmaking": "电影制作", "animation": "动画", "anime": "动漫", "scifi": "科幻", "fantasy": "奇幻", "documentaries": "纪录片", "mystery": "悬疑", "comedy": "喜剧", "crime": "犯罪", "drama": "戏剧", "bollywood": "宝莱坞", "kdrama": "韩剧", "horror": "恐惧", "romance": "浪漫", "realitytv": "电视真人秀", "action": "动作片", "music": "音乐", "blues": "布鲁斯音乐", "classical": "古典", "country": "乡村音乐", "desi": "印度音乐", "edm": "电子舞蹈音乐", "electronic": "电子音乐", "folk": "民谣音乐", "funk": "朋克音乐", "hiphop": "说唱音乐", "house": "浩室音乐", "indie": "独立音乐", "jazz": "爵士乐", "kpop": "韩国流行音乐", "latin": "拉丁音乐", "metal": "金属乐", "pop": "流行音乐", "punk": "朋克音乐", "rnb": "节奏布鲁斯", "rap": "说唱音乐", "reggae": "雷鬼音乐", "rock": "摇滚音乐", "techno": "高科技舞曲", "travel": "旅行", "concerts": "音乐会", "festivals": "节日", "museums": "博物馆", "standup": "脱口秀", "theater": "戏剧", "outdoors": "户外", "gardening": "园艺", "partying": "聚会", "gaming": "游戏", "boardgames": "桌游", "dungeonsanddragons": "地下城与勇士", "chess": "象棋", "fortnite": "堡垒之夜", "leagueoflegends": "英雄联盟", "starcraft": "星际争霸", "minecraft": "我的世界", "pokemon": "宝可梦", "food": "美食", "baking": "烘焙", "cooking": "烹饪", "vegetarian": "素食", "vegan": "素食", "birds": "鸟", "cats": "猫", "dogs": "狗", "fish": "鱼", "animals": "动物", "blacklivesmatter": "黑人的命也是命", "environmentalism": "环保主义", "feminism": "女权主义", "humanrights": "人权", "lgbtqally": "直同志", "stopasianhate": "停止亚洲仇恨", "transally": "反式盟友", "volunteering": "志愿活动", "sports": "体育运动", "badminton": "羽毛球", "baseball": "棒球", "basketball": "篮球", "boxing": "拳击", "cricket": "板球", "cycling": "骑行", "fitness": "健身", "football": "足球", "golf": "高尔夫球", "gym": "健身房", "gymnastics": "体操", "hockey": "曲棍球", "martialarts": "武术", "netball": "网球", "pilates": "普拉提", "pingpong": "乒乓球", "running": "跑步", "skateboarding": "滑板", "skiing": "滑雪", "snowboarding": "滑雪", "surfing": "冲浪", "swimming": "游泳", "tennis": "网球", "volleyball": "排球", "weightlifting": "举重", "yoga": "瑜伽", "scubadiving": "潜水", "hiking": "徒步", "capricorn": "摩羯座", "aquarius": "水瓶座", "pisces": "双鱼座", "aries": "白羊座", "taurus": "金牛座", "gemini": "双子座", "cancer": "巨蟹座", "leo": "狮子座", "virgo": "处女座", "libra": "天秤座", "scorpio": "天蝎座", "sagittarius": "射手座", "shortterm": "短期恋爱", "casual": "随意交友", "longtermrelationship": "认真谈恋爱", "single": "单身", "polyamory": "多人恋爱", "enm": "伦理非专一", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "同性恋", "lesbian": "女同性恋", "bisexual": "双性恋", "pansexual": "泛性恋", "asexual": "无性", "reddeadredemption2": "红死回头客2", "dragonage": "龙腾岁月", "assassinscreed": "刺客信条", "saintsrow": "圣徒行列", "danganronpa": "弹丸论破", "deltarune": "德尔塔之rune", "watchdogs": "监视狗", "dislyte": "迪斯莱特", "rougelikes": "红色像素游戏", "kingsquest": "国王的冒险", "soulreaver": "灵魂夺取者", "suikoden": "水门街", "subverse": "副宇宙", "legendofspyro": "斯派罗传说", "rouguelikes": "类银河战士", "syberia": "西伯利亚", "rdr2": "rdr2", "spyrothedragon": "龙之间谍", "dragonsdogma": "龙之信条", "sunsetoverdrive": "日落狂潮", "arkham": "阿卡姆", "deusex": "天神论", "fireemblemfates": "火焰纹章命运", "yokaiwatch": "妖怪手表", "rocksteady": "摇滚稳稳", "litrpg": "文学角色扮演", "haloinfinite": "光环无限", "guildwars": "公会战争", "openworld": "开放世界", "heroesofthestorm": "风暴英雄", "cytus": "细胞", "soulslike": "灵魂类", "dungeoncrawling": "地下城探险", "jetsetradio": "喷射派对радио", "tribesofmidgard": "中土部落", "planescape": "计划境界", "lordsoftherealm2": "领域之主2", "baldursgate": "博德之门", "colorvore": "色彩盛宴", "medabots": "机甲战士", "lodsoftherealm2": "开挂的领域2", "patfofexile": "放逐之路", "immersivesims": "沉浸式模拟游戏", "okage": "好家伙", "juegoderol": "角色扮演游戏", "witcher": "巫师", "dishonored": "失去荣耀", "eldenring": "艾尔登法环", "darksouls": "黑暗之魂", "kotor": "鬼泣", "wynncraft": "永利工艺", "witcher3": "巫师3", "fallout": "辐射", "fallout3": "辐射3", "fallout4": "辐射4", "skyrim": "天际", "elderscrolls": "老滚", "modding": "改装", "charactercreation": "角色创作", "immersive": "沉浸式", "falloutnewvegas": "辐射新维加斯", "bioshock": "生化危机", "omori": "大梦晚安", "finalfantasyoldschool": "最终幻想老派", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "最终幻想", "finalfantasy14": "最终幻想14", "finalfantasyxiv": "最终幻想14", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "最终幻想马托亚", "lalafell": "拉拉菲尔", "dissidia": "迪西迪亚", "finalfantasy7": "最终幻想7", "ff7": "ff7", "morbidmotivation": "病态的动力", "finalfantasyvii": "最终幻想7", "ff8": "ff8", "otome": "乙女", "suckerforlove": "爱得不得了", "otomegames": "乙女游戏", "stardew": "星露谷", "stardewvalley": "星露谷物语", "ocarinaoftime": "时间的竖笛", "yiikrpg": "噩梦rpg", "vampirethemasquerade": "吸血鬼伪装舞会", "dimension20": "维度20", "gaslands": "气土世界", "pathfinder": "探路者", "pathfinder2ndedition": "寻路者第二版", "shadowrun": "影子奔跑", "bloodontheclocktower": "钟楼上的血", "finalfantasy15": "最终幻想15", "finalfantasy11": "最终幻想11", "finalfantasy8": "最终幻想8", "ffxvi": "ffxvi", "lovenikki": "爱丽丽", "drakengard": "黑龙之眼", "gravityrush": "重力冲刺", "rpg": "角色扮演", "dota2": "刀塔2", "xenoblade": "异度之刃", "oneshot": "一击即中", "rpgmaker": "角色扮演游戏制作器", "osrs": "老旧发型游戏", "overlord": "霸主", "yourturntodie": "轮到你死了", "persona3": "人格3", "rpghorror": "角色扮演恐怖", "elderscrollsonline": "老滚在线", "reka": "人气", "honkai": "崩坏", "marauders": "掠夺者", "shinmegamitensei": "真女神转生", "epicseven": "史诗七", "rpgtext": "角色扮演文本", "genshin": "原神", "eso": "哎呀", "diablo2": "暗黑破坏神2", "diablo2lod": "暗黑破坏神2复刻版", "morrowind": "晨风", "starwarskotor": "星球大战旧共和国", "demonsouls": "恶魔之魂", "mu": "木", "falloutshelter": "废墟避难所", "gurps": "角色扮演游戏", "darkestdungeon": "黑暗地牢", "eclipsephase": "日蚀阶段", "disgaea": "魔界战记", "outerworlds": "外域", "arpg": "动作角色扮演游戏", "crpg": "桌面角色扮演游戏", "bindingofisaac": "以撒的结合", "diabloimmortal": "暗黑不朽", "dynastywarriors": "王者之战", "skullgirls": "骷髅女孩", "nightcity": "夜城", "hogwartslegacy": "霍格沃茨遗产", "madnesscombat": "疯狂对决", "jaggedalliance2": "锯齿同盟2", "neverwinter": "无冬", "road96": "<PERSON>路96", "vtmb": "vtmb", "chimeraland": "奇美兰", "homm3": "小兵3", "fe3h": "fe3h", "roguelikes": "类银河探险游戏", "gothamknights": "哥谭骑士", "forgottenrealms": "遗忘的领域", "dragonlance": "龙枪", "arenaofvalor": "荣耀战场", "ffxv": "最终幻想15", "ornarpg": "装扮角色游戏", "toontown": "玩具城", "childoflight": "光之子", "aq3d": "aq3d", "mogeko": "萌哥", "thedivision2": "分裂2", "lineage2": "血统2", "digimonworld": "数码宝贝世界", "monsterrancher": "怪物农场", "ecopunk": "生态朋克", "vermintide2": "小虫末日2", "xeno": "外星人", "vulcanverse": "火神宇宙", "fracturedthrones": "破裂的王座", "horizonforbiddenwest": "地平线禁区西部", "twewy": "街头女巫", "shadowpunk": "阴影朋克", "finalfantasyxv": "最终幻想xv", "everoasis": "永恒绿洲", "hogwartmystery": "霍格沃兹之谜", "deltagreen": "参与绿色", "diablo": "恶魔", "diablo3": "暗黑破坏神3", "diablo4": "暗黑破坏神4", "smite": "打击", "lastepoch": "最后一个时代", "starfinder": "星际探索者", "goldensun": "金太阳", "divinityoriginalsin": "神性原罪", "bladesinthedark": "在黑暗中的刀锋", "twilight2000": "twilight2000", "sandevistan": "闪影机", "cyberpunk": "赛博朋克", "cyberpunk2077": "赛博朋克2077", "cyberpunkred": "赛博朋克红", "dragonballxenoverse2": "龙珠超宇宙2", "fallenorder": "堕落秩序", "finalfantasyxii": "最终幻想xii", "evillands": "邪恶之地", "genshinimact": "原神手游", "aethyr": "气息", "devilsurvivor": "恶魔生存者", "oldschoolrunescape": "老派runescape", "finalfantasy10": "最终幻想10", "anime5e": "动漫5e", "divinity": "神性", "pf2": "pf2", "farmrpg": "农场角色扮演游戏", "oldworldblues": "旧世界忧郁", "adventurequest": "探险之旅", "dagorhir": "打戈希尔", "roleplayingames": "角色扮演游戏", "roleplayinggames": "角色扮演游戏", "finalfantasy9": "最终幻想9", "sunhaven": "阳光港湾", "talesofsymphonia": "交响曲故事", "honkaistarrail": "霍卡伊星轨", "wolong": "卧龙", "finalfantasy13": "最终幻想13", "daggerfall": "匕首之秋", "torncity": "撕裂之城", "myfarog": "我的法罗g", "sacredunderworld": "神秘地下世界", "chainedechoes": "连锁回声", "darksoul": "黑暗灵魂", "soulslikes": "灵魂类游戏", "othercide": "其他罪", "mountandblade": "骑马与砍杀", "inazumaeleven": "雷电十一", "acvalhalla": "阿尔德海姆", "chronotrigger": "时空之轮", "pillarsofeternity": "永恒的支柱", "palladiumrpg": "铂金角色扮演", "rifts": "裂痕", "tibia": "胫骨", "thedivision": "分裂", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "勇者传说", "xenobladechronicles2": "异度之刃编年史2", "vampirolamascarada": "吸血鬼面具派对", "octopathtraveler": "八方旅人", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "狼人末日", "aveyond": "艾维昂", "littlewood": "小木头", "childrenofmorta": "摩尔塔的孩子们", "engineheart": "引擎心", "fable3": "传说3", "fablethelostchapter": "迷失章节的寓言", "hiveswap": "蜂蜜交换", "rollenspiel": "角色扮演", "harpg": "哈普戈", "baldursgates": "博德之门", "edeneternal": "乐园永存", "finalfantasy16": "最终幻想16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "星际边界", "oldschoolrevival": "老派复兴", "finalfantasy12": "最终幻想12", "ff12": "ff12", "morkborg": "魔堡", "savageworlds": "狠角色的世界", "diabloiv": "暗黑破坏神iv", "pve": "玩家对战环境", "kingdomheart1": "王国之心1", "ff9": "ff9", "kingdomheart2": "王国之心2", "darknessdungeon": "黑暗地牢", "juegosrpg": "角色扮演游戏", "kingdomhearts": "王国之心", "kingdomheart3": "王国之心3", "finalfantasy6": "最终幻想6", "ffvi": "ffvi", "clanmalkavian": "克兰马尔卡维安", "harvestella": "丰收生活", "gloomhaven": "灰暗的避风港", "wildhearts": "狂野心灵", "bastion": "堡垒", "drakarochdemoner": "德拉卡罗克恶魔", "skiesofarcadia": "天空的阿卡迪亚", "shadowhearts": "影子之心", "nierreplicant": "尼尔复制体", "gnosia": "诺西亚", "pennyblood": "小钱血", "breathoffire4": "火焰之息4", "mother3": "母亲3", "cyberpunk2020": "赛博朋克2020", "falloutbos": "辐射兄弟会", "anothereden": "另一个伊甸园", "roleplaygames": "角色扮演游戏", "roleplaygame": "角色扮演游戏", "fabulaultima": "超赞的最后一刻", "witchsheart": "巫婆的心", "harrypottergame": "哈利波特游戏", "pathfinderrpg": "探索者rpg", "pathfinder2e": "探路者2e", "vampirilamasquerade": "吸血鬼面具派对", "dračák": "龙城", "spelljammer": "星界航行者", "dragonageorigins": "龙腾起源", "chronocross": "时空之旅", "cocttrpg": "合作桌游", "huntroyale": "狩猎王朝", "albertodyssey": "阿尔贝托之旅", "monsterhunterworld": "怪物猎人世界", "bg3": "bg3", "xenogear": "异界之旅", "temtem": "小精灵", "rpgforum": "角色扮演论坛", "shadowheartscovenant": "影心契约", "bladesoul": "刀魂", "baldursgate3": "博德之门3", "kingdomcome": "天国降临", "awplanet": "哇星球", "theworldendswithyou": "这个世界结束于你", "dragalialost": "失落的德拉伽莉亚", "elderscroll": "长老卷轴", "dyinglight2": "消逝光芒2", "finalfantasytactics": "最终幻想战术", "grandia": "大雅", "darkheresy": "黑暗异端", "shoptitans": "购物天神", "forumrpg": "论坛rpg", "golarion": "高拉里昂", "earthmagic": "地球魔法", "blackbook": "黑色本子", "skychildrenoflight": "天空光明的孩子们", "gryrpg": "gryrpg", "sacredgoldedition": "神圣金版", "castlecrashers": "城堡崩溃者", "gothicgame": "哥特游戏", "scarletnexus": "猩红纽带", "ghostwiretokyo": "幽灵线东京", "fallout2d20": "辐射2d20", "gamingrpg": "游戏角色扮演", "prophunt": "猎人游戏", "starrails": "星轨", "cityofmist": "迷雾之城", "indierpg": "独立rpg", "pointandclick": "点点点击", "emilyisawaytoo": "艾米莉也离开了", "emilyisaway": "艾米莉不在", "indivisible": "不可分割", "freeside": "自由边缘", "epic7": "史诗7", "ff7evercrisis": "最终幻想7永恒危机", "xenogears": "异邦之魂", "megamitensei": "女神转生", "symbaroum": "辛巴鲁姆", "postcyberpunk": "后赛博朋克", "deathroadtocanada": "死路通加拿大", "palladium": "钯金", "knightjdr": "骑士jdr", "monsterhunter": "猎怪达人", "fireemblem": "火焰纹章", "genshinimpact": "原神", "geosupremancy": "地理霸权", "persona5": "个人5", "ghostoftsushima": "影子之躯", "sekiro": "只狼", "monsterhunterrise": "怪物猎人崛起", "nier": "尼尔", "dothack": "斗黑", "ys": "ys", "souleater": "吞灵者", "fatestaynight": "命运之夜", "etrianodyssey": "异界探险", "nonarygames": "九重游戏", "tacticalrpg": "战术角色扮演游戏", "mahoyo": "妈好要", "animegames": "动漫游戏", "damganronpa": "打工人杀人委员", "granbluefantasy": "碧蓝幻想", "godeater": "吃货", "diluc": "迪卢克", "venti": "通宵", "eternalsonata": "永恒奏鸣曲", "princessconnect": "公主连接", "hexenzirkel": "女巫圈", "cristales": "水晶", "vcs": "vcs", "pes": "消费", "pocketsage": "口袋智囊", "valorant": "云顶之弈", "valorante": "valorant", "valorantindian": "valorant印度", "dota": "刀塔", "madden": "疯狂麦登", "cdl": "cdl", "efootbal": "电子足球", "nba2k": "nba2k", "egames": "电子竞技", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "电子竞技", "mlg": "mlg", "leagueofdreamers": "梦之联盟", "fifa14": "fifa14", "midlaner": "中路玩家", "efootball": "电子足球", "dreamhack": "梦境黑客", "gaimin": "盖敏", "overwatchleague": "守望先锋联赛", "cybersport": "电子竞技", "crazyraccoon": "疯狂浣熊", "test1test": "测试1测试", "fc24": "fc24", "riotgames": "拳头游戏", "eracing": "一起赛车", "brasilgameshow": "巴西游戏展", "valorantcompetitive": "守望先锋竞技", "t3arena": "t3arena", "valorantbr": "valorant热爱者", "csgo": "反恐精英", "tf2": "tf2", "portal2": "传送门2", "halflife": "半生期", "left4dead": "左4死", "left4dead2": "左4死2", "valve": "阀门", "portal": "入口", "teamfortress2": "军团要塞2", "everlastingsummer": "永恒夏天", "goatsimulator": "山羊模拟器", "garrysmod": "盖瑞模组", "freedomplanet": "自由星球", "transformice": "变鼠游戏", "justshapesandbeats": "只是一堆形状和节奏", "battlefield4": "战场4", "nightinthewoods": "森林里的夜晚", "halflife2": "半条命2", "hacknslash": "砍杀游戏", "deeprockgalactic": "深岩银河", "riskofrain2": "冒雨风险2", "metroidvanias": "恶魔城类游戏", "overcooked": "煮过头了", "interplanetary": "星际", "helltaker": "地狱收集者", "inscryption": "密藏", "7d2d": "7d2d", "deadcells": "亡細胞", "nierautomata": "尼尔伪装者", "gmod": "gmod", "dwarffortress": "矮人要塞", "foxhole": "狐狸洞", "stray": "流浪", "battlefield": "战场", "battlefield1": "战地1", "swtor": "星际战争旧共和国", "fallout2": "辐射2", "uboat": "潜艇", "eyeb": "看过来", "blackdesert": "黑色沙漠", "tabletopsimulator": "桌游模拟器", "partyhard": "嗨起来", "hardspaceshipbreaker": "破碎硬宇航舱", "hades": "哈迪斯", "gunsmith": "枪匠", "okami": "狼狈", "trappedwithjester": "被小丑困住了", "dinkum": "真正的", "predecessor": "前辈", "rainworld": "雨世界", "cavesofqud": "库德洞穴", "colonysim": "殖民模拟", "noita": "noita", "dawnofwar": "战争的黎明", "minionmasters": "小黄人大师", "grimdawn": "灰烬黎明", "darkanddarker": "黑暗与更黑暗", "motox": "摩托骑士", "blackmesa": "黑山", "soulworker": "灵魂工作者", "datingsims": "恋爱模拟游戏", "yaga": "哟嘎", "cubeescape": "立方体逃脱", "hifirush": "嗨飞冲", "svencoop": "svencoop", "newcity": "新城市", "citiesskylines": "城市天际线", "defconheavy": "准备重装", "kenopsia": "空旷的感觉", "virtualkenopsia": "虚拟空洞感", "snowrunner": "雪地狂奔", "libraryofruina": "图书馆的毁灭", "l4d2": "l4d2", "thenonarygames": "非官方游戏", "omegastrikers": "超级战击手", "wayfinder": "探索者", "kenabridgeofspirits": "肯纳灵桥", "placidplasticduck": "平静塑料鸭", "battlebit": "战斗块", "ultimatechickenhorse": "终极鸡马游戏", "dialtown": "拨号镇", "smileforme": "让我微笑", "catnight": "猫咪之夜", "supermeatboy": "超级肉男孩", "tinnybunny": "小兔子", "cozygrove": "温馨小镇", "doom": "末日", "callofduty": "使命召唤", "callofdutyww2": "现代战争二世界大战", "rainbow6": "彩虹6", "apexlegends": "顶峰传说", "cod": "炸鱼", "borderlands": "边境地带", "pubg": "和平精英", "callofdutyzombies": "使命召唤僵尸", "apex": "顶尖", "r6siege": "r6围攻", "megamanx": "洛克人x", "touhou": "东方project", "farcry": "远哭", "farcrygames": "孤岛惊魂游戏", "paladins": "骑士", "earthdefenseforce": "地球防卫队", "huntshowdown": "猎杀大决战", "ghostrecon": "幽灵再现", "grandtheftauto5": "侠盗猎车手5", "warz": "战争", "sierra117": "西雅图117", "dayzstandalone": "末日生存", "ultrakill": "超杀", "joinsquad": "加入战队", "echovr": "回声虚拟现实", "discoelysium": "迪斯科伊利西亚姆", "insurgencysandstorm": "起义沙尘暴", "farcry3": "孤岛惊魂3", "hotlinemiami": "迈阿密热线", "maxpayne": "马克斯佩恩", "hitman3": "佣兵3", "r6s": "r6s", "rainbowsixsiege": "彩虹六号围攻", "deathstranding": "死亡搁浅", "b4b": "b4b", "codwarzone": "战区使命", "callofdutywarzone": "使命召唤战区", "codzombies": "死侍僵尸", "mirrorsedge": "镜子边缘", "divisions2": "分裂2", "killzone": "杀戮地带", "helghan": "赫尔根", "coldwarzombies": "冷战僵尸", "metro2033": "地铁2033", "metalgear": "合金装备", "acecombat": "王牌战斗", "crosscode": "交叉代码", "goldeneye007": "金眼007", "blackops2": "黑色行动2", "sniperelite": "狙击精英", "modernwarfare": "现代战争", "neonabyss": "霓虹深渊", "planetside2": "行星边缘2", "mechwarrior": "机甲战士", "boarderlands": "边境之地", "owerwatch": "守望先锋", "rtype": "r型", "dcsworld": "dcs世界", "escapefromtarkov": "逃离塔尔科夫", "metalslug": "伟大的金属弹幕", "primalcarnage": "原始肉食", "worldofwarships": "战舰世界", "back4blood": "血战到底", "warframe": "战争框架", "rainbow6siege": "彩虹六号围攻", "xcom": "xcom", "hitman": "杀手", "masseffect": "质感效应", "systemshock": "系统震撼", "valkyriachronicles": "女武神编年史", "specopstheline": "特种部队最后一线", "killingfloor2": "杀戮层2", "cavestory": "洞穴故事", "doometernal": "末日永恒", "centuryageofashes": "世纪灰烬时代", "farcry4": "孤岛惊魂4", "gearsofwar": "战争机器", "mwo": "mwo", "division2": "分裂2", "tythetasmaniantiger": "ty塔斯马尼亚虎", "generationzero": "零代", "enterthegungeon": "进入地牢", "jakanddaxter": "贾克与达克斯特", "modernwarfare2": "现代战争2", "blackops1": "黑色行动1", "sausageman": "香肠人", "ratchetandclank": "随心所欲与大铠甲", "chexquest": "切克探险", "thephantompain": "幻影痛", "warface": "战脸", "crossfire": "交火", "atomicheart": "原子心态", "blackops3": "黑色行动3", "vampiresurvivors": "吸血鬼生存者", "callofdutybatleroyale": "战地召唤大逃杀", "moorhuhn": "摩尔小鸡", "freedoom": "自由梦", "battlegrounds": "战场", "frag": "脆脆的", "tinytina": "小蒂娜", "gamepubg": "游戏pubg", "necromunda": "亡灵之城", "metalgearsonsoflibert": "金属齿轮自由之子", "juegosfps": "fps游戏", "convertstrike": "转换打击", "warzone2": "战区2", "shatterline": "打破界线", "blackopszombies": "黑暗行动僵尸", "bloodymess": "血腥混乱", "republiccommando": "共和国指挥官", "elitedangerous": "精英危险", "soldat": "士兵", "groundbranch": "地面分支", "squad": "小队", "destiny1": "命运1", "gamingfps": "游戏fps", "redfall": "红色陨落", "pubggirl": "pubg女孩", "worldoftanksblitz": "坦克世界闪击战", "callofdutyblackops": "使命召唤黑色行动", "enlisted": "入伍", "farlight": "远光", "farcry5": "孤岛惊魂5", "farcry6": "孤岛惊魂6", "farlight84": "farlight84", "splatoon3": "喷射战士3", "armoredcore": "装甲核心", "pavlovvr": "巴甫洛夫vr", "xdefiant": "反叛者", "tinytinaswonderlands": "小蒂娜的奇幻乐园", "halo2": "光环2", "payday2": "发工资日2", "cs16": "cs16", "pubgindonesia": "印尼绝地求生", "pubgukraine": "乌克兰pubg", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "坦克世界手机版", "pubgromania": "pubg罗马尼亚", "empyrion": "帝王之心", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "泰坦陨落2", "soapcod": "香皂鱼", "ghostcod": "幽灵编程", "csplay": "csplay", "unrealtournament": "不真实的比赛", "callofdutydmz": "使命召唤dmz", "gamingcodm": "游戏codm", "borderlands2": "无主之地2", "counterstrike": "反恐精英", "cs2": "cs2", "pistolwhip": "炮击", "callofdutymw2": "战地使命mw2", "quakechampions": "震动冠军", "halo3": "光环3", "halo": "光环", "killingfloor": "杀戮之地", "destiny2": "命运2", "exoprimal": "超兽战士", "splintercell": "分裂细胞", "neonwhite": "霓虹白", "remnant": "残余", "azurelane": "碧蓝航线", "worldofwar": "战争世界", "gunvolt": "枪电", "returnal": "重生", "halo4": "光环4", "haloreach": "哈喽扩展", "shadowman": "影子人", "quake2": "地震2", "microvolts": "微伏", "reddead": "红死", "standoff2": "对峙2", "harekat": "harekat", "battlefield3": "战场3", "lostark": "浪潮之彼", "guildwars2": "公会战争2", "fallout76": "辐射76", "elsword": "艾尔之剑", "seaofthieves": "海盗之海", "rust": "生锈", "conqueronline": "征服线上", "dauntless": "无畏", "warships": "战舰", "dayofdragons": "龙之日", "warthunder": "战争雷霆", "flightrising": "飞翔崛起", "recroom": "乐屋", "legendsofruneterra": "runeterra传说", "pso2": "pso2", "myster": "神秘", "phantasystaronline2": "幻星在线2", "maidenless": "没妹子", "ninokuni": "二之国", "worldoftanks": "坦克世界", "crossout": "划掉", "agario": "阿嘎里奥", "secondlife": "第二人生", "aion": "爱玩aion", "toweroffantasy": "幻想之塔", "netplay": "网搓", "everquest": "永恒探索", "metin2": "梦幻2", "gtaonline": "侠盗猎车手在线", "ninokunicrossworld": "二ノ国跨界世界", "reddeadonline": "红死在线", "superanimalroyale": "超级动物大战", "ragnarokonline": "仙境传说在线", "knightonline": "骑士在线", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "以撒的结合", "dragonageinquisition": "龙腾纪元审判", "codevein": "代码之魂", "eveonline": "无尽空战", "clubpenguin": "俱乐部企鹅", "lotro": "洛特罗", "wakfu": "瓦克夫", "scum": "渣渣", "newworld": "新世界", "blackdesertonline": "黑沙漠在线", "multiplayer": "多人游戏", "pirate101": "海盗101", "honorofkings": "王者荣耀", "fivem": "五m", "starwarsbattlefront": "星战前线", "karmaland": "卡玛兰", "ssbu": "ssbu", "starwarsbattlefront2": "星战前线2", "phigros": "菲格罗斯", "mmo": "多人生存", "pokemmo": "宝可梦在线", "ponytown": "小马镇", "3dchat": "3d聊天", "nostale": "怀旧之旅", "tauriwow": "太酷了", "wowclassic": "哇经典", "worldofwarcraft": "魔兽世界", "warcraft": "魔兽", "wotlk": "wotlk", "runescape": "runescape", "neopets": "小精灵", "moba": "手游", "habbo": "哈博", "archeage": "阿克亥奇", "toramonline": "托拉姆在线", "mabinogi": "摩比诺奇", "ashesofcreation": "创世灰烬", "riotmmo": "暴乱mmo", "silkroad": "丝路", "spiralknights": "螺旋骑士", "mulegend": "魔法传奇", "startrekonline": "星际迷航在线", "vindictus": "复仇者", "albiononline": "阿尔比恩在线", "bladeandsoul": "刀剑与灵魂", "evony": "埃沃尼", "dragonsprophet": "龙的先知", "grymmo": "炫酷萌萌", "warmane": "温暖魂", "multijugador": "多玩家", "angelsonline": "天使在线", "lunia": "卢尼亚", "luniaz": "伦亚兹", "idleon": "闲聊乐园", "dcuniverseonline": "dc宇宙在线", "growtopia": "成长乌托邦", "starwarsoldrepublic": "星球大战旧共和国", "grandfantasia": "大幻想", "blueprotocol": "蓝色协议", "perfectworld": "完美世界", "riseonline": "线上崛起", "corepunk": "核心朋克", "adventurequestworlds": "冒险探险世界", "flyforfun": "尽情飞翔", "animaljam": "动物快闪", "kingdomofloathing": "厌世王国", "cityofheroes": "英雄之城", "mortalkombat": "真人快打", "streetfighter": "街头斗士", "hollowknight": "空洞骑士", "metalgearsolid": "合金装备solid", "forhonor": "为荣耀", "tekken": "铁拳", "guiltygear": "罪恶装备", "xenoverse2": "异界2", "fgc": "fgc", "streetfighter6": "街头霸王6", "multiversus": "多元宇宙", "smashbrosultimate": "超级马里奥对决终极版", "soulcalibur": "灵魂刀锋", "brawlhalla": "博拉哈", "virtuafighter": "虚拟战士", "streetsofrage": "街头的愤怒", "mkdeadlyalliance": "致命联盟", "nomoreheroes": "不再有英雄", "mhr": "mhr", "mortalkombat12": "真人快打12", "thekingoffighters": "拳皇", "likeadragon": "像龙一样", "retrofightinggames": "复古格斗游戏", "blasphemous": "亵渎", "rivalsofaether": "对手的以太", "persona4arena": "女神异闻录4竞技场", "marvelvscapcom": "漫威vs卡普空", "supersmash": "超级爆破", "mugen": "mugen", "warofthemonsters": "怪兽战争", "jogosdeluta": "战斗游戏", "cyberbots": "网络机器人", "armoredwarriors": "盔甲战士", "finalfight": "决战时刻", "poweredgear": "超级装备", "beatemup": "打击游戏", "blazblue": "燃蓝", "mortalkombat9": "真人快打9", "fightgames": "战斗游戏", "killerinstinct": "杀手本能", "kingoffigthers": "拳皇", "ghostrunner": "鬼魂跑者", "chivalry2": "骑士精神2", "demonssouls": "恶魔之魂", "blazbluecrosstag": "苍蓝境交叉标签", "blazbluextagbattle": "苍蓝境界延绵战斗", "blazbluextag": "燃蓝交叉标签", "guiltygearstrive": "罪恶gearstrive", "hollowknightsequel": "空洞骑士续集", "hollowknightsilksong": "空洞骑士丝歌", "silksonghornet": "丝歌小黄蜂", "silksonggame": "丝歌游戏", "silksongnews": "丝歌新闻", "silksong": "丝歌", "undernight": "夜未眠", "typelumina": "字光", "evolutiontournament": "进化赛季", "evomoment": "进化时刻", "lollipopchainsaw": "棒棒糖链锯", "dragonballfighterz": "龙珠格斗z", "talesofberseria": "贝尔塞里亚的故事", "bloodborne": "血源诅咒", "horizon": "天际线", "pathofexile": "放逐之路", "slimerancher": "水母牧场", "crashbandicoot": "猛犸崩坏", "bloodbourne": "血缘之地", "uncharted": "未知领域", "horizonzerodawn": "地平线零之曙光", "ps4": "ps4", "ps5": "ps5", "spyro": "斯派罗", "playstationplus": "playstationplus", "lastofus": "最后生还者", "infamous": "臭名昭著", "playstationbuddies": "playstation小伙伴", "ps1": "ps1", "oddworld": "奇幻世界", "playstation5": "playstation5", "slycooper": "小偷偷蛋", "psp": "附身局", "rabbids": "兔子军团", "splitgate": "分裂之门", "persona4": "个人4", "hellletloose": "地狱放开", "gta4": "gta4", "gta": "侠盗猎车手", "roguecompany": "流氓公司", "aisomniumfiles": "梦幻档案", "gta5": "gta5", "gtasanandreas": "侠盗猎车手圣安地列斯", "godofwar": "战神", "gris": "灰", "trove": "宝藏", "detroitbecomehuman": "底特律变人", "beatsaber": "打击光剑", "rimworld": "边缘世界", "stellaris": "星际迷航", "ps3": "ps3", "untildawn": "直到黎明", "touristtrophy": "旅游奖杯", "lspdfr": "lspdfr", "shadowofthecolossus": "巨人之影", "crashteamracing": "撞车团队竞速", "fivepd": "五个pd", "tekken7": "铁拳7", "devilmaycry": "恶魔哭泣", "devilmaycry3": "恶魔城3", "devilmaycry5": "恶魔哭泣5", "ufc4": "ufc4", "playingstation": "玩具站", "samuraiwarriors": "武士勇士", "psvr2": "psvr2", "thelastguardian": "最后的守护者", "soulblade": "灵魂刀锋", "gta5rp": "gta5角色扮演", "gtav": "gta五", "playstation3": "ps3", "manhunt": "猎人游戏", "gtavicecity": "侠盗猎车手罪恶城市", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "影之心2契约", "pcsx2": "pcsx2", "lastguardian": "最后的守护者", "xboxone": "xboxone", "forza": "为力", "cd": "cd", "gamepass": "游戏通行证", "armello": "阿美洛", "partyanimal": "派对动物", "warharmmer40k": "战锤40k", "fightnightchampion": "斗夜冠军", "psychonauts": "迷幻探索者", "mhw": "mhw", "princeofpersia": "波斯王子", "theelderscrollsskyrim": "老滚天际", "pantarhei": "追随爱", "theelderscrolls": "上古卷轴", "gxbox": "gxbox", "battlefront": "战线", "dontstarvetogether": "一起别饿死", "ori": "原汁原味", "spelunky": "探险者", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "星界探索", "xboxonex": "xboxonex", "forzahorizon5": "极限竞速5", "skate3": "滑板3", "houseflipper": "翻房达人", "americanmcgeesalice": "美国麦奇的爱丽丝", "xboxs": "xboxs", "xboxseriesx": "xbox系列x", "xboxseries": "xbox系列", "r6xbox": "r6xbox", "leagueofkingdoms": "王国联盟", "fable2": "童话2", "xboxgamepass": "xbox游戏通行证", "undertale": "地底传说", "trashtv": "垃圾电视", "skycotl": "天之国", "erica": "艾瑞卡", "ancestory": "祖先", "cuphead": "小杯头", "littlemisfortune": "小坏运气", "sallyface": "莎莉脸", "franbow": "弗兰博", "monsterprom": "怪兽高中舞会", "projectzomboid": "项目僵尸", "ddlc": "ddlc", "motos": "摩托车", "outerwilds": "外野探险", "pbbg": "好玩不停的游戏", "anshi": "安式", "cultofthelamb": "小羊教", "duckgame": "鸭子游戏", "thestanleyparable": "斯坦利寓言", "towerunite": "塔楼团聚", "occulto": "隐秘", "longdrive": "长途驾车", "satisfactory": "满意", "pluviophile": "爱雨的人", "underearth": "地下", "assettocorsa": "资产赛道", "geometrydash": "几何冲刺", "kerbal": "小绿人", "kerbalspaceprogram": "克尔巴尔太空计划", "kenshi": "剑士", "spiritfarer": "灵魂渡者", "darkdome": "黑暗穹顶", "pizzatower": "披萨塔", "indiegame": "独立游戏", "itchio": "itchio", "golfit": "高尔夫玩起来", "truthordare": "真心话还是大冒险", "game": "游戏", "rockpaperscissors": "剪刀石头布", "trampoline": "蹦床", "hulahoop": "呼啦圈", "dare": "敢dare", "scavengerhunt": "寻找宝藏挑战", "yardgames": "院子游戏", "pickanumber": "随便选个数字", "trueorfalse": "真还是假", "beerpong": "啤酒乒乓", "dicegoblin": "骰子小精灵", "cosygames": "温馨游戏", "datinggames": "约会游戏", "freegame": "免费游戏", "drinkinggames": "饮酒游戏", "sodoku": "数独", "juegos": "游戏", "mahjong": "麻将", "jeux": "游戏", "simulationgames": "模拟游戏", "wordgames": "字谜游戏", "jeuxdemots": "文字游戏", "juegosdepalabras": "文字游戏", "letsplayagame": "让我们玩个游戏", "boredgames": "无聊游戏", "oyun": "游戏", "interactivegames": "互动游戏", "amtgard": "安塔加德", "staringcontests": "盯眼比赛", "spiele": "玩耍", "giochi": "游戏", "geoguessr": "地理猜谜", "iphonegames": "苹果手机游戏", "boogames": "boo游戏", "cranegame": "抓抓机", "hideandseek": "捉迷藏", "hopscotch": "跳房子", "arcadegames": "街机游戏", "yakuzagames": "黑道游戏", "classicgame": "经典游戏", "mindgames": "脑力游戏", "guessthelyric": "猜歌词", "galagames": "银河游戏", "romancegame": "恋爱游戏", "yanderegames": "病娇游戏", "tonguetwisters": "绕口令", "4xgames": "4x游戏", "gamefi": "游戏财务", "jeuxdarcades": "街机游戏", "tabletopgames": "桌上游戏", "metroidvania": "恶魔城式探险游戏", "games90": "游戏90", "idareyou": "我敢你敢", "mozaa": "魔扎", "fumitouedagames": "父母都在玩游戏", "racinggames": "赛车游戏", "ets2": "公路之旅2", "realvsfake": "真实与假冒", "playgames": "玩游戏", "gameonline": "线上游戏", "onlinegames": "线上游戏", "jogosonline": "在线游戏", "writtenroleplay": "书写角色扮演", "playaballgame": "玩球游戏", "pictionary": "画图游戏", "coopgames": "合作游戏", "jenga": "积木游戏", "wiigames": "wiigames", "highscore": "高分", "jeuxderôles": "角色扮演游戏", "burgergames": "汉堡游戏", "kidsgames": "儿童游戏", "skeeball": "街机保龄球", "nfsmwblackedition": "极品飞车摩托车黑色版", "jeuconcour": "竞赛游戏", "tcgplayer": "tcg玩家", "juegodepreguntas": "问答游戏", "gioco": "游戏", "managementgame": "管理游戏", "hiddenobjectgame": "隐藏物品游戏", "roolipelit": "游戏狂欢", "formula1game": "方程式赛车游戏", "citybuilder": "城市建设者", "drdriving": "驾驶达人", "juegosarcade": "街机游戏", "memorygames": "记忆游戏", "vulkan": "火山", "actiongames": "动作游戏", "blowgames": "吹游戏", "pinballmachines": "弹球机", "oldgames": "老游戏", "couchcoop": "沙发合作", "perguntados": "问答游戏", "gameo": "游戏社区", "lasergame": "激光游戏", "imessagegames": "imessage游戏", "idlegames": "闲置游戏", "fillintheblank": "填空题", "jeuxpc": "pc游戏", "rétrogaming": "复古游戏", "logicgames": "逻辑游戏", "japangame": "日本游戏", "rizzupgame": "撩妹游戏", "subwaysurf": "地铁冲浪", "jeuxdecelebrite": "明星游戏", "exitgames": "逃出游戏", "5vs5": "5对5", "rolgame": "滚乐游戏", "dashiegames": "dashie游戏", "gameandkill": "游戏和击杀", "traditionalgames": "传统游戏", "kniffel": "掷骰子", "gamefps": "游戏fps", "textbasedgames": "文字冒险游戏", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "幻想足球", "retrospel": "复古游戏", "thiefgame": "盗贼游戏", "lawngames": "草坪游戏", "fliperama": "翻转乐园", "heroclix": "英雄棋", "tablesoccer": "桌上足球", "tischfußball": "桌上足球", "spieleabende": "游戏之夜", "jeuxforum": "游戏论坛", "casualgames": "休闲游戏", "fléchettes": "飞镖", "escapegames": "逃脱游戏", "thiefgameseries": "盗贼游戏系列", "cranegames": "鹤游戏", "játék": "游戏", "bordfodbold": "无聊足球", "jogosorte": "游戏运势", "mage": "魔法", "cargames": "赛车游戏", "onlineplay": "线上玩", "mölkky": "摩尔基", "gamenights": "游戏之夜", "pursebingos": "包包宾果", "randomizer": "随机生成器", "msx": "小红书", "anagrammi": "变字谜", "gamespc": "游戏pc", "socialdeductiongames": "社交推理游戏", "dominos": "多米诺", "domino": "多米诺", "isometricgames": "等距游戏", "goodoldgames": "好游戏怀旧", "truthanddare": "真心和挑战", "mahjongriichi": "麻将立直", "scavengerhunts": "寻宝游戏", "jeuxvirtuel": "虚拟游戏", "romhack": "rom黑客", "f2pgamer": "免费游戏玩家", "free2play": "免费玩", "fantasygame": "幻想游戏", "gryonline": "gryonline", "driftgame": "漂移游戏", "gamesotomes": "游戏带我走", "halotvseriesandgames": "哈喽tv剧和游戏", "mushroomoasis": "蘑菇天堂", "anythingwithanengine": "任何有引擎的东西", "everywheregame": "随处可玩", "swordandsorcery": "剑与魔法", "goodgamegiving": "好游戏分享", "jugamos": "我们玩吧", "lab8games": "实验室8游戏", "labzerogames": "零实验游戏", "grykomputerowe": "电脑游戏", "virgogami": "处女折纸", "gogame": "去玩游戏", "jeuxderythmes": "节奏游戏", "minaturegames": "迷你游戏", "ridgeracertype4": "山脊赛车4", "selflovegaming": "自爱游戏", "gamemodding": "游戏改造", "crimegames": "犯罪游戏", "dobbelspellen": "双重游戏", "spelletjes": "游戏", "spacenerf": "太空软体", "charades": "猜词游戏", "singleplayer": "单人游戏", "coopgame": "合作游戏", "gamed": "游戏狂欢", "forzahorizon": "为你而起", "nexus": "连接", "geforcenow": "现在就来geforce", "maingame": "主场游戏", "kingdiscord": "国王discord", "scrabble": "拼字游戏", "schach": "象棋", "shogi": "将棋", "dandd": "叛逆与堕落", "catan": "卡坦", "ludo": "乐斗", "backgammon": "双陆游戏", "onitama": "小熊游戏", "pandemiclegacy": "疫情遗产", "camelup": "骆驼赛跑", "monopolygame": "独占游戏", "brettspiele": "桌游", "bordspellen": "桌游", "boardgame": "桌游", "sällskapspel": "桌游", "planszowe": "桌游", "risiko": "风险", "permainanpapan": "桌游", "zombicide": "丧尸杀戮", "tabletop": "桌游", "baduk": "围棋", "bloodbowl": "血碗", "cluedo": "线索游戏", "xiangqi": "象棋", "senet": "仙人掌", "goboardgame": "玩转桌游", "connectfour": "四连珠", "heroquest": "英雄探险", "giochidatavolo": "桌游", "farkle": "方块游戏", "carrom": "桌游", "tablegames": "桌游", "dicegames": "骰子游戏", "yatzy": "骰子游戏", "parchis": "帕尔奇斯", "jogodetabuleiro": "桌游круأنит", "jocuridesocietate": "社交游戏", "deskgames": "桌上游戏", "alpharius": "阿尔法瑞斯", "masaoyunları": "麻骚云", "marvelcrisisprotocol": "漫威危机协议", "cosmicencounter": "宇宙邂逅", "creationludique": "创意乐趣", "tabletoproleplay": "桌面角色扮演", "cardboardgames": "纸板游戏", "eldritchhorror": "古怪恐怖", "switchboardgames": "换个游戏吧", "infinitythegame": "无尽游戏", "kingdomdeath": "王国死亡", "yahtzee": "拱猪", "chutesandladders": "滑梯与梯子", "társas": "社交", "juegodemesa": "桌游", "planszówki": "桌游", "rednecklife": "土腥味生活", "boardom": "无聊", "applestoapples": "苹果对苹果", "jeudesociété": "社交游戏", "gameboard": "游戏板", "dominó": "多米诺", "kalah": "卡拉", "crokinole": "桌上游戏", "jeuxdesociétés": "桌游", "twilightimperium": "黄昏帝国", "horseopoly": "马术opoly", "deckbuilding": "构建卡组", "mansionsofmadness": "疯狂的豪宅", "gomoku": "五子棋", "giochidatavola": "桌游时间", "shadowsofbrimstone": "黑暗之影", "kingoftokyo": "东京之王", "warcaby": "双人跳", "táblajátékok": "桌游", "battleship": "战舰", "tickettoride": "坐车票", "deskovehry": "桌面游戏", "catán": "卡坦", "subbuteo": "桌上足球", "jeuxdeplateau": "桌游", "stolníhry": "桌游", "xiángqi": "象棋", "jeuxsociete": "桌游", "gesellschaftsspiele": "桌游", "starwarslegion": "星战军团", "gochess": "去下棋", "weiqi": "围棋", "jeuxdesocietes": "桌游", "terraria": "泰拉瑞亚", "dsmp": "dsmp", "warzone": "战区", "arksurvivalevolved": "方舟生存进化", "dayz": "天日", "identityv": "身份v", "theisle": "岛屿", "thelastofus": "最后的我们", "nomanssky": "无人的天空", "subnautica": "海底探险", "tombraider": "古墓丽影", "callofcthulhu": "召唤克苏鲁", "bendyandtheinkmachine": "弯曲与墨水机器", "conanexiles": "柯南放逐者", "eft": "eft", "amongus": "我们之中", "eco": "生态", "monkeyisland": "猴子岛", "valheim": "瓦尔海姆", "planetcrafter": "行星造物者", "daysgone": "时光荏苒", "fobia": "恐惧症", "witchit": "女巫一下", "pathologic": "病态", "zomboid": "僵尸逆袭", "northgard": "北方领地", "7dtd": "7dtd", "thelongdark": "漫漫黑暗", "ark": "方舟", "grounded": "扎根", "stateofdecay2": "衰变2", "vrising": "维尔崛起", "madfather": "疯狂爸爸", "dontstarve": "别饿着", "eternalreturn": "永恒回归", "pathoftitans": "巨人之路", "frictionalgames": "摩擦游戏", "hexen": "女巫", "theevilwithin": "内心的恶魔", "realrac": "真实竞赛", "thebackrooms": "后室", "backrooms": "后室", "empiressmp": "帝国之声", "blockstory": "封锁故事", "thequarry": "采石场", "tlou": "tlou", "dyinglight": "绝地光辉", "thewalkingdeadgame": "行尸走肉游戏", "wehappyfew": "我们开心的一小撮", "riseofempires": "帝国崛起", "stateofsurvivalgame": "生存游戏状态", "vintagestory": "复古故事", "arksurvival": "方舟生存", "barotrauma": "潜水压伤", "breathedge": "呼吸边缘", "alisa": "阿丽莎", "westlendsurvival": "西部生存挑战", "beastsofbermuda": "百慕大野兽", "frostpunk": "霜朋克", "darkwood": "黑木", "survivalhorror": "生存恐怖", "residentevil": "生化危机", "residentevil2": "生化危机2", "residentevil4": "生化危机4", "residentevil3": "生化危机3", "voidtrain": "空洞列车", "lifeaftergame": "游戏后的生活", "survivalgames": "生存游戏", "sillenthill": "无声小镇", "thiswarofmine": "这场我的战争", "scpfoundation": "scp基金会", "greenproject": "绿色项目", "kuon": "酷恩", "cryoffear": "克服恐惧", "raft": "筏子", "rdo": "rdo", "greenhell": "绿地狱", "residentevil5": "生化危机5", "deadpoly": "死聚合", "residentevil8": "生化危机8", "onironauta": "梦行者", "granny": "奶奶", "littlenightmares2": "小夜魇2", "signalis": "信号", "amandatheadventurer": "阿曼达头号冒险家", "sonsoftheforest": "森林之子", "rustvideogame": "生锈游戏", "outlasttrials": "超越考验", "alienisolation": "外星人孤立", "undawn": "未曙光", "7day2die": "七天死去", "sunlesssea": "无日海", "sopravvivenza": "生存技巧", "propnight": "道具之夜", "deadisland2": "死亡岛2", "ikemensengoku": "帅哥战国", "ikemenvampire": "帅气吸血鬼", "deathverse": "死亡宇宙", "cataclysmdarkdays": "灾难中的黑暗日子", "soma": "小小的soma", "fearandhunger": "恐惧与饥饿", "stalkercieńczarnobyla": "黑影跟踪者", "lifeafter": "生活之后", "ageofdarkness": "黑暗时代", "clocktower3": "时钟塔3", "aloneinthedark": "黑暗中的孤独", "medievaldynasty": "中世纪王朝", "projectnimbusgame": "项目云雀游戏", "eternights": "永夜", "craftopia": "手作乐园", "theoutlasttrials": "最后生还者试炼", "bunker": "掩体", "worlddomination": "世界统治", "rocketleague": "火箭联盟", "tft": "tft", "officioassassinorum": "公务刺客", "necron": "死灵", "wfrp": "wfrp", "dwarfslayer": "杀矮人者", "warhammer40kcrush": "战锤40k心动", "wh40": "wh40", "warhammer40klove": "战锤40k爱", "warhammer40klore": "战锤40k传说", "warhammer": "战锤", "warhammer30k": "战锤30k", "warhammer40k": "战锤40k", "warhammer40kdarktide": "战锤40k黑暗潮流", "totalwarhammer3": "全面战争战锤3", "temploculexus": "模板酷玩", "vindicare": "复仇", "ilovesororitas": "我爱姐妹们", "ilovevindicare": "我爱vindicare", "iloveassasinorum": "我爱刺客信条", "templovenenum": "模板爱你呢", "templocallidus": "模板聪明", "templomaerorus": "模板马尔罗斯", "templovanus": "模板巨兽", "oficioasesinorum": "暗杀职业", "tarkov": "塔科夫", "40k": "40万", "tetris": "俄罗斯方块", "lioden": "狮子王", "ageofempires": "帝国时代", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "战锤永恒之战", "civilizationv": "文明v", "ittakestwo": "一起双人冒险", "wingspan": "展翅飞翔", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "英雄无敌", "btd6": "btd6", "supremecommander": "超级指挥官", "ageofmythology": "神话时代", "args": "args", "rime": "别再犹豫了", "planetzoo": "星球动物园", "outpost2": "前哨2", "banished": "被放逐", "caesar3": "凯撒3", "redalert": "红色警报", "civilization6": "<PERSON><PERSON>6", "warcraft2": "魔兽争霸2", "commandandconquer": "征服与统治", "warcraft3": "魔兽争霸3", "eternalwar": "永恒战争", "strategygames": "策略游戏", "anno2070": "2070年", "civilizationgame": "文明游戏", "civilization4": "文明4", "factorio": "工厂大亨", "dungeondraft": "地下草图", "spore": "孢子", "totalwar": "全面战争", "travian": "迁徙", "forts": "堡垒", "goodcompany": "好公司", "civ": "公民", "homeworld": "家园", "heidentum": "海德教", "aoe4": "aoe4", "hnefatafl": "黑暗棋", "fasterthanlight": "比光速还快", "forthekings": "为了国王", "realtimestrategy": "实时战略", "starctaft": "星际工厂", "sidmeierscivilization": "西德梅尔的文明", "kingdomtwocrowns": "王国双冠", "eu4": "欧盟4", "vainglory": "虚荣", "ww40k": "ww40k", "godhood": "神格", "anno": "安诺", "battletech": "战斗科技", "malifaux": "马里法克斯", "w40k": "w40k", "hattrick": "帽子戏法", "davesfunalgebraclass": "大卫的有趣代数课", "plagueinc": "瘟疫公司", "theorycraft": "理论craft", "mesbg": "我的热爱的朋友", "civilization3": "文明3", "4inarow": "4连庄", "crusaderkings3": "十字军之王3", "heroes3": "英雄3", "advancewars": "进击的战士", "ageofempires2": "帝国时代2", "disciples2": "弟子2", "plantsvszombies": "植物大战僵尸", "giochidistrategia": "战略游戏", "stratejioyunları": "策略游戏", "europauniversalis4": "欧陆风云4", "warhammervermintide2": "战锤贼鼠2", "ageofwonders": "奇迹时代", "dinosaurking": "恐龙王", "worldconquest": "征服世界", "heartsofiron4": "铁心4", "companyofheroes": "英雄公司", "battleforwesnoth": "争夺维斯诺斯", "aoe3": "aoe3", "forgeofempires": "帝国锻造", "warhammerkillteam": "战锤杀队", "goosegooseduck": "鹅鹅鸭", "phobies": "畏惧", "phobiesgame": "恐惧游戏", "gamingclashroyale": "游戏clashroyale", "adeptusmechanicus": "机械大师", "outerplane": "外星平面", "turnbased": "回合制", "bomberman": "炸弹人", "ageofempires4": "帝国时代4", "civilization5": "<PERSON><PERSON>5", "victoria2": "维多利亚2", "crusaderkings": "十字军之王", "cultris2": "刀具争霸2", "spellcraft": "拼字魔法", "starwarsempireatwar": "星战帝国战争", "pikmin4": "皮克敏4", "anno1800": "anno1800", "estratégia": "策略", "popfulmail": "小邮差", "shiningforce": "闪耀力量", "masterduel": "大师对决", "dysonsphereprogram": "戴森球计划", "transporttycoon": "运输大亨", "unrailed": "无轨道", "magicarena": "魔法竞技场", "wolvesville": "狼村", "ooblets": "ooblets", "planescapetorment": "飞往解脱", "uplandkingdoms": "高地王国", "galaxylife": "星际生活", "wolvesvilleonline": "狼村在线", "slaythespire": "一击制胜", "battlecats": "战斗猫咪", "sims3": "模拟人生3", "sims4": "模拟城市4", "thesims4": "模拟人生4", "thesims": "模拟人生", "simcity": "模拟城市", "simcity2000": "模拟城市2000", "sims2": "模拟人生2", "iracing": "在线赛车", "granturismo": "驾驶狂潮", "needforspeed": "追求速度", "needforspeedcarbon": "飙车碳素", "realracing3": "真实赛车3", "trackmania": "轨道狂热", "grandtourismo": "超豪之旅", "gt7": "gt7", "simsfreeplay": "模拟人生免费玩", "ts4": "ts4", "thesims2": "模拟人生2", "thesims3": "模拟人生3", "thesims1": "模拟人生1", "lossims4": "失去模拟人生4", "fnaf": "孩子们都爱五夜后宫", "outlast": "撑过来", "deadbydaylight": "死神的黄昏", "alicemadnessreturns": "爱丽丝疯狂回归", "darkhorseanthology": "暗马选集", "phasmophobia": "鬼魂恐惧症", "fivenightsatfreddys": "弗雷迪的五个夜晚", "saiko": "赛寇", "fatalframe": "致命框架", "littlenightmares": "小噩梦", "deadrising": "死而复生", "ladydimitrescu": "迪米特雷斯库女士", "homebound": "宅家", "deadisland": "死亡岛", "litlemissfortune": "小小姐不幸", "projectzero": "零计划", "horory": "恐怖派", "jogosterror": "慢跑恐惧", "helloneighbor": "你好邻居", "helloneighbor2": "你好邻居2", "gamingdbd": "游戏dbd", "thecatlady": "猫咪控", "jeuxhorreur": "恐怖游戏", "horrorgaming": "恐怖游戏", "magicthegathering": "魔法聚会", "mtg": "魔会", "tcg": "卡牌游戏", "cardsagainsthumanity": "对抗人性的卡牌", "cribbage": "cribbage", "minnesotamtg": "明尼苏达房贷", "edh": "玩edh", "monte": "蒙特", "pinochle": "拼诺克莱", "codenames": "代号", "dixit": "得意了", "bicyclecards": "自行车卡片", "lor": "咯", "euchre": "优克扑克", "thegwent": "游戯王", "legendofrunetera": "符文之地传奇", "solitaire": "接龙", "poker": "扑克", "hearthstone": "炉石传说", "uno": "一诺", "schafkopf": "撕牌", "keyforge": "钥匙铸造", "cardtricks": "扑克牌把戏", "playingcards": "扑克牌", "marvelsnap": "漫威快拍", "ginrummy": "吉米麻将", "netrunner": "网跑者", "gwent": "昆特牌", "metazoo": "元宇宙", "tradingcards": "交易卡牌", "pokemoncards": "宝可梦卡牌", "fleshandbloodtcg": "血肉之躯tcg", "sportscards": "运动卡片", "cardfightvanguard": "卡牌战斗先锋", "duellinks": "决斗链接", "spades": "扑克牌", "warcry": "wattle音乐", "digimontcg": "数码宝可梦卡牌游戏", "toukenranbu": "刀剑乱舞", "kingofhearts": "红心王", "truco": "窍门", "loteria": "彩票", "hanafuda": "花札", "theresistance": "抵抗", "transformerstcg": "变形金刚tcg", "doppelkopf": "双面头", "yugiohcards": "游戏王卡片", "yugiohtcg": "游戏王tcg", "yugiohduel": "游戏王对战", "yugiohocg": "游戏王卡牌游戏", "dueldisk": "对决盘", "yugiohgame": "游戏王", "darkmagician": "黑暗魔法师", "blueeyeswhitedragon": "蓝眼白龙", "yugiohgoat": "游戏王羊年", "briscas": "比斯卡斯", "juegocartas": "卡牌游戏", "burraco": "博拉克", "rummy": "拉米", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "多重游戏", "mtgcommander": "万智牌指挥官", "cotorro": "鸽子屋", "jeuxdecartes": "纸牌游戏", "mtgjudge": "mtg裁判", "juegosdecartas": "纸牌游戏", "duelyst": "对决者", "mtgplanschase": "mtg计划追逐", "mtgpreconcommander": "预构指挥官", "kartenspiel": "桌游", "carteado": "卡牌游戏", "sueca": "苏埃卡", "beloteonline": "线上比洛特", "karcianki": "卡牌游戏", "battlespirits": "战斗灵魂", "battlespiritssaga": "战斗灵魂传奇", "jogodecartas": "卡牌游戏", "žolíky": "球球", "facecard": "面卡", "cardfight": "卡片战斗", "biriba": "必立吧", "deckbuilders": "构筑者", "marvelchampions": "漫威冠军", "magiccartas": "魔法卡片", "yugiohmasterduel": "游戏王大师决斗", "shadowverse": "影之诗", "skipbo": "跳跃宝", "unstableunicorns": "不稳定的独角兽", "cyberse": "网络迷因", "classicarcadegames": "经典街机游戏", "osu": "osu", "gitadora": "吉他达人", "dancegames": "舞蹈游戏", "fridaynightfunkin": "周五夜狂欢", "fnf": "fnf", "proseka": "诗歌家", "projectmirai": "镜音计划", "projectdiva": "项目女神", "djmax": "djmax", "guitarhero": "吉他英雄", "clonehero": "克隆英雄", "justdance": "尽情跳舞", "hatsunemiku": "初音未来", "prosekai": "prosekai", "rocksmith": "摇滚匠", "idolish7": "偶像梦7", "rockthedead": "摇滚死去的一切", "chunithm": "春意节奏", "idolmaster": "偶像大师", "dancecentral": "舞动中心", "rhythmgamer": "节奏玩家", "stepmania": "步法狂热", "highscorerythmgames": "高分节奏游戏", "pkxd": "pkxd", "sidem": "边边", "ongeki": "音乐冲击", "soundvoltex": "音速对冲", "rhythmheaven": "节奏天堂", "hypmic": "音频赛道", "adanceoffireandice": "燃冰舞争夺战", "auditiononline": "线上试镜", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "节奏游戏", "cryptofthenecrodancer": "尸体舞者的加密币", "rhythmdoctor": "节奏医生", "cubing": "魔方", "wordle": "字谜", "teniz": "网球", "puzzlegames": "解谜游戏", "spotit": "发现它", "rummikub": "拉密牌", "blockdoku": "方块独奏", "logicpuzzles": "逻辑谜题", "sudoku": "数独", "rubik": "魔方", "brainteasers": "脑筋急转弯", "rubikscube": "魔方", "crossword": "填字游戏", "motscroisés": "填字游戏", "krzyżówki": "填字游戏", "nonogram": "拼图游戏", "bookworm": "读书虫", "jigsawpuzzles": "拼图游戏", "indovinello": "猜谜游戏", "riddle": "谜题", "riddles": "谜语", "rompecabezas": "拼图", "tekateki": "猜谜游戏", "inside": "里面", "angrybirds": "愤怒的小鸟", "escapesimulator": "逃脱模拟器", "minesweeper": "扫雷", "puzzleanddragons": "拼图与龙", "crosswordpuzzles": "填字游戏", "kurushi": "苦噜诗", "gardenscapesgame": "花园风景游戏", "puzzlesport": "拼图运动", "escaperoomgames": "密室逃脱游戏", "escapegame": "逃脱游戏", "3dpuzzle": "3d拼图", "homescapesgame": "家园奇境游戏", "wordsearch": "字谜搜索", "enigmistica": "谜题爱好者", "kulaworld": "酷拉世界", "myst": "神秘", "riddletales": "谜语故事", "fishdom": "鱼乐世界", "theimpossiblequiz": "不可能的测验", "candycrush": "糖果粉碎", "littlebigplanet": "小大球星球", "match3puzzle": "消除三连拼图", "huniepop": "爱心泡泡", "katamaridamacy": "卡塔玛里达玛西", "kwirky": "奇奇怪怪", "rubikcube": "魔方", "cuborubik": "魔方博弈", "yapboz": "yapboz", "thetalosprinciple": "塔洛斯原则", "homescapes": "家园风景", "puttputt": "迷你高尔夫", "qbert": "吃挂逼", "riddleme": "谜题来猜", "tycoongames": "大亨游戏", "cubosderubik": "魔方小方块", "cruciverba": "填字游戏", "ciphers": "密码", "rätselwörter": "谜语词", "buscaminas": "扫雷", "puzzlesolving": "解谜", "turnipboy": "小萝卜男孩", "adivinanzashot": "猜谜热潮", "nobodies": "没人啊", "guessing": "猜测", "nonograms": "非ograms", "kostkirubika": "魔方花式组合", "crypticcrosswords": "隐秘词谜", "syberia2": "西伯利亚2", "puzzlehunt": "拼图狩猎", "puzzlehunts": "解谜寻宝", "catcrime": "猫咪犯罪", "quebracabeça": "脑筋急转弯", "hlavolamy": "解谜游戏", "poptropica": "波波特罗皮卡", "thelastcampfire": "最后的营火", "autodefinidos": "自我定义", "picopark": "皮口公园", "wandersong": "漫游之歌", "carto": "卡托", "untitledgoosegame": "无标题鹅游戏", "cassetête": "卡斯泰特", "limbo": "摇摆不定", "rubiks": "魔方", "maze": "迷宫", "tinykin": "微小伙伴", "rubikovakostka": "魔方游戏", "speedcube": "速拼魔方", "pieces": "碎片", "portalgame": "门户游戏", "bilmece": "谜语", "puzzelen": "拼图", "picross": "画格游戏", "rubixcube": "魔方", "indovinelli": "脑筋急转弯", "cubomagico": "魔方", "mlbb": "mlbb", "pubgm": "和平精英", "codmobile": "战斗手机", "codm": "codm", "twistedwonderland": "扭曲仙境", "monopoly": "垄断", "futurefight": "未来战斗", "mobilelegends": "移动传说", "brawlstars": "乱斗明星", "brawlstar": "乱斗之星", "coc": "coc", "lonewolf": "孤狼", "gacha": "扭蛋", "wr": "写作挑战", "fgo": "fgo", "bitlife": "人生模拟", "pikminbloom": "皮卡米奇花园", "ff": "嗨", "ensemblestars": "偶像星团", "asphalt9": "沥青9", "mlb": "大联盟", "cookierunkingdom": "饼干跑酷王国", "alchemystars": "炼金星辰", "stateofsurvival": "幸存者的状态", "mycity": "我的城市", "arknights": "明日方舟", "colorfulstage": "多彩舞台", "bloonstowerdefense": "气球塔防", "btd": "btd", "clashroyale": "冲突royale", "angela": "安吉拉", "dokkanbattle": "dokkanbattle", "fategrandorder": "命运冠位指定", "hyperfront": "超前锋", "knightrun": "骑士狂奔", "fireemblemheroes": "火焰纹章英雄", "honkaiimpact": "崩坏影响", "soccerbattle": "足球大战", "a3": "a3", "phonegames": "手机游戏", "kingschoice": "王者之选", "guardiantales": "守护者故事", "petrolhead": "车迷", "tacticool": "战术酷", "cookierun": "饼干跑", "pixeldungeon": "像素地牢", "arcaea": "arcaea", "outoftheloop": "太落伍了", "craftsman": "匠人", "supersus": "超级可疑", "slowdrive": "慢速驾驶", "headsup": "提前知晓", "wordfeud": "字战", "bedwars": "床战", "freefire": "免费火拼", "mobilegaming": "移动游戏", "lilysgarden": "lilysgarden", "farmville2": "农场之旅2", "animalcrossing": "动物之森", "bgmi": "bgmi", "teamfighttactics": "团战战术", "clashofclans": "部落冲突", "pjsekai": "pjsekai", "mysticmessenger": "神秘信使", "callofdutymobile": "使命召唤手游", "thearcana": "神秘之弧", "8ballpool": "8号台球", "emergencyhq": "紧急总部", "enstars": "星之团", "randonautica": "随意冒险", "maplestory": "枫之谷", "albion": "艾尔比恩", "hayday": "收割季节", "onmyoji": "阴阳师", "azurlane": "碧蓝航线", "shakesandfidget": "摇摇贪吃蛇", "ml": "ml", "bangdream": "梦之队", "clashofclan": "部落冲突", "starstableonline": "星光马场在线", "dragonraja": "龙之猎手", "timeprincess": "时间公主", "beatstar": "节奏之星", "dragonmanialegend": "龙人传奇", "hanabi": "烟花", "disneymirrorverse": "迪士尼镜像宇宙", "pocketlove": "口袋爱", "androidgames": "安卓游戏", "criminalcase": "犯罪案件", "summonerswar": "召唤师战争", "cookingmadness": "厨艺疯狂", "dokkan": "斗图", "aov": "aov", "triviacrack": "百trivia棋", "leagueofangels": "天使联盟", "lordsmobile": "领主的移动", "tinybirdgarden": "小鸟花园", "gachalife": "扭蛋人生", "neuralcloud": "脑云", "mysingingmonsters": "我的唱歌怪兽", "nekoatsume": "猫咪收集nekoatsume", "bluearchive": "蓝档案", "raidshadowlegends": "掠夺阴影传奇", "warrobots": "战斗机器人", "mirrorverse": "镜界", "pou": "泡", "warwings": "战争之翼", "fifamobile": "国际足联手机", "mobalegendbangbang": "摸宝传奇嗨嗨", "evertale": "永恒传说", "futime": "放松时光", "antiyoy": "反yoy", "apexlegendmobile": "apex传奇手机", "ingress": "进入", "slugitout": "好好干一场", "mpl": "mpl", "coinmaster": "币大师", "punishinggrayraven": "惩罚灰乌鸦", "petpals": "宠物伙伴", "gameofsultans": "苏丹的大游戏", "arenabreakout": "竞技场突破", "wolfy": "狼性", "runcitygame": "跑城游戏", "juegodemovil": "手机游戏", "avakinlife": "avakin生活", "kogama": "可可阿玛", "mimicry": "模仿秀", "blackdesertmobile": "黑沙漠手游", "rollercoastertycoon": "过山车大亨", "grandchase": "大追逐", "bombmebrasil": "炸我巴西", "ldoe": "ldoe", "legendonline": "传奇在线", "otomegame": "乙女游戏", "mindustry": "心力产业", "callofdragons": "召唤巨龙", "shiningnikki": "闪耀的nikki", "carxdriftracing2": "车手漂移竞速2", "pathtonowhere": "无路可走", "sealm": "封印", "shadowfight3": "影子格斗3", "limbuscompany": "边界公司", "demolitionderby3": "拆除德比3", "wordswithfriends2": "小伙伴一起玩词语2", "soulknight": "灵魂骑士", "purrfecttale": "喵完美故事", "showbyrock": "秀摇乐队", "ladypopular": "女士流行", "lolmobile": "笑手机", "harvesttown": "收获镇", "perfectworldmobile": "完美世界手游", "empiresandpuzzles": "帝国与拼图", "empirespuzzles": "帝国拼图", "dragoncity": "龙城", "garticphone": "画画电话", "battlegroundmobileind": "战场手机独立", "fanny": "屁屁", "littlenightmare": "小噩梦", "aethergazer": "aethergazer", "mudrunner": "泥地狂奔", "tearsofthemis": "流泪的米斯", "eversoul": "永灵", "gunbound": "战斗狂热", "gamingmlbb": "游戏mlbb", "dbdmobile": "dbdmobile", "arknight": "明日方舟", "pristontale": "普里斯顿传说", "zombiecastaways": "丧尸流亡者", "eveechoes": "eveechoes", "jogocelular": "手机跑步", "mariokarttour": "马里奥卡丁车之旅", "zooba": "zooba", "mobilelegendbangbang": "手机传说bangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "厨娘", "cabalmobile": "卡巴移动", "streetfighterduel": "街头斗士决斗", "lesecretdhenri": "亨利的秘密", "gamingbgmi": "游戏bgmi", "girlsfrontline": "女孩子前线", "jurassicworldalive": "侏罗纪世界活起来", "soulseeker": "找灵魂", "gettingoverit": "克服它", "openttd": "开源运输模拟器", "onepiecebountyrush": "一块儿赏金冲刺", "moonchaistory": "月茶历史", "carxdriftracingonline": "车手漂移在线", "jogosmobile": "手机游戏", "legendofneverland": "梦幻传奇", "pubglite": "绝地求生轻量版", "gamemobilelegends": "游戏传奇手机", "timeraiders": "时光掠夺者", "gamingmobile": "手游", "marvelstrikeforce": "漫威打击力量", "thebattlecats": "战斗猫", "dnd": "龙与地下城", "quest": "寻宝", "giochidiruolo": "角色扮演游戏", "dnd5e": "dnd5e", "rpgdemesa": "桌游rpg", "worldofdarkness": "黑暗世界", "travellerttrpg": "旅行者桌游", "2300ad": "2300年", "larp": "角色扮演", "romanceclub": "浪漫俱乐部", "d20": "d20", "pokemongames": "宝可梦游戏", "pokemonmysterydungeon": "宝可梦迷宫探险", "pokemonlegendsarceus": "宝可梦传说阿尔宙斯", "pokemoncrystal": "宝可梦水晶", "pokemonanime": "宝可梦动漫", "pokémongo": "宝可梦go", "pokemonred": "宝可梦红版", "pokemongo": "口袋妖怪go", "pokemonshowdown": "宝可梦对决", "pokemonranger": "宝可梦游侠", "lipeep": "小魔怪", "porygon": "波利刚", "pokemonunite": "宝可梦联合战斗", "entai": "舔合", "hypno": "催眠", "empoleon": "帝王蝎", "arceus": "阿尔ceus", "mewtwo": "美图二", "paldea": "帕尔德亚", "pokemonscarlet": "宝可梦猩红", "chatot": "聊天特派员", "pikachu": "皮卡丘", "roxie": "roxi", "pokemonviolet": "口袋妖怪紫色版", "pokemonpurpura": "精灵紫", "ashketchum": "阿什凯奇姆", "gengar": "月影", "natu": "自然", "teamrocket": "小队火箭", "furret": "毛毛虫", "magikarp": "鲤鱼王", "mimikyu": "咪咪可忧", "snorlax": "睡神", "pocketmonsters": "口袋怪兽", "nuzlocke": "诺兹洛克", "pokemonplush": "宝可梦毛绒玩具", "teamystic": "团队神秘", "pokeball": "宝贝球", "charmander": "小火龙", "pokemonromhack": "宝可梦rom黑客", "pubgmobile": "和平精英", "litten": "燃爆", "shinypokemon": "闪亮宝可梦", "mesprit": "美斯普瑞特", "pokémoni": "宝可梦i", "ironhands": "铁拳", "kabutops": "盔甲鸟", "psyduck": "呆呆兽", "umbreon": "黑夜露露", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "波克比", "pokemonsleep": "宝可梦睡觉", "heyyoupikachu": "嘿皮卡丘", "pokémonmaster": "精灵宝可梦大师", "pokémonsleep": "宝可梦睡觉", "kidsandpokemon": "孩子和宝可梦", "pokemonsnap": "精灵拍照", "bulbasaur": "妙蛙种子", "lucario": "路卡里欧", "charizar": "秀魅力", "shinyhunter": "闪光猎人", "ajedrez": "国际象棋", "catur": "下象棋", "xadrez": "国际象棋", "scacchi": "象棋", "schaken": "霸屏", "skak": "滑滑", "ajedres": "国际象棋", "chessgirls": "棋姬", "magnuscarlsen": "马库斯卡尔森", "worldblitz": "世界闪电", "jeudéchecs": "下棋", "japanesechess": "日本棋类", "chinesechess": "中国象棋", "chesscanada": "加拿大国际象棋", "fide": "沿途", "xadrezverbal": "言语象棋", "openings": "开局", "rook": "新手", "chesscom": "棋乐无穷", "calabozosydragones": "地下城与龙", "dungeonsanddragon": "地下城与龙", "dungeonmaster": "地下城管理员", "tiamat": "提亚玛特", "donjonsetdragons": "boo地下城与巨龙", "oxventure": "冒险探险", "darksun": "暗太阳", "thelegendofvoxmachina": "机械公主演传奇", "doungenoanddragons": "带着boo一起玩龙与地下城", "darkmoor": "黑暗沼泽", "minecraftchampionship": "我的世界锦标赛", "minecrafthive": "我的minecraft群蜂", "minecraftbedrock": "我的世界基岩", "dreamsmp": "梦境多人游戏", "hermitcraft": "隐士工坊", "minecraftjava": "minecraftjava", "hypixelskyblock": "嗨像素天空岛", "minetest": "矿工测试", "hypixel": "嗨像素", "karmaland5": "karmaland5", "minecraftmods": "我的世界模组", "mcc": "麦克尔的俱乐部", "candleflame": "烛光", "fru": "果汁", "addons": "插件", "mcpeaddons": "mcpe附加包", "skyblock": "天空岛", "minecraftpocket": "我的世界口袋版", "minecraft360": "我的世界360", "moddedminecraft": "修改版我的世界", "minecraftps4": "我的世界ps4", "minecraftpc": "我的世界pc", "betweenlands": "两界之间", "minecraftdungeons": "我的世界地下城", "minecraftcity": "我的世界城市", "pcgamer": "电脑游戏玩家", "jeuxvideo": "游戏视频", "gambit": "策略", "gamers": "游戏玩家", "levelup": "提升自我", "gamermobile": "游戏手机", "gameover": "游戏结束", "gg": "gg", "pcgaming": "电脑游戏", "gamen": "游戏人", "oyunoynamak": "玩游戏", "pcgames": "电脑游戏", "casualgaming": "休闲游戏", "gamingsetup": "游戏装备设置", "pcmasterrace": "电脑大师赛", "pcgame": "电脑游戏", "gamerboy": "游戏男孩", "vrgaming": "虚拟现实游戏", "drdisrespect": "不尊重博士", "4kgaming": "4k游戏", "gamerbr": "游戏者br", "gameplays": "游戏实况", "consoleplayer": "主机玩家", "boxi": "盒子", "pro": "专业", "epicgamers": "史诗玩家", "onlinegaming": "线上游戏", "semigamer": "半个游戏玩家", "gamergirls": "游戏女孩", "gamermoms": "游戏妈妈", "gamerguy": "游戏男孩", "gamewatcher": "游戏观察者", "gameur": "游戏达人", "grypc": "羁绊", "rangugamer": "让你游戏", "gamerschicas": "游戏女孩", "otoge": "歌玩", "dedsafio": "死定了", "teamtryhard": "团队拼劲", "mallugaming": "mallugaming", "pawgers": "毛茸茸", "quests": "任务", "alax": "阿拉克斯", "avgn": "avgn", "oldgamer": "老玩家", "cozygaming": "舒适游戏", "gamelpay": "gamelpay", "juegosdepc": "电脑游戏", "dsswitch": "切换ds", "competitivegaming": "竞争游戏", "minecraftnewjersey": "迈inecraft新泽西", "faker": "假装者", "pc4gamers": "游戏玩家的电脑", "gamingff": "玩游戏ff", "yatoro": "野兔", "heterosexualgaming": "异性恋游戏", "gamepc": "游戏pc", "girlsgamer": "女孩玩家", "fnfmods": "fnf男孩", "dailyquest": "每日挑战", "gamegirl": "游戏女孩", "chicasgamer": "女孩玩家", "gamesetup": "游戏准备", "overpowered": "压倒性的", "socialgamer": "社交游戏者", "gamejam": "游戏狂欢", "proplayer": "职业玩家", "roleplayer": "角色扮演者", "myteam": "我的团队", "republicofgamers": "玩家共和国", "aorus": "超频逆袭", "cougargaming": "猎豹游戏", "triplelegend": "三重传奇", "gamerbuddies": "游戏伙伴", "butuhcewekgamers": "需要女玩家", "christiangamer": "基督徒玩家", "gamernerd": "游戏宅", "nerdgamer": "宅男游戏玩家", "afk": "暂离", "andregamer": "安德烈游戏玩家", "casualgamer": "休闲玩家", "89squad": "89小队", "inicaramainnyagimana": "一起搞事情", "insec": "不安", "gemers": "游戏人", "oyunizlemek": "看游戏", "gamertag": "游戏昵称", "lanparty": "游戏聚会", "videogamer": "电玩玩家", "wspólnegranie": "一起玩", "mortdog": "死狗", "playstationgamer": "ps玩家", "justinwong": "吴俊杰", "healthygamer": "健康玩家", "gtracing": "gtracing", "notebookgamer": "笔记本玩家", "protogen": "原生兽", "womangamer": "女游戏玩家", "obviouslyimagamer": "显然我是个游戏玩家", "mario": "马里奥", "papermario": "纸片马里奥", "mariogolf": "马里奥高尔夫", "samusaran": "萨姆萨然", "forager": "觅食者", "humanfallflat": "人跌倒扁平", "supernintendo": "超级任天堂", "nintendo64": "任天堂64", "zeroescape": "零逃脱", "waluigi": "瓦鲁伊吉", "nintendoswitch": "任天堂开关", "nintendosw": "任天堂开关", "nintendomusic": "任天堂音乐", "sonicthehedgehog": "刺猬索尼克", "sonic": "声波", "fallguys": "秋天小子", "switch": "换吧", "zelda": "塞尔达", "smashbros": "超级马里奥兄弟", "legendofzelda": "塞尔达传奇", "splatoon": "喷射战士", "metroid": "银河战士", "pikmin": "皮克敏", "ringfit": "环形健身", "amiibo": "amiibo", "megaman": "魔界战士", "majorasmask": "大地之帽", "mariokartmaster": "马里奥赛车大师", "wii": "维达", "aceattorney": "逆转裁判", "ssbm": "ssbm", "skychildrenofthelight": "天空之光的孩子们", "tomodachilife": "友达生活", "ahatintime": "时间的帽子", "tearsofthekingdom": "王国泪痕", "walkingsimulators": "散步模拟器", "nintendogames": "任天堂游戏", "thelegendofzelda": "塞尔达传说", "dragonquest": "龙之冒险", "harvestmoon": "丰收月", "mariobros": "马里奥兄弟", "runefactory": "符文工厂", "banjokazooie": "班乔卡祖伊", "celeste": "天蓝", "breathofthewild": "野外呼吸", "myfriendpedro": "我的朋友pedro", "legendsofzelda": "塞尔达传说", "donkeykong": "大金刚", "mariokart": "马里奥卡丁车", "kirby": "库比", "51games": "51游戏", "earthbound": "地球bound", "tales": "故事", "raymanlegends": "雷曼传奇", "luigismansion": "路易鬼屋", "animalcrosssing": "动物过街", "taikonotatsujin": "太空高手", "nintendo3ds": "任天堂3ds", "supermariobros": "超级玛丽兄弟", "mariomaker2": "mariomaker2", "boktai": "博克泰", "smashultimate": "粉碎终极", "nintendochile": "任天堂智利", "tloz": "tloz", "trianglestrategy": "三角策略", "supermariomaker": "超级马里奥制作器", "xenobladechronicles3": "异度之刃编年史3", "supermario64": "超级马里奥64", "conkersbadfurday": "栗子糟糕毛发日", "nintendos": "任天堂", "new3ds": "新3ds", "donkeykongcountry2": "大金刚乡村2", "hyrulewarriors": "海拉鲁勇士", "mariopartysuperstars": "马里奥派对超级明星", "marioandsonic": "马里奥与索尼克", "banjotooie": "搬走豆子", "nintendogs": "任天堂小狗", "thezelda": "塞尔达", "palia": "伙伴", "marioandluigi": "马里奥和路易吉", "mariorpg": "马里奥角色扮演游戏", "zeldabotw": "塞尔达旷野之息", "yuumimain": "悠咪美妆", "wildrift": "狂野裂境", "riven": "裂痕", "ahri": "阿狸", "illaoi": "伊拉欧伊", "aram": "阿拉姆", "cblol": "cblol", "leagueoflegendslas": "英雄联盟拉斯", "urgot": "辛苦了", "zyra": "zyra", "redcanids": "红色犬类", "vanillalol": "香草哈哈", "wildriftph": "狂野冲突菲律宾", "lolph": "哈哈嗝", "leagueoflegend": "英雄联盟", "tốcchiến": "快节奏", "gragas": "格拉加斯", "leagueoflegendswild": "英雄联盟荒野", "adcarry": "广告带飞", "lolzinho": "哈哈小子", "leagueoflegendsespaña": "西班牙英雄联盟", "aatrox": "亚托克斯", "euw": "euw", "leagueoflegendseuw": "英雄联盟欧洲区", "kayle": "凯尔", "samira": "萨米拉", "akali": "阿卡丽", "lunari": "月亮故事", "fnatic": "fnatic", "lollcs": "哈哈笑死我了", "akshan": "阿克山", "milio": "米利欧", "shaco": "沙皇", "ligadaslegendas": "传奇连线", "gaminglol": "游戏哈哈哈", "nasus": "纳萨斯", "teemo": "提莫", "zedmain": "zed主", "hexgates": "十字门", "hextech": "海克科技", "fortnitegame": "堡垒之夜游戏", "gamingfortnite": "玩fortnite", "fortnitebr": "堡垒之夜br", "retrovideogames": "复古游戏", "scaryvideogames": "恐怖游戏", "videogamemaker": "游戏制作人", "megamanzero": "超人零号", "videogame": "电子游戏", "videosgame": "视频游戏", "professorlayton": "莱顿教授", "overwatch": "守望先锋", "ow2": "ow2", "overwatch2": "守望先锋2", "wizard101": "巫师101", "battleblocktheater": "战斗方块剧院", "arcades": "街机", "acnh": "动物森友会", "puffpals": "小泡泡朋友们", "farmingsimulator": "农场模拟器", "robloxchile": "roblox智利", "roblox": "罗布乐思", "robloxdeutschland": "罗布乐思德国", "robloxdeutsch": "roblox德语", "erlc": "erlc", "sanboxgames": "沙盒游戏", "videogamelore": "游戏百科", "rollerdrome": "滚轴枪决", "parasiteeve": "寄生女神", "gamecube": "游戏机", "starcraft2": "星际争霸2", "duskwood": "黄昏森林", "dreamscape": "梦境世界", "starcitizen": "星际公民", "yanderesimulator": "病娇模拟器", "grandtheftauto": "侠盗猎车手", "deadspace": "死寂空间", "amordoce": "甜蜜爱情", "videogiochi": "视频游戏", "theoldrepublic": "旧共和国", "videospiele": "游戏", "touhouproject": "东风项目", "dreamcast": "梦幻机", "adventuregames": "探险游戏", "wolfenstein": "狼队", "actionadventure": "冒险行动", "storyofseasons": "季节的故事", "retrogames": "复古游戏", "retroarcade": "怀旧街机", "vintagecomputing": "复古计算机", "retrogaming": "复古游戏", "vintagegaming": "复古游戏", "playdate": "玩乐约会", "commanderkeen": "指挥官基因", "bugsnax": "虫子小吃", "injustice2": "不公正2", "shadowthehedgehog": "影子刺猬", "rayman": "雷曼", "skygame": "天空游戏", "zenlife": "禅意生活", "beatmaniaiidx": "节拍狂潮iidx", "steep": "陡峭", "mystgames": "我的游戏", "blockchaingaming": "区块链游戏", "medievil": "中世纪", "consolegaming": "主机游戏", "konsolen": "游戏机", "outrun": "超越", "bloomingpanic": "盛开的恐慌", "tobyfox": "托比福克斯", "hoyoverse": "hoyoverse", "senrankagura": "闪乱神乐", "gaminghorror": "游戏恐怖", "monstergirlquest": "怪物女孩大冒险", "supergiant": "超巨星", "disneydreamlightvalle": "迪士尼梦幻之谷", "farmingsims": "农场模拟", "juegosviejos": "老游戏", "bethesda": "贝塞斯达", "jackboxgames": "jackbox游戏", "interactivefiction": "互动小说", "pso2ngs": "pso2新的开始", "grimfandango": "冥界之旅", "thelastofus2": "最后的我们2", "amantesamentes": "爱意满满", "visualnovel": "视觉小说", "visualnovels": "视觉小说", "rgg": "rgg", "shadowolf": "影狼", "tcrghost": "tcr鬼魂", "payday": "发薪日", "chatherine": "凯瑟琳", "twilightprincess": "暮光公主", "jakandaxter": "杰克与达克斯特", "sandbox": "沙盒", "aestheticgames": "美学游戏", "novelavisual": "小说一视觉", "thecrew2": "团队2", "alexkidd": "阿历克斯小子", "retrogame": "复古游戏", "tonyhawkproskater": "托尼霍克职业滑手", "smbz": "smbz", "lamento": "哀伤", "godhand": "神手", "leafblowerrevolution": "吹叶机革命", "wiiu": "任天堂wiiu", "leveldesign": "关卡设计", "starrail": "星际铁路", "keyblade": "钥匙刃", "aplaguetale": "打疫苗的故事", "fnafsometimes": "五夜后宫有时候", "novelasvisuales": "视觉小说", "robloxbrasil": "罗布乐思巴西", "pacman": "吃豆人", "gameretro": "复古游戏", "videojuejos": "游戏小子", "videogamedates": "游戏约会", "mycandylove": "我的糖果爱", "megaten": "梅贺天", "mortalkombat11": "真人快打11", "everskies": "永远的天空", "justcause3": "就因为3", "hulkgames": "浩克游戏", "batmangames": "蝙蝠侠游戏", "returnofreckoning": "洗牌归来", "gamstergaming": "gamster游戏", "dayofthetantacle": "触手日", "maniacmansion": "疯狂庄园", "crashracing": "碰撞赛车", "3dplatformers": "3d平台游戏", "nfsmw": "速度与激情中的追逐", "kimigashine": "给我死吧", "oldschoolgaming": "老派游戏", "hellblade": "地狱之刃", "storygames": "故事游戏", "bioware": "生物科技", "residentevil6": "生化危机6", "soundodger": "音速闪避者", "beyondtwosouls": "超越双魂", "gameuse": "玩游戏", "offmortisghost": "离开死者之灵", "tinybunny": "小兔兔", "retroarch": "复古拱廊", "powerup": "加油", "katanazero": "刀剑零", "famicom": "家用游戏机", "aventurasgraficas": "图形冒险", "quickflash": "快闪", "fzero": "f零", "gachagaming": "抽卡游戏", "retroarcades": "复古街机", "f123": "f123", "wasteland": "荒原", "powerwashsim": "高压水枪模拟器", "coralisland": "珊瑚岛", "syberia3": "西伯利亚3", "grymmorpg": "超级魔幻rpg", "bloxfruit": "方块水果", "anotherworld": "另一个世界", "metaquest": "元探", "animewarrios2": "动漫战士2", "footballfusion": "足球融合", "edithdlc": "艾迪特dlc", "abzu": "阿布祖", "astroneer": "宇航员", "legomarvel": "乐高奇迹", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "扭曲金属", "beamngdrive": "飞车狂飙", "twdg": "twdg", "pileofshame": "羞耻堆", "simulator": "模拟器", "symulatory": "模拟器", "speedrunner": "速跑者", "epicx": "史诗级x", "superrobottaisen": "超机器人大战", "dcuo": "dc宇宙", "samandmax": "萨姆与麦克斯", "grywideo": "搞笑视频", "gaiaonline": "盖亚在线", "korkuoyunu": "恐怖游戏", "wonderlandonline": "梦幻世界在线", "skylander": "天空探险者", "boyfrienddungeon": "男友地牢", "toontownrewritten": "卡通镇重写", "simracing": "模拟赛车", "simrace": "模拟赛车", "pvp": "对战", "urbanchaos": "城市混乱", "heavenlybodies": "天体妙影", "seum": "世间凡物", "partyvideogames": "派对视频游戏", "graveyardkeeper": "墓地守护者", "spaceflightsimulator": "太空飞行模拟器", "legacyofkain": "凯恩遗产", "hackandslash": "砍杀破解", "foodandvideogames": "食物和游戏", "oyunvideoları": "游戏视频", "thewolfamongus": "在我们之间的狼", "truckingsimulator": "卡车模拟器", "horizonworlds": "地平线世界", "handygame": "好玩游戏", "leyendasyvideojuegos": "传奇与视频游戏", "oldschoolvideogames": "老派电子游戏", "racingsimulator": "赛车模拟器", "beemov": "炫动", "agentsofmayhem": "混沌特工", "songpop": "音乐泡泡", "famitsu": "famitsu", "gatesofolympus": "奥林匹斯之门", "monsterhunternow": "怪兽猎人现在", "rebelstar": "反叛之星", "indievideogaming": "独立游戏玩家", "indiegaming": "独立游戏", "indievideogames": "独立游戏", "indievideogame": "独立游戏", "chellfreeman": "切尔弗里曼", "spidermaninsomniac": "蜘蛛侠梦游", "bufffortress": "增强堡垒", "unbeatable": "无敌", "projectl": "项目l", "futureclubgames": "未来俱乐部游戏", "mugman": "杯子哥", "insomniacgames": "失眠游戏", "supergiantgames": "超级巨人游戏", "henrystickman": "亨利棒人", "henrystickmin": "亨利stickmin", "celestegame": "天上的游戏", "aperturescience": "光圈科学", "backlog": "待办事项", "gamebacklog": "游戏待办清单", "gamingbacklog": "游戏清单", "personnagejeuxvidéos": "游戏角色", "achievementhunter": "成就猎手", "cityskylines": "城市天际线", "supermonkeyball": "超级猴球", "deponia": "德波尼亚", "naughtydog": "调皮狗", "beastlord": "野兽领主", "juegosretro": "复古游戏", "kentuckyroutezero": "肯塔基零号公路", "oriandtheblindforest": "盲森林中的奥莉", "alanwake": "艾伦觉醒", "stanleyparable": "斯坦利的寓言", "reservatoriodedopamin": "多巴胺储存器", "staxel": "staxel", "videogameost": "游戏原声", "dragonsync": "龙同步", "vivapiñata": "活力皮纳塔", "ilovekofxv": "我爱kofxv", "arcanum": "阿卡南", "neoy2k": "新千年的回归", "pcracing": "pcracing", "berserk": "失控", "baki": "巴基", "sailormoon": "水手月亮", "saintseiya": "圣斗士", "inuyasha": "犬夜叉", "yuyuhakusho": "语言学博士", "initiald": "初音速", "elhazard": "艾尔哈泽德", "dragonballz": "龙珠z", "sadanime": "伤感动漫", "darkerthanblack": "比黑还黑", "animescaling": "动漫攀登", "animewithplot": "有剧情的动漫", "pesci": "鱼水之欢", "retroanime": "复古动漫", "animes": "动漫", "supersentai": "超级战队", "samuraichamploo": "武士琵琶", "madoka": "圆神", "higurashi": "寒蝉鸣泣之时", "80sanime": "80年代动漫", "90sanime": "90s动漫", "darklord": "黑暗领主", "popeetheperformer": "波比表演者", "masterpogi": "大师帅哥", "samuraix": "武士x", "dbgt": "dbgt", "veranime": "去动画", "2000sanime": "2000年代动漫", "lupiniii": "小露皮", "drstoneseason1": "博士石第一季", "rapanime": "说唱动漫", "chargemanken": "充电男孩", "animecover": "动漫封面", "thevisionofescaflowne": "幻想水域的愿景", "slayers": "屠杀者", "tokyomajin": "东京魔神", "anime90s": "九十年代动漫", "animcharlotte": "动画夏洛特", "gantz": "勇者无畏", "shoujo": "少女", "bananafish": "香蕉鱼", "jujutsukaisen": "咒术回战", "jjk": "jjk", "haikyu": "排球少年", "toiletboundhanakokun": "厕内花子君", "bnha": "我的英雄学院", "hellsing": "赫尔辛", "skipbeatmanga": "跳跃拍漫画", "vanitas": "虚无主义", "fireforce": "火力全开", "moriartythepatriot": "莫里亚蒂的爱国者", "futurediary": "未来日记", "fairytail": "仙女尾", "dorohedoro": "泥沼之城", "vinlandsaga": "维京传奇", "madeinabyss": "深渊制造", "parasyte": "寄生虫", "punpun": "punpun", "shingekinokyojin": "进击的巨人", "mushishi": "萌萌哒", "beastars": "兽王星", "vanitasnocarte": "虚无无卡", "mermaidmelody": "美人鱼旋律", "kamisamakiss": "神明亲吻", "blmanga": "漫画牛", "horrormanga": "恐怖漫画", "romancemangas": "恋爱漫画", "karneval": "狂欢节", "dragonmaid": "龙女仆", "blacklagoon": "黑色沼泽", "kentaromiura": "三浦健太郎", "mobpsycho100": "灵魂摆渡100", "terraformars": "火星改造", "geniusinc": "天才公司", "shamanking": "傻王", "kurokonobasket": "黑子的篮球", "jugo": "果汁", "bungostraydogs": "文豪野犬", "jujustukaisen": "咕咕就够了", "jujutsu": "咒术", "yurionice": "悠瑞真棒", "acertainmagicalindex": "某个魔法索引", "sao": "骚", "blackclover": "黑色三叶草", "tokyoghoul": "东京食尸鬼", "onepunchman": "一拳超人", "hetalia": "黑塔利亚", "kagerouproject": "阳炎项目", "haikyuu": "排球少年", "toaru": "到处", "crunchyroll": "脆皮卷", "aot": "进击的巨人", "sk8theinfinity": "滑板无极限", "siriusthejaeger": "星兽猎人", "spyxfamily": "间谍家族", "rezero": "重启零点", "swordartonline": "剑侠情缘在线", "dororo": "多罗多罗", "wondereggpriority": "奋战蛋优先级", "angelsofdeath": "死神天使", "kakeguri": "狂赌之渊", "dragonballsuper": "龙珠超", "hypnosismic": "嗨普诺茨密克", "goldenkamuy": "黄金神威", "monstermusume": "怪物娘", "konosuba": "康娜苏巴", "aikatsu": "偶像活动", "sportsanime": "运动动漫", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "阿尔温的游戏", "angelbeats": "天使之击", "isekaianime": "异世界动画", "sagaoftanyatheevil": "邪恶的菲安娜故事", "shounenanime": "少年动漫", "bandori": "偶像乐队", "tanya": "田雅", "durarara": "斗争者", "prettycure": "美丽治愈", "theboyandthebeast": "小男孩与野兽", "fistofthenorthstar": "北斗之拳", "mazinger": "mazinger", "blackbuttler": "黑色女仆", "towerofgod": "神之塔", "elfenlied": "精灵之歌", "akunohana": "阿库诺哈娜", "chibi": "萌系", "servamp": "使者吸血鬼", "howtokeepamummy": "如何让妈妈开心", "fullmoonwosagashite": "满月我想找的", "shugochara": "守护角色", "tokyomewmew": "东京喵喵", "gugurekokkurisan": "咕咕乐克丽桑", "cuteandcreepy": "可爱又creepy", "martialpeak": "武道巅峰", "bakihanma": "巴基汉马", "hiscoregirl": "高分妹", "orochimaru": "大蛇丸", "mierukochan": "米勒扣川", "dabi": "大比", "johnconstantine": "约翰康斯坦丁", "astolfo": "阿斯托尔福", "revanantfae": "复仇妖精", "shinji": "信司", "zerotwo": "零二", "inosuke": "伊诺斯克", "nezuko": "禰豆子", "monstergirl": "怪物女孩", "kanae": "卡娜艾", "yone": "永恩", "mitsuki": "蜜月小酱", "kakashi": "卡卡西", "lenore": "莱诺尔", "benimaru": "本丸", "saitama": "埼玉", "sanji": "三季", "bakugo": "爆豪", "griffith": "格里菲斯", "ririn": "人人", "korra": "科拉", "vanny": "vanny", "vegeta": "贝吉塔", "goromi": "咕咚米", "luci": "露西", "reigen": "在旋转", "scaramouche": "斯卡拉穆什", "amiti": "好朋友", "sailorsaturn": "海员土星", "dio": "迪欧", "sailorpluto": "水手冥王星", "aloy": "艾露伊", "runa": "跑起来", "oldanime": "老番", "chainsawman": "电锯人", "bungoustraydogs": "文豪野犬", "jogo": "游戏", "franziska": "弗朗西斯卡", "nekomimi": "猫耳", "inumimi": "耳朵眼", "isekai": "异世界", "tokyorevengers": "东京复仇者", "blackbutler": "黑执事", "ergoproxy": "ergoproxy", "claymore": "克雷莫尔", "loli": "萝莉", "horroranime": "恐怖动漫", "fruitsbasket": "水果篮", "devilmancrybaby": "恶魔人哭泣的宝贝", "noragami": "无神论神", "mangalivre": "漫咖啡", "kuroshitsuji": "黑执事", "seinen": "青年", "lovelive": "爱就活着", "sakuracardcaptor": "樱桃卡片捕手", "umibenoetranger": "海边异乡人", "owarinoseraph": "终结的骑士", "thepromisedneverland": "许诺之地", "monstermanga": "怪物漫画", "yourlieinapril": "你的四月谎言", "buggytheclown": "小丑巴基", "bokunohero": "我的英雄", "seraphoftheend": "末日天使", "trigun": "三枪", "cyborg009": "赛博009", "magi": "魔法", "deepseaprisoner": "深海囚徒", "jojolion": "乔乔之泉", "deadmanwonderland": "死亡者乐园", "bannafish": "香蕉鱼", "sukuna": "宿傩", "darwinsgame": "达尔文游戏", "husbu": "老公", "sugurugeto": "数字独立", "leviackerman": "利威尔阿克曼", "sanzu": "三足", "sarazanmai": "佐拉赞美", "pandorahearts": "潘多拉的心", "yoimiya": "夜见才女", "foodwars": "美食大战", "cardcaptorsakura": "库洛魔法使sakura", "stolas": "斯托拉斯", "devilsline": "恶魔的界线", "toyoureternity": "给你的永恒", "infpanime": "舔屏动漫", "eleceed": "电鸣", "akamegakill": "斩赤之刃", "blueperiod": "蓝色时期", "griffithberserk": "格里菲斯狂暴", "shinigami": "死神", "secretalliance": "秘密联盟", "mirainikki": "未来日记", "mahoutsukainoyome": "麻烦你嫁给我", "yuki": "雪儿", "erased": "消失", "bluelock": "蓝锁", "goblinslayer": "杀小妖", "detectiveconan": "柯南侦探", "shiki": "时纪", "deku": "德库", "akitoshinonome": "秋津真希", "riasgremory": "莉雅斯格雷莫瑞", "shojobeat": "数码节拍", "vampireknight": "吸血鬼骑士", "mugi": "萌鬼", "blueexorcist": "蓝色驱魔师", "slamdunk": "扣篮", "zatchbell": "小魔斗士", "mashle": "魔法打工仔", "scryed": "占卜", "spyfamily": "间谍家族", "airgear": "飞行装备", "magicalgirl": "魔法少女", "thesevendeadlysins": "七宗罪", "prisonschool": "监狱学园", "thegodofhighschool": "高中之神", "kissxsis": "吻姐妹", "grandblue": "大蓝", "mydressupdarling": "我的衣服搭配萌妹", "dgrayman": "黑执事", "rozenmaiden": "萝卜少女", "animeuniverse": "动漫宇宙", "swordartonlineabridge": "剑艺在线桥接", "saoabridged": "saoabridged", "hoshizora": "星空", "dragonballgt": "龙珠gt", "bocchitherock": "波奇摇滚", "kakegurui": "狂赌之吻", "mobpyscho100": "狂热心理100", "hajimenoippo": "开始的第一步", "undeadunluck": "亡者不幸", "romancemanga": "恋爱漫画", "blmanhwa": "黑潮漫画", "kimetsunoyaba": "鬼灭之刃", "kohai": "后辈", "animeromance": "动漫恋爱", "senpai": "前辈", "blmanhwas": "霸总漫画", "animeargentina": "阿根廷动漫", "lolicon": "洛丽塔控", "demonslayertothesword": "屠魔刀锋", "bloodlad": "血尸兄弟", "goodbyeeri": "再见eri", "firepunch": "火焰拳", "adioseri": "再见的你", "tatsukifujimoto": "藤本树", "kinnikuman": "筋肉人", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "少女爱", "starsalign": "星星对齐", "romanceanime": "浪漫动漫", "tsundere": "傲娇", "yandere": "病娇", "mahoushoujomadoka": "魔法少女小圆", "kenganashura": "拳愿阿修罗", "saointegralfactor": "综合因子回事", "cherrymagic": "樱桃魔法", "housekinokuni": "家里的奇幻世界", "recordragnarok": "录音大乱斗", "oyasumipunpun": "晚安呜呜", "meliodas": "梅利奥达斯", "fudanshi": "氛单时", "retromanga": "复古漫画", "highschoolofthedead": "亡灵高中", "germantechno": "德国科技诺", "oshinoko": "推しの子", "ansatsukyoushitsu": "刺客教室", "vindlandsaga": "风土传奇", "mangaka": "漫画家", "dbsuper": "超级db", "princeoftennis": "网球王子", "tonikawa": "托尼卡瓦", "esdeath": "艾斯德斯", "dokurachan": "独库拉酱", "bjalex": "boo亚历克斯", "assassinclassroom": "刺杀课堂", "animemanga": "动漫漫画", "bakuman": "画神踪", "deathparade": "死亡狂欢", "shokugekinosouma": "食戟之灵", "japaneseanime": "日本动漫", "animespace": "动漫空间", "girlsundpanzer": "女孩与坦克", "akb0048": "akb0048", "hopeanuoli": "希望那种感觉", "animedub": "动画配音", "animanga": "动画漫画", "tsurune": "弓道圈", "uqholder": "uqholder", "indieanime": "独立动漫", "bungoustray": "文豪野犬", "dagashikashi": "駄菓子放題", "gundam0": "gundam0", "animescifi": "动漫科幻", "ratman": "鼠人", "haremanime": "兔耳动漫", "kochikame": "哆啦a梦", "nekoboy": "猫男", "gashbell": "gashbell", "peachgirl": "桃子女孩", "cavalieridellozodiaco": "圣斗士星矢", "mechamusume": "机械少女", "nijigasaki": "虹咲", "yarichinbitchclub": "野莉青春姐妹会", "dragonquestdai": "龙之猎手大冒险", "heartofmanga": "漫画之心", "deliciousindungeon": "美味地牢", "manhviyaoi": "漫黑文雅oi", "recordofragnarok": "诸神黄昏日志", "funamusea": "有趣的博物馆", "hiranotokagiura": "平行空间", "mangaanime": "漫画动漫", "bochitherock": "博气特摇", "kamisamahajimemashita": "神様开始了", "skiptoloafer": "跳过懒人", "shuumatsunovalkyrie": "放学后的女武神", "tutorialistoohard": "教程太难了", "overgeared": "超装备", "toriko": "素材人", "ravemaster": "狂欢大师", "kkondae": "老干部", "chobits": "小千chobits", "witchhatatelier": "女巫帽工作室", "lansizhui": "烂丝坠", "sangatsunolion": "喧闹狮子", "kamen": "面具", "mangaislife": "漫画就是生活", "dropsofgod": "神的水滴", "loscaballerosdelzodia": "十二星座骑士", "animeshojo": "动漫少女", "reverseharem": "反向后宫", "saintsaeya": "圣斗士", "greatteacheronizuka": "伟大的鬼冢老师", "gridman": "格斗战士", "kokorone": "心连心", "soldato": "士兵", "mybossdaddy": "我老板老爹", "gear5": "装备5", "grandbluedreaming": "大蓝梦", "bloodplus": "血加加", "bloodplusanime": "血液加动画", "bloodcanime": "血腥动漫", "bloodc": "血液c", "talesofdemonsandgods": "妖神记", "goreanime": "血腥动漫", "animegirls": "动漫女孩", "sharingan": "写轮眼", "crowsxworst": "乌鸦x最糟", "splatteranime": "喷涂动漫", "splatter": "溅洒", "risingoftheshieldhero": "盾之勇者升起", "somalianime": "索马里动漫", "riodejaneiroanime": "里约热内卢动漫", "slimedattaken": "被涂抹了", "animeyuri": "动漫百合", "animeespaña": "动漫西班牙", "animeciudadreal": "动漫城市现实", "murim": "武林", "netjuunosusume": "网聚推荐", "childrenofthewhales": "海豚宝宝们", "liarliar": "说谎者说谎者", "supercampeones": "超级冠军", "animeidols": "动漫偶像", "isekaiwasmartphone": "穿越带手机的生活", "midorinohibi": "绿色日常", "magicalgirls": "魔法少女", "callofthenight": "夜晚的召唤", "bakuganbrawler": "巴卡冈斗士", "bakuganbrawlers": "巴卡玩斗士", "natsuki": "夏树", "mahoushoujo": "魔法少女", "shadowgarden": "阴影花园", "tsubasachronicle": "翼咲纪元", "findermanga": "找漫画", "princessjellyfish": "水母公主", "kuragehime": "水母姬", "paradisekiss": "天堂之吻", "kurochan": "黑猫chan", "revuestarlight": "重聚星光", "animeverse": "动画宇宙", "persocoms": "个人电脑", "omniscientreadersview": "全知读者视角", "animecat": "动漫猫", "animerecommendations": "动漫推荐", "openinganime": "开场动漫", "shinichirowatanabe": "渡边信一郎", "uzumaki": "漩涡", "myteenromanticcomedy": "我的青春爱情喜剧", "evangelion": "新世纪福音战士", "gundam": "高达", "macross": "超时空要塞", "gundams": "机动战士", "voltesv": "勇者v", "giantrobots": "巨型机器人", "neongenesisevangelion": "霓虹基因使徒", "codegeass": "命运的编程", "mobilefighterggundam": "移动战士高达", "neonevangelion": "霓虹福音", "mobilesuitgundam": "机动战士高达", "mech": "机甲", "eurekaseven": "心跳七重奏", "eureka7": "悦乐7", "thebigoanime": "大象动漫", "bleach": "漂白", "deathnote": "死亡笔记", "cowboybebop": "牛仔邦", "jjba": "jjba", "jojosbizarreadventure": "乔乔的奇妙冒险", "fullmetalalchemist": "全金属铳师", "ghiaccio": "冰冰冰", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "卡姆伊亚图", "militaryanime": "军团动漫", "greenranger": "绿战士", "jimmykudo": "基米库多", "tokyorev": "东京复仇", "zorro": "佐罗", "leonscottkennedy": "里昂斯科特肯尼迪", "korosensei": "杀老师", "starfox": "星狐", "ultraman": "奥特曼", "salondelmanga": "漫画馆", "lupinthe3rd": "鲁邦三世", "animecity": "动漫城", "animetamil": "动漫泰米尔", "jojoanime": "乔乔动漫", "naruto": "火影忍者", "narutoshippuden": "火影忍者疾风传", "onepiece": "海贼王", "animeonepiece": "动漫海贼王", "dbz": "龙珠", "dragonball": "龙珠", "yugioh": "游戏王", "digimon": "数码宝贝", "digimonadventure": "数码宝贝冒险", "hxh": "hxh", "highschooldxd": "高中dxd", "goku": "悟空", "broly": "兄弟", "shonenanime": "少年动漫", "bokunoheroacademia": "我的英雄学院", "jujustukaitsen": "就要吃开心", "drstone": "石之炼金术", "kimetsunoyaiba": "鬼灭之刃", "shonenjump": "少年跳跃", "otaka": "otaka", "hunterxhunter": "猎人x猎人", "mha": "mha", "demonslayer": "鬼灭之刃", "hinokamikagurademonsl": "火神神乐恶魔杀", "attackontitan": "进击的巨人", "erenyeager": "虎蝇煞弟", "myheroacademia": "我的英雄学院", "boruto": "博人", "rwby": "红白黄黑", "dandadan": "dan大丹", "tomodachigame": "朋友游戏", "akatsuki": "晓组织", "surveycorps": "调查军团", "onepieceanime": "海贼王动漫", "attaquedestitans": "攻击巨人", "theonepieceisreal": "海贼王是真的", "revengers": "复仇者", "mobpsycho": "精神病殿堂", "aonoexorcist": "青之驱魔师", "joyboyeffect": "快乐男孩效应", "digimonstory": "数码宝贝故事", "digimontamers": "数码宝贝勇者", "superjail": "超级监狱", "metalocalypse": "金属末日", "shinchan": "小新", "watamote": "我妹没那么可爱", "uramichioniisan": "辣味哥哥", "uruseiyatsura": "鬼灭之刃", "gintama": "银魂", "ranma": "燃马", "doraemon": "哆啦a梦", "gto": "知道你疼", "ouranhostclub": "我们的青少年社团", "flawlesswebtoon": "完美漫画", "kemonofriends": "动物好友", "utanoprincesama": "乌塔诺公主演小姐", "animecom": "动漫社区", "bobobobobobobo": "波波波波波波", "yuukiyuuna": "宇纪优娜", "nichijou": "日常", "yurucamp": "悠然露营", "nonnonbiyori": "非非日和", "flyingwitch": "飞天女巫", "wotakoi": "wotakoi", "konanime": "炸点动漫", "clannad": "小团圆", "justbecause": "就因为这样", "horimiya": "堀宫", "allsaintsstreet": "万圣街", "recuentosdelavida": "生活的回忆"}