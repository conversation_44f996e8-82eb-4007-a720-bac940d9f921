{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramostestas", "astrology": "astrologija", "cognitivefunctions": "pažinimofunkcijos", "psychology": "psichologija", "philosophy": "filosofija", "history": "istorija", "physics": "fizika", "science": "mokslas", "culture": "<PERSON><PERSON><PERSON><PERSON>", "languages": "kalbos", "technology": "technologija", "memes": "memai", "mbtimemes": "mbt<PERSON><PERSON><PERSON>", "astrologymemes": "astrologijosmemai", "enneagrammemes": "enneagram<PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "mintys<PERSON>š<PERSON>", "funny": "juokingas", "videos": "vaizdoįrašai", "gadgets": "prietaisai", "politics": "politika", "relationshipadvice": "patarimaisantykiams", "lifeadvice": "gyvenimiškipatarimai", "crypto": "kriptovaliuta", "news": "na<PERSON><PERSON><PERSON><PERSON>", "worldnews": "pasa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "archeologija", "learning": "mokymasis", "debates": "debatai", "conspiracytheories": "<PERSON>ą<PERSON>ksloteor<PERSON>jos", "universe": "visata", "meditation": "meditacija", "mythology": "mitologija", "art": "menas", "crafts": "amatai", "dance": "šokiai", "design": "<PERSON><PERSON><PERSON>", "makeup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "<PERSON><PERSON><PERSON><PERSON>", "fashion": "mada", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "photography": "fotografija", "cosplay": "cosplay", "painting": "tapyba", "drawing": "pieš<PERSON><PERSON>", "books": "kny<PERSON>", "movies": "filmai", "poetry": "poezija", "television": "televizija", "filmmaking": "filmųk<PERSON><PERSON><PERSON>", "animation": "animacija", "anime": "anime", "scifi": "mokslinėfantastika", "fantasy": "fantastika", "documentaries": "dokumentiniaifilmai", "mystery": "detektyvai", "comedy": "komedija", "crime": "kriminalai", "drama": "drama", "bollywood": "bolivudas", "kdrama": "korėjietiškiserialai", "horror": "<PERSON><PERSON><PERSON>", "romance": "romantika", "realitytv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON><PERSON>", "music": "muzika", "blues": "bli<PERSON>s", "classical": "klasika", "country": "country", "desi": "indų", "edm": "elektroninėšokiųmuzika", "electronic": "elektroninėmuzika", "folk": "liaudiesmuzika", "funk": "funk", "hiphop": "hiphopas", "house": "house", "indie": "indie", "jazz": "<PERSON><PERSON><PERSON><PERSON>", "kpop": "korėjiečiųpopmuzika", "latin": "lotynų", "metal": "metalas", "pop": "popmuzika", "punk": "punk", "rnb": "rnb", "rap": "repas", "reggae": "reggae", "rock": "rokas", "techno": "techno", "travel": "k<PERSON><PERSON><PERSON><PERSON>", "concerts": "koncertai", "festivals": "šventinėmuzika", "museums": "muziejai", "standup": "standup", "theater": "teatras", "outdoors": "atvirasora<PERSON>", "gardening": "sodininkystė", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "šachmatai", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemonai", "food": "ma<PERSON>s", "baking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetarian": "vegetaras", "vegan": "veganas", "birds": "paukščiai", "cats": "<PERSON><PERSON><PERSON><PERSON>", "dogs": "<PERSON><PERSON><PERSON>", "fish": "<PERSON><PERSON><PERSON><PERSON>", "animals": "gyvūnai", "blacklivesmatter": "blacklivesmatter", "environmentalism": "aplinkosapsauga", "feminism": "feminiz<PERSON>", "humanrights": "žmoniųteisės", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "sportas", "badminton": "badmintonas", "baseball": "beisbolas", "basketball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxing": "boksas", "cricket": "kriketas", "cycling": "dviračiųsportas", "fitness": "<PERSON><PERSON><PERSON>", "football": "futbolas", "golf": "golfas", "gym": "sportosalė", "gymnastics": "gimnast<PERSON>", "hockey": "ledori<PERSON><PERSON>s", "martialarts": "kovosmenai", "netball": "<PERSON><PERSON><PERSON><PERSON>", "pilates": "pilatesas", "pingpong": "stalote<PERSON><PERSON>", "running": "b<PERSON><PERSON><PERSON>", "skateboarding": "riedlenčiųsportas", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snieglenčiųsportas", "surfing": "banglenčiųsportas", "swimming": "plau<PERSON><PERSON>", "tennis": "tenisas", "volleyball": "<PERSON><PERSON><PERSON><PERSON>", "weightlifting": "svoriųkilnojimas", "yoga": "joga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "kopimasįkalnus", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "<PERSON><PERSON><PERSON>", "pisces": "<PERSON><PERSON><PERSON><PERSON>", "aries": "a<PERSON>s", "taurus": "j<PERSON><PERSON>", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "vėžys", "leo": "li<PERSON><PERSON>", "virgo": "mergelė", "libra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scorpio": "skorpionas", "sagittarius": "<PERSON><PERSON><PERSON>", "shortterm": "trump<PERSON><PERSON>s", "casual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "ilgalaikiais<PERSON>", "single": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polyamory": "poliamoriškisantykiai", "enm": "neįprastiryšiai", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "<PERSON><PERSON><PERSON><PERSON>", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "biseks<PERSON>us", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "raudonasismirtis2", "dragonage": "<PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "žudikųįškar", "saintsrow": "šventosiospritai", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "sa<PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "raudon<PERSON>žuvia<PERSON>", "kingsquest": "karaliauskelionė", "soulreaver": "sielųgrobėjas", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "spyrolegenda", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "saul<PERSON><PERSON><PERSON>beapdai<PERSON>ė<PERSON>", "arkham": "arkham", "deusex": "dievasirseksa<PERSON>", "fireemblemfates": "ugniesžymėlikimai", "yokaiwatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocksteady": "r<PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "atidarytaspasaugas", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "sielų<PERSON><PERSON>š<PERSON>", "dungeoncrawling": "požeminiųnuotykių", "jetsetradio": "jetsetradio", "tribesofmidgard": "vidurgiriai", "planescape": "planųkelias", "lordsoftherealm2": "lordai_klano2", "baldursgate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorvore": "spal<PERSON><PERSON><PERSON><PERSON>", "medabots": "medabotai", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersyvūsims", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "apvilkti", "eldenring": "elden<PERSON>", "darksouls": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "vyresniapalankos", "modding": "moddinimas", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "įtraukiantis", "falloutnewvegas": "pask<PERSON><PERSON>šansasve<PERSON>ą", "bioshock": "<PERSON>š<PERSON><PERSON>", "omori": "omori", "finalfantasyoldschool": "senasfinalfantasy", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "galutinifantazija", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "<PERSON><PERSON><PERSON>fant<PERSON><PERSON><PERSON>", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotyvacija", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "meilėbeprotiškai", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "žvaigždynas", "stardewvalley": "stardewvalley", "ocarinaoftime": "<PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampyrųmasquerade", "dimension20": "dimensija20", "gaslands": "<PERSON>j<PERSON>ž<PERSON><PERSON><PERSON>", "pathfinder": "kelioni<PERSON><PERSON>", "pathfinder2ndedition": "keliautojas2leidimas", "shadowrun": "šeš<PERSON><PERSON>ėgimas", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "galutinifantazija11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "tavošeimėjimirtingi", "persona3": "persona3", "rpghorror": "rpghorroresas", "elderscrollsonline": "senjo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reka": "reka", "honkai": "honkai", "marauders": "plėšikai", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekstas", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "žvaigždėtvarkakotor", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "prieglaudafallout<PERSON>", "gurps": "gū<PERSON>s", "darkestdungeon": "tams<PERSON>usiak<PERSON><PERSON>is", "eclipsephase": "eclipsefazė", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "krpg", "bindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastijoskariai", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madnesscombat", "jaggedalliance2": "smailarosvajonės2", "neverwinter": "amžinasvasarį", "road96": "kelias96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikai", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "drakonožygiai", "arenaofvalor": "valo<PERSON><PERSON>na", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "karnavalųmiestas", "childoflight": "šviesosvaikas", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "linija2", "digimonworld": "digimon<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ekopankas", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizontasuždraustasva<PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltalapė", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "žvaigždžiųpaieška", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "<PERSON><PERSON><PERSON>pan<PERSON>", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunk<PERSON><PERSON><PERSON>", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "nukritusisįsakymas", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "blogosžemės", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "velniobaigė", "oldschoolrunescape": "senamokyklisrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "fermeriapg", "oldworldblues": "senamiesčioliūdesys", "adventurequest": "nuotykiųieškotojas", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "vaidmenųžaidimai", "roleplayinggames": "vaidmenųžaidimai", "finalfantasy9": "finalfantasy9", "sunhaven": "saulėsprieglauda", "talesofsymphonia": "simfonijosist<PERSON><PERSON>", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "galutinisfantazija13", "daggerfall": "dagerfall", "torncity": "torncity", "myfarog": "manoferog", "sacredunderworld": "sacredunderworld", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "tamsosielos", "soulslikes": "sielospanaš<PERSON>i", "othercide": "kitoksisvidas", "mountandblade": "kaln<PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "amžinybėspilarai", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "drakonųlegenda", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octopathkelionininkas", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "vilkolakiųap<PERSON><PERSON>ė", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "mortavaikų", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fablestlostchapters", "hiveswap": "hiveswap", "rollenspiel": "roleplay", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "amžinasedenas", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborgas", "savageworlds": "savage<PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "karalystėsširdis1", "ff9": "ff9", "kingdomheart2": "karalystėsširdis2", "darknessdungeon": "tamsoskalėjimas", "juegosrpg": "rpgžaidimai", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "karalystėsširdys3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkaviečiai", "harvestella": "<PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastionas", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "dangausarcadija", "shadowhearts": "šeš<PERSON><PERSON>ųš<PERSON><PERSON>s", "nierreplicant": "nierreplicantas", "gnosia": "gnosia", "pennyblood": "centųkraujas", "breathoffire4": "ugnioatkvepimas4", "mother3": "mama3", "cyberpunk2020": "kibernetinispunk2020", "falloutbos": "falloutbos", "anothereden": "kit<PERSON>den", "roleplaygames": "vaidmenųžaidimai", "roleplaygame": "vaidmenųžaidimas", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "keliautojasrpg", "pathfinder2e": "keliautojas2e", "vampirilamasquerade": "vampyraimaskaradas", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chron<PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "medžiok<PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstr<PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforumas", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "karalystėateina", "awplanet": "awplanet", "theworldendswithyou": "pasaulisbaigiasiškai<PERSON>", "dragalialost": "dragalialost", "elderscroll": "senjo<PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "dydinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "tamsioji_herezija", "shoptitans": "pirkliųtitanai", "forumrpg": "forumasrpg", "golarion": "golarion", "earthmagic": "žemėsmagija", "blackbook": "juodaknygė", "skychildrenoflight": "dangausvaikaišviesoje", "gryrpg": "gryrpg", "sacredgoldedition": "šventasisauksasleidimas", "castlecrashers": "piliųsprogimas", "gothicgame": "gothikini<PERSON>ž<PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "žaidimyrpg", "prophunt": "prophunt", "starrails": "žvaigždžiųgeležinkeliai", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON>", "indivisible": "nedalomas", "freeside": "laisvasisபதிவு", "epic7": "epic7", "ff7evercrisis": "ff7amžinakrizo", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "poan<PERSON><PERSON><PERSON><PERSON>", "deathroadtocanada": "mirtieskeliasįkanadą", "palladium": "palladium", "knightjdr": "ritininkasjdr", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacija", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monstr<PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "taktinisrpg", "mahoyo": "mahojo", "animegames": "animežaidimai", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "valgo<PERSON><PERSON>", "diluc": "diluc", "venti": "<PERSON><PERSON><PERSON><PERSON>", "eternalsonata": "amžinassonata", "princessconnect": "princesėsusijungia", "hexenzirkel": "hexenzirkel", "cristales": "krist<PERSON>i", "vcs": "vcs", "pes": "b<PERSON><PERSON>", "pocketsage": "kišeninisprotas", "valorant": "valorantas", "valorante": "valorante", "valorantindian": "valorantinindas", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esportas", "mlg": "mlg", "leagueofdreamers": "svajotojuliiga", "fifa14": "fifa14", "midlaner": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "efootball", "dreamhack": "svajoniųhak<PERSON>", "gaimin": "gaimin", "overwatchleague": "overwatchlyga", "cybersport": "kibernetinissportas", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brazili<PERSON><PERSON><PERSON><PERSON>", "valorantcompetitive": "valorantkonkurencija", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "likęs<PERSON><PERSON><PERSON><PERSON>", "left4dead2": "kairė04mirtis2", "valve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "amžinasvasara", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "laisvėsplaneta", "transformice": "transformice", "justshapesandbeats": "tiesiogformosirritmai", "battlefield4": "mūšiolaukas4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "pusiaulife2", "hacknslash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "lietaúsušauliai2", "metroidvanias": "metroidvanijos", "overcooked": "pervirta", "interplanetary": "tarp<PERSON><PERSON>is", "helltaker": "p<PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "mirdančios<PERSON>ąstelių", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarfofortas", "foxhole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "ben<PERSON>s", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "mūšiolaukas1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "akių", "blackdesert": "juodasisdyku<PERSON>", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "šėltiškai", "hardspaceshipbreaker": "sunkiausdangauslaisvintojas", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "užstrigęspasijesterį", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON>", "rainworld": "lietuvossvetaingas", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "kovospradžia", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "juodap<PERSON><PERSON>", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "susitikinėjimasims", "yaga": "jaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "miegojaucities", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsija", "virtualkenopsia": "virtualkenopsija", "snowrunner": "sniegobėgikas", "libraryofruina": "bibliotekoruina", "l4d2": "l4d2", "thenonarygames": "tienonariniaižaidimai", "omegastrikers": "omegastriker<PERSON>i", "wayfinder": "kelioni<PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "ramioplastikinėan<PERSON>ė", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermėsosvaikas", "tinnybunny": "tiniukasbu<PERSON>", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "mirtis", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "vaivorykštė6", "apexlegends": "apexlegends", "cod": "kodas", "borderlands": "sienųkraštai", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6fiksas", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "far<PERSON><PERSON><PERSON>aid<PERSON><PERSON>", "paladins": "paladinai", "earthdefenseforce": "žemėsapsaugosvojska", "huntshowdown": "medžiok<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "karai", "sierra117": "sierra117", "dayzstandalone": "dienosatsiskleidimas", "ultrakill": "ultrakill", "joinsquad": "prisijunkpriekomandos", "echovr": "echovr", "discoelysium": "diskoeilysium", "insurgencysandstorm": "sušiurpinantissmėlioaudra", "farcry3": "farcry3", "hotlinemiami": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxpayne": "maxpayne", "hitman3": "žudikas3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "codkovoszona", "callofdutywarzone": "callofdutywarzone", "codzombies": "kodazombiai", "mirrorsedge": "veidrodžiųkraštas", "divisions2": "divisions2", "killzone": "žudynė", "helghan": "hel<PERSON>", "coldwarzombies": "šalčiozombiai", "metro2033": "metro2033", "metalgear": "metaloįrankiai", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "kroskodai", "goldeneye007": "auksinisakis007", "blackops2": "juodopai2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernuskaras", "neonabyss": "neonbedugnė", "planetside2": "planetamsklandžiai2", "mechwarrior": "mechwarrior", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "pabėknotarkovo", "metalslug": "metalslug", "primalcarnage": "primal<PERSON>ž<PERSON>", "worldofwarships": "laivųkarai", "back4blood": "atgal<PERSON><PERSON><PERSON>jo", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "susidoro<PERSON>s", "masseffect": "masseffect", "systemshock": "si<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "urvopasakojimas", "doometernal": "doometernal", "centuryageofashes": "amžiausšimtmečiošipų", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "divizija2", "tythetasmaniantiger": "tytetasmanijostigras", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "įženkįginklapį", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernuskaras2", "blackops1": "juodoonoriniai1", "sausageman": "dešrainisঅসমীয়া", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON>", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "juodapapagalius3", "vampiresurvivors": "vampyrųišgyvenę", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "mūšiolaukai", "frag": "frag", "tinytina": "tinatina", "gamepubg": "žaidim<PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearlaisvėsvaikai", "juegosfps": "fpsžaidimai", "convertstrike": "konvertuoksmūgis", "warzone2": "karozona2", "shatterline": "sprogstantiest<PERSON>s", "blackopszombies": "juodajieskaraizomb<PERSON>i", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "respublikoskomanda", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "<PERSON><PERSON><PERSON>", "squad": "komanda", "destiny1": "likimas1", "gamingfps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubggirl", "worldoftanksblitz": "tankųpasaulisblitz", "callofdutyblackops": "callofdu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enlisted": "prijun<PERSON><PERSON>", "farlight": "<PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "šarvuotaskernas", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "mažytinimitai", "halo2": "halo2", "payday2": "atlyginimodiena2", "cs16": "cs16", "pubgindonesia": "pubgindonezija", "pubgukraine": "pubgukrainoje", "pubgeu": "pubgeu", "pubgczsk": "pubglt", "wotblitz": "wotblitz", "pubgromania": "pubgromanija", "empyrion": "empyrion", "pubgczech": "pubglietuva", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "vaiduokliod", "csplay": "csplay", "unrealtournament": "neįtikimoturnyras", "callofdutydmz": "callofdutydmz", "gamingcodm": "žaidžiamcodm", "borderlands2": "sienųkraštai2", "counterstrike": "kontrp<PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "drebėjimųčempionai", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "skaldytaslastas", "neonwhite": "neongarso", "remnant": "likučiai", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "grąžinkit", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "šešėliažmogus", "quake2": "sukrėtimas2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "mūšiolaukas3", "lostark": "prar<PERSON><PERSON><PERSON>", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "vagi<PERSON><PERSON><PERSON><PERSON>", "rust": "r<PERSON><PERSON>s", "conqueronline": "užkariaukinternete", "dauntless": "neapsikalbėk", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "drakonųdiena", "warthunder": "warthunder", "flightrising": "s<PERSON><PERSON><PERSON><PERSON><PERSON>augin<PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "runkalnųlegendos", "pso2": "pso2", "myster": "misteris", "phantasystaronline2": "fantasyžvaigždėinternete2", "maidenless": "bejausmenė", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "<PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "antrojiįgyvenimas", "aion": "aion", "toweroffantasy": "tvirtovėsfantazija", "netplay": "netžaidimas", "everquest": "visadaieškok", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "raud<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superanimalroyale": "supergyvūnųkarai", "ragnarokonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightonline": "rite<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "drakonųamžiusinkvizicija", "codevein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klub<PERSON><PERSON><PERSON><PERSON><PERSON>", "lotro": "lotro", "wakfu": "wakfu", "scum": "šlamštas", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "juodasisdykumoonline", "multiplayer": "multiplayer", "pirate101": "piratas101", "honorofkings": "karaliųgarbė", "fivem": "fivem", "starwarsbattlefront": "žvaigždžiųkarųmūšiofrontas", "karmaland": "<PERSON><PERSON><PERSON><PERSON>", "ssbu": "ssbu", "starwarsbattlefront2": "žvaigždžiųkaraimūšiopriefrontas2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasika", "worldofwarcraft": "<PERSON><PERSON><PERSON><PERSON>", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "šilkkelias", "spiralknights": "spiralknightai", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "p<PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "angelaiinternete", "lunia": "lunija", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversolasinternetuose", "growtopia": "growtopia", "starwarsoldrepublic": "žvaigždžiųkaraisenųjųrespublika", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "kylantiestebuklas", "corepunk": "brandžiosš<PERSON><PERSON><PERSON>", "adventurequestworlds": "nuotykiųpasauliai", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "gyvūn<PERSON>ž<PERSON><PERSON><PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "gatvėskovotojas", "hollowknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsolid", "forhonor": "užgarbę", "tekken": "tekken", "guiltygear": "kaltiįrangos", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "gatvėskovotojas6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "gatvėsofrage", "mkdeadlyalliance": "mk<PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kovoskankiniai", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrokovinisspeletainiai", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmulkinimas", "mugen": "mugen", "warofthemonsters": "monst<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "kibersrobotai", "armoredwarriors": "armenotiwu<PERSON><PERSON>s", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "galingiįrankiai", "beatemup": "mūškikį", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "kovosžaidimai", "killerinstinct": "žudikostinstinktas", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "vaiduokliųbėgikas", "chivalry2": "riteriška2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "kaltiįrangosvaržybos", "hollowknightsequel": "tuščiaviduriaiškaiatsitiktinis", "hollowknightsilksong": "tusčiosiosriteriskel<PERSON>ė<PERSON>", "silksonghornet": "šilkstraublys", "silksonggame": "silksongžaidimas", "silksongnews": "silksongnaujienos", "silksong": "<PERSON><PERSON><PERSON><PERSON>", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "evoliucijosturn<PERSON>s", "evomoment": "evomoment", "lollipopchainsaw": "lazdynųsuktinukai", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizon": "horizontas", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "krašbandikutas", "bloodbourne": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzeroaušra", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "negerai", "playstationbuddies": "playstationdraugai", "ps1": "ps1", "oddworld": "keistaplanetą", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabiškai", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "velnialaisvaja", "gta4": "gta4", "gta": "gta", "roguecompany": "piktaskompany", "aisomniumfiles": "aisomnium<PERSON>ilai", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "grįžtam", "trove": "lobynas", "detroitbecomehuman": "detroittapkž<PERSON>gumi", "beatsaber": "beats<PERSON>r", "rimworld": "rimworldas", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turist<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "krišteamracing", "fivepd": "penkipdc", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "velniasneskaudžia3", "devilmaycry5": "šėtonasneapsiverčia5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "samura<PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "sieloskerštas", "gta5rp": "gta5rp", "gtav": "gtaž", "playstation3": "playstation3", "manhunt": "medžioklė", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2sąjunga", "pcsx2": "pcsx2", "lastguardian": "paskutinisglobėjas", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "<PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "šokiųgyvūnas", "warharmmer40k": "warharmmer40k", "fightnightchampion": "kovonaktisdviejų", "psychonauts": "psichonautai", "mhw": "mhw", "princeofpersia": "persijosprin<PERSON>", "theelderscrollsskyrim": "vyresniuosiosritėsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "vyresniusklaidai", "gxbox": "gxbox", "battlefront": "mūš<PERSON>front<PERSON>", "dontstarvetogether": "nesnalkkartu", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "žvaigždž<PERSON>iu", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "namųpervengėjas", "americanmcgeesalice": "amerikie<PERSON>_mcgee_alice", "xboxs": "xboxai", "xboxseriesx": "xboxserijax", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "karalysčiųlyga", "fable2": "pasaka2", "xboxgamepass": "xboxgamepass", "undertale": "undertal<PERSON>", "trashtv": "šlamštv", "skycotl": "dangauskatlas", "erica": "erica", "ancestory": "paveldas", "cuphead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlemisfortune": "mažojavirtinėnesėkmė", "sallyface": "sallyveidas", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projektazombiai", "ddlc": "ddlc", "motos": "motos", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "avinuo<PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stan<PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "<PERSON><PERSON><PERSON>", "longdrive": "<PERSON><PERSON><PERSON>", "satisfactory": "paten<PERSON><PERSON><PERSON>", "pluviophile": "pluviophile", "underearth": "požemyje", "assettocorsa": "assettocorsa", "geometrydash": "geometri<PERSON><PERSON><PERSON><PERSON>", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "tamsuskupola<PERSON>", "pizzatower": "pizzastokas", "indiegame": "indiegames", "itchio": "itchio", "golfit": "golfit", "truthordare": "tiesapratimasarizika", "game": "žaid<PERSON><PERSON>", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolinas", "hulahoop": "<PERSON><PERSON><PERSON>", "dare": "išdrįsk", "scavengerhunt": "lobiųmedžioklė", "yardgames": "kiemožaidimai", "pickanumber": "pasirinknumerį", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "<PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "<PERSON><PERSON><PERSON>ųb<PERSON>bė", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON><PERSON>", "mahjong": "ma<PERSON>as", "jeux": "<PERSON><PERSON><PERSON><PERSON>", "simulationgames": "simuliacij<PERSON>i", "wordgames": "žodžiųžaidimai", "jeuxdemots": "žodžiųžaidimai", "juegosdepalabras": "žodžiųžaidimai", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "nuobodžiųžaidimai", "oyun": "žaid<PERSON><PERSON>", "interactivegames": "interaktyvūsžaidimai", "amtgard": "amtgard", "staringcontests": "žvilgsniųvaržytuvės", "spiele": "<PERSON><PERSON><PERSON><PERSON>", "giochi": "<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphonežaidimai", "boogames": "boogames", "cranegame": "kran<PERSON>žaid<PERSON><PERSON>", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "šokinėjimas", "arcadegames": "arkadiniaižaidimai", "yakuzagames": "ya<PERSON><PERSON>žaidimai", "classicgame": "klasikinisžaidimas", "mindgames": "pro<PERSON>žaid<PERSON><PERSON>", "guessthelyric": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON>viožaidimai", "4xgames": "4xžaidimai", "gamefi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "arcadežaidimai", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvynija", "games90": "žaidimai90", "idareyou": "išdrįstušnešti", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "lenktyniųžaidimai", "ets2": "ets2", "realvsfake": "tikraspriešnetikras", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "žaidiinternete", "onlinegames": "internetiniaižaidimai", "jogosonline": "internetinėjoga", "writtenroleplay": "rašytinispasirašymas", "playaballgame": "žaistikamuolį", "pictionary": "pictionary", "coopgames": "kooperatyvin<PERSON>", "jenga": "jenga", "wiigames": "wiigames", "highscore": "aukštasrezultatas", "jeuxderôles": "vaidmenųžaidimai", "burgergames": "burgeriųžaidimai", "kidsgames": "vaikųžaidimai", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwjuodaskaita", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "k<PERSON><PERSON><PERSON><PERSON>", "gioco": "žaid<PERSON><PERSON>", "managementgame": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "paslėptųobjektųžaidi", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formul1žaidimas", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "arcadežaidimai", "memorygames": "atmintiesžaidimai", "vulkan": "vulkanas", "actiongames": "veik<PERSON>žaid<PERSON>i", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "soffokoperacija", "perguntados": "k<PERSON><PERSON><PERSON>", "gameo": "žaid<PERSON><PERSON>", "lasergame": "laserdra<PERSON><PERSON>", "imessagegames": "imess<PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "neteisėsž<PERSON>i", "fillintheblank": "užpildyktuščiąvietą", "jeuxpc": "ž<PERSON><PERSON><PERSON>", "rétrogaming": "retrožaidimai", "logicgames": "logikosžaidimai", "japangame": "japanga<PERSON>", "rizzupgame": "rizzu<PERSON>žaid<PERSON><PERSON>", "subwaysurf": "subwaybangos", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "išėjimožaidimai", "5vs5": "5prieš5", "rolgame": "rolkaispėdai", "dashiegames": "dashiegames", "gameandkill": "žaidimassuknisk", "traditionalgames": "tradiciškosžaidimai", "kniffel": "kniffel", "gamefps": "žaid<PERSON>pf<PERSON>", "textbasedgames": "tekstiniaižaidimai", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON>", "retrospel": "retro<PERSON><PERSON><PERSON>", "thiefgame": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "pie<PERSON>žaidimai", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stalofutbolas", "tischfußball": "stalofutbolas", "spieleabende": "<PERSON><PERSON><PERSON>", "jeuxforum": "<PERSON><PERSON><PERSON><PERSON>", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "flechetes", "escapegames": "pabė<PERSON><PERSON><PERSON>", "thiefgameseries": "vagystėsžaidimųserija", "cranegames": "kranožaidimai", "játék": "žaid<PERSON><PERSON>", "bordfodbold": "bordfutbolas", "jogosorte": "j<PERSON><PERSON><PERSON>", "mage": "<PERSON><PERSON><PERSON><PERSON>", "cargames": "automobiliųžaidimai", "onlineplay": "internetinигра", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pinigaiakiespinigai", "randomizer": "atsitiktiniųdalykų", "msx": "msx", "anagrammi": "anag<PERSON><PERSON>", "gamespc": "žaidimaispc", "socialdeductiongames": "socialinėsapga<PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "izometriniaižaidimai", "goodoldgames": "geriejogames", "truthanddare": "tiesaikarasizkai", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "lobiųmedžioklės", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhakas", "f2pgamer": "f2pgamer", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "fantazi<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "žaidimasmaniems", "halotvseriesandgames": "halotvserijosirzaidimai", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "vis<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "kalavijasirmagija", "goodgamegiving": "gera<PERSON><PERSON><PERSON><PERSON>", "jugamos": "ž<PERSON><PERSON><PERSON>", "lab8games": "lab8žaidimai", "labzerogames": "labzerogames", "grykomputerowe": "grykompiuteriniai", "virgogami": "virgogami", "gogame": "ž<PERSON><PERSON><PERSON>", "jeuxderythmes": "ritmųžaidimai", "minaturegames": "miniatiū<PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "savigarbosžaidimas", "gamemodding": "<PERSON><PERSON><PERSON>", "crimegames": "nusikaltimųžaidimai", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacenarf", "charades": "žodžiųžaidimas", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "kooperatyvinėžaid<PERSON>s", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "pagrin<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "karaliusdiscord", "scrabble": "žodžiųžaidimas", "schach": "<PERSON><PERSON><PERSON>", "shogi": "<PERSON><PERSON>i", "dandd": "dandd", "catan": "katanas", "ludo": "ludo", "backgammon": "š<PERSON>š<PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "pandemijospal<PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolijoszaidimas", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "komandinė<PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON>", "zombicide": "zombisavaites", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "blogasbuvo", "bloodbowl": "kraujo_keles", "cluedo": "cluedo", "xiangqi": "šachmai", "senet": "<PERSON><PERSON><PERSON><PERSON>", "goboardgame": "goboardgame", "connectfour": "<PERSON>sijun<PERSON><PERSON><PERSON><PERSON>", "heroquest": "heroj<PERSON><PERSON>š<PERSON>i", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "karomas", "tablegames": "staložaidžiai", "dicegames": "kauliuk<PERSON>žaidimai", "yatzy": "jat<PERSON>", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosminisrendezvous", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "staložaidimairpg", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "seniopgrėsmė", "switchboardgames": "staložaidžiai", "infinitythegame": "begalyb<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "šliaužiklipeltai", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "raudonokogyvenimas", "boardom": "nuobodu<PERSON>s", "applestoapples": "obuoliaiobuoliams", "jeudesociété": "socialinia<PERSON>žaidimai", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "krokinoletas", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "pragaroimperija", "horseopoly": "arkliųopoly", "deckbuilding": "kortųstatymas", "mansionsofmadness": "sukrėstųdvarų", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "brimstonėsšeš<PERSON>i", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "<PERSON><PERSON><PERSON><PERSON>", "tickettoride": "bilietasįkeliautoją", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "katano", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "šachmatai", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "žvaigždžiųkarailegiа", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terarija", "dsmp": "dsmp", "warzone": "karųzona", "arksurvivalevolved": "arkgyvencimovystymas", "dayz": "dienos", "identityv": "tapatybėv", "theisle": "salos", "thelastofus": "paskutinismūsų", "nomanssky": "nėražemėsrodyti", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyirpiešimoaparata<PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "tarpmūsų", "eco": "ekologiška", "monkeyisland": "beždžioniųsalos", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "pra<PERSON><PERSON>_dienos", "fobia": "fobija", "witchit": "ragauk", "pathologic": "patologinis", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "ilgai<PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "decadencijosbūdai2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "nealkmirk", "eternalreturn": "amžinasgrįžimas", "pathoftitans": "tita<PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "trintiesžaidimai", "hexen": "hexen", "theevilwithin": "piktasviduje", "realrac": "tikrošali<PERSON>", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "atgalinėseerdvėse", "empiressmp": "empiressmp", "blockstory": "blo<PERSON><PERSON><PERSON>asa<PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "mesla<PERSON>ingitiek", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "išgyven<PERSON><PERSON><PERSON>", "vintagestory": "vintageistorija", "arksurvival": "arkoperacija", "barotrauma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathedge": "breathege", "alisa": "alisa", "westlendsurvival": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "tams<PERSON><PERSON>is", "survivalhorror": "išgy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "gyventibaisių", "residentevil2": "rezidentasblogis2", "residentevil4": "residentevil4lt", "residentevil3": "rezidentasevil3", "voidtrain": "tuštumosgegučių", "lifeaftergame": "gyvenimaspožaidimų", "survivalgames": "išgyven<PERSON><PERSON>", "sillenthill": "tyl<PERSON><PERSON><PERSON>", "thiswarofmine": "šita<PERSON><PERSON>žaid<PERSON>", "scpfoundation": "scpfoundation", "greenproject": "žalioprojectas", "kuon": "kuon", "cryoffear": "verkužbaimės", "raft": "plau<PERSON><PERSON>", "rdo": "rdo", "greenhell": "žaliospragaro", "residentevil5": "residentevil5", "deadpoly": "mirtinaspoligonas", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "močiutė", "littlenightmares2": "mažoskošmarai2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "<PERSON><PERSON><PERSON>_sūnūs", "rustvideogame": "rust<PERSON><PERSON><PERSON>", "outlasttrials": "išgyventiįššūkius", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "naktisbešviesos", "7day2die": "7dienos2mirti", "sunlesssea": "bešniųj<PERSON><PERSON><PERSON>", "sopravvivenza": "išgyvenimas", "propnight": "propnaktis", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampyras", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "kataklizminiostamsiosdienos", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "gyven<PERSON><PERSON>", "ageofdarkness": "tamsosera", "clocktower3": "kelionėsklocktower3", "aloneinthedark": "vienastamsoje", "medievaldynasty": "viduramžiųdinastija", "projectnimbusgame": "projektasnimbusozauras", "eternights": "amžinivakarai", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "išgyvenimobandomosios", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "r<PERSON><PERSON><PERSON>ly<PERSON>", "tft": "tft", "officioassassinorum": "oficiorogelių", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "nugaliusnykams", "warhammer40kcrush": "warhammer40kįsimylėjimas", "wh40": "wh40", "warhammer40klove": "warhammer40klubas", "warhammer40klore": "warhammer40kpasakos", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40ktemposuktiltisi", "totalwarhammer3": "totalakarokaltiniai3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "mylimisororitas", "ilovevindicare": "myliuvindicare", "iloveassasinorum": "<PERSON><PERSON>ua<PERSON>sin<PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "<PERSON><PERSON><PERSON><PERSON>", "lioden": "lioden", "ageofempires": "imperijostempai", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilizationv": "civilizacijav", "ittakestwo": "gere<PERSON><PERSON><PERSON><PERSON>", "wingspan": "sparnųįtaka", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "galiųirmagi<PERSON>i", "btd6": "btd6", "supremecommander": "aukš<PERSON><PERSON>usioskomandos", "ageofmythology": "<PERSON>olo<PERSON><PERSON><PERSON><PERSON>", "args": "argai", "rime": "<PERSON>ai", "planetzoo": "planetozoo", "outpost2": "prieplaukavienas2", "banished": "<PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "raudonasįspėjimas", "civilization6": "civilizacija6", "warcraft2": "warcraft2", "commandandconquer": "valdikirinkirenk", "warcraft3": "warcraft3", "eternalwar": "amžinaskaras", "strategygames": "strategin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "civilizacijosžaid<PERSON>s", "civilization4": "civilizacija4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "visoškaskara<PERSON>", "travian": "travianas", "forts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goodcompany": "geraskompanija", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "greiciau<PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "tikrųlaikostrategija", "starctaft": "žvaigždžiųkarai", "sidmeierscivilization": "sidmeieriocivilizacija", "kingdomtwocrowns": "karalystėsduvainikai", "eu4": "eu4", "vainglory": "puikybė", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "metai", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davezafainąalgebrą", "plagueinc": "plagueinc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilizacija3", "4inarow": "4eilėnėje", "crusaderkings3": "krikščioniškikariai3", "heroes3": "herojai3", "advancewars": "advancewars", "ageofempires2": "imperijų<PERSON>ius2", "disciples2": "mokinius2", "plantsvszombies": "augalaivszombie", "giochidistrategia": "giochidistrategija", "stratejioyunları": "strategijomėgėjai", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "stebuklųera", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "pasaulioužkariaujam", "heartsofiron4": "sirdysisider4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "kovaužwesnoth", "aoe3": "aoe3", "forgeofempires": "imperij<PERSON>kal<PERSON><PERSON>", "warhammerkillteam": "karošarvaižudikųkomanda", "goosegooseduck": "žąsisžąsinukui", "phobies": "fobijos", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "<PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "empires4amžius", "civilization5": "civilizacija5", "victoria2": "victoria2", "crusaderkings": "kryžiuočiųkaraliai", "cultris2": "kultris2", "spellcraft": "burt<PERSON><PERSON>", "starwarsempireatwar": "žvaigždžiųkaraiimperijažygyje", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategija", "popfulmail": "popfulmail", "shiningforce": "švytinčijėgė", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportųkronas", "unrailed": "<PERSON>pa<PERSON><PERSON><PERSON>ę<PERSON>", "magicarena": "b<PERSON><PERSON><PERSON><PERSON>", "wolvesville": "vilkaišalyje", "ooblets": "ooblets", "planescapetorment": "planaskapetormentą", "uplandkingdoms": "uplandųkaralystės", "galaxylife": "galaksijosgyvenimas", "wolvesvilleonline": "vilkųmiestasinternete", "slaythespire": "nukausk<PERSON><PERSON>r<PERSON>", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "simai", "simcity": "simmiestas", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "greič<PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "simai2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicijapasiuntagrįžta", "darkhorseanthology": "tamsusisarklisantologija", "phasmophobia": "fob<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "penkiosnaktisfreddys", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "mažoskošmarai", "deadrising": "mirus<PERSON>iftas", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "namu<PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "ma<PERSON><PERSON>jinelaimėlė", "projectzero": "projektaszero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "sveikinimokaimynai", "helloneighbor2": "labaskaimynai2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "si<PERSON><PERSON><PERSON>aid<PERSON><PERSON>", "horrorgaming": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "kortųžaidimas", "tcg": "tcg", "cardsagainsthumanity": "kortospriešžmoniją", "cribbage": "kribidžia", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "slaptažodžiai", "dixit": "dixit", "bicyclecards": "dvir<PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "gwen<PERSON>", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "<PERSON><PERSON><PERSON><PERSON>", "poker": "pokeris", "hearthstone": "širdiesakmu<PERSON>", "uno": "uno", "schafkopf": "šafkopfas", "keyforge": "raktų<PERSON><PERSON>ras", "cardtricks": "kortųtriukai", "playingcards": "<PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "prekyboskortelės", "pokemoncards": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "mėsosirkraujostcg", "sportscards": "sportokortelės", "cardfightvanguard": "kortųkovosvanguard", "duellinks": "duellinks", "spades": "batai", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "rezistencija", "transformerstcg": "transformersktg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yug<PERSON><PERSON>žaid<PERSON><PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandinis", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kortųžaidimai", "mtgjudge": "mtgteismas", "juegosdecartas": "kortųžaidimai", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplansgaudymas", "mtgpreconcommander": "mtgpreconkomandoras", "kartenspiel": "kortųžaidimas", "carteado": "karčiadą", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "<PERSON><PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "kovosdvasiossaga", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "veidokortelė", "cardfight": "<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "magiskorte", "yugiohmasterduel": "<PERSON>ug<PERSON>hm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "šeš<PERSON><PERSON>ų<PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "kibernetinis", "classicarcadegames": "klasikiniaiarkadiniaižaidimai", "osu": "osu", "gitadora": "gitadora", "dancegames": "šokiožaidimai", "fridaynightfunkin": "penktadieniovakarųfunky", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektasmirai", "projectdiva": "projekt<PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "gitaražvaigždė", "clonehero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justdance": "tiesiogšok", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "roksmitas", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON><PERSON>", "dancecentral": "šokiaiširdyje", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "aukštųjųrezultatųritmožaidimai", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritmodangaus", "hypmic": "hypmic", "adanceoffireandice": "šokisugelmesirledas", "auditiononline": "audicijainternete", "itgmania": "itgmanija", "juegosderitmo": "rit<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kriptofthenekrodancer", "rhythmdoctor": "ritmodaktaras", "cubing": "kuba<PERSON><PERSON>", "wordle": "žodžiųžaidimas", "teniz": "teniz", "puzzlegames": "dėlioniųžaidimai", "spotit": "suraskit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "loginisuzduotys", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "galvosūkiai", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "mįsliųžaidimas", "motscroisés": "motscroisés", "krzyżówki": "mįslės", "nonogram": "nonogramas", "bookworm": "knygynas", "jigsawpuzzles": "puzzlebul<PERSON><PERSON><PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "mįslė", "riddles": "mįslės", "rompecabezas": "d<PERSON>lionė", "tekateki": "tekateki", "inside": "viduje", "angrybirds": "piktieji_paukščiai", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "minųšluotažas", "puzzleanddragons": "puzzleirdragonai", "crosswordpuzzles": "mįslės", "kurushi": "k<PERSON>hi", "gardenscapesgame": "sodųpeizažasžaidimas", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "pabėgimokambari<PERSON>", "escapegame": "pab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3d<PERSON><PERSON><PERSON><PERSON>", "homescapesgame": "namųbūsenossistema", "wordsearch": "žodžiųpaieška", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "mįsliųpasakos", "fishdom": "<PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "neįmanomasklausimas", "candycrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlebigplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "match3dėlionė", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kuirki", "rubikcube": "<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "talosprincipas", "homescapes": "namų<PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "išspręskmanąpokalbį", "tycoongames": "verslininkųžaidimai", "cubosderubik": "kubųkubikų", "cruciverba": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ciphers": "šifrai", "rätselwörter": "mįslėsžodžiai", "buscaminas": "minuokb<PERSON><PERSON>", "puzzlesolving": "dėlionėssprendimas", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "<PERSON><PERSON><PERSON>", "guessing": "spėk", "nonograms": "nenogramai", "kostkirubika": "kostkirubika", "crypticcrosswords": "slaptiejižodžiai", "syberia2": "syberia2", "puzzlehunt": "dėlionemedžioklė", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON>", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "galvosukiai", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefinuoti", "picopark": "picopark", "wandersong": "klajonesong", "carto": "karto", "untitledgoosegame": "bevardisžąsysžaidimas", "cassetête": "kasetė", "limbo": "limbo", "rubiks": "<PERSON><PERSON>", "maze": "labirintas", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON>kost<PERSON>", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "dalys", "portalgame": "portalūsžaistas", "bilmece": "bilmece", "puzzelen": "<PERSON><PERSON><PERSON><PERSON>", "picross": "p<PERSON><PERSON><PERSON>", "rubixcube": "<PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobilus", "codm": "codm", "twistedwonderland": "sukratytasstebuklas", "monopoly": "monopolis", "futurefight": "ateitieskovos", "mobilelegends": "mobilieslegendos", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "koki", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ansambliožvaigž<PERSON><PERSON>", "asphalt9": "asfaltas9", "mlb": "mlb", "cookierunkingdom": "sausainukųkaralystė", "alchemystars": "alchemystars", "stateofsurvival": "išgyvenimostija", "mycity": "manomiestas", "arknights": "arknights", "colorfulstage": "spalvingascena", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON>", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "ugniniaiherojai", "honkaiimpact": "honkaiimpact", "soccerbattle": "futbolomūšis", "a3": "a3", "phonegames": "telefonožaidimai", "kingschoice": "karališkas<PERSON>nk<PERSON><PERSON>", "guardiantales": "guardianorait<PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikaliai", "cookierun": "sausainiųbėgimas", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "užburbuls", "craftsman": "<PERSON><PERSON><PERSON><PERSON>", "supersus": "<PERSON><PERSON><PERSON>", "slowdrive": "lėtasvažiavimas", "headsup": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "žodžiųkarai", "bedwars": "lovoskarai", "freefire": "laisvas<PERSON><PERSON><PERSON>", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "gyvū<PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "komandųkovosstrategijos", "clashofclans": "kaunasklanai", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "skubiossituacijoshq", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "j<PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "šokaiirfidgetai", "ml": "ml", "bangdream": "bangdream", "clashofclan": "klanųsusid<PERSON><PERSON><PERSON>", "starstableonline": "žvaigždžiųstotelėonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "laikprincesė", "beatstar": "beatstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "kišeninismeilė", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "šauktinyskara<PERSON>", "cookingmadness": "virėjostikrųtikras", "dokkan": "dokkan", "aov": "aov", "triviacrack": "trivia<PERSON><PERSON>", "leagueofangels": "angelųliga", "lordsmobile": "lordsmobile", "tinybirdgarden": "mažiukuopsgarden", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "m<PERSON>s<PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "veidrodžiųvisata", "pou": "pou", "warwings": "karosparnai", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegend<PERSON><PERSON><PERSON>", "ingress": "įėjimas", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "monetųmeistras", "punishinggrayraven": "b<PERSON><PERSON><PERSON><PERSON>pilkasisrave<PERSON>", "petpals": "šeimininkai", "gameofsultans": "sultānųžaidimas", "arenabreakout": "arenabreakout", "wolfy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "mobiležaidimai", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mi<PERSON><PERSON>", "blackdesertmobile": "juo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "didžiausiosmedžioklės", "bombmebrasil": "bombmebrazilija", "ldoe": "ldoe", "legendonline": "legendosinternete", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "minindustrija", "callofdragons": "drakonųkvietimas", "shiningnikki": "š<PERSON><PERSON>tinik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "šešėliųkovos3", "limbuscompany": "limbuscompany", "demolitionderby3": "demoliuotuvųderbis3", "wordswithfriends2": "žodžiaissupriklausomais2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfektyvistas", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmob<PERSON>us", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "to<PERSON>sp<PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "imperijosirpuzzleai", "empirespuzzles": "empirespuzzle", "dragoncity": "drakonųmiestas", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aetheržvilgsnis", "mudrunner": "purvobėgi<PERSON>", "tearsofthemis": "ašarosaudominų", "eversoul": "amžinaspriešseks<PERSON>", "gunbound": "<PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "žaidimasmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombikybiai", "eveechoes": "<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobiliejilegendosbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "virtuviomama", "cabalmobile": "cabalmobilus", "streetfighterduel": "gatvėskovotojų<PERSON><PERSON>šis", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurasleidynosgyva", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "vienasgabalaspinignusk<PERSON>", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftlenktynėsinternete", "jogosmobile": "jogo<PERSON><PERSON><PERSON>", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "žaidimomobilailegendos", "timeraiders": "timeraiders", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrik<PERSON><PERSON><PERSON><PERSON><PERSON>", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "ieškok", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "keliautojasrpg", "2300ad": "2300ad", "larp": "larpas", "romanceclub": "romantikųklubas", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipip", "porygon": "porygon", "pokemonunite": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "p<PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "komandoraketa", "furret": "kurmis", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "kišeniniai_monstrai", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "teammystic", "pokeball": "pokebolas", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "p<PERSON><PERSON><PERSON><PERSON>", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psisdak", "umbreon": "umbreonas", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmei<PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "vaikaiir<PERSON>kemona<PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "blizgu<PERSON><PERSON><PERSON><PERSON>", "ajedrez": "šachmatai", "catur": "katur", "xadrez": "šachmatai", "scacchi": "šachmatai", "schaken": "<PERSON><PERSON><PERSON><PERSON>", "skak": "skak", "ajedres": "šachai", "chessgirls": "šachmataimerginos", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "šachmatai", "japanesechess": "japonųšachmatai", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šachmataikanada", "fide": "fide", "xadrezverbal": "žodžiųšachmatai", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "<PERSON><PERSON><PERSON><PERSON>", "chesscom": "šachmatųkomanda", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON>sirdra<PERSON>", "dungeonsanddragon": "dungeonaiirdragons", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "tamsasaulė", "thelegendofvoxmachina": "balsomakinalegenda", "doungenoanddragons": "dungeonaiirdragons", "darkmoor": "tamsusispelkė", "minecraftchampionship": "minecraftčempionatas", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "svajonųsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodai", "mcc": "mcc", "candleflame": "žvakėsliepsna", "fru": "fru", "addons": "pap<PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftpocket": "minecraftkišeninė", "minecraft360": "minecraft360", "moddedminecraft": "modifikuotasminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftkompiuteryje", "betweenlands": "tarpžemiai", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftmiestas", "pcgamer": "pcžaidėjai", "jeuxvideo": "žaidimospavydžiai", "gambit": "gambitas", "gamers": "žaidėjai", "levelup": "pakilkįnaujįlygį", "gamermobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameover": "žaidimasbaigtas", "gg": "gg", "pcgaming": "kompiuteriniaižaidimai", "gamen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "kompiuteriniaižaidimai", "casualgaming": "neformal<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "žaidimųįranga", "pcmasterrace": "pcmeist<PERSON><PERSON>", "pcgame": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "<PERSON><PERSON><PERSON><PERSON>", "vrgaming": "vržaidimai", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON>", "consoleplayer": "kons<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "epiniaižaidėjai", "onlinegaming": "internetiniaižaidimai", "semigamer": "pusgamers", "gamergirls": "<PERSON><PERSON><PERSON><PERSON>", "gamermoms": "<PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "<PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "koman<PERSON><PERSON><PERSON>gi<PERSON><PERSON>", "mallugaming": "mallugaming<PERSON>", "pawgers": "pawgers", "quests": "misijos", "alax": "aleik", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "kompiuteriniųžaidimų", "dsswitch": "dsswitch", "competitivegaming": "konkurencingaspazaidimai", "minecraftnewjersey": "minecraftnaujajersey", "faker": "fakers", "pc4gamers": "pc4žaidėjai", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "heterose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "žaidimamppc", "girlsgamer": "mergaikaitespzaidėjoms", "fnfmods": "fnfmodai", "dailyquest": "kasdieninėmisužduotys", "gamegirl": "žaidimųmerga", "chicasgamer": "gameriųmėgėms", "gamesetup": "<PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "vaid<PERSON><PERSON><PERSON><PERSON>", "myteam": "manoekipažas", "republicofgamers": "žaidėjųrepublika", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "tripleleg<PERSON>a", "gamerbuddies": "žaidėjųdraugai", "butuhcewekgamers": "betūhcewekgamers", "christiangamer": "krikščioniš<PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "neuro<PERSON><PERSON><PERSON><PERSON>", "afk": "ofl", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89komanda", "inicaramainnyagimana": "inicaramainyagimana", "insec": "insec", "gemers": "gemeriai", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "kartužaid<PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationožaidėjas", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "sveika<PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "žaidimųnotebookas", "protogen": "protogen", "womangamer": "moter<PERSON><PERSON><PERSON>", "obviouslyimagamer": "žinomaes<PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "ma<PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "piktžoliųieškotojas", "humanfallflat": "žmoguskrin<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nulinėspabėgi<PERSON>", "waluigi": "vali<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "rudenisongei", "switch": "<PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "zeldaolegenda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "padangokidslight", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "vaikščiojimo_simulatoriai", "nintendogames": "nintendogames", "thelegendofzelda": "zeldalegenda", "dragonquest": "drakonų<PERSON>ška", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "rū<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "la<PERSON><PERSON><PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "zeldalegendos", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON>aid<PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "pasakos", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "gyvūnųkirtimas", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendočile", "tloz": "tloz", "trianglestrategy": "trikampiųstrategija", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kankorėžiaiblogaisplaukaisdiena", "nintendos": "nintendos", "new3ds": "naujas3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulekariai", "mariopartysuperstars": "mariopartysuperzvaigždės", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "j<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cbvos", "leagueoflegendslas": "legendsligavalas", "urgot": "urgot", "zyra": "zyra", "redcanids": "raudonųjųkan<PERSON>dų", "vanillalol": "vanilinislol", "wildriftph": "wildriftlt", "lolph": "juokislygai", "leagueoflegend": "legend<PERSON><PERSON><PERSON>", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "lygakovaistespanija", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON>k<PERSON><PERSON>", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendos", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortni<PERSON>žaid<PERSON><PERSON>", "gamingfortnite": "žaidimfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrožaidimai", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "žaidimovideo", "professorlayton": "profesor<PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatch": "<PERSON><PERSON><PERSON><PERSON>", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "raganius101", "battleblocktheater": "battleblockteatras", "arcades": "<PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "pufpals", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "robloxlietuva", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxvokietija", "robloxdeutsch": "robloxlietuviškai", "erlc": "erlc", "sanboxgames": "smėliodė<PERSON>", "videogamelore": "<PERSON><PERSON><PERSON>", "rollerdrome": "<PERSON><PERSON><PERSON><PERSON>", "parasiteeve": "parazitas", "gamecube": "gamecube", "starcraft2": "žvaigždžiųkaryba2", "duskwood": "duskwood", "dreamscape": "svajoniųerdvė", "starcitizen": "žvaigždžiųpilietis", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "grandtheftauto", "deadspace": "nesamprata", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "senosiosrepublikos", "videospiele": "žaidimaitvar<PERSON>j", "touhouproject": "touhouprojektas", "dreamcast": "svajoniųtransliacija", "adventuregames": "nuotykiųžaidimai", "wolfenstein": "wolfenstein", "actionadventure": "veiksmųnuotykiai", "storyofseasons": "sezonųistorija", "retrogames": "retrožaidimai", "retroarcade": "retroarkada", "vintagecomputing": "vintažiniskompųnaudojimas", "retrogaming": "retrožaidimai", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON>az<PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON>", "commanderkeen": "komandorasalks", "bugsnax": "bugsnax", "injustice2": "neteisybė2", "shadowthehedgehog": "šadulisas<PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "zengyvenimas", "beatmaniaiidx": "beatmaniaiidx", "steep": "staigus", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "konsoliniužaidimai", "konsolen": "konsolė<PERSON>", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "žydintispanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "superdidelis", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "ū<PERSON>os<PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktyvia<PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "paskutinismus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "vizualinifromas", "visualnovels": "viz<PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "š<PERSON>š<PERSON>linisvilkas", "tcrghost": "tc<PERSON><PERSON><PERSON><PERSON><PERSON>", "payday": "atlyginimodiena", "chatherine": "ketherine", "twilightprincess": "twilightprincess", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "smėliod<PERSON>žė", "aestheticgames": "aestetiskosž<PERSON>s", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "ekipa2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrožaidimas", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "lapųpūtimorevoliucija", "wiiu": "wiiu", "leveldesign": "lygiuokiteiskaita", "starrail": "žvaigždžiųgeležinkelis", "keyblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafstkartais", "novelasvisuales": "viz<PERSON><PERSON>nov<PERSON><PERSON>", "robloxbrasil": "robloxlietuva", "pacman": "pacman", "gameretro": "žaidimospovintage", "videojuejos": "<PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "manosaldimeilė", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangospeju", "returnofreckoning": "grįžtandaugiausia", "gamstergaming": "<PERSON><PERSON><PERSON>", "dayofthetantacle": "tencaklodiena", "maniacmansion": "mania<PERSON><PERSON><PERSON>", "crashracing": "avarijavert<PERSON><PERSON>", "3dplatformers": "3dplatformeriai", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "senosiosmokslinėsž<PERSON>imai", "hellblade": "p<PERSON><PERSON><PERSON><PERSON>", "storygames": "stori<PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "užtrijųdūšių", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "mirtisbešnibždėjimų", "tinybunny": "mažasiskiškus", "retroarch": "retroarch", "powerup": "galing<PERSON>s", "katanazero": "katanazero", "famicom": "famikom", "aventurasgraficas": "grafikosskaitymai", "quickflash": "gre<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkados", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "galiašluota", "coralisland": "koralųsalos", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "kitas<PERSON><PERSON><PERSON>", "metaquest": "metaegzaminas", "animewarrios2": "animekarai2", "footballfusion": "futbolosujungimas", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvels", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "suk<PERSON>iptasmetala<PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "gėdoskrūva", "simulator": "simulia<PERSON><PERSON>", "symulatory": "simuliatoriai", "speedrunner": "gre<PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gryviedo", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "stebuklųšalisinternete", "skylander": "<PERSON>ngausk<PERSON><PERSON>", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "perpieštišvyturį", "simracing": "simracing", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON>est<PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "dangauskūnai", "seum": "seum", "partyvideogames": "šventėsvaizdožaidimai", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "kosmosoekskursija", "legacyofkain": "kaino_palikimas", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "žaidimųvide<PERSON>", "thewolfamongus": "vilkasykmente", "truckingsimulator": "sunk<PERSON><PERSON><PERSON>iųsimulia<PERSON><PERSON>", "horizonworlds": "horizontokantai", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "senosmokyklinė<PERSON>", "racingsimulator": "<PERSON><PERSON><PERSON>iųsimu<PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "dainuokpop", "famitsu": "famitsu", "gatesofolympus": "olimpogates", "monsterhunternow": "monsterhunterdabar", "rebelstar": "maištingasžvaigždė", "indievideogaming": "indiežaidimai", "indiegaming": "indiegaming", "indievideogames": "indiežaidimai", "indievideogame": "indiežaidimas", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "bufffortress", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projekt<PERSON><PERSON>", "futureclubgames": "ateitiesklubozaidimai", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "<PERSON><PERSON><PERSON><PERSON>oksla<PERSON>", "backlog": "atlikti", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "žaid<PERSON>ųatsargųsą<PERSON>ša<PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "miestoatvaizdžiai", "supermonkeyball": "supermonkeyball", "deponia": "deponija", "naughtydog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "žvėrisvaldytojas", "juegosretro": "retrožaidimai", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervatoriodopamino", "staxel": "staxel", "videogameost": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "drakonsinchronizacija", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "mylimikofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "sprogstam", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "pir<PERSON>d", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "drakonoobuziukai", "sadanime": "li<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "tamsiauužjuodą", "animescaling": "animeša<PERSON><PERSON>", "animewithplot": "animesuiskynu", "pesci": "pesci", "retroanime": "retroanime", "animes": "anime", "supersentai": "superseimai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "90sanimacija", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezonas1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "escaflownevizija", "slayers": "žudikai", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "banan<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "ugni<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartypatri<PERSON><PERSON>", "futurediary": "ateities<PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "pasakaitė", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "paga<PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "šingekinokojin", "mushishi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "undinė<PERSON>ėsdain<PERSON>i", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "siau<PERSON>manga", "romancemangas": "romantikomanagų", "karneval": "<PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "drak<PERSON>mergina", "blacklagoon": "juo<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "<PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "tamtikrasburtųindexas", "sao": "sao", "blackclover": "juodas<PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyokauk<PERSON><PERSON><PERSON>", "onepunchman": "vienosmūgiožmogus", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8begalybė", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioritetas", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldenkamuy": "auksiniskamuy", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportoanimacija", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaižaidime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "šiaurėsž<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "dievobokštas", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kaiplaikytiaplinkmamą", "fullmoonwosagashite": "pil<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "<PERSON><PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "mielaiiršiektiekbaisiai", "martialpeak": "k<PERSON>tip<PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoremerga", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "miegaikom", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "inosukė", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstrųmergina", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "senasanime", "chainsawman": "troninėspjo<PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengrai", "blackbutler": "juodas<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "<PERSON><PERSON><PERSON>", "loli": "loli", "horroranime": "siau<PERSON>nime", "fruitsbasket": "vaisiųkrepšys", "devilmancrybaby": "velnioklisverkšlenimokū<PERSON>kis", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "gyvenkmeilėje", "sakuracardcaptor": "sakurakortųgaudytoja", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "tavysožesbalandį", "buggytheclown": "buggyklounas", "bokunohero": "bokunohero", "seraphoftheend": "seraphepabaigoje", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "gil<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "maistokarai", "cardcaptorsakura": "kortųgaudykliųsakura", "stolas": "stolas", "devilsline": "velniūkųlinija", "toyoureternity": "tavoamžinybei", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "grifit<PERSON><PERSON><PERSON>", "shinigami": "<PERSON><PERSON><PERSON>", "secretalliance": "slaptasaliansas", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON>š<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detektyvasconanas", "shiki": "šiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riaskremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "mėlynasekzorcistas", "slamdunk": "skaldykirąstą", "zatchbell": "<PERSON><PERSON>bell", "mashle": "<PERSON><PERSON><PERSON><PERSON>", "scryed": "scryed", "spyfamily": "spyšeim<PERSON>", "airgear": "airoversas", "magicalgirl": "maginėmergaitė", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "ka<PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "mok<PERSON><PERSON><PERSON><PERSON>", "kissxsis": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "manodrabužiaiškabejo", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animevisata", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nebegyvasnepasiseks", "romancemanga": "roman<PERSON>ėman<PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromantika", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "demonųžudikaskirtukas", "bloodlad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "atsisveikinuboo", "firepunch": "ugni<PERSON><PERSON><PERSON><PERSON>", "adioseri": "atsisveikinipaketai", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "žvaigždėsįsitvirtina", "romanceanime": "romantinisanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "vyšniųmagija", "housekinokuni": "namukinuodalyje", "recordragnarok": "įrašykragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "mirt<PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "mirtiesparadas", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "j<PERSON><PERSON><PERSON><PERSON>", "animespace": "animesfera", "girlsundpanzer": "merginosundpanzer", "akb0048": "akb0048", "hopeanuoli": "viltiesnuolis", "animedub": "animevo", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "žmogusrates", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklubas", "dragonquestdai": "drakonoieškodai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "skan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnaraksoįrašas", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "perdaugįrankių", "toriko": "<PERSON><PERSON>o", "ravemaster": "r<PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "raganiųkelniųateljė", "lansizhui": "lansizuok", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangųgyvenimas", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "<PERSON>od<PERSON><PERSON><PERSON><PERSON>", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "švent<PERSON>ey<PERSON>", "greatteacheronizuka": "nuostabus<PERSON><PERSON>", "gridman": "tinklinisžmogus", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>_tėtis", "gear5": "įrangos5", "grandbluedreaming": "didžiosiosmėlynossvajonės", "bloodplus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "<PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "demonųirdievųpasakos", "goreanime": "goreanime", "animegirls": "animemerginos", "sharingan": "<PERSON><PERSON>", "crowsxworst": "varnai<PERSON><PERSON><PERSON><PERSON>", "splatteranime": "splashanime", "splatter": "sp<PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "skydheroauga", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimep<PERSON><PERSON><PERSON>", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animeispanija", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "vaikaiišbalų", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superkempionai", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "naktieskvietimas", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bak<PERSON><PERSON><PERSON><PERSON>i", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "findermanga": "finderman<PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "para<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevisata", "persocoms": "persokai", "omniscientreadersview": "viskąžinanciosknygosperspektyva", "animecat": "animekatė", "animerecommendations": "animerekomendacijos", "openinganime": "atgimi<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "manopaaugliųroma<PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "mi<PERSON>žiniškirobotai", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobiluskovojaresggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bleach": "bali<PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojospakraščiai", "fullmetalalchemist": "pilnasmetaloalchemikas", "ghiaccio": "ledas", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorros", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "žvaigždžiųlapė", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupin3tas", "animecity": "animemiestas", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "vienasgabalas", "animeonepiece": "animevienasvnt", "dbz": "dbz", "dragonball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonnuotykių", "hxh": "hxh", "highschooldxd": "vid<PERSON><PERSON><PERSON><PERSON>", "goku": "goku", "broly": "<PERSON><PERSON><PERSON><PERSON>", "shonenanime": "šonenoanime", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "drakmens", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "medžiotojasxmedžiotojas", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "eren<PERSON><PERSON>", "myheroacademia": "manoh<PERSON>inėakademija", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "vienaskydanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "vienasvienetasyratikras", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoekzorcistas", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "digimonistorija", "digimontamers": "digimontameriai", "superjail": "superkalėjimas", "metalocalypse": "metalokalipsė", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostklubas", "flawlesswebtoon": "bepriekaištiswebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animekomika", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "jurųstovykla", "nonnonbiyori": "nenonbiorai", "flyingwitch": "skrajojanti<PERSON><PERSON>ė", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "visųšventųjųgatvė", "recuentosdelavida": "gy<PERSON><PERSON><PERSON><PERSON>"}