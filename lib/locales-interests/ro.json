{"2048": "2048", "mbti": "mbti", "enneagram": "eneagrama", "astrology": "astrologie", "cognitivefunctions": "functiicognitive", "psychology": "psihologie", "philosophy": "filozofie", "history": "istorie", "physics": "fizica", "science": "stiinta", "culture": "cultura", "languages": "limbi", "technology": "tehnologie", "memes": "meme", "mbtimemes": "meme<PERSON><PERSON>", "astrologymemes": "memedinastrologie", "enneagrammemes": "memecueneagrame", "showerthoughts": "gand<PERSON><PERSON><PERSON>", "funny": "amuzant", "videos": "videouri", "gadgets": "dispozitive", "politics": "politica", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "sfaturideviata", "crypto": "criptom<PERSON>a", "news": "nouta<PERSON>", "worldnews": "noutatidinlume", "archaeology": "arheologie", "learning": "invatare", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "teoriiconspirationale", "universe": "univers", "meditation": "meditatie", "mythology": "mitologie", "art": "arta", "crafts": "lucrurimanuale", "dance": "dans", "design": "design", "makeup": "<PERSON><PERSON><PERSON>", "beauty": "frumusete", "fashion": "moda", "singing": "cantat", "writing": "scris", "photography": "fotografie", "cosplay": "cosplay", "painting": "pictura", "drawing": "desen", "books": "carti", "movies": "filme", "poetry": "poezie", "television": "televiziune", "filmmaking": "cinematografie", "animation": "animatie", "anime": "anime", "scifi": "sf", "fantasy": "<PERSON><PERSON><PERSON>", "documentaries": "documentare", "mystery": "mister", "comedy": "comedie", "crime": "crima", "drama": "drama", "bollywood": "bollywood", "kdrama": "dramacore<PERSON>a", "horror": "horror", "romance": "poveștidedragoste", "realitytv": "realitytv", "action": "actiune", "music": "muzica", "blues": "clues", "classical": "muzicaclasica", "country": "muzicacountry", "desi": "desi", "edm": "edm", "electronic": "muzicaelectronica", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latino", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "calatorie", "concerts": "concerte", "festivals": "festivaluri", "museums": "muzee", "standup": "standup", "theater": "teatru", "outdoors": "<PERSON><PERSON><PERSON><PERSON>", "gardening": "gradina<PERSON>", "partying": "petrecere", "gaming": "gaming", "boardgames": "jocuridesocietate", "dungeonsanddragons": "dungeonsanddragons", "chess": "sah", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "mancare", "baking": "preparare", "cooking": "gatit", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "pasari", "cats": "pisici", "dogs": "caini", "fish": "peste", "animals": "animale", "blacklivesmatter": "blacklivesmatter", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminism", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "adeptlgbtq", "stopasianhate": "stopuraasiatică", "transally": "aliattrans", "volunteering": "voluntariat", "sports": "sporturi", "badminton": "badminton", "baseball": "baseball", "basketball": "baschet", "boxing": "box", "cricket": "crichet", "cycling": "ciclism", "fitness": "fitness", "football": "fotbal", "golf": "golf", "gym": "sala", "gymnastics": "gimnastica", "hockey": "hochei", "martialarts": "artemartiale", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "alergat", "skateboarding": "skateboarding", "skiing": "schi", "snowboarding": "snowboarding", "surfing": "surf", "swimming": "<PERSON>not", "tennis": "tenis", "volleyball": "volei", "weightlifting": "haltere", "yoga": "yoga", "scubadiving": "scubadiving", "hiking": "<PERSON><PERSON><PERSON>", "capricorn": "capricorn", "aquarius": "var<PERSON><PERSON>", "pisces": "pesti", "aries": "be<PERSON><PERSON>", "taurus": "taur", "gemini": "gemeni", "cancer": "rac", "leo": "leu", "virgo": "fecioara", "libra": "balanta", "scorpio": "scorpion", "sagittarius": "sage<PERSON>or", "shortterm": "petermen", "casual": "casual", "longtermrelationship": "rela<PERSON><PERSON>petermenlung", "single": "<PERSON>ur", "polyamory": "poliamorie", "enm": "nonmonogamie", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbiană", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "asasiniscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "câinipaznici", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "reavedesuflete", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "apuneabuznăvilor", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemdestin", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "războaielegildelor", "openworld": "lumeaîntreagă", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "explorareadungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribur<PERSON><PERSON><PERSON><PERSON>", "planescape": "planulaventurii", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "patfofexil", "immersivesims": "simsimmersive", "okage": "okage", "juegoderol": "juegoderol", "witcher": "vârcolac", "dishonored": "dezonorat", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "pulbere", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "creareacaracterelor", "immersive": "imersiv", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivațiemoartea", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "nebundupentrudragoste", "otomegames": "jocuride<PERSON>e", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vamp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimension20": "dimension20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "explorator", "pathfinder2ndedition": "căutător2ediția", "shadowrun": "umbrașanșului", "bloodontheclocktower": "sângepeceasornică", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravit<PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "uițitotul", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "suveran", "yourturntodie": "evidatastatului", "persona3": "persona3", "rpghorror": "rpggroaza", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "mara<PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demon<PERSON>uf<PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "adăpostpentrufugă", "gurps": "gurps", "darkestdungeon": "temniculdungeon", "eclipsephase": "eclipsephase", "disgaea": "disgaea", "outerworlds": "lumeadetrebuiri", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "legaturadeisaac", "diabloimmortal": "diabloimortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "fetelecranilor", "nightcity": "noapteacitiesc", "hogwartslegacy": "moștenireahogwarts", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "drumul96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "cavaleriigotham<PERSON>i", "forgottenrealms": "tărâmurileuitate", "dragonlance": "dragonlance", "arenaofvalor": "arenadevalor", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "copildelumină", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "feric<PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "verminte2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "tronurifisurate", "horizonforbiddenwest": "orizontulinterzisvest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON>er<PERSON><PERSON><PERSON>", "deltagreen": "deltaverde", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "ultimileepoci", "starfinder": "caut<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "soareleaurit", "divinityoriginalsin": "divinitatepăcatoriginal", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrosu", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordonafallen", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "țărilemalefice", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diavolulsupraviețuitor", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinitate", "pf2": "pf2", "farmrpg": "fermarpg", "oldworldblues": "bluesulvechiuluișu", "adventurequest": "aventurapetrecere", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "jocurideimitație", "roleplayinggames": "jocuriderol", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "povestileasymfoniei", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "orașulrăvășit", "myfarog": "bifarog", "sacredunderworld": "sacredunderworld", "chainedechoes": "lan<PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "sufletîntunecat", "soulslikes": "soulslikes", "othercide": "altcidem", "mountandblade": "muntesiapentru", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "cronotimp", "pillarsofeternity": "stâlpiideeternitate", "palladiumrpg": "palladiumrpg", "rifts": "feliile", "tibia": "tibia", "thedivision": "divizia", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legend<PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "cop<PERSON><PERSON>rmorte", "engineheart": "motorulin<PERSON>ii", "fable3": "poveste3", "fablethelostchapter": "fablecapitolost", "hiveswap": "hiveswap", "rollenspiel": "jocderol", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "revenireanostalgică", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "lumeasavagenilor", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "regatulinimii1", "ff9": "ff9", "kingdomheart2": "regatul_inimilor2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "jocurirpg", "kingdomhearts": "regatulinimilor", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "<PERSON>az<PERSON><PERSON><PERSON><PERSON><PERSON>", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "recoltează", "gloomhaven": "gloomhaven", "wildhearts": "inimivii", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "cer<PERSON><PERSON><PERSON><PERSON>", "shadowhearts": "inimileumbră", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "suflulfocului4", "mother3": "mama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "altăeden", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "jocderol", "fabulaultima": "fabulault<PERSON>", "witchsheart": "inimădevrăjitoare", "harrypottergame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "exploratorirpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "masqueradăvampirică", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "cronocross", "cocttrpg": "cocttrpg", "huntroyale": "vânătoareroyală", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "hashtagulbooshadowheartscovenant", "bladesoul": "sufletdecampaignă", "baldursgate3": "baldursgate3", "kingdomcome": "regatulvine", "awplanet": "awplanet", "theworldendswithyou": "lumeafinalizatecutine", "dragalialost": "dragalialost", "elderscroll": "bunicisca", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "necredincioșieîntunecată", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magieapământului", "blackbook": "carteanegra", "skychildrenoflight": "cop<PERSON>eld<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "edițiaaurasacrat", "castlecrashers": "<PERSON><PERSON><PERSON>", "gothicgame": "jocgothic", "scarletnexus": "nex<PERSON><PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "miculorașdeceață", "indierpg": "indierpg", "pointandclick": "clickșițintește", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivizibil", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "drumulcătremoartăcătrecanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "vânătormoniterapeuţi", "fireemblem": "emblemauitată", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremație", "persona5": "persona5", "ghostoftsushima": "fantomadetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "vânătorimonstri", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "mâncătordesuflete", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "joc<PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtactic", "mahoyo": "mahoyo", "animegames": "joc<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "mâncătorcumparaturi", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeterna", "princessconnect": "princessconnect", "hexenzirkel": "cer<PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "cristale", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "maddean", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "jocurialelectronice", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligavisătoarelor", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "cybersport", "crazyraccoon": "râsneșcrazy", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitiv", "t3arena": "t3arena", "valorantbr": "valorantro", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "jumătateviață", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valvă", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simulategoat", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetaliberă", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battleground4", "nightinthewoods": "noapteenawoods", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riscurideploaie2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "interplanetar", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7zile2zile", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "b<PERSON><PERSON><PERSON>", "stray": "stray", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "câmpuldealuptă1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboot", "eyeb": "ochi", "blackdesert": "desertnegru", "tabletopsimulator": "simulator<PERSON>abele", "partyhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "incarcer<PERSON><PERSON>", "hades": "hades", "gunsmith": "făcă<PERSON><PERSON>pisto<PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "prin<PERSON><PERSON><PERSON><PERSON>", "dinkum": "dinkum", "predecessor": "predecesor", "rainworld": "lumeaploaie", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "simulcolonial", "noita": "noita", "dawnofwar": "dawn<PERSON>ră<PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "darkșiîntunecat", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "cubesupravie<PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "or<PERSON><PERSON><PERSON>", "citiesskylines": "orizonturideorașe", "defconheavy": "defconheavy", "kenopsia": "kenopsie", "virtualkenopsia": "virtualkenopsia", "snowrunner": "zăpadărunners", "libraryofruina": "bibliotecadepăcat", "l4d2": "l4d2", "thenonarygames": "joc<PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "căutătorde<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "rațucadeplasticatranquila", "battlebit": "battlebit", "ultimatechickenhorse": "ultimacodă<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "dialtown", "smileforme": "zâmbeșteptmine", "catnight": "noapteapisicilor", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "grani<PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "fortadefensăpământului", "huntshowdown": "vânătoareapereților", "ghostrecon": "fantomarecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "alăturateechipei", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "furtunadelainsurgență", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divizii2", "killzone": "zonadeucidere", "helghan": "hel<PERSON>", "coldwarzombies": "zombicidefrig", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "războimodern", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "eva<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnajprimal", "worldofwarships": "lumeawarships", "back4blood": "înapoi4sânge", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON>n", "masseffect": "masseffect", "systemshock": "șoculsistemului", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "povesteadev<PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "secoluleprafurilor", "farcry4": "farcry4", "gearsofwar": "roboțiiolimpici", "mwo": "mwo", "division2": "divizia2", "tythetasmaniantiger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "generatiazero", "enterthegungeon": "intrăînplutonă", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "războimodern2", "blackops1": "blackops1", "sausageman": "omulsaucisă", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON>", "warface": "fațaderăzboi", "crossfire": "cruciada", "atomicheart": "inimăatomică", "blackops3": "blackops3", "vampiresurvivors": "vampirisupraviețuitori", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "moricel", "freedoom": "libertate", "battlegrounds": "câmpdebătă", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearuitorilalibertății", "juegosfps": "jocurifps", "convertstrike": "convertsocietate", "warzone2": "warzone2", "shatterline": "spargelinie", "blackopszombies": "blackopszombies", "bloodymess": "haosuldeinsangerat", "republiccommando": "comandorepublicii", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "echipă", "destiny1": "destin1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "înrolat", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "nuclearmăcinat", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tin<PERSON>lesfant<PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "ziuaplății2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "fantomcod", "csplay": "csplay", "unrealtournament": "turneulunreal", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "contratac", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "campioniicutremurului", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "celulădecum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonwhite": "neonwhite", "remnant": "resturi", "azurelane": "azurelane", "worldofwar": "lumeawar", "gunvolt": "gunnvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "microvolts", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battleground3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "mareahoților", "rust": "rugina", "conqueronline": "conqueronline", "dauntless": "indemnatnic", "warships": "vaporerebate", "dayofdragons": "zileledragonilor", "warthunder": "warthunder", "flightrising": "zborîn<PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "misterios", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "lumeatancuri<PERSON>", "crossout": "taicaiere", "agario": "agario", "secondlife": "viațadupămoarte", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "cavaleriionline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "legăturadeisa<PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "scumlifepodcast", "newworld": "lumeanouă", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "lumeawarcraft", "warcraft": "wow", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "resturilecreației", "riotmmo": "răzv<PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrackonline", "vindictus": "vindicativ", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragonsprophet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "îngeripeinternet", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikaveche", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "lumeaperfectă", "riseonline": "cresteonline", "corepunk": "corepunk", "adventurequestworlds": "aventuraplanetelor", "flyforfun": "zburapentruamuzament", "animaljam": "animaljam", "kingdomofloathing": "regat<PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "luptătordepestradă", "hollowknight": "cavernagolita", "metalgearsolid": "metalgearsolid", "forhonor": "pentruono<PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "lupt<PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "străzilefoaie", "mkdeadlyalliance": "mkaliantaursupermoarte", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "joc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "blasfemiator", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "razboiulmonstrilor", "jogosdeluta": "joc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "războinicifortificați", "finalfight": "luptafinală", "poweredgear": "echipamentputernic", "beatemup": "b<PERSON><PERSON>ep<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "luptevideo", "killerinstinct": "instinctulucigaș", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "fantomac<PERSON>e", "chivalry2": "cavaleri2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelulhollowknight", "hollowknightsilksong": "cânteculsutrăluihollowknight", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "știrisilksong", "silksong": "silksong", "undernight": "subnoapte", "typelumina": "<PERSON><PERSON><PERSON>ă", "evolutiontournament": "turneulevoluției", "evomoment": "evomoment", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "povestileberseriei", "bloodborne": "bloodborne", "horizon": "orizont", "pathofexile": "caminulexilului", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "sângeboorne", "uncharted": "neexplorat", "horizonzerodawn": "orizontzer<PERSON><PERSON><PERSON>", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON>ti<PERSON><PERSON><PERSON><PERSON>", "infamous": "infamă", "playstationbuddies": "prieteniiplaystation", "ps1": "ps1", "oddworld": "lumeaceaciudată", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "băiețiirăi", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "companierăliberă", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "z<PERSON>ldarama", "gris": "gris", "trove": "comoara", "detroitbecomehuman": "detroitdevineuman", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "pânalalumină", "touristtrophy": "trofeulturistului", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "echipadeciocniri", "fivepd": "cincipd", "tekken7": "tekken7", "devilmaycry": "diavolulplân<PERSON>e", "devilmaycry3": "diavolulpoateplânge3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "războinicisam<PERSON>", "psvr2": "psvr2", "thelastguardian": "ultimul<PERSON>ian", "soulblade": "sufletcuțit", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "inimileîntunecate2covenant", "pcsx2": "pcsx2", "lastguardian": "ultimulguardian", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "abonamentdejoacă", "armello": "armello", "partyanimal": "animaldepetrecere", "warharmmer40k": "warhamm45k", "fightnightchampion": "noaptealuptelorchampion", "psychonauts": "psihonauti", "mhw": "mhw", "princeofpersia": "princealpersiei", "theelderscrollsskyrim": "vârsteledescanatuluiskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxboc", "battlefront": "<PERSON>uld<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "nufoameîmpreună", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "încăutareastelelor", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "aproapeacasa", "americanmcgeesalice": "aliceamericande<PERSON><PERSON>gee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligaregatelor", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "televiziuneapragii", "skycotl": "cer<PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "mi<PERSON><PERSON>anefericire", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "boo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "explorăriexterioare", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "cultulovitelor", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "pove<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "oculto", "longdrive": "drumlung", "satisfactory": "<PERSON>is<PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviophile", "underearth": "subpământ", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "domeîntunecat", "pizzatower": "pizzatower", "indiegame": "juc<PERSON>rie<PERSON><PERSON>", "itchio": "itchio", "golfit": "golfeste", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "joc", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolină", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "provocare", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON>ared<PERSON><PERSON>", "yardgames": "joc<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "alegeunnumar", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "j<PERSON><PERSON><PERSON><PERSON>", "datinggames": "jocuri<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "jocgratuit", "drinkinggames": "jocuride<PERSON>ut", "sodoku": "sodoku", "juegos": "jocuri", "mahjong": "mahjong", "jeux": "jocuri", "simulationgames": "jocuri<PERSON><PERSON><PERSON><PERSON>", "wordgames": "jocuridecuvinte", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "jocuridecuvinte", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "jocuriplictisitoare", "oyun": "joc", "interactivegames": "jocuriinteractive", "amtgard": "amtgard", "staringcontests": "concurs<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "<PERSON><PERSON><PERSON><PERSON>", "giochi": "joa<PERSON>", "geoguessr": "geoguessr", "iphonegames": "jocuripeiphone", "boogames": "boogames", "cranegame": "j<PERSON>ul<PERSON><PERSON><PERSON>", "hideandseek": "văcăreșteșugărit", "hopscotch": "sărituraîncoadă", "arcadegames": "jocurarcade", "yakuzagames": "jocuriya<PERSON>za", "classicgame": "jocclasic", "mindgames": "jocurideminte", "guessthelyric": "g<PERSON>eștevers<PERSON>", "galagames": "galagames", "romancegame": "romancegame", "yanderegames": "jocuri<PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4x<PERSON><PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "jocuridearcade", "tabletopgames": "jocurideimensiune", "metroidvania": "metroidvania", "games90": "jocuri90", "idareyou": "teprovoc", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "jocuridecurse", "ets2": "ets2", "realvsfake": "realvsfals", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "jocurionline", "onlinegames": "jocurionline", "jogosonline": "jocurionline", "writtenroleplay": "r<PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "jocuridecoop", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON>", "highscore": "punctajmare", "jeuxderôles": "jocuriderol", "burgergames": "jocuriburger", "kidsgames": "joc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwversiuneaîntunecată", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "joa<PERSON>", "managementgame": "joc<PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "jocurideobiecteascunse", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "joculformula1", "citybuilder": "constructorurban", "drdriving": "sofatresponsabil", "juegosarcade": "jocuria<PERSON>de", "memorygames": "jocuridememorare", "vulkan": "vulkan", "actiongames": "jocurideacțiune", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "mașinidepinball", "oldgames": "j<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "couchcoop", "perguntados": "întreabămă", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "jocuriiimessage", "idlegames": "jocurinefolositoare", "fillintheblank": "completelacrimă", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "jocurilogice", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "jocuriexit", "5vs5": "5împotriva5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "joa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>țiura", "traditionalgames": "jocuritraditionale", "kniffel": "kniffel", "gamefps": "jocfps", "textbasedgames": "jocuripetext", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospelu", "thiefgame": "joculhotilor", "lawngames": "joc<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "fotbal<PERSON><PERSON><PERSON>", "tischfußball": "fotbaldemasă", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "jocuricasuale", "fléchettes": "darts", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "jocuride<PERSON><PERSON>", "cranegames": "jocuridecran", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "fotbalpebirou", "jogosorte": "jogosorte", "mage": "mage", "cargames": "jocurideautomobile", "onlineplay": "joa<PERSON><PERSON>", "mölkky": "molkky", "gamenights": "nopțidegaming", "pursebingos": "pursebingos", "randomizer": "randomizator", "msx": "msx", "anagrammi": "anagram<PERSON>", "gamespc": "jocuripc", "socialdeductiongames": "jocuridesocietate", "dominos": "dominos", "domino": "domino", "isometricgames": "jocuriizometrice", "goodoldgames": "jocuribunevechi", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "vânătoridecomori", "jeuxvirtuel": "jeuxvirtuale", "romhack": "romhack", "f2pgamer": "jucătorf2p", "free2play": "free2play", "fantasygame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "driftgame", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvser<PERSON>ș<PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "oricecearemotor", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "sabie<PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "juc<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "jocurilabzero", "grykomputerowe": "škăpcine", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "jeușideritm", "minaturegames": "jocuriminatură", "ridgeracertype4": "tipul4delaridgereacer", "selflovegaming": "iubiresinegaming", "gamemodding": "modificare<PERSON><PERSON><PERSON>", "crimegames": "jocuricrime", "dobbelspellen": "dobbelspellen", "spelletjes": "jocuri", "spacenerf": "spacenerf", "charades": "charadele", "singleplayer": "<PERSON>uc<PERSON><PERSON><PERSON><PERSON>", "coopgame": "joccooperativ", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrabble": "scrabble", "schach": "<PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "moștenireapandemiei", "camelup": "camelup", "monopolygame": "joculmonopol", "brettspiele": "jocurideboard", "bordspellen": "jocuridebord", "boardgame": "jocuride<PERSON><PERSON>", "sällskapspel": "jocuridecompanie", "planszowe": "jocurideboard", "risiko": "risiko", "permainanpapan": "jocuridepeisoară", "zombicide": "zombicide", "tabletop": "ma<PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "tune<PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "căutareaeroului", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "jocuridetablă", "dicegames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchisi", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "joc<PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "întâlnirecosmică", "creationludique": "creaț<PERSON>lu<PERSON>", "tabletoproleplay": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "jocuridecutie", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "joc<PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "infinitublocului", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "caderisiscari", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "juegodemasă", "planszówki": "boardgames", "rednecklife": "viatadeprovincial", "boardom": "boreală", "applestoapples": "<PERSON><PERSON><PERSON>", "jeudesociété": "jocdesocietate", "gameboard": "tablagame", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jocuridesocietate", "twilightimperium": "imperiultwilight", "horseopoly": "cavalopoly", "deckbuilding": "con<PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "man<PERSON>anelenebuniei", "gomoku": "gomoku", "giochidatavola": "jocuriatafietable", "shadowsofbrimstone": "umbrelordinului", "kingoftokyo": "regileluitokyo", "warcaby": "warcaby", "táblajátékok": "jocuridebord", "battleship": "navaderă<PERSON><PERSON><PERSON>", "tickettoride": "biletpentruplimbare", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "jocurideplatformă", "stolníhry": "jocuridemas<PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jocurisocietate", "gesellschaftsspiele": "jocuridesocietate", "starwarslegion": "legionulstarwars", "gochess": "joachess", "weiqi": "weiqi", "jeuxdesocietes": "jeuxdesocietate", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonaderăzboi", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identitatev", "theisle": "insula", "thelastofus": "ultimultinet<PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyșisirilecerneșete", "conanexiles": "conanexiles", "eft": "eft", "amongus": "printrenoi", "eco": "eco", "monkeyisland": "insulamonkey", "valheim": "valheim", "planetcrafter": "planetcrafters", "daysgone": "zileleaufugit", "fobia": "fobia", "witchit": "vrăjitoarele", "pathologic": "patologic", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "thelong<PERSON>", "ark": "ark", "grounded": "ancorat", "stateofdecay2": "stareadecadentă2", "vrising": "vrising", "madfather": "tatafurios", "dontstarve": "nuțifiefoame", "eternalreturn": "întoarcereetern<PERSON>", "pathoftitans": "caleatitânilor", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "cameraî<PERSON><PERSON><PERSON><PERSON>", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "povestiblocate", "thequarry": "cariera", "tlou": "tlou", "dyinglight": "lumipereajdyinglight", "thewalkingdeadgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "suntemfericițicâțiva", "riseofempires": "ascensiuneaimperiilor", "stateofsurvivalgame": "joc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "povestevintage", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "survivalvestului", "beastsofbermuda": "bestiiledinbermuda", "frostpunk": "frostpunk", "darkwood": "padureadark", "survivalhorror": "horror<PERSON>up<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "rezidentevil", "residentevil2": "rezidentevil2", "residentevil4": "rezidentevil4", "residentevil3": "rezidentulmalefic3", "voidtrain": "trenulgol", "lifeaftergame": "v<PERSON><PERSON>adedupajoc", "survivalgames": "jocuri<PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "aceastăruledesp<PERSON><PERSON>milor", "scpfoundation": "scpfoundation", "greenproject": "proiectverde", "kuon": "kuon", "cryoffear": "plângedefrică", "raft": "raft", "rdo": "rdo", "greenhell": "iadulverdep", "residentevil5": "residentevil5", "deadpoly": "mortpoligon", "residentevil8": "rezistindevil8", "onironauta": "onironaută", "granny": "bătrân<PERSON>", "littlenightmares2": "cosmiciidimensiuni2", "signalis": "signalis", "amandatheadventurer": "amandatexplorator", "sonsoftheforest": "fiiipă<PERSON>rii", "rustvideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "<PERSON>zi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "izolareal<PERSON>", "undawn": "<PERSON><PERSON><PERSON><PERSON>", "7day2die": "7zile2murim", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "<PERSON>pra<PERSON><PERSON><PERSON><PERSON>", "propnight": "propnight", "deadisland2": "insulamitralor2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "vers<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "cataclismzileîntunecate", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeafter": "viatadupă", "ageofdarkness": "epocaimunerii", "clocktower3": "turnuldecumul3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "dinastiameieval<PERSON>", "projectnimbusgame": "projectnimbusgame", "eternights": "eterneleالمناظر", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "dominațiamondială", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficiulucigașilor", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "ucigașuldepitici", "warhammer40kcrush": "cărăpășcăre40k", "wh40": "wh40", "warhammer40klove": "iubirewarhammer40k", "warhammer40klore": "warhammer40klegende", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindecare", "ilovesororitas": "ilovesororitas", "ilovevindicare": "iubescvindicarea", "iloveassasinorum": "ilovedesasinilor", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "of<PERSON>uleasa<PERSON><PERSON>r", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "epocamempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerep<PERSON>asi<PERSON><PERSON>", "civilizationv": "civilizațiev", "ittakestwo": "îitrebuiescdouă", "wingspan": "wingspan", "terraformingmars": "terraformareamarte", "heroesofmightandmagic": "eroiiputeriișimagiei", "btd6": "btd6", "supremecommander": "comandantulsuprem", "ageofmythology": "epocamitologiei", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "avanpost2", "banished": "exilat", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilizația6", "warcraft2": "warcraft2", "commandandconquer": "comandăşiînvinge", "warcraft3": "warcraft3", "eternalwar": "războiureltern", "strategygames": "jocuridestrategie", "anno2070": "anno2070", "civilizationgame": "joculcivilizației", "civilization4": "civilizație4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "războitotal", "travian": "travian", "forts": "forturi", "goodcompany": "companiebună", "civ": "civ", "homeworld": "lumeacasa", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "mairepedeadecâtlumina", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "strategierealaîntimpreal", "starctaft": "starctaft", "sidmeierscivilization": "civilizațiileluisidmeier", "kingdomtwocrowns": "regatul2coroane", "eu4": "eu4", "vainglory": "m<PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "divinitate", "anno": "anul", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "clasaaladavecualgebră", "plagueinc": "plagueinc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilizația3", "4inarow": "4peșinoue", "crusaderkings3": "crusaderkings3", "heroes3": "eroi3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "epocadeimperii2", "disciples2": "ucenicii2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "giochidistrategia", "stratejioyunları": "strate<PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozaurulețul", "worldconquest": "conquistamondial", "heartsofiron4": "inimioaredeferon4", "companyofheroes": "companiadeeroi", "battleforwesnoth": "bataliaforwesnoth", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gâscașarpești", "phobies": "fobii", "phobiesgame": "joculețulpho<PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "peședinte", "bomberman": "bomberman", "ageofempires4": "eraempires4", "civilization5": "civilizația5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "meșteșugulvrăjitoriei", "starwarsempireatwar": "imperiulinrazboistarwars", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "mailfuldepop", "shiningforce": "puterealuminată", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "programuldysonsphere", "transporttycoon": "transporturipatron", "unrailed": "neoprit", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "împărățiileupland", "galaxylife": "viațagalactică", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "bătătorindvârful", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "nevoiedepestevite", "needforspeedcarbon": "nevoiedepunctajcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "pierdems4", "fnaf": "fnaf", "outlast": "supravie<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "revin<PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologiacălărețuluiîncrezător", "phasmophobia": "p<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "cincanoaptesilfreddy", "saiko": "saiko", "fatalframe": "cadrufatal", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "morțiiînviază", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "blocatacasă", "deadisland": "insula_mortilor", "litlemissfortune": "mici<PERSON><PERSON>", "projectzero": "proiectzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "salutvecin", "helloneighbor2": "salutvecinul2", "gamingdbd": "gamingdbd", "thecatlady": "doamnapisica", "jeuxhorreur": "joc<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "gamingdegroază", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartede<PERSON>ii", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "numelecodurilor", "dixit": "dixit", "bicyclecards": "bicyclecards", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendadelaruneterra", "solitaire": "solitaire", "poker": "poker", "hearthstone": "pear<PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "ch<PERSON><PERSON><PERSON>", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "cart<PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartestradable", "pokemoncards": "cart<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "carneșisângetcg", "sportscards": "carduridesport", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "pahare", "warcry": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "regeleinimilor", "truco": "trucoboo", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "rezistența", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "cartiyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "magicianulîntunecoase", "blueeyeswhitedragon": "dragondealbaochelblue", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjude<PERSON><PERSON><PERSON>", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "povesteabatalionului", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "facecard": "fațacard", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "constructorideckuri", "marvelchampions": "marve<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "magiccartas", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicorniinstabili", "cyberse": "ciberspatiu", "classicarcadegames": "jocuriclasicearcade", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "vinerinoaptefunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "proiectdiva", "djmax": "djmax", "guitarhero": "erougu<PERSON><PERSON>", "clonehero": "clonehero", "justdance": "dansdesimplu", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "danscentral", "rhythmgamer": "jucătorderitm", "stepmania": "stepmania", "highscorerythmgames": "jocurihighscorelyd<PERSON>s", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "danspefocșigheață", "auditiononline": "auditionline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "jocuridepită", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "cubing", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "jocuridepuzzle", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "puzzlelogice", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "cubulrubik", "crossword": "cuvin<PERSON>î<PERSON><PERSON><PERSON>ș<PERSON>", "motscroisés": "cuvin<PERSON>î<PERSON><PERSON><PERSON>ș<PERSON>", "krzyżówki": "cuvinteleîncru<PERSON>ș<PERSON>", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON>", "indovinello": "ghicitoarea", "riddle": "enigmă", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "puzzle", "tekateki": "tekateki", "inside": "înăuntru", "angrybirds": "puiiiri<PERSON>ți", "escapesimulator": "escapegame", "minesweeper": "minesweeper", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "croswordpuzzle", "kurushi": "k<PERSON>hi", "gardenscapesgame": "joculgardenscapes", "puzzlesport": "puzzlesport", "escaperoomgames": "jocurideescaperoom", "escapegame": "escapegame", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgame", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "kulaworld", "myst": "misterios", "riddletales": "povestiriddles", "fishdom": "fishdom", "theimpossiblequiz": "quizulimposibil", "candycrush": "candycrush", "littlebigplanet": "miculplanetagrande", "match3puzzle": "puzzle3match", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "ciudățel", "rubikcube": "cubulrubik", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "princi<PERSON><PERSON><PERSON>", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "cubert", "riddleme": "întreabămă", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "cruciver<PERSON>", "ciphers": "cifre", "rätselwörter": "cuvintepești", "buscaminas": "buscaminas", "puzzlesolving": "rezolvarepuzzle", "turnipboy": "b<PERSON>ia<PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "ni<PERSON>i", "guessing": "g<PERSON><PERSON>", "nonograms": "nonograme", "kostkirubika": "kostkirubika", "crypticcrosswords": "crosworduricripte", "syberia2": "syberia2", "puzzlehunt": "v<PERSON><PERSON><PERSON>aredepuzzle", "puzzlehunts": "vânătoridepuzzle", "catcrime": "delictdeclanță", "quebracabeça": "puzzlebooster", "hlavolamy": "boorăzboideminte", "poptropica": "poptropica", "thelastcampfire": "ultimulfocdecampare", "autodefinidos": "autodefiniți", "picopark": "picopark", "wandersong": "cântărețuldeştept", "carto": "carteo", "untitledgoosegame": "joculgâscanefă<PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "<PERSON><PERSON><PERSON>", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "piese", "portalgame": "jocportal", "bilmece": "bilmecile", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "cubulrubik", "indovinelli": "îndo<PERSON><PERSON><PERSON>", "cubomagico": "cubomagic", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "taramulturburat", "monopoly": "monopol", "futurefight": "luptăpent<PERSON>â<PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "lupsingur", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "stelele<PERSON><PERSON>", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "regatulbiscuitilor", "alchemystars": "alchemystars", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "scenacolorata", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fategrandorder": "destinulordonului", "hyperfront": "hyperfront", "knightrun": "cogutaaventura", "fireemblemheroes": "eroiidefoc", "honkaiimpact": "honkaiimpact", "soccerbattle": "bat<PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "jocuripentrumobil", "kingschoice": "alegerearegelui", "guardiantales": "povestilepazitorului", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "tacticool", "cookierun": "<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON>", "craftsman": "meșteșugar", "supersus": "supersus", "slowdrive": "conduslento", "headsup": "fiipefaza", "wordfeud": "wordfeud", "bedwars": "războiulatedeținut", "freefire": "firegratuit", "mobilegaming": "gamingmobil", "lilysgarden": "grad<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "strategiideech<PERSON><PERSON>", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "bucurie", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tremurășifugează", "ml": "ml", "bangdream": "bangdream", "clashofclan": "ciocnirea<PERSON>lan<PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "timprprincess", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegenda", "hanabi": "hanabi", "disneymirrorverse": "universuldisney", "pocketlove": "dragosteînbu<PERSON>nar", "androidgames": "jocuriandroid", "criminalcase": "cazcriminal", "summonerswar": "summonerswar", "cookingmadness": "nebunieintracucina", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaîngerilor", "lordsmobile": "lordsmobile", "tinybirdgarden": "grădinicuțacabutăi", "gachalife": "gachalife", "neuralcloud": "norneural", "mysingingmonsters": "monstrulețelemele", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "oglinaversului", "pou": "pou", "warwings": "a<PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "intrare", "slugitout": "scoatea<PERSON><PERSON>le", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "razbunătorulgrayraven", "petpals": "prietenipet", "gameofsultans": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "jocurilacircuit", "juegodemovil": "juegodemovil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimică", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "<PERSON><PERSON><PERSON><PERSON>", "bombmebrasil": "bombmeregelia", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "chemareadragonilor", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowfight3": "shadowfight3", "limbuscompany": "companialimbus", "demolitionderby3": "demolitionsuvor3", "wordswithfriends2": "cuvinteleftreifriends2", "soulknight": "cavalerulsufletului", "purrfecttale": "purrfecttale", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "or<PERSON><PERSON><PERSON><PERSON>ules<PERSON>", "perfectworldmobile": "lumeaperfectămobil", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "puzzlebucuriilor", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "bunny", "littlenightmare": "co<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mur<PERSON><PERSON><PERSON>", "tearsofthemis": "lacrimile<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendshakeshake", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalamobilă", "streetfighterduel": "luptătoridepeulță", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "frontlineleorului", "jurassicworldalive": "jurassicworldînviață", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "trecustopeste", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "poveste<PERSON><PERSON>na", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jocurimobile", "legendofneverland": "legend<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "jocmobillegends", "timeraiders": "vânătoridezile", "gamingmobile": "gamingmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "aventur<PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "lumeaîntunericului", "travellerttrpg": "călătorttrpg", "2300ad": "2300dad", "larp": "larping", "romanceclub": "clubderomantici", "d20": "d20", "pokemongames": "joc<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipiip", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "echiparoșie", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "echipaimistic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "poke<PERSON><PERSON><PERSON>u", "mesprit": "mesprit", "pokémoni": "pokeboo", "ironhands": "m<PERSON><PERSON>_<PERSON>_<PERSON>er", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "poked<PERSON><PERSON>", "kidsandpokemon": "copișicompokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "vânătorulstrălucitor", "ajedrez": "<PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON>", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "feted<PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "b<PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudecarte", "japanesechess": "șahjaponez", "chinesechess": "șahcinezesc", "chesscanada": "șahcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "pion", "chesscom": "chesscom", "calabozosydragones": "calabo<PERSON><PERSON>dra<PERSON><PERSON>", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventură", "darksun": "soareleîntunecat", "thelegendofvoxmachina": "legendavoxmachina", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON>", "darkmoor": "darkmoor", "minecraftchampionship": "campionatulminecraft", "minecrafthive": "mincraftbuză<PERSON>", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "moduriminecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "adons", "mcpeaddons": "addonurimcpe", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodificat", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "orasulminecraft", "pcgamer": "gamercupc", "jeuxvideo": "jocuri<PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "gamers", "levelup": "îmbunătățeștete", "gamermobile": "gamermobile", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "gamingpepc", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "jocuripepc", "casualgaming": "gamingrelaxat", "gamingsetup": "<PERSON><PERSON><PERSON>", "pcmasterrace": "masterulpc", "pcgame": "jocpc", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "gaminginvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerro", "gameplays": "gameplays", "consoleplayer": "jucă<PERSON><PERSON>consolă", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "gamingonline", "semigamer": "semigamer", "gamergirls": "fete<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "mamegamer", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gamers", "grypc": "grypc", "rangugamer": "rangudivertisment", "gamerschicas": "gamersfete", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "echipatotalegăm", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "misi<PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "gamervechi", "cozygaming": "gamingconfortabil", "gamelpay": "gamelpay", "juegosdepc": "jocuridepc", "dsswitch": "schi<PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "gamingcompetitiv", "minecraftnewjersey": "minecraftnewjersey", "faker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pc4gamers": "pcpentrugamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "gamingheterosexual", "gamepc": "jocpc", "girlsgamer": "gameritlor", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "gamerițelor", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "superior", "socialgamer": "jucatorsocial", "gamejam": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "jucătorprofesionist", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "echipamea", "republicofgamers": "republicadegamers", "aorus": "aorus", "cougargaming": "cugargaming", "triplelegend": "legendă<PERSON><PERSON><PERSON>", "gamerbuddies": "prieteniigaming", "butuhcewekgamers": "darafemeiigameri", "christiangamer": "gamercreștin", "gamernerd": "gamerpro", "nerdgamer": "nerdgamer", "afk": "departe", "andregamer": "andregamer", "casualgamer": "gamerdevariantă", "89squad": "89echipa", "inicaramainnyagimana": "inițiarămâinnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "jocurideprivit", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "gamerdevideo", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "gamerdinplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamers<PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "femeiegamer", "obviouslyimagamer": "evidentcӑsuntjucӑtor", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonic<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON>", "switch": "schimb<PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majormask", "mariokartmaster": "ma<PERSON>kar<PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "avocatulace", "ssbm": "ssbm", "skychildrenofthelight": "copiideasprafiintei", "tomodachilife": "tomodachilife", "ahatintime": "ahatintimp", "tearsofthekingdom": "lacrimileregelui", "walkingsimulators": "simulatoaredeflăcat", "nintendogames": "jocuridenintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "cautareadragonului", "harvestmoon": "lunar<PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "fabricaderune", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "respira<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "povesti", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "triunghiulstrategiei", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "bunicicreaunepăraie", "nintendos": "nintendos", "new3ds": "nou3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "războiniciihyrule", "mariopartysuperstars": "mariopartygeneratia", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "canidemașcați", "vanillalol": "vanillalol", "wildriftph": "wild<PERSON>ro", "lolph": "lolph", "leagueoflegend": "ligalegendelor", "tốcchiến": "toccianță", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adporti", "lolzinho": "lo<PERSON><PERSON>o", "leagueoflegendsespaña": "leagueoflegendsromânia", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "legăturăculegendile", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "videopurretro", "scaryvideogames": "jocurifiindemult", "videogamemaker": "creatordevideogame", "megamanzero": "megamanzero", "videogame": "videoclipuri", "videosgame": "jocurivideo", "professorlayton": "pro<PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battlespaceluiboo", "arcades": "<PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulatordefermă", "robloxchile": "robloxromania", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxromânia", "erlc": "erlc", "sanboxgames": "jocurisanbox", "videogamelore": "lorejocurivideo", "rollerdrome": "rollerdrome", "parasiteeve": "paraziteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "<PERSON>t<PERSON><PERSON>", "deadspace": "s<PERSON><PERSON><PERSON><PERSON>cereb<PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "videogame", "theoldrepublic": "republicaveche", "videospiele": "jocuridevideo", "touhouproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamcast": "visează", "adventuregames": "jocurideaventura", "wolfenstein": "wolfenstein", "actionadventure": "aventurăînacțiune", "storyofseasons": "povestidesezoane", "retrogames": "joc<PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retroarcade", "vintagecomputing": "calcula<PERSON><PERSON><PERSON><PERSON>", "retrogaming": "retrogaming", "vintagegaming": "gamingvintage", "playdate": "<PERSON>nt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "comandantulînțelept", "bugsnax": "bugsnax", "injustice2": "nedreptate2", "shadowthehedgehog": "umbraariciului", "rayman": "rayman", "skygame": "joc<PERSON><PERSON><PERSON>", "zenlife": "stildeviatazen", "beatmaniaiidx": "beatmaniaiidx", "steep": "cantonat", "mystgames": "mystgames", "blockchaingaming": "gamingpeblockchain", "medievil": "medieval", "consolegaming": "gamingpeconsola", "konsolen": "konsolen", "outrun": "fuge", "bloomingpanic": "panicalblooming", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horroregaming", "monstergirlquest": "monstergirlquest", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simularifermă", "juegosviejos": "j<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ficțiuneinteractivă", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "ultimuldintrnoi2", "amantesamentes": "amantesamentes", "visualnovel": "romanvisual", "visualnovels": "romanevizuale", "rgg": "rgg", "shadowolf": "shadowulf", "tcrghost": "fantomătcr", "payday": "ziuadeplat<PERSON>", "chatherine": "catherine", "twilightprincess": "prințesăntunecată", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "jocuristilizatice", "novelavisual": "noveleavizual", "thecrew2": "echipacastiga2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revoluțiacompresoarelor", "wiiu": "wiiu", "leveldesign": "designnivel", "starrail": "starrail", "keyblade": "sabiecheie", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafuneori", "novelasvisuales": "novelavisual", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "jocuridevideo", "videogamedates": "întâln<PERSON>î<PERSON><PERSON><PERSON>", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "revenireajustiției", "gamstergaming": "gamstergaming", "dayofthetantacle": "ziatentacolelor", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "cursaîngroapă", "3dplatformers": "platformere3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON>dinvreme", "hellblade": "hellblade", "storygames": "jocuridepoveste", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "dincolodedouăsuflete", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "<PERSON><PERSON><PERSON><PERSON>", "tinybunny": "iepurașmici", "retroarch": "retroarch", "powerup": "încărcareenergie", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurischizate", "quickflash": "flashrapid", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcadeuri", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "insulacoralilor", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "o<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "fuziuneafotbalului", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomanel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalînvârtit", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "munteledetestere", "simulator": "simulator", "symulatory": "simulaturi", "speedrunner": "vite<PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "jocdeporeclit<PERSON>", "wonderlandonline": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uniloronline", "skylander": "skylander", "boyfrienddungeon": "caverndupreț<PERSON><PERSON><PERSON>", "toontownrewritten": "toontownres<PERSON>ris", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "haosurban", "heavenlybodies": "corpuriheavenly", "seum": "seum", "partyvideogames": "jocuridepetrecere", "graveyardkeeper": "îngrijitordecimitir", "spaceflightsimulator": "simulatorzborspatial", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "mâncareșijocuridevideo", "oyunvideoları": "videoteboo", "thewolfamongus": "lupuldintrenoi", "truckingsimulator": "simulatordecamioane", "horizonworlds": "lumeahorizonturilor", "handygame": "jocpractic", "leyendasyvideojuegos": "poveștivideogame", "oldschoolvideogames": "jocurivideovintage", "racingsimulator": "simulatorderacing", "beemov": "bee<PERSON>v", "agentsofmayhem": "agen<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "cântecpop", "famitsu": "famitsu", "gatesofolympus": "porțileolimpului", "monsterhunternow": "vânătoaremonsteracum", "rebelstar": "rebelstar", "indievideogaming": "indievideogaming", "indiegaming": "juc<PERSON><PERSON>iindi<PERSON>", "indievideogames": "jocuri<PERSON>e", "indievideogame": "indiegames", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "fort<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "proiectl", "futureclubgames": "jocurileclubuluiviitorului", "mugman": "băiatulcucana", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "științaaperturii", "backlog": "<PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "joc<PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "personajedejocuriviideo", "achievementhunter": "vânătorideperformanțe", "cityskylines": "liniedeskyline", "supermonkeyball": "supermaimuțăbila", "deponia": "deponia", "naughtydog": "câinilopatici", "beastlord": "bestiașul", "juegosretro": "jocuriretrogaming", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "orișipădureaorbească", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervatoriodedopamin", "staxel": "staxel", "videogameost": "ostdevideogame", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animefericit", "darkerthanblack": "darkerdecâtnegru", "animescaling": "scalareaanimeurilor", "animewithplot": "animecuintrigă", "pesci": "pesci", "retroanime": "retroanime", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "anime90s", "darklord": "domnulîntunericului", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "maestrucool", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON>i", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "robotdecablu", "animecover": "animecover", "thevisionofescaflowne": "viziuneaescaflowne", "slayers": "<PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90ani", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "peștelebanană", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "forțadefoc", "moriartythepatriot": "moriartypatriotul", "futurediary": "jurnalulviitorului", "fairytail": "povestedebasm", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "m<PERSON><PERSON><PERSON><PERSON>", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirenaecoului", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "maiddragon", "blacklagoon": "lagunanegru", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformar", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "unindexmagical", "sao": "sao", "blackclover": "trifoiulnegru", "tokyoghoul": "tokyoghoul", "onepunchman": "unlovitură", "hetalia": "hetalia", "kagerouproject": "proiectulkagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8infinitate", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamilie", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioritatewonderegg", "angelsofdeath": "îngeriideadevărsare", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeindisporturi", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaofanyatereauaevil", "shounenanime": "animebă<PERSON><PERSON>i", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "frum<PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "băiatulșibestia", "fistofthenorthstar": "pumnulsteleinordului", "mazinger": "mazinger", "blackbuttler": "neg<PERSON><PERSON><PERSON>", "towerofgod": "turnuldivinității", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "luna<PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "cutesiunusual", "martialpeak": "vârfuldearteunificat", "bakihanma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "băiatul<PERSON>cei", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amici", "sailorsaturn": "marinilasaturn", "dio": "dio", "sailorpluto": "marinploo", "aloy": "aloy", "runa": "runa", "oldanime": "animevechi", "chainsawman": "bărbatulpânzei", "bungoustraydogs": "câinideuiz<PERSON>", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "butlerulnegru", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animehorror", "fruitsbasket": "fruitsbasket", "devilmancrybaby": "diavolulpleacăbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON>v<PERSON><PERSON>", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "drag<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "astaeserafinfricosator", "thepromisedneverland": "țarăapromisă", "monstermanga": "monstermanga", "yourlieinapril": "minciunavutainaprilie", "buggytheclown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "seraphealauneiend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "priz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "ţarzanulmort", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "j<PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "inimilepandorei", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "războiuldelaalimente", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "liniadiavolului", "toyoureternity": "pentrutinever", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "perioadablues", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "alianțasecret<PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "ucig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detectivulconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "cavaleriidevampiri", "mugi": "mugi", "blueexorcist": "exor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "familiespion", "airgear": "aergear", "magicalgirl": "fatamagica", "thesevendeadlysins": "celeșaptepăcatemorti", "prisonschool": "școalam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "dumnezeulșcoliideliceu", "kissxsis": "<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandbleu", "mydressupdarling": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "prim<PERSON>as", "undeadunluck": "neînsuflețităneșansă", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonucisitorpeepsword", "bloodlad": "s<PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "adio<PERSON>", "firepunch": "lovituralfoc", "adioseri": "adioserii", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stelelesealiniat", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saoîntregfactor", "cherrymagic": "magiacherries", "housekinokuni": "caselekino", "recordragnarok": "înregistreazăragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "lice<PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuuman", "deathparade": "paradadecemortii", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponez", "animespace": "animespace", "girlsundpanzer": "feteundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedublat", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeindie", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "omulețul_deștepților", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "fatapiersica", "cavalieridellozodiaco": "cavaleriidelazodie", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "inimademanga", "deliciousindungeon": "delicios<PERSON>ung<PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON>rag<PERSON><PERSON>", "funamusea": "distracțiemu<PERSON>al<PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "bochithecrai", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "sarălapatul", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "supraalimentat", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravefață", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierpălărievrăjitoare", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "pietricica", "mangaislife": "mangaestviata", "dropsofgod": "p<PERSON><PERSON><PERSON>idedumnezeu", "loscaballerosdelzodia": "cavalerii_zodiacului", "animeshojo": "animeshojo", "reverseharem": "haremreversat", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "mareleducatoroni<PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldat0", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "echipamentul5", "grandbluedreaming": "visegrandbleu", "bloodplus": "sângeplus", "bloodplusanime": "sângeplusanime", "bloodcanime": "s<PERSON><PERSON><PERSON><PERSON>", "bloodc": "sângec", "talesofdemonsandgods": "poveștioaredespredemoniișidumnezei", "goreanime": "goreanime", "animegirls": "feteanime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "ciorixtotalfake", "splatteranime": "animepicat", "splatter": "stropi", "risingoftheshieldhero": "războinicdescut", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedatumnezei", "animeyuri": "animeyuri", "animeespaña": "animespania", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "cop<PERSON>awhale<PERSON>", "liarliar": "mincinosmincinos", "supercampeones": "supercampioni", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiaveamtelefon", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "fetelemagice", "callofthenight": "apelulnopții", "bakuganbrawler": "brawlerdebakugan", "bakuganbrawlers": "brawleribakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "grădinaumbră", "tsubasachronicle": "tsubasachronicle", "findermanga": "gasestemanga", "princessjellyfish": "princesajellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revueștiarlight", "animeverse": "animevers", "persocoms": "persocoms", "omniscientreadersview": "viziunilecititoriloromniști", "animecat": "anibiscuit", "animerecommendations": "recomand<PERSON><PERSON><PERSON>", "openinganime": "deschidereanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mec", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "decolorant", "deathnote": "notepentru<PERSON>idere", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventurafantastică<PERSON><PERSON><PERSON>jo", "fullmetalalchemist": "alchimistulcompletmetal", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "aventurilebizarrejobo", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitar", "greenranger": "rangerulverde", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeoraș", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonaventura", "hxh": "hxh", "highschooldxd": "liceuldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "ucigașidemoni", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "atacațititanul", "erenyeager": "eagerbubească", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "joculprietenilor", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "echipadechestionare", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "unulpeciulesteadevărat", "revengers": "răzbunătorii", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efectuljoyboy", "digimonstory": "povest<PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "superjail", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonperfect", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bubub<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "vrăjitorifugar<PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "reîntâlnirilevieții"}