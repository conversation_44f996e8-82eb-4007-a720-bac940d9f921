{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologie", "cognitivefunctions": "kognitivnífunkce", "psychology": "psychologie", "philosophy": "filozofie", "history": "historie", "physics": "fyzika", "science": "věda", "culture": "kultura", "languages": "jazyky", "technology": "technologie", "memes": "memy", "mbtimemes": "mbtime<PERSON>", "astrologymemes": "astrologieme<PERSON>", "enneagrammemes": "enneagrammemy", "showerthoughts": "myšlenkyzesprchy", "funny": "zábavné", "videos": "videa", "gadgets": "gadgety", "politics": "politika", "relationshipadvice": "vztahovérady", "lifeadvice": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "krypto", "news": "<PERSON>vinky", "worldnews": "<PERSON><PERSON><PERSON>zes<PERSON><PERSON><PERSON>", "archaeology": "archeologie", "learning": "učení", "debates": "diskuse", "conspiracytheories": "konspiračníteorie", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "meditace", "mythology": "mytologie", "art": "umění", "crafts": "vyrábění", "dance": "tanec", "design": "design", "makeup": "makeup", "beauty": "krása", "fashion": "m<PERSON><PERSON>", "singing": "zpěv", "writing": "psaní", "photography": "fotografování", "cosplay": "cosplay", "painting": "malování", "drawing": "kreslení", "books": "knihy", "movies": "filmy", "poetry": "poezie", "television": "televize", "filmmaking": "filmařina", "animation": "animace", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumenty", "mystery": "tajemno", "comedy": "komedie", "crime": "krimi", "drama": "diva<PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romantika", "realitytv": "realityshow", "action": "akční", "music": "hudba", "blues": "blues", "classical": "vážnáhudba", "country": "country", "desi": "indická", "edm": "edm", "electronic": "elektronick<PERSON>", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "nezávislá", "jazz": "jazz", "kpop": "kpop", "latin": "latinskoamerická", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "cestování", "concerts": "koncerty", "festivals": "festivaly", "museums": "muzea", "standup": "standup", "theater": "diva<PERSON><PERSON>", "outdoors": "outdoor", "gardening": "prá<PERSON>nazahradě", "partying": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON>", "dungeonsanddragons": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "šachy", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pok<PERSON><PERSON>", "food": "<PERSON><PERSON><PERSON><PERSON>", "baking": "pečení", "cooking": "vaření", "vegetarian": "vegetarián", "vegan": "vegan", "birds": "p<PERSON><PERSON><PERSON>", "cats": "k<PERSON>č<PERSON>", "dogs": "psi", "fish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animals": "zvířata", "blacklivesmatter": "blacklivesmatter", "environmentalism": "environmentalismus", "feminism": "feminismus", "humanrights": "lidskápráva", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "dobrovolnictví", "sports": "sporty", "badminton": "badminton", "baseball": "baseball", "basketball": "basketbal", "boxing": "box", "cricket": "kriket", "cycling": "cyklist<PERSON>", "fitness": "fitness", "football": "fotbal", "golf": "golf", "gym": "posilovna", "gymnastics": "gymnast<PERSON>", "hockey": "hokej", "martialarts": "bojováumění", "netball": "netball", "pilates": "<PERSON><PERSON><PERSON><PERSON>", "pingpong": "pingpong", "running": "b<PERSON>h", "skateboarding": "skateboarding", "skiing": "lyžování", "snowboarding": "snowboarding", "surfing": "surfing", "swimming": "plavání", "tennis": "tenis", "volleyball": "volejbal", "weightlifting": "posilování", "yoga": "jóga", "scubadiving": "potápění", "hiking": "turistika", "capricorn": "kozoroh", "aquarius": "vod<PERSON><PERSON>", "pisces": "ryby", "aries": "beran", "taurus": "b<PERSON>k", "gemini": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancer": "rak", "leo": "lev", "virgo": "panna", "libra": "v<PERSON><PERSON>", "scorpio": "štír", "sagittarius": "střelec", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "nezavazne", "longtermrelationship": "dlou<PERSON>dobýv<PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON><PERSON>", "polyamory": "polyamorie", "enm": "etickánemonogamie", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON>", "bisexual": "bisexuální", "pansexual": "pansexuální", "asexual": "asexuální", "reddeadredemption2": "reddeadredemption2", "dragonage": "dračívěk", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON>lí<PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "královskávýprava", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverze", "legendofspyro": "legendazspyra", "rouguelikes": "roguelike", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dračíhoučedníka", "sunsetoverdrive": "západslunceoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "ohniváznameníosudu", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildovnív<PERSON>lk<PERSON>", "openworld": "otevř<PERSON>ý<PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "hrdinovébouře", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "kmenystředníhoráje", "planescape": "plánovacest", "lordsoftherealm2": "lordovéříše2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "barevnáčtverec", "medabots": "medabots", "lodsoftherealm2": "lodězreálum2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivnísimulace", "okage": "okage", "juegoderol": "hrajrol", "witcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dishonored": "<PERSON>poct<PERSON><PERSON>", "eldenring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "zaklínač3", "fallout": "fallout", "fallout3": "pád3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modding": "modding", "charactercreation": "vytvářenípostav", "immersive": "pohlcující", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidnímotivace", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "srdcařprolásku", "otomegames": "<PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "upířímaskarada", "dimension20": "dimension20", "gaslands": "plynovézemě", "pathfinder": "hledačcesty", "pathfinder2ndedition": "cestovatelé2edice", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "krvavynacase", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitacnípohroma", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "pán", "yourturntodie": "tvářadaum<PERSON><PERSON>t", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "elderscrollsonline", "reka": "řeka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "j<PERSON>", "falloutshelter": "falloutshelter", "gurps": "gurps", "darkestdungeon": "nejtemnějšízkdungeon", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "vnějšítsvěty", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastickébojovníky", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "nočníměsto", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "šílenstvívboji", "jaggedalliance2": "zubatáaliance2", "neverwinter": "neverwinter", "road96": "silnice96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamš<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "zapomenuté<PERSON><PERSON>še", "dragonlance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "arena<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "d<PERSON><PERSON>ěsvětla", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "rozpadlétr<PERSON>ny", "horizonforbiddenwest": "horizontzakazanýzápad", "twewy": "twewy", "shadowpunk": "stínovápunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "posledníepochu", "starfinder": "hledačhvězd", "goldensun": "zlatéslunce", "divinityoriginalsin": "božstvípůvodníhřích", "bladesinthedark": "čepelivtemnotě", "twilight2000": "soumrak2000", "sandevistan": "sandevistan", "cyberpunk": "kyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "kyberpunkčervený", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlézemě", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "ďáblovapřeživší", "oldschoolrunescape": "staráškolarunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božství", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "staréčasyblues", "adventurequest": "dobrodr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "stolníhry", "roleplayinggames": "hryn<PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sluníčkovádomovina", "talesofsymphonia": "pohádkyosymfonii", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "dagařskápohádka", "torncity": "torncity", "myfarog": "mo<PERSON><PERSON>na", "sacredunderworld": "sacrednípodsvětí", "chainedechoes": "spojenéozvěny", "darksoul": "temnésoul", "soulslikes": "soulslike", "othercide": "othercide", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "kronoshifter", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "trhl<PERSON>y", "tibia": "tibia", "thedivision": "divize", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "cestovatelvětvičkou", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "vlkodlaciapokalypsy", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorověsrdce", "fable3": "fable3", "fablethelostchapter": "bajkalostkapitola", "hiveswap": "hiveswap", "rollenspiel": "rolovani", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "věčnosteden", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "hvězdnápole", "oldschoolrevival": "oldschoolrevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworldy", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "královstvísrdce1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "temn<PERSON><PERSON><PERSON>", "juegosrpg": "rpghry", "kingdomhearts": "královstvísrdcí", "kingdomheart3": "královstvísrdcí3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "sklizeň", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "nebearcadie", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "dechohně4", "mother3": "matka3", "cyberpunk2020": "kyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "hrovánísituační", "roleplaygame": "hranarole", "fabulaultima": "fabulault<PERSON>", "witchsheart": "srdcečarodějky", "harrypottergame": "ha<PERSON><PERSON><PERSON>hrají<PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "cestovatel2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "kouzelnáplavba", "dragonageorigins": "drakonskáéra_původů", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "lovecísařství", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstrov<PERSON>lov<PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "stínovásrdcepakt", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "královstvípřichází", "awplanet": "awplanet", "theworldendswithyou": "světotvůjkončí", "dragalialost": "dragalialost", "elderscroll": "staríš<PERSON>roll", "dyinglight2": "umírajícísvětlo2", "finalfantasytactics": "finalfantasystrategie", "grandia": "grandia", "darkheresy": "temnásetba", "shoptitans": "shoptitans", "forumrpg": "fórumrpg", "golarion": "golarion", "earthmagic": "kouzlozemě", "blackbook": "černáknížka", "skychildrenoflight": "nebesk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "svatázlatáedice", "castlecrashers": "<PERSON>rad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gothicgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "hernirpg", "prophunt": "prophunt", "starrails": "hvězdnékoleje", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "pointandclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7navždykrize", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postkyberpunk", "deathroadtocanada": "cestadokan<PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "rytí<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>ů", "fireemblem": "ohnivapevnost", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacie", "persona5": "persona5", "ghostoftsushima": "duchtsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "doťáhkni", "ys": "ys", "souleater": "požírač<PERSON>ší", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "taktickýrpg", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ů", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "princeznakonekt", "hexenzirkel": "hexenzirkel", "cristales": "krystaly", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligasnů", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "overwatchleague", "cybersport": "kybersport", "crazyraccoon": "š<PERSON>lenýpanda", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "ezávodění", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkonkurenceschopný", "t3arena": "t3arena", "valorantbr": "valorantcz", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "p<PERSON><PERSON>ž<PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portál", "teamfortress2": "týmovápevnost2", "everlastingsummer": "navě<PERSON><PERSON><PERSON>", "goatsimulator": "kozasim", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "svobodnýplanet", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "nocvlese", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "rizikopadůdeště2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "interplanetární", "helltaker": "pekelnýřečník", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "mrtvébuňky", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "trpasličípevnost", "foxhole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "tulis", "battlefield": "bojiště", "battlefield1": "bojiště1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oka", "blackdesert": "černápoušť", "tabletopsimulator": "stolníhrasimulator", "partyhard": "pařttvrdě", "hardspaceshipbreaker": "tvrdýprostorovýrozcestník", "hades": "<PERSON><PERSON><PERSON><PERSON>", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "uvězněn_s_jestrem", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON>v<PERSON>sv<PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonie", "noita": "noita", "dawnofwar": "začátekválky", "minionmasters": "minionmasters", "grimdawn": "temnýsvítání", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "duchovnít<PERSON><PERSON><PERSON><PERSON>", "datingsims": "rand<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "únikzkovu", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nové<PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconheavy", "kenopsia": "kenopsie", "virtualkenopsia": "virtuálníkenopsie", "snowrunner": "sněžnýběžec", "libraryofruina": "<PERSON>ihov<PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "naslocestovatel", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "poklidnáp<PERSON><PERSON>na", "battlebit": "bitvabitt", "ultimatechickenhorse": "ultimátníkuřecíkoň", "dialtown": "dialtown", "smileforme": "usmějseprom<PERSON>", "catnight": "kočkonoční", "supermeatboy": "supermasomuž", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "útulnýzáliv", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "hranic<PERSON>ze", "pubg": "pubg", "callofdutyzombies": "povolánídutyzombie", "apex": "vrchol", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "obranazemě", "huntshowdown": "lovnašponěření", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "povstaleckapoušť", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "volánízbraněválečnázona", "codzombies": "codzombíci", "mirrorsedge": "zrcadlovookraj", "divisions2": "divize2", "killzone": "zónavraždy", "helghan": "<PERSON><PERSON><PERSON><PERSON>", "coldwarzombies": "studenávojnaživýchmrtvých", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "křížovýkód", "goldeneye007": "zlatéoko007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "moderniválka", "neonabyss": "neonovápropast", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "hraničářskézemě", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalovápulka", "primalcarnage": "primalcarnage", "worldofwarships": "světl<PERSON><PERSON><PERSON><PERSON>ů", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "hitman", "masseffect": "masseffekt", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "jeskynníp<PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "zbraněválky", "mwo": "mwo", "division2": "divize2", "tythetasmaniantiger": "tytas<PERSON><PERSON>sky<PERSON>ger", "generationzero": "generacezero", "enterthegungeon": "vstupdozbraně", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernívojna2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "phantombolest", "warface": "válečnýobličej", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomickésrdce", "blackops3": "blackops3", "vampiresurvivors": "vampírovipřeživší", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "svoboda", "battlegrounds": "bojiště", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON><PERSON>", "gamepubg": "hrajpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalguerovislobody", "juegosfps": "fpshry", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "shatterline", "blackopszombies": "černáoperacizombíci", "bloodymess": "krvavýbordel", "republiccommando": "republikovýkomando", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON>", "groundbranch": "groundbranch", "squad": "skva<PERSON>", "destiny1": "osud1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "světotan<PERSON><PERSON><PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "malátininakrajinazázraků", "halo2": "halo2", "payday2": "pátekvýplaty2", "cs16": "cs16", "pubgindonesia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgrománie", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "mýdlovka", "ghostcod": "duchovkod", "csplay": "csplay", "unrealtournament": "neuvěřitelnýturnaj", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "zabijackapod<PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "trhlinalož<PERSON>í", "neonwhite": "neonwhite", "remnant": "zbytek", "azurelane": "azurováplavba", "worldofwar": "světválky", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "stínovýmuž", "quake2": "quake2", "microvolts": "mikrov<PERSON>y", "reddead": "červenámrtvá", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rustuj", "conqueronline": "dobijs<PERSON><PERSON><PERSON>", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "válečnélodě", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "letandění", "recroom": "recroom", "legendsofruneterra": "legendypoběžnézemě", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "bezprincezny", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "přeškrtnout", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "věžfantazie", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superzvířecíroyal", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "rytironline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "bindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "kódovéviny", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubtučňáků", "lotro": "lotro", "wakfu": "wakfu", "scum": "šmejd", "newworld": "novy<PERSON><PERSON>", "blackdesertonline": "černápoušťonline", "multiplayer": "multiplayer", "pirate101": "pirát101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "hvězdnéválkybitvafront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "světvojenskýchkouzel", "warcraft": "warcraft", "wotlk": "wwotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "popelstvoření", "riotmmo": "<PERSON><PERSON><PERSON><PERSON>", "silkroad": "hed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknighti", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "pomsta", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "drakuvp<PERSON><PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "anděléonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsstarárepublika", "grandfantasia": "velkefantazie", "blueprotocol": "modryprotokol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "vystoupitonline", "corepunk": "corepunk", "adventurequestworlds": "dobrodružstvíhledánísvěta", "flyforfun": "létaníprozábavu", "animaljam": "zvířecíhrátka", "kingdomofloathing": "královstvíznechutenství", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "pouličníbojovník", "hollowknight": "prázdn<PERSON><PERSON><PERSON>íř", "metalgearsolid": "metalgearsolid", "forhonor": "pročestvuj", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuálníbojovník", "streetsofrage": "ulicehr<PERSON><PERSON>", "mkdeadlyalliance": "mksmrtícíaliance", "nomoreheroes": "žádníhrdinové", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "králbojovníků", "likeadragon": "jako_dragon", "retrofightinggames": "retroheryzbiti", "blasphemous": "svatokrádežný", "rivalsofaether": "rivalskaetheru", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>ů", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "kyberboty", "armoredwarriors": "ozbrojeníbojovníci", "finalfight": "konečnétlčení", "poweredgear": "silovyzavadeni", "beatemup": "mláceníz<PERSON><PERSON><PERSON><PERSON>ů", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "b<PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "zabijáckýinstinkt", "kingoffigthers": "králbojovníků", "ghostrunner": "ghostrunner", "chivalry2": "rytířství2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hallowed<PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "prázdnírytí<PERSON>í", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silksong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undernight": "podeznínoc", "typelumina": "typelum<PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON>", "evomoment": "evomoment", "lollipopchainsaw": "lízátkovásekera", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "příběhyberserie", "bloodborne": "krvavění", "horizon": "horizont", "pathofexile": "cestazapovědí", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "krvavéprobuzení", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonbezsvítání", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "poslednízná<PERSON>", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationkamarádi", "ps1": "ps1", "oddworld": "<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "b<PERSON><PERSON><PERSON><PERSON>", "gris": "šedivka", "trove": "poklad", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turistickátrofej", "lspdfr": "lspdfr", "shadowofthecolossus": "stín<PERSON><PERSON><PERSON><PERSON><PERSON>ů", "crashteamracing": "crashteamracing", "fivepd": "pětpd", "tekken7": "tekken7", "devilmaycry": "ďábelpláče", "devilmaycry3": "ďábeldokážeplakat3", "devilmaycry5": "diablospláče5", "ufc4": "ufc4", "playingstation": "hracistation", "samuraiwarriors": "samuraiwarriors", "psvr2": "psvr2", "thelastguardian": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "duš<PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "lov<PERSON>už<PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "stínohry2smlouva", "pcsx2": "pcsx2", "lastguardian": "posled<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "hernípass", "armello": "armello", "partyanimal": "pař<PERSON>", "warharmmer40k": "warharmmer40k", "fightnightchampion": "b<PERSON>jovánějakýšampion", "psychonauts": "psychonauti", "mhw": "mhw", "princeofpersia": "princ<PERSON><PERSON>", "theelderscrollsskyrim": "starážeňskáskárym", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "bojiště", "dontstarvetogether": "neum<PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "sprelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "hvězdnévýpravy", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americkýmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakralov", "fable2": "bajka2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "odpadnítv", "skycotl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "ma<PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstróznípárování", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "vnějšídivočiny", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stanley<PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "okultní", "longdrive": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "spokojenost", "pluviophile": "pluviophile", "underearth": "podzemí", "assettocorsa": "assettocorsa", "geometrydash": "geometrickářina", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON>", "darkdome": "temnákupole", "pizzatower": "pizzav<PERSON>ž", "indiegame": "indiehra", "itchio": "itchio", "golfit": "golfit", "truthordare": "pravdanebovýzva", "game": "hra", "rockpaperscissors": "kámenpap<PERSON><PERSON>", "trampoline": "trampolína", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "výzva", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "zahradníhry", "pickanumber": "vyberčíslo", "trueorfalse": "pravdanep<PERSON><PERSON><PERSON>", "beerpong": "pivnípong", "dicegoblin": "kostkovýpřízrak", "cosygames": "útulnéhry", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON><PERSON>", "simulationgames": "simulacníhry", "wordgames": "slovníhry", "jeuxdemots": "hraváslova", "juegosdepalabras": "slovní<PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "pojďmehrátnějakouhru", "boredgames": "nud<PERSON><PERSON><PERSON><PERSON>", "oyun": "hura", "interactivegames": "interaktivníhry", "amtgard": "amtgard", "staringcontests": "staringcontesty", "spiele": "hraj", "giochi": "hry", "geoguessr": "geoguessr", "iphonegames": "i<PERSON><PERSON>y", "boogames": "boohry", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "schovávaná", "hopscotch": "skákání", "arcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klasick<PERSON>ra", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "tiptexty", "galagames": "galahry", "romancegame": "<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "jazykolamy", "4xgames": "4xgames", "gamefi": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "herna", "tabletopgames": "stolníhry", "metroidvania": "metroidvania", "games90": "hry90", "idareyou": "vydovolujuси", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "skutečnývšifalešný", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "hraníonline", "onlinegames": "onlinehry", "jogosonline": "hryonline", "writtenroleplay": "<PERSON>an<PERSON><PERSON>eplay", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "kooperacnehry", "jenga": "jenga", "wiigames": "wiihry", "highscore": "vysokaskóre", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgerhry", "kidsgames": "dětskéhry", "skeeball": "skiball", "nfsmwblackedition": "nfsmwčernáedice", "jeuconcour": "jeukonkurs", "tcgplayer": "tc<PERSON><PERSON><PERSON><PERSON>", "juegodepreguntas": "hraš<PERSON><PERSON><PERSON><PERSON>", "gioco": "hra", "managementgame": "hermanagementu", "hiddenobjectgame": "skrytéobjekty", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1hra", "citybuilder": "městskýstavitel", "drdriving": "drdriving", "juegosarcade": "hernyarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "akčníhry", "blowgames": "blowgames", "pinballmachines": "pinballové<PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON>", "couchcoop": "kanapovákooperace", "perguntados": "zeptejtese", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imess<PERSON><PERSON>y", "idlegames": "hryprolenošení", "fillintheblank": "vyplňdíru", "jeuxpc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rétrogaming": "rétrogaming", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "železničnísurf", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "výstupovéhry", "5vs5": "5v5", "rolgame": "rolovačka", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tradicional<PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "herafps", "textbasedgames": "textov<PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafy", "fantacalcio": "fantacalcio", "retrospel": "retrohrátka", "thiefgame": "zlodějskahra", "lawngames": "trávn<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stolnífotbal", "tischfußball": "koupitfotbal", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "útěkovéhry", "thiefgameseries": "kradějhraserie", "cranegames": "žirafíhry", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbald", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "mág", "cargames": "<PERSON><PERSON><PERSON>", "onlineplay": "onlinehra", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "náhodník", "msx": "msx", "anagrammi": "anagramy", "gamespc": "hrajpc", "socialdeductiongames": "sociálníodhalovacihry", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrickéhry", "goodoldgames": "dobréstar<PERSON><PERSON><PERSON>", "truthanddare": "<PERSON>rav<PERSON>av<PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "lovcenabytku", "jeuxvirtuel": "virtuálníhry", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "fan<PERSON>zijnih<PERSON>", "gryonline": "gryonline", "driftgame": "driftova_hra", "gamesotomes": "hernesotomes", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "houbovýr<PERSON><PERSON>", "anythingwithanengine": "cokolisnmožností", "everywheregame": "hravsude", "swordandsorcery": "mečazaklínání", "goodgamegiving": "dobráhraintakdávám", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8hry", "labzerogames": "<PERSON><PERSON><PERSON><PERSON>", "grykomputerowe": "hryn<PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "hrarhythmů", "minaturegames": "<PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "hernímódo<PERSON>", "crimegames": "kriminalnizahry", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacenref", "charades": "člověknenírobot", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "kooperacnihra", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON>nah<PERSON>", "kingdiscord": "králd<PERSON>rd<PERSON>", "scrabble": "scrabble", "schach": "<PERSON><PERSON>", "shogi": "<PERSON><PERSON>i", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "pandemickéod<PERSON><PERSON>", "camelup": "velbloudískok", "monopolygame": "monopolyhra", "brettspiele": "<PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON>", "boardgame": "stolníhra", "sällskapspel": "společenskéhry", "planszowe": "<PERSON><PERSON><PERSON>", "risiko": "riziko", "permainanpapan": "<PERSON><PERSON><PERSON>", "zombicide": "zombicida", "tabletop": "stolníhry", "baduk": "baduk", "bloodbowl": "krvavýbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "hrdinskávýprava", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "karambol", "tablegames": "stolníhry", "dicegames": "kostkovéhry", "yatzy": "jatzí", "parchis": "parchis", "jogodetabuleiro": "jogodselského", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "stolníhry", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kosmickéuzuření", "creationludique": "hravá_kreativita", "tabletoproleplay": "stolníhry", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchboardhry", "infinitythegame": "nekonečnáhra", "kingdomdeath": "královstvosedmích", "yahtzee": "jdes<PERSON>", "chutesandladders": "chutěazebříky", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "stolníhry", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "životprovincie", "boardom": "nuda<PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudes<PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "stolníhry", "twilightimperium": "<PERSON><PERSON><PERSON><PERSON>", "horseopoly": "koňopolí", "deckbuilding": "stavění<PERSON>ů", "mansionsofmadness": "sídlašilenství", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "stínybrimstone", "kingoftokyo": "králtokya", "warcaby": "šachy", "táblajátékok": "táblajátékok", "battleship": "bitevníloď", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON>", "stolníhry": "stolníhry", "xiángqi": "šachy", "jeuxsociete": "<PERSON><PERSON><PERSON>", "gesellschaftsspiele": "společenskéhry", "starwarslegion": "hvězdnéválkylegie", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON>", "terraria": "terrarie", "dsmp": "dsmp", "warzone": "válečnáózóna", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identitav", "theisle": "ostrov", "thelastofus": "poslednízná<PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "hrobka", "callofcthulhu": "volank<PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyazinkostka", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amongus", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "dnyuplynuly", "fobia": "fobie", "witchit": "kouzelnice", "pathologic": "patologický", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "uzemněný", "stateofdecay2": "stavrozpad2", "vrising": "vrising", "madfather": "šílenámatka", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "věčnévracení", "pathoftitans": "cestaztitánů", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "zlouvnitř", "realrac": "skutečnátrasa", "thebackrooms": "zadnímístnosti", "backrooms": "backrooms", "empiressmp": "impériumsmp", "blockstory": "blokovýpř<PERSON><PERSON>ěh", "thequarry": "lomy", "tlou": "tlou", "dyinglight": "umírají<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "hravstvenmrtvých", "wehappyfew": "jsmešťastnípár", "riseofempires": "vzestupdohod", "stateofsurvivalgame": "stavpřežitíhravel", "vintagestory": "vintagepříbě<PERSON>", "arksurvival": "arkpřežití", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "prežitíwestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "mrazivýpunk", "darkwood": "temnýhvozd", "survivalhorror": "přežitková<PERSON>ů<PERSON>", "residentevil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil2": "rezidentnízlo2", "residentevil4": "rezidentnízlo4", "residentevil3": "rezidencevelké3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "zivotpo<PERSON>ni", "survivalgames": "přežití<PERSON><PERSON><PERSON>", "sillenthill": "tichéúdolí", "thiswarofmine": "tahlenavšednémživota", "scpfoundation": "scpfoundation", "greenproject": "zelenýprojekt", "kuon": "kuon", "cryoffear": "krevzestrachu", "raft": "raft", "rdo": "rdo", "greenhell": "zelenépek<PERSON>", "residentevil5": "rezidentnízlo5", "deadpoly": "mrtvopolya", "residentevil8": "residentvil8", "onironauta": "onironauta", "granny": "babička", "littlenightmares2": "malekosmaru2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "synovélesa", "rustvideogame": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "přežítzápas<PERSON>", "alienisolation": "izolacealiena", "undawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "7dnůk<PERSON><PERSON><PERSON><PERSON><PERSON>", "sunlesssea": "bezkyslce", "sopravvivenza": "přežití", "propnight": "propnoci", "deadisland2": "mrtvésa2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "smrtvers", "cataclysmdarkdays": "kataklysmickétemnédny", "soma": "soma", "fearandhunger": "stracha<PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "životpotom", "ageofdarkness": "věktemnoty", "clocktower3": "hodinovávěž3", "aloneinthedark": "sámvtemnotě", "medievaldynasty": "středověkádynastie", "projectnimbusgame": "projekt<PERSON><PERSON><PERSON><PERSON>hra", "eternights": "věčnostnoci", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "out<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunkr", "worlddomination": "světovládnutí", "rocketleague": "raketováliga", "tft": "tft", "officioassassinorum": "oficiálnívrazi", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "zabijaktr<PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40klesk", "wh40": "wh40", "warhammer40klove": "warhammer40kamilujeme", "warhammer40klore": "warhammer40kpróza", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40ktemnátide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "v<PERSON><PERSON><PERSON><PERSON>", "ilovesororitas": "milujusororitas", "ilovevindicare": "milujuvindicare", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "povolánímorčat", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "věkempire", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammervěksigmarův", "civilizationv": "civilizacev", "ittakestwo": "vytvárátohromadě", "wingspan": "rozpětí", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "hrdinovésílyaválky", "btd6": "btd6", "supremecommander": "supremekomandér", "ageofmythology": "věkmytologie", "args": "a<PERSON><PERSON>", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "předsíň2", "banished": "vyhnán", "caesar3": "caesar3", "redalert": "červenýpoplach", "civilization6": "civilizace6", "warcraft2": "warcraft2", "commandandconquer": "vláďaapřemož", "warcraft3": "warcraft3", "eternalwar": "věčnáválka", "strategygames": "strategickéhr<PERSON>", "anno2070": "anno2070", "civilizationgame": "civilizačníhra", "civilization4": "civilizace4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "tot<PERSON>lníválk<PERSON>", "travian": "travian", "forts": "<PERSON><PERSON><PERSON><PERSON>", "goodcompany": "dobráspolečnost", "civ": "civ", "homeworld": "domovskýsvět", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "rychlejcnezlampa", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "realnátaktika", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "královstvídvacoron", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "božství", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveszábavnáalgebra", "plagueinc": "morvinc", "theorycraft": "teoretickévytváření", "mesbg": "mesbg", "civilization3": "civilizace3", "4inarow": "4vř<PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "hrdinové3", "advancewars": "pokrocilé<PERSON><PERSON>lk<PERSON>", "ageofempires2": "věkempírů2", "disciples2": "učení2", "plantsvszombies": "rost<PERSON>yv<PERSON>mb<PERSON><PERSON>", "giochidistrategia": "giochidistrategia", "stratejioyunları": "strategiehrání", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "věk<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosaurskýk<PERSON><PERSON><PERSON>", "worldconquest": "světovépokoření", "heartsofiron4": "srdceželeza4", "companyofheroes": "společenstvivrlegendách", "battleforwesnoth": "bitvazawesnoth", "aoe3": "aoe3", "forgeofempires": "kovárenempérií", "warhammerkillteam": "warhammerzabijackýtým", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "fobie", "phobiesgame": "phobiesgame", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "vnějšíplán", "turnbased": "systémovka", "bomberman": "bomberman", "ageofempires4": "vekempérií4", "civilization5": "civilizace5", "victoria2": "victoria2", "crusaderkings": "krcizinakralu", "cultris2": "cultris2", "spellcraft": "kouzelnictví", "starwarsempireatwar": "hvězdnéválkydějištěválky", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategie", "popfulmail": "popfulmail", "shiningforce": "zářivásíla", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "dopravnítycoon", "unrailed": "nesestavitelní", "magicarena": "<PERSON><PERSON>na", "wolvesville": "vlkoviště", "ooblets": "ooblets", "planescapetorment": "plánujútěchouzení", "uplandkingdoms": "uplandkralovství", "galaxylife": "galaxylife", "wolvesvilleonline": "vlkovenonline", "slaythespire": "zrasitspičku", "battlecats": "bitvacatů", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potřebunájezd", "needforspeedcarbon": "potřebujespeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "ztracims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "mrtvypodsvětle", "alicemadnessreturns": "vraceníalicemadness", "darkhorseanthology": "temnýkůňantologie", "phasmophobia": "fobiek<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "pětknížk<PERSON><PERSON>dy<PERSON>", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "malénoční<PERSON>", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "doma", "deadisland": "mrt<PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "malápaníneštěstí", "projectzero": "projektzero", "horory": "boohorory", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "ahojsoused2", "gamingdbd": "gamingdbd", "thecatlady": "kočkoholka", "jeuxhorreur": "hrôznéhry", "horrorgaming": "hororovépobyty", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartyprotičlověku", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "krycíjména", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "srdcovka", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "klíčovépečení", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "neuronovýběh", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "obchodníkarty", "pokemoncards": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "masoakrvetcg", "sportscards": "sportovníkarty", "cardfightvanguard": "karetníbojvanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON>", "warcry": "válečnýpokřik", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "králsrdcí", "truco": "truc", "loteria": "loterie", "hanafuda": "hana<PERSON>da", "theresistance": "odpor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "temnýkouzelník", "blueeyeswhitedragon": "modrýmaočimabílýdrak", "yugiohgoat": "yugiohkoza", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rumy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandér", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgrozhodčí", "juegosdecartas": "kartovýhry", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "karty", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "karcia<PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "bitvanejsoupribehu", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "<PERSON><PERSON>", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "nestabilníjednorozci", "cyberse": "kyberse", "classicarcadegames": "klasickeaark<PERSON>vky", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "kytarovýhrdina", "clonehero": "<PERSON><PERSON><PERSON>šek", "justdance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rozhýbtemrtváky", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tancovacentral", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "vysokýskórehravérytmusovéhry", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rytmickénebe", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "audiceonline", "itgmania": "it<PERSON><PERSON>", "juegosderitmo": "hrerenytmu", "cryptofthenecrodancer": "cryptovnecrohrávač", "rhythmdoctor": "rytmdiagnostik", "cubing": "krychlení", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "<PERSON><PERSON><PERSON>znam<PERSON>", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubikovakostka", "crossword": "křížovka", "motscroisés": "křížovky", "krzyżówki": "křížovky", "nonogram": "nonogram", "bookworm": "knižníž<PERSON>", "jigsawpuzzles": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "hádač<PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "skládačka", "tekateki": "tekateki", "inside": "uvnitř", "angrybirds": "zlobivépixels", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON>", "minesweeper": "minesweeper", "puzzleanddragons": "složenkaazdragony", "crosswordpuzzles": "křížovky", "kurushi": "k<PERSON>hi", "gardenscapesgame": "zahradníscéna", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "<PERSON><PERSON><PERSON>", "escapegame": "únikovka", "3dpuzzle": "3dskládačka", "homescapesgame": "homescapesgame", "wordsearch": "slovnakreslení", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "rybíkrálovství", "theimpossiblequiz": "neproveditelnýkvíz", "candycrush": "candycrush", "littlebigplanet": "malývelkýsvět", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "rubikova_kostka", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "talosůvprinci<PERSON>", "homescapes": "domovskákrajina", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "hádejmě", "tycoongames": "tycoongames", "cubosderubik": "kubikovakrabicka", "cruciverba": "k<PERSON><PERSON><PERSON>ov<PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "puzzlewords", "buscaminas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "ř<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "řepákboy", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogramy", "kostkirubika": "kostkirubika", "crypticcrosswords": "kryptickékřížovky", "syberia2": "syberia2", "puzzlehunt": "hledanípuzzle", "puzzlehunts": "hledán<PERSON><PERSON><PERSON>dankovýchsklá<PERSON>ček", "catcrime": "kočkolidství", "quebracabeça": "puzzle", "hlavolamy": "boohl<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "p<PERSON>led<PERSON>í<PERSON><PERSON><PERSON>", "autodefinidos": "autodefinovaní", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "kartička", "untitledgoosegame": "nepojmenovaná<PERSON><PERSON><PERSON>", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubikova_kostka", "maze": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "rychlostníkostka", "pieces": "kousky", "portalgame": "portalgame", "bilmece": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixkostka", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomágico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobilka", "codm": "codm", "twistedwonderland": "zvrácenáříše", "monopoly": "monopol", "futurefight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilnílegendy", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "osamělávlčice", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "královstvícukroví", "alchemystars": "alchemystars", "stateofsurvival": "stavebnezbytí", "mycity": "mojecity", "arknights": "arknights", "colorfulstage": "barevnéjeviště", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hyperfront": "hyperfront", "knightrun": "ritířskéběhání", "fireemblemheroes": "fireemblemhrdinové", "honkaiimpact": "honkaiimpact", "soccerbattle": "fotbalovaboj", "a3": "a3", "phonegames": "hranafonech", "kingschoice": "kralovskavybora", "guardiantales": "strážcovsképohádky", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "takt<PERSON><PERSON><PERSON><PERSON>", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON>", "craftsman": "řemeslník", "supersus": "supersus", "slowdrive": "pomalájízda", "headsup": "hlavnění", "wordfeud": "slovoklub", "bedwars": "postelebitvy", "freefire": "freefire", "mobilegaming": "mobilníhraní", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "týmovéboje", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "nouzovka", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "bitva<PERSON><PERSON>ů", "starstableonline": "hvězdnástájonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "časováprincezna", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegenda", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "kapsovánláska", "androidgames": "and<PERSON><PERSON><PERSON>", "criminalcase": "kriminálnípřípad", "summonerswar": "summonerswar", "cookingmadness": "vařenívšílenství", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacloud", "leagueofangels": "ligaandelů", "lordsmobile": "lordsmobile", "tinybirdgarden": "malýpt<PERSON>zahrada", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mojiprozpěvujícímonstra", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "válečníroboti", "mirrorverse": "zrcadlovývesmír", "pou": "pou", "warwings": "vojenskákřídla", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antijoy", "apexlegendmobile": "apexlegendmobile", "ingress": "vstup", "slugitout": "bo<PERSON><PERSON>", "mpl": "mpl", "coinmaster": "mincovnimestar", "punishinggrayraven": "trestaj<PERSON><PERSON><PERSON><PERSON>k", "petpals": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "běžeckářskáhrazdářka", "juegodemovil": "hramobilne", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "černápoušťmobil", "rollercoastertycoon": "traknorollercoaster", "grandchase": "grandchase", "bombmebrasil": "bombmemebrazílie", "ldoe": "ldoe", "legendonline": "legendyonline", "otomegame": "o<PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "zářícínik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "cestaprinikam", "sealm": "sealm", "shadowfight3": "stínovýtaktika3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "slovazkamarády2", "soulknight": "soulknight", "purrfecttale": "mňaukavýpříběh", "showbyrock": "showbyrock", "ladypopular": "ladyoblíben<PERSON>", "lolmobile": "lolmobil", "harvesttown": "sk<PERSON><PERSON><PERSON><PERSON>ěsto", "perfectworldmobile": "dokonalýsvětmobile", "empiresandpuzzles": "říšeapuzzle", "empirespuzzles": "empirspuzzle", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilecz", "fanny": "prdel", "littlenightmare": "malénoční<PERSON>", "aethergazer": "aethergazer", "mudrunner": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "sl<PERSON><PERSON>mi", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobilně", "arknight": "arknight", "pristontale": "přistontale", "zombiecastaways": "zombiepotáp<PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilnílegendašupšup", "gachaclub": "gachaklub", "v4": "v4", "cookingmama": "kuchyňskámama", "cabalmobile": "cabalmobil", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "dívčinapřednílině", "jurassicworldalive": "jurassicworldživě", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "dostáv<PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilníhry", "legendofneverland": "legendaneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "mobilnígaming", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bitvacakcats", "dnd": "dnd", "quest": "výzva", "giochidiruolo": "hrazenáoblíbená", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "s<PERSON><PERSON><PERSON><PERSON>noty", "travellerttrpg": "cestovatelskáttrpg", "2300ad": "2300nl", "larp": "larp", "romanceclub": "romanceclub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokémonmysterydungeon", "pokemonlegendsarceus": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkrystal", "pokemonanime": "pokémonanime", "pokémongo": "pokémongo", "pokemonred": "pokémončervená", "pokemongo": "pokemongo", "pokemonshowdown": "pokémonbitva", "pokemonranger": "poké<PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "pok<PERSON><PERSON><PERSON>", "chatot": "pokecení", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "t<PERSON><PERSON><PERSON>", "pokeball": "pokéball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "lesklépokémony", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "železnépěstě", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "lesklovytac", "ajedrez": "šachy", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "šachy", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "japonskésachy", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šachykanada", "fide": "fide", "xadrezverbal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openings": "otvíračky", "rook": "<PERSON><PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "kalabozyadragony", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "pánjeskyně", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventura", "darksun": "temnéslunce", "thelegendofvoxmachina": "legendavoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "temnémoor", "minecraftchampionship": "minecraftšampionát", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodifikace", "mcc": "mcc", "candleflame": "plam<PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeony", "minecraftcity": "minecraftměsto", "pcgamer": "pcgamer", "jeuxvideo": "hernivideo", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "levelup", "gamermobile": "gamermobil", "gameover": "konechrálek", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "hrát_hry", "pcgames": "p<PERSON>ry", "casualgaming": "casualgaming", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "pchračka", "gamerboy": "gamerkluk", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "epič<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "onlinehraní", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "gamerguy", "gamewatcher": "hrajesledovatel", "gameur": "<PERSON>ra<PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "týmovkanaplno", "mallugaming": "mallugaming", "pawgers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quests": "úkoly", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "útulnéhraní", "gamelpay": "gamelpay", "juegosdepc": "p<PERSON><PERSON>y", "dsswitch": "dsswitch", "competitivegaming": "konkurenčníhraní", "minecraftnewjersey": "minecraftnovyjersy", "faker": "falešník", "pc4gamers": "pc4gamery", "gamingff": "gamingftw", "yatoro": "yatoro", "heterosexualgaming": "heterosexuálníhraní", "gamepc": "her<PERSON><PERSON>", "girlsgamer": "holkyhrají", "fnfmods": "fnfmodifikace", "dailyquest": "dailynástraha", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "gamerky", "gamesetup": "hernínastavení", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "sociálnívpíči", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "hračrole", "myteam": "můj<PERSON><PERSON>", "republicofgamers": "republikaherníků", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "triplelegend", "gamerbuddies": "gamerká<PERSON>ši", "butuhcewekgamers": "potrebujemcewekgamery", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON>ek", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "neformálnítahoun", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gameri", "oyunizlemek": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "<PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationhráč", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "ženskásuperhráčka", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "zběratel", "humanfallflat": "lidskypadpadu", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "podzimníchkluků", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "asistentdefensora", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON>hysv<PERSON>tl<PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "slzazakrainy", "walkingsimulators": "chodícís<PERSON>ul<PERSON><PERSON>", "nintendogames": "nintendohry", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "drakovaquest", "harvestmoon": "sklizňovýměsíc", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON>ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51her", "earthbound": "zaměřenénazemi", "tales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "trojúhelníkovástrategie", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kaštanyšpatnédny", "nintendos": "nintendos", "new3ds": "nový3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartytophvězdám", "marioandsonic": "marioazsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON>s", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligaslegendcz", "urgot": "urgot", "zyra": "zyra", "redcanids": "červenéps<PERSON>ky", "vanillalol": "vanillalol", "wildriftph": "wildriftcz", "lolph": "lolph", "leagueoflegend": "leagueoflegends", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligalegenddivoký", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsčesko", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcz", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "spojujemlegendama", "gaminglol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitehra", "gamingfortnite": "hranífortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retroherníků", "scaryvideogames": "strašidelnýhry", "videogamemaker": "tv<PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videohra", "videosgame": "<PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "<PERSON><PERSON><PERSON><PERSON>", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "kouzelník101", "battleblocktheater": "battleblocktheater", "arcades": "herny", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "farmingsimulator", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sanboxgames", "videogamelore": "videohrylegendy", "rollerdrome": "rollerdrom", "parasiteeve": "parazitkaeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "snospánek", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "mrtvýsva<PERSON><PERSON>v<PERSON>p<PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "<PERSON><PERSON><PERSON>", "theoldrepublic": "starárepublika", "videospiele": "<PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "<PERSON><PERSON><PERSON><PERSON>", "adventuregames": "dobrodružnéhr<PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "akčnídobrodružství", "storyofseasons": "příběhysezóny", "retrogames": "retrogames", "retroarcade": "retroherna", "vintagecomputing": "vintážovépocítání", "retrogaming": "retrogaming", "vintagegaming": "vintagehraní", "playdate": "hrací<PERSON><PERSON>", "commanderkeen": "komandérkeen", "bugsnax": "bugsnax", "injustice2": "nespravedlnost2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "skygame", "zenlife": "zenživot", "beatmaniaiidx": "beatmaniaiidx", "steep": "str<PERSON><PERSON>", "mystgames": "myst<PERSON>n<PERSON>", "blockchaingaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "středověk", "consolegaming": "konsolovéhraní", "konsolen": "konzošení", "outrun": "u<PERSON>č", "bloomingpanic": "kvetoucípanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "superobří", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "farmingsims", "juegosviejos": "<PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktivnifikce", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "posledníznás2", "amantesamentes": "milovníciamenet", "visualnovel": "vizuálníromán", "visualnovels": "vizuálníromány", "rgg": "rgg", "shadowolf": "stínovývlk", "tcrghost": "tcrghost", "payday": "výplata", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "twiłightprincezna", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "pískoviště", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "novávizuální", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "lamentování", "godhand": "božíruce", "leafblowerrevolution": "revolucevýfuků", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "hvězdnádráha", "keyblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizuálnídějiny", "robloxbrasil": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "<PERSON><PERSON><PERSON>", "videogamedates": "videohrydatování", "mycandylove": "mojevrbovka", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "návratzkoumání", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "maniacmansion", "crashracing": "nárazovzávody", "3dplatformers": "3dplatformy", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "staraskolaplay", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "hernistory", "bioware": "bioware", "residentevil6": "rezidentnízlo6", "soundodger": "soundodger", "beyondtwosouls": "mimo2<PERSON>š<PERSON>", "gameuse": "hranjedně", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "zvětšiténergie", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafickydo<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "rychlávizit", "fzero": "fzero", "gachagaming": "gachahraní", "retroarcades": "retroherny", "f123": "f123", "wasteland": "wasteland", "powerwashsim": "powerwashsim", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animebojovníci2", "footballfusion": "fotbalovásou融合", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "zkrivenáocel", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "hromadastydy", "simulator": "simul<PERSON><PERSON>", "symulatory": "symulátory", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "korkuhra", "wonderlandonline": "zázračnáříšeonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ě", "toontownrewritten": "toontownpřebraný", "simracing": "simracing", "simrace": "simrace", "pvp": "pzv", "urbanchaos": "městskýchaos", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "hřbitovnictví", "spaceflightsimulator": "simulátor<PERSON>m<PERSON><PERSON>ů", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "hernívide<PERSON>", "thewolfamongus": "vlkmezinámi", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizontovésvě<PERSON>", "handygame": "handygame", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "starš<PERSON>levideohrání", "racingsimulator": "závodnísim<PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "rebelstars", "indievideogaming": "indievideohry", "indiegaming": "indiegaming", "indievideogames": "indievideohry", "indievideogame": "indievideohra", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufortress", "unbeatable": "neporazitelný", "projectl": "projektl", "futureclubgames": "budoucíklubovky", "mugman": "hrníčkovýtyp", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "backlog", "gamebacklog": "hranízpětně", "gamingbacklog": "hernízpoždění", "personnagejeuxvidéos": "postavyhernezapomene", "achievementhunter": "love<PERSON><PERSON>ů", "cityskylines": "městskélinie", "supermonkeyball": "superopičkaboule", "deponia": "deponia", "naughtydog": "neposlušnýpes", "beastlord": "běsník", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervatoriodedopamin", "staxel": "staxel", "videogameost": "hernísoundtrack", "dragonsync": "dracisynchronizace", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "milujukofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berzerk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "smut<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "tmavšínežčerná", "animescaling": "animescaling", "animewithplot": "anime_s_příběhem", "pesci": "pesci", "retroanime": "retroanime", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "temnápán", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezon1", "rapanime": "rapanime", "chargemanken": "nabijmanka", "animecover": "animeobal", "thevisionofescaflowne": "vizeescaflowne", "slayers": "vrazi", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON><PERSON>", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletověvázanýhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "ohnivásíla", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "vyrobenovhlubinách", "parasyte": "para<PERSON>ta", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "mořskástvícennost", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horormanga", "romancemangas": "romantickémangy", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "drakosluzka", "blacklagoon": "černálaguna", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terra<PERSON><PERSON><PERSON><PERSON>", "geniusinc": "geniusinc", "shamanking": "šamanění", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "jistěmagickýindex", "sao": "sao", "blackclover": "černýtrifolium", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON>man", "hetalia": "hetalia", "kagerouproject": "kagerouprojekt", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8nebežný", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriorita", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosmik", "goldenkamuy": "zlatýkamuy", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportovnianime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "andělskébitvy", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "hezkouzlečení", "theboyandthebeast": "klukazvydražení", "fistofthenorthstar": "pěstseverníhvězdou", "mazinger": "mazinger", "blackbuttler": "černýslužebník", "towerofgod": "věžboží", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "jakudržetmamku", "fullmoonwosagashite": "plnotměníwosagashite", "shugochara": "šug<PERSON>č<PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "roztomiléazařezavé", "martialpeak": "bojovytop", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amiti": "amiti", "sailorsaturn": "námornícisaturn<PERSON>", "dio": "dio", "sailorpluto": "námorn<PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "b<PERSON><PERSON><PERSON><PERSON>", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "motorovka", "bungoustraydogs": "bungoustraydogs", "jogo": "hrání", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "klíčovýmeč", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "ovocná<PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "žijelásku", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "umibenoet<PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "zeměslibovaný<PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "tv<PERSON>le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggykluk", "bokunohero": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "seraphkonec<PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "m<PERSON><PERSON>", "deepseaprisoner": "hlubokomořskývězeň", "jojolion": "jojo<PERSON>", "deadmanwonderland": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorov<PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "jídlozápasy", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "ktojejoafinitě", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "modréobdobí", "griffithberserk": "griffithšilenství", "shinigami": "shinigami", "secretalliance": "tajnáaliance", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bluelock": "modr<PERSON><PERSON>ka", "goblinslayer": "zabijákgoblins", "detectiveconan": "detektivconan", "shiki": "shiki", "deku": "d<PERSON><PERSON>", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "upíříjezdec", "mugi": "mugi", "blueexorcist": "modrýexorcista", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "v<PERSON><PERSON><PERSON><PERSON>", "spyfamily": "špionážnírodina", "airgear": "vzdušnékola", "magicalgirl": "magickáholka", "thesevendeadlysins": "tyto7smrtelnýchhříchů", "prisonschool": "vězenoškoly", "thegodofhighschool": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "velkázamblue", "mydressupdarling": "mujoblíbenýkostým", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozenmáma", "animeuniverse": "animevesmír", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saozkráceně", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nemrtvístěstí", "romancemanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "blman<PERSON>", "animeargentina": "animeargentína", "lolicon": "lolikon", "demonslayertothesword": "démonízabijákknoži", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "ohnivýař<PERSON>", "adioseri": "sbohemseru", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "hvězdysehnuly", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "třešňovámagie", "housekinokuni": "domovskékino", "recordragnarok": "nahrávragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "středníškolazombíků", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "učebnaansatsu", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinskáškola", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "smrtivystoupení", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japon<PERSON>anime", "animespace": "animespace", "girlsundpanzer": "dívky<PERSON>dpan<PERSON>", "akb0048": "akb0048", "hopeanuoli": "doufá<PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animečscifi", "ratman": "<PERSON><PERSON><PERSON><PERSON>", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "broskvoňka", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mecamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "drakovaquestdai", "heartofmanga": "srdcemanga", "deliciousindungeon": "deliciousnvězení", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON>áz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "zábavnémuzeum", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemistr", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "čarodějnicképokladny", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaježivot", "dropsofgod": "kapkyboha", "loscaballerosdelzodia": "pánovizodiaku", "animeshojo": "animeshejnice", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "skvělejučitelonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "můjšéfujeme", "gear5": "gear5", "grandbluedreaming": "grandbluesně<PERSON><PERSON>", "bloodplus": "krveplus", "bloodplusanime": "krevplusanime", "bloodcanime": "krevníanime", "bloodc": "krvavác", "talesofdemonsandgods": "příběhy<PERSON>ů", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vrabcixtrefy", "splatteranime": "splatteranime", "splatter": "plivnout", "risingoftheshieldhero": "vzrůsthrdinastitě", "somalianime": "somalsk<PERSON><PERSON>me", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "<PERSON><PERSON><PERSON><PERSON>", "animeciudadreal": "animeciudadreal", "murim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superšampioni", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekai_byl_jsem_na_smartphonu", "midorinohibi": "midorinokvítění", "magicalgirls": "magick<PERSON><PERSON><PERSON>", "callofthenight": "volejvnoci", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakugan<PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "princeznažralok", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "rajové<PERSON>inky", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevesmír", "persocoms": "persocomy", "omniscientreadersview": "všev<PERSON><PERSON><PERSON><PERSON>čtenářůvútok", "animecat": "animekočka", "animerecommendations": "doporučenianime", "openinganime": "otvíracíanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "můjteenromantickákomedie", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamy", "voltesv": "voltesv", "giantrobots": "obrovskýroboti", "neongenesisevangelion": "neonovégenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilníbojovníkggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilníoblekgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "b<PERSON><PERSON><PERSON>", "deathnote": "smrtnotes", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarredobrodružství", "fullmetalalchemist": "plnýtranskmutant", "ghiaccio": "led", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "vojenskéanime", "greenranger": "zelen<PERSON><PERSON><PERSON><PERSON>ř", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeměsto", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "drak<PERSON>", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimondobrodružství", "hxh": "hxh", "highschooldxd": "středníškoladxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "můjhrdinškýakademie", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "útoknatitána", "erenyeager": "erenyeager", "myheroacademia": "mojaheroakademie", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "průzkumnýbor", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ů", "theonepieceisreal": "tenjedinýkusjeopravdovej", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonovýexorcista", "joyboyeffect": "efektklukašťastlivce", "digimonstory": "digimonpří<PERSON>ěh", "digimontamers": "digimontamers", "superjail": "supervězení", "metalocalypse": "metalokalypse", "shinchan": "<PERSON><PERSON><PERSON><PERSON>", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "bezchybnejwebtoon", "kemonofriends": "kemonopřátelé", "utanoprincesama": "utíkejprincezně", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "létajícíčarodějnice", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "všechsvatýchulice", "recuentosdelavida": "příběhyživota"}