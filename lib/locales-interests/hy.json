{"2048": "2048", "mbti": "mbti", "enneagram": "էնեագրամ", "astrology": "աստղագիտություն", "cognitivefunctions": "ճանաչողականգործառույթներ", "psychology": "հոգեբանություն", "philosophy": "փիլիսոփայություն", "history": "պատմություն", "physics": "ֆիզիկա", "science": "գիտություն", "culture": "մշակույթ", "languages": "լեզուներ", "technology": "տեխնոլոգիա", "memes": "մեմեր", "mbtimemes": "mbtiմեմեր", "astrologymemes": "աստղագիտությանմեմեր", "enneagrammemes": "էնեագրամմեմեր", "showerthoughts": "հոգումտքեր", "funny": "զվարճալի", "videos": "տեսանյութեր", "gadgets": "գաջետներ", "politics": "քաղաքականություն", "relationshipadvice": "հարաբերություններիխորհուրդներ", "lifeadvice": "կյանքիխորհուրդներ", "crypto": "կրիպտո", "news": "նորություններ", "worldnews": "համաշխարհայիննորություններ", "archaeology": "հնէաբանություն", "learning": "ուսուցում", "debates": "բանավեճեր", "conspiracytheories": "դավադրությանտեսություն", "universe": "աշխարհ", "meditation": "մեդիտացիա", "mythology": "դիցաբանություն", "art": "արվեստ", "crafts": "արհեստ", "dance": "պար", "design": "դիզայն", "makeup": "դիմահարդարում", "beauty": "գեղեցկություն", "fashion": "նորաձևություն", "singing": "երգ", "writing": "ստեղծագործել", "photography": "լուսանկարչություն", "cosplay": "քոսփլեյ", "painting": "նկարչություն", "drawing": "նկարչություն", "books": "գրքեր", "movies": "ֆիլմեր", "poetry": "պոեզիա", "television": "հեռուստատեսություն", "filmmaking": "ֆիլմարտադրություն", "animation": "անիմացիա", "anime": "անիմե", "scifi": "գիտականֆանտաստիկա", "fantasy": "ֆենթըզի", "documentaries": "վավերագրականֆիլմեր", "mystery": "առեղծված", "comedy": "կատակերգություն", "crime": "հանցագործություն", "drama": "դրամա", "bollywood": "բոլիվուդ", "kdrama": "կդարամկորեականդրամաներ", "horror": "սարսափ", "romance": "սիրավեպ", "realitytv": "ռեալիթիհեռուստատեսություն", "action": "մարտաֆիլմ", "music": "երաժշտություն", "blues": "բլյուզ", "classical": "դասական", "country": "քանտրի", "desi": "դեսի", "edm": "edmէլեկտրոնայինպարայիներաժշտություն", "electronic": "էլեկտրոնայիներաժշտություն", "folk": "ժողովրդականերաժշտություն", "funk": "ֆանկ", "hiphop": "հիփհոփ", "house": "hաուսերաժշտություն", "indie": "ինդիռոք", "jazz": "ջազ", "kpop": "քեյփոփ", "latin": "լատինականերաժշտություն", "metal": "մետալ", "pop": "փոփ", "punk": "փանկ", "rnb": "rnbռիթմընդբլյուզ", "rap": "ռեփ", "reggae": "ռեգգեյ", "rock": "ռոք", "techno": "տեխնո", "travel": "ճանապարհորդություն", "concerts": "համերգներ", "festivals": "փառատոններ", "museums": "թանգարաններ", "standup": "սթենդափ", "theater": "թատրոն", "outdoors": "բացօթյազբաղմունքներ", "gardening": "այգեգործություն", "partying": "երեկույթներ", "gaming": "խաղեր", "boardgames": "սեղանիխաղեր", "dungeonsanddragons": "զնդաններևվիշապներ", "chess": "շախմատ", "fortnite": "ֆորտնայթ", "leagueoflegends": "լեգենդներիլիգա", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "պոկեմոն", "food": "սնունդ", "baking": "թխվածքիպատրաստում", "cooking": "խոհարարություն", "vegetarian": "բուսակեր", "vegan": "վեգան", "birds": "թռչուններ", "cats": "կատուներ", "dogs": "շներ", "fish": "ձկներ", "animals": "կենդանիներ", "blacklivesmatter": "սևամորթներիկյանքընշանակությունունի", "environmentalism": "բնապահպանություն", "feminism": "ֆեմինիզմ", "humanrights": "մարդուիրավունքներ", "lgbtqally": "լգբտաջակից", "stopasianhate": "ոչռասիզմին", "transally": "տրանսսեքսուալներ", "volunteering": "կամավորություն", "sports": "սպորտ", "badminton": "բադմինթոն", "baseball": "բեյսբոլ", "basketball": "բասկեթբոլ", "boxing": "բոքս", "cricket": "կրիկետ", "cycling": "հեծանվավազք", "fitness": "ֆիթնես", "football": "ֆուտբոլ", "golf": "գոլֆ", "gym": "մարզասրահ", "gymnastics": "մարմնամարզություն", "hockey": "հոկեյ", "martialarts": "մարտականարվեստներ", "netball": "նեթբոլ", "pilates": "պիլատես", "pingpong": "պինգպոնգ", "running": "վազք", "skateboarding": "սքեյթբորդինգ", "skiing": "դահուկավազք", "snowboarding": "սնոուբորդինգ", "surfing": "սերֆինգ", "swimming": "լող", "tennis": "թենիս", "volleyball": "վոլեյբոլ", "weightlifting": "ծանարամարտ", "yoga": "յոգա", "scubadiving": "սկուբադայվինգ", "hiking": "քայլարշավ", "capricorn": "այծեղջյուր", "aquarius": "ջրհոս", "pisces": "ձկներ", "aries": "խոյ", "taurus": "ցուլ", "gemini": "երկվորյակ", "cancer": "խեցգետին", "leo": "առյուծ", "virgo": "կույս", "libra": "կշեռք", "scorpio": "կարիճ", "sagittarius": "աղեղնավոր", "shortterm": "կարճաժամկետ", "casual": "առանձպարտավորություններ", "longtermrelationship": "երկարաժամկետհարաբերություն", "single": "անկապ", "polyamory": "բազմասիրություն", "enm": "բացհարաբերություն", "lgbt": "լգբտ", "lgbtq": "լգբտք", "gay": "գեյ", "lesbian": "լեսբուհի", "bisexual": "բիսեքսուալ", "pansexual": "պանսեքսուալ", "asexual": "ասեքսուալ", "reddeadredemption2": "ռեդդեդռեդեմպշն2", "dragonage": "դրագոնէյջ", "assassinscreed": "սպանությանհախարկարգը", "saintsrow": "սուրբսյուներ", "danganronpa": "դանգանռոպա", "deltarune": "դելթարուն", "watchdogs": "հսկիչներ", "dislyte": "դիսլայթ", "rougelikes": "ռոզգեյմերներ", "kingsquest": "թագավորներիերթություն", "soulreaver": "հոգիառնչվող", "suikoden": "սուիկոդեն", "subverse": "սաբվերս", "legendofspyro": "spyroтиlegendary", "rouguelikes": "ռուգլայքս", "syberia": "սիբիրիայի", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "թևավորաղջիկ", "sunsetoverdrive": "արեւածաղիկիարդյունք", "arkham": "երկաղանթ", "deusex": "դիևսեքս", "fireemblemfates": "հրահրվեց_ազդեցության_պատերազմներ", "yokaiwatch": "իոկայվwatch", "rocksteady": "ռոքստեդի", "litrpg": "լիտռպգ", "haloinfinite": "հալոանսահմանափակ", "guildwars": "guildwars", "openworld": "հավաքականաշխարհ", "heroesofthestorm": "բոլորիդսառյուծներ", "cytus": "ցիտուս", "soulslike": "հոգեբանիականընթացքներ", "dungeoncrawling": "կարգայոթեղափոխություն", "jetsetradio": "ջեթսեթռեդիո", "tribesofmidgard": "միդգարդիեզներ", "planescape": "նախագծայինաշխարհ", "lordsoftherealm2": "հնարավորություններիամերց2", "baldursgate": "բալդուրիտուրքեր", "colorvore": "գույնավառ", "medabots": "մեդաբոտներ", "lodsoftherealm2": "լոդսոֆդերեմլ2", "patfofexile": "պատֆոֆէքզայլ", "immersivesims": "հիմնականսիմս", "okage": "ոքաժե", "juegoderol": "ռոլխաղ", "witcher": "վիթչեր", "dishonored": "բացառված", "eldenring": "eldensomething", "darksouls": "դարկսոլս", "kotor": "կոտոր", "wynncraft": "wynn<PERSON>", "witcher3": "գիշերաման", "fallout": "ֆոլաուտ", "fallout3": "ֆոլաութ3", "fallout4": "ֆոլաուտ4", "skyrim": "սկայրիմ", "elderscrolls": "ղերեգիրքեր", "modding": "մոդինգ", "charactercreation": "հերոսստեղծում", "immersive": "խորը_ներգրավվածություն", "falloutnewvegas": "ֆոլլաութնյուուեգաս", "bioshock": "բիոշոկ", "omori": "օմորի", "finalfantasyoldschool": "ֆինանսականֆանտազիաարդինձեռգիսնավեջ", "ffvii": "ֆֆվիի", "ff6": "ֆֆ6", "finalfantasy": "վերջինֆանտազիա", "finalfantasy14": "վերջինֆանտազիա14", "finalfantasyxiv": "վերջինֆանտազիաxiv", "ff14": "ֆֆ14", "ffxiv": "ffxiv", "ff13": "ֆֆ13", "finalfantasymatoya": "ֆինալֆանտազիանմատոյա", "lalafell": "լալաֆելլ", "dissidia": "դիսիդիա", "finalfantasy7": "վերջինֆանտազիա7", "ff7": "ֆֆ7", "morbidmotivation": "մանկականառաջադիմություն", "finalfantasyvii": "վերջինֆանտազիա7", "ff8": "ֆֆ8", "otome": "օտոմե", "suckerforlove": "սերասերboo", "otomegames": "ոթոմեգեյմերներ", "stardew": "աստղայինդաշտ", "stardewvalley": "ստարդիւվե莉", "ocarinaoftime": "օկարինայիժամանակ", "yiikrpg": "յիքռպգ", "vampirethemasquerade": "վամպիրներիմասկանուրումը", "dimension20": "dimension20", "gaslands": "gasılands", "pathfinder": "ուղեցույց", "pathfinder2ndedition": "ուղեցույց2րդմասն", "shadowrun": "46409booեզրափակիչռելիզ", "bloodontheclocktower": "արյունժողովրդիժամացույցին", "finalfantasy15": "ֆինալֆանտազի15", "finalfantasy11": "ավարտականֆանտազիա11", "finalfantasy8": "վերաստեղծմանֆանտազիա8", "ffxvi": "ffxvi", "lovenikki": "լարվածկապանցիկ", "drakengard": "դրակենգարդ", "gravityrush": "գրավիտյուըշտ", "rpg": "rpg", "dota2": "dota2", "xenoblade": "ժենոբլեյդ", "oneshot": "մեկօրեն", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "գերագույն", "yourturntodie": "եդորդեպիամուսնանալ", "persona3": "պրեսոնա3", "rpghorror": "ռպգահhorror", "elderscrollsonline": "հինչեռնիկներառաջինգործառույթ", "reka": "ռեկան", "honkai": "հոնկայ", "marauders": "խափանողներ", "shinmegamitensei": "շինմեգամիտենսեյ", "epicseven": "էպիկ7", "rpgtext": "rpgթեքստ", "genshin": "genshin", "eso": "էսո", "diablo2": "դիաբլո2", "diablo2lod": "դիաբլո2լոդ", "morrowind": "մորոուինդ", "starwarskotor": "աստղայինպաշտոնականkotor", "demonsouls": "դեմոնսոլս", "mu": "մու", "falloutshelter": "գարունադshelter", "gurps": "gurps", "darkestdungeon": "մութավանդակ", "eclipsephase": "էկլիպսփազա", "disgaea": "դիզգեա", "outerworlds": "արտաքինաշխարհներ", "arpg": "առյուծպորտյաներ", "crpg": "crpg", "bindingofisaac": "իսահակիարաշչումը", "diabloimmortal": "դիաբլոիմմորտալ", "dynastywarriors": "դինաստիաներաուզերներ", "skullgirls": "ուղուրտաղջիկներ", "nightcity": "գիշերայինքաղաք", "hogwartslegacy": "հոգվոարտսսթառուն", "madnesscombat": "մոլորությունճանապարհորդություն", "jaggedalliance2": "ծալքավորներդաշինք2", "neverwinter": "միշտահիմունք", "road96": "ճանապարհ96", "vtmb": "vtmb", "chimeraland": "խիմերալենդ", "homm3": "հոմմ3", "fe3h": "ֆե3հ", "roguelikes": "ռոգուլայքեր", "gothamknights": "գոթամիացներ", "forgottenrealms": "մոռացվածաշխարհներ", "dragonlance": "դրակոնիlance", "arenaofvalor": "արիանակառավարման", "ffxv": "ffxv", "ornarpg": "որնարպգ", "toontown": "տունտաուն", "childoflight": "լույսի_երեխա", "aq3d": "aq3d", "mogeko": "mog<PERSON><PERSON>", "thedivision2": "դիվիզիան2", "lineage2": "լայնեճ2", "digimonworld": "դիջիմոնաշխարհ", "monsterrancher": "մոնստերֆերմեր", "ecopunk": "էկոպանկ", "vermintide2": "վերմինտայդ2", "xeno": "զենո", "vulcanverse": "վулканվարուս", "fracturedthrones": "ճանապարհյալթագեր", "horizonforbiddenwest": "խորիզոնարգելվածարևմուտք", "twewy": "twewy", "shadowpunk": "թshadowpunk", "finalfantasyxv": "վերջինֆանտազիաxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "հոգվարդեմիստերիա", "deltagreen": "դելտաապրող", "diablo": "դիաբլո", "diablo3": "դիաբո3", "diablo4": "դիաբլո4", "smite": "սմայթ", "lastepoch": "վերջինմիջոցները", "starfinder": "աստղաստեղծող", "goldensun": "ոսկեգունեարև", "divinityoriginalsin": "բռնաբարելիդեպքից", "bladesinthedark": "հայելունումհյուսվածքներ", "twilight2000": "թtwilight2000", "sandevistan": "սանդեվիստան", "cyberpunk": "ցիփերպանկ", "cyberpunk2077": "ցիբերպանկ2077", "cyberpunkred": "ծաղրաշնորհամիտկարմիր", "dragonballxenoverse2": "դրագոնբոլքսենովերս2", "fallenorder": "ընկածհաջորդականություն", "finalfantasyxii": "վերջինֆանտազիաxii", "evillands": "վատաշխարհներ", "genshinimact": "գենշինիմակթ", "aethyr": "այդիր", "devilsurvivor": "դևերիապրող", "oldschoolrunescape": "հինդպրոցիվազք", "finalfantasy10": "վերջինֆանտազիա10", "anime5e": "անիմե5ե", "divinity": "երեսմիանություն", "pf2": "pf2", "farmrpg": "հողագործrpg", "oldworldblues": "ծերաշխարհիամխեցումը", "adventurequest": "արկածայինմենegment", "dagorhir": "դագոռհիր", "roleplayingames": "նշադրողականխաղեր", "roleplayinggames": "թատրոնայինխաղեր", "finalfantasy9": "վերջինքոփելիւս9", "sunhaven": "արևшայք", "talesofsymphonia": "սիմֆոնիահեքիքներ", "honkaistarrail": "հոնկանստարլ", "wolong": "վոլոնգ", "finalfantasy13": "պատերազմիֆանտազիա13", "daggerfall": "դագերֆոլл", "torncity": "փլուզվածքաղաք", "myfarog": "իմֆարող", "sacredunderworld": "փոքրիկսրբավայր", "chainedechoes": "շղթայվածհնչյուններ", "darksoul": "մութգիրդ", "soulslikes": "սոուլսլայկս", "othercide": "այլասեր", "mountandblade": "լեռներնուդարբացի", "inazumaeleven": "ինազումաելևեն", "acvalhalla": "ակվալհալա", "chronotrigger": "հրապարակիժամանակ", "pillarsofeternity": "երաժշտություն_դիտողություն", "palladiumrpg": "պալադիումrpg", "rifts": "բաժանումներ", "tibia": "տիբիա", "thedivision": "բաժանում", "hellocharlotte": "հելլոչարլոտ", "legendofdragoon": "թագավորությունիսկյուաբուլ", "xenobladechronicles2": "քսենոբլեյդכרոնիքլս2", "vampirolamascarada": "վամպիրաբանալի", "octopathtraveler": "օկտոպաթսայթող", "afkarena": "աֆկարենա", "werewolftheapocalypse": "լվախորդիապոկալիպսը", "aveyond": "ավեյոնդ", "littlewood": "փոքրիկտուն", "childrenofmorta": "մորտայիերեխաներ", "engineheart": "մոտոցիկլիսիրտ", "fable3": "ֆեիբլ3", "fablethelostchapter": "ֆաբուլաանհետացածբառարան", "hiveswap": "հայվսվափ", "rollenspiel": "ռոլպլեյ", "harpg": "հարփգ", "baldursgates": "բալդուրսգեյթս", "edeneternal": "էջմանմահeterna", "finalfantasy16": "վերջինֆանտազիա16", "andyandleyley": "անդիևլեյլի", "ff15": "ֆֆ15", "starfield": "աստղադաշտ", "oldschoolrevival": "հինդպրոցվերականգնում", "finalfantasy12": "ֆինալֆանտազիա12", "ff12": "ֆֆ12", "morkborg": "մորկբորգ", "savageworlds": "հրեշավորաշխարհներ", "diabloiv": "դիաբլոiv", "pve": "pve", "kingdomheart1": "միապետություն1", "ff9": "ֆֆ9", "kingdomheart2": "թագավորությանսրտերը2", "darknessdungeon": "մութիանդատար", "juegosrpg": "juegosrpg", "kingdomhearts": "թագավորությանսրտերը", "kingdomheart3": "երկրագնդիեսերություն3", "finalfantasy6": "վերջինֆանտազիա6", "ffvi": "ֆֆվի", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "հնձելլա", "gloomhaven": "գլումհևեն", "wildhearts": "հալածանքներ", "bastion": "բաստիոն", "drakarochdemoner": "դրակարանուխագայք", "skiesofarcadia": "արկադիայիամպեր", "shadowhearts": "հայտնիրտեր", "nierreplicant": "նյերռեպլիկանտ", "gnosia": "gnosia", "pennyblood": "պեննիարյուն", "breathoffire4": "հրաբուխլարիսչ4", "mother3": "մայր3", "cyberpunk2020": "կիբերպանկ2020", "falloutbos": "ֆոլաուտբոս", "anothereden": "մեկայլէդեն", "roleplaygames": "ռոմանավառխաղեր", "roleplaygame": "հայեցակարգխաղ", "fabulaultima": "ֆաբուլլաուտիմա", "witchsheart": "հաղորդագրությունսրտերը", "harrypottergame": "հարիփոթերհամpermainan", "pathfinderrpg": "ուղենշիչrpg", "pathfinder2e": "ուղեցույց2e", "vampirilamasquerade": "վամպիրինարազառանդագրություն", "dračák": "դրաստակ", "spelljammer": "մոգականչափիչ", "dragonageorigins": "ստանպշլիաշխարհ", "chronocross": "ժամաչափհատումն", "cocttrpg": "cocttrpg", "huntroyale": "հանդեսհայաստան", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "արհեստականմոնտրներիաշխարհ", "bg3": "bg3", "xenogear": "քսենոգար", "temtem": "թեմթեմ", "rpgforum": "rpgֆորում", "shadowheartscovenant": "ամպերահարութիւնսմէջ", "bladesoul": "բլեյդսոուլ", "baldursgate3": "բալդուրսգեյթ3", "kingdomcome": "թագավորությանժամանակը", "awplanet": "անվտանգլplanet", "theworldendswithyou": "աշխարհըհիմենկարդինքتեքուզպրոցնեմեսիգի", "dragalialost": "դրագալիալոստ", "elderscroll": "ծերաստղեր", "dyinglight2": "մահացողլույս2", "finalfantasytactics": "վերջինֆանտազիականտանելիքներ", "grandia": "գրանդիա", "darkheresy": "մութհերձականություն", "shoptitans": "դրժթպադրս", "forumrpg": "ֆորումrpg", "golarion": "golarion", "earthmagic": "երկրայինհործություն", "blackbook": "սևգիրք", "skychildrenoflight": "հանրայինլույսերիիդեազարկերներ", "gryrpg": "գրելrpg", "sacredgoldedition": "սուրբարծաթէջեր", "castlecrashers": "շղարշուկներ", "gothicgame": "գոթականխաղ", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "գողնալուստեթոկիո", "fallout2d20": "fallout2d20", "gamingrpg": "խաղերrpg", "prophunt": "պրոֆհանդ", "starrails": "աստղագիրքեր", "cityofmist": "մառախուղիքաղաք", "indierpg": "ինքնուրույնrpg", "pointandclick": "bետևևստացիր", "emilyisawaytoo": "էմիլինհեռուա", "emilyisaway": "էմիլիատսaway", "indivisible": "չմասնատելի", "freeside": "ազատկողմ", "epic7": "էպիկ7", "ff7evercrisis": "ff7միշտճգնաժամ", "xenogears": "զենոգեարքեր", "megamitensei": "մեգամիթենսեյ", "symbaroum": "սիմբարում", "postcyberpunk": "փոստցիբերպունկ", "deathroadtocanada": "մահվանճանապարհկանադա", "palladium": "պալադիում", "knightjdr": "ճկարդjdr", "monsterhunter": "մարամասիորսորդ", "fireemblem": "հրադարան", "genshinimpact": "գենշինինպակտ", "geosupremancy": "գեոսուպրեմացիա", "persona5": "բերսի5", "ghostoftsushima": "ճառագրվածցուցման", "sekiro": "սեկիրո", "monsterhunterrise": "մոնստրապարգևելարձում", "nier": "նիեր", "dothack": "դոտհեք", "ys": "բյուզ", "souleater": "հոգակեր", "fatestaynight": "հետադիմականլույս", "etrianodyssey": "էտրիանոդեսի", "nonarygames": "ոչանորդախաղեր", "tacticalrpg": "տակտիկականrpg", "mahoyo": "մահոյո", "animegames": "անիմեխամներ", "damganronpa": "դամգանռոնぱ", "granbluefantasy": "գրանբլյուֆանտզի", "godeater": "գործիքեր", "diluc": "դիլուկ", "venti": "վենտի", "eternalsonata": "հավերժականսոնատա", "princessconnect": "princessconnect", "hexenzirkel": "գաղտնիքասոսաեր", "cristales": "կրիստալներ", "vcs": "վցs", "pes": "պես", "pocketsage": "արկղիկխիմիկ", "valorant": "վալորանտ", "valorante": "վալորանտ", "valorantindian": "վալորանտհնդկական", "dota": "դոտա", "madden": "մադեն", "cdl": "cdl", "efootbal": "էֆուտբոլ", "nba2k": "nba2k", "egames": "հգիտելիքներ", "fifa23": "ֆիֆա23", "wwe2k": "wwe2k", "esport": "էսպորտ", "mlg": "mlg", "leagueofdreamers": "ապակուրկերիանդամներ", "fifa14": "ֆիֆա14", "midlaner": "միջինխաղացող", "efootball": "էֆուտբոլ", "dreamhack": "երազներիբանեցում", "gaimin": "գեյմին", "overwatchleague": "օվերշադլիգնակի", "cybersport": "ցիբերսպորտ", "crazyraccoon": "աղմկոտռակուն", "test1test": "ընթերցում1ընթերցում", "fc24": "fc24", "riotgames": "ռայոթխաղեր", "eracing": "էռեյսինգ", "brasilgameshow": "բրազիլյանխաղերիցուցահանդես", "valorantcompetitive": "վալորանտմրցակցային", "t3arena": "t3arena", "valorantbr": "վալորանտբռ", "csgo": "csgo", "tf2": "tf2", "portal2": "պորտալ2", "halflife": "համընկնում", "left4dead": "հաղթահարեցված", "left4dead2": "մահվանիցհայտնվելուվերադարձ2", "valve": "վալվ", "portal": "պորտալ", "teamfortress2": "տիմփորսես2", "everlastingsummer": "մeternoամառ", "goatsimulator": "արածոենգիշակ", "garrysmod": "գարիրսմոդ", "freedomplanet": "ազատությանլուսին", "transformice": "տրավանսֆորմիս", "justshapesandbeats": "միասինձևերիմուսիկաներ", "battlefield4": "ուղեղամարտ4", "nightinthewoods": "գիշերումասերում", "halflife2": "հաղթահարումը2", "hacknslash": "հաքնսլեշ", "deeprockgalactic": "դիպրավոքգալակտիկ", "riskofrain2": "հնարավորությունըմաքրին2", "metroidvanias": "մետրոիդվանիաներ", "overcooked": "գերէջված", "interplanetary": "միջմոլորակային", "helltaker": "հետաքրքիրվերցրու", "inscryption": "ինսկրիպցիա", "7d2d": "7d2d", "deadcells": "մահացածբջիջներ", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "փոքրիկֆեստիվալ", "foxhole": "ցուլաքում", "stray": "ոչխար", "battlefield": "հարվածավայր", "battlefield1": "բաթլֆիլդ1", "swtor": "swtor", "fallout2": "ֆոլաուտ2", "uboat": "ուբոտ", "eyeb": "տեսանելությունն", "blackdesert": "սևաբլուր", "tabletopsimulator": "թագուհhidratlıqցին", "partyhard": "партиևաուthereyougo", "hardspaceshipbreaker": "բարդաստղերանցաչափիչ", "hades": "հադես", "gunsmith": "հրաձիգ", "okami": "օկամի", "trappedwithjester": "հարվածելեքջեսթերին", "dinkum": "դինքում", "predecessor": "նախորդ", "rainworld": "տափաթողաշխարհ", "cavesofqud": "քանդաքաղզեղերի", "colonysim": "քոլոնիասիմ", "noita": "noita", "dawnofwar": "զարթոնքիպատերազմին", "minionmasters": "մինիոնվարպետներ", "grimdawn": "գրիմժամվա", "darkanddarker": "սևևմառախառը", "motox": "մոտոկս", "blackmesa": "սևmesa", "soulworker": "հոգեցարար", "datingsims": "դեդինգսիմս", "yaga": "յագա", "cubeescape": "կյուբիփescaping", "hifirush": "հիֆիրուշ", "svencoop": "սվենկոպ", "newcity": "նորքաղաք", "citiesskylines": "քաղաքահորիզոններ", "defconheavy": "դեֆկոնհեբի", "kenopsia": "կենոպսիա", "virtualkenopsia": "վիրտուալկինոպսիա", "snowrunner": "սնոուռներ", "libraryofruina": "գրադարանըռուինա", "l4d2": "l4d2", "thenonarygames": "չարահարկախաղեր", "omegastrikers": "օմեգաստրայքեր", "wayfinder": "ուղեցույց", "kenabridgeofspirits": "կենաբրիիջօղոգիների", "placidplasticduck": "աղվամաքրակեղև", "battlebit": "battlebit", "ultimatechickenhorse": "վերջինաղաֆարստեր", "dialtown": "դայլթաուն", "smileforme": "ծիծաղիրիմարևման", "catnight": "կատուգիշեր", "supermeatboy": "սուպերմիսեռտղա", "tinnybunny": "տինիմուկա", "cozygrove": "կոպիտգման", "doom": "դոմ", "callofduty": "զանգելուպաշտպանություն", "callofdutyww2": "քաղաքվածությունըww2", "rainbow6": "գերագույն6", "apexlegends": "ապեքսлегенդս", "cod": "կոդ", "borderlands": "սահմանապատեր", "pubg": "pubg", "callofdutyzombies": "զանգակոչելէլինքնածիներ", "apex": "ապեքս", "r6siege": "r6siege", "megamanx": "մեգամանx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "հեռավորականչ", "farcrygames": "հեռավորամարտեր", "paladins": "պալադիններ", "earthdefenseforce": "երկրիապաշտպանուժ", "huntshowdown": "հետախուզություն", "ghostrecon": "երկրիանցներգուն", "grandtheftauto5": "գրանդթևթաու5", "warz": "պատերազմz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "օրհնվածսպանություն", "joinsquad": "միացիրխմբին", "echovr": "էչովռ", "discoelysium": "դիսքոյլիզիում", "insurgencysandstorm": "բռնիամվռնչեցմանալուորամիրթյանեռստի", "farcry3": "ֆարքրայ3", "hotlinemiami": "հոթլինեմայամի", "maxpayne": "մաքսփեյն", "hitman3": "նախագահների3", "r6s": "r6s", "rainbowsixsiege": "ռեյնբոուսիքսսիիջ", "deathstranding": "գործարանըզոհված", "b4b": "b4b", "codwarzone": "կոդզոդաբանություն", "callofdutywarzone": "կոչէքստիպումյոդզոն", "codzombies": "կոդզոմբիներ", "mirrorsedge": "երբիզմիեզր", "divisions2": "մասքեր2", "killzone": "սպանածէլքը", "helghan": "հելղյան", "coldwarzombies": "ցուրտպատերազմզոմբիներ", "metro2033": "մետոտրո2033", "metalgear": "մետալգյար", "acecombat": "էյսկոմբատ", "crosscode": "միջանցայինկոդ", "goldeneye007": "ոսկրեալեզու007", "blackops2": "սևործւթյուններ2", "sniperelite": "զինագործէլիտ", "modernwarfare": "հետաքրքրաշարայինպատերազմ", "neonabyss": "նեոնաբերանի", "planetside2": "planetside2", "mechwarrior": "մեխպայքարող", "boarderlands": "տախտակներիաշխարհ", "owerwatch": "օվերչաթ", "rtype": "rtype", "dcsworld": "dcsաշխարհ", "escapefromtarkov": "հեռանումէրստարկով", "metalslug": "մետաղաբոկը", "primalcarnage": "պրայմալկարնեջ", "worldofwarships": "սպառազինություններիաշխարհ", "back4blood": "հետադարձ4արյուն", "warframe": "wargframe", "rainbow6siege": "հասմիկ6շարժում", "xcom": "էքսքոմ", "hitman": "հիթմեն", "masseffect": "մասսայինազդեցություն", "systemshock": "համակարգայինշոկ", "valkyriachronicles": "վալկիրիանոսագրերը", "specopstheline": "սպեկօպսդիրքին", "killingfloor2": "killingfloor2", "cavestory": "հ洞առողությանպատմություն", "doometernal": "դումէտերնալ", "centuryageofashes": "դարիափոշուոտներկ", "farcry4": "ֆարքրայ4", "gearsofwar": "պատերազմիաուժեր", "mwo": "mwo", "division2": "դիվիզիա2", "tythetasmaniantiger": "տիթհետասմանյանսագուրը", "generationzero": "երրորդականսերունդ", "enterthegungeon": "փոխիրամուսացանկը", "jakanddaxter": "ջականդդաքսթեր", "modernwarfare2": "մոդեռնպատերազմ2", "blackops1": "սևգործությունները1", "sausageman": "վառեցրածղեկ", "ratchetandclank": "ռատչետաեվկլանկ", "chexquest": "չեխերևլու", "thephantompain": "հոգվաֆանտոմ", "warface": "պատերազմայինդեմք", "crossfire": "կռվիմեջ", "atomicheart": "ատոմիկսիրտ", "blackops3": "սևպoperación3", "vampiresurvivors": "վամպիրներիապրsurviving", "callofdutybatleroyale": "գրավեքհաղթանակըboo", "moorhuhn": "մուռխուհն", "freedoom": "ազատություն", "battlegrounds": "մարտահրավերներ", "frag": "ֆրագ", "tinytina": "միկրոտինա", "gamepubg": "խաղpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "մետալգիրզորդիներիազատության", "juegosfps": "fpsխաղեր", "convertstrike": "փոխարկումահ至少", "warzone2": "հետախուզական2", "shatterline": "շատերլայն", "blackopszombies": "սևգործողություններզոմբիներ", "bloodymess": "արյունոտթափոթի", "republiccommando": "բանակայինհրամանիչ", "elitedangerous": "էլիտադangerous", "soldat": "soldat", "groundbranch": "գրունդբրանչ", "squad": "հարաբերություններ", "destiny1": "հորիզոն1", "gamingfps": "գեյմինգֆփս", "redfall": "կարմիքենի", "pubggirl": "pubgkız", "worldoftanksblitz": "աշխարհիփռթյուններբլից", "callofdutyblackops": "հրահանգումստղիապնաև", "enlisted": "գրանցված", "farlight": "ֆառլայթ", "farcry5": "առաջնորդություն5", "farcry6": "ապվածպայքար6", "farlight84": "բարձրնակար84", "splatoon3": "սփլատուն3", "armoredcore": "բանակցայինշարժում", "pavlovvr": "pawlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "միկրոատիբույսերին", "halo2": "հալո2", "payday2": "շաբաթվավերջ2", "cs16": "cs16", "pubgindonesia": "pubgհայաստան", "pubgukraine": "pubgհայաստան", "pubgeu": "պաբգեւ", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgհայաստան", "empyrion": "էմպիրիոն", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "թիթեռուժ2", "soapcod": "սապոկոդ", "ghostcod": "բզիկկոդ", "csplay": "csplay", "unrealtournament": "անհավատալիփառատոն", "callofdutydmz": "բոյկայինկոչեալռազմեստրոն", "gamingcodm": "gamingcodm", "borderlands2": "սահմանիանց2", "counterstrike": "վտանգվածություն", "cs2": "cs2", "pistolwhip": "պիստոլհիպ", "callofdutymw2": "կոչելիդունիmw2", "quakechampions": "ցնցումիոքուպանտներ", "halo3": "հալո3", "halo": "հալո", "killingfloor": "սպանարարահող", "destiny2": "ախտագիր2", "exoprimal": "էքսոպրայմալ", "splintercell": "սպլինտերտվածուտեր", "neonwhite": "չափայտսպիտակ", "remnant": "մնացորդ", "azurelane": "ազուրլեյն", "worldofwar": "շնորհալիաշխարհ", "gunvolt": "գանվոլտ", "returnal": "վերադարձային", "halo4": "հալո4", "haloreach": "հաղորդակցություն", "shadowman": "հոգեբանահայր", "quake2": "երկրաշարժ2", "microvolts": "միկրովոլտներ", "reddead": "կինգսանուք", "standoff2": "standoff2", "harekat": "հայկականհարեկատ", "battlefield3": "հարվածայինպաշտպանություն3", "lostark": "կորչածարգ", "guildwars2": "գիլդվարս2", "fallout76": "ֆոլաուտ76", "elsword": "էլսվորդ", "seaofthieves": "ծովիգողեր", "rust": "ռուսթ", "conqueronline": "գաղափարավերևում", "dauntless": "ամբարտավան", "warships": "հրաժիռներ", "dayofdragons": "քայլըգազանների", "warthunder": "պատերազմհրթիռ", "flightrising": "թռիչքիաջընկեր", "recroom": "ռեկռում", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "միստեր", "phantasystaronline2": "ֆանտաստիկաստարոնլայն2", "maidenless": "նկարագրությունչունեցող", "ninokuni": "նիночունի", "worldoftanks": "թանկերիաշխարհ", "crossout": "տրոշել", "agario": "ագարիո", "secondlife": "երկրափոփոխություն", "aion": "aion", "toweroffantasy": "հեքիաթներիիաթոռ", "netplay": "նետխաղ", "everquest": "էկրանայինորոնում", "metin2": "մետին2", "gtaonline": "gtaonline", "ninokunicrossworld": "նինոքունիcrossworld", "reddeadonline": "կարմիրմահիճանտեղում", "superanimalroyale": "հրաշագործկենդանիներ", "ragnarokonline": "ռագնածրակոնլայն", "knightonline": "կալվածքիտեսակով", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "իսահակիառևանգումը", "dragonageinquisition": "դրագոնիաժամկետ_հարցնում", "codevein": "կոդարյուն", "eveonline": "էվենլայն", "clubpenguin": "գործարանեփրիկ", "lotro": "լոտրո", "wakfu": "վակֆու", "scum": "թափթփություն", "newworld": "նորաշխարհ", "blackdesertonline": "անշուշտբոլորսմեկտեղblackdesertonlineիմեջենք", "multiplayer": "բազմանվագ", "pirate101": "փիրատ101", "honorofkings": "հարգելութագավորությունը", "fivem": "ֆայվեմ", "starwarsbattlefront": "աստղայինպատերազմներարտադրություն", "karmaland": "կարմալանդ", "ssbu": "ssbu", "starwarsbattlefront2": "աստղայինլուռերիվալբեթլֆրոնթ2", "phigros": "ֆիգրոս", "mmo": "մմո", "pokemmo": "փոկեմմո", "ponytown": "պոնիաքաղաք", "3dchat": "3դհաղորդակցություն", "nostale": "նոստալե", "tauriwow": "tauriwow", "wowclassic": "վովկլասիկ", "worldofwarcraft": "աշխարհմտագործություն", "warcraft": "պատերազմականարհեստ", "wotlk": "wotlk", "runescape": "ռունոմֆեսք", "neopets": "նեոպետս", "moba": "մոբա", "habbo": "հաբբո", "archeage": "արխեժեյջ", "toramonline": "toramonline", "mabinogi": "մաբինոգի", "ashesofcreation": "creationիփոշիներ", "riotmmo": "ռեակցիաmmo", "silkroad": "աթենյանապրանքներ", "spiralknights": "սպիրալաձենով骑士们", "mulegend": "մուլեգենդ", "startrekonline": "ստարտրեքօնլայն", "vindictus": "վինդիկտուս", "albiononline": "ալբիոնօնլայն", "bladeandsoul": "bladeandsoul", "evony": "եվոնի", "dragonsprophet": "դրակոններիուեստը", "grymmo": "գրիմմո", "warmane": "ջերմանալ", "multijugador": "բազմալուսանք", "angelsonline": "արտիստներանլայն", "lunia": "լունիա", "luniaz": "լունիազ", "idleon": "<PERSON>on", "dcuniverseonline": "dcամիաշխարհ", "growtopia": "գրոուտոպիա", "starwarsoldrepublic": "աստղայինպատերազմներհինհանրապետություն", "grandfantasia": "մեծֆանտազիա", "blueprotocol": "երկնագույնպրոտոկոլ", "perfectworld": "հիանալիաշխարհ", "riseonline": "նստածելլինց", "corepunk": "կորեփանկ", "adventurequestworlds": "ձեռնարկություններիցաշխարհներ", "flyforfun": "թռչիուրախությամբ", "animaljam": "փետուրախաղ", "kingdomofloathing": "նենգությանթագավորություն", "cityofheroes": "ևրոպայիքաղաք英雄", "mortalkombat": "մորտալկոմբատ", "streetfighter": "փողոցայինպատերազմարար", "hollowknight": "տարօրինակknight", "metalgearsolid": "մետալգիրսոլիդ", "forhonor": "հարգանքիհամար", "tekken": "տեքեն", "guiltygear": "բեղմնավորլիկ", "xenoverse2": "խոսնորմ2", "fgc": "ֆգց", "streetfighter6": "ճանապարհամարտիկ6", "multiversus": "մուլտիֆերս⅞", "smashbrosultimate": "սմաշբրոսուլտիմատ", "soulcalibur": "սոուլքալիբուր", "brawlhalla": "բրոուհալլա", "virtuafighter": "վիրտուալքնամարտիչ", "streetsofrage": "րևումաղարիտ", "mkdeadlyalliance": "mkհավերժապայքար", "nomoreheroes": "այլևսհերոսներչկան", "mhr": "mhr", "mortalkombat12": "մորթիչպատերազմ12", "thekingoffighters": "մարտականներիարքա", "likeadragon": "օրինակվեսարծիվիպես", "retrofightinggames": "հինպայքարիտեսքեր", "blasphemous": "ծաղրածու", "rivalsofaether": "առյուրակարիեմ", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "սուպերսմեշ", "mugen": "մուգեն", "warofthemonsters": "մոնստրներիպատերազմ", "jogosdeluta": "հավերժապայքար", "cyberbots": "սայբերբոտեր", "armoredwarriors": "զրահապատպատերազմավորներ", "finalfight": "վճռիչնամարտ", "poweredgear": "ուժեղծիչներ", "beatemup": "հարվածելտուգանальному", "blazblue": "բլազբլու", "mortalkombat9": "մորտալկանբատ9", "fightgames": "ճանապարհամարտեր", "killerinstinct": "կillerinstinct", "kingoffigthers": "ռոքքինգսպրողներ", "ghostrunner": "բուիմղձավանջ", "chivalry2": "հետաօրհնություն2", "demonssouls": "դեմոնսոուլս", "blazbluecrosstag": "բլազբլույկորսթագ", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "բլազբլուքսթագ", "guiltygearstrive": "հանցագործախաղավիճակ", "hollowknightsequel": "հալովիդաստուորboo", "hollowknightsilksong": "հալոքնայթսիլքսոնг", "silksonghornet": "սիլքսոնգհոռնեթ", "silksonggame": "սիլքսոնգխաղ", "silksongnews": "սիլքսոնգնորություններ", "silksong": "սիլքսոնգ", "undernight": "գիշերում", "typelumina": "թայպելյումինա", "evolutiontournament": "առաջադիմությանմրցաշար", "evomoment": "եվոմոմենտ", "lollipopchainsaw": "լոլիպոպշթորը", "dragonballfighterz": "դրագոնբոլֆայթերզ", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "արյունառաջող", "horizon": "հորիզոն", "pathofexile": "ուղիիեզրեր", "slimerancher": "սլայմարանչեր", "crashbandicoot": "կրաշբանդիկուտ", "bloodbourne": "արյունակիր", "uncharted": "անփառունակ", "horizonzerodawn": "հորիզոնզերօդաւն", "ps4": "ps4", "ps5": "ps5", "spyro": "սպայռո", "playstationplus": "պլեյստեյշնփլյուս", "lastofus": "վերջնիներս", "infamous": "անհայտ", "playstationbuddies": "պլեյսթեյշնինկերներ", "ps1": "ps1", "oddworld": "հասցեականաշխարհ", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "ռաբիդներ", "splitgate": "splitscape", "persona4": "պերսոնա4", "hellletloose": "հլլեթլուսնեցես", "gta4": "gta4", "gta": "gta", "roguecompany": "դավաճանածնկերություն", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "գթասանանդրեաս", "godofwar": "վռտիաստված", "gris": "գրիş", "trove": "դիմակագի", "detroitbecomehuman": "դետրոյտդարձիրմարդ", "beatsaber": "բիթսաբեր", "rimworld": "գլխփակում", "stellaris": "սթելլառիս", "ps3": "ps3", "untildawn": "միջանցիկյան_ժամանակ", "touristtrophy": "տուրիստականթիվեր", "lspdfr": "լսպդֆռ", "shadowofthecolossus": "գոլոսոսիստվերում", "crashteamracing": "ճեղքմանկոմանդայինվազք", "fivepd": "հինգpd", "tekken7": "տեկեն7", "devilmaycry": "շայտախառնություն", "devilmaycry3": "դևնինչորկարծեսթե3", "devilmaycry5": "շնորհիվդևերս5", "ufc4": "ufc4", "playingstation": "մանագետիկ", "samuraiwarriors": "սամուրայատերեր", "psvr2": "psvr2", "thelastguardian": "վերջինպահապանը", "soulblade": "հոգիակblade", "gta5rp": "գթա5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "մարդահետախուզություն", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "հետեւանտերինիթոց2հոգի", "pcsx2": "pcsx2", "lastguardian": "վերջինպաշտպան", "xboxone": "xboxone", "forza": "ֆորսա", "cd": "կոդ", "gamepass": "խաղանցում", "armello": "առմելլո", "partyanimal": "հոգեպեսզարդուկ", "warharmmer40k": "warhammer40k", "fightnightchampion": "քրեակայունիրվոթյաներեկո", "psychonauts": "փսիխոներ", "mhw": "mhw", "princeofpersia": "պարսկիահայրը", "theelderscrollsskyrim": "ծերերիփառքիսկայրիմ", "pantarhei": "պանտարեհի", "theelderscrolls": "ծերերիսcrolls", "gxbox": "gxbox", "battlefront": "հակամարտությանոլորտ", "dontstarvetogether": "շարունակենքկարոտելմեկտեղ", "ori": "ori", "spelunky": "սպելոնկի", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "աստղայինուղի", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "սקייט3", "houseflipper": "տունփոխող", "americanmcgeesalice": "ամերիկացի_mcgee_ալիսայում", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "լիեգքինգդոմս", "fable2": "ֆեյյբլ2", "xboxgamepass": "xboxgamepass", "undertale": "անդերտեյլ", "trashtv": "վատիառապատում", "skycotl": "կապրենքէկապույտներկինअर्थկապույտերկինք", "erica": "երիկան", "ancestory": "ծառայությունboo", "cuphead": "կափհեդ", "littlemisfortune": "մեծացավհետո", "sallyface": "սալիբերան", "franbow": "ֆրանբո", "monsterprom": "մոնստերպրոմ", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motoն<PERSON>ր", "outerwilds": "արտաքինհwilde", "pbbg": "pbbg", "anshi": "անսի", "cultofthelamb": "լամբիֆուռջերմոց", "duckgame": "ընկուզախաղ", "thestanleyparable": "ստենլիպարաբոլ", "towerunite": "թումարունիտ", "occulto": "օկուլտո", "longdrive": "երկերկարճանապարհ", "satisfactory": "համբավելի", "pluviophile": "ջրասեր", "underearth": "երկրային", "assettocorsa": "ասեթտոքնորա", "geometrydash": "ագանրալեզու", "kerbal": "կերբալ", "kerbalspaceprogram": "կերահարspaceprogram", "kenshi": "կենշի", "spiritfarer": "սպիրիթֆեր", "darkdome": "տավարանտյան", "pizzatower": "պիցզատուն", "indiegame": "ինդիխաղ", "itchio": "itchio", "golfit": "golfit", "truthordare": "ճիշտուղելախիզընդանդիր", "game": "խաղ", "rockpaperscissors": "ռոքաթղթեիակ", "trampoline": "տրամպլին", "hulahoop": "հուլահոպ", "dare": "հետառուկ", "scavengerhunt": "սպասարկումիորսություն", "yardgames": "դատարկաշխարհներ", "pickanumber": "ընտրիրհամարը", "trueorfalse": "ճշմարիտկամխոստացած", "beerpong": "բիրբողուշ", "dicegoblin": "հոգեգլուխ", "cosygames": "հարմարխաղեր", "datinggames": "սիրահարությունիխաղեր", "freegame": "շնորհյալխաղ", "drinkinggames": "խմելուխաղեր", "sodoku": "սոդոկու", "juegos": "խաղեր", "mahjong": "մահջոնգ", "jeux": "խաղեր", "simulationgames": "համակարգմանխաղեր", "wordgames": "առնչվողխաղեր", "jeuxdemots": "խաղաբառեր", "juegosdepalabras": "խաղերաբառերով", "letsplayagame": "գնանքխաղալխաղ", "boredgames": "հեռելախաղեր", "oyun": "ոինգ", "interactivegames": "ինտերակտիվխաղեր", "amtgard": "ամթգարդ", "staringcontests": "բացահայտմանմրցույթներ", "spiele": "խաղեր", "giochi": "կարկատուներ", "geoguessr": "երկրաչափություն", "iphonegames": "այֆոնախաղեր", "boogames": "բուգեյմերներ", "cranegame": "քցածախաղ", "hideandseek": "թաքնվելուներնեւհետեւելը", "hopscotch": "հեփսկոճ", "arcadegames": "արխադայինխաղեր", "yakuzagames": "yakuzagames", "classicgame": "դասականխաղ", "mindgames": "մտածմունքներ", "guessthelyric": "գուշակիերգը", "galagames": "գալագեյմս", "romancegame": "սիրայինխաղ", "yanderegames": "յանդերեխաղեր", "tonguetwisters": "լեզվակապեր", "4xgames": "4xխաղեր", "gamefi": "խաղֆայ", "jeuxdarcades": "երեւակայմանծաղիկներ", "tabletopgames": "թղթեխաղեր", "metroidvania": "մետրոյդվանիա", "games90": "խաղեր90", "idareyou": "եսքումիացնես", "mozaa": "մոզաա", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "արշավայինխաղեր", "ets2": "էտս2", "realvsfake": "հարուստսուպեկեղծ", "playgames": "խաղալխաղեր", "gameonline": "խաղայինցանց", "onlinegames": "օնլայնխաղեր", "jogosonline": "օնլայնխաղեր", "writtenroleplay": "գրածռոլփլեյ", "playaballgame": "խաղանքմոտանը", "pictionary": "պիկշենարի", "coopgames": "գործընկերականխաղեր", "jenga": "ջենգա", "wiigames": "վիիգեյմերներ", "highscore": "բարձրմիավոր", "jeuxderôles": "ռոլայինխաղեր", "burgergames": "բուրգերախաղեր", "kidsgames": "մանկականխաղեր", "skeeball": "սկիբոլ", "nfsmwblackedition": "nfsmwսևտիրություն", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "խաղուրեմիհարցեր", "gioco": "խաղ", "managementgame": "հավաքմանխաղ", "hiddenobjectgame": "թաքնվածառobjectgame", "roolipelit": "ռուլիպելիտ", "formula1game": "ֆորմուլա1խաղ", "citybuilder": "քաղաքաշինող", "drdriving": "դրիվինգ", "juegosarcade": "ամենագլուխ", "memorygames": "հիշողությանխաղեր", "vulkan": "վulkan", "actiongames": "գործողություններիխաղեր", "blowgames": "բլոուհանգոյներ", "pinballmachines": "փինբոլمեքենաներ", "oldgames": "հինխաղեր", "couchcoop": "կառուցապատիրտեսակետը", "perguntados": "հարցվածներ", "gameo": "խաղո", "lasergame": "լազերայինխաղ", "imessagegames": "իմեջսխաղեր", "idlegames": "կոտրածխաղեր", "fillintheblank": "լրիրթույլափականը", "jeuxpc": "պցաղցեր", "rétrogaming": "հինխաղեր", "logicgames": "արգիլահոգեբանականխաղեր", "japangame": "ژاپոնյանխաղ", "rizzupgame": "rizzupgame", "subwaysurf": "հետուսումբաճ", "jeuxdecelebrite": "սիրվածխաղեր", "exitgames": "մուտքցուցակներ", "5vs5": "5vs5", "rolgame": "երաժշտականխաղ", "dashiegames": "դաշիգեյմս", "gameandkill": "խաղաևգրավիր", "traditionalgames": "ավանդականխաղեր", "kniffel": "կնիֆել", "gamefps": "խաղերիֆպս", "textbasedgames": "տեքստաբազխաղեր", "gryparagrafowe": "գրի_պարագրաֆային", "fantacalcio": "ֆանտաֆուտբոլ", "retrospel": "ռետրոսկաֆ", "thiefgame": "գողությունխաղ", "lawngames": "լանջախաղեր", "fliperama": "ֆլիպերամա", "heroclix": "հերոքումեր", "tablesoccer": "թենդռակ", "tischfußball": "տիշֆուսբոլ", "spieleabende": "խաղերիերեկոներ", "jeuxforum": "jeuֆորում", "casualgames": "խաղարկասէրեր", "fléchettes": "ֆլեշետներ", "escapegames": "փախուստիխաղեր", "thiefgameseries": "թերիխաղերիշարք", "cranegames": "կրանիխաղեր", "játék": "բախում", "bordfodbold": "բորդֆուտբոլ", "jogosorte": "ջոգոսորտ", "mage": "մագե", "cargames": "մեքենախաղեր", "onlineplay": "աոնլայնխաղ", "mölkky": "մյոլկի", "gamenights": "խաղիգիշերներ", "pursebingos": "պարկմանպատումներ", "randomizer": "հիմարացնող", "msx": "մսխ", "anagrammi": "անագրամմի", "gamespc": "խաղերpc", "socialdeductiongames": "սոցիալականհավաքածուփաթեներ", "dominos": "դոմինոս", "domino": "դոմինո", "isometricgames": "իզոմետրիկխաղեր", "goodoldgames": "լավհինխաղեր", "truthanddare": "ճիշտերսուպեր", "mahjongriichi": "մահջոնգռիչի", "scavengerhunts": "ամփոփիչորսահավաքներ", "jeuxvirtuel": "առցանցխաղեր", "romhack": "ռոմհակ", "f2pgamer": "f2pbօղղող", "free2play": "բացառապեսխաղալ", "fantasygame": "մանացածխաղ", "gryonline": "գրիօնլայն", "driftgame": "խորտուկխաղ", "gamesotomes": "գեյմսոտոմես", "halotvseriesandgames": "հալոտվսերիալներնուխաղերը", "mushroomoasis": "մուշարանումհանգստավայր", "anythingwithanengine": "ինչորոքշարժիչով", "everywheregame": "բոլորաթերթումխաղ", "swordandsorcery": "կտրիչևմոգություն", "goodgamegiving": "լավաղյումներ", "jugamos": "խաղումենք", "lab8games": "լաբ8խաղեր", "labzerogames": "լաբզրոնկախումներ", "grykomputerowe": "գրումփյութեր", "virgogami": "վիրոգամի", "gogame": "gogame", "jeuxderythmes": "թեմականխաղեր", "minaturegames": "մինիատյուրըխաղեր", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "սիրտսսիրումհաղորդակցում", "gamemodding": "խաղայինմոդավորում", "crimegames": "հանցագործություններիխաղեր", "dobbelspellen": "dobbelspellen", "spelletjes": "խաղեր", "spacenerf": "սպեյսներֆ", "charades": "շարաջներ", "singleplayer": "միակխաղացող", "coopgame": "համագործակցայինխաղ", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "նեքսուս", "geforcenow": "geforcenow", "maingame": "հիմնականխաղ", "kingdiscord": "թագավորդիսկորդ", "scrabble": "սկրաբլ", "schach": "շախ", "shogi": "շոգի", "dandd": "դանդդ", "catan": "կատարան", "ludo": "լուդո", "backgammon": "բակգամոն", "onitama": "օնիտամա", "pandemiclegacy": "համաշխարհայինտարրքեր", "camelup": "ուղտափաթաթ", "monopolygame": "մենաշնորհախաղ", "brettspiele": "խաղաբառեր", "bordspellen": "խաղերիալեր", "boardgame": "բորդգեյմ", "sällskapspel": "խաղեր", "planszowe": "խաղաթղթային", "risiko": "րիսկո", "permainanpapan": "խաղադաշտ", "zombicide": "զոմբիցիդ", "tabletop": "սեղանին", "baduk": "բադուկ", "bloodbowl": "արյունոտգավաթ", "cluedo": "կլուդո", "xiangqi": "շախմատ", "senet": "սենեթ", "goboardgame": "գոբորդիխաղ", "connectfour": "համակցելչորս", "heroquest": "հերոսավանդույթ", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "ֆարկլ", "carrom": "կարոմ", "tablegames": "խաղայինսեղաններ", "dicegames": "խաղաթղթերով", "yatzy": "յացին", "parchis": "պարչիս", "jogodetabuleiro": "խաղայինհամակարգ", "jocuridesocietate": "հասարակականխաղեր", "deskgames": "թենդերխաղեր", "alpharius": "ալֆարիուս", "masaoyunları": "մասաբռնել", "marvelcrisisprotocol": "marvelkrizinprotokol", "cosmicencounter": "կոսմիկականնշման遇见", "creationludique": "ստեղծագործականուրախություն", "tabletoproleplay": "ճաշակայիներիանդերձմանձև", "cardboardgames": "սարքածխաղեր", "eldritchhorror": "ելդրիչհτρόվը", "switchboardgames": "switchboardխաղեր", "infinitythegame": "բացարձակագնդիխաղը", "kingdomdeath": "գրմոնքիմահ", "yahtzee": "յահցի", "chutesandladders": "խորխորդներորեագույներ", "társas": "սոցիալ", "juegodemesa": "սեղանիխաղ", "planszówki": "պլանզովքներ", "rednecklife": "կարմիրթաթաբնակություն", "boardom": "բորդոմ", "applestoapples": "խնձորիցխնձոր", "jeudesociété": "սոցիալականխաղեր", "gameboard": "խաղատախտակ", "dominó": "դոմինո", "kalah": "կալահ", "crokinole": "կրոկինոլ", "jeuxdesociétés": "օլիմպիականխաղեր", "twilightimperium": "երեկոյանամենամարտ", "horseopoly": "ասպատակոլիա", "deckbuilding": "դեկտբիլդինգ", "mansionsofmadness": "խենթերիքնիքունքներ", "gomoku": "գոմոկու", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "մութերիամբրեցրեր", "kingoftokyo": "երևանիակառավարիչ", "warcaby": "վարկաբի", "táblajátékok": "թմբկահարներ", "battleship": "բու_հասարակած", "tickettoride": "թռիչքիֆոնդ", "deskovehry": "եղանակիշուկա", "catán": "կատան", "subbuteo": "սուբբուտեո", "jeuxdeplateau": "խաղահավաք", "stolníhry": "ստոլնիհրեր", "xiángqi": "շախմատ", "jeuxsociete": "խաղերիպատկերասրահ", "gesellschaftsspiele": "հասարակականխաղեր", "starwarslegion": "աստղերիապատակառյուծ", "gochess": "գոlխես", "weiqi": "վեյչի", "jeuxdesocietes": "հասարակականխաղեր", "terraria": "terraria", "dsmp": "dsmp", "warzone": "պատերազմիինքնաչափություն", "arksurvivalevolved": "արկsurvivalevolved", "dayz": "օրեր", "identityv": "ճանաչողական", "theisle": "հայաստան", "thelastofus": "վերջինմերից", "nomanssky": "ոչմիտքկրիտիկ", "subnautica": "սուբնաուտիկան", "tombraider": "տամբրայդեր", "callofcthulhu": "կոչիստղկան", "bendyandtheinkmachine": "կրաֆիկնութերթիկը", "conanexiles": "կոնանեկանչիքներ", "eft": "էֆթ", "amongus": "մերմեջ", "eco": "էկո", "monkeyisland": "ապտաշխելիվայր", "valheim": "վալհայմ", "planetcrafter": "մոլորակստեղծող", "daysgone": "օրերնանցնումեն", "fobia": "ֆոբիա", "witchit": "հագեքհուդու", "pathologic": "պատահական", "zomboid": "զոմբոիդ", "northgard": "հյուսիսայինգարդ", "7dtd": "7dtd", "thelongdark": "երկարմութք", "ark": "արկ", "grounded": "բազմանշանակ", "stateofdecay2": "կորուստիաղետի2", "vrising": "վառվող", "madfather": "հրաշալիհայրը", "dontstarve": "չնայածմահվան", "eternalreturn": "հասարակականվերադարձ", "pathoftitans": "տիտաններիուղի", "frictionalgames": "ֆրիկշնալգեյմս", "hexen": "հեքսեն", "theevilwithin": "վատիներանում", "realrac": "իրոքռաք", "thebackrooms": "թիկնոցները", "backrooms": "հինասենյակներ", "empiressmp": "էմբայրսմփ", "blockstory": "բլոքհանգիստ", "thequarry": "թեժարևելցյուն", "tlou": "տլոու", "dyinglight": "մահուդիրլույս", "thewalkingdeadgame": "հլուօրիշմեռաղաղորդայինճամբար", "wehappyfew": "մենքուրախիշենք", "riseofempires": "երևույթներիվերելք", "stateofsurvivalgame": "պետությունապրումխաղ", "vintagestory": "վինտաժպատահարան", "arksurvival": "արկսուրvival", "barotrauma": "բարոտրավմա", "breathedge": "բրիզիկ", "alisa": "ալիսա", "westlendsurvival": "արաբեջականկյանք", "beastsofbermuda": "բերմուդայիողболмайեք", "frostpunk": "սառցայինթրբո", "darkwood": "գետնածու", "survivalhorror": "կsobrevivorhorror", "residentevil": "ապրողչարագործ", "residentevil2": "վիճակագրություն2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "հոուստրեն", "lifeaftergame": "համաշխարհայինհամալսարան", "survivalgames": "կանխատեսմանխաղեր", "sillenthill": "խաղաղվայր", "thiswarofmine": "այսպատերազմնմերոնք", "scpfoundation": "scpհիմնադրամ", "greenproject": "կանաչպроект", "kuon": "քուն", "cryoffear": "կլանվելուսհայտնություն", "raft": "ռաֆթ", "rdo": "rdo", "greenhell": "կանաչհետինարմունկ", "residentevil5": "residentevil5", "deadpoly": "մեռյալպոլի", "residentevil8": "ռեզիդենտէվիլ8", "onironauta": "օնիրոներ", "granny": "պապիկ", "littlenightmares2": "փոքրարթուների2", "signalis": "սիգնալիւս", "amandatheadventurer": "ամանդազակատարող", "sonsoftheforest": "լիցքանոցներիտղերք", "rustvideogame": "ռուստվիդեոախաղ", "outlasttrials": "հաղթահարենքփորձերնեն", "alienisolation": "հարուստաչափարտադրություն", "undawn": "չզարթնես", "7day2die": "7օր2մեռնալ", "sunlesssea": "չորափիզածծով", "sopravvivenza": "կյանքըրոխիկ", "propnight": "պրոպգիշեր", "deadisland2": "կայլերիկղզի2", "ikemensengoku": "իքնմանիշերեն", "ikemenvampire": "երեխուֆորմիա", "deathverse": "մահվանձեր", "cataclysmdarkdays": "կատակլիզմսեւօրեր", "soma": "սոմա", "fearandhunger": "վախէեւհացիսով", "stalkercieńczarnobyla": "ստալկերչիենցառնոբիլա", "lifeafter": "կյանքամբողջուն", "ageofdarkness": "մութիժամանակաշրջան", "clocktower3": "ժամատուն3", "aloneinthedark": "տանըկիշերում", "medievaldynasty": "միջնադարյանթագավորություն", "projectnimbusgame": "նիմբուսիախաղ", "eternights": "ամբողջգիշեր", "craftopia": "կանաչաշխարհ", "theoutlasttrials": "theoutlasttrials", "bunker": "բնկեր", "worlddomination": "աշխարհակալում", "rocketleague": "ռոկետլիգ", "tft": "թֆթ", "officioassassinorum": "գործառույթներկրագործների", "necron": "նեկրոն", "wfrp": "wfrp", "dwarfslayer": "ճիճվահոշող", "warhammer40kcrush": "վարպետակռիվ40կոտորել", "wh40": "wh40", "warhammer40klove": "warhammer40kսիրել", "warhammer40klore": "warhammer40կողմունք", "warhammer": "պատերազմիլիա", "warhammer30k": "վպատերազմզանգծար30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "ընդհանուրմահապարտ3", "temploculexus": "temploculexus", "vindicare": "վինդիկարեն", "ilovesororitas": "եսսիրումսորորիտաս", "ilovevindicare": "եսսիրումեմվինդիկարեilovevindicare", "iloveassasinorum": "եսսսիրումեմասասինորումiloveassasinorum", "templovenenum": "temptlovenenum", "templocallidus": "temploc<PERSON><PERSON>", "templomaerorus": "tempломայերորուս", "templovanus": "տեմփլովանըզ", "oficioasesinorum": "սպանությանաշխատանքներ", "tarkov": "տարկով", "40k": "40կ", "tetris": "տետրիս", "lioden": "լիոդեն", "ageofempires": "հայոցաշտարակներ", "aoe2": "աօե2", "hoi4": "հայ4", "warhammerageofsigmar": "պատերազմատերընհաջողություն", "civilizationv": "քաղաքակրթությունv", "ittakestwo": "երկուիանցպետքէ", "wingspan": "թևերիերկարություն", "terraformingmars": "մարսիփոփոխություն", "heroesofmightandmagic": "հերոսներուժևմոգություն", "btd6": "btd6", "supremecommander": "վեհթեքcommander", "ageofmythology": "մասշտաբիերասեր", "args": "արգս", "rime": "բառում", "planetzoo": "մոլորակսկետրոն", "outpost2": "օվպոստ2", "banished": "չլինես", "caesar3": "ցեզար3", "redalert": "կարմիրզանգված", "civilization6": "քաղաքակրթություն6", "warcraft2": "վարքրոֆտ2", "commandandconquer": "կարգադրիրևճանապարհդիւնէш", "warcraft3": "wowcraft3", "eternalwar": "հավերժապարտություն", "strategygames": "տրործախաղեր", "anno2070": "anno2070", "civilizationgame": "մակրամիտուփում", "civilization4": "քաղաքակրթություն4", "factorio": "ֆակտորիոն", "dungeondraft": "դունջենդռաֆտ", "spore": "սպոր", "totalwar": "ներքինպատերազմ", "travian": "տրավիան", "forts": "ֆորթս", "goodcompany": "լավընկերներ", "civ": "սիվ", "homeworld": "տունաշխարհ", "heidentum": "հեյդենտում", "aoe4": "aoe4", "hnefatafl": "հընեֆաթաֆլ", "fasterthanlight": "լույսիցարագ", "forthekings": "թագավորների_համար", "realtimestrategy": "բարետրադռազմավարություն", "starctaft": "ստარტկրաֆտ", "sidmeierscivilization": "սիդմայերիքաղաքակիրթություն", "kingdomtwocrowns": "թագավորություներկուսպսակներ", "eu4": "եվ4", "vainglory": "պերճախոսություն", "ww40k": "ww40k", "godhood": "ներվածություն", "anno": "անկյուն", "battletech": "պատերազմականտեխնիկա", "malifaux": "մալիբու", "w40k": "w40k", "hattrick": "հետտրիկ", "davesfunalgebraclass": "դեյվիզվարճալիալգեբրադասը", "plagueinc": "հիվանդությունinc", "theorycraft": "թեորիաներ_ստեղծել", "mesbg": "մեսբգ", "civilization3": "ցիվիլիզացիա3", "4inarow": "4մեկընթաց", "crusaderkings3": "կռիվթագավորներ3", "heroes3": "հերոսներ3", "advancewars": "առաջխաղացումներ", "ageofempires2": "հինթագավորություններ2", "disciples2": "սովորեցողներ2", "plantsvszombies": "ծառերներանզոմբիներիcontrelazombis", "giochidistrategia": "giochidistrategia", "stratejioyunları": "ստրատեգիականխաղեր", "europauniversalis4": "եվրոպահավաք4", "warhammervermintide2": "վրանքմահվաներկրորդحصار", "ageofwonders": "հասակներդիադարաշրջան", "dinosaurking": "դինոզավրերիժالملك", "worldconquest": "աշխարհակերկրագործություն", "heartsofiron4": "ironերիսիսիրտերը4", "companyofheroes": "հերոսներիընկերություն", "battleforwesnoth": "ոճեամարտնվեսնոթ", "aoe3": "aoe3", "forgeofempires": "հիմնարարներ", "warhammerkillteam": "րանսենգուրվա", "goosegooseduck": "թռչունկուկիզմ", "phobies": "ֆոբիաներ", "phobiesgame": "ֆոբիաներիխաղ", "gamingclashroyale": "խաղայինհակամարտություն", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "արտաքիներևույթ", "turnbased": "դրամախաղ", "bomberman": "բոմբեռմեն", "ageofempires4": "ամպիրներիէր", "civilization5": "քաղաքագիտություն5", "victoria2": "վիկտորլիա2", "crusaderkings": "քրուսադավորներ", "cultris2": "cultris2", "spellcraft": "հմաիմիջոցներ", "starwarsempireatwar": "աստղերիպատերազմներիմպերիայիամարտ", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "ստրատեգիա", "popfulmail": "պոպֆուլմեյլ", "shiningforce": "լույսիուժ", "masterduel": "մարզավիճակ", "dysonsphereprogram": "դայրոնսֆերպրոգրամ", "transporttycoon": "հիվանդանոցիտնօրեն", "unrailed": "անվերադարձ", "magicarena": "հեքիաթարան", "wolvesville": "կռունկներքաղաք", "ooblets": "օոբլեթս", "planescapetorment": "թռիչքայինաղետիցփախուստ", "uplandkingdoms": "բարձրատարածքներ", "galaxylife": "գալակտիկահանդես", "wolvesvilleonline": "գարշիկներու53", "slaythespire": "սլեյթեսպայրս", "battlecats": "պայքարականկատուներ", "sims3": "սիմս3", "sims4": "sims4", "thesims4": "սիմս4", "thesims": "thesims", "simcity": "սիմսիթի", "simcity2000": "սիմսիթիհինգ2000", "sims2": "սիմս2", "iracing": "իracing", "granturismo": "գրանտուրիզմո", "needforspeed": "ժամանակիապահովություն", "needforspeedcarbon": "արագությունկարիբյան", "realracing3": "ճանաչումայավազք3", "trackmania": "trackmania", "grandtourismo": "մեծառաջադեմություն", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "թս4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "եվսիմս1", "lossims4": "մենքս4", "fnaf": "ֆնֆ", "outlast": "գերակատարել", "deadbydaylight": "մահառաչօրալույս", "alicemadnessreturns": "ալիսմանասիրությանվերադարձ", "darkhorseanthology": "մութաճախորդներիանտոլոգիա", "phasmophobia": "փազմոֆոբիա", "fivenightsatfreddys": "հինգերեքերհարկումfreddyի", "saiko": "սայկո", "fatalframe": "արժանապատիվկերպարանք", "littlenightmares": "ճիշտգիշերահանդեսներ", "deadrising": "մահացյալներթափառ", "ladydimitrescu": "լեդիդիմիտրեսկու", "homebound": "տանըմնացած", "deadisland": "մահացյանկղզին", "litlemissfortune": "փոքրիկադեպրեսիա", "projectzero": "նախագիծզրո", "horory": "հորորք", "jogosterror": "ժողոսթterror", "helloneighbor": "բարևխneighbor", "helloneighbor2": "բարևմերձավորը2", "gamingdbd": "գեյմինգդբդ", "thecatlady": "բերիկկատուներիսիրող", "jeuxhorreur": "խաղերասվեր", "horrorgaming": "ներածուերխաղեր", "magicthegathering": "ֆանտազիայիերևույթ", "mtg": "բլու", "tcg": "tcg", "cardsagainsthumanity": "կարտերընդդեմմարդկության", "cribbage": "կրիբեջ", "minnesotamtg": "միննեսոտամթգ", "edh": "էջ", "monte": "մոնտե", "pinochle": "փինոչլ", "codenames": "հաղորդանիշեր", "dixit": "դիքսիթ", "bicyclecards": "ճանապարհապարգեր", "lor": "լոռ", "euchre": "յուքր", "thegwent": "գվենտ", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "սոլիթեր", "poker": "պոկեր", "hearthstone": "հոգեհարցոդ", "uno": "ուโน", "schafkopf": "շաֆկոֆ", "keyforge": "հիմքիֆորջ", "cardtricks": "քարտերիհնարքներ", "playingcards": "պլեյինգքարտեր", "marvelsnap": "marvelsnap", "ginrummy": "գինռամի", "netrunner": "նետռնկման", "gwent": "գվենտ", "metazoo": "մետաձոու", "tradingcards": "առևտրայինքարտեր", "pokemoncards": "պոկեմոնկարտեր", "fleshandbloodtcg": "արմատներևարյունtcg", "sportscards": "ժողովրդականքարտեր", "cardfightvanguard": "քարտամարտվանգարդ", "duellinks": "դուելլինգս", "spades": "սպանասուրիկներ", "warcry": "ռազմականմենագրություն", "digimontcg": "դիջիումոնտկանգ", "toukenranbu": "թոուքենռամբու", "kingofhearts": "սրտերիարքա", "truco": "տրուկո", "loteria": "լոտոյան", "hanafuda": "հանափուդա", "theresistance": "բացառությունը", "transformerstcg": "տրանսֆորմերսթցգ", "doppelkopf": "դոպելկոք", "yugiohcards": "yugiohքարտեր", "yugiohtcg": "յուգիոհոtcg", "yugiohduel": "յուգիոհպարտություն", "yugiohocg": "յուգիոհոքг", "dueldisk": "dueldisk", "yugiohgame": "յոււਗੀոհխաղ", "darkmagician": "սևքաղաքականություն", "blueeyeswhitedragon": "կապույտձեռքերայնուամենայնիվdragon", "yugiohgoat": "յուգիոհգեոթ", "briscas": "բրիսկաս", "juegocartas": "կարտերախաղ", "burraco": "բուրրակո", "rummy": "ռամենտ", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "դոբլ", "mtgcommander": "mtgկոմանդիր", "cotorro": "կոտռո", "jeuxdecartes": "carteերիխաղեր", "mtgjudge": "mtgվիճաբանություն", "juegosdecartas": "քարտախաղերդ", "duelyst": "դյուելիստ", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "մթգպրեքոնկոմանդեր", "kartenspiel": "խաղաթղթեր", "carteado": "քարտեալ", "sueca": "զուգահեռ", "beloteonline": "բելոտոնլայն", "karcianki": "կարճյանկի", "battlespirits": "պատերազմիrum", "battlespiritssaga": "պատերազմիոգեւորություններ", "jogodecartas": "գործիքներboo", "žolíky": "žolíky", "facecard": "երևադարձ", "cardfight": "կարտֆայտ", "biriba": "բիրիբա", "deckbuilders": "դեկբիլդերներ", "marvelchampions": "marvelchampions", "magiccartas": "հեքիաթայինքարտեր", "yugiohmasterduel": "յուգիոհմաստերդուել", "shadowverse": "շեդովերս", "skipbo": "skip<PERSON>", "unstableunicorns": "անհավասարապիտոններ", "cyberse": "հիմեական", "classicarcadegames": "կլասիկարկադայինխաղեր", "osu": "օսու", "gitadora": "գիտադորա", "dancegames": "պարիխաղեր", "fridaynightfunkin": "վայրկյանվերջիֆունկը", "fnf": "fnf", "proseka": "պրոսեկա", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "դիսկերակազմգործոն", "clonehero": "կլոնհերո", "justdance": "միասինծավշենք", "hatsunemiku": "հացունեմիկու", "prosekai": "պրոսեկանայ", "rocksmith": "ռոքսմիթ", "idolish7": "իդոլիշ7", "rockthedead": "շարժիրմահերին", "chunithm": "չունիթհմ", "idolmaster": "հերոսներ", "dancecentral": "dancecentral", "rhythmgamer": "հնչյունխաղացող", "stepmania": "քայլմանիա", "highscorerythmgames": "բարձրնկչիrhythmխաղեր", "pkxd": "pkxd", "sidem": "սայդեմ", "ongeki": "ոնգեկի", "soundvoltex": "ձայնվոլտեքս", "rhythmheaven": "հնչյուններիաստիճան", "hypmic": "հիպմիկ", "adanceoffireandice": "երկվորյակածալիսոփայություն", "auditiononline": "օնլայնփորձություն", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "համաձայնություն", "cryptofthenecrodancer": "կրիպտոների", "rhythmdoctor": "թrhythmdoctor", "cubing": "հասկերում", "wordle": "հաղորդիր", "teniz": "տենիզ", "puzzlegames": "պազլիխաղեր", "spotit": "spotit", "rummikub": "ռումիկուբ", "blockdoku": "բլոքդոքու", "logicpuzzles": "լոգիկփազլեր", "sudoku": "սուդոկու", "rubik": "ռուբիկ", "brainteasers": "մտածողություններիխաղեր", "rubikscube": "ռուբիկիաշանի", "crossword": "խաչբառ", "motscroisés": "մարձայինխաղեր", "krzyżówki": "բուքսեր", "nonogram": "ննոգրամ", "bookworm": "գրքիհարուստ", "jigsawpuzzles": "զրահայինպազլներ", "indovinello": "հանելուկ", "riddle": "հանելուկ", "riddles": "հանելուկներ", "rompecabezas": "հանելխզվել", "tekateki": "տեկատեկի", "inside": "ներսում", "angrybirds": "եռանգներ", "escapesimulator": "փախուստիիսիմուլյատոր", "minesweeper": "ծխախույք", "puzzleanddragons": "ղևորդիկներևօձեր", "crosswordpuzzles": "հանելուկներ", "kurushi": "կուրուշի", "gardenscapesgame": "հեռանկարայինփողոցներ", "puzzlesport": "պազլայինֆուտբոլ", "escaperoomgames": "ապահովմանցմանխաղեր", "escapegame": "փախուստախաղ", "3dpuzzle": "3դիկխաղալիք", "homescapesgame": "տուներիզխաղ", "wordsearch": "բառարշման", "enigmistica": "ենիգմիստիկա", "kulaworld": "կուլավorld", "myst": "միստ", "riddletales": "ռիդդլթեյլս", "fishdom": "ֆիշդոմ", "theimpossiblequiz": "անհնարինհարցաշար", "candycrush": "շոկոլադապայքար", "littlebigplanet": "լիտլբիգփլանետ", "match3puzzle": "համընկնում3պազլ", "huniepop": "հունիփոփ", "katamaridamacy": "կատարամիդամասի", "kwirky": "քյուրքի", "rubikcube": "ռուբիկիանկավառակ", "cuborubik": "կուբոռուբիկ", "yapboz": "յապպոզ", "thetalosprinciple": "թեթալոսիսծինձը", "homescapes": "հայրենիքը", "puttputt": "պուտպուտ", "qbert": "քբերտ", "riddleme": "ռիդլեմե", "tycoongames": "թայքունխաղեր", "cubosderubik": "կուբոսդերուբիկ", "cruciverba": "խաչբառ", "ciphers": "սահակներ", "rätselwörter": "ռետսելվորթեր", "buscaminas": "բուսկամինաս", "puzzlesolving": "փuzzlesolving", "turnipboy": "կարտոֆիլաճուտիկ", "adivinanzashot": "պայմանավորերվում", "nobodies": "առանցենցներ", "guessing": "գուշակել", "nonograms": "նոնոգրամներ", "kostkirubika": "կոստկիրուբիկա", "crypticcrosswords": "կրիպտիկխոսքեր", "syberia2": "սիբերիա2", "puzzlehunt": "փատկերայինորս", "puzzlehunts": "հանելուկայինպառքաներ", "catcrime": "կատուներիպատրաստություններ", "quebracabeça": "գլուխկոտրուկ", "hlavolamy": "հլավոլամեր", "poptropica": "պոփտրոպիկա", "thelastcampfire": "որույրբռնվել", "autodefinidos": "ինքնորոշված", "picopark": "պիկոպարկ", "wandersong": "նավարկողերգություն", "carto": "կարոտել", "untitledgoosegame": "անմեղուտիկոզիմեզ", "cassetête": "կանեփ", "limbo": "լիմբո", "rubiks": "ռուբիկս", "maze": "լաբիրինթոս", "tinykin": "tinykin", "rubikovakostka": "ռուբիկիապուրակ", "speedcube": "արագկուբ", "pieces": "միավորներ", "portalgame": "պորտալխաղ", "bilmece": "բիլմեցե", "puzzelen": "պազլել", "picross": "պիկրոս", "rubixcube": "ռուբիկիալdj", "indovinelli": "ինդովինելով", "cubomagico": "կուբոմագիկո", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "կոդմ", "twistedwonderland": "հանգույցներգիծակալի", "monopoly": "մոնոպոլիա", "futurefight": "գալիսնհարցել", "mobilelegends": "մոբայլլեգենդներ", "brawlstars": "բռունցքbattle", "brawlstar": "բրոլստար", "coc": "կոկ", "lonewolf": "լինկլավառ", "gacha": "գաչա", "wr": "wr", "fgo": "ֆգո", "bitlife": "բիթլայֆ", "pikminbloom": "pikminբույսեր", "ff": "ֆֆ", "ensemblestars": "համեստարս", "asphalt9": "ասფալտ9", "mlb": "mlb", "cookierunkingdom": "cookieբլուրիարքություն", "alchemystars": "ալքեմիաստղեր", "stateofsurvival": "առողջապահությանպատերազմ", "mycity": "իմքաղաքը", "arknights": "արկնայթս", "colorfulstage": "գունագեղբեմ", "bloonstowerdefense": "բլունստաուերապաշտպանություն", "btd": "btd", "clashroyale": "clashroyale", "angela": "անգելա", "dokkanbattle": "դոկկանպայքար", "fategrandorder": "ասումենքհետօրը", "hyperfront": "հաճախակիառջև", "knightrun": "սուստրերկա", "fireemblemheroes": "հրազենիարքեր", "honkaiimpact": "հոնկայիմպակտ", "soccerbattle": "ֆուտբոլապարեր", "a3": "ա3", "phonegames": "հեռախոսայինհամալիրներ", "kingschoice": "թագավորիընտրությունը", "guardiantales": "համաշխարհայինհեքիաթներ", "petrolhead": "բենզինասեր", "tacticool": "տաքտիկուլ", "cookierun": "խմորեղենավազք", "pixeldungeon": "փիքսելադեն", "arcaea": "արխայա", "outoftheloop": "հայտարարություններիցհետու", "craftsman": "որդեգրո", "supersus": "հարսխահոս", "slowdrive": "հարուստվարում", "headsup": "թեքգլխիդ", "wordfeud": "բառնեկտոր", "bedwars": "բարձերիպատերազմ", "freefire": "անկախհրաձիգ", "mobilegaming": "մոբիլխաղեր", "lilysgarden": "լիլիթսպարտեզը", "farmville2": "ֆարմվիլ22", "animalcrossing": "կենդանականանցում", "bgmi": "բգմի", "teamfighttactics": "թիմապայքարայինմարտեր", "clashofclans": "հայրենականպատերազմիճակատամարտ", "pjsekai": "պջսեկայ", "mysticmessenger": "միստիկհաղորդագրություն", "callofdutymobile": "հրավիրելխաղիմոբայլ", "thearcana": "thearcana", "8ballpool": "8գնդիկլիճ", "emergencyhq": "անհրաժեշտությունhq", "enstars": "ենստարզս", "randonautica": "ռանդոնաուտիկա", "maplestory": "մեյփլստորի", "albion": "ալբիոն", "hayday": "հայդեյ", "onmyoji": "ոնմյոջի", "azurlane": "azurlane", "shakesandfidget": "շեյքսևֆիջետ", "ml": "ելություն", "bangdream": "բանգդրիմ", "clashofclan": "ամպրոպիապրանք", "starstableonline": "աստղասենյակցանցում", "dragonraja": "դրագոնռաջա", "timeprincess": "ժամանակահարստուհի", "beatstar": "բիթստար", "dragonmanialegend": "դրագոնմանիայիlegend", "hanabi": "հանաբի", "disneymirrorverse": "դիսնեյայնացրածբացարան", "pocketlove": "հարմոնիաbesar", "androidgames": "անդրոիդխաղեր", "criminalcase": "հանցագործություն", "summonerswar": "կանչողներիպատերազմ", "cookingmadness": "խոհարարականգժություն", "dokkan": "դոկկան", "aov": "aov", "triviacrack": "տրիվիաֆլեշ", "leagueofangels": "հրեշտակներիլիգա", "lordsmobile": "լորդսմոբայլ", "tinybirdgarden": "փոքրելվարդապարիսպ", "gachalife": "գաչալայֆ", "neuralcloud": "նեյրալհcloud", "mysingingmonsters": "մերերգեցիկմետաղները", "nekoatsume": "նեկոացում", "bluearchive": "կապույտարխիվ", "raidshadowlegends": "raidshadowlegends", "warrobots": "պատերազմականռոբոտներ", "mirrorverse": "առտաբառատuniverse", "pou": "պու", "warwings": "պատերազմիաթևեր", "fifamobile": "ֆիֆամобայլ", "mobalegendbangbang": "մոբալեգենդբանգբանգ", "evertale": "էվերտալ", "futime": "ֆուտայմ", "antiyoy": "անտիյոյ", "apexlegendmobile": "apexlegendmobile", "ingress": "ընդլայնում", "slugitout": "դժվարությունառաջարկիր", "mpl": "mpl", "coinmaster": "մետաղականօրինակիամսայակիր", "punishinggrayraven": "հետապնդումgriraven", "petpals": "կենդանիընկերներ", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "արանապակուստ", "wolfy": "գայթակղիչ", "runcitygame": "գագաթնակետամսվաբաժանորդիխաղ", "juegodemovil": "հեռախաղ", "avakinlife": "ավակինկյանք", "kogama": "քոգամա", "mimicry": "միմիկրիա", "blackdesertmobile": "սևանապատmóvil", "rollercoastertycoon": "ռոլերիկոստերտիկոն", "grandchase": "մեծշարք", "bombmebrasil": "բոմբմեբրազիլ", "ldoe": "ldoe", "legendonline": "լեգենդոնլայն", "otomegame": "օտոմե게임", "mindustry": "միտողջություն", "callofdragons": "առշիններուկանչ", "shiningnikki": "փայլողնիքին", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "պատթոնորտեղ", "sealm": "sealm", "shadowfight3": "ճնշմանպատերազմ3", "limbuscompany": "limbuscompany", "demolitionderby3": "ամրագոտիերաժռում3", "wordswithfriends2": "թվերնաշխարհընկերներ2", "soulknight": "հոգեվարտիկ", "purrfecttale": "կատվիամենagerie", "showbyrock": "showbyrock", "ladypopular": "էջուհիապառա", "lolmobile": "լոլհետաքրքիր", "harvesttown": "հնձիլաքաղաք", "perfectworldmobile": "շարունակիրաշխարհթաղամաս", "empiresandpuzzles": "մոտեցումներնուհանելուկները", "empirespuzzles": "էմպայրսփուզլս", "dragoncity": "դրակոնականքաղաք", "garticphone": "գարտիկֆոն", "battlegroundmobileind": "պաշտպանականհրապարակmobileind", "fanny": "ֆաննի", "littlenightmare": "փոքրաբերցում", "aethergazer": "հավերժահամալիր", "mudrunner": "մութրաներ", "tearsofthemis": "կոկորդիփոթորիկ", "eversoul": "էվերսոուլ", "gunbound": "գունբաունդ", "gamingmlbb": "գեյմինգmlbb", "dbdmobile": "dbdmobile", "arknight": "արկնայտ", "pristontale": "պրիստոնթեյլ", "zombiecastaways": "զոմբիաղետաբերներ", "eveechoes": "եվէկոսներ", "jogocelular": "ջոգոցելular", "mariokarttour": "mariokarttուր", "zooba": "զուբա", "mobilelegendbangbang": "հեռախոսահангակիչbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "խոհարարականմամա", "cabalmobile": "կաբալմոբայլ", "streetfighterduel": "ուղղափորձակռիվ", "lesecretdhenri": "լեսեկրետդենրի", "gamingbgmi": "gamingbgmi", "girlsfrontline": "աղջիկներառջևում", "jurassicworldalive": "յուրասիկաշխարհապրանքին", "soulseeker": "վերադարձրածհոգի", "gettingoverit": "անցնեմիդե", "openttd": "բացառված", "onepiecebountyrush": "միկտորին_ղեկավար_հավաքածու", "moonchaistory": "ամուսնիկայիններգիծանք", "carxdriftracingonline": "մեքենասղթտալուաճճի", "jogosmobile": "ջոգոսմոբիլ", "legendofneverland": "անհավատալի_երկիր", "pubglite": "pubglite", "gamemobilelegends": "խաղաուսումելեգենդներ", "timeraiders": "ժամանակագրողներ", "gamingmobile": "մոբիլխաղեր", "marvelstrikeforce": "մոմենտիլովներ", "thebattlecats": "պատերազմիկատուներ", "dnd": "դնդ", "quest": "ոչնչովչաման", "giochidiruolo": "գյոգիջիդուրոլո", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "մութաշխարհ", "travellerttrpg": "ֆործարարներtrpg", "2300ad": "2300թ", "larp": "լարպ", "romanceclub": "ռոմանտիկակումբ", "d20": "d20", "pokemongames": "պոկեմոնխաղեր", "pokemonmysterydungeon": "փոքեմոնաղբյուրգտություն", "pokemonlegendsarceus": "փոքեմոնմիթահայծնավորություններ", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "պոկեամենիմե", "pokémongo": "փոքեմոնգո", "pokemonred": "պոկեմոնկարմիր", "pokemongo": "պոկեմոնգո", "pokemonshowdown": "pokemonհանդիպում", "pokemonranger": "պոկեմոնինկունի", "lipeep": "լիպիպ", "porygon": "պորիգոն", "pokemonunite": "պոկեմոնմիավորվի", "entai": "ենթեի", "hypno": "հիպնո", "empoleon": "էմբոլեոն", "arceus": "արխես", "mewtwo": "մյուտու", "paldea": "պալդեան", "pokemonscarlet": "պոկեմոնսկարլետ", "chatot": "chatot", "pikachu": "պիկաչու", "roxie": "ռոքսի", "pokemonviolet": "pokemonմանուշակ", "pokemonpurpura": "փոկեմոնփուրկրա", "ashketchum": "աշկետչում", "gengar": "gengar", "natu": "naturoo", "teamrocket": "թիմրոքի", "furret": "փոքրիկֆլեշ", "magikarp": "մագիկարպ", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "սնորլաքս", "pocketmonsters": "պpocketmonsters", "nuzlocke": "նուզլոկե", "pokemonplush": "պոկեմոնմուշկ", "teamystic": "թիմիստիկ", "pokeball": "փոքեբոլ", "charmander": "ջրմուղզառիկ", "pokemonromhack": "պոկեմոնռոմհեք", "pubgmobile": "pubgmobile", "litten": "լիտտեն", "shinypokemon": "պայլողփոկեմոն", "mesprit": "մեսպրիտ", "pokémoni": "փոքեմոնւի", "ironhands": "ժայռափեղկեր", "kabutops": "կաբուտոփս", "psyduck": "սթափուղ", "umbreon": "ումբreon", "pokevore": "պոկևորե", "ptcg": "պտչգ", "piplup": "փիպլեփ", "pokemonsleep": "պոկեմոնջերմություն", "heyyoupikachu": "հեղյուփիկաչու", "pokémonmaster": "պոկեմոնվարպետ", "pokémonsleep": "փոքեմոնեռը", "kidsandpokemon": "շներնեւփոկեմոնները", "pokemonsnap": "պոկեմոնսնափ", "bulbasaur": "բուլբասոուր", "lucario": "լուսկարիո", "charizar": "չարիզար", "shinyhunter": "բլուրասող", "ajedrez": "շախմատ", "catur": "ցատուր", "xadrez": "շախմատ", "scacchi": "մանրաթղթերի", "schaken": "շաքեն", "skak": "սկակ", "ajedres": "շախմատ", "chessgirls": "շախմարիայներ", "magnuscarlsen": "մագնուսկարսլեն", "worldblitz": "աշխարհի_վշտեր", "jeudéchecs": "շախմատայինֆլեշ", "japanesechess": "ճապոնականշախմատ", "chinesechess": "չինականշախմատ", "chesscanada": "շախմատրապորտը", "fide": "ֆիդե", "xadrezverbal": "խոսնակմրցաշար", "openings": "բացթողումներ", "rook": "ռուկ", "chesscom": "շախմադոկտոր", "calabozosydragones": "կալաբոզոներևողներ", "dungeonsanddragon": "դղյակներևառյուծներ", "dungeonmaster": "զընգերունվարպետ", "tiamat": "տիամատ", "donjonsetdragons": "դոնջոններնուドラգոնները", "oxventure": "օքսվենչուր", "darksun": "մութարևիգալ", "thelegendofvoxmachina": "voxmachinայիlegendը", "doungenoanddragons": "դումգենոևդրագոններ", "darkmoor": "մութպոնդ", "minecraftchampionship": "մայնկրաֆթչեմպիոնատ", "minecrafthive": "մայնկրաֆթհայվ", "minecraftbedrock": "մայնքրավտբեդրոք", "dreamsmp": "պատրանքսմպ", "hermitcraft": "հերմիտկրաֆթ", "minecraftjava": "մայնքրաֆթջավա", "hypixelskyblock": "հիպեքսելսկայբլոկ", "minetest": "մայնթեստ", "hypixel": "հիպիքսել", "karmaland5": "karmaland5", "minecraftmods": "minecraftմոդեր", "mcc": "mcc", "candleflame": "ջահիկրակ", "fru": "ֆրու", "addons": "հավելումներ", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "небосвод", "minecraftpocket": "մայնկրաֆթեփpocket", "minecraft360": "minecraft360", "moddedminecraft": "մոդեդմայնկրաֆթ", "minecraftps4": "minecraftps4", "minecraftpc": "մայնկրաֆթպհ", "betweenlands": "ընդմեջերկրներ", "minecraftdungeons": "մայնքրաֆթյանաշխարհներ", "minecraftcity": "մայնկրաֆթքաղաք", "pcgamer": "պցգեյմեր", "jeuxvideo": "խաղեր", "gambit": "gambit", "gamers": "խաղացողներ", "levelup": "լևելափ", "gamermobile": "խաղացողմոբիլ", "gameover": "խաղըավարտվեց", "gg": "gg", "pcgaming": "համակարգչայինխաղեր", "gamen": "gamen", "oyunoynamak": "օյունոիդնամակ", "pcgames": "համակարգչայինխաղեր", "casualgaming": "խաղալիսվառցլիմուլում", "gamingsetup": "գեյմինգսեթափ", "pcmasterrace": "պսկոստերաս", "pcgame": "համակարգչայինխաղ", "gamerboy": "գեյմերբoy", "vrgaming": "վիրտուալխաղեր", "drdisrespect": "դոկտորհարգանք", "4kgaming": "4կգեյմինգ", "gamerbr": "gamerbr", "gameplays": "gamerականներ", "consoleplayer": "համակարգչայինխաղացող", "boxi": "բոքսի", "pro": "պրո", "epicgamers": "էպիկխաղեր", "onlinegaming": "օնլայնխաղեր", "semigamer": "կիսասահմանախաղացա", "gamergirls": "խաղացողգաղթնիքներ", "gamermoms": "խաղացողմայրեր", "gamerguy": "բոխուտղա", "gamewatcher": "խաղտեսող", "gameur": "խաղացեք", "grypc": "գրիպց", "rangugamer": "ռանգուգեյմեր", "gamerschicas": "գեյմերուհիներ", "otoge": "օտոգե", "dedsafio": "դեդսաֆիո", "teamtryhard": "թիմիանձնավորություն", "mallugaming": "մալլուգեյմինգ", "pawgers": "թաթիկներ", "quests": "հարցեր", "alax": "ալաքս", "avgn": "տարբերակ", "oldgamer": "հինխաղացող", "cozygaming": "հարմարավետխաղեր", "gamelpay": "gamelpay", "juegosdepc": "համակարգչայինխաղեր", "dsswitch": "dsswitch", "competitivegaming": "մրցակցայինխաղեր", "minecraftnewjersey": "մայնքraftնյուջերսի", "faker": "ֆեյքեր", "pc4gamers": "քսթառակօլորտի", "gamingff": "խաղադրույքներ", "yatoro": "յատորո", "heterosexualgaming": "հետերոսեքսուալխաղեր", "gamepc": "խաղպկ", "girlsgamer": "աղջիկխաղացող", "fnfmods": "fnfմոդս", "dailyquest": "օրվափորձություն", "gamegirl": "խաղաղաղիկ", "chicasgamer": "չիկասգեյմեր", "gamesetup": "խաղայինսարքավորումներ", "overpowered": "անցորդ", "socialgamer": "ասոցիաներայինխաղացող", "gamejam": "խաղիաժուրդ", "proplayer": "պրոֆիպլեյեր", "roleplayer": "ռոլպլեյեր", "myteam": "իմթիմը", "republicofgamers": "խաղացողներյանհանրապետություն", "aorus": "աորուս", "cougargaming": "կուգառգեյմինգ", "triplelegend": "հերոսատրիաեն", "gamerbuddies": "խաղագործընկերներ", "butuhcewekgamers": "բուտուխջիգեյմերներ", "christiangamer": "հիսուսականխաղացող", "gamernerd": "գեյմերոմիք", "nerdgamer": "բժիշկխաղացող", "afk": "աֆկ", "andregamer": "անդրեգեյմեր", "casualgamer": "անհոգխաղացող", "89squad": "89հավաք", "inicaramainnyagimana": "ինիկարամայննյագիմանա", "insec": "իսկեվ", "gemers": "գեմերներ", "oyunizlemek": "oyunիզլեմ", "gamertag": "գեյմերաթեգ", "lanparty": "լանշքիպառտի", "videogamer": "վիդեոխաղեր", "wspólnegranie": "հասարակականխաղեր", "mortdog": "մորտդոգ", "playstationgamer": "պլեյստեյշնհամաունքեր", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "առողջխաղացող", "gtracing": "gtracing", "notebookgamer": "դեդպլեյեր", "protogen": "պրոտոժեն", "womangamer": "wոմանգեյմեր", "obviouslyimagamer": "obviamenteխաղացողեմ", "mario": "մարիո", "papermario": "paperմարիո", "mariogolf": "մարիոգոլֆ", "samusaran": "սամուսարան", "forager": "հավաքելուն", "humanfallflat": "մարդաթափընկնում", "supernintendo": "սուպերնինդենդո", "nintendo64": "նինտենդո64", "zeroescape": "ազորուղ逃", "waluigi": "վալուիջի", "nintendoswitch": "նինտենդոսվիչ", "nintendosw": "նintendosw", "nintendomusic": "նինթենդոմուսիկ", "sonicthehedgehog": "սոնիկհրճեալ", "sonic": "սոնիկ", "fallguys": "հոգվիճակիր", "switch": "փոխվել", "zelda": "ձելդա", "smashbros": "սմեշբրոս", "legendofzelda": "զելդայիարդյունաբերություն", "splatoon": "սպլատուն", "metroid": "մետրոիդ", "pikmin": "փիքմին", "ringfit": "링բիfit", "amiibo": "արձրագոյն", "megaman": "մեգամեն", "majorasmask": "մայորասմասկ", "mariokartmaster": "մարիոքարթմասնագետ", "wii": "wii", "aceattorney": "եսզատեմիրեմատյունը", "ssbm": "ssbm", "skychildrenofthelight": "առնախաղերիզնետույս", "tomodachilife": "տոմոդաչիլայֆ", "ahatintime": "ահատինթայմ", "tearsofthekingdom": "թագավորությանքարթերը", "walkingsimulators": "քայլողսիմուլյատորներ", "nintendogames": "նինտենդոխաղեր", "thelegendofzelda": "զելդայիերիքը", "dragonquest": "դրագոնկարողություն", "harvestmoon": "հնձհարսնաքար", "mariobros": "մարիոհեղբայրներ", "runefactory": "եռմանգործարան", "banjokazooie": "բանջոկազուի", "celeste": "ցելեստե", "breathofthewild": "բնությանհոտը", "myfriendpedro": "երեքընկերոջպեդրո", "legendsofzelda": "ազգերիlegendsofzelda", "donkeykong": "դոնկիքընգ", "mariokart": "մարիոկարտ", "kirby": "կիրբի", "51games": "51խաղեր", "earthbound": "երկրային", "tales": "պատմություններ", "raymanlegends": "raymanlegends", "luigismansion": "լուիգիսիակապը", "animalcrosssing": "նվերներանցիներ", "taikonotatsujin": "թայկոնոտացուհին", "nintendo3ds": "նինթենդո3դս", "supermariobros": "սూపերմարիոբրոս", "mariomaker2": "մարիոյարիչ2", "boktai": "բոկթաի", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "քառակուսայինպլան", "supermariomaker": "սուպերմարիոխաղստեղծող", "xenobladechronicles3": "զենոբլեյդքիարտադրության3", "supermario64": "սուպերմարիո64", "conkersbadfurday": "կոնկերներիզավերջանում", "nintendos": "նթենդոս", "new3ds": "նոր3դս", "donkeykongcountry2": "դոնկիկոնգերկիր2", "hyrulewarriors": "հայրուլպատերազմողներ", "mariopartysuperstars": "մարիոփառատոնիsuperstars", "marioandsonic": "մարիոեւսոնիկ", "banjotooie": "բանջոտուի", "nintendogs": "նինտենդոգս", "thezelda": "եզելդա", "palia": "պալիա", "marioandluigi": "մարիոկինալիուժի", "mariorpg": "մարիորպգ", "zeldabotw": "զելդաբոտվ", "yuumimain": "յումըիմայն", "wildrift": "վայրիսոսիք", "riven": "ռիվեն", "ahri": "ահրի", "illaoi": "իլլաի", "aram": "արամ", "cblol": "cblol", "leagueoflegendslas": "լիգանվավորանքներlas", "urgot": "ուրքոթ", "zyra": "զայրա", "redcanids": "կարմիրսկերով", "vanillalol": "վանիլլալոլ", "wildriftph": "վայրիեռuib", "lolph": "լոլֆ", "leagueoflegend": "լիգայիlegend", "tốcchiến": "տողզերծ", "gragas": "գրագաս", "leagueoflegendswild": "աշխարհիալիքերիհանրույթ", "adcarry": "վերածիրcandy", "lolzinho": "լոլզինյո", "leagueoflegendsespaña": "լեգենդներիարժանիքիսպանություն", "aatrox": "ատրոքս", "euw": "եվ", "leagueoflegendseuw": "լիգանիպատվերներeuw", "kayle": "կայլ", "samira": "սամիրա", "akali": "ակալի", "lunari": "լունարի", "fnatic": "ֆնայատիկ", "lollcs": "ծիծաղումչի", "akshan": "աքշան", "milio": "milio", "shaco": "շակո", "ligadaslegendas": "լիգադասլեգենդաս", "gaminglol": "համակարգչայինխաղերlol", "nasus": "նասկուս", "teemo": "տիմո", "zedmain": "զեդմայն", "hexgates": "հեքսգեյթս", "hextech": "հեքստեք", "fortnitegame": "ֆորտնայտխաղ", "gamingfortnite": "ֆորտնիտխաղեր", "fortnitebr": "ֆորտնայթբռ", "retrovideogames": "հինվիդեոաղուրդներ", "scaryvideogames": "հնարավորությունորըվախենալուրախաղեր", "videogamemaker": "վիրտուալխաղերիարտադրող", "megamanzero": "մեգամենզերո", "videogame": "videogame", "videosgame": "խաղերտեսանյութեր", "professorlayton": "պրոֆեսորլեյթոն", "overwatch": "օվերվաչ", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "հաղորդակցության101", "battleblocktheater": "արտադրականհարթակ", "arcades": "արխադեններ", "acnh": "acnh", "puffpals": "փափուկընկերներ", "farmingsimulator": "հողագործությանսիմուլատոր", "robloxchile": "robloxչիլե", "roblox": "ռոբլոքս", "robloxdeutschland": "ռոբլոքսգերմանիա", "robloxdeutsch": "roboxհայերեն", "erlc": "էրլց", "sanboxgames": "սանվոքսխաղեր", "videogamelore": "վիդեո게임երեղբայրություն", "rollerdrome": "ռոլերդրոմ", "parasiteeve": "փարազիտեվ", "gamecube": "գեյմկյուբ", "starcraft2": "աստղադաշտ2", "duskwood": "դուսքվուդ", "dreamscape": "երազայինաշխարհ", "starcitizen": "աստղաբնակ", "yanderesimulator": "յանդերեսիմուլյատոր", "grandtheftauto": "մեծգողությունauto", "deadspace": "ամսվաազնիվ", "amordoce": "համովդուզ", "videogiochi": "վիդեոխաղեր", "theoldrepublic": "հինհանրապետություն", "videospiele": "վիդեոխաղեր", "touhouproject": "touhouproject", "dreamcast": "հրեշտակայինէս", "adventuregames": "մակաբույծախաղեր", "wolfenstein": "wolfenstein", "actionadventure": "գործողությունևարկածություն", "storyofseasons": "վերջիշխանություն", "retrogames": "հինխաղեր", "retroarcade": "ռետրոարխադ", "vintagecomputing": "հինհամակարգեր", "retrogaming": "հինխաղեր", "vintagegaming": "վետերանայինհամակարգիչներ", "playdate": "խաղայինօր", "commanderkeen": "կոմանդիքին", "bugsnax": "բուգսնաքս", "injustice2": "ինենցարդարություն2", "shadowthehedgehog": "վիզականնարդուկը", "rayman": "ռեյմեն", "skygame": "երկնադաշտ", "zenlife": "զենկյանք", "beatmaniaiidx": "բիթմանիաիդդքս", "steep": "խիստ", "mystgames": "mystgames", "blockchaingaming": "բլոկչեյնխաղեր", "medievil": "միջնադարյան", "consolegaming": "համակարգիչխաղեր", "konsolen": "կոնսոլեն", "outrun": "բարեխոսել", "bloomingpanic": "թափանցիկուզման", "tobyfox": "tobyfox", "hoyoverse": "հոիովերս", "senrankagura": "սենրանկագործություն", "gaminghorror": "gamingհτρόսանք", "monstergirlquest": "չարանումազգացեռնցում", "supergiant": "սուպերգիգանտ", "disneydreamlightvalle": "դիսնեյդրեմլայթվալլե", "farmingsims": "գյուղացիականսիմուլյացիաներ", "juegosviejos": "հինխաղեր", "bethesda": "բեթսդա", "jackboxgames": "ջեքբոքսխաղեր", "interactivefiction": "համագործակցայինպատմություն", "pso2ngs": "pso2ngs", "grimfandango": "հավեսմահը", "thelastofus2": "վերջինմեզից2", "amantesamentes": "սերևրենք", "visualnovel": "հաղորդակցականառճակատ", "visualnovels": "հայկականվիզուալհեքերը", "rgg": "ռգգ", "shadowolf": "հյուսիսայինգայլ", "tcrghost": "tcrհարրի", "payday": "կամավորիօր", "chatherine": "քեթարին", "twilightprincess": "քիչևեռագլուխ", "jakandaxter": "ջականդաքստեր", "sandbox": "ավազլիճ", "aestheticgames": "ոստիկանականխաղեր", "novelavisual": "նորավիզուալ", "thecrew2": "thecrew2", "alexkidd": "ալեքսկիդ", "retrogame": "հինխաղ", "tonyhawkproskater": "տոնիհոկպրոսկեյթեր", "smbz": "միմինբո", "lamento": "լամենտո", "godhand": "աստվածայինդուք", "leafblowerrevolution": "տերևաբողկայծակնահար", "wiiu": "ւյուղեք", "leveldesign": "հարթակիզում", "starrail": "թղթայինշավերը", "keyblade": "ключеագեց", "aplaguetale": "aplaguetale", "fnafsometimes": "ֆնֆարտեառնաշատ", "novelasvisuales": "տեսավեպեր", "robloxbrasil": "առավելապիտուին", "pacman": "պակման", "gameretro": "gameretro", "videojuejos": "վիդեոներ", "videogamedates": "վիդեոխաղայինշնորհավերներ", "mycandylove": "իմքաղցրսերան", "megaten": "մեգատեն", "mortalkombat11": "մորտալկոմբատ11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "հուլկխաղեր", "batmangames": "բաթմանխաղեր", "returnofreckoning": "վերադարձիժամը", "gamstergaming": "գամստերգեյմինգ", "dayofthetantacle": "ուղղաթալեկտոնիօր", "maniacmansion": "մանիակյանհայտարություն", "crashracing": "քարշավավթար", "3dplatformers": "3դհարթակայիններ", "nfsmw": "nfsmw", "kimigashine": "քիմիգաշինե", "oldschoolgaming": "հինդպրոցխաղեր", "hellblade": "հեղբայրաձախոց", "storygames": "հաղորդմանխաղեր", "bioware": "բիոեր", "residentevil6": "ապոկալիպսիս6", "soundodger": "ձայնադողեր", "beyondtwosouls": "անցյալիցմարզվիր", "gameuse": "խաղարկում", "offmortisghost": "շարժվիրկարողիմեռելից", "tinybunny": "ամենափոքրկապիկ", "retroarch": "ռետրոարտ", "powerup": "ուժեղացիր", "katanazero": "կատանաzeros", "famicom": "ֆամիկոմ", "aventurasgraficas": "gra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "արագլույս", "fzero": "ֆզերո", "gachagaming": "գախագեյմինգ", "retroarcades": "հինարխադներ", "f123": "f123", "wasteland": "վայրիաշխարհ", "powerwashsim": "ուժեղմաքրողսիմ", "coralisland": "զատկիերկիր", "syberia3": "սիբերիա3", "grymmorpg": "գրիմմորմպգ", "bloxfruit": "bloxպտուղ", "anotherworld": "այլաշխարհ", "metaquest": "մետավիրություն", "animewarrios2": "անիմեվերներ2", "footballfusion": "ֆուտբոլայինֆյուզիոն", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "աստրոնիր", "legomarvel": "լեգոմառվել", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "թեքմետալ", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "շղթայի_հիմքը", "simulator": "սիմուլյատոր", "symulatory": "սիմուլյատոր", "speedrunner": "հրեշավող", "epicx": "եզդեպքx", "superrobottaisen": "սուպերրոբոտաիսեն", "dcuo": "dcuo", "samandmax": "սամանդմաքս", "grywideo": "գրիջիդեո", "gaiaonline": "gaiaonline", "korkuoyunu": "լոպարապարկերը", "wonderlandonline": "կարմանաշխարհցանցում", "skylander": "երկինքիներ", "boyfrienddungeon": "հանրապետությանցուն", "toontownrewritten": "տունտաունվերագրություն", "simracing": "սիմռեյսինգ", "simrace": "սիմռեյս", "pvp": "pvp", "urbanchaos": "քաղաքայինխառնաշփոթ", "heavenlybodies": "կենդանիւրաւներ", "seum": "<PERSON>եմ", "partyvideogames": "ղեկայինխաղեր", "graveyardkeeper": "կենդանությանպահապան", "spaceflightsimulator": "տիեզերականթռիչքներիսիմուլյատոր", "legacyofkain": "քեյնիանհատականություն", "hackandslash": "հակեկտրոնիկ", "foodandvideogames": "ուտելիքևխաղեր", "oyunvideoları": "օյունվիդեոներ", "thewolfamongus": "հ狼们մեզմիջև", "truckingsimulator": "բեռնատարերիսիմուլյատոր", "horizonworlds": "հորիզոնականաշխարհներ", "handygame": "լուռխաղ", "leyendasyvideojuegos": "լեգենդայինվիդեոխաղեր", "oldschoolvideogames": "հինսոցիալականվիդեոխաղեր", "racingsimulator": "արջիօդանավ", "beemov": "բիմով", "agentsofmayhem": "մայիսիանները", "songpop": "հնչյունառողջություն", "famitsu": "ֆամիցու", "gatesofolympus": "օլիմպոսիդռներն", "monsterhunternow": "իշխանազոներիորսթույլտվեք", "rebelstar": "հակառակաստղ", "indievideogaming": "ինդիvideոgaming", "indiegaming": "ինքնուրալիտիկխաղեր", "indievideogames": "ինդիვიდეოխաղեր", "indievideogame": "ինդիֆիլմայինխաղ", "chellfreeman": "չելլֆրիմեն", "spidermaninsomniac": "սպայդերմենինսոմնիակ", "bufffortress": "բումֆորտրես", "unbeatable": "անհաղթ", "projectl": "պռոեկտլ", "futureclubgames": "աշխարհիանցյալխաղեր", "mugman": "մագման", "insomniacgames": "ինսոմնիախաղեր", "supergiantgames": "սուպերհսկայինխաղեր", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "հենռիստիկմին", "celestegame": "celestegame", "aperturescience": "բացառությունագիտություն", "backlog": "հետաձգման_ցուցակ", "gamebacklog": "խաղայինհաշիվ", "gamingbacklog": "գեյմինգի_հաստվածություն", "personnagejeuxvidéos": "կինոաշխարհիֆիգուրներ", "achievementhunter": "հաղթանակասեր", "cityskylines": "քաղաքիտեսարաններ", "supermonkeyball": "սուպեռմոնկիգունդ", "deponia": "դեպոնիա", "naughtydog": "ոչխարիշտշուն", "beastlord": "բիստլորդ", "juegosretro": "հինխաղեր", "kentuckyroutezero": "քենթուկիուղի00", "oriandtheblindforest": "օրիևզոհրեվտիանտառ", "alanwake": "ալանվարթոյի", "stanleyparable": "սթենլիիպարաբոլան", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "ստաքսել", "videogameost": "վիդեոխաղերիօրստ", "dragonsync": "դրակոններապահովում", "vivapiñata": "վիվապինյաթա", "ilovekofxv": "եսսիրումկոֆxv", "arcanum": "արդյունաբերություն", "neoy2k": "նեոյ2կ", "pcracing": "pcracing", "berserk": "բերսերկ", "baki": "բակի", "sailormoon": "սեյլորմուն", "saintseiya": "սուրբսեյա", "inuyasha": "ինույաշա", "yuyuhakusho": "յույուհակusho", "initiald": "նախնականd", "elhazard": "էլհազարդ", "dragonballz": "դրակոնաբարցի", "sadanime": "դառաջանիմե", "darkerthanblack": "մռմռuetthanblack", "animescaling": "անիմեսկեյլինգ", "animewithplot": "անիմեներևմանավեր", "pesci": "պեսչի", "retroanime": "ռետրոանիմե", "animes": "անշուններ", "supersentai": "սուպերսենտայ", "samuraichamploo": "սամուրայչամպլու", "madoka": "մադոկա", "higurashi": "հիգուրաշի", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "90սերիալ", "darklord": "մութնահանգապետ", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "աշխարհիամսագրի", "samuraix": "սամուրայք", "dbgt": "դբգթ", "veranime": "կարդալով", "2000sanime": "2000ներիանշիմե", "lupiniii": "լուպինովա", "drstoneseason1": "դոկտորստոնեսեզոն1", "rapanime": "ռափանիմե", "chargemanken": "չարչարենքմենք", "animecover": "անիմեոփտավ", "thevisionofescaflowne": "էսկաֆլոներիիրականությունը", "slayers": "սլեյերզ", "tokyomajin": "տոկոյոմաջին", "anime90s": "անիմե90ականներ", "animcharlotte": "անինչարլոթ", "gantz": "գանթզ", "shoujo": "շոջո", "bananafish": "բանանաձուկ", "jujutsukaisen": "ջուջուցուկայսեն", "jjk": "jjk", "haikyu": "հայքյու", "toiletboundhanakokun": "դոնորանվածստացիոնար", "bnha": "բնհա", "hellsing": "հելսինգ", "skipbeatmanga": "skipbeatհումոր", "vanitas": "վանինտաս", "fireforce": "հրեղենուժ", "moriartythepatriot": "moriartyանասասպան", "futurediary": "ֆուտուրքամառս", "fairytail": "ֆեյրիտեյլ", "dorohedoro": "դորոհեդորո", "vinlandsaga": "վինլանդսագա", "madeinabyss": "աստվածածնիերկրից", "parasyte": "պարազիտ", "punpun": "պանպան", "shingekinokyojin": "շինգեկինոկյոջին", "mushishi": "մուշիշի", "beastars": "բիստարս", "vanitasnocarte": "վանիտասնոկարտ", "mermaidmelody": "հիմնեարդհնչյուն", "kamisamakiss": "կամիսամաքսkiss", "blmanga": "բլմանգա", "horrormanga": "հրեշմանգա", "romancemangas": "բառսյացմանգաներ", "karneval": "կարմենվալ", "dragonmaid": "դրակոնիծառեր", "blacklagoon": "սևլագուն", "kentaromiura": "քենտարոյմիուրա", "mobpsycho100": "մոբսիկո100", "terraformars": "տերֆորմերս", "geniusinc": "գենրիուսինկ", "shamanking": "շամանկինգ", "kurokonobasket": "կուրոկոնոբասկետ", "jugo": "ջուգո", "bungostraydogs": "բունգոաշխատողկեղտերն", "jujustukaisen": "ջուջուսթուկայսեն", "jujutsu": "ջուջուցու", "yurionice": "յուրիօնիսը", "acertainmagicalindex": "հայտնիմոգականցուցակ", "sao": "սաո", "blackclover": "սևհասpopular", "tokyoghoul": "թոքիգոհուլ", "onepunchman": "մեկհարվածամարդկանց", "hetalia": "հետալիա", "kagerouproject": "կատեսարքպրոեկտ", "haikyuu": "հայքյու", "toaru": "տոարու", "crunchyroll": "կռանչիռոլ", "aot": "<PERSON><PERSON>", "sk8theinfinity": "sk8կայծնորություն", "siriusthejaeger": "սիրիուսթեհեյգեր", "spyxfamily": "մեղվորականխfamilien", "rezero": "րեզերո", "swordartonline": "սուրայինարվեստներցանցում", "dororo": "դորորո", "wondereggpriority": "հնչեցուցաելայինօրակարգ", "angelsofdeath": "մահվանթմարգարիտներ", "kakeguri": "կակեգուրի", "dragonballsuper": "դրագոնբալsuper", "hypnosismic": "հիպնոսիմսիկ", "goldenkamuy": "գոլդենկամույ", "monstermusume": "մոնստերմուսումե", "konosuba": "կոնոսուբա", "aikatsu": "այկացում", "sportsanime": "համարյաանիմե", "sukasuka": "սուկասուկա", "arwinsgame": "արզվինսպորտ", "angelbeats": "հերոսապտուղներ", "isekaianime": "իսեկայաընիմե", "sagaoftanyatheevil": "սագաոֆթանքյանբարեպաշտ", "shounenanime": "շounenanime", "bandori": "բանդորի", "tanya": "տանիա", "durarara": "դուրառա", "prettycure": "prettycure", "theboyandthebeast": "տղանեւգազանը", "fistofthenorthstar": "հյուսիսայինաստղիաթափառիկ", "mazinger": "mazinger", "blackbuttler": "սևմենեջեր", "towerofgod": "աստծոաշտարակ", "elfenlied": "էլֆենլիդ", "akunohana": "ակալունոհանա", "chibi": "չիբի", "servamp": "սերվամպ", "howtokeepamummy": "ինչպեսպահենքմամային", "fullmoonwosagashite": "լուսինբուրեցնեմboo", "shugochara": "շուգոչարա", "tokyomewmew": "թոքիզաբիծ", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "լուսավորուվևվախեցնող", "martialpeak": "ռազմականշեռք", "bakihanma": "բակիենհամա", "hiscoregirl": "հայհամարաղջիկ", "orochimaru": "որոչիմարու", "mierukochan": "միերուկոչան", "dabi": "դաբի", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "աստոլֆո", "revanantfae": "ռևանանդֆեյ", "shinji": "շինջի", "zerotwo": "զերոերկու", "inosuke": "ինոսուկե", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "մոնստերաղջիկ", "kanae": "կանաե", "yone": "յոնե", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "կակաշի", "lenore": "լենոր", "benimaru": "բենիմարու", "saitama": "սայթամա", "sanji": "սանջի", "bakugo": "բակուգո", "griffith": "գրիֆիթ", "ririn": "ռիրին", "korra": "կոռա", "vanny": "վանի", "vegeta": "վեգետա", "goromi": "գորոմի", "luci": "լուչի", "reigen": "ռեյգեն", "scaramouche": "սկարակուչ", "amiti": "հարիսա", "sailorsaturn": "ծովագնացներսատուրն", "dio": "դիո", "sailorpluto": "ծովաճգնաժամպլուտո", "aloy": "ալոյ", "runa": "ռունա", "oldanime": "հինմանգա", "chainsawman": "մետաղականբացասանը", "bungoustraydogs": "բունգոոկուճկաններ", "jogo": "jogo", "franziska": "ֆրանցիսկա", "nekomimi": "նեկոմիմի", "inumimi": "ինումիմի", "isekai": "իսեկայ", "tokyorevengers": "տոկիոնկեղծարարներ", "blackbutler": "սևսեռեխոս", "ergoproxy": "երգոպրոքսի", "claymore": "կլեյմորը", "loli": "լոլի", "horroranime": "հրաշալիանշաններ", "fruitsbasket": "մրգերիաստղիկ", "devilmancrybaby": "դևիընկերնէ哭泣", "noragami": "նորագաման", "mangalivre": "mangalivre", "kuroshitsuji": "կուրոշիցուջի", "seinen": "սեյնեն", "lovelive": "սերապրագործի", "sakuracardcaptor": "սակուրակարդքապտոր", "umibenoetranger": "ումիբենոետրանկեր", "owarinoseraph": "օվարինոսեռաֆ", "thepromisedneverland": "ղեկցողաշխարհ", "monstermanga": "մոնստերմանգա", "yourlieinapril": "քոստերըապրիլում", "buggytheclown": "բուգիֆրկը", "bokunohero": "բոկունոհերո", "seraphoftheend": "զենիթիարջան", "trigun": "տրիգուն", "cyborg009": "կիբերգայֆ009", "magi": "magi", "deepseaprisoner": "խորասուզմանոստալեյը", "jojolion": "jojo<PERSON>", "deadmanwonderland": "մահացածըկախարդություններիերկիր", "bannafish": "բաննաֆիշ", "sukuna": "սուկունա", "darwinsgame": "դարվինիխաղը", "husbu": "հյուսբու", "sugurugeto": "սուգուրուգետո", "leviackerman": "լևիակերման", "sanzu": "սանցու", "sarazanmai": "сарազанmai", "pandorahearts": "pandoraիհcardíանց", "yoimiya": "յոիմիա", "foodwars": "ուտելիքապարտություն", "cardcaptorsakura": "քարտեղբերողսակուրա", "stolas": "ստոլաս", "devilsline": "դևերի_գիծ", "toyoureternity": "թվածիքիդնեն", "infpanime": "ինֆպանիեմն", "eleceed": "ելիսիդ", "akamegakill": "ակամեգակիլ", "blueperiod": "藍色期", "griffithberserk": "գրիֆիթբերզերկ", "shinigami": "շինիգամի", "secretalliance": "գաղտնիalianս", "mirainikki": "միրաինիքի", "mahoutsukainoyome": "մահուցուկայինում", "yuki": "յուկի", "erased": "կանոսվեց", "bluelock": "կապույտղեկոց", "goblinslayer": "goblinslayer", "detectiveconan": "դետեկտիվկոնան", "shiki": "շիկի", "deku": "դեկու", "akitoshinonome": "ակիտոշինոնոմե", "riasgremory": "րիասգրեմորի", "shojobeat": "շոջոբիթ", "vampireknight": "վամպիրիւամարտիչ", "mugi": "մուկի", "blueexorcist": "կանաչեկյալ", "slamdunk": "սլեմդանկ", "zatchbell": "զաչբել", "mashle": "մաշլե", "scryed": "հորանջեց", "spyfamily": "լրտեսներիընտանիք", "airgear": "օդայինհագուստ", "magicalgirl": "հետաքրքիրուհի", "thesevendeadlysins": "հկըյեիյհուլիյույրնրրըխ77777", "prisonschool": "զուցերիդպրոց", "thegodofhighschool": "հայտնիերիորդիաղջիկ", "kissxsis": "համբույրքույր", "grandblue": "մեծինկեր", "mydressupdarling": "իմուտցեղտիկս", "dgrayman": "դգրեյման", "rozenmaiden": "ռոզենմայդեն", "animeuniverse": "անիմեաշխարհ", "swordartonlineabridge": "տրենայինարվեստիապրեցը", "saoabridged": "saoբեռնավերացված", "hoshizora": "հոշիզորա", "dragonballgt": "dragonballgt", "bocchitherock": "բոչիդեթռոք", "kakegurui": "կակեմուրի", "mobpyscho100": "մոբպեսխո100", "hajimenoippo": "հաջիմենոիպպո", "undeadunluck": "ոչմեռնբախտ", "romancemanga": "ռոմանսմանգա", "blmanhwa": "բլմանիhwa", "kimetsunoyaba": "կիմեցունոյաբա", "kohai": "կոհայ", "animeromance": "իմածինսպասքնարց", "senpai": "սենպայ", "blmanhwas": "բլմանհվաs", "animeargentina": "անիմեարկենտինա", "lolicon": "լոլիկոն", "demonslayertothesword": "դեմոնսլեյերևդրախտին", "bloodlad": "արյունլադ", "goodbyeeri": "մաղթումեմբու", "firepunch": "հրաոաջություն", "adioseri": "ադիոսերի", "tatsukifujimoto": "թացուկիֆուժիմոտո", "kinnikuman": "կիննիկուման", "mushokutensei": "մուշոկուտենսեյ", "shoujoai": "շոջոայ", "starsalign": "աստղերըհամընկնումեն", "romanceanime": "երիտասարդներիռոմանտիկանիմե", "tsundere": "ցունդերե", "yandere": "յանդերե", "mahoushoujomadoka": "մահուշոջոջոմադոկա", "kenganashura": "կենգանաշուրա", "saointegralfactor": "սաոինտեգրամպրակտոր", "cherrymagic": "խաղողիկախարդություն", "housekinokuni": "տունկինոաշխարհ", "recordragnarok": "հասցեականռագնարոկ", "oyasumipunpun": "oyasumipunpun", "meliodas": "մելիոդաս", "fudanshi": "ֆուդանշի", "retromanga": "ռետրոմանգա", "highschoolofthedead": "մահվճաղмектеп", "germantechno": "գերմանթեխնո", "oshinoko": "<PERSON><PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "անսացուկյոշիցու", "vindlandsaga": "անտառաննշառներ", "mangaka": "մանգա", "dbsuper": "dbsuper", "princeoftennis": "թենիսիքաղաքապետ", "tonikawa": "թոնիկավա", "esdeath": "էսդեթ", "dokurachan": "դոքուրաչան", "bjalex": "բջալեքս", "assassinclassroom": "սպանաուսաստան", "animemanga": "անիմեմանգա", "bakuman": "բաքուման", "deathparade": "մահապարուտ", "shokugekinosouma": "շոկուգեկինոսումա", "japaneseanime": "ճապոնականանիմե", "animespace": "անիմեաշխարհ", "girlsundpanzer": "աղջիկներունպանծեր", "akb0048": "ակբ0048", "hopeanuoli": "հուսանքնորալի", "animedub": "ենիմեդուբ", "animanga": "անշիմանգա", "tsurune": "ծուռունե", "uqholder": "ուղեղաբու", "indieanime": "ինդիանիմե", "bungoustray": "բունգոստրեյ", "dagashikashi": "դագաշիկաշի", "gundam0": "գունդամ0", "animescifi": "անիմեսկայֆայ", "ratman": "լապտերայինչը", "haremanime": "հարեմանիմé", "kochikame": "կոչիկամե", "nekoboy": "նեկոբոյ", "gashbell": "gashbell", "peachgirl": "ծաղիկաղջիկ", "cavalieridellozodiaco": "զodiacգալերիստներ", "mechamusume": "մեջամուսումե", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "յարիչինբիչկլուբ", "dragonquestdai": "դրագոնքեստդայ", "heartofmanga": "մանգայիदिल", "deliciousindungeon": "ճճուրթուվdungeonում", "manhviyaoi": "մանհվիայոի", "recordofragnarok": "ռեգիստրավագար", "funamusea": "ֆանամուզեա", "hiranotokagiura": "հիրանոտոկագիուրա", "mangaanime": "մանգաանիմե", "bochitherock": "բոցիմիռոք", "kamisamahajimemashita": "կամիսամահաջիմեմաշիտա", "skiptoloafer": "skipթուլամորթ", "shuumatsunovalkyrie": "շումացունովալկիրյե", "tutorialistoohard": "հայեցիբավականինբարդ", "overgeared": "overgeared", "toriko": "թորիկո", "ravemaster": "ռեյվմաստեր", "kkondae": "քքոնդայ", "chobits": "չոբիտս", "witchhatatelier": "հառաչատառան", "lansizhui": "լանցիզհույ", "sangatsunolion": "սանգացունոլիոն", "kamen": "քամեն", "mangaislife": "մանգայիկյանքնէ", "dropsofgod": "աստծոկաթիլներ", "loscaballerosdelzodia": "զոդիակիմեկարդիտղաները", "animeshojo": "հայալիզյալ", "reverseharem": "հակառակհարեմ", "saintsaeya": "սուրբսեյա", "greatteacheronizuka": "իանալուրալիզավիկուց", "gridman": "գրիղդման", "kokorone": "կոկորոնե", "soldato": "soldato", "mybossdaddy": "իմսկզբումադուն", "gear5": "gear5", "grandbluedreaming": "հայաստանէլերիմի", "bloodplus": "արյունպլյուս", "bloodplusanime": "արյունանիմե", "bloodcanime": "արյունկանիմե", "bloodc": "արյունc", "talesofdemonsandgods": "իշխանություններըևաստվածները", "goreanime": "արյունալիանիմե", "animegirls": "անիմեգիրլս", "sharingan": "շերինգան", "crowsxworst": "տۇقու_ի_ավերիչներ", "splatteranime": "տատումանեմե", "splatter": "քաիմյութ", "risingoftheshieldhero": "պաշտպանիհերոսուհուառաջացումը", "somalianime": "սոմալիանիմե", "riodejaneiroanime": "րիոդեժանեյրոանիմե", "slimedattaken": "սլայմդատաքէն", "animeyuri": "ամնևսուրի", "animeespaña": "անիմեսպանյա", "animeciudadreal": "animeciudadreal", "murim": "murin", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "ծովախեցգետիններիանմեղները", "liarliar": "կեղծիքքեղծիք", "supercampeones": "սուպերքեմպիոններ", "animeidols": "անիմետուքիներ", "isekaiwasmartphone": "իսկայիկանսմարտահամար", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "մաղթանքնաղջիկներ", "callofthenight": "գիշերայինկանչ", "bakuganbrawler": "բակուգանխաղացող", "bakuganbrawlers": "բակուգանմենամարտողներ", "natsuki": "նացուկի", "mahoushoujo": "մահուշոյի", "shadowgarden": "եղերապուրի", "tsubasachronicle": "ծղակուհուօրհներգ", "findermanga": "գտիրմանգա", "princessjellyfish": "נסելիայինհարսի", "kuragehime": "կուրաժեհիմե", "paradisekiss": "պարադիսիհամբույր", "kurochan": "քուրոչան", "revuestarlight": "քաղաքայինաստառ", "animeverse": "անիմեաշխարհ", "persocoms": "փերսոկոմներ", "omniscientreadersview": "բոլիլիասումարդուերևույթ", "animecat": "անիմեկատ", "animerecommendations": "անշաչենկանալիսвучեմ", "openinganime": "սկիզբանիմե", "shinichirowatanabe": "շինիչիրովաթանաբե", "uzumaki": "ուզումակի", "myteenromanticcomedy": "իմանիորկոմեդիա", "evangelion": "երևանյան", "gundam": "գունդամ", "macross": "մակրոս", "gundams": "գունդամներ", "voltesv": "վոլտեսվ", "giantrobots": "մեծռոբոտներ", "neongenesisevangelion": "նեոնգենեսիսեվանգելյոն", "codegeass": "քոդգեաս", "mobilefighterggundam": "մոբայլֆայթർջիգունդամ", "neonevangelion": "նեոնեվանգելիոն", "mobilesuitgundam": "հեռախոսայինհագնոցիուական", "mech": "մեջ", "eurekaseven": "երեքսեան", "eureka7": "յուրեկաներ7", "thebigoanime": "thebigoanime", "bleach": "յուրեղին", "deathnote": "մահվանմատյան", "cowboybebop": "կովբոյբեփ", "jjba": "jjba", "jojosbizarreadventure": "jojոսբիզարարկություն", "fullmetalalchemist": "լpenuhmetalalchemist", "ghiaccio": "ցրտավարութիուն", "jojobizarreadventures": "ժաժոբիզաառարկաներ", "kamuiyato": "կամուիյատո", "militaryanime": "հայաստանյանանիմե", "greenranger": "կապույտջիգուր", "jimmykudo": "ջիմիկուդո", "tokyorev": "տոկիոռև", "zorro": "զորո", "leonscottkennedy": "լեոնսկոտկենեդի", "korosensei": "կորոսենսեյ", "starfox": "աստղատեսլա", "ultraman": "ուլտրաման", "salondelmanga": "սալոնդելմանգա", "lupinthe3rd": "լուպինթեթրու", "animecity": "անիմեքաղաք", "animetamil": "անիմետամիլ", "jojoanime": "jojոանիմե", "naruto": "նարաբուտո", "narutoshippuden": "նարուտոշիպուդեն", "onepiece": "մեկսենյակ", "animeonepiece": "անիմեօնեպիս", "dbz": "դբզ", "dragonball": "դրագոնբոլtíp", "yugioh": "յուգիոհ", "digimon": "դիգիմոն", "digimonadventure": "դիջիմոնարկած", "hxh": "hxh", "highschooldxd": "հայերձբեռբվաչան", "goku": "գոքու", "broly": "բրոալին", "shonenanime": "շոնենանիմե", "bokunoheroacademia": "բոկունոհերոակադեմիա", "jujustukaitsen": "ջուջուստուքայլենք", "drstone": "դոկտորքար", "kimetsunoyaiba": "կիմեցունոյաիբա", "shonenjump": "շոնենջամպ", "otaka": "ոտակի", "hunterxhunter": "հանտերxhանտեր", "mha": "մհա", "demonslayer": "դևասլայեր", "hinokamikagurademonsl": "հինոքամիկագուրադեմոնսլ", "attackontitan": "հարձակմանհսկաներ", "erenyeager": "երեներկյուն", "myheroacademia": "միմիհերոսակադեմիա", "boruto": "բոռուտո", "rwby": "ռուվի", "dandadan": "դանդադան", "tomodachigame": "տոմոդաչիգեյմ", "akatsuki": "ակացիքի", "surveycorps": "հարցմանֆորսը", "onepieceanime": "միառենքանսահարը", "attaquedestitans": "հարձակվեցանթիթակների", "theonepieceisreal": "մեկպեսերեալէ", "revengers": "հետապնդողներ", "mobpsycho": "մոբահոգիչ", "aonoexorcist": "aonoexorcist", "joyboyeffect": "ուրախտղեքիազդեցություն", "digimonstory": "դիջիմոնզինություն", "digimontamers": "դիջիմոնտամեններ", "superjail": "սուպերբանտ", "metalocalypse": "մետալակլիպս", "shinchan": "շինչան", "watamote": "watamote", "uramichioniisan": "ուրամիչիոնիզան", "uruseiyatsura": "ուրսեյացուրա", "gintama": "gintama", "ranma": "ռանմա", "doraemon": "դորայեմոն", "gto": "gto", "ouranhostclub": "հուրանհոստկլուբ", "flawlesswebtoon": "հաջողագույնվեբթուն", "kemonofriends": "կեմոնոֆրենդս", "utanoprincesama": "ուտանոփրինցեսամա", "animecom": "անջելներ", "bobobobobobobo": "բոբոբոբոբոբո", "yuukiyuuna": "յուքիյունա", "nichijou": "նիխիջոու", "yurucamp": "յուրուքեմպ", "nonnonbiyori": "նոննոնբիյորի", "flyingwitch": "թռչողդրամա", "wotakoi": "wotakoi", "konanime": "կոնանիմե", "clannad": "կլաննադ", "justbecause": "իմանկանիպատճառ", "horimiya": "հորիմիա", "allsaintsstreet": "ավետմիրբանջարան", "recuentosdelavida": "կյանքիանտառներ"}