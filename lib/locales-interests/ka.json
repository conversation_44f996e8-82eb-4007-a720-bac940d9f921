{"2048": "2048", "mbti": "mbti", "enneagram": "ენეაგრამა", "astrology": "ასტროლოგია", "cognitivefunctions": "შემეცნებითიფუნქციები", "psychology": "ფსიქოლოგია", "philosophy": "ფილოსოფია", "history": "ისტორია", "physics": "ფიზიკა", "science": "მეცნიერება", "culture": "კულტურა", "languages": "ენები", "technology": "ტექნოლოგია", "memes": "memeები", "mbtimemes": "mbtimemeები", "astrologymemes": "ასტროლოგიურიmemeები", "enneagrammemes": "ენეაგრამაmemeები", "showerthoughts": "შხაპისფიქრები", "funny": "სასაცილო", "videos": "ვიდეოები", "gadgets": "გაჯეტები", "politics": "პოლიტიკა", "relationshipadvice": "ურთიერთობისრჩევა", "lifeadvice": "ცხოვრებისეულირჩევა", "crypto": "კრიპტო", "news": "ახალიამბები", "worldnews": "მსოფლიოამბები", "archaeology": "არქეოლოგია", "learning": "სწავლა", "debates": "დებატები", "conspiracytheories": "შეთქმულებისთეორიები", "universe": "სამყარო", "meditation": "მედიტაცია", "mythology": "მითოლოგია", "art": "ხელოვნება", "crafts": "ხელსაქმე", "dance": "ცეკვა", "design": "დიზაინი", "makeup": "მაკიაჟი", "beauty": "სილამაზე", "fashion": "მოდა", "singing": "სიმღერა", "writing": "წერა", "photography": "ფოტოგრაფია", "cosplay": "კოსფლეი", "painting": "მხატვრობა", "drawing": "ხატვა", "books": "წიგნები", "movies": "ფილმები", "poetry": "პოეზია", "television": "ტელევიზორი", "filmmaking": "ფილმები", "animation": "ანიმაცია", "anime": "ანიმე", "scifi": "სამეცნიეროფანტასტიკა", "fantasy": "ფანტაზია", "documentaries": "დოკუმენტურიფილმები", "mystery": "მისტიკა", "comedy": "კომედია", "crime": "დანაშაული", "drama": "დრამა", "bollywood": "ბოლივუდი", "kdrama": "კორეულიდრამა", "horror": "საშინელება", "romance": "რომანტიკა", "realitytv": "რეალითიშოუ", "action": "მძაფრსიუჟეტიანი", "music": "მუსიკა", "blues": "ბლუზი", "classical": "კლასიკური", "country": "ქანთრი", "desi": "დესი", "edm": "ელექტრონული", "electronic": "ელექტრონული", "folk": "ფოლკლორი", "funk": "ფანკი", "hiphop": "ჰიპჰოპი", "house": "ჰაუსი", "indie": "ინდი", "jazz": "ჯაზი", "kpop": "კორეულიპოპი", "latin": "ლათინური", "metal": "მეტალი", "pop": "პოპი", "punk": "პანკი", "rnb": "rnb", "rap": "რეპი", "reggae": "რეგე", "rock": "როკი", "techno": "ტექნო", "travel": "მოგზაურობა", "concerts": "კონცერტები", "festivals": "ფესტივალი", "museums": "მუზეუმები", "standup": "კომედიშოუ", "theater": "თეატრი", "outdoors": "გარეთ", "gardening": "მებაღეობა", "partying": "წვეულებები", "gaming": "თამაშები", "boardgames": "სამაგიდოთამაშები", "dungeonsanddragons": "dungeonsanddragons", "chess": "ჭადრაკი", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "პოკემონი", "food": "საკვები", "baking": "ცხობა", "cooking": "კულინარია", "vegetarian": "ვეგეტარიანელი", "vegan": "ვეგანი", "birds": "ჩიტები", "cats": "კატები", "dogs": "ძაღლები", "fish": "თევზი", "animals": "ცხოველები", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ბუნებისდაცვა", "feminism": "ფემინიზმი", "humanrights": "ადამიანისუფლებები", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "მოხალისე", "sports": "სპორტი", "badminton": "ბადმინტონი", "baseball": "ბეისბოლი", "basketball": "კალათბურთი", "boxing": "ბოქსი", "cricket": "კრიკეტი", "cycling": "ველოსპორტი", "fitness": "ფიტნესი", "football": "ფეხბურთი", "golf": "გოლფი", "gym": "სპორტდარბაზი", "gymnastics": "ტანვარჯიში", "hockey": "ჰოკეი", "martialarts": "საბრძოლოხელოვნება", "netball": "ნეტბოლი", "pilates": "პილატესი", "pingpong": "პინგპონგი", "running": "სირბილი", "skateboarding": "სკეიტბორდინგი", "skiing": "თხილამურებითსრიალი", "snowboarding": "სნოუბორდინგი", "surfing": "სერფინგი", "swimming": "ცურვა", "tennis": "ჩოგბურთი", "volleyball": "ფრენბურთი", "weightlifting": "ძალოსნობა", "yoga": "იოგა", "scubadiving": "ყვინთვა", "hiking": "ლაშქრობა", "capricorn": "თხისრქა", "aquarius": "მერწყული", "pisces": "თევზები", "aries": "ვერძი", "taurus": "კურო", "gemini": "ტყუპები", "cancer": "კირჩხიბი", "leo": "ლომი", "virgo": "ქალწული", "libra": "სასწორი", "scorpio": "მორიელი", "sagittarius": "მშვილდოსანი", "shortterm": "მოკლევადიანი", "casual": "არაოფიციალური", "longtermrelationship": "გრძელვადიანიურთიერთობა", "single": "მარტოხელა", "polyamory": "პოლიამორია", "enm": "არატრადიციულიურთიერთობა", "lgbt": "ლგბტ", "lgbtq": "ლგბტქ", "gay": "გეი", "lesbian": "ლესბოსელი", "bisexual": "ბისექსუალი", "pansexual": "პანსექსუალი", "asexual": "ასექსუალური", "reddeadredemption2": "წითელისიტყვებისშური2", "dragonage": "დრაკონებისხანა", "assassinscreed": "ასასინსქრედი", "saintsrow": "წმინდანებიrow", "danganronpa": "დანგანრაონა", "deltarune": "დელტარნი", "watchdogs": "wachdogs", "dislyte": "დისლაითი", "rougelikes": "როგულისაყიდი", "kingsquest": "მეფეებისმისია", "soulreaver": "სულმომითი", "suikoden": "სუიკოდენი", "subverse": "სუბვერსია", "legendofspyro": "პატარძლისგვერდზე", "rouguelikes": "რუგლაიკები", "syberia": "საიბერია", "rdr2": "რდრ2", "spyrothedragon": "სპაიროთედრაგონი", "dragonsdogma": "დრაკონებისსიმღერა", "sunsetoverdrive": "მასალაკივირტუალები", "arkham": "არხემი", "deusex": "დეუგექსი", "fireemblemfates": "ცეცხლურინიშნებიბედისქრონიკები", "yokaiwatch": "იოკაივാഩტ", "rocksteady": "როკსტედი", "litrpg": "ლიტრიპგ", "haloinfinite": "ჰელოჰინფინიტ", "guildwars": "გილდიომები", "openworld": "გამotvorteeმსოფლიო", "heroesofthestorm": "ბრძოლისგმირები", "cytus": "ციტუს", "soulslike": "შეხვედრასულიერებისsoullike", "dungeoncrawling": "დუნჯისჩასაფრება", "jetsetradio": "jetsetროთინდება", "tribesofmidgard": "მიდგარდისსამოსახელოები", "planescape": "პლანესქეიპი", "lordsoftherealm2": "ლორდები_სამეფოში2", "baldursgate": "ბალდურსგეით", "colorvore": "კოლორვორი", "medabots": "მედაბოტები", "lodsoftherealm2": "ლოდსოფთერეალმ2", "patfofexile": "პატფოფექსაილი", "immersivesims": "იმერწივისიმები", "okage": "ოკეიჯე", "juegoderol": "მორიგებისმოთამაშე", "witcher": "ვიჩერი", "dishonored": "საქმისგაუფასურება", "eldenring": "ელდენრინგ", "darksouls": "დარკსოულსი", "kotor": "კოტორი", "wynncraft": "ვინკრაფტ", "witcher3": "უინდერლანდესი3", "fallout": "ფოლაუატი", "fallout3": "ფოლაუტ3", "fallout4": "ფოლოუთ4", "skyrim": "სკაირიმი", "elderscrolls": "მოხუცების_scrolls", "modding": "მოდინგი", "charactercreation": "კარაქტერებისშექნა", "immersive": "ყლაპავი", "falloutnewvegas": "ფოლაუტნიუვამგზა", "bioshock": "ბიოშოკი", "omori": "ომორი", "finalfantasyoldschool": "ფინალფანთაზიოლდსქულ", "ffvii": "ffvii", "ff6": "ფფ6", "finalfantasy": "ბოლოფანტაზია", "finalfantasy14": "ფინალფანტაზიას14", "finalfantasyxiv": "ფინალიფენტაზიაქივ", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ფფ13", "finalfantasymatoya": "ბოლოიდეებიამატოია", "lalafell": "ლალაֆელი", "dissidia": "დისიდია", "finalfantasy7": "ფინალფანტაზი7", "ff7": "ff7", "morbidmotivation": "მზაკვრული_motivაცია", "finalfantasyvii": "ფინალფენტაზივii", "ff8": "ფფ8", "otome": "ოტომე", "suckerforlove": "სიყვარულისხულიგანი", "otomegames": "ოტომეთამაშები", "stardew": "სტარდიუ", "stardewvalley": "სტარდიუველი", "ocarinaoftime": "ობიექტი_დროის_ინციდენტი", "yiikrpg": "yiiкрpg", "vampirethemasquerade": "ვამბირისსკვანძი", "dimension20": "ზომა20", "gaslands": "გასლანდიები", "pathfinder": "გზისმაძიებელი", "pathfinder2ndedition": "პატფაინდერი2წელი", "shadowrun": "ინტერნეტსასწავლო", "bloodontheclocktower": "შხამი_წაგებული_მსაათზე", "finalfantasy15": "ფინალფენტაზი15", "finalfantasy11": "ფინალფანტაზი11", "finalfantasy8": "ფინალფანტაზიაი8", "ffxvi": "ფფქვსი", "lovenikki": "ლოვენიკი", "drakengard": "დრაიკენგარდ", "gravityrush": "გზისძვრა", "rpg": "რვგ", "dota2": "დოტა2", "xenoblade": "ზენობლייד", "oneshot": "ერთიატვირთვით", "rpgmaker": "რპგმეიქერი", "osrs": "<PERSON><PERSON><PERSON><PERSON>", "overlord": "მოსული", "yourturntodie": "თქვენირიგიაგარდაიქმნდეთ", "persona3": "პერსონას3", "rpghorror": "როლურითამაშისაშინელება", "elderscrollsonline": "ვაჟბატონებისონლაინ", "reka": "რეა", "honkai": "ჰონკაი", "marauders": "მარაუდერები", "shinmegamitensei": "შინმეგამიტენსეი", "epicseven": "ეპიკ7", "rpgtext": "რპგტექსტი", "genshin": "გენშინი", "eso": "ესო", "diablo2": "დიაფლო2", "diablo2lod": "დიაბლო2ლოდ", "morrowind": "მოროუინდ", "starwarskotor": "ვარსკვლავურიომლებიkotor", "demonsouls": "დემონებისსულები", "mu": "მუსია", "falloutshelter": "მხუთავითავშესაფარი", "gurps": "გურპსი", "darkestdungeon": "მუქიმხარეშიცdarkestdungeons", "eclipsephase": "ეპლიპსფაზა", "disgaea": "დისგეა", "outerworlds": "გარემოსგარეთ", "arpg": "არიპიჯი", "crpg": "ვირტუალურიrpg", "bindingofisaac": "ბაინდინგოფისაკი", "diabloimmortal": "დიაბლოიმორტალი", "dynastywarriors": "დინასტიურიომები", "skullgirls": "ქვინისგოგონები", "nightcity": "ღიისქალაქი", "hogwartslegacy": "ჰოგვარტსისმემკვიდრეობა", "madnesscombat": "მადნესკომბატი", "jaggedalliance2": "ჯაგედალაიანს2", "neverwinter": "არცრახანიაwinter", "road96": "გზა96", "vtmb": "ვტმბ", "chimeraland": "ჩიმერალენდი", "homm3": "ჰომი3", "fe3h": "ფე3ჰ", "roguelikes": "როუგლეიკები", "gothamknights": "გოთემისრაინდები", "forgottenrealms": "დავიწყებულიმეფობები", "dragonlance": "დრაგონლანსი", "arenaofvalor": "არენაავსალტორის", "ffxv": "ffxv", "ornarpg": "ორნარპგ", "toontown": "ტუნტაუნი", "childoflight": "მზისშვილი", "aq3d": "aq3d", "mogeko": "მოგეკო", "thedivision2": "დივიზია2", "lineage2": "ლინეიჯი2", "digimonworld": "დიჯიმონმსოფლიო", "monsterrancher": "მონსტრერთანჩერი", "ecopunk": "ეკოპანკი", "vermintide2": "ვერმინტაიდ2", "xeno": "ქსენო", "vulcanverse": "ვულკანვერსი", "fracturedthrones": "გატეხილიტახტები", "horizonforbiddenwest": "ჰორიზონტიბრძოლებისაღმოსავლელი", "twewy": "ტუეივი", "shadowpunk": "შედარიპუნქტი", "finalfantasyxv": "ფინალფანტაზიაxv", "everoasis": "ყველასოასისი", "hogwartmystery": "ჰოგვორტსისსაიდუმლოებები", "deltagreen": "დელტაგრინი", "diablo": "დიაბლო", "diablo3": "დიაბლო3", "diablo4": "დიაბლო4", "smite": "სმაითი", "lastepoch": "ბოლოეპოქა", "starfinder": "ასტროზარი", "goldensun": "ოქროსმზე", "divinityoriginalsin": "ბუნებაორიგინალურიცოდვა", "bladesinthedark": "ბლედსიშუქუწყვაში", "twilight2000": "ტვაილით2000", "sandevistan": "სანდევისტანი", "cyberpunk": "საიბერპანკი", "cyberpunk2077": "ციბერპანკ2077", "cyberpunkred": "კიბერპანკრედ", "dragonballxenoverse2": "დრაგონბოლიxენოვერსე2", "fallenorder": "დაიცემულიobjednaut", "finalfantasyxii": "ბოლოფენტაზიაxii", "evillands": "მკვლელიმიწები", "genshinimact": "გენშინიმაქტ", "aethyr": "აეთირი", "devilsurvivor": "დევილსაუვერი", "oldschoolrunescape": "ძველისკოლისრუნესკეიპი", "finalfantasy10": "ფინალფანტაზია10", "anime5e": "anime5e", "divinity": "ღვთაეობა", "pf2": "pf2", "farmrpg": "ფერმერულიrpg", "oldworldblues": "შინდისმოდისblues", "adventurequest": "გადასავლებისმค้นვა", "dagorhir": "დაგორჰირი", "roleplayingames": "როლურითამაშები", "roleplayinggames": "როლურითამაშები", "finalfantasy9": "ფინალფანტაზიაცხრა", "sunhaven": "მზისდაყრილი", "talesofsymphonia": "სიმფონიებისმუსიკა", "honkaistarrail": "ჰონკაისტარრაილ", "wolong": "wolong", "finalfantasy13": "ფინალიფანტაზია13", "daggerfall": "დაგერაფალი", "torncity": "ტორეპოლი", "myfarog": "მაზეცხვარი", "sacredunderworld": "საკრედუანდერვალდი", "chainedechoes": "ნახშირბადისხმაური", "darksoul": "შავსულები", "soulslikes": "სულელეშტოები", "othercide": "სხვაანთავისუფლებული", "mountandblade": "ბალახსადამახვილისას", "inazumaeleven": "ინაზუმაელევენი", "acvalhalla": "აკვალჰალა", "chronotrigger": "ქრონოშტრიპერი", "pillarsofeternity": "სამუდიდოისვირტოები", "palladiumrpg": "პალადიუმიrpg", "rifts": "რიფტები", "tibia": "ტიბია", "thedivision": "დივიზია", "hellocharlotte": "გამარჯობაშარლოტა", "legendofdragoon": "დრაკონისლეგენდა", "xenobladechronicles2": "ზენობლედქრონიკლსი2", "vampirolamascarada": "ვამპიროლამაზქარდა", "octopathtraveler": "ოქტოპათიმგზავრი", "afkarena": "აფკარენა", "werewolftheapocalypse": "ვერwolvesაპოკალიფსი", "aveyond": "ავეიონდი", "littlewood": "ლენენგულა", "childrenofmorta": "მორტისბავშვები", "engineheart": "მძიმეგული", "fable3": "ფაბლ3", "fablethelostchapter": "ბუთქუნწისგამlostchapter", "hiveswap": "ჰაივსვეპ", "rollenspiel": "როლური_თამაშები", "harpg": "ჰარფგ", "baldursgates": "ბალდურსგეიტსი", "edeneternal": "ედენéternal", "finalfantasy16": "ფინალფანტაზია16", "andyandleyley": "ანდიედლეილი", "ff15": "ფფ15", "starfield": "თვინააწყო", "oldschoolrevival": "ძველმოდურიახალიაღდგომა", "finalfantasy12": "ფინალფანტაზიაც12", "ff12": "ფფ12", "morkborg": "მორკბერგი", "savageworlds": "საზიზღარიმსოფლიოები", "diabloiv": "დიაბლოივ", "pve": "პვე", "kingdomheart1": "სამეფოსგული1", "ff9": "ფფ9", "kingdomheart2": "მეფობისგული2", "darknessdungeon": "ბნელმზითბო", "juegosrpg": "როლურიაჰოდესაც게임들", "kingdomhearts": "მეფისგული", "kingdomheart3": "სამეფოსგული3", "finalfantasy6": "ფინალურიარმია6", "ffvi": "ffvi", "clanmalkavian": "კლანმალკავიან", "harvestella": "ჰარვესტელა", "gloomhaven": "გლუმჰეივენ", "wildhearts": "ნაჭრისგულები", "bastion": "ბასტიონი", "drakarochdemoner": "დრაკაროჩდემონერ", "skiesofarcadia": "არტკადიისმთები", "shadowhearts": "შედარებისგულები", "nierreplicant": "ნიორრესპლიკანტი", "gnosia": "გნოსია", "pennyblood": "პენიიზ血", "breathoffire4": "ცეცხლისსუნთქვა4", "mother3": "დედა3", "cyberpunk2020": "საიბერპანკ2020", "falloutbos": "ფოლოუატიბოს", "anothereden": "მორიგეედენი", "roleplaygames": "როლითამაშითამაშები", "roleplaygame": "ენტერროლურითამაში", "fabulaultima": "ფაბულაულტიმა", "witchsheart": "ჯადოქრებისგულები", "harrypottergame": "ჰარისპოტერისთამაშო", "pathfinderrpg": "პატფაინდერსპჯი", "pathfinder2e": "პატონდი2ე", "vampirilamasquerade": "ვამპირებისმასკარადი", "dračák": "დრაჩაკი", "spelljammer": "სპელჯამერი", "dragonageorigins": "დრაკონისწლისორჯინალები", "chronocross": "ქრონოკროსი", "cocttrpg": "კოქტბეირსპელინგი", "huntroyale": "ნადირიმეფობა", "albertodyssey": "ალბერტოსodyssey", "monsterhunterworld": "მონსტრებისმონადირეებისსამყაროც", "bg3": "ბგ3", "xenogear": "ხოდიშიგალარია", "temtem": "თემდემი", "rpgforum": "rpgფორუმი", "shadowheartscovenant": "ნამდვილიგულისკავშირი", "bladesoul": "ბლეიდკისული", "baldursgate3": "ბალდურწკარი3", "kingdomcome": "სამეფოსმომავალი", "awplanet": "ავპლანეტ", "theworldendswithyou": "თქვენთანმთავრდებამსოფლიო", "dragalialost": "დრაგალიალოსტ", "elderscroll": "მოწვეულიამაზეც", "dyinglight2": "დაიინგლაით2", "finalfantasytactics": "ფინალფანტაზიატაქტიკა", "grandia": "გრადია", "darkheresy": "შავიერესია", "shoptitans": "შოპტიტანში", "forumrpg": "ფორუმრპგ", "golarion": "გოლარიონ", "earthmagic": "მიწისმagic", "blackbook": "შავიწიგნი", "skychildrenoflight": "მოსაწყისისგზაზებარათზე", "gryrpg": "გრიეფპჯი", "sacredgoldedition": "სიწმინდისოქროსგამოსახულება", "castlecrashers": "ციხისშემოსევები", "gothicgame": "გოთიკურითამაში", "scarletnexus": "სკარლეტნექსუსი", "ghostwiretokyo": "გულიამიყვავისტოკიო", "fallout2d20": "ფოლაუტ2დ20", "gamingrpg": "გეიმინგრიპი", "prophunt": "პროფჰანტი", "starrails": "საქონელაყɐs", "cityofmist": "მოსახლებურიმურა", "indierpg": "ინდირპგ", "pointandclick": "პუნქტირკლიკი", "emilyisawaytoo": "ემილისდიდიხარtoo", "emilyisaway": "ემილიერტშიარასაზღვარზე", "indivisible": "წარმოიდგინე", "freeside": "თავისუფალი", "epic7": "ეპიკ7", "ff7evercrisis": "ff7უდიდკრიზისი", "xenogears": "ზენოგირეები", "megamitensei": "მეგამიტენსეი", "symbaroum": "სიმბაროუm", "postcyberpunk": "პოსტსაიბერპანკი", "deathroadtocanada": "გარდაცვალებისგზაკანადისკენ", "palladium": "პალადიუმი", "knightjdr": "რაინდჯდრი", "monsterhunter": "მონსტრებზემონადირე", "fireemblem": "აჩვენებითმუხტი", "genshinimpact": "გენშინიმპაქტი", "geosupremancy": "გეოსუპრემასია", "persona5": "პერსონაც5", "ghostoftsushima": "გულიცუციშიმას", "sekiro": "სეკირო", "monsterhunterrise": "მონსტრებისმონადირებელიავლა", "nier": "ნიერ", "dothack": "დოთჰაკი", "ys": "ბუ", "souleater": "სულხამი", "fatestaynight": "ფატესთეინაით", "etrianodyssey": "ეტირიანოდეზი", "nonarygames": "გარემოსაშუალებები", "tacticalrpg": "ტაქტიკურიროოლიplaying", "mahoyo": "მაჰოიო", "animegames": "ანიმეისტამაშები", "damganronpa": "დამგანრონპა", "granbluefantasy": "გრანბლუფანტაზიგანთავისუფლდი", "godeater": "გადამუშავებელი", "diluc": "დილუკი", "venti": "ვენთი", "eternalsonata": "ბუნებრივი_სიმფონია", "princessconnect": "პრინცესაკავშირება", "hexenzirkel": "ჰექსენცირკელი", "cristales": "კრისტალები", "vcs": "ვცს", "pes": "პეს", "pocketsage": "ჯიბისბიცხელა", "valorant": "ვალორანტი", "valorante": "ვალორანტე", "valorantindian": "ვალორანტინდური", "dota": "დოტა", "madden": "მადდენ", "cdl": "ცდლ", "efootbal": "იაფიფეხბურთი", "nba2k": "nba2k", "egames": "ელექტრონულითამაშები", "fifa23": "ფიფა23", "wwe2k": "wwe2k", "esport": "ესპორტი", "mlg": "მლგ", "leagueofdreamers": "სამართლებრივიოცნებებისილიგა", "fifa14": "ფიფა14", "midlaner": "მიდლინერი", "efootball": "ელექტრონულიფეხბურთი", "dreamhack": "დრიმჰექი", "gaimin": "გეიმინ", "overwatchleague": "ზიარებისლიგა", "cybersport": "ცისკარატაინინგი", "crazyraccoon": "მგზავრი_ერეკო", "test1test": "ტესტ1ტესტ", "fc24": "ფს24", "riotgames": "რაიოტგეიმსი", "eracing": "ეგოისტობა", "brasilgameshow": "ბრაზილურითამაშებისშოუ", "valorantcompetitive": "ვალორანტიკონკურენტული", "t3arena": "t3arena", "valorantbr": "ვალორანტბრ", "csgo": "ცსგო", "tf2": "tf2", "portal2": "პორტალ2", "halflife": "ჰალფლაიფ", "left4dead": "left4dead", "left4dead2": "ბოლოსმდე2", "valve": "ვალვო", "portal": "პორტალი", "teamfortress2": "ტიმფოსტრეს2", "everlastingsummer": "უდიდესხანგრძილობისზაფხული", "goatsimulator": "ბრძოლაბიტათი", "garrysmod": "გარიჟმოდი", "freedomplanet": "თავისუფლებისპლანეტა", "transformice": "ტრანსფორმისე", "justshapesandbeats": "მხოლოდჯგუფებიდაბითები", "battlefield4": "ბატალიონი4", "nightinthewoods": "ღამისტყეში", "halflife2": "ჰალფლაიფ2", "hacknslash": "ჰაკნსლაში", "deeprockgalactic": "დასწვდილქვისგალაქტიკა", "riskofrain2": "ბოროტებისსახელი2", "metroidvanias": "მეტროიდვანიები", "overcooked": "გაუფუჭებული", "interplanetary": "ინტერპლანეტური", "helltaker": "ჰელტეიკერი", "inscryption": "ინსკრიპცია", "7d2d": "7d2d", "deadcells": "დედსელს", "nierautomata": "ნიერიაოტომატა", "gmod": "gmod", "dwarffortress": "დორჩისციხე", "foxhole": "ბუდესი", "stray": "მარტოსული", "battlefield": "მოედანზე", "battlefield1": "ბატლფილდ1", "swtor": "სვტორი", "fallout2": "ფოლაუტ2", "uboat": "იუბოატი", "eyeb": "ეიებ", "blackdesert": "შავიუდაბნო", "tabletopsimulator": "ტაბლეტოპსიმულატორი", "partyhard": "წვეულებიძალად", "hardspaceshipbreaker": "ჰარდსპეისშიფბრეიქერი", "hades": "ჰეიდესი", "gunsmith": "გამბოტი", "okami": "ოქამი", "trappedwithjester": "დაყვეტილი_ჯესტერთან", "dinkum": "დინა", "predecessor": "წინამორბედი", "rainworld": "მთვარისდრო", "cavesofqud": "ყუდისმღვიმეები", "colonysim": "კოლონიისიმ", "noita": "ნოიტა", "dawnofwar": "გარდობსშეიშენმიძენიმნdawnofwar", "minionmasters": "მინიიონებისმეფეები", "grimdawn": "გვანათლება", "darkanddarker": "მუქიდაბნელა", "motox": "მოტოქსი", "blackmesa": "ბლექმესა", "soulworker": "სულზეამუშავებელი", "datingsims": "დატინგსიმები", "yaga": "ყოგა", "cubeescape": "კუბისგაქცევა", "hifirush": "ჰაიფირუსი", "svencoop": "სვენკუპი", "newcity": "ახალქალაქი", "citiesskylines": "ქალაქებისივეცისღარები", "defconheavy": "დეიფკონშიმძიმე", "kenopsia": "კენოპსია", "virtualkenopsia": "ვირტუალურკენოსპია", "snowrunner": "სნოურანიrige", "libraryofruina": "ბიბლიოთეკა_რუტინა", "l4d2": "l4d2", "thenonarygames": "არარიტორებიც", "omegastrikers": "ომეგასტრაიკერები", "wayfinder": "მარშრუტკაცი", "kenabridgeofspirits": "კენარკინასულიების", "placidplasticduck": "საშუალოპლასტიკურიიხვი", "battlebit": "ბატლბიტ", "ultimatechickenhorse": "უცბადკოკოათვი", "dialtown": "დაილტაუნ", "smileforme": "მეგობრებისთვისუბრალოსმაილიძამით", "catnight": "კატებისღამე", "supermeatboy": "სუპერმmeatboy", "tinnybunny": "ტინიბანქო", "cozygrove": "კოზიგროუ", "doom": "დუმი", "callofduty": "კომენტარიომისკcalls", "callofdutyww2": "callofdutyww2", "rainbow6": "რეინბოუ6", "apexlegends": "აპექსლეგენდები", "cod": "კოდები", "borderlands": "საზღვარი", "pubg": "პაბჯი", "callofdutyzombies": "კომანდირებაისგოგოებო", "apex": "აპექსი", "r6siege": "r6siege", "megamanx": "მეგამენექს", "touhou": "ტოჰოუ", "farcry": "ფარაიკრაი", "farcrygames": "ფარ克რაიგეიმსი", "paladins": "პალადინები", "earthdefenseforce": "დედამიწისდაცვა", "huntshowdown": "ჰანტშოუდაუნ", "ghostrecon": "მოჩვენებაშესაკვეთილი", "grandtheftauto5": "დიდიქურდობაავტომობილების5", "warz": "ომი", "sierra117": "სიერა117", "dayzstandalone": "დაიზსტენდალოუნი", "ultrakill": "ულტრაკილი", "joinsquad": "გუნდშეუერთდი", "echovr": "ეგოჰვრვ", "discoelysium": "დისკოელისიუმ", "insurgencysandstorm": "გაღვიძებისქარისწიში", "farcry3": "ფარაქრი3", "hotlinemiami": "ჰოთლაინმაიამი", "maxpayne": "მაქსპეინ", "hitman3": "ჰიტმანის3", "r6s": "რ6ს", "rainbowsixsiege": "ეროვნილი6სიეჯი", "deathstranding": "მკვდრულიგადაცემა", "b4b": "b4b", "codwarzone": "კოდკარანტინი", "callofdutywarzone": "კომანდოსომშიcallofdutywarzone", "codzombies": "კოდზომბები", "mirrorsedge": "სარკეიბისკიდე", "divisions2": "დონშიც2", "killzone": "მკვლელებისზონა", "helghan": "ჰელგან", "coldwarzombies": "ცივიომისძმები", "metro2033": "მეტრო2033", "metalgear": "მეტალგეარი", "acecombat": "ასეთიბრძოლა", "crosscode": "კროსკოდი", "goldeneye007": "ოქროსთვალი007", "blackops2": "ბლექოპს2", "sniperelite": "სნაიპერიაelites", "modernwarfare": "მოდერნომები", "neonabyss": "ნეონაბისი", "planetside2": "პლანეტები2", "mechwarrior": "მეტჩვორიორი", "boarderlands": "ბორდერლენდსი", "owerwatch": "ოჰევერვაით", "rtype": "რები", "dcsworld": "dcsworld", "escapefromtarkov": "ბfugatarkov", "metalslug": "მეტალslug", "primalcarnage": "პრიმალურიკარნიჯი", "worldofwarships": "სამყაროსმებრძოლები", "back4blood": "ბრუნდებიან_კრავისთვის", "warframe": "ვარფრეიმი", "rainbow6siege": "რემბოუ6სიჯი", "xcom": "იქსკომ", "hitman": "ჰიტმენი", "masseffect": "მასეფექტი", "systemshock": "სისტემურიშოკი", "valkyriachronicles": "ვალკირიისქრონიკები", "specopstheline": "სპექოპსტელაიენი", "killingfloor2": "კილინგფლოორ2", "cavestory": "კავესთორი", "doometernal": "დუმეტერნალი", "centuryageofashes": "საუკუნეაბურდის", "farcry4": "ფარ்கრაი4", "gearsofwar": "მისერისმექანიზმები", "mwo": "მვო", "division2": "განყოფილება2", "tythetasmaniantiger": "ტაისმანიური_ვეფხვი", "generationzero": "დებილობახარისხი", "enterthegungeon": "გამდერსარდაფში", "jakanddaxter": "ჯაკანდააქსტერი", "modernwarfare2": "მოდერნვარფერ2", "blackops1": "ბლექოპსი1", "sausageman": "საუზგმენი", "ratchetandclank": "რაჩეტანაკლანკი", "chexquest": "ჩეხქვესტი", "thephantompain": "ოთხოცხმაინი", "warface": "ომისსახე", "crossfire": "კროსფაირ", "atomicheart": "ატომურიგული", "blackops3": "შავიოპერაციები3", "vampiresurvivors": "ვამპირებისგადარჩენილი", "callofdutybatleroyale": "კოლდუტიბათლეროიალში", "moorhuhn": "მორჰჰუმ", "freedoom": "თავისუფლება", "battlegrounds": "ბრძოლისველები", "frag": "ფრაგ", "tinytina": "ტინითიენა", "gamepubg": "გეიმპუბგ", "necromunda": "ნეკრომუნდა", "metalgearsonsoflibert": "მეტალგირსიშვილებიანიის", "juegosfps": "fpsთამაშები", "convertstrike": "გადაიქცევაboo", "warzone2": "ომისხანა2", "shatterline": "შატერლაინი", "blackopszombies": "ბლექოფსზომბები", "bloodymess": "სისხლიანახდა", "republiccommando": "rtaibitmo", "elitedangerous": "ელიიტდანგერიიუს", "soldat": "სოლდატი", "groundbranch": "გრაუნდბრანჩი", "squad": "სკვად", "destiny1": "ბედისწერა1", "gamingfps": "სათამაშოfps", "redfall": "წითელიდაცემა", "pubggirl": "pubggirl", "worldoftanksblitz": "მსოფლიოთავსატანკებისბლიცი", "callofdutyblackops": "კომანდოსგადასახლებას", "enlisted": "წარმოვიდია", "farlight": "ფარლაითი", "farcry5": "ფარი_კრაი5", "farcry6": "ფარკრაი6", "farlight84": "ფარლაით84", "splatoon3": "სპლატუნი3", "armoredcore": "ამორტიზიდული_core", "pavlovvr": "პავლოვრვი", "xdefiant": "xdefiant", "tinytinaswonderlands": "ტინიტინაასეპრეწეფები", "halo2": "ჰrental2", "payday2": "ფულითგასაღები2", "cs16": "cs16", "pubgindonesia": "პაბჯინდონეზია", "pubgukraine": "პუბლიკაბგუშეთი", "pubgeu": "პაბგეუ", "pubgczsk": "pubgczsk", "wotblitz": "ვოტბლიც", "pubgromania": "პუბგრომანია", "empyrion": "ემპირიონ", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "ტიტანფოლის2", "soapcod": "საპნისკოდი", "ghostcod": "გნდი_კოდი", "csplay": "ცსპლეი", "unrealtournament": "ივნისისტურნირი", "callofdutydmz": "კოლდოუტიფერდი", "gamingcodm": "გეიმინგქოდმ", "borderlands2": "ბორდერლენდი2", "counterstrike": "კაუნტერსტრაიკი", "cs2": "cs2", "pistolwhip": "პისტოლითკაკუნი", "callofdutymw2": "callofdutymw2", "quakechampions": "უნდაიყომტრისგამარჯვებაquakechampions", "halo3": "ჰალო3", "halo": "ჰალო", "killingfloor": "კილინგფლორი", "destiny2": "ბედისწერადა2", "exoprimal": "ექსოპრაიმალ", "splintercell": "სპლინტერწელი", "neonwhite": "ნეონთეთრი", "remnant": "რჩეული", "azurelane": "აზიურიმისაღები", "worldofwar": "მსოფლიოომის", "gunvolt": "გუნვოლტი", "returnal": "რუტერნალი", "halo4": "ჰალო4", "haloreach": "ჰალორიჩი", "shadowman": "ჩრდილოვანკაცად", "quake2": "ძვრილი2", "microvolts": "მიკროვოლთსი", "reddead": "წითელი_მკვდარი", "standoff2": "standoff2", "harekat": "ჰარეკატი", "battlefield3": "ბრძოლისველი3", "lostark": "დაკარგულიარქონის", "guildwars2": "გილდવარს2", "fallout76": "ფოლაუტ76", "elsword": "ელსვორდ", "seaofthieves": "მეკობრეებისმორევი", "rust": "რკინა", "conqueronline": "გაიმარჯვეამონლაინში", "dauntless": "დაუნტლეს", "warships": "სარაკეტო_საწვეთი", "dayofdragons": "დრაკონისდღე", "warthunder": "რაბლიდავი", "flightrising": "ფრენისგაზრდა", "recroom": "რეკრუმი", "legendsofruneterra": "legendsofruneterra", "pso2": "პსო2", "myster": "მისტერი", "phantasystaronline2": "ფანტაზიისტარებისაონლაინ2", "maidenless": "მარგალიტისგარეშე", "ninokuni": "ნინოკუნი", "worldoftanks": "იამზედილიამოწინააღმდეგეები", "crossout": "გადახურვა", "agario": "აგარიო", "secondlife": "მეორესიცოცხლე", "aion": "აიონ", "toweroffantasy": "ტავანისსიზმრები", "netplay": "ნეტპლეი", "everquest": "მუდმივშეგვართ", "metin2": "მეთინ2", "gtaonline": "გტაეონლაინ", "ninokunicrossworld": "ბუნიკუნი_კროსვორლდ", "reddeadonline": "წითელბოლოსონლაინ", "superanimalroyale": "სუპერმინაარქი", "ragnarokonline": "რაგნაროკონლაინ", "knightonline": "რაინდებიონლაინ", "gw2": "გვ2", "tboi": "ტბოი", "thebindingofisaac": "ბინდინგიისაიზაკი", "dragonageinquisition": "დრაგონიჟინიაქცია", "codevein": "კოდვენი", "eveonline": "ევეონლაინ", "clubpenguin": "კლუბპინგვინი", "lotro": "ლოთრო", "wakfu": "ვაკფუ", "scum": "ბრახუ", "newworld": "ახალიამერი", "blackdesertonline": "შავიუდაბნოსონლაინ", "multiplayer": "მულტიპლეიერი", "pirate101": "პირატული101", "honorofkings": "მენიუსარკიზი", "fivem": "ფივემ", "starwarsbattlefront": "ვალვარჩეპოიდები", "karmaland": "კარმალენდი", "ssbu": "ssbu", "starwarsbattlefront2": "სტარვარსბატლფრონტ2", "phigros": "ფიგროს", "mmo": "მმო", "pokemmo": "პოკემმო", "ponytown": "პონიტაუნ్", "3dchat": "3დჩატი", "nostale": "ნოსთალე", "tauriwow": "თაურიwow", "wowclassic": "ვაუკლასიკი", "worldofwarcraft": "მსოფლიოსგმირები", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "რბოლასკეიპი", "neopets": "ნეოპეტსი", "moba": "მობა", "habbo": "ჰაბო", "archeage": "არქეიჯი", "toramonline": "ტორამონლაინ", "mabinogi": "მაბინოგი", "ashesofcreation": "სახლისმტვერი", "riotmmo": "ბოოჯორიოტმმო", "silkroad": "საბგეროგზები", "spiralknights": "სპირალპირველები", "mulegend": "მულეგენდები", "startrekonline": "სტართრეკონლაინ", "vindictus": "ვინდეგტუს", "albiononline": "ალბიონონლაინი", "bladeandsoul": "ბლეიდანდსოული", "evony": "ევონი", "dragonsprophet": "დრახონისქურუმი", "grymmo": "გრიმმო", "warmane": "უარღმაე", "multijugador": "მულტიპლეიერი", "angelsonline": "ანგელოზებისაჩივარზე", "lunia": "ლუნია", "luniaz": "ლუნიაზ", "idleon": "აიდლონ", "dcuniverseonline": "დკუნივერსიონლაინი", "growtopia": "გროთოპია", "starwarsoldrepublic": "ისტორიულთაგანიstarwarsoldrepublic", "grandfantasia": "გრანდფანტაზია", "blueprotocol": "_blu<PERSON><PERSON><PERSON><PERSON>_", "perfectworld": "完美世界", "riseonline": "უთვალდებულიriseonline", "corepunk": "კორიელქე", "adventurequestworlds": "ურბანულითავგადასავლებისquest", "flyforfun": "გამოქვავებული_ცისარტყელაზე", "animaljam": "უცნაურიათამაშო", "kingdomofloathing": "სიცილისმეფობა", "cityofheroes": "ბატონიგმირებისქალაქი", "mortalkombat": "მორტალკომბატი", "streetfighter": "სტრიტფაიტერი", "hollowknight": "ჰოლოგანცხადება", "metalgearsolid": "მეტალგეარსოლიდ", "forhonor": "მისიამაყრე", "tekken": "თექენ", "guiltygear": "გილტიგერია", "xenoverse2": "ზენოვერსი2", "fgc": "fgc", "streetfighter6": "სტრიტფაიტერი6", "multiversus": "მულტივერსუს", "smashbrosultimate": "ბოუსმაშიბროსიუკრიდოსტვდილი", "soulcalibur": "სოულკალიბრი", "brawlhalla": "ბროჰალკა", "virtuafighter": "პროფაილფაიტერ", "streetsofrage": "გზებიაბრაგის", "mkdeadlyalliance": "mkგამოხდიაშრომი", "nomoreheroes": "ამდენიგმირიაღარ", "mhr": "მჰრ", "mortalkombat12": "მორტალკომპატ12", "thekingoffighters": "ბრძოლისმეფე", "likeadragon": "ჯორჯიაზე_გავიტანჯები", "retrofightinggames": "რეტრომებრძოლებისთამაშები", "blasphemous": "ბრალმდებლური", "rivalsofaether": "მიუერთდიbooისმეგობრებსrivalsofaether", "persona4arena": "პერსონა4არენა", "marvelvscapcom": "მარველივსკაპკომ", "supersmash": "სუპერსმაში", "mugen": "მუგენ", "warofthemonsters": "მონსტრებისომი", "jogosdeluta": "ბრძოლებისთამაშები", "cyberbots": "საიბერბოტები", "armoredwarriors": "შესაძლებლობიანისამხედროები", "finalfight": "ბოლობრძოლა", "poweredgear": "კარადისძალა", "beatemup": "ბითემაპი", "blazblue": "ბლეიზბლუ", "mortalkombat9": "მორტალკომბატი9", "fightgames": "ბრძოლებისთამაშები", "killerinstinct": "მხეცურიინსტინქტი", "kingoffigthers": "მებრძოლებისმეფე", "ghostrunner": "ჰხალხი", "chivalry2": "ავადმდგარობა2", "demonssouls": "დემონსული", "blazbluecrosstag": "ბლაზბლუკროსტაგ", "blazbluextagbattle": "ბლეიზბლუსქტეგბატლი", "blazbluextag": "ბლეზბლოოქსთაგ", "guiltygearstrive": "გილტიგირსტრაივ", "hollowknightsequel": "ხალიათისპასპორტი", "hollowknightsilksong": "ჰოლოდნაითნისსილქსონგი", "silksonghornet": "silksongნახტომი", "silksonggame": "სილქსონstabile", "silksongnews": "სილქსონგისახალდედა", "silksong": "სილქსონგ", "undernight": "სუშისღამეს", "typelumina": "ტიპელუმინა", "evolutiontournament": "evolutiontournament", "evomoment": "ევომომენტი", "lollipopchainsaw": "შოკოლადისტიკებიდაჯაჭვები", "dragonballfighterz": "დრაგონბოლიფაიტერز", "talesofberseria": "ბერსერიასისტემების_მცოდნეები", "bloodborne": "სისხლითგაწვდილი", "horizon": "ჰორიზონტი", "pathofexile": "გამოწყოpathofexile", "slimerancher": "სლაიმერანჩერი", "crashbandicoot": "კრაშბანდიკუტი", "bloodbourne": "ბლუდბორნი", "uncharted": "არისწვდილი", "horizonzerodawn": "ჰორიზონტზენულდილზე", "ps4": "პს4", "ps5": "პს5", "spyro": "სპირო", "playstationplus": "პლეისთეიშენიპლუს", "lastofus": "ჩვენებურადp", "infamous": "მდგომარეობისგარეთ", "playstationbuddies": "პლეისტეიშენბადები", "ps1": "პს1", "oddworld": "უცნაურისამყარო", "playstation5": "პლეიშტეიშნ5", "slycooper": "სლაიკუპერი", "psp": "ფსპ", "rabbids": "რაბბიდსი", "splitgate": "საკეტება", "persona4": "პერსონა4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "გტა", "roguecompany": "როღკომპანია", "aisomniumfiles": "აისომნიუმფაილები", "gta5": "gta5", "gtasanandreas": "გტასანანდრიუს", "godofwar": "გულისამაჩვენებელიgodofwar", "gris": "გრის", "trove": "ტროვი", "detroitbecomehuman": "დეტრോയტიბერსადაცნობი", "beatsaber": "ბიტსაბერი", "rimworld": "რიმვორლდ", "stellaris": "სტელარისი", "ps3": "პს3", "untildawn": "დილისადრე", "touristtrophy": "ტურისტებისთასი", "lspdfr": "ლსპდფრ", "shadowofthecolossus": "ბრანდაგიგანტისა", "crashteamracing": "კრაშტიმურასინგი", "fivepd": "ხუთიპიდი", "tekken7": "ტექკენ7", "devilmaycry": "დევილმემიედღილი", "devilmaycry3": "დევილმეიკრაი3", "devilmaycry5": "დევილისხვედრილი5", "ufc4": "ufc4", "playingstation": "პლეიस्टეიშენი", "samuraiwarriors": "სამურაიმომმოვლებლები", "psvr2": "psvr2", "thelastguardian": "ბოლოსდაცუალი", "soulblade": "სულიანიმშვენიერი", "gta5rp": "gta5rp", "gtav": "გტავ", "playstation3": "პლეისტეიშენი3", "manhunt": "მამაკაცებისმონატრება", "gtavicecity": "გატავივენციტი", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "შედარებულადხეების2კოვენანტი", "pcsx2": "pcsx2", "lastguardian": "ბოლომცველები", "xboxone": "ექსბოქსი1", "forza": "ფორცა", "cd": "ცდ", "gamepass": "თამაშისპასპორტი", "armello": "არმელო", "partyanimal": "წვეულებისცხოველი", "warharmmer40k": "ბრძოლისმუხტი40კ", "fightnightchampion": "მარატონი_მებრძოლი", "psychonauts": "ზოგჯერბრიყვები", "mhw": "mhw", "princeofpersia": "პრინციპერსიის", "theelderscrollsskyrim": "უფროსებიატრალურადუსკაირიმი", "pantarhei": "პენტარჰეი", "theelderscrolls": "ძველიჩამატება", "gxbox": "გცბოქსი", "battlefront": "ბრძოლისფრონტი", "dontstarvetogether": "არამარტოგაგვიმართალია", "ori": "ორი", "spelunky": "სპელუნკი", "xbox1": "xbox1", "xbox360": "ექსბოქს360", "starbound": "ვარსკვლავებისმხარეს", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "ბინისგადამყვანი", "americanmcgeesalice": "ამერიკელმკგის앨ისი", "xboxs": "ექსბიოქსები", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ქვეყნებისლიგა", "fable2": "ფებელ2", "xboxgamepass": "xboxgamepass", "undertale": "უნდერტეილ", "trashtv": "ბზარიtv", "skycotl": "მთვარისეთი", "erica": "ერიკა", "ancestory": "ძალიანცავთობის", "cuphead": "კაფჰედ", "littlemisfortune": "ბუ_ცუდიიღბალი", "sallyface": "სალიface", "franbow": "ფრანბოუ", "monsterprom": "მონსტრისპრომი", "projectzomboid": "პროექტზამბოიდი", "ddlc": "ddlc", "motos": "მोटोები", "outerwilds": "გარეთულიმირთო", "pbbg": "პბბგ", "anshi": "ანშირი", "cultofthelamb": "ბრძოლისმატყუილი", "duckgame": "თუთიყუშისთამაშის", "thestanleyparable": "სტენლისპარაბოლი", "towerunite": "ტაუერუერტი", "occulto": "ოკულტო", "longdrive": "დიდიიპოვნა", "satisfactory": "კმაყოფილება", "pluviophile": "პლუვიოფილი", "underearth": "ქვემოთ", "assettocorsa": "ასეტტოკორსა", "geometrydash": "გომეტრიისდაშ", "kerbal": "ტერების", "kerbalspaceprogram": "კერბალსპეისპროქრამი", "kenshi": "კენში", "spiritfarer": "სპირიტფარერი", "darkdome": "შავიკუპოლი", "pizzatower": "პიცატაუერი", "indiegame": "ინდიგამი", "itchio": "itchio", "golfit": "გოლფიტ", "truthordare": "ჭეშმარიტებათუგამოწვევა", "game": "თამაში", "rockpaperscissors": "ქვაქაღალდიმაკრატელი", "trampoline": "ტრაპლინზე", "hulahoop": "ჰულაჰუპი", "dare": "დარ", "scavengerhunt": "მოსაპოვებელითამაშები", "yardgames": "პატარათამაშები", "pickanumber": "ნომრისარჩევი", "trueorfalse": "მართალიათუტყუილი", "beerpong": "ბირპონგი", "dicegoblin": "დედოფალიოთხკვირიანი", "cosygames": "ატმოსფერულითამაშები", "datinggames": "შეფასებები", "freegame": "თავისუფალითამაში", "drinkinggames": "სიმთვრალისთამაშები", "sodoku": "სოდოკუ", "juegos": "მამაკაცებს", "mahjong": "महजोंग", "jeux": "ბჟუტა", "simulationgames": "სიმულაციურითამაშები", "wordgames": "სიტყვათამაშები", "jeuxdemots": "სიტყვებისბრძოლა", "juegosdepalabras": "სიტყვათაასოციაციები", "letsplayagame": "მოდითთამაშობთ", "boredgames": "ბoredgames", "oyun": "ოხუნჯი", "interactivegames": "ინტერტაქტიულითამაშები", "amtgard": "ამტგარიდ", "staringcontests": "გადართვისკონკურსები", "spiele": "mọrɖi", "giochi": "გიოჩი", "geoguessr": "გეოგესერი", "iphonegames": "აიფონისთამაშები", "boogames": "ბუუთამაშები", "cranegame": "კრანებისთამაში", "hideandseek": "მალახსადაძიებას", "hopscotch": "ხტომი", "arcadegames": "არქადულითამაშები", "yakuzagames": "იამაყუტითამაშიyakuzagames", "classicgame": "კლასიკურაზრდასრულითამაში", "mindgames": "სულიერითამაშები", "guessthelyric": "უმაგრესიენაკვეთისმოულოდნელობა", "galagames": "გალაგეიმსი", "romancegame": "რომანტიკულითამაში", "yanderegames": "იანდერეთამაშები", "tonguetwisters": "გახელახებები", "4xgames": "4xთამაშები", "gamefi": "გეიმფი", "jeuxdarcades": "არენების_ჭიდილი", "tabletopgames": "ტელურიგულითამაშები", "metroidvania": "მეტროიდვანია", "games90": "თამაშები90", "idareyou": "გაიმარჯვე", "mozaa": "მოზაა", "fumitouedagames": "ფუმიტოუდეგეიმსი", "racinggames": "მრბოლობისთამაშები", "ets2": "ets2", "realvsfake": "რეალურიც_vs_მხატვრულიც", "playgames": "ახალთამაშებსვთამაშობთ", "gameonline": "ონლაინთამაში", "onlinegames": "ონლაინთამაშები", "jogosonline": "ონლაინჯოკები", "writtenroleplay": "წერილიექსპრესია", "playaballgame": "გავრცელდიბურთისთამაშში", "pictionary": "პიკშენერი", "coopgames": "კოოპგარიყვლი", "jenga": "ჯენგას", "wiigames": "ვიგეიმსი", "highscore": "უმაღლესიქულა", "jeuxderôles": "თამაშებიერთობით", "burgergames": "ბურგერებისთამაშები", "kidsgames": "ბავშვებისთამაშები", "skeeball": "სკიბოლი", "nfsmwblackedition": "nfsmwშავიგამოცემა", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "მოთამაშისკითხვა", "gioco": "გიაკო", "managementgame": "მმართველობისთამაში", "hiddenobjectgame": "მალულისაგანისთამაში", "roolipelit": "როლიპელები", "formula1game": "ფორმულა1თამაშის", "citybuilder": "ქალაქმშენებელი", "drdriving": "დრაივინგი", "juegosarcade": "არcade", "memorygames": "მეხსიერებისთამაშები", "vulkan": "ვულკანი", "actiongames": "აქტიურითამაშები", "blowgames": "ბლოიომები", "pinballmachines": "პინბოოლისგაზეთები", "oldgames": "ძველბრძოლები", "couchcoop": "კაღლმარი", "perguntados": "პერსონაჟები", "gameo": "გამეო", "lasergame": "ლაზერკარი", "imessagegames": "იმესიჯისთამაში", "idlegames": "იდლიგამები", "fillintheblank": "შეავსეთ_დამატებული_ტექსტი", "jeuxpc": "პისთამაშები", "rétrogaming": "რეტროგეიმინგი", "logicgames": "ლოგიკურითამაშები", "japangame": "იაპონურიgame", "rizzupgame": "რუზზაფენთასვილი", "subwaysurf": "სუბვეიშერფინგი", "jeuxdecelebrite": "სარჩელილ", "exitgames": "გამოსასვლელითამაშები", "5vs5": "5ზუ5", "rolgame": "როლური게임ი", "dashiegames": "დაშისთამაში", "gameandkill": "თამაშიგამოკლა", "traditionalgames": "რადიოთამაშები", "kniffel": "ქნიფელ", "gamefps": "თამაშიფპს", "textbasedgames": "მწერლობითთამაშები", "gryparagrafowe": "გრიაპარაგრაფowe", "fantacalcio": "ფანტაკალჩო", "retrospel": "რეტროსპელი", "thiefgame": "თუშანური게임", "lawngames": "უნივერსალურითამაშები", "fliperama": "ფლიპერამა", "heroclix": "ჰეროკლიქსი", "tablesoccer": "ტელეფონურიფეხბურთი", "tischfußball": "ტისტჩფუსბოლი", "spieleabende": "სპილეაბენდები", "jeuxforum": "ჯეიკსიფორუმი", "casualgames": "შემთხვევითიგoyunebi", "fléchettes": "ფლეარეტები", "escapegames": "გასაქცევითამაშები", "thiefgameseries": "ჩხუბისთამაშებისსერია", "cranegames": "კრანესთამაშები", "játék": "ბუ", "bordfodbold": "ბორდფუტბოლდი", "jogosorte": "ჯოგოსორტე", "mage": "მაჟე", "cargames": "ავტოთამაშები", "onlineplay": "ონლაინखेигра", "mölkky": "მოლკი", "gamenights": "მიუკვდებლები", "pursebingos": "ბურგერი_აიჯობს", "randomizer": "რენდომაიზერი", "msx": "მშვენიერი", "anagrammi": "ანაგრამი", "gamespc": "თამაშებიpc", "socialdeductiongames": "სოციალურიგადახედვისთამაშები", "dominos": "დომინოს", "domino": "დომინოს", "isometricgames": "იზომეტრიულითამაშები", "goodoldgames": "ძველითამაშები", "truthanddare": "სიმართლეიძავი", "mahjongriichi": "მასტერიუიჩი", "scavengerhunts": "გადისპოვნისთამაშები", "jeuxvirtuel": "ვირტუალურითამაშები", "romhack": "რომჰეკი", "f2pgamer": "f2pმოსულები", "free2play": "უფასოდათამაშო", "fantasygame": "ფანტაზიისთამაში", "gryonline": "გრიონსივრცეში", "driftgame": "დრიფტთამაშები", "gamesotomes": "თამაშდებაო", "halotvseriesandgames": "ჰალოtvსერიალებიდათამაშები", "mushroomoasis": "ყლორტებისოთახი", "anythingwithanengine": "თითოეულიმანქანისთვის", "everywheregame": "ყველგანთამაშო", "swordandsorcery": "შედევრიდამინაწილეობა", "goodgamegiving": "მშვენიერითამაშისაჩუქრები", "jugamos": "თამაშობთ", "lab8games": "ლაბ8თამაშები", "labzerogames": "ლაბზეროგეიმსი", "grykomputerowe": "გრიკომპიუტერული", "virgogami": "ვირგოგამი", "gogame": "gogame", "jeuxderythmes": "თამაშისრიტმს", "minaturegames": "მინიატურებისთამაში", "ridgeracertype4": "რიჯერასტიპი4ridgeracertype4", "selflovegaming": "თავისქომარდობაამტიპში", "gamemodding": "მეთვალყურეობა", "crimegames": "დანაშაულისთამაშები", "dobbelspellen": "დუბლსპელენი", "spelletjes": "ბავშვისთამაშები", "spacenerf": "სპეისნერფ", "charades": "ჩარადები", "singleplayer": "მებრძოლიერთეულში", "coopgame": "კოოპსაცხოვრებელი", "gamed": "გეიმედ", "forzahorizon": "forzahorizon", "nexus": "ნექსუს", "geforcenow": "გეფორჩენოუ", "maingame": "მთავარითამაში", "kingdiscord": "ბაათვიკრიშილი", "scrabble": "სკრაბლი", "schach": "შახმატი", "shogi": "შოგი", "dandd": "დანდდ", "catan": "კატარანი", "ludo": "ლუდო", "backgammon": "ბექგამონი", "onitama": "ონიტამა", "pandemiclegacy": "პანდემიისმემკვიდრეობა", "camelup": "კატოხი", "monopolygame": "მონოპოლიასთამაშობ", "brettspiele": "ბრეთშპილე", "bordspellen": "ბორჯგლები", "boardgame": "ბორდგეიმი", "sällskapspel": "სახლურირამაშები", "planszowe": "ფლანსზოუე", "risiko": "რისკი", "permainanpapan": "პატარებებისბურთი", "zombicide": "ზოთბებისბრძოლა", "tabletop": "ნაყუცი", "baduk": "ბადუკ", "bloodbowl": "სისხლისთასი", "cluedo": "კლუდო", "xiangqi": "შიანგქი", "senet": "სენეტ", "goboardgame": "გობლოკოთამაშები", "connectfour": "კავშირიოთხზე", "heroquest": "ჰეროისquest", "giochidatavolo": "გიოხვიდათავოლო", "farkle": "ფარკლე", "carrom": "კარომი", "tablegames": "თამაშებისმაგიდა", "dicegames": "საფეხურებისთამაშები", "yatzy": "იაცტი", "parchis": "პარჩისი", "jogodetabuleiro": "ბუჯოდეტაბულირო", "jocuridesocietate": "სოციალურიარეპლიკაცია", "deskgames": "გამწუფითამაშები", "alpharius": "ალფარიუსი", "masaoyunları": "მასაობები", "marvelcrisisprotocol": "მარველილქრიზისპროტოკოლი", "cosmicencounter": "კოსმოსურიშეხვედრა", "creationludique": "დამპალებულიშემოქმედება", "tabletoproleplay": "საჯარომაგიდურიროლურითამაშები", "cardboardgames": "კარტონისთამაშები", "eldritchhorror": "ელდრიჩჰორორი", "switchboardgames": "სვიჩბორდგეიმზი", "infinitythegame": "უდუმოთათამაში", "kingdomdeath": "სამეფოსმौतება", "yahtzee": "იაჰტზი", "chutesandladders": "ჩუტებიდაჩალიჩები", "társas": "ტარსას", "juegodemesa": "ჯგუფისთამაშები", "planszówki": "პლანზოხები", "rednecklife": "წითელნაკბენურიცხოვრება", "boardom": "ბორდომი", "applestoapples": "სიტყვებსთანამიმდევრულად", "jeudesociété": "სოციალურიპ게임ები", "gameboard": "თამაშისდაფა", "dominó": "დომინო", "kalah": "კალაჰ", "crokinole": "კროკინოლი", "jeuxdesociétés": "ბეჭვდურითამაშები", "twilightimperium": "თიბლაითიმპერიუმ", "horseopoly": "თავისიცხენები", "deckbuilding": "დეკბილდინგი", "mansionsofmadness": "გიჟურიასატყავები", "gomoku": "გომოკუ", "giochidatavola": "გიოხიდათავოლა", "shadowsofbrimstone": "დაკარგულიზღაპრები", "kingoftokyo": "ტოკიოსმეფე", "warcaby": "ვარხაბი", "táblajátékok": "თამაშები", "battleship": "ბრძოლახომალდით", "tickettoride": "ბილეთიდაკანადგურेबითბილეთიმსგავსიუბიწო", "deskovehry": "დესკოვეწრი", "catán": "კათან", "subbuteo": "სუბუტეო", "jeuxdeplateau": "ყუთებისთამაშები", "stolníhry": "სტოლნიჰრიუ", "xiángqi": "შიანგყი", "jeuxsociete": "ბოშუორები", "gesellschaftsspiele": "ბუდუკებისთამაში", "starwarslegion": "ვარსკვლავიომებისლეგიონი", "gochess": "გოგოცესი", "weiqi": "ვეიკი", "jeuxdesocietes": "სამოქალაქოთამაშები", "terraria": "ტერარია", "dsmp": "დსმპ", "warzone": "ბრძოლისველი", "arksurvivalevolved": "არკსურვაიוואַלევოლვეიდი", "dayz": "დღეები", "identityv": "იდენტობა", "theisle": "კუნძული", "thelastofus": "ბოლოჩვენგან", "nomanssky": "არანაირიადიდიურა", "subnautica": "სუბნელური", "tombraider": "ტოქმრეიდერი", "callofcthulhu": "კოლხიდაკუთულhu", "bendyandtheinkmachine": "ბენდიოდაინისმანქანა", "conanexiles": "კონანიექსაილსი", "eft": "ეფტ", "amongus": "ჩვენშორის", "eco": "ეკო", "monkeyisland": "მ_chunkტაჭუტუნი", "valheim": "ვალჰეიმი", "planetcrafter": "პლანეტკრაფტერი", "daysgone": "დღეებიგავიდა", "fobia": "ფობია", "witchit": "ჯადოქრობი", "pathologic": "პათოლოგიური", "zomboid": "ზომბოიდი", "northgard": "ჩრდილოეთქართოგირი", "7dtd": "7dtd", "thelongdark": "გრძელიბნელი", "ark": "არკი", "grounded": "გაგდებული", "stateofdecay2": "stateofdecay2", "vrising": "ვრესინგი", "madfather": "მაგარი_papa", "dontstarve": "მშიერიარობდე", "eternalreturn": "უდიდესიდაბრუნება", "pathoftitans": "ტიტნებისიმართვა", "frictionalgames": "ფრიკშენალგეიმსი", "hexen": "ჰექსენი", "theevilwithin": "კაირად_შიგნით", "realrac": "რეალრეკი", "thebackrooms": "ბექრუმები", "backrooms": "უკანათები", "empiressmp": "ემპირდული_სმპ", "blockstory": "ბლოკსტორი", "thequarry": "დაყრა", "tlou": "ტლოუ", "dyinglight": "მკვდარისინათლე", "thewalkingdeadgame": "დwalkingdeadgame", "wehappyfew": "ჩვენცოტაბედნიერებივართ", "riseofempires": "იმპერიებისაღლება", "stateofsurvivalgame": "გადარჩენისნაკრები", "vintagestory": "ვინტაჟისტორია", "arksurvival": "არკსურვაივალ", "barotrauma": "ბაროატრავმა", "breathedge": "ბრიდიჯი", "alisa": "ალისა", "westlendsurvival": "დასავლურიგადარჩენა", "beastsofbermuda": "ბერმუდისჩვრები", "frostpunk": "ფროსტპანკ", "darkwood": "დარკვუდ", "survivalhorror": "სიცოცხლისგადასარჩენადსაშინელებათათამაში", "residentevil": "ბresidentevil", "residentevil2": "რეზიდენტიეშავი2", "residentevil4": "რეზიდენტივილ4", "residentevil3": "შიში3", "voidtrain": "არარხიმატარებელი", "lifeaftergame": "თამაშისშემდეგزندگی", "survivalgames": "სათხოვრთამაშები", "sillenthill": "სილენტჰილი", "thiswarofmine": "ამომხედოთამომხედოთამომხედოთამომხედოთ", "scpfoundation": "scpფონდექსი", "greenproject": "მწვანეპროექტი", "kuon": "კუონი", "cryoffear": "უინძირიოფობიისგან", "raft": "რაფტი", "rdo": "რდომი", "greenhell": "მწვანეასე", "residentevil5": "რეზიდენტევილ5", "deadpoly": "დედპოლი", "residentevil8": "რეზიდენტივილო8", "onironauta": "ონირონავტა", "granny": "ბებია", "littlenightmares2": "ნაკლებასასწnightmare2", "signalis": "სიგნალები", "amandatheadventurer": "ამანდაოსმოგზაური", "sonsoftheforest": "ბუჩქებისშვილები", "rustvideogame": "რუსთავიוידეომეტი", "outlasttrials": "დაამარცხებტესტებს", "alienisolation": "უცხოელისიზოლაცია", "undawn": "უდაბნოში", "7day2die": "7დღეშიმკვდარარი", "sunlesssea": "უსიცოცხლოზღვა", "sopravvivenza": "სიცოცხლე", "propnight": "პროპნაით", "deadisland2": "დედისლანდი2", "ikemensengoku": "აიკეითმენბურთი", "ikemenvampire": "იკემენვამპირი", "deathverse": "მკვდრებისქედი", "cataclysmdarkdays": "კატაკლიზმიდედაწაგებები", "soma": "სომა", "fearandhunger": "შიშსადამადას", "stalkercieńczarnobyla": "სტალკერ_ციანჩარნობილა", "lifeafter": "ისნისშემდეგ", "ageofdarkness": "ბნელეთისდროა", "clocktower3": "ქართულიბარათები3", "aloneinthedark": "მარტოეთამაშში", "medievaldynasty": "მედივალურიავადությունը", "projectnimbusgame": "პროექტნიმბუსიgame", "eternights": "უკვდავღამისღამეები", "craftopia": "კრაფტოპია", "theoutlasttrials": "theoutlasttrials", "bunker": "ბანკერი", "worlddomination": "მსოფლიოსმასტობა", "rocketleague": "რობოტისლიiga", "tft": "ტიფტი", "officioassassinorum": "ოფიციისმკვლელები", "necron": "ნეკრონი", "wfrp": "wfrp", "dwarfslayer": "ჯუჯებისმკვლელი", "warhammer40kcrush": "ბოოვარჰამერ40კისთქენი", "wh40": "wh40", "warhammer40klove": "ვარჰამერ40kსიყვარული", "warhammer40klore": "ვარლამის40კლორი", "warhammer": "ვარჰემერი", "warhammer30k": "გალაქტიკისამიომაგი30კ", "warhammer40k": "ვორჰემმერ40k", "warhammer40kdarktide": "ვარჰემერი40კნელურიარქტიდი", "totalwarhammer3": "ტოტალურიომლისურაიდან3", "temploculexus": "თემპლოკულექსუს", "vindicare": "ვინდიაკარე", "ilovesororitas": "მიყვარსსორორიტები", "ilovevindicare": "მიყვარსვინდიკარე", "iloveassasinorum": "ილოვაობასასინორუმი", "templovenenum": "ტემპლოვენენუმ", "templocallidus": "ტემპლოკალიიდუს", "templomaerorus": "ტემპლოსიტყვისბოლომდე", "templovanus": "ტემპლოვანი", "oficioasesinorum": "ოფიციალურარაცუოთაძე", "tarkov": "ტარკოვი", "40k": "40კ", "tetris": "ტეტრისი", "lioden": "ლიოდენ", "ageofempires": "ერებისიმპერიები", "aoe2": "aoe2", "hoi4": "ჰოი4", "warhammerageofsigmar": "ვისკვერცხეთთაყვანისმცემელი", "civilizationv": "ცივილიზაციაv", "ittakestwo": "იტtakestwo", "wingspan": "ფრთებისსიგრძე", "terraformingmars": "მარსისტერაფორმირება", "heroesofmightandmagic": "მძლავრისდაჯადოსუხეშგმირები", "btd6": "btd6", "supremecommander": "სუპრემუმკომანდირი", "ageofmythology": "როგორიმითი", "args": "არგსი", "rime": "რიმი", "planetzoo": "პლანეტისზოო", "outpost2": "აუტპოსტი2", "banished": "გადაყლაპულია", "caesar3": "კეისარ3", "redalert": "წითელი_განგაში", "civilization6": "ცივილიზაცია6", "warcraft2": "ვარკრაფტ2", "commandandconquer": "კომანდისადამმართველობის", "warcraft3": "ვარქრაფტ3", "eternalwar": "უდიდესიომი", "strategygames": "სტრატეგიულითამაშები", "anno2070": "ანო2070", "civilizationgame": "ცივილიზაციისთამაში", "civilization4": "ცივილიზაცია4", "factorio": "ფაქტორია", "dungeondraft": "დუნჯენდრაფტი", "spore": "სპორე", "totalwar": "სრულიომლობა", "travian": "ტრავიან", "forts": "ქონება", "goodcompany": "კარგიკომპანიისთვის", "civ": "ცივ", "homeworld": "სახლურისამყარო", "heidentum": "ჰაიდენტუმი", "aoe4": "aoe4", "hnefatafl": "ჰნიოფატაფლი", "fasterthanlight": "ლურჯი_მღვიმესასხად", "forthekings": "ბრძოლისსაფირმოላይforthekings", "realtimestrategy": "ჰქონებაპროფესიონალურისტრატეგიები", "starctaft": "სტარქტაფტი", "sidmeierscivilization": "სიმდმაიერსსივილიზაცია", "kingdomtwocrowns": "ეფლესნაკები2تاجები", "eu4": "ბუ4", "vainglory": "მეცვიერება", "ww40k": "w38კ", "godhood": "ღვთაებურობა", "anno": "ანო", "battletech": "ბატლტექსტი", "malifaux": "მალიფო", "w40k": "w40k", "hattrick": "ჰატრიკი", "davesfunalgebraclass": "დევისმხიარულიალგებრათklasse", "plagueinc": "პლაგიენდქი", "theorycraft": "თეორიულიქმნა", "mesbg": "მესბგ", "civilization3": "მოქალაქეობა3", "4inarow": "4სიტყვაში", "crusaderkings3": "კრუსეიდერებისთათვის3", "heroes3": "ჰეროზები3", "advancewars": "წინაომარები", "ageofempires2": "კლასიკურიიმპერიებისასლი2", "disciples2": "მაომარები2", "plantsvszombies": "ც꽃ებიvsზმzombies", "giochidistrategia": "გიოchisპრობლემატიკა", "stratejioyunları": "სტრატეგიულითამაშები", "europauniversalis4": "ევროპაუਨੀევრალები4", "warhammervermintide2": "ვარჰემერმაერგმინტაიდი2", "ageofwonders": "შესანიშნაობებისდრო", "dinosaurking": "დინოზავრებისმეფე", "worldconquest": "მსოფლიოს_განconquistar", "heartsofiron4": "რკინისგულები4", "companyofheroes": "გმირებისკომპანია", "battleforwesnoth": "ბრძოლავესნოთისთვის", "aoe3": "აოე3", "forgeofempires": "ეპოქებისუგforged", "warhammerkillteam": "ვარჰემმერკილსტიმი", "goosegooseduck": "ხავსიხოჭო", "phobies": "ფობიები", "phobiesgame": "ფობიესთამაშები", "gamingclashroyale": "გასართობიკლაშრॉयალი", "adeptusmechanicus": "ადეპტუსმექანიკუს", "outerplane": "გარეთათვითმფრინავი", "turnbased": "ტურნისდაფუძნებული", "bomberman": "ბომბერი", "ageofempires4": "იმპერიებისუმართლოება4", "civilization5": "ცივილიზაცია5", "victoria2": "ვიქტორია2", "crusaderkings": "კრუსადერისმეფეები", "cultris2": "კულტრის2", "spellcraft": "ჯადოქრობა", "starwarsempireatwar": "ვარსკვლავურისქემებიომიიმპერიისწინააღმდეგ", "pikmin4": "პიკმინი4", "anno1800": "ანო1800", "estratégia": "სტრატეგია", "popfulmail": "პოპფულმაილ", "shiningforce": "ბრწყინვალება", "masterduel": "მასტერდუელი", "dysonsphereprogram": "დაისონსფერებისპროგრამა", "transporttycoon": "ტრანსპორტისმილიარდერი", "unrailed": "არაჩვეულებრივად", "magicarena": "მაგიკარენა", "wolvesville": "ვულვსვილი", "ooblets": "უბლეტები", "planescapetorment": "პლანეტიდანგასescape", "uplandkingdoms": "მაღალმთიანისამეფოები", "galaxylife": "გალაქსისცხოვრება", "wolvesvilleonline": "მგლებისქალაქიონლაინ", "slaythespire": "ბრძოლაწამოწვდეთ", "battlecats": "ፈარვალიკატები", "sims3": "სიმს3", "sims4": "sims4", "thesims4": "თამაშები4", "thesims": "სიმსი", "simcity": "სიმსიტი", "simcity2000": "სიმსიტი2000", "sims2": "სიმსი2", "iracing": "ირეისინგი", "granturismo": "გრანტურიზმო", "needforspeed": "სიჩქარისსაჭიროება", "needforspeedcarbon": "აჭრილიანათქგილდიანიბივდისწვები", "realracing3": "რეალრేసინგ3", "trackmania": "ტრეკმანია", "grandtourismo": "გრანდტურიზმო", "gt7": "ბგ7", "simsfreeplay": "simsfreeplay", "ts4": "ცს4", "thesims2": "დასიმსი2", "thesims3": "თესიმსი3", "thesims1": "თესიმს1", "lossims4": "ლოსსიმს4", "fnaf": "ფნაფ", "outlast": "გადარჩენილები", "deadbydaylight": "მკვდარიდღეები", "alicemadnessreturns": "ალისისმართლაუკანძვრა", "darkhorseanthology": "შავიცხენისანთოლოგია", "phasmophobia": "ფაზმოფობია", "fivenightsatfreddys": "ხუთიღამეფრედისთან", "saiko": "საიკო", "fatalframe": "მკვდარიჩ_frame", "littlenightmares": "ჭკვიანიღამისკოშმარები", "deadrising": "მიცვალებულებისამაღლება", "ladydimitrescu": "ლედიდიმიტრეკუ", "homebound": "ბუნაგში", "deadisland": "დედაკუნძული", "litlemissfortune": "პატარაქალბატონიგამისწორდა", "projectzero": "პროექტი0", "horory": "ჰორორი", "jogosterror": "ჯოგოსტერორი", "helloneighbor": "გამარჯობამეზობელო", "helloneighbor2": "გამარჯობამეზობელ2", "gamingdbd": "გეიმინგდბდ", "thecatlady": "კატებისქალბატონი", "jeuxhorreur": "საშინელობებისთამაში", "horrorgaming": "საშინელებათათამაშები", "magicthegathering": "ჯადოსნური_შეკრება", "mtg": "მტგ", "tcg": "ტმგაწვდილიtcg", "cardsagainsthumanity": "კარდებიადამიანებისწინააღმდეგ", "cribbage": "კრიბიჯი", "minnesotamtg": "მინესოტამტგ", "edh": "ეძახე", "monte": "მონტე", "pinochle": "პინოსკლერი", "codenames": "კოდური_names", "dixit": "დიქსიტი", "bicyclecards": "სკუტერისბარათები", "lor": "ლორ", "euchre": "ეუხერი", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "სოლიტერი", "poker": "პოკერი", "hearthstone": "ჰართსტოუნი", "uno": "უნო", "schafkopf": "შაფკოფი", "keyforge": "კლავმჭრიჭინა", "cardtricks": "ბარათების_გატყუება", "playingcards": "ქარტისთამაშები", "marvelsnap": "მარველსნეპ", "ginrummy": "გინრამი", "netrunner": "ნეტრანერი", "gwent": "გვენტი", "metazoo": "მეტაზოო", "tradingcards": "ჩანაცვლებისბარათი", "pokemoncards": "პოკემონქარტები", "fleshandbloodtcg": "ხორცშესახებიდასისხლიtcg", "sportscards": "სპორტულიბარათები", "cardfightvanguard": "კარდფაითვანკარდ", "duellinks": "დუელლინკსი", "spades": "სპეიდები", "warcry": "ბრძოლისხმა", "digimontcg": "დიგിമონტცგ", "toukenranbu": "ტოკენრანბი", "kingofhearts": "გულისმეფე", "truco": "ტრუსო", "loteria": "ლატერია", "hanafuda": "ჰანაფუდა", "theresistance": "ბრძოლა", "transformerstcg": "ტრანსფორმერსტკგ", "doppelkopf": "დოპელკოფი", "yugiohcards": "იუგიოსბარათები", "yugiohtcg": "იუგიოჰთცგ", "yugiohduel": "იუგიოიდუელი", "yugiohocg": "იუგიჰოოკიჯი", "dueldisk": "დუელდისკი", "yugiohgame": "იუგიოჰისთამაშ", "darkmagician": "შავიმაგი", "blueeyeswhitedragon": "კონკიასთვალებითეთრიდრაკონი", "yugiohgoat": "იუგიოჰგოათ", "briscas": "ბრისკასი", "juegocartas": "ბეჭდვოკარტები", "burraco": "ბურაჩო", "rummy": "რემი", "grawkarty": "გრაუკარტი", "dobble": "დობლი", "mtgcommander": "მტგკომანდერი", "cotorro": "კოტორო", "jeuxdecartes": "ბარათებისთამაშები", "mtgjudge": "მთგენჯაჯი", "juegosdecartas": "ბარათებისთამაში", "duelyst": "დუელისტი", "mtgplanschase": "მტგპლანშესულს", "mtgpreconcommander": "მთელიmtgpreconcommander", "kartenspiel": "ბუკაგამარჯობა", "carteado": "კარტეადო", "sueca": "სუეკა", "beloteonline": "ბელოტონლაინ", "karcianki": "კარტიანქი", "battlespirits": "ბატლებურისულები", "battlespiritssaga": "ბრძოლისსულებისეპოპეა", "jogodecartas": "ჯოგოდეკარტასი", "žolíky": "ბუგი", "facecard": "ფეისკარდი", "cardfight": "ბარათებისბრძოლა", "biriba": "ბირიბა", "deckbuilders": "დეკბილდერები", "marvelchampions": "მარველჩემპიონები", "magiccartas": "ჯადოსახვევები", "yugiohmasterduel": "იუგიოჰმასტერდუელი", "shadowverse": "ჩრდილოვანიმსოფლები", "skipbo": "სკიპბო", "unstableunicorns": "არასტაბილურიუნიკორნები", "cyberse": "ცაიბერსე", "classicarcadegames": "კლასიკურიარკადულითამაშები", "osu": "ოსუ", "gitadora": "გიტადორა", "dancegames": "ნვთყერიღებისdancegames", "fridaynightfunkin": "პარასკევსათიფანკინი", "fnf": "fnf", "proseka": "პროცეკა", "projectmirai": "პროექტმირაი", "projectdiva": "პროექტდივა", "djmax": "დიჯეიმაქსი", "guitarhero": "გიტარასბრძოლა", "clonehero": "კლონჰერო", "justdance": "იძივუკრავით", "hatsunemiku": "ჰაცუნემიკუ", "prosekai": "პროზეკაი", "rocksmith": "როკსმითი", "idolish7": "აიდილიშ7", "rockthedead": "ბრუნდემიცვალებულებს", "chunithm": "ჩუნითმ", "idolmaster": "აიდოლმასტერი", "dancecentral": "dancecentral", "rhythmgamer": "რიტმულგეიმერი", "stepmania": "სტეპმანია", "highscorerythmgames": "ჰაისკორერითმგეიმები", "pkxd": "პკžd", "sidem": "სიდემ", "ongeki": "ონგეკი", "soundvoltex": "საუდვოლტექსი", "rhythmheaven": "რიტმისსამოთახი", "hypmic": "ჰიპმიკ", "adanceoffireandice": "მარათონისცეცხლიდაყინული", "auditiononline": "auditiononline", "itgmania": "იტგამანია", "juegosderitmo": "რიტმისთამაში", "cryptofthenecrodancer": "კრიპტოვი_nეკროდანსერი", "rhythmdoctor": "რითმდოქტორი", "cubing": "კუბინგი", "wordle": "ვორდლი", "teniz": "ტენიზი", "puzzlegames": "პაზლთამაშები", "spotit": "სტუპიტ", "rummikub": "რუმიკუბი", "blockdoku": "ბლოკდოკუ", "logicpuzzles": "ლოგიკურითავსატეხები", "sudoku": "სუდოკუ", "rubik": "რუბიკ", "brainteasers": "გონებრივიგობი", "rubikscube": "რუბიკისკვარცი", "crossword": "კვიზი", "motscroisés": "ბუნტლი", "krzyżówki": "კრიზოებები", "nonogram": "ნონოგრამი", "bookworm": "წიგნებისჭია", "jigsawpuzzles": "ჯიგსაპაზლები", "indovinello": "ინდოვინელლო", "riddle": "ბრიყვობა", "riddles": "ბუზები", "rompecabezas": "რომპეკაბეზა", "tekateki": "ტეკატეკი", "inside": "შიგნით", "angrybirds": "გაბრაზებულიჩიტები", "escapesimulator": "არბისკენგამოსასვლელი", "minesweeper": "მაინცშემხმარი", "puzzleanddragons": "პანღურებიიდdragons", "crosswordpuzzles": "ბვერებისცხრილები", "kurushi": "კურუში", "gardenscapesgame": "ბაღებისლანდიაუნივერსი", "puzzlesport": "პაზლსპორტი", "escaperoomgames": "გასescaperoomთამაშები", "escapegame": "გასაფრენითამაში", "3dpuzzle": "3დპაზლი", "homescapesgame": "მარტოდანსახლებში", "wordsearch": "სიტყვებისძიება", "enigmistica": "ენიგმისტიკა", "kulaworld": "კულავორლდ", "myst": "myst", "riddletales": "მეცხრილი_გაყინვა", "fishdom": "ფიშდომ", "theimpossiblequiz": "უძვირგვინოარჩევნი", "candycrush": "კანდიკრუნშიუ", "littlebigplanet": "კითხვისდიდსახელიწოეთი", "match3puzzle": "მეთანხმე3პაზლი", "huniepop": "ჰუნიპოპ", "katamaridamacy": "კატამარიდამაცი", "kwirky": "კვირკი", "rubikcube": "რუბიკისკუბი", "cuborubik": "კუბორუბიკ", "yapboz": "იარაღები", "thetalosprinciple": "თეატროსპრინციპი", "homescapes": "ჰომსკეიფსი", "puttputt": "პუტპუტი", "qbert": "ქვერტ", "riddleme": "მითხარითთავსატეხი", "tycoongames": "ტაიკუნგთამაშები", "cubosderubik": "კუბოსდერუბიკი", "cruciverba": "ბუკრიფტი", "ciphers": "ციფრები", "rätselwörter": "ბონუსისცხელეულები", "buscaminas": "ბუსკამინასი", "puzzlesolving": "პაზლებისმოგვარება", "turnipboy": "ფუხთუხისბიჭი", "adivinanzashot": "ადივინანზაშოტ", "nobodies": "არავინ", "guessing": "გახდი_შესაძლებლობა", "nonograms": "ნონოგრამები", "kostkirubika": "კოსტკირუბიკა", "crypticcrosswords": "კრიპტიკული_სულელბები", "syberia2": "საიბერია2", "puzzlehunt": "პაზლიქნობისცხოვება", "puzzlehunts": "პაზლისმონადირებები", "catcrime": "კატისდანაშაულიც", "quebracabeça": "ბრეკაბეჭედი", "hlavolamy": "ჰლავოლამი", "poptropica": "პოპტროპიკა", "thelastcampfire": "ბოლოსნათებისცეცხლიthelastcampfire", "autodefinidos": "ავტოდეფინირებული", "picopark": "პიკოპარკი", "wandersong": "ვანდერსონგ", "carto": "კარტო", "untitledgoosegame": "არაჩნდაგოზინები", "cassetête": "კასეტეთი", "limbo": "ლიმბო", "rubiks": "რუბიკის", "maze": "მეზი", "tinykin": "ტაინიკინი", "rubikovakostka": "რუბიკოვასკი", "speedcube": "სპიდკუბი", "pieces": "ხparçka", "portalgame": "პორტალმoyun", "bilmece": "ბილმეცე", "puzzelen": "პაზლი", "picross": "პიქროსი", "rubixcube": "რუბიკისკუბი", "indovinelli": "ინდოვინელი", "cubomagico": "კუბომაჯიკო", "mlbb": "მილიონნახვადიboo", "pubgm": "პუბგმ", "codmobile": "კოდმობაილი", "codm": "კოდემ", "twistedwonderland": "ბოლძლიერსამყაროში", "monopoly": "მონოპოლია", "futurefight": "მომავლისჩხუბი", "mobilelegends": "მობილურილეგენდები", "brawlstars": "ბრაუოლსტარს", "brawlstar": "ბროლსტარ", "coc": "კოკ", "lonewolf": "ერთობამგელი", "gacha": "გაჩა", "wr": "wr", "fgo": "ფგო", "bitlife": "ბიტლაიფი", "pikminbloom": "პიკმინბloom", "ff": "ეფფ", "ensemblestars": "ენსემბლისვარსკვლავები", "asphalt9": "ასფალტი9", "mlb": "მლბ", "cookierunkingdom": "ქუქიერჟેინინგდემი", "alchemystars": "ალქიმისტისვsterne", "stateofsurvival": "შენობისყოფნისმოდერი", "mycity": "ჩემი_ქალაქი", "arknights": "არკნაიಟსი", "colorfulstage": "გუნდურისცენა", "bloonstowerdefense": "ბლუნსტაუერიდაცვა", "btd": "ბთდ", "clashroyale": "კლაშროიალი", "angela": "ანგელა", "dokkanbattle": "დოკკანბატლი", "fategrandorder": "ბედიდიდიშეკვეთა", "hyperfront": "ჰიპერფრონტის", "knightrun": "რაჯდასვლა", "fireemblemheroes": "ცეცხლისგმირები", "honkaiimpact": "ჰონკაიიმპაქტი", "soccerbattle": "სოკერთასმი", "a3": "ა3", "phonegames": "ტელეფონურითამაშები", "kingschoice": "მეფეებისარჩევანი", "guardiantales": "გარდიანთალსი", "petrolhead": "პეტროლჰედი", "tacticool": "ტაქტიკუალი", "cookierun": "ხაჭაპურითალიზაცია", "pixeldungeon": "პიქსელურიმრუდი", "arcaea": "არხეას", "outoftheloop": "გარედანმოვლენებისგან", "craftsman": "ხელოსანი", "supersus": "სუპერსუს", "slowdrive": "სულნელიმოძრაობა", "headsup": "ჰისთეპი", "wordfeud": "ბუwordfeud", "bedwars": "საწოლურიომები", "freefire": "ფრიფაიერული", "mobilegaming": "მობილურითამაშები", "lilysgarden": "ლილიმესატბარი", "farmville2": "ფარმვილი2", "animalcrossing": "ანიმალკროსინგ", "bgmi": "ბგმიზი", "teamfighttactics": "თამაშისბრძოლებისტაქტიკა", "clashofclans": "კლანისბრძოლები", "pjsekai": "პიჯი8", "mysticmessenger": "მისტიკურიმესენჯერი", "callofdutymobile": "კოლფოტოგრაფიამობილური", "thearcana": "დასახულები", "8ballpool": "8ballpool", "emergencyhq": "იუწმოთბაhq", "enstars": "ენსტარსი", "randonautica": "რანდონავტიკა", "maplestory": "მაპლეისტორი", "albion": "ალბიონ", "hayday": "ჰეიდეი", "onmyoji": "ონმიოჯი", "azurlane": "აზურლაინი", "shakesandfidget": "მემუქრებისიზვრები", "ml": "მლ", "bangdream": "ბენგდრიმშე", "clashofclan": "კლანებისომაშვილი", "starstableonline": "სუპერსათიონლაინ", "dragonraja": "დრაგონრაჯა", "timeprincess": "ტაიმპრინcess", "beatstar": "ბიტსტარი", "dragonmanialegend": "დრაგონმანიალლეგენდი", "hanabi": "ჰანაბი", "disneymirrorverse": "დისნეიმირრატივერსი", "pocketlove": "ჯიბისსიყვარული", "androidgames": "ანდროიდურთამაშები", "criminalcase": "კრიმინალურისაქმე", "summonerswar": "სამ召召", "cookingmadness": "სარამლობისკენ", "dokkan": "დოკკან", "aov": "ცარიელი", "triviacrack": "თვითმმართველობისმტვერი", "leagueofangels": "ანგელოთალიგა", "lordsmobile": "ლორდსმობაილი", "tinybirdgarden": "მცირემწვანეთაბაღი", "gachalife": "გაჩალაიფი", "neuralcloud": "ნეირალკლაუდ", "mysingingmonsters": "ჩემისიმღერამmonsters", "nekoatsume": "ნეკოატსუმე", "bluearchive": "blåარქივი", "raidshadowlegends": "რაიდშედოულეგენდები", "warrobots": "სენასბრძოლები", "mirrorverse": "მზისსარკისსამყარო", "pou": "პოუ", "warwings": "რავალფიქერები", "fifamobile": "ფიფამობილური", "mobalegendbangbang": "მობილეგენდისბანბან", "evertale": "ევერტეილი", "futime": "ფუტიმე", "antiyoy": "ანტიიოიდი", "apexlegendmobile": "აპექსლეგენდგამდმobile", "ingress": "ინგრესში", "slugitout": "დანკებისშეჭიდება", "mpl": "მpll", "coinmaster": "მონეტისოსტატი", "punishinggrayraven": "სასჯელმცლობილიიშავი", "petpals": "ბუნებრივიმეგობრები", "gameofsultans": "გუნდურიაშვილი", "arenabreakout": "არენასაჩოლოგი", "wolfy": "ვულფი", "runcitygame": "ქალაქურისპორტი", "juegodemovil": "მობილურისთამაშები", "avakinlife": "ავაყინკლაიფი", "kogama": "კოგამა", "mimicry": "მიმიკრია", "blackdesertmobile": "შავიუდაბნომობილური", "rollercoastertycoon": "როლერკოსტერტიკონა", "grandchase": "გრანდჩეისი", "bombmebrasil": "ბომბმებრაზილი", "ldoe": "ლდოე", "legendonline": "ლეგენდაონლაინ", "otomegame": "ოტომეგასი", "mindustry": "მინდუსტრი", "callofdragons": "დdragonsისხმაც", "shiningnikki": "მზისენიკოს", "carxdriftracing2": "კარგხრიფთრეისინგ2", "pathtonowhere": "პეთხტონოუჰერ", "sealm": "sealm", "shadowfight3": "შედარებისმომხმარებელი3", "limbuscompany": "ლიმბუსკომპანია", "demolitionderby3": "დანგრევისდერბი3", "wordswithfriends2": "გამარჯვებულსიტყვები2", "soulknight": "სულმებრძოლი", "purrfecttale": "პერფექტთეილი", "showbyrock": "შოუბიროკი", "ladypopular": "ლედიპოპულარული", "lolmobile": "ლოლმობილური", "harvesttown": "მოსავალიერემდე", "perfectworldmobile": "სუპერუბისმობილური", "empiresandpuzzles": "ციხეებიდაპაზლები", "empirespuzzles": "ემპირესპაზლები", "dragoncity": "დრაგონსიტი", "garticphone": "გარტიკფონი", "battlegroundmobileind": "ბატლგრასმობილურიind", "fanny": "ფანი", "littlenightmare": "მცირეnightmare", "aethergazer": "აუტერgazeი", "mudrunner": "მუდრანერი", "tearsofthemis": "ცრემლებიგალაქტიკის", "eversoul": "სრულგული", "gunbound": "გუნბაუნდ", "gamingmlbb": "მთამაშობურიmlbb", "dbdmobile": "dbdmobile", "arknight": "არქნაითი", "pristontale": "პრისტონტალე", "zombiecastaways": "ზომბიპორტიუები", "eveechoes": "ევივილისჯიხური", "jogocelular": "ჯოგოცელულარ", "mariokarttour": "მარიოკარტტურის", "zooba": "ზუბა", "mobilelegendbangbang": "მობილურილეგენდაbangbang", "gachaclub": "გაჩაკლუბი", "v4": "ვ4", "cookingmama": "სამზარეულომამა", "cabalmobile": "კაბალმობილური", "streetfighterduel": "ქუჩურიმებრძოლიduel", "lesecretdhenri": "ლესეკრეტდერჰნი", "gamingbgmi": "გასართობიboo", "girlsfrontline": "გოგოებისწინსაზიარო", "jurassicworldalive": "ჯურასიქმზისფერიalive", "soulseeker": "სულისმოძებნელი", "gettingoverit": "გამოცდილებისგადალახვა", "openttd": "მხიარული_ტრასპორტი", "onepiecebountyrush": "ერთიანინაწილიბაუნטיრუში", "moonchaistory": "მუნჩაისტორი", "carxdriftracingonline": "კარიანმძილაობისონლაინმრბოლა", "jogosmobile": "მობილურიასინიბები", "legendofneverland": "ლეგენდააქართველოსნიუავლება", "pubglite": "pubglite", "gamemobilelegends": "გამოიყენე_მობილური_ლეგენდები", "timeraiders": "ტაიმრეიდერები", "gamingmobile": "მობილურიახლო", "marvelstrikeforce": "მარvelstrikeforce", "thebattlecats": "ბრძოლაკატისები", "dnd": "დნდ", "quest": "მძევრობა", "giochidiruolo": "გიოჩიდირულო", "dnd5e": "dnd5e", "rpgdemesa": "რიპიჯიდემესა", "worldofdarkness": "შავეთისმსოფლიო", "travellerttrpg": "გვიანებისrpg", "2300ad": "2300თვეში", "larp": "ლარპ", "romanceclub": "რომანტიკულიკლუბი", "d20": "d20", "pokemongames": "პოკემონქეიმები", "pokemonmysterydungeon": "პოკემონმისტერიზdungeon", "pokemonlegendsarceus": "პოკემონებისმითიარსეუისი", "pokemoncrystal": "პოკემონქრისტალი", "pokemonanime": "პოკემონანიმე", "pokémongo": "პოკემონგო", "pokemonred": "პოკემონწითელი", "pokemongo": "პოკემონგო", "pokemonshowdown": "პოკემონშოუ다운", "pokemonranger": "პოკემონრენჯერი", "lipeep": "ლიპიპ", "porygon": "მოიცახლვა", "pokemonunite": "პოკემონიუნაით", "entai": "ენტაი", "hypno": "ჰიპნო", "empoleon": "ემპოლეონ", "arceus": "არსეუს", "mewtwo": "მიუთუ", "paldea": "პალდეა", "pokemonscarlet": "პოკემონსკარლეტი", "chatot": "ჩათოტ", "pikachu": "პიკაჩუ", "roxie": "როქსი", "pokemonviolet": "პოკემონსciviliansviolet", "pokemonpurpura": "პოკემონპუფურა", "ashketchum": "აშკეტჩუმი", "gengar": "გენგარ", "natu": "ნატუ", "teamrocket": "ექსპრესმხოლოდ_ტიმროკეტი", "furret": "ფურეტი", "magikarp": "მაგიკარპ", "mimikyu": "მიმიკyu", "snorlax": "სნორლაქსი", "pocketmonsters": "ჯიბისმონსტრები", "nuzlocke": "ნუზლოქი", "pokemonplush": "პოკემონიპლუშჭუშები", "teamystic": "თიმისტიკ", "pokeball": "პოკებოლი", "charmander": "ჩარმანდერი", "pokemonromhack": "პოკემონისრომჰაკი", "pubgmobile": "pubgmobile", "litten": "ლიტენ", "shinypokemon": "მბრწყინავიპოკემონები", "mesprit": "მეექვსეprit", "pokémoni": "პოკემონი", "ironhands": "რკინისპალმები", "kabutops": "კაბუტოპსი", "psyduck": "პსაიდაკი", "umbreon": "უმბreon", "pokevore": "პოკევორი", "ptcg": "პეტჩი", "piplup": "პიპლუპ", "pokemonsleep": "პოკემონდგეობა", "heyyoupikachu": "ჰეიიოფიკაჩუ", "pokémonmaster": "პოკემონმასტერი", "pokémonsleep": "პოკემონმძინარება", "kidsandpokemon": "ბავშვებიდაპოკემონები", "pokemonsnap": "პოკემონააგება", "bulbasaur": "ბულბასაური", "lucario": "ლუკარიო", "charizar": "ჩარიზარ", "shinyhunter": "ბრილიანტისმონადირე", "ajedrez": "შახმატი", "catur": "კატურ", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "შაჰმატები", "schaken": "შახმი", "skak": "სკაკი", "ajedres": "შაჰმატისტები", "chessgirls": "შახმატგოგოები", "magnuscarlsen": "მაგნუსკარსლსენ", "worldblitz": "მსოფლიოსბლიქსი", "jeudéchecs": "ჭადრაკი", "japanesechess": "იაპონურიჭადრაკი", "chinesechess": "ჩინურისაჭადრაკო", "chesscanada": "შეჯიბრებაკანადაში", "fide": "ფიდე", "xadrezverbal": "შექსპირულიxadrezverbal", "openings": "გამოქვეყნებები", "rook": "რუკი", "chesscom": "ჩესკომ", "calabozosydragones": "კალაბოზოსიდdragons", "dungeonsanddragon": "მკვლელობანიანდრაკონები", "dungeonmaster": "დუნჯნმასტერ", "tiamat": "ტიანატ", "donjonsetdragons": "დინჯონებიდადრაკონები", "oxventure": "ოქსვენჩური", "darksun": "მუქიმზის", "thelegendofvoxmachina": "ვოქსმახინაებისlegend", "doungenoanddragons": "დუნჯენოიბრძოლები", "darkmoor": "შავიმორე", "minecraftchampionship": "მაინკროფტჩემპიოენსი", "minecrafthive": "მაინკრაფთჰაივი", "minecraftbedrock": "მაინკრაფტბედროკი", "dreamsmp": "სიზმრებისმხარი", "hermitcraft": "ჰერმიტკრაფტი", "minecraftjava": "მაინკრეფტჯავა", "hypixelskyblock": "ჰაიპიქელსკაიბლოკ", "minetest": "მაინტესტი", "hypixel": "ჰიპიქსელი", "karmaland5": "კარმაแลนด์5", "minecraftmods": "მაინკრაფტმოდები", "mcc": "მკკ", "candleflame": "ჩუქურთმებისცეცხლი", "fru": "ფრუ", "addons": "დამატებები", "mcpeaddons": "მკპეეთქუხილები", "skyblock": "კვებითიბლოკი", "minecraftpocket": "minecraftბეჭედი", "minecraft360": "მაინკრაფტ360", "moddedminecraft": "მოდიფიცირებულიმיינკრაფტი", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "ინტერვალებში", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "მაინკრაფტისქალაქი", "pcgamer": "პისითამაშები", "jeuxvideo": "ვიდეოთამაშები", "gambit": "გამბიტი", "gamers": "გეიმერები", "levelup": "მაღლააღება", "gamermobile": "გეიმერმობილური", "gameover": "თამაშიდამთავრებულია", "gg": "გგ", "pcgaming": "პისითამაშები", "gamen": "gamen", "oyunoynamak": "ოჯონოისთამაშობა", "pcgames": "პცთამაშები", "casualgaming": "ყავისფერითამაშები", "gamingsetup": "აგარაათუმერომი", "pcmasterrace": "პკმასტერრესი", "pcgame": "პისითამაში", "gamerboy": "გეიმერბოი", "vrgaming": "ვროგეიმინგი", "drdisrespect": "დრდისპრესექთ", "4kgaming": "4კგეიმინგი", "gamerbr": "გეიმერბრ", "gameplays": "თამაშები", "consoleplayer": "კონსოლახლოს", "boxi": "ბოქსი", "pro": "პროფი", "epicgamers": "ეპიკგეიმერები", "onlinegaming": "ონლაინგეიმინგი", "semigamer": "სემიგეიმერი", "gamergirls": "გეიმერკkızlebi", "gamermoms": "გამერმამები", "gamerguy": "გეიმერგეი", "gamewatcher": "გეიმვაჩერი", "gameur": "ბუგურ", "grypc": "გრიპც", "rangugamer": "რანუგამერი", "gamerschicas": "გამაზრელყველები", "otoge": "ოცნება", "dedsafio": "დედსაfio", "teamtryhard": "ტიმტრაიჰარდი", "mallugaming": "მალლუთაgეიმინგი", "pawgers": "პაუგერს", "quests": "მოგზაურობები", "alax": "ალახ", "avgn": "ავგნ", "oldgamer": "ძველიამაშა", "cozygaming": "კომფორტულიგასართობი", "gamelpay": "gamelpay", "juegosdepc": "პკთამაშები", "dsswitch": "დსხავნე", "competitivegaming": "კონკურსულიპერიოდისთამაშები", "minecraftnewjersey": "მაინკრაფტნიუიჯერსი", "faker": "ფეიკერი", "pc4gamers": "pc4გეიმერებისთვის", "gamingff": "გეიმინგფფ", "yatoro": "იურატორო", "heterosexualgaming": "ჰეტეროსექსუალურითამაში", "gamepc": "თამაშიპკ", "girlsgamer": "გოგოებითამაშიუზრუნველყოფენ", "fnfmods": "fnfმოდები", "dailyquest": "დღიურიგამოწვევა", "gamegirl": "თამაშისგpiger", "chicasgamer": "გეიმერაბოებები", "gamesetup": "თამაშისმოწყობილობა", "overpowered": "დაუმარცხებელი", "socialgamer": "სოციალურიგეიმერი", "gamejam": "გეიმჯემი", "proplayer": "პროფლეიერი", "roleplayer": "როლურიმოთამაშე", "myteam": "ჩემიაგენტი", "republicofgamers": "მურმანებისრеспублика", "aorus": "აორის", "cougargaming": "კუგარისგეიმინგი", "triplelegend": "მოკლელეგენდა", "gamerbuddies": "გეიმეერებისმეგობრები", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "ქრისტიანულითამაშებისმოყვარული", "gamernerd": "გეიმერშైన్", "nerdgamer": "ნერდგეიმერი", "afk": "პოზიცია", "andregamer": "ანდრეგეიმერი", "casualgamer": "არაყისმოთამაშე", "89squad": "89სექტორი", "inicaramainnyagimana": "ინიკარამაინნიაგიმანა", "insec": "ინსეკ", "gemers": "გემერს", "oyunizlemek": "ოჯონიზლემი", "gamertag": "გეიმერუცნობი", "lanparty": "ლანპარტი", "videogamer": "ვიდეომოთამაშე", "wspólnegranie": "ერთადერთითამაში", "mortdog": "მორტდოგი", "playstationgamer": "პლეისტეიშენგეიმერი", "justinwong": "ჯასტინვონგ", "healthygamer": "ს건강게임ერი", "gtracing": "გტრეიცი", "notebookgamer": "ნოუთბუქგეიმერი", "protogen": "პროტოჯენი", "womangamer": "ქალიბრძოლერი", "obviouslyimagamer": "რათქმაუნდაგეიმერივარ", "mario": "მარიო", "papermario": "პაპერმარიო", "mariogolf": "მარიოგოლფ", "samusaran": "სამუსარან", "forager": "მარცვლაძე", "humanfallflat": "ადამიანებისგასხლტომა", "supernintendo": "სუპერნინტენდო", "nintendo64": "닌텐도64", "zeroescape": "დასავლეთუკescape", "waluigi": "ვანცუგი", "nintendoswitch": "ნინტენდოსვიჩი", "nintendosw": "ნინტენდოსვ", "nintendomusic": "ნინტენდომუსიკა", "sonicthehedgehog": "სონიკიღვლადი", "sonic": "სონიკური", "fallguys": "ფერებისბიჭები", "switch": "გაუშვით", "zelda": "ზელდა", "smashbros": "სმაშბროსი", "legendofzelda": "ჰაშთაგიზელდასლეგენდა", "splatoon": "სპლატონ", "metroid": "მეტრויד", "pikmin": "პიკმინი", "ringfit": "რინგფიტ", "amiibo": "ამიიბო", "megaman": "მეგამენი", "majorasmask": "მაიორასმასკი", "mariokartmaster": "მარიოკარტმასტერი", "wii": "ვინი", "aceattorney": "ბრაუზერისშემსწავლელი", "ssbm": "სსბმ", "skychildrenofthelight": "ჯერაოდიქმქმანისყეskychildrenofthelight", "tomodachilife": "ტომოდაჩილაიფი", "ahatintime": "ამაზრზენორთულბოონზე", "tearsofthekingdom": "მეფისმდირი", "walkingsimulators": "სამივლისმიკერძოებები", "nintendogames": "ნინტენდოსთამაშები", "thelegendofzelda": "ზeldoსლეგენდა", "dragonquest": "დრაონქვესტი", "harvestmoon": "ბოსტნეულისმთვარე", "mariobros": "მარიობროსი", "runefactory": "რუნეფაქტორი", "banjokazooie": "ბანჯოკაზუი", "celeste": "ცელესტე", "breathofthewild": "თავისუფალისუნთქვა", "myfriendpedro": "ჩემოიმედნი_pedro", "legendsofzelda": "ზელდაlegendები", "donkeykong": "დონკიკონგი", "mariokart": "მარიოკარტი", "kirby": "კირბი", "51games": "51თამაში", "earthbound": "დედამიწაზე", "tales": "პატარაისტორიები", "raymanlegends": "რეიმანლეგენდები", "luigismansion": "ლუიგისძველი_მანსარდი", "animalcrosssing": "მოწუწეები_გადასასვლი", "taikonotatsujin": "ტაიკონოტაცუჭინ", "nintendo3ds": "ნინტენდო3დს", "supermariobros": "სუპერმარიობროსი", "mariomaker2": "მარიომეიკერი2", "boktai": "ბოკთაი", "smashultimate": "დანგრაშიდიდი", "nintendochile": "닌텐도ჩილე", "tloz": "ტლოზ", "trianglestrategy": "ტრიანგლურისტრატეგია", "supermariomaker": "სუპერმარიომეიკერი", "xenobladechronicles3": "ზენობლეიდქრონიკულები3", "supermario64": "სუპერმარიო64", "conkersbadfurday": "კონკერებიარამოსულები", "nintendos": "ნინტენდოს", "new3ds": "ახალი3დინდი", "donkeykongcountry2": "ბუზღუნაყუთი2", "hyrulewarriors": "ჰაირულისმეომრები", "mariopartysuperstars": "მარიოპარტიsuperstars", "marioandsonic": "მარიოდასონიკი", "banjotooie": "ბანჯოტოი", "nintendogs": "ნინთენდოგები", "thezelda": "დზელდა", "palia": "პალია", "marioandluigi": "მარიოდალუიჯი", "mariorpg": "მარიორპგ", "zeldabotw": "ზელდააბრევესთკანონ", "yuumimain": "იუმიმაინ", "wildrift": "ვაილდრიფტი", "riven": "რივი", "ahri": "აჰრი", "illaoi": "ილიაოდი", "aram": "არამ️", "cblol": "ცბლოლ", "leagueoflegendslas": "ლიგაენდებისლთაველები", "urgot": "ურგოტ", "zyra": "ზირა", "redcanids": "წითელი_canids", "vanillalol": "ვანილალოლ", "wildriftph": "ვაილდრიფთფჰ", "lolph": "ლოლფ", "leagueoflegend": "ლიგაlegend", "tốcchiến": "ტოკჩიენის", "gragas": "გრაგას", "leagueoflegendswild": "ლეგენდებისლიგა_ველური", "adcarry": "ადკარი", "lolzinho": "ლოლძინיו", "leagueoflegendsespaña": "ლიგაალეგენდებისესპანეთი", "aatrox": "აოტროკსი", "euw": "ევიუ", "leagueoflegendseuw": "ლეგენდებისლიგაeuw", "kayle": "კაილე", "samira": "სამირა", "akali": "აკალი", "lunari": "ლუნარი", "fnatic": "ფნატიკი", "lollcs": "ლოლცს", "akshan": "აქშანი", "milio": "მილიო", "shaco": "შაკო", "ligadaslegendas": "ლიგადანისლეგენდები", "gaminglol": "გეიმინგლოლ", "nasus": "ნასუს", "teemo": "ტიმო", "zedmain": "ზედმაინდი", "hexgates": "ჰექსგეითსი", "hextech": "ჰექსტექი", "fortnitegame": "ფორტნაითგეიმი", "gamingfortnite": "გვარამაღიზომბებისო", "fortnitebr": "ფორტნაითბრ", "retrovideogames": "რეტროვიდეოთამაში", "scaryvideogames": "შიშიანივიდეოთამაშები", "videogamemaker": "ვიდეოთამაშებისდამამზადებელი", "megamanzero": "მეგამანზერო", "videogame": "ვიდეოთამაში", "videosgame": "ვიდეოებისთამაში", "professorlayton": "პროფესორლეიტონ", "overwatch": "მონიტორი", "ow2": "ow2", "overwatch2": "ოვერვოტჩ2", "wizard101": "wizard101", "battleblocktheater": "ბატლბლოკთეატრი", "arcades": "არქადები", "acnh": "acnh", "puffpals": "ფაფუმეგობრები", "farmingsimulator": "ფერმერებისსიმულატორი", "robloxchile": "რობლოქსჩილი", "roblox": "რობლოქსი", "robloxdeutschland": "რობლოქსგერმანია", "robloxdeutsch": "რობლოქსგერმანულში", "erlc": "ერlc", "sanboxgames": "სანბოქსიგრებები", "videogamelore": "ვიდეოთამაშებისძიება", "rollerdrome": "როლერდრომი", "parasiteeve": "პარაზიტები", "gamecube": "გეიმკიუბი", "starcraft2": "სტარგრაფტ2", "duskwood": "დუსკვუდ", "dreamscape": "საწყისი_მიწა", "starcitizen": "ასტრონავტი", "yanderesimulator": "იანდერესიმულატორი", "grandtheftauto": "გრანდთეფტრაციო", "deadspace": "დედსპეისი", "amordoce": "ამორმოწე", "videogiochi": "ვიდეობgiochi", "theoldrepublic": "ძველირესპუბლიკა", "videospiele": "ვიდეოთამაში", "touhouproject": "touhouproject", "dreamcast": "დრიმკასტი", "adventuregames": "მაოდსაინტერესოთამაშები", "wolfenstein": "ვოლფენშტაინი", "actionadventure": "ადრენალინისთავგადასავალი", "storyofseasons": "ბრძოლისწლებისისტორია", "retrogames": "რეტროგეიმები", "retroarcade": "რეტროსადგური", "vintagecomputing": "ვინტაჟურიკომპიუტერებით", "retrogaming": "რეტროგეიმინგი", "vintagegaming": "ვინტაჟურითამაშები", "playdate": "გასართობიშეხვედრა", "commanderkeen": "კომანდერ_კინი", "bugsnax": "ბაგსნაქსი", "injustice2": "არასწორობა2", "shadowthehedgehog": "შედარებისხოხობი", "rayman": "რეიმენი", "skygame": "სიმღერაboo", "zenlife": "ზენმოწვევა", "beatmaniaiidx": "ბითმანიაiidx", "steep": "დამძიმება", "mystgames": "მისტიკურითამაშები", "blockchaingaming": "ბლოკჩეინთამაშები", "medievil": "მედიევილი", "consolegaming": "კონსოლისთამაში", "konsolen": "კონსოლენი", "outrun": "დამარცხე", "bloomingpanic": "აივნიანმავი", "tobyfox": "ტობიფოქსი", "hoyoverse": "ჰოიოვერსი", "senrankagura": "სენრანკაგურა", "gaminghorror": "მოსამართლებელითამაშები", "monstergirlquest": "მონსტროგოგოსძიება", "supergiant": "სუპერგიგანტი", "disneydreamlightvalle": "დისნეიდრიმლაინთვალლი", "farmingsims": "სოფლისმინიმები", "juegosviejos": "ძველიხასე", "bethesda": "ბეთესდა", "jackboxgames": "ჯეკბოქსგეიმსი", "interactivefiction": "ინტერაქტიურიიფიქცია", "pso2ngs": "pso2ngs", "grimfandango": "გრიმფანდანგო", "thelastofus2": "ბოლოები2", "amantesamentes": "მთავარიამორები", "visualnovel": "ვილიზუალურირომანი", "visualnovels": "ვიდეონოველები", "rgg": "რგგ", "shadowolf": "შავმელიავარდი", "tcrghost": "tcrგმირი", "payday": "ფულმდა", "chatherine": "ჩათრინინგი", "twilightprincess": "თხელიათიპრინცესა", "jakandaxter": "ჯადგანდაქსტერი", "sandbox": "სანდბოქსი", "aestheticgames": "ესტეთიკურითამაშები", "novelavisual": "რეჟიმისიმულაცია", "thecrew2": "thecrew2", "alexkidd": "ალექსკიდი", "retrogame": "რეტროგეიმი", "tonyhawkproskater": "ტონი_hawk_პროფესიონალი_სკეიტერი", "smbz": "სმბზ", "lamento": "ლამენტო", "godhand": "ბრძოლაgodhand", "leafblowerrevolution": "შალისყნუტისრევოლუცია", "wiiu": "wiiu", "leveldesign": "ლეველდიზაინი", "starrail": "სტარრაილი", "keyblade": "ქვემეხი", "aplaguetale": "აპლაგუეტეიმე", "fnafsometimes": "fnafsometimes", "novelasvisuales": "ვიდეორომანები", "robloxbrasil": "რობლოქსი_ბრაზილია", "pacman": "პაკმანი", "gameretro": "გეიმრეტრო", "videojuejos": "ვიდეოთამაშები", "videogamedates": "ვიდეოთამაშებისშეხვედრები", "mycandylove": "ჩემიენთ甘甜სიყვარული", "megaten": "მეგატენი", "mortalkombat11": "მორტალსწავლის11", "everskies": "ევერსკაიზი", "justcause3": "justcause3", "hulkgames": "ჰალკთამაშიები", "batmangames": "ბატმანითამაშები", "returnofreckoning": "მოწვეულია_აღდგენის_დრო", "gamstergaming": "გამსტერგეიმინგი", "dayofthetantacle": "ტანტაკლისდღე", "maniacmansion": "მანიაკებისმამული", "crashracing": "კრაშრეისინგი", "3dplatformers": "3დპლატფორმერები", "nfsmw": "nfsmw", "kimigashine": "კიმიგაშინე", "oldschoolgaming": "ძველისკოლათამაში", "hellblade": "ადამიანურტამასი", "storygames": "ისტორიებისთამაშები", "bioware": "ბიოუერ", "residentevil6": "თვითმკვლელობა6", "soundodger": "საუნდოდჯერი", "beyondtwosouls": "ბeyondtwosouls", "gameuse": "თამაშისას", "offmortisghost": "არიყაზმისმოჩვენება", "tinybunny": "პატარაარამედ", "retroarch": "რეტროარქი", "powerup": "პოგვარი", "katanazero": "კატანაზერო", "famicom": "ფამიკომ", "aventurasgraficas": "გრაფიკულიამოცანები", "quickflash": "ფრენბურთი", "fzero": "ფზერო", "gachagaming": "გაჩაგეიმინგი", "retroarcades": "რეტროარქადები", "f123": "f123", "wasteland": "ბირეთისქვეყანა", "powerwashsim": "პაუერვაშსიმ", "coralisland": "კორალურიკუნძული", "syberia3": "სიბერია3", "grymmorpg": "გრიმმორპგ", "bloxfruit": "ბლოქსფruits", "anotherworld": "მეორემსოფლიო", "metaquest": "მეტაკვესტი", "animewarrios2": "ანიმეარიორები2", "footballfusion": "ფეხბურთისფუზია", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "ასტრონერი", "legomarvel": "ლეგომარველ", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "გრეხილიმეტალი", "beamngdrive": "ბიმნჯიდრაივი", "twdg": "ტვიდიჯი", "pileofshame": "შეუძლებელი_შუდა", "simulator": "სიმულატორი", "symulatory": "სიმულატორი", "speedrunner": "სპიდრანერი", "epicx": "ეპიკისx", "superrobottaisen": "სუპერრობოტთანმიმართება", "dcuo": "dcuo", "samandmax": "სამანდმაქს", "grywideo": "გრიოვიდეო", "gaiaonline": "გაიაონლაინ", "korkuoyunu": "კორკუოინუ", "wonderlandonline": "სასწაულებისქვეყანაონლაინ", "skylander": "ჰაერისმცველი", "boyfrienddungeon": "ბოიფრენდინჯონი", "toontownrewritten": "ტუნტაუნისგანღწეული", "simracing": "სიმრేసინგი", "simrace": "სიმრესი", "pvp": "pvp", "urbanchaos": "ქალაქურიქაოსი", "heavenlybodies": "ღმერთისსხეულები", "seum": "სიუმ", "partyvideogames": "პარტივიდეოთამაშები", "graveyardkeeper": "სამხედროებისმფლობელი", "spaceflightsimulator": "დაბოლოსმთვარისშეხება", "legacyofkain": "კაინიძმისმემკვიდრეობა", "hackandslash": "ჰაკიდაგაწყვეტა", "foodandvideogames": "სასურსათებიდავიდეოთამაშები", "oyunvideoları": "მოსასვენებლები", "thewolfamongus": "გვაჩნიამგელიჩვენშორისthewolfamongus", "truckingsimulator": "ტრანსპორტირებისსიმულატორი", "horizonworlds": "ჰორიზონტალურდრუნვები", "handygame": "მარტივითამაში", "leyendasyvideojuegos": "ლეგენდებისგანვიდეოთამაშები", "oldschoolvideogames": "ძველსკოლისვიდეოთამაშები", "racingsimulator": "რაისინგსიმულატორი", "beemov": "ბიმოვი", "agentsofmayhem": "მხეცებისაგენტები", "songpop": "სონგპოპ", "famitsu": "ფერადი", "gatesofolympus": "ოლიმპოსისკარიბები", "monsterhunternow": "მონსტრებდევდიერიახლა", "rebelstar": "ბუნეისტარ", "indievideogaming": "ინდივიდუალურივიდეოთამაშები", "indiegaming": "ინდიგეთამაშები", "indievideogames": "ინდიპთამაშები", "indievideogame": "ინდიურივიდეოთამაში", "chellfreeman": "ჩელფრიმანი", "spidermaninsomniac": "სპაიდერმენიინსომნიაკი", "bufffortress": "ბუფფორტესი", "unbeatable": "უწყვეტი", "projectl": "პროექტიl", "futureclubgames": "მომავალკლუბურითამაშები", "mugman": "მუგმანი", "insomniacgames": "ინსომნიაკგეიმსი", "supergiantgames": "სუპერგიგანტებიგმირები", "henrystickman": "ჰენრი_სტიკმენი", "henrystickmin": "ჰენრიაგლუვკონტროლი", "celestegame": "ცელესტეგეიმი", "aperturescience": "აპერტურასაინსი", "backlog": "ბექლოგი", "gamebacklog": "თამაშებისრეპორტი", "gamingbacklog": "თამაშებისჩამონათვალი", "personnagejeuxvidéos": "პერსონაჟებიმარხი", "achievementhunter": "სწრაფიმონადირე", "cityskylines": "ქალაქისცისხորձები", "supermonkeyball": "სუპერმონკიაბალი", "deponia": "დეპონია", "naughtydog": "ბილწმოკლეძაღლი", "beastlord": "ბისტლორდი", "juegosretro": "რეტროგეიმები", "kentuckyroutezero": "კენტაკიდისमार्गი零", "oriandtheblindforest": "ორიაჩდხმაში", "alanwake": "ალანიამბავი", "stanleyparable": "სტენლიაპარაბლი", "reservatoriodedopamin": "რეზერვატორიოდედოპამინი", "staxel": "სტაქსელი", "videogameost": "ვიდეოთამაშებისოუსტი", "dragonsync": "დრაკონსინქ", "vivapiñata": "ვივაპინატა", "ilovekofxv": "მიყვარსკოფქვ", "arcanum": "არამაშვი", "neoy2k": "ნეო2000", "pcracing": "პჭარბებისგარბენი", "berserk": "ბერგერკი", "baki": "ბაკი", "sailormoon": "სეილისმუნი", "saintseiya": "ვარსკვლავურიჯუჯა", "inuyasha": "ინუჯაში", "yuyuhakusho": "იუჯიუახუსო", "initiald": "ინიციალდ", "elhazard": "ელჰაზარდი", "dragonballz": "დრაგონბოლზ", "sadanime": "დამწუხრებულიანიმე", "darkerthanblack": "ბნელიაშკერა", "animescaling": "ანიმესკალინგი", "animewithplot": "ანიმეგავაში", "pesci": "პესჩი", "retroanime": "რეტროანიმე", "animes": "ანიმეები", "supersentai": "სუპერსენტაი", "samuraichamploo": "სამურაიჩამპლუ", "madoka": "მადოკა", "higurashi": "ჰიგურაშიზე", "80sanime": "80ანიმე", "90sanime": "90სანიმე", "darklord": "შავიმმართველი", "popeetheperformer": "პოპიტეფორმერი", "masterpogi": "მარიონეტებისმეფე", "samuraix": "სამურაიმხიარულობა", "dbgt": "დბგტ", "veranime": "არამიადნი", "2000sanime": "2000ანიმე", "lupiniii": "ლუპინიიи", "drstoneseason1": "დრესტონემსეზონი1", "rapanime": "რეპანიმე", "chargemanken": "ჩამუხტეიადაგვიდგე", "animecover": "ანიმეკავი", "thevisionofescaflowne": "ბრძოლისტექნოლოგიაescaflowne", "slayers": "სლაერებზე", "tokyomajin": "ტოკიომაჯინ", "anime90s": "ანიმე90იანელები", "animcharlotte": "ანიმშარლოტი", "gantz": "განტზ", "shoujo": "შოუჯო", "bananafish": "ბანანასწყალი", "jujutsukaisen": "ჯუჯუცუსაესენ", "jjk": "ჯჯკ", "haikyu": "ჰაიკიუს", "toiletboundhanakokun": "ტუილეტზეარბილიანჰანაკოკუნ", "bnha": "ბნჰა", "hellsing": "ჰელსინგ", "skipbeatmanga": "შეწყვიტე_ბეში_მანგა", "vanitas": "ვანიტასი", "fireforce": "ცეცხლამდღეგვყოფილი", "moriartythepatriot": "მორიარტიაპატრიოტი", "futurediary": "მომავალიდღიურიfuturediary", "fairytail": "ფერადიზღაპარი", "dorohedoro": "დოროჰედორო", "vinlandsaga": "ვინლანდსაგა", "madeinabyss": "მოსწავლებითნაკლებად", "parasyte": "პარაზიტი", "punpun": "პუნპუნ", "shingekinokyojin": "შინგეკინოკიოჯინ", "mushishi": "მუშიში", "beastars": "ბისტარს", "vanitasnocarte": "ვანიტასნოკარტე", "mermaidmelody": "მელოდიაწყლისნინძების", "kamisamakiss": "კამიშვილი", "blmanga": "ბლმანგა", "horrormanga": "ჰორორისმანგა", "romancemangas": "რომანსმანგები", "karneval": "კარნავალი", "dragonmaid": "დრაგონმაიდ", "blacklagoon": "ბლექლაგუნი", "kentaromiura": "კენტარომიური", "mobpsycho100": "მობსპაიკო100", "terraformars": "ტერრაფორმარს", "geniusinc": "გენიოსიinc", "shamanking": "შამანკინგი", "kurokonobasket": "კუროკონობლომბოკეტი", "jugo": "ჯუგო", "bungostraydogs": "ბუნგოსტრეიდოგები", "jujustukaisen": "ჯუჯუსტუკეისენი", "jujutsu": "ჯუჯუცუ", "yurionice": "იურსიოკი", "acertainmagicalindex": "მისითაუმეცდამარდელიასაჩუქარი", "sao": "საო", "blackclover": "შავისამკუთხედი", "tokyoghoul": "ტოკიოგჰოული", "onepunchman": "ერთიკაველბული", "hetalia": "ჰეტალია", "kagerouproject": "კაგეროუმოქმედება", "haikyuu": "ჰაიკიუს", "toaru": "ტოარუ", "crunchyroll": "კრანჩიროლი", "aot": "aot", "sk8theinfinity": "სკეითთეინფინითი", "siriusthejaeger": "სირიუსთიაეგერი", "spyxfamily": "სპაიxოჯახი", "rezero": "რეზერო", "swordartonline": "მახვილისხელოვნებამონლაინში", "dororo": "დომორო", "wondereggpriority": "წეღანისკვერცხისპრიორიტეტი", "angelsofdeath": "მკვდრებისანგელოზები", "kakeguri": "კაკეგური", "dragonballsuper": "დრაკონბოლისუზერა", "hypnosismic": "ჰიპნოსიმსიკი", "goldenkamuy": "გოლდენკამუი", "monstermusume": "მონსტრმუსუმე", "konosuba": "კონოსუბა", "aikatsu": "აიკატцу", "sportsanime": "სპორტულანიმე", "sukasuka": "სუკასუქა", "arwinsgame": "აროორდენისთამაში", "angelbeats": "ანგელბითส์", "isekaianime": "ისეკაიანიმე", "sagaoftanyatheevil": "საგაოფთანიათამბოროტება", "shounenanime": "შონენანიმე", "bandori": "ბანდორი", "tanya": "ტანია", "durarara": "დურარარა", "prettycure": "ლამაზმყუდროება", "theboyandthebeast": "ბიჭიდამანიაკი", "fistofthenorthstar": "ბუზისტაფურქში", "mazinger": "მაზინგერი", "blackbuttler": "შავიმსახური", "towerofgod": "ღვაწლისკოშკი", "elfenlied": "ელფენლებდი", "akunohana": "აუზოგადოთ", "chibi": "ჩიბი", "servamp": "servamp", "howtokeepamummy": "როგორისტბრუნოსმამას", "fullmoonwosagashite": "მთვარისშუაღამეboo", "shugochara": "შუგოჭარა", "tokyomewmew": "ტოკიომიუმიუ", "gugurekokkurisan": "გუგურეკოკურிசანი", "cuteandcreepy": "უცნაურიდასაყვარელი", "martialpeak": "მარტიალპიკ", "bakihanma": "ბაკიჰანმა", "hiscoregirl": "ჰაისკორგოგო", "orochimaru": "ორგოიწიმარუ", "mierukochan": "მიერუკოჭანი", "dabi": "დაბი", "johnconstantine": "ჯონკონსტანტინი", "astolfo": "ასტროფლო", "revanantfae": "რევანანტფე", "shinji": "შინჯი", "zerotwo": "ზეროტუუ", "inosuke": "ინოსუკე", "nezuko": "ნეზუკო", "monstergirl": "მონსტროგოგო", "kanae": "ქანაე", "yone": "იონის", "mitsuki": "მიცუკი", "kakashi": "კაკაში", "lenore": "ლენორე", "benimaru": "ბენიმარუ", "saitama": "საიტამა", "sanji": "სანჯი", "bakugo": "ბაკუგო", "griffith": "გრიფითი", "ririn": "რირინ", "korra": "კორა", "vanny": "ვანი", "vegeta": "ვეგეტა", "goromi": "გორომი", "luci": "ლუწი", "reigen": "რეიგენი", "scaramouche": "სკარამუში", "amiti": "ამითი", "sailorsaturn": "გემსfahrerსატურნი", "dio": "დიო", "sailorpluto": "მეზღვაურიპლუტო", "aloy": "ალოი", "runa": "რუანა", "oldanime": "قدიდოლანიმე", "chainsawman": "ჩაინსოუმენი", "bungoustraydogs": "ბუნგოძაღლებო", "jogo": "ჯოგო", "franziska": "ფრანცისკა", "nekomimi": "ნეკომიმი", "inumimi": "ინუმიმი", "isekai": "იზეკაი", "tokyorevengers": "ტოკიორევენდერზი", "blackbutler": "შავიიქმნელს", "ergoproxy": "ერგოპროქსი", "claymore": "კლემორი", "loli": "ლოლი", "horroranime": "ჰორორანიმე", "fruitsbasket": "ხილიებისკორომლი", "devilmancrybaby": "შენიშვნააქმოდისეშმაკოტმუქთვინათ", "noragami": "ნორაგამე", "mangalivre": "მანგალივრე", "kuroshitsuji": "კუროშიცუიჯი", "seinen": "დისკუსია", "lovelive": "სიყვარულითცოცხალი", "sakuracardcaptor": "საკურაკარდქეპტორი", "umibenoetranger": "უმიბენოეტრანჟერი", "owarinoseraph": "ოარნოსერაფი", "thepromisedneverland": "დამზადებულიარასდროსყოფილა", "monstermanga": "მონსტრსმანგა", "yourlieinapril": "თუარწერაიაპრილში", "buggytheclown": "ბუგიეთკლაუაზე", "bokunohero": "ბოკუნოელაა", "seraphoftheend": "შემდეგიდასასრულისთვის", "trigun": "ტრიგუნი", "cyborg009": "საიბერგ009", "magi": "მაჯი", "deepseaprisoner": "ღრმაზღვისპატიმარი", "jojolion": "ჯოხოლიონ", "deadmanwonderland": "მკვდარადაექვქება", "bannafish": "ბანაფიში", "sukuna": "სუკუნა", "darwinsgame": "დარვინისთამაშები", "husbu": "ჰუსბუ", "sugurugeto": "სუგურუგეტო", "leviackerman": "ლევისაქერმანი", "sanzu": "სანსუ", "sarazanmai": "სარაზანმეï", "pandorahearts": "პანდორასგული", "yoimiya": "იოიმია", "foodwars": "საკვებისომები", "cardcaptorsakura": "ბარათებისაპოკაპტორი_საკურა", "stolas": "სტოლას", "devilsline": "დევილისლაინი", "toyoureternity": "შენსუსოვში", "infpanime": "ინფპანიმე", "eleceed": "ელისიდი", "akamegakill": "აქამეგაკილი", "blueperiod": "ბლუპერიოდი", "griffithberserk": "გრიფიტბერსერკი", "shinigami": "შინიგამი", "secretalliance": "საიდუმლოალიანსი", "mirainikki": "მირაინიკი", "mahoutsukainoyome": "მაჰოუცუკაინოიომე", "yuki": "იუკი", "erased": "წაშლილი", "bluelock": "ბლულოგი", "goblinslayer": "გობლინებისმკვლელი", "detectiveconan": "დეტექტივკონანი", "shiki": "შიკი", "deku": "დეკუ", "akitoshinonome": "აკხმკი", "riasgremory": "რიასგრემორი", "shojobeat": "შოჯობით", "vampireknight": "ვასილაკალისცაvampireknight", "mugi": "მუგი", "blueexorcist": "ლურჯიექსორცისტი", "slamdunk": "სლემდანკი", "zatchbell": "ზატჩბელი", "mashle": "მართლა", "scryed": "სკრაიდed", "spyfamily": "სპაიფემილი", "airgear": "ჰაერისგადასაშლელი", "magicalgirl": "აოდნისგოგო", "thesevendeadlysins": "ესქვეყნიერებისშვიდიცოდვა", "prisonschool": "ჯილდოსკოლა", "thegodofhighschool": "ბრძოლისსკოლა", "kissxsis": "კოცნაასახლში", "grandblue": "გრანდბლუ", "mydressupdarling": "მიმოსაბოლოსმრუქნა", "dgrayman": "დgrayman", "rozenmaiden": "როზენმაიდენი", "animeuniverse": "ანიმეუნივერსი", "swordartonlineabridge": "ესკრახიswordartonlineabridge", "saoabridged": "საოცრად_გადმოჭრილი", "hoshizora": "ჰოშიზორა", "dragonballgt": "დეdragonballgt", "bocchitherock": "ბოჩითეროკი", "kakegurui": "კაკეგურუი", "mobpyscho100": "მობფსიხო100", "hajimenoippo": "ჰაჯიმენოიპო", "undeadunluck": "უსიცოცხლოაზრიანობა", "romancemanga": "რომანსულიმანგა", "blmanhwa": "ბლმანვა", "kimetsunoyaba": "კიმეცუნოიაბა", "kohai": "კოჰაი", "animeromance": "ანიმერომანსი", "senpai": "სენპაის", "blmanhwas": "ბლმანჰვას", "animeargentina": "ანიმეარგენტინა", "lolicon": "ლოლიკონი", "demonslayertothesword": "დემონებისმკვლელიფერადი_დაჯაჭვული", "bloodlad": "ბლდლედ", "goodbyeeri": "გადაგებუშორობას", "firepunch": "მხოლოდმარტივი", "adioseri": "ადიოსერი", "tatsukifujimoto": "ტაცუკიფუჯიმოტო", "kinnikuman": "კინიკუმანი", "mushokutensei": "მუშოკუტენსეი", "shoujoai": "შოჯოაი", "starsalign": "ვარსკვლავებიაერთიანდებიან", "romanceanime": "რომანტიკულიანიმე", "tsundere": "tsundere", "yandere": "იანდილერი", "mahoushoujomadoka": "მაჰოუშოუომადოკა", "kenganashura": "კენგანაშურა", "saointegralfactor": "საეზოინტეგრალფაქტორი", "cherrymagic": "ბურცუკებისჯადოქრობა", "housekinokuni": "ჰაუზკინოიკუნი", "recordragnarok": "რეკორდრაგნაროკ", "oyasumipunpun": "ოიასუმიპუნპუნ", "meliodas": "მელიოდა스", "fudanshi": "ფუდანშী", "retromanga": "ნოსტალგიომანგა", "highschoolofthedead": "ბრძოლისსკოლა", "germantechno": "გერმანურიتكنო", "oshinoko": "ოიშინოკო", "ansatsukyoushitsu": "ანსამსწყოახლიას", "vindlandsaga": "ვენდისლანდსაგა", "mangaka": "მანგაკა", "dbsuper": "dbსუპერ", "princeoftennis": "ტენისისპრინცი", "tonikawa": "ტონიკავა", "esdeath": "ესდეთათვის", "dokurachan": "დოკურაჩანი", "bjalex": "ბჯალექს", "assassinclassroom": "ასასინებისკლასroom", "animemanga": "ანიმემანგა", "bakuman": "ბაკუმან", "deathparade": "გარდაცვალებისპარადი", "shokugekinosouma": "შოკუგეკინოზოωμα", "japaneseanime": "იაპონურიანიმე", "animespace": "ანიმესივრცე", "girlsundpanzer": "წითელიამძემზე", "akb0048": "აკბ0048", "hopeanuoli": "იმედისმარცვალი", "animedub": "ანიმედუბლირება", "animanga": "ანიმანგა", "tsurune": "ცურინე", "uqholder": "უფრემოსები", "indieanime": "ინდიანიმე", "bungoustray": "ბუნგოუჩხიკი", "dagashikashi": "დაგაშიკაში", "gundam0": "გუნდამო0", "animescifi": "ანიმეპოლიტიკა", "ratman": "რათმანი", "haremanime": "ჰარემანიმე", "kochikame": "კოჩიკამე", "nekoboy": "ნეკობოი", "gashbell": "გაშბელл", "peachgirl": "კენჭისგოგო", "cavalieridellozodiaco": "კავალერიდელოზოდიაკო", "mechamusume": "მეჯამუსუმე", "nijigasaki": "ნიჯიგასაკი", "yarichinbitchclub": "იარიჩინისბიჩკლუბი", "dragonquestdai": "დრაგონქվესტდაი", "heartofmanga": "მანგასმუსკული", "deliciousindungeon": "დელიკატურიდამარütჩეგილში", "manhviyaoi": "მახვიაოი", "recordofragnarok": "რაგნაროკისჩაწერა", "funamusea": "ფუნამუზეა", "hiranotokagiura": "ჰირანოტოკაგიური", "mangaanime": "მანგაანიმე", "bochitherock": "ბოჭითეროკ", "kamisamahajimemashita": "კამისამაჰაჯიმემაშიდა", "skiptoloafer": "შეუჩერებელმოლოდინში", "shuumatsunovalkyrie": "შუმაცუნოვალკირი", "tutorialistoohard": "ტუტორიალიძალიანრთულია", "overgeared": "ზედმეტიაპარატურა", "toriko": "ტორიკო", "ravemaster": "რევმასტერი", "kkondae": "კონდაე", "chobits": "ჩობიტსი", "witchhatatelier": "რქებიათმოსაწარმო", "lansizhui": "ლანციუიუ", "sangatsunolion": "სანგაცნობილიონ", "kamen": "კამენ", "mangaislife": "მანგაიარისცხოვრება", "dropsofgod": "ღმერთისწვეთები", "loscaballerosdelzodia": "ზოიდიისმამაკაცები", "animeshojo": "ანიმეშოუ", "reverseharem": "რევერსჰარემ", "saintsaeya": "სამშვენიერება", "greatteacheronizuka": "დიდიმასწავლებელიონისუკა", "gridman": "გრიდმენი", "kokorone": "კოკორონე", "soldato": "სოლდატო", "mybossdaddy": "ჩემიბოსიამამა", "gear5": "gear5", "grandbluedreaming": "დიდვერცხლისფერიოცნებები", "bloodplus": "ბრძოლისძალაცplus", "bloodplusanime": "სისხლიანიმე", "bloodcanime": "კლანისმანი", "bloodc": "სისხვიდა", "talesofdemonsandgods": "მიცვალებულებისადაღმერთებისისტორიები", "goreanime": "გუორანიმე", "animegirls": "ანიმეგოგოები", "sharingan": "შეიარაღებული", "crowsxworst": "ჩხირიsxგაკლებები", "splatteranime": "სპლატტერანიმე", "splatter": "სისხლიცხევა", "risingoftheshieldhero": "მოსვლისმდინარებისმოჯახურებლად", "somalianime": "სომალიურიენიმე", "riodejaneiroanime": "რიოდეჟანეიანიმე", "slimedattaken": "სპილენძზეგაწუწული", "animeyuri": "ანიმეიურში", "animeespaña": "ანიმეესპანეთი", "animeciudadreal": "ანიმექალაქრეალი", "murim": "მურიმ", "netjuunosusume": "ნეტჯუნოსუსუმე", "childrenofthewhales": "ბუნებისბავშვები", "liarliar": "ყალბყალბ", "supercampeones": "სუპერდუბლების", "animeidols": "ანიმეისიდოლები", "isekaiwasmartphone": "ისეკაიზესმარტფონისმქონე", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "საშინელიგოგოები", "callofthenight": "ძილისგანწვლილი", "bakuganbrawler": "ბაკუგანბროულერი", "bakuganbrawlers": "ბაკუგანბრაუერებს", "natsuki": "ნაცუკი", "mahoushoujo": "მაჰოუშოუjo", "shadowgarden": "სიბნელეშgarten", "tsubasachronicle": "წუწბასასარწმუნოება", "findermanga": "მანგისაძიება", "princessjellyfish": "პრინცესაჟელქაფი", "kuragehime": "კურაჟებისპრინესა", "paradisekiss": "პარადიზისკისი", "kurochan": "კუროჩანი", "revuestarlight": "რევიუსტარლაით", "animeverse": "ანიმესამყაროა", "persocoms": "პერსოკომები", "omniscientreadersview": "ყველაფერიიცინისმკითხავისთვალით", "animecat": "ანიმეცხვარი", "animerecommendations": "ანიმერეკომენდაციები", "openinganime": "ანიმესამნახავი", "shinichirowatanabe": "შინიჩიროუატანაბე", "uzumaki": "უზუმაკი", "myteenromanticcomedy": "მაპინოზღაპრულირომანტიულიკომედია", "evangelion": "ევანგელიონი", "gundam": "გუნდამ", "macross": "მაკროსი", "gundams": "გუნდამსი", "voltesv": "ვოლტესვი", "giantrobots": "რბოლამეტყველებისსილამაზით", "neongenesisevangelion": "ნეონგენეზისევანგელიონ", "codegeass": "კოდეგას", "mobilefighterggundam": "მობილურიიბრძოლებიგგუნდამ", "neonevangelion": "ნეონეგვანგელიონ", "mobilesuitgundam": "मोबाइलसुइटगुंडम", "mech": "მეჩ", "eurekaseven": "ეურეკასseven", "eureka7": "ეუირეკა7", "thebigoanime": "დიდაბუნთქვიანიმე", "bleach": "ბლებოქსი", "deathnote": "მკვდარინოტები", "cowboybebop": "კოვბოიბიბოპ", "jjba": "ჯჯბა", "jojosbizarreadventure": "ჯოჯოსუცუგოარილიზაცია", "fullmetalalchemist": "სრულიმონაზონი", "ghiaccio": "გიაჩო", "jojobizarreadventures": "ჯოჯობიზარიშედევრები", "kamuiyato": "კამუიახატო", "militaryanime": "სამხედროანიმე", "greenranger": "მწვანერენჯერი", "jimmykudo": "ჯიმიკუდო", "tokyorev": "ტოკიოწყლით", "zorro": "ზორო", "leonscottkennedy": "ლეონსკოტკენედი", "korosensei": "კოროსენსეი", "starfox": "სტარფოქსი", "ultraman": "ალტრამენი", "salondelmanga": "სალონდელმანგა", "lupinthe3rd": "ლუპინმესიანში3", "animecity": "ანიმესახლი", "animetamil": "ანიმეტამილ", "jojoanime": "ჯოჯოანიმე", "naruto": "ნარუტო", "narutoshippuden": "ნარუტოსვეპუდენი", "onepiece": "ერთინაწილაკი", "animeonepiece": "ანიმეonepiece", "dbz": "დბზ", "dragonball": "დრაკონბოლი", "yugioh": "იუგიჰო", "digimon": "დიჯიმონი", "digimonadventure": "დიჯიმონმოგზაურობა", "hxh": "ჰხჰ", "highschooldxd": "სკოლისდდ", "goku": "გოკუ", "broly": "ბროლი", "shonenanime": "შონენანიმე", "bokunoheroacademia": "ბოკუნოჰეროაკადემია", "jujustukaitsen": "ჯუჯუსტკაიცენ", "drstone": "დრსტოუნი", "kimetsunoyaiba": "კიმეცუნოიაბა", "shonenjump": "შონენჯემპი", "otaka": "ოთაკა", "hunterxhunter": "ჰუნტერxhუნტერ", "mha": "მჰა", "demonslayer": "დემონებისმებრძოლი", "hinokamikagurademonsl": "ჰინოკამიკაგურადემონსლ", "attackontitan": "ატაკიტიტანებზე", "erenyeager": "ერენიისმოწურო", "myheroacademia": "ჩემიიღბლიანიადამიანი", "boruto": "ბოსუტო", "rwby": "როცი_boo", "dandadan": "დანდან", "tomodachigame": "ტომოდისთამაშები", "akatsuki": "აკაშუკი", "surveycorps": "გამოკითხვისკორპუსი", "onepieceanime": "ოფიციალურადერთხელმხოლოდanime", "attaquedestitans": "ატაკიეშვიდეულები", "theonepieceisreal": "ერთიანინაწილინამდვილადარსებობს", "revengers": "მარადონი", "mobpsycho": "მობსიქო", "aonoexorcist": "aonoexorcist", "joyboyeffect": "ცოტაბიჭისეფექტი", "digimonstory": "დიგიმონიზამი", "digimontamers": "დიჯიმონთამერები", "superjail": "სუპერგარიერი", "metalocalypse": "მეტალოკალიფსი", "shinchan": "შინჩანი", "watamote": "ვატამოტე", "uramichioniisan": "ურმიჭიონიიან", "uruseiyatsura": "ურუსეიაცურა", "gintama": "გინტამა", "ranma": "რანმა", "doraemon": "დორაიმონი", "gto": "გტო", "ouranhostclub": "იურანჰოსტკლუბი", "flawlesswebtoon": "უსახურიwebtoon", "kemonofriends": "კემონოფრენდები", "utanoprincesama": "უდიდესიპრინცესა", "animecom": "ანიმესკომი", "bobobobobobobo": "ბობობობობობო", "yuukiyuuna": "იუუქიიუმა", "nichijou": "ნიჩიჯოუ", "yurucamp": "იურუკემპი", "nonnonbiyori": "ნონონბიოიორი", "flyingwitch": "მფრინავიwitch", "wotakoi": "ვოტაკოი", "konanime": "კონანიმე", "clannad": "კლანადი", "justbecause": "უბრალოდასე", "horimiya": "ჰორიმია", "allsaintsstreet": "ყველაარლვიდან", "recuentosdelavida": "ცხოვრებისმოწყვეტები"}