{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologie", "cognitivefunctions": "cognitievefuncties", "psychology": "psychologie", "philosophy": "filosofie", "history": "ges<PERSON><PERSON><PERSON>", "physics": "natuurkunde", "science": "wetenschap", "culture": "cultuur", "languages": "talen", "technology": "technologie", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiememes", "enneagrammemes": "enneagrammemes", "showerthoughts": "showerthoughts", "funny": "grappig", "videos": "videos", "gadgets": "gadgets", "politics": "politiek", "relationshipadvice": "relatieadvies", "lifeadvice": "levensadvies", "crypto": "crypto", "news": "nieuws", "worldnews": "wereldnieuws", "archaeology": "archeologie", "learning": "leren", "debates": "debatten", "conspiracytheories": "complot<PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "universum", "meditation": "meditatie", "mythology": "mythologie", "art": "kunst", "crafts": "ambacht", "dance": "dansen", "design": "ontwerp", "makeup": "makeup", "beauty": "schoon<PERSON>id", "fashion": "mode", "singing": "zingen", "writing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "photography": "fotografie", "cosplay": "cosplay", "painting": "schilderen", "drawing": "tekenen", "books": "boeken", "movies": "films", "poetry": "po<PERSON><PERSON>", "television": "televisie", "filmmaking": "film<PERSON>roduc<PERSON>", "animation": "animatie", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "documentaires", "mystery": "mysterie", "comedy": "comedy", "crime": "misdaad", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romantiek", "realitytv": "realitytv", "action": "actie", "music": "muziek", "blues": "blues", "classical": "klassiek", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronisch", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "reizen", "concerts": "concerten", "festivals": "festivals", "museums": "musea", "standup": "cabaret", "theater": "theater", "outdoors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "tuinieren", "partying": "feesten", "gaming": "gamen", "boardgames": "b<PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "schaken", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "eten", "baking": "bakken", "cooking": "koken", "vegetarian": "vegetarisch", "vegan": "veganistisch", "birds": "vogels", "cats": "katten", "dogs": "honden", "fish": "vissen", "animals": "dieren", "blacklivesmatter": "blacklivesmatter", "environmentalism": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminisme", "humanrights": "mensenrechten", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "vrijwilligerswerk", "sports": "sport", "badminton": "badminton", "baseball": "honkbal", "basketball": "basketbal", "boxing": "boksen", "cricket": "cricket", "cycling": "<PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "voetbal", "golf": "golfen", "gym": "sportschool", "gymnastics": "gymnastiek", "hockey": "hockey", "martialarts": "martialarts", "netball": "netbal", "pilates": "pilates", "pingpong": "pingpong", "running": "hardlopen", "skateboarding": "skateboarden", "skiing": "skiën", "snowboarding": "snowboarden", "surfing": "surfen", "swimming": "<PERSON><PERSON><PERSON><PERSON>", "tennis": "tennis", "volleyball": "volleybal", "weightlifting": "gewichtheffen", "yoga": "yoga", "scubadiving": "snorkelen", "hiking": "hiken", "capricorn": "steenbok", "aquarius": "waterman", "pisces": "vissen", "aries": "ram", "taurus": "stier", "gemini": "tweeling", "cancer": "kreeft", "leo": "leeuw", "virgo": "maagd", "libra": "weegschaal", "scorpio": "schor<PERSON><PERSON>", "sagittarius": "boogschutter", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "<PERSON><PERSON><PERSON>", "longtermrelationship": "langer<PERSON><PERSON>", "single": "alleenstaand", "polyamory": "polyamorie", "enm": "ethischnietmonogaam", "lgbt": "lhbt", "lgbtq": "lgbtq", "gay": "homoseksueel", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bi<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "heiligestraat", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "wacht<PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "zielherover", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "zonsondergangsnelheid", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildeoorlogen", "openworld": "openwereld", "heroesofthestorm": "heldenvandestorm", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "stam<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "plannenwereld", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kleurenvriend", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "rolspel", "witcher": "wetter", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modden", "charactercreation": "karaktercreatie", "immersive": "onderdompeling", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoudeschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidemotivaties", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "suckervoorliefde", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampierhetverkleedbal", "dimension20": "dimensie20", "gaslands": "gasonderland", "pathfinder": "<PERSON><PERSON><PERSON>", "pathfinder2ndedition": "padvinder2deeditie", "shadowrun": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "bloedopdekloktoren", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "zwaartekrachtgolven", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "oneshot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "oppermachtige", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "schu<PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "donkerstedungeon", "eclipsephase": "eclipsfase", "disgaea": "disgaea", "outerworlds": "buitenwerelden", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "nachtstad", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madnesscombat", "jaggedalliance2": "gekarteldealliantie2", "neverwinter": "nooitwinter", "road96": "weg96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "vergetenkoninkrijken", "dragonlance": "d<PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mokeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonwereld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "geb<PERSON>nt<PERSON>n", "horizonforbiddenwest": "horizonverbodenwest", "twewy": "twewy", "shadowpunk": "schaduwpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmysterie", "deltagreen": "deltagroen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON><PERSON>", "starfinder": "sterfinder", "goldensun": "goudenzon", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "bladesinthedark", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrood", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "gevallenorde", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "slechtescholen", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "duivelsurvivor", "oldschoolrunescape": "oudschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "boerderijrpg", "oldworldblues": "oudelandsblues", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rollenspellen", "roleplayinggames": "rollenspelletjes", "finalfantasy9": "finalfantasy9", "sunhaven": "zonnedorp", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "dagerfall", "torncity": "gescheurdestad", "myfarog": "<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "heiligeonderwereld", "chainedechoes": "kettingechos", "darksoul": "darksoul", "soulslikes": "soulslikes", "othercide": "anderevermoeding", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "zuilenvanlatenigheid", "palladiumrpg": "palladiumrpg", "rifts": "scheuren", "tibia": "tibia", "thedivision": "dedivisie", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfapocalyps", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "kinderen<PERSON><PERSON><PERSON>", "engineheart": "motorhart", "fable3": "fabel3", "fablethelostchapter": "fabelhetverlorenchapter", "hiveswap": "hiveswap", "rollenspiel": "rollenspel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "sterreveld", "oldschoolrevival": "oudschoolrevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savagewerelden", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "koninkrijkhart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "rpgspellen", "kingdomhearts": "koninkrijkharten", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "wildhearts", "bastion": "bastion", "drakarochdemoner": "drakarochdemonen", "skiesofarcadia": "luchtenvancaradia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennybloed", "breathoffire4": "ademvandevuur4", "mother3": "moeder3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "andereden", "roleplaygames": "rollenspelletjes", "roleplaygame": "rols<PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "heartsderooie", "harrypottergame": "harrypottergame", "pathfinderrpg": "padvinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampierlamazwartekat", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "jachtroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterwereld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "schaduwhartbond", "bladesoul": "bladziel", "baldursgate3": "baldursgate3", "kingdomcome": "koninkrijkkomend", "awplanet": "awplanet", "theworldendswithyou": "dewereldeindigmetjou", "dragalialost": "dragalialost", "elderscroll": "elders<PERSON>rollen", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "donker<PERSON><PERSON>gheid", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "aardemagie", "blackbook": "zwartboek", "skychildrenoflight": "hemelkinderenvangelicht", "gryrpg": "gryrpg", "sacredgoldedition": "heiligengoudeditie", "castlecrashers": "kasteel<PERSON><PERSON>", "gothicgame": "gothicgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "sterrails", "cityofmist": "stadvanmist", "indierpg": "indierpg", "pointandclick": "wijzenenklikken", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "ondoorbreekbaar", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "dodenwe<PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "monsterjager", "fireemblem": "v<PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosuperioriteit", "persona5": "persona5", "ghostoftsushima": "spookvantsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nieren", "dothack": "dothack", "ys": "ys", "souleater": "zielvre<PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "tactischrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "eeuwigesonate", "princessconnect": "prinsessenkonnect", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "kristallen", "vcs": "vcs", "pes": "pes", "pocketsage": "zakkenwijsheid", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "evoetbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "droomteam", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "overwatchleague", "cybersport": "cybersport", "crazyraccoon": "gekkeraccoon", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitief", "t3arena": "t3arena", "valorantbr": "valorantnl", "csgo": "csgo", "tf2": "tf2", "portal2": "poort2", "halflife": "halflife", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "klep", "portal": "portaal", "teamfortress2": "teamfortress2", "everlastingsummer": "e<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "geitenimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "vrijheidplaneet", "transformice": "transformice", "justshapesandbeats": "gewoonvormenengenoten", "battlefield4": "battlefield4", "nightinthewoods": "nachtindeweiden", "halflife2": "halflife2", "hacknslash": "hacke<PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risicoopregen2", "metroidvanias": "metroidvanias", "overcooked": "overcooked", "interplanetary": "interplanetaire", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "dodecellen", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwergenburcht", "foxhole": "vossenhol", "stray": "zwerf", "battlefield": "slagveld", "battlefield1": "slagveld1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "eyeb", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "tabletopsimulator", "partyhard": "feesthard", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "hades", "gunsmith": "wapenmaker", "okami": "<PERSON>ami", "trappedwithjester": "vastmetjester", "dinkum": "dinkum", "predecessor": "voorganger", "rainworld": "regenwereld", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonysim", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "<PERSON><PERSON><PERSON>onker", "motox": "motoxnl", "blackmesa": "blackmesa", "soulworker": "zielwerkers", "datingsims": "datinggames", "yaga": "yaga", "cubeescape": "cubeontsnapping", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nieuwestad", "citiesskylines": "stadsgezichten", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtuelekenopsia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bibliotheekvanruina", "l4d2": "l4d2", "thenonarygames": "denonarygames", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "kalmplasticente", "battlebit": "battlebit", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "belstad", "smileforme": "smilevoormij", "catnight": "ka<PERSON>av<PERSON>", "supermeatboy": "supervleesjongen", "tinnybunny": "tinnybunny", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "doem", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "grensland", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "verrecreep", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "aardverdedigingsleger", "huntshowdown": "jachtshowdown", "ghostrecon": "geestenjacht", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "komsealteam", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencyzandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "spie<PERSON><PERSON><PERSON>", "divisions2": "divisions2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "koudwarzombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "moderneoorlogvoering", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "grensgebieden", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "wereldvanoorlogsschepen", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6verovering", "xcom": "xcom", "hitman": "hittepiloot", "masseffect": "massaleffect", "systemshock": "systeemschok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "ho<PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "eeuwtijdschenen", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generatiezero", "enterthegungeon": "enterdegungeon", "jakanddaxter": "j<PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON>man", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "oorlogsgezicht", "crossfire": "crossfire", "atomicheart": "<PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampiersurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "schlagvelden", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "spelpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearzoonvavanvrijheid", "juegosfps": "fpsgames", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "bloedigezooi", "republiccommando": "republiekcommando", "elitedangerous": "elitedangerous", "soldat": "sold<PERSON>t", "groundbranch": "grondtak", "squad": "squad", "destiny1": "bestemming1", "gamingfps": "gamingfps", "redfall": "roodvallend", "pubggirl": "pubggirl", "worldoftanksblitz": "wereldvantanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "inges<PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "gepanzerdkern", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "betaaldag2", "cs16": "cs16", "pubgindonesia": "pubgindonesië", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "zeepcod", "ghostcod": "spookcod", "csplay": "csplay", "unrealtournament": "onrealtoernooi", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "aardschokkampioenen", "halo3": "halo3", "halo": "hallo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON>t", "remnant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "wereldvanoorlog", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "microvolts", "reddead": "rooddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "zeevanrovers", "rust": "rust", "conqueronline": "veroveronline", "dauntless": "ontzettendmoedig", "warships": "oorlogschepen", "dayofdragons": "dagvandragons", "warthunder": "oorlogsdonder", "flightrising": "vliegoprijs", "recroom": "recroom", "legendsofruneterra": "legendenvanruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "maidenlo<PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "wereldvantanks", "crossout": "<PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "tweedelevens", "aion": "aion", "toweroffantasy": "torenfant<PERSON><PERSON>", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "rooddeadonline", "superanimalroyale": "superdierenroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ridderonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "scum", "newworld": "nieuwewereld", "blackdesertonline": "blackdesertonline", "multiplayer": "meerderespelers", "pirate101": "pirate101", "honorofkings": "eervankoningen", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponystad", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "wereldvanwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "mobaparty", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "asfenvancreatie", "riotmmo": "schree<PERSON><PERSON><PERSON>o", "silkroad": "zijdenweg", "spiralknights": "spiraalknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "wraak", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "drakenprofet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "engelenonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoudrepubliek", "grandfantasia": "grotefantasie", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON>", "riseonline": "opstijgenonline", "corepunk": "corepunk", "adventurequestworlds": "avonturenzoekerwerelds", "flyforfun": "vliegenvoorkick", "animaljam": "<PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "koninkrijkvanellende", "cityofheroes": "<PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "straatvechter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "voor<PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "straatoverleg", "mkdeadlyalliance": "mkdodelijkealliantie", "nomoreheroes": "geenheldenmeer", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "deking<PERSON>vechters", "likeadragon": "likeeenrokende", "retrofightinggames": "retrofightgames", "blasphemous": "godslasterlijk", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "oorlogvanhemonsters", "jogosdeluta": "strijdspellen", "cyberbots": "cyberbots", "armoredwarriors": "gepantserdestrijders", "finalfight": "finalfight", "poweredgear": "krachtuit<PERSON>ing", "beatemup": "<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "vechtgames", "killerinstinct": "killerinstinct", "kingoffigthers": "koningvantekeningen", "ghostrunner": "ghostrunner", "chivalry2": "ridderlijkheid2", "demonssouls": "demonzielen", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightvervolg", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongnieuws", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutiontournament", "evomoment": "evomoment", "lollipopchainsaw": "lollipopkettingzaag", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "verhalenvanberseria", "bloodborne": "bloodborne", "horizon": "horizon", "pathofexile": "padvanexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "onbekend", "horizonzerodawn": "horizonzeroochtend", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationmaatjes", "ps1": "ps1", "oddworld": "vreemdewi<PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "godof<PERSON>", "gris": "grijs", "trove": "s<PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "totdeochtend", "touristtrophy": "toeristentrofee", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "vijfpd", "tekken7": "tekken7", "devilmaycry": "devilkanjijjanken", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "speelstation", "samuraiwarriors": "samuraiwarriors", "psvr2": "psvr2", "thelastguardian": "deletztewachtere", "soulblade": "zielzwaard", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2verbond", "pcsx2": "pcsx2", "lastguardian": "laatstewachter", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "feestbeest", "warharmmer40k": "oorlogshamer40k", "fightnightchampion": "vechtavondkampioen", "psychonauts": "psychonauten", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "deouderscrollsskyyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "deouderenrollen", "gxbox": "gxbox", "battlefront": "frontlinie", "dontstarvetogether": "nietsterfenmetznallen", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "sterrenbound", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxjes", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "koninkrijkenspel", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "afvaltelevisie", "skycotl": "skycotl", "erica": "erica", "ancestory": "voorouders", "cuphead": "cuphead", "littlemisfortune": "kleinetegenspoed", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterbal", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultvandeweidemis", "duckgame": "eendspel", "thestanleyparable": "thestanleyparabel", "towerunite": "torenvereniging", "occulto": "occulto", "longdrive": "langeafstand", "satisfactory": "tevreden", "pluviophile": "pluviophile", "underearth": "ondergrond", "assettocorsa": "assettocorsa", "geometrydash": "geometriestoten", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "donkerdome", "pizzatower": "pizzatoren", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "spel", "rockpaperscissors": "steenpapiersch<PERSON>", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "durf", "scavengerhunt": "speurt<PERSON>t", "yardgames": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "kieseengetal", "trueorfalse": "waarofniet", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "datinggames", "freegame": "gratis<PERSON>", "drinkinggames": "drinkenmetspelletjes", "sodoku": "sodoku", "juegos": "spellen", "mahjong": "mahjong", "jeux": "spelen", "simulationgames": "simulatiespellen", "wordgames": "woordspelletjes", "jeuxdemots": "woordenjungle", "juegosdepalabras": "woordspellen", "letsplayagame": "latenweeenspelletjespelen", "boredgames": "verveelspellen", "oyun": "boo", "interactivegames": "interactievegames", "amtgard": "amtgard", "staringcontests": "starenwedstrij<PERSON><PERSON><PERSON>", "spiele": "spelen", "giochi": "<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphonegames", "boogames": "boogames", "cranegame": "kra<PERSON>pel", "hideandseek": "verstoppertje", "hopscotch": "hink<PERSON><PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "yakuzaspellen", "classicgame": "klassiekspel", "mindgames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "raaddezing", "galagames": "galagames", "romancegame": "romantiespel", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "spraakverwarrers", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "arcadespelletjes", "tabletopgames": "tafelspellen", "metroidvania": "metroidvania", "games90": "games90", "idareyou": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "racegames", "ets2": "ets2", "realvsfake": "echtvsnep", "playgames": "spelletjesspelen", "gameonline": "gameonline", "onlinegames": "onlinegames", "jogosonline": "onlinegames", "writtenroleplay": "geschrevenrollenspel", "playaballgame": "speeljespelletje", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON>", "highscore": "highscore", "jeuxderôles": "rolspellet<PERSON>s", "burgergames": "burgerspellen", "kidsgames": "kinderspellen", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwzwarteeditie", "jeuconcour": "jeuconcour", "tcgplayer": "tcgspeler", "juegodepreguntas": "spelletjevragen", "gioco": "<PERSON><PERSON><PERSON>", "managementgame": "managementspel", "hiddenobjectgame": "verborgenobjectspel", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formule1spel", "citybuilder": "stadbouwer", "drdriving": "drdriving", "juegosarcade": "arcadespelletjes", "memorygames": "geheugenspellen", "vulkan": "vulkan", "actiongames": "actiegames", "blowgames": "blowgames", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON>", "oldgames": "oudegames", "couchcoop": "bankcoop", "perguntados": "gev<PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imessagegames", "idlegames": "zitspelletjes", "fillintheblank": "vulhetin", "jeuxpc": "pcgames", "rétrogaming": "rétrogaming", "logicgames": "logicaspellen", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurfen", "jeuxdecelebrite": "celebritygames", "exitgames": "uitgangsspelen", "5vs5": "5tegen5", "rolgame": "rolspel", "dashiegames": "dashiegames", "gameandkill": "gameneer", "traditionalgames": "<PERSON><PERSON>_spellen", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "tekstgebaseerdespellen", "gryparagrafowe": "grapigeparagrafen", "fantacalcio": "fantacalcio", "retrospel": "retrogames", "thiefgame": "die<PERSON><PERSON>", "lawngames": "grasgames", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "tafelvoetbal", "tischfußball": "tafelvoetbal", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "casualgames", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "ontsnappingspellen", "thiefgameseries": "diefspelseries", "cranegames": "kraanenspellen", "játék": "ookspel", "bordfodbold": "bordvoetbal", "jogosorte": "spellenmix", "mage": "magi", "cargames": "autospelletjes", "onlineplay": "onlinespelen", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "gamenachten", "pursebingos": "handtasbingo", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gamespc", "socialdeductiongames": "socialdeductiespellen", "dominos": "dominos", "domino": "domino", "isometricgames": "isometrischegames", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "speurt<PERSON><PERSON>", "jeuxvirtuel": "virtuelegames", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "gratisomtespel<PERSON>", "fantasygame": "fantasyspeel", "gryonline": "gryonline", "driftgame": "driftgame", "gamesotomes": "gamesometomes", "halotvseriesandgames": "halotvseriesengames", "mushroomoasis": "paddoasis", "anythingwithanengine": "allesmeteenmotor", "everywheregame": "overalspel", "swordandsorcery": "zwaardenmagie", "goodgamegiving": "goodgamege<PERSON>", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "grycomputerspelletjes", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "ritmespelletjes", "minaturegames": "miniatu<PERSON><PERSON>s", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "zelfliefdegaming", "gamemodding": "gamemodding", "crimegames": "misdaadspelletjes", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacerf", "charades": "charades", "singleplayer": "singleplayer", "coopgame": "coopgame", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "hoofdspel", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemischerfgoed", "camelup": "ka<PERSON><PERSON><PERSON>", "monopolygame": "monopolygame", "brettspiele": "b<PERSON><PERSON><PERSON><PERSON>", "bordspellen": "b<PERSON><PERSON><PERSON><PERSON>", "boardgame": "bordspel", "sällskapspel": "gezelschapsspel", "planszowe": "bordenplannen", "risiko": "risico", "permainanpapan": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "tafelspel", "baduk": "baduk", "bloodbowl": "bloedkomkomen", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "connectfour", "heroquest": "heldenstrijd", "giochidatavolo": "tafelspelletjes", "farkle": "farkle", "carrom": "carrom", "tablegames": "tafelspelletjes", "dicegames": "do<PERSON><PERSON>pel<PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "bordspellenfeest", "jocuridesocietate": "jocuridesocietate", "deskgames": "bureaugames", "alpharius": "alpharius", "masaoyunları": "masagames", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "cosmischeontmoeting", "creationludique": "crea<PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "tafelrollenspellen", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchbordspellen", "infinitythegame": "oneindigheidhetspel", "kingdomdeath": "koninkrijkdood", "yahtzee": "yahtzee", "chutesandladders": "gli<PERSON><PERSON><PERSON>ngenk<PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "tafelspel", "planszówki": "b<PERSON><PERSON><PERSON><PERSON>", "rednecklife": "achterwijkleven", "boardom": "boomoeite", "applestoapples": "appelsopappels", "jeudesociété": "jeudesociété", "gameboard": "spelbord", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "b<PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "paardenopoly", "deckbuilding": "deckbuilding", "mansionsofmadness": "h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "spelentafel", "shadowsofbrimstone": "schaduwenvanbrimstone", "kingoftokyo": "koningvantokyo", "warcaby": "warcaby", "táblajátékok": "tafelspelletjes", "battleship": "scheepsstrijd", "tickettoride": "biljettenomterijden", "deskovehry": "bureaugames", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "b<PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "b<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "b<PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "gaa<PERSON>aken", "weiqi": "weiqi", "jeuxdesocietes": "b<PERSON><PERSON><PERSON><PERSON>", "terraria": "terarijdagen", "dsmp": "dsmp", "warzone": "oorlogsgebied", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identiteitv", "theisle": "<PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyendemachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "onderonsje", "eco": "eco", "monkeyisland": "aapjesland", "valheim": "valheim", "planetcrafter": "planeetbouwer", "daysgone": "dagenzijnvoorbij", "fobia": "fobie", "witchit": "<PERSON><PERSON><PERSON><PERSON>", "pathologic": "pathologisch", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "geaard", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "boos<PERSON><PERSON>r", "dontstarve": "niethong<PERSON>n", "eternalreturn": "e<PERSON>wigterug", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "echterac", "thebackrooms": "<PERSON>achter<PERSON><PERSON>", "backrooms": "achterkamers", "empiressmp": "empiresmp", "blockstory": "blokverha<PERSON>", "thequarry": "degroeve", "tlou": "tlou", "dyinglight": "sterf<PERSON>t", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "wijgelukkigweinig", "riseofempires": "opkomstvankoninkrijken", "stateofsurvivalgame": "staatvanoverlevingsspel", "vintagestory": "vintagestory", "arksurvival": "arkoverleving", "barotrauma": "barotrauma", "breathedge": "g<PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "beastsofber<PERSON>da", "frostpunk": "frostpunk", "darkwood": "donkerhout", "survivalhorror": "overlevingsangst", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "nuttelozetrein", "lifeaftergame": "levennahetspel", "survivalgames": "overlevingsspellen", "sillenthill": "stilletalent", "thiswarofmine": "thiswarofmine", "scpfoundation": "scpfoundation", "greenproject": "groenproject", "kuon": "kuon", "cryoffear": "huilvandangst", "raft": "vlot", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "doodpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "oma", "littlenightmares2": "kleinenachtmerries2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "zonenvanhetwoud", "rustvideogame": "rustgame", "outlasttrials": "overlevingenvtrials", "alienisolation": "alienisolatie", "undawn": "undawn", "7day2die": "7dagenomteverliezen", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "overleven", "propnight": "propnacht", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "dodenvers", "cataclysmdarkdays": "cataclysmdonkere_dagen", "soma": "soma", "fearandhunger": "angsten<PERSON>er", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "levennaafter", "ageofdarkness": "ti<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "klokkentoren3", "aloneinthedark": "alleenindonker", "medievaldynasty": "middeleeuwsedynastie", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dwergenslayers", "warhammer40kcrush": "warhammer40kverliefd", "wh40": "wh40", "warhammer40klove": "warhammer40kliefde", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ilovesororitas", "ilovevindicare": "ikhouvanvindicare", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "boomurderjobs", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerleeftijdvansigmar", "civilizationv": "besch<PERSON><PERSON>", "ittakestwo": "hetkosttwee", "wingspan": "vleugelspanwijdte", "terraformingmars": "marsterraformen", "heroesofmightandmagic": "heldenvankrachtengenmagie", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "tijdvanklokken", "args": "args", "rime": "rijm", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "verbannen", "caesar3": "caesar3", "redalert": "rode<PERSON><PERSON>", "civilization6": "civilisatie6", "warcraft2": "warcraft2", "commandandconquer": "bevelenenoverwinnen", "warcraft3": "warcraft3", "eternalwar": "eeuwigeoorlog", "strategygames": "strategiespellen", "anno2070": "anno2070", "civilizationgame": "beschavinggame", "civilization4": "beschaving4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totaaloorlog", "travian": "travian", "forts": "forts", "goodcompany": "goedegezelschap", "civ": "civ", "homeworld": "th<PERSON>swer<PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "voordekoningen", "realtimestrategy": "echtetactiek", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "koninkrijktweekruinen", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "goddelijke状态", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesleukealge<PERSON><PERSON>", "plagueinc": "plagueinc", "theorycraft": "theoriecraft", "mesbg": "mesbg", "civilization3": "beschaving3", "4inarow": "4<PERSON><PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "helden3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "discipelen2", "plantsvszombies": "plantenvszombies", "giochidistrategia": "strategiegame", "stratejioyunları": "strategiegames", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "tijdvandewonderen", "dinosaurking": "dinoh<PERSON><PERSON>", "worldconquest": "wereldverovering", "heartsofiron4": "heartsofiron4", "companyofheroes": "companyofheroes", "battleforwesnoth": "strijdvoorwesnoth", "aoe3": "aoe3", "forgeofempires": "smidvanimperiums", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansgansemmer", "phobies": "fobieën", "phobiesgame": "fobieëntspel", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "buitenplaneet", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilisatie5", "victoria2": "victoria2", "crusaderkings": "kruisvaarderskoningen", "cultris2": "cultris2", "spellcraft": "toverkunst", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategieën", "popfulmail": "popfulmail", "shiningforce": "schitterkracht", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transporttycoon", "unrailed": "ontspoord", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "vluchtenuitdeellende", "uplandkingdoms": "uplandkoninkrijken", "galaxylife": "galaxieleven", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "battlekatten", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "desims1", "lossims4": "verlusthems4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON>back", "darkhorseanthology": "donkerpaardantologie", "phasmophobia": "fasmofobie", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "saiko", "fatalframe": "fataleafbeelding", "littlenightmares": "kleinetonennachten", "deadrising": "deadrising", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "deadisland", "litlemissfortune": "littlemisspech", "projectzero": "projectzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "hall<PERSON><PERSON><PERSON>", "helloneighbor2": "helloneighbor2", "gamingdbd": "gamedbd", "thecatlady": "dekattenmevrouw", "jeuxhorreur": "horrorgames", "horrorgaming": "gruwelgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardsagainsthumanity", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenamen", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON>art<PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitair", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "speelkaarten", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "ruilkaarten", "pokemoncards": "pokemonkaarten", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportkaarten", "cardfightvanguard": "kaartvechtenvanguard", "duellinks": "duellinks", "spades": "scho<PERSON>n", "warcry": "oorlogsroep", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "koningvandeharten", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "deresistentie", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkaarten", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspel", "darkmagician": "donkerdemagie", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgrechter", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommandant", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "gezichtskaart", "cardfight": "kaartvechten", "biriba": "biriba", "deckbuilders": "deckbouwers", "marvelchampions": "marvelk<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "magischekaarten", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "onzekereeenhoorns", "cyberse": "cyberse", "classicarcadegames": "classicarcadegames", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansspellen", "fridaynightfunkin": "vrijdagavondfun", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "guitarheld", "clonehero": "klonenheld", "justdance": "gewoonopschudden", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rocktotheekreinen", "chunithm": "chunithm", "idolmaster": "idoolmeester", "dancecentral": "danscentraal", "rhythmgamer": "ritmejgamer", "stepmania": "stepmania", "highscorerythmgames": "hoogscorerytmspellen", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "dansenvuurengeneve", "auditiononline": "auditiononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ritmespelletjes", "cryptofthenecrodancer": "cryptovandeknedanser", "rhythmdoctor": "ritmearts", "cubing": "<PERSON><PERSON><PERSON><PERSON>", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puzzlegames", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logischepuzzels", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "breinbrekers", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "kruiswoord", "motscroisés": "woordpuzzels", "krzyżówki": "woordpuzzels", "nonogram": "nonogram", "bookworm": "boekenworm", "jigsawpuzzles": "puzzelstukjes", "indovinello": "raadspel", "riddle": "raads<PERSON>", "riddles": "raadsels", "rompecabezas": "puzzel<PERSON><PERSON>", "tekateki": "tekateki", "inside": "binnen", "angrybirds": "boz<PERSON><PERSON><PERSON>", "escapesimulator": "ontsnappingsimulator", "minesweeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzzelenendragons", "crosswordpuzzles": "kruiswoordraadsels", "kurushi": "k<PERSON>hi", "gardenscapesgame": "tuinenspeelspel", "puzzlesport": "puzzelsport", "escaperoomgames": "escaperoomspellen", "escapegame": "ontsnappingspel", "3dpuzzle": "3dpuzzel", "homescapesgame": "homescapesgame", "wordsearch": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "puzzelen", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "raadseltales", "fishdom": "fishdom", "theimpossiblequiz": "deonmogelijkquiz", "candycrush": "snoepjesbasten", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3puzzel", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprin<PERSON><PERSON>", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "cubosderubik", "cruciverba": "kruiswoordpuzzels", "ciphers": "cijfers", "rätselwörter": "raadsels", "buscaminas": "zoekmijnen", "puzzlesolving": "puzzelen", "turnipboy": "knolletjesjongen", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "nobodys", "guessing": "raadseltje", "nonograms": "nonogrammen", "kostkirubika": "kostkirubika", "crypticcrosswords": "cryptischekruiswoordpuzzels", "syberia2": "syberia2", "puzzlehunt": "puzzeljacht", "puzzlehunts": "puzzeljachten", "catcrime": "kattenmisdaad", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "hoofdknobbels", "poptropica": "poptropica", "thelastcampfire": "hetlaatstkampvuur", "autodefinidos": "autodefined", "picopark": "picopark", "wandersong": "wanderdlied", "carto": "karton", "untitledgoosegame": "ontiteldganzenspel", "cassetête": "cassettevibes", "limbo": "limbo", "rubiks": "rubiks", "maze": "doolhof", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "snel<PERSON><PERSON>en", "pieces": "stuk<PERSON>s", "portalgame": "portalgame", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "puzzel<PERSON><PERSON>", "rubixcube": "rubixkubus", "indovinelli": "raa<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "verwrongenwonderland", "monopoly": "monopoly", "futurefight": "toekomststrijd", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "koc", "lonewolf": "eenzaamepad", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblesterren", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkonink<PERSON>jk", "alchemystars": "alchemystars", "stateofsurvival": "overlevingsstaat", "mycity": "<PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "kleurigpodium", "bloonstowerdefense": "bloontorenverdediging", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "ridderloop", "fireemblemheroes": "vuurtekenhelden", "honkaiimpact": "honkaiimpact", "soccerbattle": "voetbalstrijd", "a3": "a3", "phonegames": "telefoonspellen", "kingschoice": "kiesvanadams", "guardiantales": "beschermerverhalen", "petrolhead": "petrolhead", "tacticool": "tacticoole", "cookierun": "koekjesloop", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "buitenhetcircuit", "craftsman": "v<PERSON>man", "supersus": "supersus", "slowdrive": "lang<PERSON><PERSON><PERSON>", "headsup": "kijkuit", "wordfeud": "woordvechten", "bedwars": "bedoorlogs", "freefire": "freefire", "mobilegaming": "mobielegaming", "lilysgarden": "lilystuin", "farmville2": "farmville2", "animalcrossing": "dierenoversteek", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "dearcan<PERSON>", "8ballpool": "8ballpool", "emergencyhq": "noodhulpcentrale", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hooidag", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON>dget", "ml": "ml", "bangdream": "bangdreamnl", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "tijdp<PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "zakliefde", "androidgames": "androidgames", "criminalcase": "crim<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "kokenverrukt", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leagueofangels", "lordsmobile": "lordsmobile", "tinybirdgarden": "kleinevo<PERSON><PERSON>uin", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mijnzingendemonstrum", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "blau<PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "oorlogsrobots", "mirrorverse": "mirrorverse", "pou": "pou", "warwings": "oorlogsvoordelen", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiwalgelijk", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "tegenaanbijten", "mpl": "mpl", "coinmaster": "munt<PERSON><PERSON>", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "gamevansultanen", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "runcitygame", "juegodemovil": "mobielspel", "avakinlife": "avakinleven", "kogama": "kogama", "mimicry": "nadoen", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "achtbaantycoon", "grandchase": "g<PERSON>jacht", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "r<PERSON>pvandraken", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "schaduwgevecht3", "limbuscompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "sloopderby3", "wordswithfriends2": "woordenmetvrienden2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purr<PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "oogststad", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "empiresenpuzzels", "empirespuzzles": "empirepuzzels", "dragoncity": "<PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "kleinelitteken", "aethergazer": "aether<PERSON><PERSON><PERSON>", "mudrunner": "mod<PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "tranenvoordehimiss", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "kokenmet<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "straatvechtersduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "girlsfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "ziel<PERSON><PERSON><PERSON>", "gettingoverit": "erger<PERSON><PERSON>n", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracenonline", "jogosmobile": "mobielegames", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "tijdverkenners", "gamingmobile": "gamingmobiel", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "zoektocht", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "travellerttrpg", "2300ad": "2300<PERSON><PERSON><PERSON>us", "larp": "larp", "romanceclub": "romanceclub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON>d", "pokemongo": "pokemongo", "pokemonshowdown": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chat<PERSON>ll", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonpaars", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furrat", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonsters", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonknuffels", "teamystic": "teamystic", "pokeball": "pokebal", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "glanzendepokemon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "ijzerenhanden", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokémonslaap", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "pokémonslaap", "kidsandpokemon": "kinderenen<PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "shinyhunter", "ajedrez": "schaak", "catur": "<PERSON>ur", "xadrez": "schaken", "scacchi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "wereldflits", "jeudéchecs": "jeudéchecs", "japanesechess": "japansechess", "chinesechess": "chinesechess", "chesscanada": "scha<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "openingen", "rook": "rook<PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "calabozensyndragons", "dungeonsanddragon": "dungeonsanddragons", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "donsjonenspelletjes", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "delegendevanvoxmachina", "doungenoanddragons": "dungeonsendragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftkampioenschap", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "droomsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftzakelijk", "minecraft360": "minecraft360", "moddedminecraft": "gemoddeminecraft", "minecraftps4": "mincraftps4", "minecraftpc": "minecraftpc", "betweenlands": "tussen<PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftstad", "pcgamer": "pcgamer", "jeuxvideo": "videogames", "gambit": "gambiet", "gamers": "gamers", "levelup": "niveauverhogen", "gamermobile": "gamermobiel", "gameover": "gameover", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "spelenmetboo", "pcgames": "pcgames", "casualgaming": "casualgaming", "gamingsetup": "gamingopstelling", "pcmasterrace": "pcmasterrace", "pcgame": "pcgaming", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "consoleplayer", "boxi": "boxi", "pro": "pro", "epicgamers": "epischegamers", "onlinegaming": "onlinegamen", "semigamer": "semigamer", "gamergirls": "gamergirls", "gamermoms": "gamermoms", "gamerguy": "gamerjongen", "gamewatcher": "gamewatcher", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamhardwerken", "mallugaming": "mallugaming", "pawgers": "p<PERSON><PERSON><PERSON>", "quests": "quests", "alax": "alax", "avgn": "gemid<PERSON><PERSON>", "oldgamer": "oudergamers", "cozygaming": "gezelliggamemen", "gamelpay": "gamelpay", "juegosdepc": "pcgames", "dsswitch": "dsswitch", "competitivegaming": "competitievegaming", "minecraftnewjersey": "minecraftnewjersey", "faker": "faker", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksueelgamend", "gamepc": "gamepc", "girlsgamer": "me<PERSON>jes<PERSON>r", "fnfmods": "fnfmods", "dailyquest": "dagelijksequest", "gamegirl": "gamegirl", "chicasgamer": "chicasgamer", "gamesetup": "gamesetup", "overpowered": "overpowered", "socialgamer": "socia<PERSON>peler", "gamejam": "gamejam", "proplayer": "prospeler", "roleplayer": "rollenspelers", "myteam": "<PERSON>jn<PERSON><PERSON>", "republicofgamers": "republiekvangamers", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "drievoudigelegende", "gamerbuddies": "gamermaatjes", "butuhcewekgamers": "needchickgamers", "christiangamer": "christ<PERSON>jkegamers", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "casualgamer", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "gamekijken", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "videogamer", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gezondegamer", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "womangamer", "obviouslyimagamer": "duidelijkikbenengamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forager", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nulontsnapping", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuziek", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON>fst<PERSON><PERSON>", "switch": "wisse<PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "luchtkinderenvandedonker", "tomodachilife": "tomodachileven", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tranenvanhetkoninkrijk", "walkingsimulators": "wandeloefeningen", "nintendogames": "nintendogames", "thelegendofzelda": "delegendenvanzelda", "dragonquest": "drakenzoektocht", "harvestmoon": "<PERSON>og<PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mijn<PERSON><PERSON>dped<PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON><PERSON><PERSON>", "earthbound": "aardegebonden", "tales": "ver<PERSON>n", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossingnl", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "driehoekstrategie", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kastanjebadhaardag", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioensonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "<PERSON><PERSON><PERSON>", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "rodekanids", "vanillalol": "vanillalol", "wildriftph": "wildriftbe", "lolph": "lolph", "leagueoflegend": "leagueoflegenden", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsnederland", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadeslegendes", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrospelletjes", "scaryvideogames": "engespelletjes", "videogamemaker": "videogamebouwer", "megamanzero": "megamanzero", "videogame": "videospel", "videosgame": "videospelletjes", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arcades", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "boerde<PERSON>jsimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxduitsland", "robloxdeutsch": "robloxduits", "erlc": "erlc", "sanboxgames": "sandboxspellen", "videogamelore": "videogamelore", "rollerdrome": "rollerdrome", "parasiteeve": "parasietenfeest", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "droomland", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "dood<PERSON>imte", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videogames", "theoldrepublic": "deouderepubliek", "videospiele": "videospellen", "touhouproject": "touhouproject", "dreamcast": "droomcast", "adventuregames": "avonturenspellen", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retrogames", "retroarcade": "retroarcade", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "command<PERSON>en", "bugsnax": "bugsnax", "injustice2": "onrecht2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "luchtspel", "zenlife": "zenleven", "beatmaniaiidx": "beatmaniaiidx", "steep": "steil", "mystgames": "mystgames", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "consolegaming", "konsolen": "consoles", "outrun": "<PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaming<PERSON>reur", "monstergirlquest": "monstergirlquest", "supergiant": "superreus", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "farmingsims", "juegosviejos": "oudegames", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interactieveverhalen", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "vis<PERSON><PERSON><PERSON><PERSON>", "visualnovels": "visualnovels", "rgg": "rgg", "shadowolf": "s<PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "betaaldag", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "schemerprinses", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "zandbak", "aestheticgames": "esthetischegames", "novelavisual": "<PERSON><PERSON><PERSON><PERSON>", "thecrew2": "decrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "godhand", "leafblowerrevolution": "bladblazerrevolutie", "wiiu": "wiiu", "leveldesign": "levelontwerp", "starrail": "sterrenrail", "keyblade": "sle<PERSON>lswaard", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsoms", "novelasvisuales": "vis<PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxnederland", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospelletjes", "videogamedates": "videogamedates", "mycandylove": "mijnsnoepje", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "terugvanrekening", "gamstergaming": "gamstergaming", "dayofthetantacle": "dagvandetentakel", "maniacmansion": "maniakemansion", "crashracing": "crashracing", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oudeschoolgaming", "hellblade": "hellblade", "storygames": "verhaalspellen", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "beyondtwosouls", "gameuse": "gamegebruiker", "offmortisghost": "afgekeurdegeest", "tinybunny": "kleinkonijntje", "retroarch": "retroarch", "powerup": "krachtop", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafischeavonturen", "quickflash": "snelflits", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "woestenij", "powerwashsim": "powerwashsim", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animeoorlogfans2", "footballfusion": "voetbalfusie", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "stapelvanverborgenheid", "simulator": "simulator", "symulatory": "simuleren", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobotgevecht", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "vriendjeskerker", "toontownrewritten": "toontownheruitgeschreven", "simracing": "simracen", "simrace": "simracen", "pvp": "pvp", "urbanchaos": "stedelijkchaos", "heavenlybodies": "hemelselichamen", "seum": "seum", "partyvideogames": "feestvideogames", "graveyardkeeper": "gravenbewaker", "spaceflightsimulator": "ruimtereisimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "ha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "etenenvideogames", "oyunvideoları": "gametutorials", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "truckingsimulator", "horizonworlds": "horizonwerelden", "handygame": "handygame", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "oudeklassiekergames", "racingsimulator": "raceparcours", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentenvanhetverkloek", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "poortenvanolympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON>", "rebelstar": "rebelsster", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "indievideogames", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffortress", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projectl", "futureclubgames": "toekomstclubspellen", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "openingenwetenschap", "backlog": "achterstand", "gamebacklog": "gameachterstand", "gamingbacklog": "gamingachterstand", "personnagejeuxvidéos": "personagevideogames", "achievementhunter": "<PERSON>sta<PERSON><PERSON><PERSON>", "cityskylines": "stadsgezichten", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "ondeugendhond", "beastlord": "beestheer", "juegosretro": "retrospelletjes", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stan<PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamine", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragensync", "vivapiñata": "vivalapina<PERSON>", "ilovekofxv": "ikhouvankofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracen", "berserk": "woest", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "verdrietigeanime", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animescaling", "animewithplot": "animemetverhaal", "pesci": "visjes", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseizoen1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "animecover", "thevisionofescaflowne": "devisievanescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "animejaren90", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "v<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartydepatriot", "futurediary": "toekomstdagboek", "fairytail": "sp<PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "zeemeermelodie", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "carnaval", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "zwartelaanse", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "bepaaldebetoverendeindex", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8deinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioriteit", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "vuistvanhetnoorden", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "torenvangod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON>lemaanwosagas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "liefenengrepig", "martialpeak": "martialpeak", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "highscoremeisje", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwee", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstermeisje", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "zeemanpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "fruitsbasket", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "hetbelovendeland", "monstermanga": "monstermanga", "yourlieinapril": "jouwleugenininapril", "buggytheclown": "buggydeclown", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "diepzeegevangene", "jojolion": "jojo<PERSON>", "deadmanwonderland": "dodenmanwonderland", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "voedseloorlogen", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "tejourewarenheid", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blauweperiode", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "geheimealliantie", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "ver<PERSON><PERSON><PERSON>d", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampiervriend", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spionnenfamilie", "airgear": "luchtwielen", "magicalgirl": "magischemeis<PERSON>", "thesevendeadlysins": "dezezevendedodelijkezonden", "prisonschool": "gevangenisschool", "thegodofhighschool": "degodvanhetenhogeeschool", "kissxsis": "<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "mijnb<PERSON>emenspeler", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozenmeisje", "animeuniverse": "animeuniversum", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "eerstestap", "undeadunluck": "ondoodgeluk", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinie", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslachtertotdeswaard", "bloodlad": "bloedmaat", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "vuist<PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "ster<PERSON><PERSON>ang<PERSON><PERSON>", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "<PERSON>ui<PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "middelbareschoolvandedoden", "germantechno": "duitsetechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "dodenparade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanseanime", "animespace": "animespace", "girlsundpanzer": "meisjesundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "peachgirl": "perzikmeisje", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechameisjes", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "hartvanmanga", "deliciousindungeon": "lekkerindedungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "overgeared", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "heksenhoedatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatzonnelion", "kamen": "kamen", "mangaislife": "mangaishetleven", "dropsofgod": "dropsofgod", "loscaballerosdelzodia": "dejongetjesvanzodiac", "animeshojo": "animeshojo", "reverseharem": "reverseharem", "saintsaeya": "<PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "geweldigejuffrouwonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "mijnbaasdieet", "gear5": "gear5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "bloedplus", "bloodplusanime": "bloedplusanime", "bloodcanime": "blo<PERSON>ni<PERSON>", "bloodc": "bloedc", "talesofdemonsandgods": "ver<PERSON>nvandeemonene<PERSON>den", "goreanime": "goreanime", "animegirls": "animegirls", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxworst", "splatteranime": "spetteranime", "splatter": "spetteren", "risingoftheshieldhero": "deopkomstvandeheldmethetschild", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "leugenaarleugenaar", "supercampeones": "superkampioenen", "animeidols": "animeidolen", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magische<PERSON><PERSON><PERSON><PERSON>", "callofthenight": "<PERSON>roep<PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganvechters", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON>", "princessjellyfish": "prinsessjellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuesterrenlicht", "animeverse": "animeversum", "persocoms": "persocoms", "omniscientreadersview": "alziendlezersperspectief", "animecat": "animekat", "animerecommendations": "animaanbevelingen", "openinganime": "<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mijnjongerenromantischekomedie", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "gigantischerobots", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobieleuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militairanime", "greenranger": "groeneranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animestad", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonavontuur", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "mijnheldenacademie", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "aanvalopdetitanen", "erenyeager": "erenzeiker", "myheroacademia": "mijnheldenacademie", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "enquêtecorps", "onepieceanime": "onepieceanime", "attaquedestitans": "aanvallenopdestitans", "theonepieceisreal": "deonepieceisreal", "revengers": "verklikkers", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffect", "digimonstory": "digimonverhaal", "digimontamers": "digimontamers", "superjail": "supergevangenis", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "perfectewwebtoon", "kemonofriends": "kemonovrienden", "utanoprincesama": "utanoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "vliegendeheks", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "gewoonomdat", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "levensverhalen"}