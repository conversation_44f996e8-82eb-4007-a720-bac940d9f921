{"2048": "2048", "mbti": "mbti", "enneagram": "енеаґрама", "astrology": "астрологія", "cognitivefunctions": "когнітивніфункції", "psychology": "психологія", "philosophy": "філософія", "history": "історія", "physics": "фізика", "science": "наука", "culture": "культура", "languages": "мови", "technology": "технології", "memes": "меми", "mbtimemes": "mbtiмеми", "astrologymemes": "астрологіямеми", "enneagrammemes": "енеаґрамамеми", "showerthoughts": "думкивду<PERSON>і", "funny": "смішне", "videos": "відео", "gadgets": "гаджети", "politics": "політика", "relationshipadvice": "порадищодостосунків", "lifeadvice": "порадидляжиття", "crypto": "крипто", "news": "новини", "worldnews": "світовіновини", "archaeology": "археологія", "learning": "навчання", "debates": "дебати", "conspiracytheories": "теоріїзмови", "universe": "всесвіт", "meditation": "медитація", "mythology": "міфологія", "art": "мистецтво", "crafts": "ремесла", "dance": "танці", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "мак<PERSON><PERSON><PERSON>", "beauty": "краса", "fashion": "мода", "singing": "спів", "writing": "письменництво", "photography": "фотографія", "cosplay": "косплей", "painting": "малювання", "drawing": "малювання", "books": "книги", "movies": "фільми", "poetry": "поезія", "television": "телебачення", "filmmaking": "кінорежисура", "animation": "анімація", "anime": "аніме", "scifi": "науковафантастика", "fantasy": "фентезі", "documentaries": "документальніфільми", "mystery": "таємничіісторії", "comedy": "комедія", "crime": "детективи", "drama": "драма", "bollywood": "боллівуд", "kdrama": "кдрама", "horror": "жа<PERSON>и", "romance": "романтика", "realitytv": "реаліт<PERSON><PERSON><PERSON>у", "action": "екшн", "music": "музика", "blues": "блюз", "classical": "кла<PERSON><PERSON>чнамузика", "country": "каунтрі", "desi": "десі", "edm": "edm", "electronic": "електроннамузика", "folk": "фолк", "funk": "фанк", "hiphop": "хі<PERSON>хоп", "house": "<PERSON><PERSON><PERSON><PERSON>", "indie": "інді", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "кейпоп", "latin": "латинська", "metal": "метал", "pop": "поп", "punk": "панк", "rnb": "rnb", "rap": "реп", "reggae": "регі", "rock": "рок", "techno": "техно", "travel": "подорожі", "concerts": "концерти", "festivals": "фестивалі", "museums": "музе<PERSON>", "standup": "стендап", "theater": "театр", "outdoors": "навідкритомуповітрі", "gardening": "садівництво", "partying": "вечірки", "gaming": "ігри", "boardgames": "настільніігри", "dungeonsanddragons": "dungeonsanddragons", "chess": "шахи", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "покемони", "food": "їжа", "baking": "випікання", "cooking": "кулінарія", "vegetarian": "вегетаріанське", "vegan": "веганське", "birds": "птахи", "cats": "коти", "dogs": "собаки", "fish": "рибки", "animals": "тварини", "blacklivesmatter": "blacklivesmatter", "environmentalism": "екологізм", "feminism": "фемінізм", "humanrights": "правалюдини", "lgbtqally": "лгбтсоюзник", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "транссоюзник", "volunteering": "волонтерство", "sports": "спорт", "badminton": "бад<PERSON><PERSON>нтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велоспорт", "fitness": "фітнес", "football": "футбол", "golf": "ґольф", "gym": "спортзал", "gymnastics": "гімнастика", "hockey": "хокей", "martialarts": "бойовімистецтва", "netball": "нетбол", "pilates": "пілатес", "pingpong": "пінпонг", "running": "біг", "skateboarding": "скейтбординг", "skiing": "лижнийспорт", "snowboarding": "сноубординг", "surfing": "серфінг", "swimming": "плавання", "tennis": "теніс", "volleyball": "волейбол", "weightlifting": "важкаатлетика", "yoga": "йога", "scubadiving": "пірнаннязаквалангом", "hiking": "пішохіднийтуризм", "capricorn": "козеріг", "aquarius": "водолій", "pisces": "риби", "aries": "овен", "taurus": "телець", "gemini": "близнюки", "cancer": "рак", "leo": "лев", "virgo": "діва", "libra": "терези", "scorpio": "скорпіон", "sagittarius": "стрілець", "shortterm": "короткостроковi", "casual": "невимушений", "longtermrelationship": "довгостроковістосунки", "single": "самотня", "polyamory": "поліаморія", "enm": "енм", "lgbt": "лгбт", "lgbtq": "лгбтq", "gay": "гей", "lesbian": "лесбійка", "bisexual": "бісексуал", "pansexual": "пансексуал", "asexual": "асексуальний", "reddeadredemption2": "reddeadredemption2", "dragonage": "драгонейдж", "assassinscreed": "асасинскрид", "saintsrow": "святевулиця", "danganronpa": "данганронпа", "deltarune": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "watchdogs": "<PERSON>ран<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "дисліт", "rougelikes": "ругелайки", "kingsquest": "королівськаподорож", "soulreaver": "душерозриватель", "suikoden": "суйкоден", "subverse": "сабверс", "legendofspyro": "легендаспайро", "rouguelikes": "ругайки", "syberia": "сибір", "rdr2": "рдар2", "spyrothedragon": "спайротедрагон", "dragonsdogma": "драконидопомагають", "sunsetoverdrive": "заходеньсонця", "arkham": "арк<PERSON>ем", "deusex": "девсекс", "fireemblemfates": "вогнянийемблемафатів", "yokaiwatch": "йокайвwatch", "rocksteady": "рокстеді", "litrpg": "літрпг", "haloinfinite": "haloinfinite", "guildwars": "гутерськівійни", "openworld": "відкритий<PERSON>в<PERSON>т", "heroesofthestorm": "героївшторму", "cytus": "цитус", "soulslike": "душепод<PERSON>бні", "dungeoncrawling": "підземелля", "jetsetradio": "джетсетрадіо", "tribesofmidgard": "племенамідґарда", "planescape": "планетнийпростір", "lordsoftherealm2": "lordsrealm2", "baldursgate": "балдаурськіворота", "colorvore": "кольоровор", "medabots": "медаботи", "lodsoftherealm2": "лігомрії2", "patfofexile": "патфофексайл", "immersivesims": "іммерсивнісими", "okage": "окейдж", "juegoderol": "рольоваігра", "witcher": "відьмак", "dishonored": "обдурений", "eldenring": "елденринг", "darksouls": "темнідуші", "kotor": "котрий", "wynncraft": "вінкрафт", "witcher3": "відьмак3", "fallout": "випадіння", "fallout3": "фоллаут3", "fallout4": "фоллаут4", "skyrim": "скайрим", "elderscrolls": "elderscrolls", "modding": "моддинг", "charactercreation": "створенняперсонажів", "immersive": "занурення", "falloutnewvegas": "фоллаутновийвегас", "bioshock": "біошок", "omori": "оморі", "finalfantasyoldschool": "старошколафиналфентезі", "ffvii": "ffvii", "ff6": "фф6", "finalfantasy": "фінальнафантазія", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "фіналфентезіxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "фф13", "finalfantasymatoya": "матоявфіналфантазії", "lalafell": "лала<PERSON>елл", "dissidia": "дисидія", "finalfantasy7": "фінальнафантезія7", "ff7": "фінал7", "morbidmotivation": "мертвінадбання", "finalfantasyvii": "финалфантазіяvii", "ff8": "ff8", "otome": "отоме", "suckerforlove": "залипнувнуніжність", "otomegames": "отомегри", "stardew": "стардю", "stardewvalley": "стардювлівлёді", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "йік<PERSON>рп<PERSON>", "vampirethemasquerade": "вампірськіпартії", "dimension20": "dimension20", "gaslands": "газленди", "pathfinder": "мандрівник", "pathfinder2ndedition": "шляховик2другевидання", "shadowrun": "тіньов<PERSON>б<PERSON>г", "bloodontheclocktower": "кровнагодиннику", "finalfantasy15": "finalfantasy15", "finalfantasy11": "фінальнафантазія11", "finalfantasy8": "фінальнафантазія8", "ffxvi": "ffxvi", "lovenikki": "люблюнікі", "drakengard": "дра<PERSON><PERSON><PERSON>га<PERSON>д", "gravityrush": "гравіт<PERSON><PERSON><PERSON><PERSON>нийбум", "rpg": "рпг", "dota2": "дота2", "xenoblade": "ксеноблейд", "oneshot": "однимкадром", "rpgmaker": "rpgmaker", "osrs": "осрс", "overlord": "повелитель", "yourturntodie": "вашчергадомирати", "persona3": "персона3", "rpghorror": "рпгжахи", "elderscrollsonline": "старшіскроллионлайн", "reka": "ріка", "honkai": "хонкай", "marauders": "мародери", "shinmegamitensei": "шінмегамітенсей", "epicseven": "епіксевен", "rpgtext": "текстрпг", "genshin": "ген<PERSON><PERSON>н", "eso": "есо", "diablo2": "діабло2", "diablo2lod": "діабло2лод", "morrowind": "моровінд", "starwarskotor": "зорянівійникотор", "demonsouls": "демонду<PERSON>і", "mu": "му", "falloutshelter": "притулокдлявипадків", "gurps": "gурпс", "darkestdungeon": "найтемнішепідземелля", "eclipsephase": "епохазатемнення", "disgaea": "ди<PERSON><PERSON><PERSON><PERSON>", "outerworlds": "зовнішнісвіти", "arpg": "арпг", "crpg": "крпг", "bindingofisaac": "звязокісаяка", "diabloimmortal": "діаблоіммортал", "dynastywarriors": "іграпрестолів", "skullgirls": "черепашки", "nightcity": "нічнемісто", "hogwartslegacy": "спадщинагогвартсу", "madnesscombat": "божевільнабитва", "jaggedalliance2": "доскиданс2", "neverwinter": "невервінтер", "road96": "road96", "vtmb": "втмб", "chimeraland": "химерназемля", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "рогалики", "gothamknights": "gothamлицар<PERSON>", "forgottenrealms": "забутіцарства", "dragonlance": "драконячиймолотовий", "arenaofvalor": "аренацінностей", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "тунтаун", "childoflight": "дитина<PERSON>вітла", "aq3d": "aq3d", "mogeko": "могеко", "thedivision2": "thedivision2", "lineage2": "лінія2", "digimonworld": "світдидимонів", "monsterrancher": "монстрранчер", "ecopunk": "екопанк", "vermintide2": "вермінтайд2", "xeno": "ксенос", "vulcanverse": "вулканверс", "fracturedthrones": "пошкодженіпрестоли", "horizonforbiddenwest": "горизонтзабороненогозахіду", "twewy": "твейві", "shadowpunk": "тіньовийпанк", "finalfantasyxv": "фінальнафантазіясв", "everoasis": "еверо<PERSON><PERSON><PERSON>с", "hogwartmystery": "гогвартсмагія", "deltagreen": "делатайзеленим", "diablo": "діа<PERSON>ло", "diablo3": "діабло3", "diablo4": "діабло4", "smite": "знищити", "lastepoch": "останняепоха", "starfinder": "зіркогон", "goldensun": "золотесонце", "divinityoriginalsin": "божестворіднийгріх", "bladesinthedark": "лезвяحققрівніть", "twilight2000": "twilight2000", "sandevistan": "сандевістан", "cyberpunk": "сайберпанк", "cyberpunk2077": "кіберпанк2077", "cyberpunkred": "кіберпанкчервоний", "dragonballxenoverse2": "драгонболксеноверс2", "fallenorder": "падінняпорядку", "finalfantasyxii": "фінальнафантазія12", "evillands": "злоявища", "genshinimact": "ген<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "aethyr": "ейтер", "devilsurvivor": "дияв<PERSON><PERSON><PERSON>и<PERSON><PERSON>", "oldschoolrunescape": "старийшколярунескейп", "finalfantasy10": "фіналфентезі10", "anime5e": "аніме5е", "divinity": "божественність", "pf2": "пф2", "farmrpg": "фермеррпг", "oldworldblues": "старосвітнідепресії", "adventurequest": "пригодамісія", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "рольовіігри", "roleplayinggames": "рольовіігри", "finalfantasy9": "фінальнафантазія9", "sunhaven": "сонячнеспівжиття", "talesofsymphonia": "історіїсімфонії", "honkaistarrail": "хонкаіста<PERSON><PERSON><PERSON><PERSON><PERSON>л", "wolong": "вологонг", "finalfantasy13": "финалфэнтези13", "daggerfall": "дагерфол", "torncity": "гнилушаремісто", "myfarog": "м<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>г", "sacredunderworld": "сакре́днийпідземнийсвіт", "chainedechoes": "ланцюговіехи", "darksoul": "темнадуша", "soulslikes": "душепод<PERSON>бні", "othercide": "іншосмерть", "mountandblade": "гориталезо", "inazumaeleven": "інадзумавлівен", "acvalhalla": "аквалгалла", "chronotrigger": "хронозапуск", "pillarsofeternity": "стовпиетernity", "palladiumrpg": "палади<PERSON><PERSON><PERSON>г", "rifts": "розриви", "tibia": "тібіа", "thedivision": "тактика", "hellocharlotte": "привітщарлотто", "legendofdragoon": "легендапонедрагона", "xenobladechronicles2": "ксеноблейдхронік2", "vampirolamascarada": "вампіроламаскарад", "octopathtraveler": "октапатподорожуючий", "afkarena": "афкарена", "werewolftheapocalypse": "вовчапостапокаліпсис", "aveyond": "авейонд", "littlewood": "літлвуд", "childrenofmorta": "дітиморта", "engineheart": "двигунсерця", "fable3": "казка3", "fablethelostchapter": "казкавтінійглаві", "hiveswap": "хайвсвап", "rollenspiel": "рольовагра", "harpg": "гарпг", "baldursgates": "бальдурівворота", "edeneternal": "еденеостанні", "finalfantasy16": "финальнафантазія16", "andyandleyley": "ендітайлейлі", "ff15": "ff15", "starfield": "зорянеполе", "oldschoolrevival": "ретроаніміція", "finalfantasy12": "фінальнафантазія12", "ff12": "фф12", "morkborg": "моркборг", "savageworlds": "savageworlds", "diabloiv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pve": "пве", "kingdomheart1": "королівськесерце1", "ff9": "ff9", "kingdomheart2": "королівствосердець2", "darknessdungeon": "темницятемряви", "juegosrpg": "ігриrpg", "kingdomhearts": "королівськісерця", "kingdomheart3": "королівствосердець3", "finalfantasy6": "фінальнафантазія6", "ffvi": "ffvi", "clanmalkavian": "кланмалькавіан", "harvestella": "урожайсталла", "gloomhaven": "глумхейвен", "wildhearts": "дикісерця", "bastion": "бастіон", "drakarochdemoner": "дракарочдемонер", "skiesofarcadia": "небаділяркадиї", "shadowhearts": "тінісердець", "nierreplicant": "ніерреплікант", "gnosia": "гнозія", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "диханнявогню4", "mother3": "мама3", "cyberpunk2020": "кібервперед2020", "falloutbos": "фоллаутбос", "anothereden": "іншаедена", "roleplaygames": "ігор<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>і", "roleplaygame": "рольоваігра", "fabulaultima": "фабулаультима", "witchsheart": "серцевідьми", "harrypottergame": "гарріпоттергра", "pathfinderrpg": "провідникrpg", "pathfinder2e": "шляхопрокладач2е", "vampirilamasquerade": "вампірськеперевтілення", "dračák": "багатоекспериментів", "spelljammer": "спеллджаммер", "dragonageorigins": "драгонейджпоходження", "chronocross": "хронокрос", "cocttrpg": "сокттрпг", "huntroyale": "полюваннянакоролеву", "albertodyssey": "альбертодисея", "monsterhunterworld": "полюваннянамонстрів", "bg3": "бг3", "xenogear": "ксеногер", "temtem": "темтем", "rpgforum": "rpgфорум", "shadowheartscovenant": "клятватінісердець", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "балдурсгейт3", "kingdomcome": "царствокому", "awplanet": "awplanet", "theworldendswithyou": "світзакінчуєтьсясягода", "dragalialost": "драгалялост", "elderscroll": "скроллистарі", "dyinglight2": "боярсвіт2", "finalfantasytactics": "фінальнафантазіятактики", "grandia": "грандiя", "darkheresy": "темнаєресь", "shoptitans": "шоптитани", "forumrpg": "форумрпг", "golarion": "галар<PERSON><PERSON>н", "earthmagic": "земнамагія", "blackbook": "чорнакнига", "skychildrenoflight": "небеснідітисвітла", "gryrpg": "грирпг", "sacredgoldedition": "сакральнезолотевидання", "castlecrashers": "замокрушителі", "gothicgame": "gothicгра", "scarletnexus": "скарлетнексус", "ghostwiretokyo": "привидативудетокіо", "fallout2d20": "fallout2d20", "gamingrpg": "геймерськірпг", "prophunt": "пропхант", "starrails": "зірковіколії", "cityofmist": "місто<PERSON><PERSON><PERSON>й", "indierpg": "індір<PERSON>г", "pointandclick": "точкитаклик", "emilyisawaytoo": "емілійзабраласядосвіту", "emilyisaway": "емілійдодому", "indivisible": "неділимо", "freeside": "вільняк", "epic7": "епік7", "ff7evercrisis": "ff7навікикриза", "xenogears": "зеногири", "megamitensei": "мегамітенсей", "symbaroum": "сімбарум", "postcyberpunk": "посткіберпанк", "deathroadtocanada": "смертельнийшляхдоканади", "palladium": "палад<PERSON><PERSON>м", "knightjdr": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "мисливецьзмонстрів", "fireemblem": "вогнянийгерой", "genshinimpact": "genshinimpact", "geosupremancy": "геосупремасі", "persona5": "персона5", "ghostoftsushima": "привидцушими", "sekiro": "сокіро", "monsterhunterrise": "мисливецьнамонстріввисота", "nier": "<PERSON><PERSON>р", "dothack": "дотхак", "ys": "йс", "souleater": "поїдачдуш", "fatestaynight": "фатес<PERSON><PERSON>й<PERSON>йт", "etrianodyssey": "етрианодісі", "nonarygames": "нонавальніігри", "tacticalrpg": "тактичнарпг", "mahoyo": "маґоё", "animegames": "анімеігри", "damganronpa": "дамганронпа", "granbluefantasy": "гранблюфентезі", "godeater": "пожирач<PERSON>роби", "diluc": "ді<PERSON><PERSON><PERSON>", "venti": "венті", "eternalsonata": "вічнасоната", "princessconnect": "принцесапоєднує", "hexenzirkel": "відьомськеколо", "cristales": "кристали", "vcs": "всім", "pes": "пес", "pocketsage": "кишеняветхник", "valorant": "валора́нт", "valorante": "валорант", "valorantindian": "валорантіндійський", "dota": "дота", "madden": "мадден", "cdl": "кдл", "efootbal": "ефутбол", "nba2k": "нба2к", "egames": "егри", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "кіберспорт", "mlg": "млг", "leagueofdreamers": "ліг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "fifa14": "фіфа14", "midlaner": "мід<PERSON><PERSON><PERSON><PERSON><PERSON>р", "efootball": "електроннийфутбол", "dreamhack": "дремхак", "gaimin": "гай<PERSON><PERSON>н", "overwatchleague": "лігаоверва<PERSON>у", "cybersport": "кіберспорт", "crazyraccoon": "божевільнийєнот", "test1test": "тест1тест", "fc24": "фк24", "riotgames": "riotgames", "eracing": "ерейсинг", "brasilgameshow": "бразильськеігровешоу", "valorantcompetitive": "валарантконкурентний", "t3arena": "t3arena", "valorantbr": "валора́нтбр", "csgo": "ксго", "tf2": "тф2", "portal2": "портал2", "halflife": "півжиття", "left4dead": "left4dead", "left4dead2": "лівуй4мертвих2", "valve": "клапан", "portal": "портал", "teamfortress2": "командафортеця2", "everlastingsummer": "вічнеліто", "goatsimulator": "козосимулятор", "garrysmod": "гаррісмод", "freedomplanet": "світвільностей", "transformice": "трансформіс", "justshapesandbeats": "простоформитабіти", "battlefield4": "бійцівськеполе4", "nightinthewoods": "вночівлісі", "halflife2": "половиннежиття2", "hacknslash": "хак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "глибокийскелястийгалактичний", "riskofrain2": "ризикидощу2", "metroidvanias": "метродванії", "overcooked": "перепечене", "interplanetary": "міжпланетний", "helltaker": "пекельнийзаб<PERSON>р", "inscryption": "інскрипція", "7d2d": "7д2д", "deadcells": "мертвіклітини", "nierautomata": "нієратомата", "gmod": "gmod", "dwarffortress": "дверффортес", "foxhole": "фоксхол", "stray": "бездомний", "battlefield": "майдан<PERSON>ик", "battlefield1": "польобоювання1", "swtor": "свтор", "fallout2": "fallout2", "uboat": "убот", "eyeb": "чекаюнаboo", "blackdesert": "чорнапустеля", "tabletopsimulator": "симуляторстолів", "partyhard": "вечірканаповну", "hardspaceshipbreaker": "жорсткийкосмічнийламкозламник", "hades": "<PERSON>а<PERSON><PERSON><PERSON>", "gunsmith": "зброяр", "okami": "окамі", "trappedwithjester": "захопленізжестером", "dinkum": "дін<PERSON><PERSON>м", "predecessor": "попередник", "rainworld": "дощовийсвіт", "cavesofqud": "печерикуд", "colonysim": "колонійка", "noita": "<PERSON><PERSON><PERSON>", "dawnofwar": "раноквійни", "minionmasters": "міоніонмайстри", "grimdawn": "похмурийсвітанок", "darkanddarker": "темряватемніша", "motox": "мотох", "blackmesa": "чорнамета", "soulworker": "сoulworker", "datingsims": "сімкидляроманів", "yaga": "яга", "cubeescape": "квестзбоку", "hifirush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "свенкуб", "newcity": "новемісто", "citiesskylines": "містобудування", "defconheavy": "дефконважкий", "kenopsia": "кенопсія", "virtualkenopsia": "віртуальнакенопсія", "snowrunner": "снігопробіг", "libraryofruina": "бібліотекападіння", "l4d2": "l4d2", "thenonarygames": "некласичніігри", "omegastrikers": "омегастрайкери", "wayfinder": "шляховказ", "kenabridgeofspirits": "кенабріджетиспиртів", "placidplasticduck": "плассидпластиковийпатрік", "battlebit": "бітв<PERSON><PERSON><PERSON>т", "ultimatechickenhorse": "ультимативнийпівенькурки", "dialtown": "дайлтown", "smileforme": "поскаржсямені", "catnight": "кот<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "супермясопідліток", "tinnybunny": "тінн<PERSON>кр<PERSON>лик", "cozygrove": "затишнийгай", "doom": "закинуто", "callofduty": "дзвінокдивізії", "callofdutyww2": "callofdutyww2", "rainbow6": "райдужний6", "apexlegends": "апекслегендс", "cod": "код", "borderlands": "пограниччя", "pubg": "пубг", "callofdutyzombies": "кличдутямерці", "apex": "апекс", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "тохо", "farcry": "да<PERSON><PERSON><PERSON><PERSON>йкрик", "farcrygames": "farcryігри", "paladins": "паладини", "earthdefenseforce": "захистземлі", "huntshowdown": "полюваннянаотпад", "ghostrecon": "привидузброєнні", "grandtheftauto5": "грандтехфтавто5", "warz": "війна", "sierra117": "сієра117", "dayzstandalone": "dayzstandalone", "ultrakill": "ультракіл", "joinsquad": "приєднуйсядоскладу", "echovr": "еховр", "discoelysium": "дискоеліз<PERSON>ум", "insurgencysandstorm": "повстанськийпісок", "farcry3": "farcry3", "hotlinemiami": "гаря<PERSON>ийлініямаямі", "maxpayne": "макспейн", "hitman3": "хітмен3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "смертездистанція", "b4b": "б4б", "codwarzone": "коду<PERSON><PERSON><PERSON><PERSON>زون", "callofdutywarzone": "callofdutywarzone", "codzombies": "кодзомбі", "mirrorsedge": "відображення边缘", "divisions2": "дваподіли2", "killzone": "зонавбивств", "helghan": "гельґан", "coldwarzombies": "холоднізомбі", "metro2033": "метро2033", "metalgear": "ме<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acecombat": "аскомбат", "crosscode": "кроскод", "goldeneye007": "золотий_ока007", "blackops2": "blackops2", "sniperelite": "снайпереліт", "modernwarfare": "сучаснівійни", "neonabyss": "нeонaбіс", "planetside2": "планетасторон2", "mechwarrior": "мехвоїн", "boarderlands": "кордони", "owerwatch": "оверва<PERSON><PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "втеча_з_таркова", "metalslug": "мета<PERSON><PERSON>уг", "primalcarnage": "первіснажертва", "worldofwarships": "світкорабельнихбою", "back4blood": "back4blood", "warframe": "війнопис", "rainbow6siege": "райдужний6облога", "xcom": "ікском", "hitman": "кілер", "masseffect": "массефект", "systemshock": "системнийшок", "valkyriachronicles": "валкірійськіхроніки", "specopstheline": "спецопераціяграницею", "killingfloor2": "killfloor2", "cavestory": "печернаісторія", "doometernal": "долюблювічність", "centuryageofashes": "століттяпопелу", "farcry4": "farcry4", "gearsofwar": "передачивійни", "mwo": "мвб", "division2": "девізія2", "tythetasmaniantiger": "титасманійськийтигр", "generationzero": "поколіннянуль", "enterthegungeon": "входьугнездо", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "сучаснавайнa2", "blackops1": "чорніоперації1", "sausageman": "сосисканчик", "ratchetandclank": "ratchetandclank", "chexquest": "чексквест", "thephantompain": "паралізованеболю", "warface": "військовеобличчя", "crossfire": "кросфайр", "atomicheart": "атомне_серце", "blackops3": "чорніоперації3", "vampiresurvivors": "вампіриживці", "callofdutybatleroyale": "кличвійнибатлерояль", "moorhuhn": "мургун", "freedoom": "важливістьсвободи", "battlegrounds": "польобою", "frag": "фраг", "tinytina": "маленькатіша", "gamepubg": "граємоpubg", "necromunda": "некромунда", "metalgearsonsoflibert": "синисвободиmetalgear", "juegosfps": "ігриfps", "convertstrike": "перетворенняудару", "warzone2": "warzone2", "shatterline": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "чорніопераціїзомбі", "bloodymess": "кривавийбезлад", "republiccommando": "республіканськийкомандос", "elitedangerous": "елітнанебезпечна", "soldat": "солдат", "groundbranch": "землянагілка", "squad": "команда", "destiny1": "доля1", "gamingfps": "ігриfps", "redfall": "червонопад", "pubggirl": "пабгдів<PERSON>ина", "worldoftanksblitz": "світтанківбліц", "callofdutyblackops": "колдютиблекопс", "enlisted": "включений", "farlight": "дальнєсвітло", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "сплатун3", "armoredcore": "бронювальник", "pavlovvr": "павловвр", "xdefiant": "xдефіант", "tinytinaswonderlands": "маленькічудесамарії", "halo2": "гелікс2", "payday2": "пейдейт2", "cs16": "cs16", "pubgindonesia": "pubgіндонезія", "pubgukraine": "pubgукраїна", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgроманія", "empyrion": "емпіріон", "pubgczech": "pubgcz", "titanfall2": "titanfall2", "soapcod": "милокод", "ghostcod": "привидкод", "csplay": "ксдплей", "unrealtournament": "нереа<PERSON><PERSON><PERSON><PERSON>турн<PERSON>р", "callofdutydmz": "закликобовязкудмз", "gamingcodm": "геймінгкодм", "borderlands2": "кордони2", "counterstrike": "контрстрайк", "cs2": "кс2", "pistolwhip": "пістолетналупцюга", "callofdutymw2": "callofdutymw2", "quakechampions": "квакачемпіони", "halo3": "хейло3", "halo": "хало", "killingfloor": "вбивчийповерх", "destiny2": "дестині2", "exoprimal": "експраймл", "splintercell": "сплі́нтерселл", "neonwhite": "неоновийбілий", "remnant": "залишок", "azurelane": "azurelane", "worldofwar": "світвійни", "gunvolt": "ганвольт", "returnal": "повернення", "halo4": "хало4", "haloreach": "привітприйдеться", "shadowman": "тіньовийчоловік", "quake2": "землетрус2", "microvolts": "мікровольти", "reddead": "червониймертвий", "standoff2": "стендаф2", "harekat": "харекат", "battlefield3": "польбою3", "lostark": "лостарк", "guildwars2": "гильдійнівійни2", "fallout76": "фоллаут76", "elsword": "елсворд", "seaofthieves": "морезлод<PERSON>їв", "rust": "іржа", "conqueronline": "завойовуйонлайн", "dauntless": "безстрашний", "warships": "військовікораблі", "dayofdragons": "деньд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "warthunder": "війнагрім", "flightrising": "польотипідйому", "recroom": "рекрум", "legendsofruneterra": "легендирунетери", "pso2": "псо2", "myster": "містично", "phantasystaronline2": "фантастичнізіркионлайн2", "maidenless": "бездівчини", "ninokuni": "нінокуні", "worldoftanks": "світта<PERSON><PERSON><PERSON>в", "crossout": "закреслити", "agario": "агаріо", "secondlife": "другежиття", "aion": "aion", "toweroffantasy": "вежапрfantasies", "netplay": "нетплей", "everquest": "вічнийпошук", "metin2": "метін2", "gtaonline": "г<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "ninokunicrossworld": "нінокунікроссвіт", "reddeadonline": "реддедонлайн", "superanimalroyale": "суперанималройал", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "лица<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "gw2": "гв2", "tboi": "тбой", "thebindingofisaac": "звязокізака", "dragonageinquisition": "драгонейджінквізиція", "codevein": "кодовени", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпінгвін", "lotro": "лотро", "wakfu": "вакафу", "scum": "сміття", "newworld": "новийсвіт", "blackdesertonline": "чорнеземонлайн", "multiplayer": "мультиплеєр", "pirate101": "підпільний101", "honorofkings": "шанакор<PERSON><PERSON>ів", "fivem": "фівем", "starwarsbattlefront": "зірковівійнибітвамар<PERSON>а<PERSON><PERSON>в", "karmaland": "кармаленд", "ssbu": "ссбу", "starwarsbattlefront2": "звізднівійнибійфронт2", "phigros": "фігроси", "mmo": "ммо", "pokemmo": "покеммо", "ponytown": "поніт<PERSON><PERSON>н", "3dchat": "3dчат", "nostale": "ностальгія", "tauriwow": "таурівау", "wowclassic": "воукласик", "worldofwarcraft": "світвійникра<PERSON>т", "warcraft": "воркрафт", "wotlk": "вотлк", "runescape": "рунескап", "neopets": "неопети", "moba": "моба", "habbo": "хаббо", "archeage": "арчэйдж", "toramonline": "торомон<PERSON>айн", "mabinogi": "мабіноґі", "ashesofcreation": "попілстворення", "riotmmo": "риотомо", "silkroad": "шовковийшлях", "spiralknights": "спіральн<PERSON>лицарі", "mulegend": "мулегенд", "startrekonline": "стартреконлайн", "vindictus": "віндиктус", "albiononline": "альбіон<PERSON>н<PERSON><PERSON><PERSON>н", "bladeandsoul": "bladeandsoul", "evony": "евоні", "dragonsprophet": "драконівпророк", "grymmo": "гріммо", "warmane": "warmane", "multijugador": "мультіплеєр", "angelsonline": "ангелио<PERSON><PERSON><PERSON><PERSON>н", "lunia": "луня", "luniaz": "<PERSON>у<PERSON><PERSON><PERSON><PERSON>", "idleon": "айдлон", "dcuniverseonline": "дкунверсон<PERSON><PERSON><PERSON>н", "growtopia": "гровтопія", "starwarsoldrepublic": "старварсстарареспубліка", "grandfantasia": "грандфантазія", "blueprotocol": "блакитнийпротокол", "perfectworld": "ідеальнийсвіт", "riseonline": "піднімайсявмережі", "corepunk": "корепанк", "adventurequestworlds": "пригодивсвітіквестів", "flyforfun": "літайдлязадоволення", "animaljam": "анім<PERSON><PERSON><PERSON><PERSON><PERSON>м", "kingdomofloathing": "королівстволомання", "cityofheroes": "містогер<PERSON>їв", "mortalkombat": "морталкомбат", "streetfighter": "вуличнийбійець", "hollowknight": "порожнійлицар", "metalgearsolid": "мета<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "forhonor": "зарадипоц<PERSON><PERSON><PERSON>н<PERSON>у", "tekken": "текен", "guiltygear": "виннийлист", "xenoverse2": "ксеноверс2", "fgc": "фгц", "streetfighter6": "стрітфайтер6", "multiversus": "мультівсесвіт", "smashbrosultimate": "смешбросультімейт", "soulcalibur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "бравлхалла", "virtuafighter": "віртуальнийбійець", "streetsofrage": "вулицямирадості", "mkdeadlyalliance": "жахлива<PERSON><PERSON><PERSON><PERSON><PERSON>с", "nomoreheroes": "більше_нема_героїв", "mhr": "мгс", "mortalkombat12": "морталкомбат12", "thekingoffighters": "корольб<PERSON>йців", "likeadragon": "якдракон", "retrofightinggames": "ретрофайтинґігри", "blasphemous": "богохульний", "rivalsofaether": "суперникивітрів", "persona4arena": "persona4arena", "marvelvscapcom": "марвелпротикапком", "supersmash": "супербитва", "mugen": "мудзен", "warofthemonsters": "війнамонстрів", "jogosdeluta": "ігриборотьби", "cyberbots": "кіберботи", "armoredwarriors": "бронированівоїни", "finalfight": "останнябитва", "poweredgear": "потужнеобладнання", "beatemup": "бійка", "blazblue": "блазблю", "mortalkombat9": "морталкомбат9", "fightgames": "ігрибитви", "killerinstinct": "вбивчийінстинкт", "kingoffigthers": "корольфайтерів", "ghostrunner": "привид<PERSON><PERSON>гун", "chivalry2": "лицарство2", "demonssouls": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "blazbluecrosstag": "блазблукросстаг", "blazbluextagbattle": "блазблуекстагбатл", "blazbluextag": "blazbluextag", "guiltygearstrive": "гіркавиннаборотьба", "hollowknightsequel": "продовженняхоллоунайта", "hollowknightsilksong": "порожнійлицарсілкпісня", "silksonghornet": "силксонггорнет", "silksonggame": "сілксонггра", "silksongnews": "новинисилксонг", "silksong": "силксонг", "undernight": "піднічне", "typelumina": "типелю<PERSON><PERSON>нa", "evolutiontournament": "турніреволюції", "evomoment": "евомомент", "lollipopchainsaw": "ліпучкоюсендзігери", "dragonballfighterz": "драгонболфайтерз", "talesofberseria": "казкибершерії", "bloodborne": "кровопотік", "horizon": "горизонт", "pathofexile": "шляхекзилу", "slimerancher": "слімера<PERSON>нер", "crashbandicoot": "крешбандикут", "bloodbourne": "кровнабірня", "uncharted": "незвідане", "horizonzerodawn": "горизонтнульвосходу", "ps4": "пс4", "ps5": "пс5", "spyro": "спайро", "playstationplus": "плейстейшнплюс", "lastofus": "останн<PERSON><PERSON><PERSON>с", "infamous": "поганарепутація", "playstationbuddies": "плейстейшндрузяки", "ps1": "пс1", "oddworld": "дивн<PERSON><PERSON><PERSON><PERSON>т", "playstation5": "плейстейшн5", "slycooper": "слайкупер", "psp": "псп", "rabbids": "рабідси", "splitgate": "сплітгейт", "persona4": "персона4", "hellletloose": "хайзаводиться", "gta4": "gta4", "gta": "гта", "roguecompany": "недисциплінованийкомпанія", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "гтасанандреас", "godofwar": "богвійни", "gris": "грис", "trove": "скарбниця", "detroitbecomehuman": "детройтстаєлюдиною", "beatsaber": "біт<PERSON><PERSON><PERSON><PERSON><PERSON>р", "rimworld": "римвсесвіт", "stellaris": "стел<PERSON><PERSON><PERSON><PERSON><PERSON>", "ps3": "пс3", "untildawn": "дор<PERSON><PERSON><PERSON>у", "touristtrophy": "туристичнийтрофей", "lspdfr": "лспдфр", "shadowofthecolossus": "тіньколосів", "crashteamracing": "крэшкомандагонки", "fivepd": "пятьпд", "tekken7": "теккен7", "devilmaycry": "дияволможеплакати", "devilmaycry3": "дияволплаче3", "devilmaycry5": "дияволплачев5", "ufc4": "ufc4", "playingstation": "плейстейшн", "samuraiwarriors": "самур<PERSON>йськібійці", "psvr2": "psvr2", "thelastguardian": "останнійохоронець", "soulblade": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5рп", "gtav": "гтаві", "playstation3": "плейстейшн3", "manhunt": "полюваннянамужиків", "gtavicecity": "гтавайсситі", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "останнійохоронець", "xboxone": "xboxone", "forza": "форца", "cd": "сд", "gamepass": "геймпасс", "armello": "арміло", "partyanimal": "творьовість", "warharmmer40k": "warhammer40k", "fightnightchampion": "битвенафестиваль", "psychonauts": "психонавти", "mhw": "mhw", "princeofpersia": "принцперсії", "theelderscrollsskyrim": "стар<PERSON><PERSON><PERSON>исткискайрім", "pantarhei": "пантер<PERSON><PERSON>", "theelderscrolls": "старшіпрокляття", "gxbox": "gxbox", "battlefront": "фронтбою", "dontstarvetogether": "неголодуйтевместе", "ori": "орі", "spelunky": "спеланкі", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "зоряниймежа", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "скейт3", "houseflipper": "перевертеньбудинків", "americanmcgeesalice": "американськийалісміджибу", "xboxs": "іксбокс", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ліга<PERSON><PERSON><PERSON><PERSON>тв", "fable2": "казка2", "xboxgamepass": "xboxgamepass", "undertale": "анде<PERSON><PERSON><PERSON><PERSON><PERSON>", "trashtv": "сміттєветв", "skycotl": "небосхил", "erica": "еріка", "ancestory": "родовід", "cuphead": "капітанч<PERSON>шка", "littlemisfortune": "маленьканезгода", "sallyface": "саліфейс", "franbow": "франбоу", "monsterprom": "монстрменти", "projectzomboid": "проектзомбойд", "ddlc": "ддлц", "motos": "мото", "outerwilds": "зовнішнідикі", "pbbg": "пббг", "anshi": "а<PERSON><PERSON><PERSON>", "cultofthelamb": "культягняти", "duckgame": "качинийг<PERSON><PERSON>й", "thestanleyparable": "стенлі<PERSON>арабл", "towerunite": "towerunite", "occulto": "оккульто", "longdrive": "дотягнись", "satisfactory": "задовільно", "pluviophile": "плувіофіл", "underearth": "підземка", "assettocorsa": "асеттоцорса", "geometrydash": "геометричнийстрибок", "kerbal": "кербал", "kerbalspaceprogram": "кербалкосмічнапрограма", "kenshi": "кен<PERSON>і", "spiritfarer": "проводникдуш", "darkdome": "темнийкупол", "pizzatower": "піцата<PERSON>ер", "indiegame": "індіге<PERSON>м", "itchio": "itchio", "golfit": "golfit", "truthordare": "правдаабооборот", "game": "гра", "rockpaperscissors": "каміньножиціпапір", "trampoline": "трамплін", "hulahoop": "хулу<PERSON><PERSON>нг", "dare": "скинься", "scavengerhunt": "квест", "yardgames": "ігр<PERSON><PERSON>г<PERSON><PERSON><PERSON><PERSON>і", "pickanumber": "виберітьномер", "trueorfalse": "правдаабообріхня", "beerpong": "пивнийпул", "dicegoblin": "гоблінкостіків", "cosygames": "затишніігри", "datinggames": "ігридлязнайомств", "freegame": "безкоштовнагра", "drinkinggames": "ігринапиття", "sodoku": "содоку", "juegos": "ігри", "mahjong": "маха<PERSON><PERSON><PERSON>н", "jeux": "ігри", "simulationgames": "симулятори", "wordgames": "словесніігри", "jeuxdemots": "словограї", "juegosdepalabras": "ігрисловами", "letsplayagame": "давайтез<PERSON>гранафутбол", "boredgames": "настільніігри", "oyun": "ойун", "interactivegames": "інтерактивніігри", "amtgard": "ам<PERSON>г<PERSON><PERSON>д", "staringcontests": "конкурспоглядів", "spiele": "ігри", "giochi": "ігри", "geoguessr": "геогесер", "iphonegames": "ігоридляайфону", "boogames": "бограїдвіжбогами", "cranegame": "кранівгра", "hideandseek": "схованкиігри", "hopscotch": "скакалка", "arcadegames": "аркадніігри", "yakuzagames": "япокемонгеймс", "classicgame": "класичногра", "mindgames": "ігриуму", "guessthelyric": "вгадайтекст", "galagames": "галарозваги", "romancegame": "грошіромантичні", "yanderegames": "яндерегеймс", "tonguetwisters": "язикові_вправи", "4xgames": "4хігри", "gamefi": "геймфаї", "jeuxdarcades": "ігринавідкритому", "tabletopgames": "настільніігри", "metroidvania": "метродванія", "games90": "ігри90", "idareyou": "сміютебе", "mozaa": "мозаа", "fumitouedagames": "фумітудейгеймс", "racinggames": "ігридлягонок", "ets2": "ets2", "realvsfake": "справжнєпротивпідробленого", "playgames": "гративігри", "gameonline": "ігрио<PERSON><PERSON><PERSON><PERSON>н", "onlinegames": "онлайнігри", "jogosonline": "онлайнігри", "writtenroleplay": "письмовийрольовийгравець", "playaballgame": "грайвгрубу", "pictionary": "пікшенері", "coopgames": "кооперативніігри", "jenga": "дженга", "wiigames": "вігри", "highscore": "високийрахунок", "jeuxderôles": "рольовіігри", "burgergames": "бургерігри", "kidsgames": "ігридля<PERSON><PERSON><PERSON>ей", "skeeball": "скібол", "nfsmwblackedition": "нфсмвблакошедевр", "jeuconcour": "конкурсгирий", "tcgplayer": "tcgplayer", "juegodepreguntas": "грайпитаннями", "gioco": "гра", "managementgame": "іградлякерів", "hiddenobjectgame": "гралкаскритіобєкти", "roolipelit": "руліпеліти", "formula1game": "формула1гра", "citybuilder": "містобудівельник", "drdriving": "докторма<PERSON>ини", "juegosarcade": "аркадніігри", "memorygames": "ігрипамяті", "vulkan": "вулкан", "actiongames": "ігридійства", "blowgames": "благаємобіг", "pinballmachines": "пінбольніавтомати", "oldgames": "старіігри", "couchcoop": "кухоннийкооп", "perguntados": "питання", "gameo": "геймобо", "lasergame": "лазертаг", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "ледач<PERSON>гри", "fillintheblank": "заповнипропуск", "jeuxpc": "ігринапк", "rétrogaming": "ретрогеймінг", "logicgames": "логічніігри", "japangame": "японськогра", "rizzupgame": "різза<PERSON>гейм", "subwaysurf": "сабвейсерф", "jeuxdecelebrite": "ігриіззірками", "exitgames": "ігри<PERSON><PERSON><PERSON><PERSON><PERSON>д", "5vs5": "5на5", "rolgame": "ролграми", "dashiegames": "dashiegames", "gameandkill": "грай<PERSON><PERSON><PERSON><PERSON><PERSON>в<PERSON>й", "traditionalgames": "традиц<PERSON>йніігри", "kniffel": "кіфел", "gamefps": "ігрифпс", "textbasedgames": "ігринаосновітекста", "gryparagrafowe": "грипар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>і", "fantacalcio": "фантафутбол", "retrospel": "ретрошпель", "thiefgame": "граве<PERSON><PERSON>кр<PERSON><PERSON><PERSON>й", "lawngames": "ігринагазоні", "fliperama": "фліперама", "heroclix": "героклікс", "tablesoccer": "столовийфутбол", "tischfußball": "настільнийфутбол", "spieleabende": "ігровівечори", "jeuxforum": "ігровийфорум", "casualgames": "повсякденніігри", "fléchettes": "флешети", "escapegames": "ігривтечі", "thiefgameseries": "геймсеріякрадій", "cranegames": "гейминакадрі", "játék": "гра", "bordfodbold": "бордфутбол", "jogosorte": "ігросорт", "mage": "маю", "cargames": "ігрипроавто", "onlineplay": "онлайнгра", "mölkky": "молккі", "gamenights": "ігровіночі", "pursebingos": "пурси_бінго", "randomizer": "рандомайзер", "msx": "msx", "anagrammi": "анаграмки", "gamespc": "ігриpc", "socialdeductiongames": "ігрисоціальноїдедукції", "dominos": "домер<PERSON>ї", "domino": "доміно", "isometricgames": "ізометричніігри", "goodoldgames": "старіігри", "truthanddare": "правдаісиλвини", "mahjongriichi": "ма<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>і", "scavengerhunts": "пошукскарбів", "jeuxvirtuel": "віртуальніігри", "romhack": "ромхак", "f2pgamer": "геймербезгрошей", "free2play": "безкоштовноіграти", "fantasygame": "фентезігра", "gryonline": "грятакожен", "driftgame": "дрифтгра", "gamesotomes": "ігрищобу", "halotvseriesandgames": "галосеріїіігри", "mushroomoasis": "грибневийоазис", "anythingwithanengine": "усеіздвигуном", "everywheregame": "всюдигра", "swordandsorcery": "меч<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на", "goodgamegiving": "класнагращодобро", "jugamos": "граймо", "lab8games": "лаб8ігри", "labzerogames": "лабнульігри", "grykomputerowe": "компютерніігри", "virgogami": "вірагамі", "gogame": "грайназдоровя", "jeuxderythmes": "ритмогри", "minaturegames": "мінідиставки", "ridgeracertype4": "тип4для<PERSON><PERSON><PERSON>дерів", "selflovegaming": "самолюбствоігри", "gamemodding": "геймоддинг", "crimegames": "ігрипреступництв", "dobbelspellen": "двоїчніігри", "spelletjes": "ігри", "spacenerf": "космічнийнерф", "charades": "шаради", "singleplayer": "одиночнийгравець", "coopgame": "кооперативнагра", "gamed": "геймед", "forzahorizon": "форзахоризон", "nexus": "нексус", "geforcenow": "гефо<PERSON><PERSON><PERSON><PERSON><PERSON>", "maingame": "головнагра", "kingdiscord": "корольдискорду", "scrabble": "скрабл", "schach": "шахи", "shogi": "шогі", "dandd": "дандд", "catan": "катан", "ludo": "людо", "backgammon": "нарди", "onitama": "онітома", "pandemiclegacy": "спад<PERSON>инапандемії", "camelup": "верблюдгетьуп", "monopolygame": "монополіягра", "brettspiele": "настільніігри", "bordspellen": "настільніігри", "boardgame": "настільнагра", "sällskapspel": "настільніігри", "planszowe": "настільніігри", "risiko": "ризик", "permainanpapan": "настільніігри", "zombicide": "зомбіапокаліпсис", "tabletop": "настільніігри", "baduk": "бадук", "bloodbowl": "кровяначаша", "cluedo": "<PERSON><PERSON>ued<PERSON>", "xiangqi": "сянці", "senet": "сенет", "goboardgame": "goboardgame", "connectfour": "підключитичотири", "heroquest": "героїчнезавдання", "giochidatavolo": "настільніігри", "farkle": "фаркл", "carrom": "каром", "tablegames": "настільніігри", "dicegames": "ігризкубиками", "yatzy": "ятзі", "parchis": "парчас", "jogodetabuleiro": "йогадотаблиці", "jocuridesocietate": "ігриспільноти", "deskgames": "ігринадеску", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "ма<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "marvelcrisisprotocol": "марвелкритичнапротокол", "cosmicencounter": "косм<PERSON>чназустр<PERSON>ч", "creationludique": "ігровестворення", "tabletoproleplay": "настільнарольоваіграшка", "cardboardgames": "картонніігри", "eldritchhorror": "елдрічнийжах", "switchboardgames": "перемикайігри", "infinitythegame": "безкінечністьгри", "kingdomdeath": "королівськасмерть", "yahtzee": "яцці", "chutesandladders": "лістінаитарічки", "társas": "тусовка", "juegodemesa": "настільніігри", "planszówki": "настільніігри", "rednecklife": "життядеревенщини", "boardom": "нудота", "applestoapples": "яблукадояблук", "jeudesociété": "громадськіпригоди", "gameboard": "ігровадошка", "dominó": "доміно", "kalah": "калаh", "crokinole": "крокінол", "jeuxdesociétés": "грайвкомпанії", "twilightimperium": "світанковийімперій", "horseopoly": "horseopoly", "deckbuilding": "будівництво_колод", "mansionsofmadness": "магіябожевілля", "gomoku": "гомоку", "giochidatavola": "настільніігри", "shadowsofbrimstone": "тін<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoftokyo": "корольтокіо", "warcaby": "шахи", "táblajátékok": "настільніігри", "battleship": "бattleship", "tickettoride": "квитокнакатання", "deskovehry": "настільніігри", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "настільніігри", "stolníhry": "настільніігри", "xiángqi": "сянці", "jeuxsociete": "настільніігри", "gesellschaftsspiele": "настільніігри", "starwarslegion": "зорянівійнилегеніія", "gochess": "гопартія", "weiqi": "вайці", "jeuxdesocietes": "ігриспільноти", "terraria": "терарія", "dsmp": "дсмп", "warzone": "військовазона", "arksurvivalevolved": "аркурvivalеволюція", "dayz": "деньки", "identityv": "ідентичністьv", "theisle": "острів", "thelastofus": "останн<PERSON><PERSON><PERSON>с", "nomanssky": "номанськый", "subnautica": "субнаутіка", "tombraider": "томбрейдер", "callofcthulhu": "кличкрутул<PERSON>у", "bendyandtheinkmachine": "гнучкийтіперенесенийчернилами", "conanexiles": "конанекзай<PERSON>з", "eft": "ефт", "amongus": "середнас", "eco": "еко", "monkeyisland": "макакострів", "valheim": "вал<PERSON>ейм", "planetcrafter": "планетокрафтер", "daysgone": "дн<PERSON><PERSON>р<PERSON><PERSON><PERSON>ли", "fobia": "фобія", "witchit": "відьмачка", "pathologic": "патологічно", "zomboid": "зомбойд", "northgard": "північназемля", "7dtd": "7дтд", "thelongdark": "долгаятемрява", "ark": "арк", "grounded": "приземлений", "stateofdecay2": "stateofdecay2", "vrising": "вризинг", "madfather": "бож<PERSON><PERSON><PERSON><PERSON>ьнамат<PERSON>р", "dontstarve": "неголодуй", "eternalreturn": "вічнийповертання", "pathoftitans": "<PERSON>л<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "фрикційніігри", "hexen": "гекси", "theevilwithin": "злоусередині", "realrac": "реальнийрак", "thebackrooms": "заднікімнати", "backrooms": "заднікімнати", "empiressmp": "імперіясмп", "blockstory": "блокісторія", "thequarry": "карєра", "tlou": "tlou", "dyinglight": "мертвесвітло", "thewalkingdeadgame": "ігривмертвяків", "wehappyfew": "мищасливіідея", "riseofempires": "зростанняімперій", "stateofsurvivalgame": "грастанувиживання", "vintagestory": "вінтажнаісторія", "arksurvival": "арквиживання", "barotrauma": "баротравма", "breathedge": "дихатиме", "alisa": "alisa", "westlendsurvival": "вестлендвиження", "beastsofbermuda": "звірібермуда", "frostpunk": "моруза", "darkwood": "темноліс", "survivalhorror": "виживальнужахи", "residentevil": "мертвийжитеть", "residentevil2": "резидентевіл2", "residentevil4": "резидентзло4", "residentevil3": "residentevil3", "voidtrain": "потягпорожнечі", "lifeaftergame": "життяпіслягри", "survivalgames": "ігринавиживання", "sillenthill": "ти<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "цяйвійнамене", "scpfoundation": "scpфонду", "greenproject": "зеленапроєкт", "kuon": "куон", "cryoffear": "плачвідстраху", "raft": "плот", "rdo": "рдо", "greenhell": "зеленепекло", "residentevil5": "резидентзло5", "deadpoly": "мертвіполі", "residentevil8": "резидентзло8", "onironauta": "онірована", "granny": "бабуся", "littlenightmares2": "маленькінічнікошмари2", "signalis": "сигнали", "amandatheadventurer": "амандавинахідниця", "sonsoftheforest": "сини<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "ігра<PERSON>", "outlasttrials": "вистоїмоексперименти", "alienisolation": "інопланетянкарантин", "undawn": "недосвіт", "7day2die": "7днівщобпомерти", "sunlesssea": "безсонячне_mоре", "sopravvivenza": "виживання", "propnight": "пропнайт", "deadisland2": "мертвийострів2", "ikemensengoku": "ікеменсенгоку", "ikemenvampire": "ікеменвампір", "deathverse": "смертельнийверс", "cataclysmdarkdays": "катаклізмтемнідні", "soma": "сома", "fearandhunger": "страхіхарчування", "stalkercieńczarnobyla": "сталькерчіньчарнобиля", "lifeafter": "життяпісля", "ageofdarkness": "епохатемряви", "clocktower3": "годинниковавежа3", "aloneinthedark": "одинувтемряві", "medievaldynasty": "середньовічнадинастія", "projectnimbusgame": "проектнібусгейм", "eternights": "вічнінощі", "craftopia": "кравтопія", "theoutlasttrials": "випробуваннявистояти", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "світовепанування", "rocketleague": "рокетліга", "tft": "тфг", "officioassassinorum": "офіснівбивці", "necron": "некрон", "wfrp": "вфрп", "dwarfslayer": "хобітубийця", "warhammer40kcrush": "хайп40к", "wh40": "wh40", "warhammer40klove": "коханнявоєнногомартіру40k", "warhammer40klore": "ворграмер40кліда", "warhammer": "вархаммер", "warhammer30k": "wahammer30k", "warhammer40k": "вархаммер40к", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "темплокулексус", "vindicare": "виндикаре", "ilovesororitas": "ялюблюсорорітас", "ilovevindicare": "ялюблювіндикація", "iloveassasinorum": "ялюблюасасінорам", "templovenenum": "темпловененум", "templocallidus": "темплокалідус", "templomaerorus": "темпломерорус", "templovanus": "темплуванус", "oficioasesinorum": "діло<PERSON><PERSON><PERSON>вць", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40к", "tetris": "тетріс", "lioden": "ліоден", "ageofempires": "ераімперій", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "вархаммерерасігмара", "civilizationv": "цивіл<PERSON>заціяv", "ittakestwo": "длядвох", "wingspan": "розмахкрила", "terraformingmars": "терраформуваннямарса", "heroesofmightandmagic": "героїсилитаmágii", "btd6": "btd6", "supremecommander": "супремекомандувач", "ageofmythology": "епоха<PERSON><PERSON><PERSON><PERSON>в", "args": "аргс", "rime": "райм", "planetzoo": "планетнийзоопарк", "outpost2": "постріл2", "banished": "вигнаний", "caesar3": "цезар3", "redalert": "червонасигналізація", "civilization6": "цивілізація6", "warcraft2": "варкрафт2", "commandandconquer": "командуйтаперемагай", "warcraft3": "warcraft3", "eternalwar": "вічнавійна", "strategygames": "стратегічніігри", "anno2070": "анно2070", "civilizationgame": "цивіл<PERSON>з<PERSON><PERSON><PERSON>йнагра", "civilization4": "цивілізація4", "factorio": "факторіо", "dungeondraft": "dungeondraft", "spore": "спора", "totalwar": "повнав<PERSON>йна", "travian": "травіан", "forts": "фортс", "goodcompany": "класнеколо", "civ": "цив", "homeworld": "дом<PERSON>шнійсвіт", "heidentum": "хайдентизм", "aoe4": "аое4", "hnefatafl": "гнимедивини", "fasterthanlight": "швидшен<PERSON>жсвітло", "forthekings": "для<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "realtimestrategy": "реальнийчасстратегія", "starctaft": "старктавт", "sidmeierscivilization": "сі́дмейєрцивілізація", "kingdomtwocrowns": "королівстводвохкорон", "eu4": "євро4", "vainglory": "гордість", "ww40k": "вр40к", "godhood": "божество", "anno": "анно", "battletech": "боювітехнології", "malifaux": "маліфо", "w40k": "в40к", "hattrick": "хеттрик", "davesfunalgebraclass": "девівзабавнийалгебріклас", "plagueinc": "чум<PERSON><PERSON><PERSON>н", "theorycraft": "теоріястворення", "mesbg": "межброви", "civilization3": "цивілізація3", "4inarow": "4уновряд", "crusaderkings3": "крусадери3", "heroes3": "герої3", "advancewars": "війнапросунків", "ageofempires2": "епохаімперій2", "disciples2": "дослідження2", "plantsvszombies": "рослинипротизомбі", "giochidistrategia": "ігристратег<PERSON>ї", "stratejioyunları": "стратегічніігри", "europauniversalis4": "європауніверсаліс4", "warhammervermintide2": "warhammerвермінтид2", "ageofwonders": "ерадивоочікувань", "dinosaurking": "дослідникдинозаврів", "worldconquest": "світоваокупація", "heartsofiron4": "серцяіззаліза4", "companyofheroes": "компаніягероїв", "battleforwesnoth": "битвазавесноте", "aoe3": "aoe3", "forgeofempires": "кузняімперій", "warhammerkillteam": "військовіфішки", "goosegooseduck": "гусакгусакпитон", "phobies": "фобії", "phobiesgame": "фобіїгри", "gamingclashroyale": "ігриклашрояль", "adeptusmechanicus": "адептимеханікусу", "outerplane": "зовнішняплита", "turnbased": "покроковий", "bomberman": "бомбермен", "ageofempires4": "вікімперій4", "civilization5": "цивілізація5", "victoria2": "віктора2", "crusaderkings": "крусадерикоролів", "cultris2": "cultris2", "spellcraft": "магіясловес", "starwarsempireatwar": "зорянівійниімперіянавійну", "pikmin4": "пікмін4", "anno1800": "anno1800", "estratégia": "стратегія", "popfulmail": "попфулмейл", "shiningforce": "сіяючасила", "masterduel": "майстердуель", "dysonsphereprogram": "дізонсфернапрограма", "transporttycoon": "транспортнийтікун", "unrailed": "нерозпізнаний", "magicarena": "маг<PERSON>чнаар<PERSON>на", "wolvesville": "вовчеземля", "ooblets": "убодлети", "planescapetorment": "плануйвтечуизтерпіння", "uplandkingdoms": "високіцарства", "galaxylife": "галактичнежиття", "wolvesvilleonline": "вестиуо<PERSON><PERSON><PERSON><PERSON>н", "slaythespire": "вламайспайр", "battlecats": "бійцівськікоти", "sims3": "сімс3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "сімси", "simcity": "сімсіті", "simcity2000": "симсітіз2000", "sims2": "sims2", "iracing": "ірейсинг", "granturismo": "грантуризмо", "needforspeed": "нужноштрафу", "needforspeedcarbon": "нужнешвидкостікарбон", "realracing3": "реальнегонитвоє3", "trackmania": "трекманія", "grandtourismo": "грандтуризмо", "gt7": "гт7", "simsfreeplay": "симсвільнопограти", "ts4": "ts4", "thesims2": "сімі2", "thesims3": "thesims3", "thesims1": "сімс1", "lossims4": "втратисимс4", "fnaf": "фна<PERSON>", "outlast": "витримати", "deadbydaylight": "мертвимвдень", "alicemadnessreturns": "алісаповерненнябожевілля", "darkhorseanthology": "темнаконячкаантологія", "phasmophobia": "фазмофобія", "fivenightsatfreddys": "пятьнічейзфреддів", "saiko": "сайко", "fatalframe": "фатальнарама", "littlenightmares": "маленькікошмари", "deadrising": "підніманнямертвих", "ladydimitrescu": "ледідімітреску", "homebound": "нака<PERSON><PERSON>н<PERSON>ині", "deadisland": "мертвіострови", "litlemissfortune": "маленьканещастинка", "projectzero": "проектнуль", "horory": "горори", "jogosterror": "жогостерор", "helloneighbor": "привіт<PERSON><PERSON><PERSON><PERSON>д", "helloneighbor2": "привітсусіді2", "gamingdbd": "ігровийдбд", "thecatlady": "котяшка", "jeuxhorreur": "жахометри", "horrorgaming": "жахи<PERSON>гри", "magicthegathering": "магія<PERSON>борів", "mtg": "мтг", "tcg": "тцг", "cardsagainsthumanity": "картипротилюдства", "cribbage": "крибедж", "minnesotamtg": "мінеаполісмережа", "edh": "едх", "monte": "монте", "pinochle": "пінокль", "codenames": "кодоваігра", "dixit": "діксит", "bicyclecards": "велокартки", "lor": "лор", "euchre": "юха", "thegwent": "теґвент", "legendofrunetera": "легендарунетери", "solitaire": "солитер", "poker": "покер", "hearthstone": "хартсто<PERSON>н", "uno": "уномомент", "schafkopf": "шафкопф", "keyforge": "ключефорж", "cardtricks": "картковіфокуси", "playingcards": "ігровікарти", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "ґінрамі", "netrunner": "нетранер", "gwent": "гвент", "metazoo": "метазу", "tradingcards": "трейдингкартки", "pokemoncards": "покемо́нкартки", "fleshandbloodtcg": "мясоіменатакартковаигра", "sportscards": "спортивнікартки", "cardfightvanguard": "битвакартвангарду", "duellinks": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spades": "тазики", "warcry": "криквійни", "digimontcg": "ді<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toukenranbu": "тоукенр<PERSON><PERSON><PERSON>у", "kingofhearts": "корольсердець", "truco": "труко", "loteria": "лотерея", "hanafuda": "ханафуда", "theresistance": "сопротивление", "transformerstcg": "трансформеритцг", "doppelkopf": "доппельkopf", "yugiohcards": "карткиюгіо", "yugiohtcg": "югіохтцг", "yugiohduel": "югіо<PERSON><PERSON><PERSON>л", "yugiohocg": "югіоокг", "dueldisk": "дуелдиск", "yugiohgame": "югіохгра", "darkmagician": "темниймаг", "blueeyeswhitedragon": "блакитноокийбілийдракон", "yugiohgoat": "югіоґоат", "briscas": "бристки", "juegocartas": "ігрикарти", "burraco": "буррако", "rummy": "раммі", "grawkarty": "graw<PERSON><PERSON><PERSON>", "dobble": "дубль", "mtgcommander": "мтгкомандир", "cotorro": "котурро", "jeuxdecartes": "ігринасходах", "mtgjudge": "mtgсуддя", "juegosdecartas": "картковіігри", "duelyst": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgпреконкомандер", "kartenspiel": "ігровийкартон", "carteado": "картейдо", "sueca": "світла", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карціанки", "battlespirits": "бійцівськідухи", "battlespiritssaga": "боюсьмирівсага", "jogodecartas": "картковийсаундтрек", "žolíky": "жолікі", "facecard": "фейскартка", "cardfight": "бійкартами", "biriba": "біриба", "deckbuilders": "декобuilders", "marvelchampions": "марвелчемпіони", "magiccartas": "маг<PERSON>чнікарти", "yugiohmasterduel": "югіо<PERSON>майстердуел", "shadowverse": "теневийвсесвіт", "skipbo": "скіпбо", "unstableunicorns": "недостатньоунікорнів", "cyberse": "кіберсфера", "classicarcadegames": "класичніаркадніігри", "osu": "осуту", "gitadora": "гітадора", "dancegames": "танцювальніігри", "fridaynightfunkin": "пятничнанічфанкін", "fnf": "фнф", "proseka": "поросека", "projectmirai": "проектмірай", "projectdiva": "проектдіва", "djmax": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guitarhero": "гитар<PERSON><PERSON>гер<PERSON>й", "clonehero": "клонгерой", "justdance": "простотанцюй", "hatsunemiku": "хацун<PERSON><PERSON><PERSON><PERSON>у", "prosekai": "пір<PERSON><PERSON><PERSON>", "rocksmith": "роксміт", "idolish7": "айдоліш7", "rockthedead": "рокуймертвих", "chunithm": "<PERSON>ун<PERSON><PERSON><PERSON><PERSON>", "idolmaster": "айдолмастер", "dancecentral": "танцювальнийцентр", "rhythmgamer": "ритмгеймер", "stepmania": "степманія", "highscorerythmgames": "високібалиритмгри", "pkxd": "пкxd", "sidem": "сайдєм", "ongeki": "онгекі", "soundvoltex": "саундволтекс", "rhythmheaven": "рит<PERSON><PERSON><PERSON>й", "hypmic": "гипмик", "adanceoffireandice": "танецьвогнютальоду", "auditiononline": "аудішнозалишайся", "itgmania": "itgманія", "juegosderitmo": "ритмівідео", "cryptofthenecrodancer": "криптофтанекродансер", "rhythmdoctor": "ритм<PERSON><PERSON>кар", "cubing": "куб<PERSON>н<PERSON>", "wordle": "вордл", "teniz": "теніз", "puzzlegames": "головоломки", "spotit": "знайдице", "rummikub": "руммікуб", "blockdoku": "блокдоку", "logicpuzzles": "логічніголоволомки", "sudoku": "судоку", "rubik": "рубік", "brainteasers": "головоломки", "rubikscube": "рубіківкуб", "crossword": "кросворд", "motscroisés": "кросворди", "krzyżówki": "кросворди", "nonogram": "нонограм", "bookworm": "книжковийчервяк", "jigsawpuzzles": "пазли", "indovinello": "загадка", "riddle": "загадка", "riddles": "загадки", "rompecabezas": "пазл", "tekateki": "текатекі", "inside": "всередині", "angrybirds": "злюкімурахи", "escapesimulator": "сіміляторвтікань", "minesweeper": "міннийдо<PERSON>від", "puzzleanddragons": "пазлитаказки", "crosswordpuzzles": "кросворди", "kurushi": "куро<PERSON>і", "gardenscapesgame": "садовікраєвидиігри", "puzzlesport": "пазлспорт", "escaperoomgames": "ігри_кімнати_таємниць", "escapegame": "гранадвиження", "3dpuzzle": "3dпазл", "homescapesgame": "грайвдом<PERSON>шніхіграх", "wordsearch": "пошукслів", "enigmistica": "енігмістика", "kulaworld": "кулавсесвіт", "myst": "міст", "riddletales": "загадковісказки", "fishdom": "фішдом", "theimpossiblequiz": "неможливийквіз", "candycrush": "цукерковийрозрив", "littlebigplanet": "малийвеликийсвіт", "match3puzzle": "хардкорнапазлка", "huniepop": "ханіпоп", "katamaridamacy": "катама<PERSON><PERSON>домачі", "kwirky": "квіркі", "rubikcube": "рубіккуб", "cuborubik": "кубикрубіка", "yapboz": "яппаз", "thetalosprinciple": "принципталоса", "homescapes": "домашніпейзажі", "puttputt": "путпут", "qbert": "кюберто", "riddleme": "загадимені", "tycoongames": "тарантулькигр", "cubosderubik": "кубикрубіка", "cruciverba": "кросворди", "ciphers": "шифри", "rätselwörter": "загадковіслова", "buscaminas": "бузка<PERSON><PERSON>нас", "puzzlesolving": "розгадуванняпазлів", "turnipboy": "редисочкабої", "adivinanzashot": "адивіна<PERSON><PERSON><PERSON><PERSON>т", "nobodies": "нікому", "guessing": "вгадування", "nonograms": "нонограми", "kostkirubika": "косткірубаки", "crypticcrosswords": "криптичнікросворди", "syberia2": "сибиря2", "puzzlehunt": "пазлполювання", "puzzlehunts": "пошукпазлів", "catcrime": "котячийзлочин", "quebracabeça": "кебракабеса", "hlavolamy": "гліволами", "poptropica": "поптропіка", "thelastcampfire": "останнявогнище", "autodefinidos": "автодефіноси", "picopark": "пікопарк", "wandersong": "блукаючапісня", "carto": "картон", "untitledgoosegame": "безіменнагракачка", "cassetête": "кассета", "limbo": "лімбо", "rubiks": "рубікс", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "tinykin": "дитятка", "rubikovakostka": "рубиковакубик", "speedcube": "швидкістькубик", "pieces": "частинки", "portalgame": "порталграєм", "bilmece": "білмеця", "puzzelen": "пазли", "picross": "пікр<PERSON><PERSON>с", "rubixcube": "рубікскубик", "indovinelli": "індовінеллі", "cubomagico": "кубомагік", "mlbb": "млбб", "pubgm": "pubgm", "codmobile": "кодмобайл", "codm": "кодм", "twistedwonderland": "кривийказковийсвіт", "monopoly": "монополія", "futurefight": "майбутнєборотьба", "mobilelegends": "мобільнілегенди", "brawlstars": "бравлстарс", "brawlstar": "бравлстар", "coc": "кок", "lonewolf": "одинокийвовк", "gacha": "гача", "wr": "вр", "fgo": "фго", "bitlife": "битжиття", "pikminbloom": "пікмінцвіт", "ff": "фф", "ensemblestars": "зіркиансамблю", "asphalt9": "асфальт9", "mlb": "млб", "cookierunkingdom": "кулінарнецарство", "alchemystars": "алхіміязірок", "stateofsurvival": "станвиживання", "mycity": "міємісто", "arknights": "арканайти", "colorfulstage": "кольоровасцена", "bloonstowerdefense": "блунстоурбронабор", "btd": "битата", "clashroyale": "клашрояль", "angela": "ангела", "dokkanbattle": "докканбатл", "fategrandorder": "долявеликепорядку", "hyperfront": "гиперфронт", "knightrun": "книгогон", "fireemblemheroes": "вогнянігерої", "honkaiimpact": "хонкайімпакт", "soccerbattle": "футбольнаябитва", "a3": "а3", "phonegames": "ігринамобільному", "kingschoice": "вибіркоролів", "guardiantales": "легендиохоронця", "petrolhead": "автолюбитель", "tacticool": "тактичнокруто", "cookierun": "кукерун", "pixeldungeon": "піксельнепідземелля", "arcaea": "аркаea", "outoftheloop": "внециклу", "craftsman": "майстер", "supersus": "суперсас", "slowdrive": "повільнаездка", "headsup": "підготовані", "wordfeud": "словофеуд", "bedwars": "ліжковібі8910", "freefire": "вільнийвогонь", "mobilegaming": "мобільніігри", "lilysgarden": "лавандовийсад", "farmville2": "farmville2", "animalcrossing": "анімал<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "bgmi": "бгмі", "teamfighttactics": "команднібійнітактики", "clashofclans": "клановийбій", "pjsekai": "пджсвіт", "mysticmessenger": "містичнийпосланник", "callofdutymobile": "callofdutymobile", "thearcana": "арканна", "8ballpool": "8кульковийпул", "emergencyhq": "емер<PERSON>женсіhq", "enstars": "енстарс", "randonautica": "рандонавтика", "maplestory": "maplestory", "albion": "альбіон", "hayday": "гейдей", "onmyoji": "онмьодзі", "azurlane": "азур<PERSON>е<PERSON>н", "shakesandfidget": "тряскитаіперетворення", "ml": "мл", "bangdream": "бумсниться", "clashofclan": "клановийзапал", "starstableonline": "starstableonline", "dragonraja": "драконрая", "timeprincess": "часпринцеси", "beatstar": "бітстар", "dragonmanialegend": "драконовийкультлегенда", "hanabi": "хана<PERSON><PERSON>", "disneymirrorverse": "диснеємірорвс", "pocketlove": "киш<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "андроїдігри", "criminalcase": "кримінальнасправа", "summonerswar": "світзакликачів", "cookingmadness": "кулинарнийбожевільний", "dokkan": "доккан", "aov": "аов", "triviacrack": "тривіакрак", "leagueofangels": "ліг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "lordsmobile": "lordsmobile", "tinybirdgarden": "тіночкисад", "gachalife": "gachalife", "neuralcloud": "нейромережа", "mysingingmonsters": "моїспівомонстри", "nekoatsume": "некоацуме", "bluearchive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "військовіроботи", "mirrorverse": "дзеркальновсесвіт", "pou": "поу", "warwings": "войсокрила", "fifamobile": "фіфамобіль", "mobalegendbangbang": "мобалегендбумбум", "evertale": "евертейл", "futime": "футайм", "antiyoy": "ан<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "апекслегендмобайл", "ingress": "вхід", "slugitout": "слугиразом", "mpl": "мпл", "coinmaster": "монетниймайстер", "punishinggrayraven": "покараннясіромавка", "petpals": "друзі_для_тварин", "gameofsultans": "гра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "arenabreakout": "аренаутечі", "wolfy": "вовчиці", "runcitygame": "грайвмісті", "juegodemovil": "мобільнагра", "avakinlife": "авак<PERSON>н<PERSON>иття", "kogama": "кога́ма", "mimicry": "імітація", "blackdesertmobile": "чорнапустелямобільно", "rollercoastertycoon": "американськігіркиtycoon", "grandchase": "великеперегони", "bombmebrasil": "бомбімебразил", "ldoe": "ldoe", "legendonline": "легендапартії", "otomegame": "отомеігри", "mindustry": "майндустрія", "callofdragons": "покликдраконів", "shiningnikki": "сяюча_ніккі", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "шляхникунекуди", "sealm": "заварюєм", "shadowfight3": "тіньовабійка3", "limbuscompany": "лимбускомпані", "demolitionderby3": "демонтажнігонки3", "wordswithfriends2": "словаіздрузями2", "soulknight": "ду<PERSON><PERSON><PERSON><PERSON>ийриц<PERSON>р", "purrfecttale": "пуррфектнаісторія", "showbyrock": "showbyrock", "ladypopular": "ледіпопуляр", "lolmobile": "лолмобіль", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ер", "perfectworldmobile": "ідеальнийсвітмобіль", "empiresandpuzzles": "імперіїіпазли", "empirespuzzles": "емпірслайди", "dragoncity": "драконумісто", "garticphone": "гартікфон", "battlegroundmobileind": "battlegroundmobileind", "fanny": "феня", "littlenightmare": "маленькийкошмар", "aethergazer": "ефірогляд", "mudrunner": "mudrunner", "tearsofthemis": "сльозимісяці", "eversoul": "всесерце", "gunbound": "ганд<PERSON><PERSON><PERSON><PERSON>д", "gamingmlbb": "геймінгмлбб", "dbdmobile": "dbdmobile", "arknight": "арканайти", "pristontale": "пристонтейл", "zombiecastaways": "зомбікастоівці", "eveechoes": "ехобoo", "jogocelular": "дж<PERSON>госеллар", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "зооба", "mobilelegendbangbang": "мобільнілегендибумбум", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "кухнямама", "cabalmobile": "кабаламобіль", "streetfighterduel": "вулицькийбійдодзю", "lesecretdhenri": "таємницяденрі", "gamingbgmi": "геймінгбгмі", "girlsfrontline": "девчат<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "jurassicworldalive": "юрськийсвітживий", "soulseeker": "душепошу<PERSON><PERSON>ч", "gettingoverit": "переживаємобуде", "openttd": "openttd", "onepiecebountyrush": "одинкорабельбагатстварусь", "moonchaistory": "місячніпригоди", "carxdriftracingonline": "кархдрифтерейсінгонлайн", "jogosmobile": "мобільніігри", "legendofneverland": "легенданевланду", "pubglite": "pubglite", "gamemobilelegends": "гейммобайллегенди", "timeraiders": "тимерайдери", "gamingmobile": "геймерськамобілка", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "бійкікошки", "dnd": "<PERSON><PERSON><PERSON><PERSON>", "quest": "квест", "giochidiruolo": "рольовіігри", "dnd5e": "днд5е", "rpgdemesa": "rpgдемеса", "worldofdarkness": "світтемряви", "travellerttrpg": "мандрівникттрпг", "2300ad": "2300нм", "larp": "лар<PERSON>", "romanceclub": "романтичнийклуб", "d20": "д20", "pokemongames": "покемондодатки", "pokemonmysterydungeon": "покемономистичнаулогія", "pokemonlegendsarceus": "покемонлегендиарсеус", "pokemoncrystal": "покемонкристал", "pokemonanime": "покемонастік", "pokémongo": "покемонофан", "pokemonred": "покемондобрий", "pokemongo": "покемонофан", "pokemonshowdown": "покемонтусовка", "pokemonranger": "покемонтренер", "lipeep": "лі<PERSON><PERSON><PERSON>", "porygon": "поригон", "pokemonunite": "покемонунайт", "entai": "ентай", "hypno": "гипно", "empoleon": "емпoleon", "arceus": "арце<PERSON>с", "mewtwo": "mewtwo", "paldea": "палдея", "pokemonscarlet": "покемонсяйво", "chatot": "чатот", "pikachu": "піка<PERSON>у", "roxie": "roxie", "pokemonviolet": "покемоновихілка", "pokemonpurpura": "покемондоджі", "ashketchum": "ашкечум", "gengar": "генгар", "natu": "нату", "teamrocket": "командаракета", "furret": "фуррет", "magikarp": "маг<PERSON><PERSON><PERSON><PERSON><PERSON>", "mimikyu": "мімікью", "snorlax": "снорлакс", "pocketmonsters": "покемонивки", "nuzlocke": "нузлока", "pokemonplush": "покемонтедді", "teamystic": "команда<PERSON>стинна", "pokeball": "покебол", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "покемонтворення", "pubgmobile": "pubgmobile", "litten": "літтен", "shinypokemon": "блискучіпокемони", "mesprit": "месприт", "pokémoni": "покемони", "ironhands": "залізніруки", "kabutops": "кабутопс", "psyduck": "псайдак", "umbreon": "умбреон", "pokevore": "покевор", "ptcg": "птчг", "piplup": "піплуп", "pokemonsleep": "покемонсон", "heyyoupikachu": "геййоу_pікачу", "pokémonmaster": "покемономайстер", "pokémonsleep": "покемонсон", "kidsandpokemon": "дітиіпокемони", "pokemonsnap": "покемонснап", "bulbasaur": "бульбозавр", "lucario": "лукаріо", "charizar": "чаризар", "shinyhunter": "бліскукамовець", "ajedrez": "шахи", "catur": "катур", "xadrez": "шахи", "scacchi": "шахи", "schaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skak": "скука", "ajedres": "шахи", "chessgirls": "шахістки", "magnuscarlsen": "магнускарлсен", "worldblitz": "світбліц", "jeudéchecs": "шахи", "japanesechess": "японськішахи", "chinesechess": "китайськішахи", "chesscanada": "шахиканада", "fide": "файде", "xadrezverbal": "шаховасловесність", "openings": "відкриття", "rook": "рук", "chesscom": "шахиком", "calabozosydragones": "калабозосидрагонес", "dungeonsanddragon": "підземеллятадракони", "dungeonmaster": "владикатакожі", "tiamat": "тіамат", "donjonsetdragons": "донжонитаведмеді", "oxventure": "оксвертура", "darksun": "темносонце", "thelegendofvoxmachina": "легендапровоксмашину", "doungenoanddragons": "доўгеножкитадракони", "darkmoor": "темниймур", "minecraftchampionship": "чемпіонатмайнкрафт", "minecrafthive": "майн<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ей", "minecraftbedrock": "minecraftбідрок", "dreamsmp": "дримснп", "hermitcraft": "герметкра<PERSON>т", "minecraftjava": "майнкрафтджава", "hypixelskyblock": "гипіксельскайблок", "minetest": "мінестрес", "hypixel": "гупіксель", "karmaland5": "кармаланд5", "minecraftmods": "майнкрафтмоди", "mcc": "мцц", "candleflame": "вогниксвічки", "fru": "фру", "addons": "додатки", "mcpeaddons": "доповненняmcpe", "skyblock": "небеснийблок", "minecraftpocket": "майнкрафтпокет", "minecraft360": "майнкрафт360", "moddedminecraft": "модденомайнкрафт", "minecraftps4": "майнкрафтпс4", "minecraftpc": "майнкрафтпк", "betweenlands": "міжземлі", "minecraftdungeons": "майнкрафтпідземелля", "minecraftcity": "майнкрафтмісто", "pcgamer": "пкгеймер", "jeuxvideo": "ігри", "gambit": "гембіт", "gamers": "геймери", "levelup": "піднімисьнавище", "gamermobile": "геймермобіль", "gameover": "граскінчилась", "gg": "гг", "pcgaming": "пкгеймерство", "gamen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunoynamak": "грайразом", "pcgames": "пкігри", "casualgaming": "казуальніігри", "gamingsetup": "ігр<PERSON><PERSON>на<PERSON><PERSON>р", "pcmasterrace": "пкмайстерклас", "pcgame": "пкгра", "gamerboy": "геймербой", "vrgaming": "віртуальнеграйання", "drdisrespect": "докторнедозволений", "4kgaming": "4кг<PERSON><PERSON><PERSON><PERSON>нг", "gamerbr": "геймерб", "gameplays": "геймплеї", "consoleplayer": "консоліст", "boxi": "бокси", "pro": "про", "epicgamers": "епічнігеймери", "onlinegaming": "онлайнігри", "semigamer": "півгеймера", "gamergirls": "геймерки", "gamermoms": "геймермами", "gamerguy": "геймерчувак", "gamewatcher": "геймвачер", "gameur": "геймер", "grypc": "грипк", "rangugamer": "рангюгеймер", "gamerschicas": "геймершички", "otoge": "отоге", "dedsafio": "дедсаліфо", "teamtryhard": "командапрацюєнаповну", "mallugaming": "малю<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "pawgers": "павжери", "quests": "квести", "alax": "алекс", "avgn": "срібло", "oldgamer": "стар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "cozygaming": "затишніігри", "gamelpay": "gamelpay", "juegosdepc": "ігридляпк", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "конкурентніігри", "minecraftnewjersey": "майнкр<PERSON>фтньюджерсі", "faker": "фейкер", "pc4gamers": "пкдлягеймерів", "gamingff": "ігровийфф", "yatoro": "ятороблю", "heterosexualgaming": "гетеросексуальніігри", "gamepc": "ігровийпк", "girlsgamer": "дівчатаігроманки", "fnfmods": "фнфмоди", "dailyquest": "щоденнийквест", "gamegirl": "ігровадівчина", "chicasgamer": "дівчата<PERSON>громани", "gamesetup": "ігроваінсталяція", "overpowered": "перевант<PERSON>жений", "socialgamer": "соцгеймер", "gamejam": "ігровийджем", "proplayer": "програвець", "roleplayer": "ролеплеєр", "myteam": "mоякоманда", "republicofgamers": "республікагеймерів", "aorus": "аорус", "cougargaming": "куга<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>г", "triplelegend": "триплегенда", "gamerbuddies": "геймерськідрузяки", "butuhcewekgamers": "нужнеківядлягеймерів", "christiangamer": "християнськийгеймер", "gamernerd": "геймер<PERSON><PERSON>", "nerdgamer": "нрдгеймер", "afk": "в<PERSON><PERSON><PERSON>", "andregamer": "андрег<PERSON>ймер", "casualgamer": "повсякденнийгеймер", "89squad": "89команда", "inicaramainnyagimana": "наполігон<PERSON>бoo", "insec": "незачепи", "gemers": "геймери", "oyunizlemek": "дивитисяігри", "gamertag": "геймерський_псевдонім", "lanparty": "лан<PERSON><PERSON><PERSON><PERSON>і", "videogamer": "ігроман", "wspólnegranie": "спільнагра", "mortdog": "мортдог", "playstationgamer": "плейстейшнгеймер", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "здороваігроманія", "gtracing": "gtracing", "notebookgamer": "ноутбукгеймер", "protogen": "протоген", "womangamer": "жінкагеймер", "obviouslyimagamer": "очевидноігроман", "mario": "марио", "papermario": "папермаріо", "mariogolf": "мариогольф", "samusaran": "самусаран", "forager": "дляжиття", "humanfallflat": "людинизколотитися", "supernintendo": "супернітендо", "nintendo64": "нінтендо64", "zeroescape": "нулевівтеча", "waluigi": "валуїджі", "nintendoswitch": "нінтендосвітч", "nintendosw": "нінтендоsw", "nintendomusic": "нітендомузика", "sonicthehedgehog": "сонікпоросенчик", "sonic": "сонік", "fallguys": "осінніпацанчики", "switch": "поміняти", "zelda": "зелда", "smashbros": "смішнібрати", "legendofzelda": "легендарнезельда", "splatoon": "сплатун", "metroid": "метроид", "pikmin": "пікмін", "ringfit": "кігтіспортивноїгри", "amiibo": "амібо", "megaman": "мегамен", "majorasmask": "маскамаєра", "mariokartmaster": "мариокартмайстер", "wii": "вії", "aceattorney": "асистентюриста", "ssbm": "ssbm", "skychildrenofthelight": "небеснідітисвітла", "tomodachilife": "томодач<PERSON>лейф", "ahatintime": "аха<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "сльозикоролівства", "walkingsimulators": "симуляторипрогулянок", "nintendogames": "нітендогри", "thelegendofzelda": "легендапрозельду", "dragonquest": "драконячаподорож", "harvestmoon": "урожайнаямісяць", "mariobros": "мариоброс", "runefactory": "рунівиробництво", "banjokazooie": "банжоказуїє", "celeste": "целе́стé", "breathofthewild": "диханнядикоїприроди", "myfriendpedro": "мійдругпедро", "legendsofzelda": "легендипрозельду", "donkeykong": "донкіконг", "mariokart": "мариокарт", "kirby": "кірбі", "51games": "51ігри", "earthbound": "земляний", "tales": "казки", "raymanlegends": "рейманлегенди", "luigismansion": "люгісімansion", "animalcrosssing": "анімал<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "taikonotatsujin": "таіконататсудзін", "nintendo3ds": "nintendo3ds", "supermariobros": "супермаріобратки", "mariomaker2": "mariomaker2", "boktai": "бок<PERSON>ай", "smashultimate": "смигайвсесвіт", "nintendochile": "nintendouchile", "tloz": "тлоz", "trianglestrategy": "трикутнастратегія", "supermariomaker": "супермаріомейкер", "xenobladechronicles3": "ксеноблейдхронікс3", "supermario64": "супермаріо64", "conkersbadfurday": "конкерипоганіодежі", "nintendos": "нінтендо", "new3ds": "новий3дс", "donkeykongcountry2": "донкіконгкраїна2", "hyrulewarriors": "героїг<PERSON><PERSON><PERSON><PERSON>у", "mariopartysuperstars": "мариопартісуперзірки", "marioandsonic": "маріотастонік", "banjotooie": "ба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogs": "нітєндогс", "thezelda": "зельда", "palia": "палія", "marioandluigi": "маріо<PERSON>люїджі", "mariorpg": "mariorpg", "zeldabotw": "зельдаботв", "yuumimain": "юумімейн", "wildrift": "вайлдрифту", "riven": "ривен", "ahri": "ахр<PERSON>", "illaoi": "іллаої", "aram": "арам", "cblol": "цблол", "leagueoflegendslas": "лігалегендлас", "urgot": "ургот", "zyra": "зіра", "redcanids": "червоніканіди", "vanillalol": "ванількалол", "wildriftph": "вайлдрифтпіа", "lolph": "лолпф", "leagueoflegend": "лігалегенд", "tốcchiến": "топчений", "gragas": "грагас", "leagueoflegendswild": "лігавlegendівдикий", "adcarry": "адкари", "lolzinho": "лоловчик", "leagueoflegendsespaña": "лігафlegendіванія", "aatrox": "атрокс", "euw": "еуі", "leagueoflegendseuw": "лігалегендевро", "kayle": "кейл", "samira": "саміра", "akali": "акалі", "lunari": "лу<PERSON><PERSON><PERSON>", "fnatic": "фна<PERSON>ік", "lollcs": "лолцс", "akshan": "ак<PERSON>н", "milio": "міліо", "shaco": "шако", "ligadaslegendas": "повязанізлегендами", "gaminglol": "геймерство", "nasus": "насус", "teemo": "тімо", "zedmain": "зедмейн", "hexgates": "хексворота", "hextech": "хекстек", "fortnitegame": "фортнайтгра", "gamingfortnite": "гоймингфорнайт", "fortnitebr": "фортнайтбр", "retrovideogames": "ретровідеоігри", "scaryvideogames": "страшніігри", "videogamemaker": "дизайн<PERSON>р<PERSON>гор", "megamanzero": "мегаменнуль", "videogame": "видеогра", "videosgame": "ігровівабірки", "professorlayton": "професорлейтон", "overwatch": "оверва<PERSON>у", "ow2": "ow2", "overwatch2": "овервач2", "wizard101": "чарівник101", "battleblocktheater": "бійдосідловоготеатру", "arcades": "аркади", "acnh": "акнх", "puffpals": "пухнастики", "farmingsimulator": "сіміляторфермерства", "robloxchile": "роблоксчілє", "roblox": "роблокс", "robloxdeutschland": "роблокснімеччина", "robloxdeutsch": "robloxукраїнською", "erlc": "erlc", "sanboxgames": "пісочницянеможливостей", "videogamelore": "ігровіінфо", "rollerdrome": "роллердром", "parasiteeve": "паразітнавечірка", "gamecube": "геймкюб", "starcraft2": "старкрафт2", "duskwood": "дусквуд", "dreamscape": "дримскейп", "starcitizen": "зірковийгромадянин", "yanderesimulator": "яндересимулятор", "grandtheftauto": "грандіозневикраденняавто", "deadspace": "мертвийпростір", "amordoce": "солодкал<PERSON><PERSON>ов", "videogiochi": "ігри", "theoldrepublic": "старореспубліка", "videospiele": "ігри", "touhouproject": "touhouпроект", "dreamcast": "дримкаст", "adventuregames": "пригодиігри", "wolfenstein": "вольфенштайн", "actionadventure": "екшнпригоди", "storyofseasons": "історіїсезонів", "retrogames": "ретрогри", "retroarcade": "ретроаркейд", "vintagecomputing": "вінтажнеобчислення", "retrogaming": "ретрогеймінг", "vintagegaming": "вінтажніігри", "playdate": "теплосидіння", "commanderkeen": "коман<PERSON><PERSON><PERSON><PERSON><PERSON>н", "bugsnax": "багзнак", "injustice2": "несправедливість2", "shadowthehedgehog": "тіньовийїжак", "rayman": "рейман", "skygame": "небогоргри", "zenlife": "дзенжиття", "beatmaniaiidx": "бітманія<PERSON><PERSON><PERSON>д", "steep": "круто", "mystgames": "mystgames", "blockchaingaming": "блокчейнгейминг", "medievil": "середньовіччя", "consolegaming": "консольніігор", "konsolen": "консолі", "outrun": "втекти", "bloomingpanic": "блум<PERSON><PERSON>чийпанік", "tobyfox": "тобіфокс", "hoyoverse": "hoyoverse", "senrankagura": "сенранкагура", "gaminghorror": "геймінгжахіття", "monstergirlquest": "монстрдевочоквест", "supergiant": "суперг<PERSON><PERSON>ант", "disneydreamlightvalle": "диснейсвітлотіней", "farmingsims": "фермерськісимулятори", "juegosviejos": "старіігри", "bethesda": "бетесда", "jackboxgames": "jackboxігри", "interactivefiction": "інтерактивнафантастика", "pso2ngs": "pso2ngs", "grimfandango": "грімфанденго", "thelastofus2": "останнізнас2", "amantesamentes": "любовемислення", "visualnovel": "візуальнановела", "visualnovels": "візуальніромани", "rgg": "ргг", "shadowolf": "тін<PERSON><PERSON>овк", "tcrghost": "tcrджghost", "payday": "пpayday", "chatherine": "катеринка", "twilightprincess": "світанковапринцеса", "jakandaxter": "дж<PERSON><PERSON><PERSON>ндайстер", "sandbox": "пісочниця", "aestheticgames": "естетичніігри", "novelavisual": "новелавізуал", "thecrew2": "команда2", "alexkidd": "алекскід", "retrogame": "ретрогра<PERSON><PERSON>д", "tonyhawkproskater": "тоніхокпроскейтер", "smbz": "smbz", "lamento": "ламенту", "godhand": "божественнарука", "leafblowerrevolution": "революція_пускового_вітра", "wiiu": "віїу", "leveldesign": "дизайнеріврівнів", "starrail": "зорянийпотяг", "keyblade": "ключова_меча", "aplaguetale": "аплагуймене", "fnafsometimes": "фнафіноді", "novelasvisuales": "візуальніромани", "robloxbrasil": "роблоксбразил", "pacman": "пакмен", "gameretro": "геймретро", "videojuejos": "видеогри", "videogamedates": "ігровідати", "mycandylove": "мояцукровалюбов", "megaten": "мегатен", "mortalkombat11": "mortalcombat11", "everskies": "еврескаїз", "justcause3": "простотомущо3", "hulkgames": "халкгеймс", "batmangames": "батманігри", "returnofreckoning": "поверненнярозплати", "gamstergaming": "геймериг<PERSON>йминг", "dayofthetantacle": "деньтарантула", "maniacmansion": "маніякоселщина", "crashracing": "крутоїгонки", "3dplatformers": "3дплатформи", "nfsmw": "нфсму", "kimigashine": "кімідо<PERSON>світ", "oldschoolgaming": "олдскульніігри", "hellblade": "пекельн<PERSON><PERSON><PERSON>ч", "storygames": "історіягри", "bioware": "біоваре", "residentevil6": "резидентівіл6", "soundodger": "звуковийдоджер", "beyondtwosouls": "поза_двома_душами", "gameuse": "гравец<PERSON>нь", "offmortisghost": "оффмортискостяка", "tinybunny": "маленькийкролик", "retroarch": "ретроарх", "powerup": "підзарядка", "katanazero": "катаназеро", "famicom": "фамікон", "aventurasgraficas": "графічніпригоди", "quickflash": "швидкеясике", "fzero": "фзеро", "gachagaming": "гачаігри", "retroarcades": "ретроарткеди", "f123": "f123", "wasteland": "пустка", "powerwashsim": "powerwashсим", "coralisland": "коральнийострів", "syberia3": "сибір3", "grymmorpg": "гримморпг", "bloxfruit": "блокфрути", "anotherworld": "ін<PERSON><PERSON>світ", "metaquest": "метаquest", "animewarrios2": "анімевоїни2", "footballfusion": "футбольнийфюжн", "edithdlc": "едидлц", "abzu": "абзу", "astroneer": "астронір", "legomarvel": "легошедеври", "wranduin": "вранд<PERSON><PERSON><PERSON>н", "twistedmetal": "крутизнякметал", "beamngdrive": "beamngdrive", "twdg": "тводг", "pileofshame": "купаприниження", "simulator": "симулятор", "symulatory": "симулятори", "speedrunner": "спідра<PERSON><PERSON>р", "epicx": "епікс", "superrobottaisen": "суперроботайсен", "dcuo": "дцуо", "samandmax": "самітаммакс", "grywideo": "грайвидео", "gaiaonline": "гайа<PERSON><PERSON><PERSON><PERSON><PERSON>н", "korkuoyunu": "коркюойну", "wonderlandonline": "чудеснийсвітонлайн", "skylander": "скайлендер", "boyfrienddungeon": "бойфрендпідземелля", "toontownrewritten": "тунтаунпереписаний", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "пвп", "urbanchaos": "урбаністичнийхаос", "heavenlybodies": "небеснітіла", "seum": "сеум", "partyvideogames": "вічіркаігри", "graveyardkeeper": "доглядачпоховань", "spaceflightsimulator": "симуляторкосмічнихпольотів", "legacyofkain": "спадщинакейна", "hackandslash": "хакуйтабий", "foodandvideogames": "їжаіігри", "oyunvideoları": "ігровівідео", "thewolfamongus": "вовкипоміжнас", "truckingsimulator": "тракінгсимулятор", "horizonworlds": "горизонтівсвіту", "handygame": "хендз<PERSON>г<PERSON><PERSON>м", "leyendasyvideojuegos": "легендиігровогосвіту", "oldschoolvideogames": "олдвскульніігри", "racingsimulator": "симуляторгонок", "beemov": "<PERSON><PERSON><PERSON><PERSON>", "agentsofmayhem": "агентипекла", "songpop": "пісняпоп", "famitsu": "фамітсу", "gatesofolympus": "воротаолімпу", "monsterhunternow": "монстрокиївтепер", "rebelstar": "бунтівназоря", "indievideogaming": "індіігри", "indiegaming": "індіге<PERSON><PERSON>инг", "indievideogames": "індіігри", "indievideogame": "індіігри", "chellfreeman": "ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spidermaninsomniac": "людиспайдераінсомніак", "bufffortress": "багатирськафортеця", "unbeatable": "незрівнянний", "projectl": "проектл", "futureclubgames": "майбутнєклубніігри", "mugman": "чоловікзкружкою", "insomniacgames": "безсонняігри", "supergiantgames": "супергігантигри", "henrystickman": "хенрістікмен", "henrystickmin": "генристрікмін", "celestegame": "целестегра", "aperturescience": "апертурнанаука", "backlog": "беклог", "gamebacklog": "ігровийвідкладенийсписок", "gamingbacklog": "ігринапізніше", "personnagejeuxvidéos": "персонажіігри", "achievementhunter": "мисливецьнадосягнення", "cityskylines": "містоградофіки", "supermonkeyball": "супермавпячийкулька", "deponia": "депонія", "naughtydog": "непокірнийсобака", "beastlord": "звірокнязь", "juegosretro": "ретроигри", "kentuckyroutezero": "кентукійськиймаршрутнуль", "oriandtheblindforest": "оріанда<PERSON><PERSON>гл<PERSON>хлісів", "alanwake": "аланвейк", "stanleyparable": "стенліпаребель", "reservatoriodedopamin": "резервуардопаміну", "staxel": "стаксель", "videogameost": "відеогеймоваost", "dragonsync": "дракон<PERSON>инк", "vivapiñata": "живипіната", "ilovekofxv": "ялюблюкӯфxv", "arcanum": "арканум", "neoy2k": "нео2к", "pcracing": "пцрей<PERSON>инг", "berserk": "підбешкити", "baki": "баки", "sailormoon": "сейлормун", "saintseiya": "сейнтсейя", "inuyasha": "інуяша", "yuyuhakusho": "ююхакушо", "initiald": "перш<PERSON>д", "elhazard": "елхазард", "dragonballz": "драго<PERSON>бо<PERSON>з", "sadanime": "сумнеаніме", "darkerthanblack": "чорнішийзачорного", "animescaling": "анімескейлінг", "animewithplot": "анімешказсюжетом", "pesci": "песці", "retroanime": "ретроаніме", "animes": "аніме", "supersentai": "суперсентай", "samuraichamploo": "самурайкамплу", "madoka": "мадока", "higurashi": "х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "80sanime": "80сантиме", "90sanime": "90сортиментаніме", "darklord": "темнийволодар", "popeetheperformer": "попітейперформер", "masterpogi": "майстерпоги", "samuraix": "самура<PERSON>х", "dbgt": "дбгт", "veranime": "вераніме", "2000sanime": "аніме2000х", "lupiniii": "луп<PERSON>н<PERSON><PERSON>", "drstoneseason1": "докторстоунсезон1", "rapanime": "рапаніме", "chargemanken": "заряджуймене", "animecover": "анімековер", "thevisionofescaflowne": "візіяескафлони", "slayers": "слейєри", "tokyomajin": "токійськимаджином", "anime90s": "аніме90х", "animcharlotte": "ані<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ка", "gantz": "ганц", "shoujo": "шоджо", "bananafish": "банановариба", "jujutsukaisen": "джу<PERSON>утсукайсен", "jjk": "дж<PERSON><PERSON>", "haikyu": "х<PERSON><PERSON><PERSON><PERSON>", "toiletboundhanakokun": "туалетнаяханако", "bnha": "бнха", "hellsing": "хелл<PERSON><PERSON>нг", "skipbeatmanga": "скіпбітманга", "vanitas": "ванітас", "fireforce": "пожежнасила", "moriartythepatriot": "морартитепатриот", "futurediary": "щоденникмайбутнього", "fairytail": "казковийхвіст", "dorohedoro": "дорохедорo", "vinlandsaga": "вілландсага", "madeinabyss": "зробленовабісі", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "шінгекінокйодзін", "mushishi": "му<PERSON><PERSON><PERSON>і", "beastars": "бістари", "vanitasnocarte": "ванітаснокарте", "mermaidmelody": "русалчинапісня", "kamisamakiss": "камі<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>с", "blmanga": "блманга", "horrormanga": "жахаманта", "romancemangas": "романтичніманги", "karneval": "карнаваль", "dragonmaid": "драконяча_служниця", "blacklagoon": "чорналагуна", "kentaromiura": "кентароміура", "mobpsycho100": "мобпсихо100", "terraformars": "терраформери", "geniusinc": "ген<PERSON><PERSON><PERSON><PERSON>к", "shamanking": "ша<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>г", "kurokonobasket": "куроконоволейбол", "jugo": "юґо", "bungostraydogs": "бунгострейдоги", "jujustukaisen": "юстукайсен", "jujutsu": "джиujutsu", "yurionice": "юрьонепогано", "acertainmagicalindex": "визначениймагічнийіндекс", "sao": "сао", "blackclover": "чорнийконюшина", "tokyoghoul": "токіогул", "onepunchman": "одинударлюдина", "hetalia": "геталія", "kagerouproject": "кагероувпроект", "haikyuu": "ха<PERSON><PERSON>ю", "toaru": "тоару", "crunchyroll": "кранч<PERSON><PERSON><PERSON><PERSON>л", "aot": "аот", "sk8theinfinity": "скейтбезмежжя", "siriusthejaeger": "сіріустеджейгер", "spyxfamily": "шпигунськісімейнісправи", "rezero": "резетобу", "swordartonline": "мечімистецтвоонлайн", "dororo": "дороро", "wondereggpriority": "приоритетяйцячудес", "angelsofdeath": "анге<PERSON><PERSON><PERSON>мерті", "kakeguri": "кекегурі", "dragonballsuper": "драгонболсупер", "hypnosismic": "гіпносмішик", "goldenkamuy": "золотакамуй", "monstermusume": "монстрмусумэ", "konosuba": "коносуба", "aikatsu": "айкатсу", "sportsanime": "спортивнеаніме", "sukasuka": "сукацука", "arwinsgame": "граве<PERSON><PERSON><PERSON>р<PERSON><PERSON>на", "angelbeats": "янгольськіудари", "isekaianime": "ісекайаніме", "sagaoftanyatheevil": "сагаофтанязла", "shounenanime": "шуненаніме", "bandori": "банд<PERSON><PERSON><PERSON>", "tanya": "таня", "durarara": "дурарa<PERSON>а", "prettycure": "prettycure", "theboyandthebeast": "хлопецьтазвір", "fistofthenorthstar": "кулакпівночноїзірки", "mazinger": "маз<PERSON><PERSON><PERSON><PERSON>р", "blackbuttler": "чорнийбатлер", "towerofgod": "вежабога", "elfenlied": "ельфенлід", "akunohana": "акуноганя", "chibi": "ч<PERSON><PERSON><PERSON>", "servamp": "сервамп", "howtokeepamummy": "якзберегтимамусю", "fullmoonwosagashite": "повнявосагашитэ", "shugochara": "шуговчара", "tokyomewmew": "токіомюмю", "gugurekokkurisan": "гугурекокурісан", "cuteandcreepy": "милашкитастрашненькі", "martialpeak": "марціа<PERSON>ьнашпальта", "bakihanma": "бахінамара", "hiscoregirl": "високийбалгурл", "orochimaru": "орочімару", "mierukochan": "мієрукохан", "dabi": "<PERSON><PERSON><PERSON><PERSON>", "johnconstantine": "дж<PERSON>нконстантін", "astolfo": "астolfo", "revanantfae": "реванантфея", "shinji": "сіндзі", "zerotwo": "нульдва", "inosuke": "іносуке", "nezuko": "незуко", "monstergirl": "монстрянка", "kanae": "канае", "yone": "йоне", "mitsuki": "міцу<PERSON>і", "kakashi": "кака<PERSON>і", "lenore": "ленора", "benimaru": "бенімару", "saitama": "сайтама", "sanji": "сан<PERSON>і", "bakugo": "бакугo", "griffith": "гриффіт", "ririn": "ріриَن", "korra": "кортка", "vanny": "ванни", "vegeta": "вегета", "goromi": "горомі", "luci": "луці", "reigen": "рейген", "scaramouche": "скарамуш", "amiti": "лалал<PERSON><PERSON>у", "sailorsaturn": "морякисатурна", "dio": "діо", "sailorpluto": "морякплутон", "aloy": "алой", "runa": "руна", "oldanime": "стареаніме", "chainsawman": "чоловікзпилою", "bungoustraydogs": "бунгострейдоги", "jogo": "джогопартія", "franziska": "франциска", "nekomimi": "некочимі", "inumimi": "інумімі", "isekai": "ісекай", "tokyorevengers": "токіоревенджери", "blackbutler": "чорнийбетмен", "ergoproxy": "ергопроксі", "claymore": "клеймор", "loli": "лолі", "horroranime": "жаханіме", "fruitsbasket": "коши<PERSON><PERSON>р<PERSON><PERSON><PERSON><PERSON>в", "devilmancrybaby": "дьяволоманкриконтур", "noragami": "норагамі", "mangalivre": "mangalivre", "kuroshitsuji": "куро<PERSON><PERSON><PERSON>у<PERSON><PERSON>і", "seinen": "сейнен", "lovelive": "живи_коханням", "sakuracardcaptor": "сакуракарткепер", "umibenoetranger": "умібеноетран<PERSON><PERSON>р", "owarinoseraph": "овариносереф", "thepromisedneverland": "обіцяназемля", "monstermanga": "монстрмультаки", "yourlieinapril": "твоябрехнявквітні", "buggytheclown": "баг<PERSON><PERSON><PERSON><PERSON><PERSON>н", "bokunohero": "бокунохіро", "seraphoftheend": "серафкінця", "trigun": "тригун", "cyborg009": "кіборг009", "magi": "магія", "deepseaprisoner": "глибоководнийприкарний", "jojolion": "йоджолліон", "deadmanwonderland": "мертвийчоловікстранадив", "bannafish": "ба<PERSON><PERSON><PERSON><PERSON>", "sukuna": "сукуна", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "хасбу", "sugurugeto": "сугурогетто", "leviackerman": "левіаккерман", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "пандорасердець", "yoimiya": "йоміях", "foodwars": "їднаявойна", "cardcaptorsakura": "карточнібояркисакура", "stolas": "столос", "devilsline": "диявольськалінія", "toyoureternity": "довашоївічності", "infpanime": "інфпаніме", "eleceed": "елісід", "akamegakill": "акамеґакіл", "blueperiod": "синійперіод", "griffithberserk": "ґріффітбожевільний", "shinigami": "шинига<PERSON>і", "secretalliance": "секретнаальянс", "mirainikki": "міра<PERSON>ік<PERSON>і", "mahoutsukainoyome": "махотсукайнойоме", "yuki": "юкі", "erased": "стерті", "bluelock": "блулак", "goblinslayer": "гоблінубивця", "detectiveconan": "детективкона", "shiki": "шики", "deku": "депіки", "akitoshinonome": "акітосінономе", "riasgremory": "ріасгреморі", "shojobeat": "шойоб<PERSON>т", "vampireknight": "вампірськийлицар", "mugi": "мужі", "blueexorcist": "блакитнийекзорцист", "slamdunk": "сламданк", "zatchbell": "зачебел", "mashle": "машле", "scryed": "загадали", "spyfamily": "шпигунськаясімя", "airgear": "повітряніколеса", "magicalgirl": "маг<PERSON>ч<PERSON>ді<PERSON><PERSON>ина", "thesevendeadlysins": "сімсмертнихгріхів", "prisonschool": "школавязниці", "thegodofhighschool": "богшколи", "kissxsis": "поцілунки_та_сестри", "grandblue": "грандіозно", "mydressupdarling": "моялялькадляодягу", "dgrayman": "дгр<PERSON><PERSON><PERSON><PERSON>н", "rozenmaiden": "rozenмайдан", "animeuniverse": "анімеюніверсум", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "саоабриджений", "hoshizora": "хошізора", "dragonballgt": "драгонболлгт", "bocchitherock": "бокчирок", "kakegurui": "какеґурі", "mobpyscho100": "мобпсихо100", "hajimenoippo": "першийкрок", "undeadunluck": "неклороганнедфарт", "romancemanga": "романтичнеманга", "blmanhwa": "блманхва", "kimetsunoyaba": "кимецунояба", "kohai": "кохай", "animeromance": "анімеро<PERSON><PERSON>нс", "senpai": "сенпай", "blmanhwas": "блманхва", "animeargentina": "анімеаргентина", "lolicon": "лолікон", "demonslayertothesword": "демонубивцядослова", "bloodlad": "кровнийбрат", "goodbyeeri": "прощавайери", "firepunch": "вогнянийпpunch", "adioseri": "бувайсері", "tatsukifujimoto": "тосукіфудзімото", "kinnikuman": "кінн<PERSON><PERSON>у<PERSON>ан", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "девочкалайф", "starsalign": "зіркипадають", "romanceanime": "романтичнеаніме", "tsundere": "цундере", "yandere": "яндере", "mahoushoujomadoka": "магузоджомадока", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "саоінтегральнийфактор", "cherrymagic": "вишневамагія", "housekinokuni": "домакинокраїна", "recordragnarok": "записуйраґнарок", "oyasumipunpun": "оясуміпунпун", "meliodas": "меліодас", "fudanshi": "фуда<PERSON><PERSON>і", "retromanga": "ретроманга", "highschoolofthedead": "школамертвих", "germantechno": "німецькотехно", "oshinoko": "ошиноко", "ansatsukyoushitsu": "анццукиошицю", "vindlandsaga": "вінландсага", "mangaka": "мангака", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "принцтен<PERSON><PERSON>у", "tonikawa": "тонікава", "esdeath": "есдед", "dokurachan": "докурачан", "bjalex": "бяалекс", "assassinclassroom": "класвбивць", "animemanga": "анімеманга", "bakuman": "бакуман", "deathparade": "смертельнийпарад", "shokugekinosouma": "шокугекиносума", "japaneseanime": "японськеаніме", "animespace": "анімепростір", "girlsundpanzer": "дівчата_під_танками", "akb0048": "акб0048", "hopeanuoli": "надіянаулиці", "animedub": "анімедуб", "animanga": "аніманга", "tsurune": "цуруне", "uqholder": "uqholder", "indieanime": "індіаніме", "bungoustray": "бунгос<PERSON><PERSON><PERSON>й", "dagashikashi": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "гандом0", "animescifi": "ан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratman": "ратмен", "haremanime": "хареманіме", "kochikame": "kochikame", "nekoboy": "некобой", "gashbell": "гашбелл", "peachgirl": "персикова_дівчина", "cavalieridellozodiaco": "кавалериозодіаку", "mechamusume": "мехамусумe", "nijigasaki": "ніджикасаки", "yarichinbitchclub": "яричи<PERSON>б<PERSON>чклаб", "dragonquestdai": "драгонквестдай", "heartofmanga": "серцяманги", "deliciousindungeon": "смачнезавданні", "manhviyaoi": "манхв<PERSON>я<PERSON>й", "recordofragnarok": "записрагнароку", "funamusea": "веселаамагона", "hiranotokagiura": "хіратанокагіура", "mangaanime": "мангааніме", "bochitherock": "бочитерак", "kamisamahajimemashita": "камісамагахімемашита", "skiptoloafer": "пропустислофера", "shuumatsunovalkyrie": "шумат<PERSON>уновалькірія", "tutorialistoohard": "навітьвчителіскладні", "overgeared": "перегружений", "toriko": "торіко", "ravemaster": "рейвмайстер", "kkondae": "ккондае", "chobits": "чобіти", "witchhatatelier": "крамницячаклунськихкапелюхів", "lansizhui": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангацуноліон", "kamen": "камінь", "mangaislife": "mangaєжиття", "dropsofgod": "краплібога", "loscaballerosdelzodia": "лицарізодіа<PERSON>у", "animeshojo": "анімешодзьо", "reverseharem": "реверсгадо", "saintsaeya": "сейнтсеея", "greatteacheronizuka": "класнийвчительонідзука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "солдат", "mybossdaddy": "мійбостато", "gear5": "gear5", "grandbluedreaming": "великоголубоснуєш", "bloodplus": "кровплюс", "bloodplusanime": "кровьплюсаніме", "bloodcanime": "кровавеаніме", "bloodc": "кровюшка", "talesofdemonsandgods": "казкипdemonітабогів", "goreanime": "горошкінеаніме", "animegirls": "ані<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "шарінґан", "crowsxworst": "вороникхудші", "splatteranime": "сплatterаніме", "splatter": "плювок", "risingoftheshieldhero": "відродженнящитовогогероя", "somalianime": "соматане", "riodejaneiroanime": "ріодежанейроаніме", "slimedattaken": "slimedatakesенс", "animeyuri": "аніме<PERSON><PERSON>і", "animeespaña": "анімеіспанія", "animeciudadreal": "анімесіудадреал", "murim": "мурим", "netjuunosusume": "нетюносуми", "childrenofthewhales": "діт<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "брехунябрехуня", "supercampeones": "суперчемпіони", "animeidols": "анімеідоли", "isekaiwasmartphone": "ісекайзсмартфоном", "midorinohibi": "мідо<PERSON><PERSON>нохіби", "magicalgirls": "чарівнідіви", "callofthenight": "закликночі", "bakuganbrawler": "бакуга́нбравлер", "bakuganbrawlers": "бакугамбравлери", "natsuki": "натсукі", "mahoushoujo": "чарівнадівчина", "shadowgarden": "тіньовийсад", "tsubasachronicle": "цусабахроніки", "findermanga": "знайдімультики", "princessjellyfish": "принцесажелейка", "kuragehime": "куреджиме", "paradisekiss": "поцілунокраю", "kurochan": "кура<PERSON>ан", "revuestarlight": "ревюззірковесяйво", "animeverse": "анімевселенна", "persocoms": "персональнікомпютери", "omniscientreadersview": "всезнаючийпоглядчитача", "animecat": "анімекіт", "animerecommendations": "анімепоради", "openinganime": "відкриттяаніме", "shinichirowatanabe": "шин<PERSON><PERSON><PERSON>роватанабе", "uzumaki": "узумакі", "myteenromanticcomedy": "мійпідлітковийромантичнийкомедійнийфільм", "evangelion": "евангеліон", "gundam": "гундами", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гундамс", "voltesv": "вольтесв", "giantrobots": "гігантськіроботи", "neongenesisevangelion": "неонгенезисеванджелійон", "codegeass": "кодгеас", "mobilefighterggundam": "мобільнийбоецьггундам", "neonevangelion": "неоновангеліон", "mobilesuitgundam": "мобільнийкостюмгандама", "mech": "меч", "eurekaseven": "єврикасімка", "eureka7": "юрека7", "thebigoanime": "бигоніми", "bleach": "відб<PERSON>л<PERSON><PERSON><PERSON>ч", "deathnote": "смертнийблокнот", "cowboybebop": "кобоєбоб", "jjba": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojosbizarreadventure": "дивнапригодаджоджо", "fullmetalalchemist": "повнийметалевийалхімік", "ghiaccio": "лід", "jojobizarreadventures": "пригодибізаруджобо", "kamuiyato": "камуйято", "militaryanime": "мілтар<PERSON>аніме", "greenranger": "зеленопояс", "jimmykudo": "джиммікудо", "tokyorev": "токіорев", "zorro": "зорро", "leonscottkennedy": "леонскотткеннеді", "korosensei": "коросенсей", "starfox": "зорянийлисиця", "ultraman": "ультраман", "salondelmanga": "салондельманги", "lupinthe3rd": "лу<PERSON><PERSON>нт<PERSON><PERSON><PERSON><PERSON>й", "animecity": "анімемісто", "animetamil": "анімета<PERSON><PERSON>л", "jojoanime": "йоджоаніме", "naruto": "нарутo", "narutoshippuden": "нарутосhipпуден", "onepiece": "одинкодунок", "animeonepiece": "анімеодинчастина", "dbz": "дб<PERSON>", "dragonball": "драгонбол", "yugioh": "югіо", "digimon": "діг<PERSON>е<PERSON>он", "digimonadventure": "диджімонпригоди", "hxh": "ххх", "highschooldxd": "школадиядевчат", "goku": "goku", "broly": "броулі", "shonenanime": "шоненаніме", "bokunoheroacademia": "бокуногероякадемія", "jujustukaitsen": "щойноукраїнців", "drstone": "докторстоун", "kimetsunoyaiba": "кимецунаояйба", "shonenjump": "шоненджамп", "otaka": "отака", "hunterxhunter": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "mha": "мха", "demonslayer": "вбивцидемонів", "hinokamikagurademonsl": "хінокамікагурадемонсл", "attackontitan": "ата<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "еренеґер", "myheroacademia": "мійгеройакадемія", "boruto": "боруто", "rwby": "rwby", "dandadan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachigame": "томодачігра", "akatsuki": "акатсукі", "surveycorps": "опитувальнабригада", "onepieceanime": "анімеодинчастина", "attaquedestitans": "атак<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "одинпєсісправжній", "revengers": "вендета", "mobpsycho": "мобпсихо", "aonoexorcist": "аоноекзорцист", "joyboyeffect": "ефектщасливчика", "digimonstory": "дигімоністорія", "digimontamers": "диджитомонтери", "superjail": "суперкатівня", "metalocalypse": "металокаліпсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "ватамоте", "uramichioniisan": "урамичіон<PERSON>сан", "uruseiyatsura": "урусеіятура", "gintama": "гінтама", "ranma": "ранма", "doraemon": "дораемон", "gto": "гто", "ouranhostclub": "нашанхостклуб", "flawlesswebtoon": "бездоганнийвебтун", "kemonofriends": "кемонодрузів", "utanoprincesama": "утанопринцесами", "animecom": "анімеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юукюйуна", "nichijou": "нічніпригоди", "yurucamp": "юрукемп", "nonnonbiyori": "ноннобійорі", "flyingwitch": "літаючавідьма", "wotakoi": "вотакаі", "konanime": "конаніме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "простотак", "horimiya": "горімія", "allsaintsstreet": "всіхсвятихвулиця", "recuentosdelavida": "перепискажиття"}