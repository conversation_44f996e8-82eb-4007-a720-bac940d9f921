{"2048": "2048", "mbti": "мбти", "enneagram": "енеаграма", "astrology": "астрология", "cognitivefunctions": "когнитивнифункции", "psychology": "психология", "philosophy": "философия", "history": "история", "physics": "физика", "science": "наука", "culture": "култура", "languages": "езици", "technology": "технология", "memes": "мемета", "mbtimemes": "mbtiмемета", "astrologymemes": "астрологиямемета", "enneagrammemes": "енеаграмамемете", "showerthoughts": "мисли", "funny": "смешно", "videos": "видеа", "gadgets": "д<PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "политика", "relationshipadvice": "съветизавръзка", "lifeadvice": "съветзаживот", "crypto": "крипто", "news": "новини", "worldnews": "новиниотсвета", "archaeology": "археология", "learning": "учение", "debates": "дебати", "conspiracytheories": "конспиративнитеории", "universe": "вселена", "meditation": "медитация", "mythology": "митология", "art": "изкуство", "crafts": "занаяти", "dance": "танц", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "грим", "beauty": "красота", "fashion": "мода", "singing": "пеене", "writing": "писане", "photography": "фотография", "cosplay": "косплей", "painting": "рисуване", "drawing": "рисуване", "books": "книги", "movies": "филми", "poetry": "поезия", "television": "телевизия", "filmmaking": "филмография", "animation": "анимация", "anime": "аниме", "scifi": "научнафантастика", "fantasy": "фентъзи", "documentaries": "документалнифилми", "mystery": "мистерия", "comedy": "комедия", "crime": "криминално", "drama": "драма", "bollywood": "боливууд", "kdrama": "кдрама", "horror": "ужаси", "romance": "романтика", "realitytv": "риалититв", "action": "екшън", "music": "музика", "blues": "сини", "classical": "класически", "country": "кънтри", "desi": "деси", "edm": "електроннамузика", "electronic": "електроника", "folk": "народна", "funk": "фънк", "hiphop": "хипхоп", "house": "<PERSON><PERSON><PERSON><PERSON>", "indie": "инди", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "кейпоп", "latin": "латино", "metal": "метъл", "pop": "поп", "punk": "пънк", "rnb": "арендби", "rap": "рап", "reggae": "реге", "rock": "роцк", "techno": "техно", "travel": "пътуване", "concerts": "концерти", "festivals": "фестивали", "museums": "музеи", "standup": "стендъп", "theater": "театър", "outdoors": "природа", "gardening": "градинарство", "partying": "забавляващисе", "gaming": "гейминг", "boardgames": "бордовиигри", "dungeonsanddragons": "подземияидракони", "chess": "шах", "fortnite": "фортнайт", "leagueoflegends": "лигатаналегендите", "starcraft": "старкр<PERSON><PERSON>т", "minecraft": "майнкра<PERSON>т", "pokemon": "покемон", "food": "храна", "baking": "пекарство", "cooking": "готвене", "vegetarian": "вегетарианство", "vegan": "веган", "birds": "птици", "cats": "котки", "dogs": "кучета", "fish": "риба", "animals": "животни", "blacklivesmatter": "чернитеживотииматзначение", "environmentalism": "природозащита", "feminism": "феминизъм", "humanrights": "човешкиправа", "lgbtqally": "лгбт", "stopasianhate": "спретеомразатакъмазиатците", "transally": "транссъюзник", "volunteering": "доброволци", "sports": "спорт", "badminton": "бадминтон", "baseball": "бейзбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "колоездене", "fitness": "фитнес", "football": "футбол", "golf": "голф", "gym": "фитнес", "gymnastics": "гимнастика", "hockey": "хокей", "martialarts": "смесенибойниизкуства", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "бягане", "skateboarding": "скейтбординг", "skiing": "ски", "snowboarding": "сноуборд", "surfing": "сърф", "swimming": "плуване", "tennis": "тенис", "volleyball": "волейбол", "weightlifting": "вдиганенатежести", "yoga": "йога", "scubadiving": "гмуркане", "hiking": "катерене", "capricorn": "козирог", "aquarius": "водолей", "pisces": "риби", "aries": "овен", "taurus": "телец", "gemini": "близнаци", "cancer": "рак", "leo": "лъв", "virgo": "дева", "libra": "везни", "scorpio": "скорпион", "sagittarius": "стрелец", "shortterm": "краткосрочна", "casual": "неангажиращо", "longtermrelationship": "дългот<PERSON><PERSON>йнавръзка", "single": "самотен", "polyamory": "полиамория", "enm": "етичнанемоногамия", "lgbt": "лгбт", "lgbtq": "лг<PERSON><PERSON>к", "gay": "гей", "lesbian": "лесбийка", "bisexual": "бисексуален", "pansexual": "пансексуален", "asexual": "асексуален", "reddeadredemption2": "червенопогребение2", "dragonage": "драконовевъзраст", "assassinscreed": "убийцисредата", "saintsrow": "святизавръзки", "danganronpa": "данганронпа", "deltarune": "дел<PERSON><PERSON><PERSON><PERSON><PERSON>", "watchdogs": "пазителите", "dislyte": "ди<PERSON><PERSON><PERSON><PERSON>т", "rougelikes": "ругелайкс", "kingsquest": "царскотоприключение", "soulreaver": "душепогълвач", "suikoden": "суйкоден", "subverse": "субверс", "legendofspyro": "легендатанаспайро", "rouguelikes": "ругила<PERSON>кс", "syberia": "сибирия", "rdr2": "рдв2", "spyrothedragon": "шпиончетодракон", "dragonsdogma": "драконоваприказка", "sunsetoverdrive": "залезнавитрината", "arkham": "аркхам", "deusex": "деви<PERSON><PERSON><PERSON>", "fireemblemfates": "огненатайнствосъдба", "yokaiwatch": "йокайстwatch", "rocksteady": "рокстеди", "litrpg": "литрпг", "haloinfinite": "халоин<PERSON>инит", "guildwars": "гилдвари", "openworld": "отворенсвят", "heroesofthestorm": "героитевсичкостормове", "cytus": "цитус", "soulslike": "душеподобни", "dungeoncrawling": "изследваненаподземия", "jetsetradio": "джетсетрадио", "tribesofmidgard": "племенатанамидгард", "planescape": "планирайскейп", "lordsoftherealm2": "лордоветенацарствата2", "baldursgate": "балдаурсврат", "colorvore": "цветодвор", "medabots": "медаботи", "lodsoftherealm2": "многоотцарствата2", "patfofexile": "патфоризгнание", "immersivesims": "иммерсивнисимулатори", "okage": "окаджe", "juegoderol": "игратероли", "witcher": "вучер", "dishonored": "непочетен", "eldenring": "елденринг", "darksouls": "тъмендуши", "kotor": "котор", "wynncraft": "уайнкрафт", "witcher3": "вещер3", "fallout": "фалут", "fallout3": "фоллаут3", "fallout4": "фоллаут4", "skyrim": "скайрим", "elderscrolls": "стари<PERSON><PERSON><PERSON><PERSON>с", "modding": "моддинг", "charactercreation": "създаваненагерои", "immersive": "потапящо", "falloutnewvegas": "новиятвегасфалут", "bioshock": "биошок", "omori": "омори", "finalfantasyoldschool": "финалнатфаантазияолдскул", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "финалнфантазия", "finalfantasy14": "финалнемечти14", "finalfantasyxiv": "финалнатафантазияxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "финалнатафантазияматоя", "lalafell": "лалефел", "dissidia": "дисидия", "finalfantasy7": "финалнатайна7", "ff7": "ff7", "morbidmotivation": "мотивиращоустрашително", "finalfantasyvii": "финалналегендияvii", "ff8": "ff8", "otome": "отоме", "suckerforlove": "заблуденвлюбен", "otomegames": "отомеигри", "stardew": "стардью", "stardewvalley": "стардюваждата", "ocarinaoftime": "окаринанавремето", "yiikrpg": "йиик<PERSON>г", "vampirethemasquerade": "вампирскатамаскарад", "dimension20": "измерение20", "gaslands": "газовистрани", "pathfinder": "пътеводител", "pathfinder2ndedition": "пътешествиявтораиздание", "shadowrun": "сенчевбяг", "bloodontheclocktower": "кръвпохранителнакула", "finalfantasy15": "финалнасагадание15", "finalfantasy11": "финалнозфентези11", "finalfantasy8": "финалналечба8", "ffxvi": "ffxvi", "lovenikki": "обичамники", "drakengard": "драконохранител", "gravityrush": "гравитационенбум", "rpg": "рпг", "dota2": "dota2", "xenoblade": "ксеноблейд", "oneshot": "еднократен", "rpgmaker": "практиказарпг", "osrs": "осрс", "overlord": "владетел", "yourturntodie": "вашиятредзадушане", "persona3": "персона3", "rpghorror": "рпгужасии", "elderscrollsonline": "старискроловеонлайн", "reka": "река", "honkai": "хонкай", "marauders": "марадери", "shinmegamitensei": "шинмегамітансей", "epicseven": "епикседем", "rpgtext": "текстоварпг", "genshin": "ген<PERSON>ин", "eso": "есо", "diablo2": "диабло2", "diablo2lod": "диабло2лод", "morrowind": "моровинд", "starwarskotor": "starwarsкотор", "demonsouls": "демонсълс", "mu": "му", "falloutshelter": "сховищезабавление", "gurps": "гърпс", "darkestdungeon": "найтъмнатаподземия", "eclipsephase": "еклипсфаза", "disgaea": "дисгаея", "outerworlds": "външнисветове", "arpg": "арпг", "crpg": "рпг", "bindingofisaac": "прикованиянаисаак", "diabloimmortal": "диаблоиммортал", "dynastywarriors": "действиянасемейства", "skullgirls": "черепнидевойки", "nightcity": "нощенград", "hogwartslegacy": "харипотернаследство", "madnesscombat": "лудостсбоя", "jaggedalliance2": "зъбчатаалианс2", "neverwinter": "никогаснежнатазима", "road96": "път96", "vtmb": "втмб", "chimeraland": "<PERSON>им<PERSON><PERSON><PERSON><PERSON><PERSON>д", "homm3": "хомм3", "fe3h": "fe3h", "roguelikes": "роуглайкове", "gothamknights": "gothamлюде", "forgottenrealms": "забравенисветове", "dragonlance": "драконоваострица", "arenaofvalor": "аре<PERSON>навал<PERSON>р", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "тоонтън", "childoflight": "детенасветлината", "aq3d": "aq3d", "mogeko": "могеко", "thedivision2": "дивизия2", "lineage2": "линейдж2", "digimonworld": "димид<PERSON>ънсвят", "monsterrancher": "монстер<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "ecopunk": "екопънк", "vermintide2": "верминтайд2", "xeno": "ксано", "vulcanverse": "вулканвърс", "fracturedthrones": "счупеницарства", "horizonforbiddenwest": "хоризонтиззабраненазапад", "twewy": "твеуи", "shadowpunk": "шадоупънк", "finalfantasyxv": "финалнaфантазияхв", "everoasis": "евероазис", "hogwartmystery": "харипотързагадка", "deltagreen": "участвайвзелено", "diablo": "дявол", "diablo3": "диабло3", "diablo4": "диабло4", "smite": "удари", "lastepoch": "последнесезонче", "starfinder": "намирачевзвезди", "goldensun": "златнослънце", "divinityoriginalsin": "божественопървородство", "bladesinthedark": "остриятъмно", "twilight2000": "залез2000", "sandevistan": "сандевистан", "cyberpunk": "киберпънк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "киберпънкчервен", "dragonballxenoverse2": "драгонболксановерс2", "fallenorder": "падналоредер", "finalfantasyxii": "финалнафантасия12", "evillands": "злиземища", "genshinimact": "геншинимакт", "aethyr": "ейтар", "devilsurvivor": "дяволскиоцелял", "oldschoolrunescape": "старашколнелоджънскейп", "finalfantasy10": "финалнафантазия10", "anime5e": "аниме5е", "divinity": "божественост", "pf2": "пф2", "farmrpg": "фермерскаигра", "oldworldblues": "старасветовнамузика", "adventurequest": "приключенскиквест", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "ролеплейигри", "roleplayinggames": "игрисролеваигра", "finalfantasy9": "финалнасага9", "sunhaven": "слънчевадома", "talesofsymphonia": "историинасимфонията", "honkaistarrail": "хонкайстар<PERSON>ейл", "wolong": "волонг", "finalfantasy13": "финалнатфантазия13", "daggerfall": "дагер<PERSON>ал", "torncity": "счупеноград", "myfarog": "моятфарог", "sacredunderworld": "свещенподземенсвят", "chainedechoes": "вързаниехо", "darksoul": "тъмендуша", "soulslikes": "душевниигри", "othercide": "другосъдие", "mountandblade": "гораивалкратка", "inazumaeleven": "инозумаелевън", "acvalhalla": "аквалхала", "chronotrigger": "хронотригър", "pillarsofeternity": "стълбоветенад永恆ността", "palladiumrpg": "паладиумrpg", "rifts": "разломи", "tibia": "тибиа", "thedivision": "дивизията", "hellocharlotte": "здравейчарлот", "legendofdragoon": "легендаилетащерите", "xenobladechronicles2": "ксеноблейдхроникс2", "vampirolamascarada": "вампирскамаскарада", "octopathtraveler": "пътешествиетонаосемтепътеки", "afkarena": "афкарена", "werewolftheapocalypse": "вълкътапокалипсис", "aveyond": "авейонд", "littlewood": "малкотодърво", "childrenofmorta": "децатанаморта", "engineheart": "двигателносърце", "fable3": "фейбъл3", "fablethelostchapter": "приказкатаизгубенатаглава", "hiveswap": "хайвсвап", "rollenspiel": "ролеваигра", "harpg": "хар<PERSON>г", "baldursgates": "балдаурсвратата", "edeneternal": "единавечен", "finalfantasy16": "финалнатафантазия16", "andyandleyley": "андиендлейли", "ff15": "ff15", "starfield": "звезденполе", "oldschoolrevival": "възражданенастариястил", "finalfantasy12": "финалнайтфенатазия12", "ff12": "феф12", "morkborg": "моркборг", "savageworlds": "свиреписветове", "diabloiv": "ди<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pve": "пвe", "kingdomheart1": "кралствотосърца1", "ff9": "ff9", "kingdomheart2": "царствосърца2", "darknessdungeon": "тъмниненадеждник", "juegosrpg": "игрирпг", "kingdomhearts": "царствотосърца", "kingdomheart3": "кралствосърца3", "finalfantasy6": "финалнатафантазия6", "ffvi": "ффви", "clanmalkavian": "кланмалкавиан", "harvestella": "жътварела", "gloomhaven": "глумхейвън", "wildhearts": "дивосърца", "bastion": "бастион", "drakarochdemoner": "дракарочдемонер", "skiesofarcadia": "небесатаонаркадия", "shadowhearts": "сърцатасенки", "nierreplicant": "ниеерепликант", "gnosia": "гносия", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "дъхнаогън4", "mother3": "мама3", "cyberpunk2020": "кибепънк2020", "falloutbos": "falloutbos", "anothereden": "анаде<PERSON>е<PERSON><PERSON>н", "roleplaygames": "игрисразвръзка", "roleplaygame": "ролеваигра", "fabulaultima": "фабулалтимa", "witchsheart": "отсърцетонавещицата", "harrypottergame": "харипотъригра", "pathfinderrpg": "пътешественикrpg", "pathfinder2e": "пътепоказател2е", "vampirilamasquerade": "вампирскамаскарада", "dračák": "дра<PERSON><PERSON>к", "spelljammer": "спелджамър", "dragonageorigins": "драконовиевъзход", "chronocross": "хронокръст", "cocttrpg": "коктейлрпг", "huntroyale": "ловнацарство", "albertodyssey": "албертоодисея", "monsterhunterworld": "монстроловецсвят", "bg3": "bg3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "форумиппо", "shadowheartscovenant": "сенкисърцатакавенант", "bladesoul": "блейддуша", "baldursgate3": "бээлдарсгейт3", "kingdomcome": "кралствотоидва", "awplanet": "awplanet", "theworldendswithyou": "светътсвършвастеб", "dragalialost": "драгалиялост", "elderscroll": "стариобърквания", "dyinglight2": "умиращасветлина2", "finalfantasytactics": "финалнафантазиятактики", "grandia": "грандиа", "darkheresy": "тъменересийник", "shoptitans": "шоптитани", "forumrpg": "форумрпг", "golarion": "golarion", "earthmagic": "земнатамmagic", "blackbook": "чернакнига", "skychildrenoflight": "небеснитедецанасветлината", "gryrpg": "грирпг", "sacredgoldedition": "свещенозлатноиздание", "castlecrashers": "замъкразрушители", "gothicgame": "gothicигра", "scarletnexus": "скарлетнексус", "ghostwiretokyo": "призрачносвързванетокио", "fallout2d20": "фаллот2д20", "gamingrpg": "игриrpg", "prophunt": "прорепчица", "starrails": "звезднирелси", "cityofmist": "граднасмутане", "indierpg": "индиrpg", "pointandclick": "точкиикликове", "emilyisawaytoo": "емилиецкъсменя", "emilyisaway": "емили<PERSON>ъсблагодаря", "indivisible": "неразделими", "freeside": "свободнатранаг", "epic7": "епик7", "ff7evercrisis": "ff7вечнокриза", "xenogears": "ксеногири", "megamitensei": "мегамиен<PERSON>ей", "symbaroum": "симбарум", "postcyberpunk": "постсайбърпънк", "deathroadtocanada": "смъртнопътяканадa", "palladium": "пала<PERSON><PERSON><PERSON>", "knightjdr": "риц<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "монстрловец", "fireemblem": "огнензнак", "genshinimpact": "геншинимпакт", "geosupremancy": "геосупремност", "persona5": "персона5", "ghostoftsushima": "призрацитенацушима", "sekiro": "секиро", "monsterhunterrise": "ловецнамонстрииздигане", "nier": "ниер", "dothack": "дотхак", "ys": "яси", "souleater": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "фатес<PERSON><PERSON>й<PERSON>йт", "etrianodyssey": "етрианодисея", "nonarygames": "нованарниигри", "tacticalrpg": "тактическирпг", "mahoyo": "махойо", "animegames": "анимеигри", "damganronpa": "дамгандронпа", "granbluefantasy": "гранблуфантазия", "godeater": "ядосер", "diluc": "дилук", "venti": "венти", "eternalsonata": "вечнатасоната", "princessconnect": "принцесасвързани", "hexenzirkel": "вещерскияткружок", "cristales": "кристали", "vcs": "всичкиподедно", "pes": "пеc", "pocketsage": "дж<PERSON>бнапомощ", "valorant": "валорент", "valorante": "валоренте", "valorantindian": "валоронтиндийски", "dota": "дота", "madden": "маден", "cdl": "чдл", "efootbal": "ефутбол", "nba2k": "нба2к", "egames": "игри", "fifa23": "фифа23", "wwe2k": "вве2к", "esport": "еспорт", "mlg": "млг", "leagueofdreamers": "лиганазавършениачери", "fifa14": "фифа14", "midlaner": "мид<PERSON><PERSON><PERSON><PERSON><PERSON>р", "efootball": "ефутбол", "dreamhack": "мечтателскихак", "gaimin": "геймин", "overwatchleague": "лигатапонаблюдение", "cybersport": "киберспорт", "crazyraccoon": "луди<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "test1test": "тест1тест", "fc24": "фк24", "riotgames": "riotgames", "eracing": "еразинг", "brasilgameshow": "бразилскиигришоу", "valorantcompetitive": "валорънтконкурентен", "t3arena": "т3арена", "valorantbr": "валорентбг", "csgo": "ксгог", "tf2": "tf2", "portal2": "портал2", "halflife": "половинжитие", "left4dead": "оставен4мертъв", "left4dead2": "левых4мъртви2", "valve": "клапан", "portal": "портал", "teamfortress2": "тимфортрес2", "everlastingsummer": "безкраечнолято", "goatsimulator": "козосимулация", "garrysmod": "гариесмод", "freedomplanet": "свободнасвета", "transformice": "трансформир<PERSON><PERSON>с", "justshapesandbeats": "самоформиидвукпиане", "battlefield4": "бойнополе4", "nightinthewoods": "нощвгорите", "halflife2": "половинживот2", "hacknslash": "бъгвайисечи", "deeprockgalactic": "дълбокскалиненгалактик", "riskofrain2": "рискотдъжд2", "metroidvanias": "метроидвании", "overcooked": "преготвено", "interplanetary": "междупланетен", "helltaker": "адоставам", "inscryption": "инскри<PERSON><PERSON>ън", "7d2d": "7д2д", "deadcells": "мъртвиклетки", "nierautomata": "ниеравтомата", "gmod": "гмод", "dwarffortress": "дворцовасградивна", "foxhole": "лисица", "stray": "бездомник", "battlefield": "бойнополе", "battlefield1": "боящището1", "swtor": "свтор", "fallout2": "фалот2", "uboat": "убот", "eyeb": "гледайме", "blackdesert": "черенпустиня", "tabletopsimulator": "симулат<PERSON><PERSON>натаблетки", "partyhard": "партитайздраво", "hardspaceshipbreaker": "труденкосмическиразрушител", "hades": "<PERSON>а<PERSON><PERSON><PERSON>", "gunsmith": "оръжейник", "okami": "оками", "trappedwithjester": "западналосичковец", "dinkum": "истински", "predecessor": "предшественик", "rainworld": "дъждовенсвят", "cavesofqud": "пещеритенакуд", "colonysim": "колонисим", "noita": "ноита", "dawnofwar": "зоранавайната", "minionmasters": "миньонмайстори", "grimdawn": "тъмензорак", "darkanddarker": "тъменидарки", "motox": "мотокс", "blackmesa": "чернамеса", "soulworker": "душепрораб", "datingsims": "симулатори_за_срещи", "yaga": "яга", "cubeescape": "кубизбягване", "hifirush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "свензадружение", "newcity": "новаград", "citiesskylines": "градскитоплини", "defconheavy": "дефконтежък", "kenopsia": "кенопсия", "virtualkenopsia": "виртуаленкенопсия", "snowrunner": "снеженшофьор", "libraryofruina": "библиотеканасъсипията", "l4d2": "л4д2", "thenonarygames": "ненаредбитеигри", "omegastrikers": "омегастрайкъри", "wayfinder": "пътеводител", "kenabridgeofspirits": "кенабриджнадухове", "placidplasticduck": "спокоенпластмасовпатица", "battlebit": "биткабит", "ultimatechickenhorse": "върховенпетелконят", "dialtown": "диа<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "усмивкайсеимен", "catnight": "котешканощ", "supermeatboy": "супермесар", "tinnybunny": "тунелепухче", "cozygrove": "уютнодърво", "doom": "падение", "callofduty": "виканадългия", "callofdutyww2": "колобораванеww2", "rainbow6": "дъга<PERSON><PERSON><PERSON>с", "apexlegends": "апекслегендс", "cod": "код", "borderlands": "граничниземи", "pubg": "пубг", "callofdutyzombies": "повикваниюнаумрелизомбита", "apex": "апекс", "r6siege": "r6разпад", "megamanx": "мегаменикс", "touhou": "топоу", "farcry": "далеченвик", "farcrygames": "farcryигри", "paladins": "паладини", "earthdefenseforce": "защита_на_земята", "huntshowdown": "ловесблъсканици", "ghostrecon": "призрачнонаключение", "grandtheftauto5": "грандафтото5", "warz": "войни", "sierra117": "сиера117", "dayzstandalone": "днинастояние", "ultrakill": "ултракит", "joinsquad": "присъединисебеии", "echovr": "еховр", "discoelysium": "дискоелизия", "insurgencysandstorm": "бунтсандвят", "farcry3": "farcry3", "hotlinemiami": "хотлинемайами", "maxpayne": "макспейн", "hitman3": "наемник3", "r6s": "r6s", "rainbowsixsiege": "дъгасекунда", "deathstranding": "световнапорадица", "b4b": "б4б", "codwarzone": "кодвойна", "callofdutywarzone": "callofdutywarzone", "codzombies": "кодзомбита", "mirrorsedge": "огледалаторграничие", "divisions2": "дивизии2", "killzone": "убийствозона", "helghan": "хелган", "coldwarzombies": "студеназомбирани", "metro2033": "метро2033", "metalgear": "мета<PERSON><PERSON><PERSON>р", "acecombat": "аскомбат", "crosscode": "кроскод", "goldeneye007": "златноока007", "blackops2": "блекопс2", "sniperelite": "снайперелит", "modernwarfare": "модернавойна", "neonabyss": "неоновопадение", "planetside2": "планетасайд2", "mechwarrior": "мехвоин", "boarderlands": "граниченсвят", "owerwatch": "овер<PERSON><PERSON><PERSON>", "rtype": "rtype", "dcsworld": "дцсвят", "escapefromtarkov": "убежищеоттарков", "metalslug": "металслъг", "primalcarnage": "праймълкарнидж", "worldofwarships": "светанаплавателнибойникораби", "back4blood": "назадзакръв", "warframe": "войнавазата", "rainbow6siege": "dgrad6siege", "xcom": "триквести", "hitman": "убиец", "masseffect": "масоефект", "systemshock": "системеншок", "valkyriachronicles": "валкирияхроники", "specopstheline": "спецоперациялинията", "killingfloor2": "killingfloor2", "cavestory": "пещернаистория", "doometernal": "долеменаправление", "centuryageofashes": "векътнаепохатаоскверни", "farcry4": "farcry4", "gearsofwar": "зъбинивойнствa", "mwo": "мво", "division2": "дивизия2", "tythetasmaniantiger": "тигъръттазиможеби", "generationzero": "поколениенула", "enterthegungeon": "влезвълчатаگیرد", "jakanddaxter": "жакендъкстър", "modernwarfare2": "модернавойна2", "blackops1": "черниоперации1", "sausageman": "сосисмаджията", "ratchetandclank": "ратчетинакланк", "chexquest": "чексквэст", "thephantompain": "фантомнатаболка", "warface": "войнауйлица", "crossfire": "кръстосаногън", "atomicheart": "атомносредце", "blackops3": "черниоперации3", "vampiresurvivors": "вампирскиоцелели", "callofdutybatleroyale": "виканиенадългаигра", "moorhuhn": "моорхьон", "freedoom": "свободия", "battlegrounds": "бойниполя", "frag": "фраг", "tinytina": "малката<PERSON>ина", "gamepubg": "играйпубг", "necromunda": "некромунда", "metalgearsonsoflibert": "металджерасиновенаясвобода", "juegosfps": "фпсигри", "convertstrike": "преводняк", "warzone2": "войната2", "shatterline": "шахтосвета", "blackopszombies": "блекопсзомби", "bloodymess": "кървавабъркотия", "republiccommando": "републиканскикомандос", "elitedangerous": "елитноопастно", "soldat": "системчик", "groundbranch": "земенклон", "squad": "екипа", "destiny1": "съдба1", "gamingfps": "игралнифпс", "redfall": "червенпад", "pubggirl": "pubгърл", "worldoftanksblitz": "светаназначенияблиц", "callofdutyblackops": "повикваненасмъртнитеоперации", "enlisted": "включен", "farlight": "фла<PERSON><PERSON><PERSON><PERSON>т", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "фартлайт84", "splatoon3": "сплатуун3", "armoredcore": "брониранаоснова", "pavlovvr": "павловвр", "xdefiant": "xоткачен", "tinytinaswonderlands": "малкитеприказнисветовенатина", "halo2": "хало2", "payday2": "пейдей2", "cs16": "cs16", "pubgindonesia": "pubgиндонезия", "pubgukraine": "пабгукрайна", "pubgeu": "пъбгев", "pubgczsk": "pubgбг", "wotblitz": "wotб<PERSON><PERSON><PERSON>", "pubgromania": "pubgромания", "empyrion": "емпирион", "pubgczech": "pubgчехия", "titanfall2": "титаничнопадане2", "soapcod": "сапункод", "ghostcod": "призрачнокод", "csplay": "csplay", "unrealtournament": "невероятентурнир", "callofdutydmz": "викамдутидмз", "gamingcodm": "играемcodm", "borderlands2": "граничниземи2", "counterstrike": "контрастрайк", "cs2": "cs2", "pistolwhip": "пистолетнабута", "callofdutymw2": "callofdutymw2", "quakechampions": "зомбитесъстезатели", "halo3": "хало3", "halo": "хало", "killingfloor": "убийственетавангоон", "destiny2": "дсъдбата2", "exoprimal": "екзопримал", "splintercell": "сплитерсел", "neonwhite": "неонбяло", "remnant": "остатък", "azurelane": "азурноубежище", "worldofwar": "светанавойната", "gunvolt": "гънволт", "returnal": "върнато", "halo4": "хало4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "сенчестомъж", "quake2": "куейк2", "microvolts": "микроволти", "reddead": "червеномъртвец", "standoff2": "стендаф2", "harekat": "хореография", "battlefield3": "битка3", "lostark": "изгубенарк", "guildwars2": "гилдипартии2", "fallout76": "фалут76", "elsword": "елсворд", "seaofthieves": "мореоткрадци", "rust": "ръжда", "conqueronline": "завладейонлайн", "dauntless": "неустрашим", "warships": "военникораби", "dayofdragons": "деннадраконите", "warthunder": "войнаоблаците", "flightrising": "летящоизкачване", "recroom": "рекруум", "legendsofruneterra": "легендитеонарунетера", "pso2": "псо2", "myster": "мистери", "phantasystaronline2": "фантастичностаронлайн2", "maidenless": "безмадама", "ninokuni": "нинокуни", "worldoftanks": "светананитовете", "crossout": "премахни", "agario": "агарио", "secondlife": "вторживот", "aion": "айон", "toweroffantasy": "кулаофантазия", "netplay": "мрежоваигра", "everquest": "вечноизбор", "metin2": "метин2", "gtaonline": "gtaonline", "ninokunicrossworld": "нинокуникръстосвят", "reddeadonline": "червенобързинтернет", "superanimalroyale": "суперживотнокралство", "ragnarokonline": "рагарноконлайн", "knightonline": "рицаринтернет", "gw2": "гw2", "tboi": "тбой", "thebindingofisaac": "свързванетонаисак", "dragonageinquisition": "драконововъзкресение", "codevein": "кодоваартерия", "eveonline": "евон<PERSON><PERSON><PERSON>н", "clubpenguin": "клубпингвин", "lotro": "лотро", "wakfu": "вакуф", "scum": "боклук", "newworld": "новсвят", "blackdesertonline": "черенпустинедотоварно", "multiplayer": "мулти<PERSON><PERSON><PERSON><PERSON><PERSON>р", "pirate101": "пирати101", "honorofkings": "почетзацарете", "fivem": "фивем", "starwarsbattlefront": "звезднивойнибоец", "karmaland": "кар<PERSON><PERSON><PERSON><PERSON><PERSON>д", "ssbu": "ссбу", "starwarsbattlefront2": "звезднивойнибиткафронт2", "phigros": "фигрос", "mmo": "ммо", "pokemmo": "покеммо", "ponytown": "пониград", "3dchat": "3dчат", "nostale": "носталия", "tauriwow": "тауриу<PERSON>у", "wowclassic": "уаукласик", "worldofwarcraft": "святнавойната", "warcraft": "войнствост", "wotlk": "вотлк", "runescape": "рунескейп", "neopets": "неопетс", "moba": "моба", "habbo": "хаббо", "archeage": "архидейство", "toramonline": "торомон<PERSON>айн", "mabinogi": "мабиноги", "ashesofcreation": "пепелтанадворите", "riotmmo": "действиерезпоредба", "silkroad": "шелковпът", "spiralknights": "спиралнирицари", "mulegend": "мулегенд", "startrekonline": "стартреконлайн", "vindictus": "вендетта", "albiononline": "албиононлайн", "bladeandsoul": "блейдентдуша", "evony": "евони", "dragonsprophet": "драконовпророк", "grymmo": "гриммо", "warmane": "уарм<PERSON><PERSON>н", "multijugador": "мултипле<PERSON>р", "angelsonline": "ангелите<PERSON><PERSON><PERSON><PERSON><PERSON>н", "lunia": "луния", "luniaz": "лунезаздрака", "idleon": "<PERSON>on", "dcuniverseonline": "дкуниверсон<PERSON><PERSON><PERSON>н", "growtopia": "растежотопия", "starwarsoldrepublic": "звезднивойнистарарепублика", "grandfantasia": "грандфентезия", "blueprotocol": "синпротокол", "perfectworld": "перфектниятсвят", "riseonline": "възходонлайн", "corepunk": "основенпънк", "adventurequestworlds": "приключенскиquestсветове", "flyforfun": "летимзабавно", "animaljam": "животинскоексплозия", "kingdomofloathing": "царствотонамръщението", "cityofheroes": "граднасупергероите", "mortalkombat": "морталкомбат", "streetfighter": "стрийт<PERSON>айтер", "hollowknight": "празенрицар", "metalgearsolid": "металгирсолид", "forhonor": "зачестта", "tekken": "теккен", "guiltygear": "г<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "xenoverse2": "ксеновърс2", "fgc": "фгц", "streetfighter6": "асфалтовабойка6", "multiversus": "мултивселенски", "smashbrosultimate": "смешбросультимейт", "soulcalibur": "душасблестяща", "brawlhalla": "бравлхала", "virtuafighter": "виртуаленбоец", "streetsofrage": "улицитоскандал", "mkdeadlyalliance": "mkсмъртоносенсъюз", "nomoreheroes": "нормалнитащитери", "mhr": "мхр", "mortalkombat12": "морталкомбат12", "thekingoffighters": "царятнабойците", "likeadragon": "като_дракон", "retrofightinggames": "ретрофайтингигри", "blasphemous": "богохулен", "rivalsofaether": "съперницинаефира", "persona4arena": "персона4арена", "marvelvscapcom": "марвелскапком", "supersmash": "суперразбиване", "mugen": "муген", "warofthemonsters": "войнатанамонстрите", "jogosdeluta": "играйбитки", "cyberbots": "кибертехника", "armoredwarriors": "брониранивоеводи", "finalfight": "финалнабитка", "poweredgear": "мощниджаджета", "beatemup": "биешебити", "blazblue": "блазублу", "mortalkombat9": "морталкомбат9", "fightgames": "борбенигри", "killerinstinct": "убийственнаинтуиция", "kingoffigthers": "царьнатенисите", "ghostrunner": "призраченбегач", "chivalry2": "рыцарство2", "demonssouls": "демонскитедуши", "blazbluecrosstag": "блазблукростаг", "blazbluextagbattle": "блазблютагбатъл", "blazbluextag": "блейзблуекстаг", "guiltygearstrive": "виновенгангтрудно", "hollowknightsequel": "сиквелнахолоунайт", "hollowknightsilksong": "празенрицарпесен", "silksonghornet": "шилкпесенстършел", "silksonggame": "играла<PERSON>илксонг", "silksongnews": "новиниотсиликсонг", "silksong": "шилксонг", "undernight": "подлуната", "typelumina": "типелуминa", "evolutiontournament": "турнирнапроцъфтяване", "evomoment": "евомомент", "lollipopchainsaw": "бодливозахарчетолигионките", "dragonballfighterz": "драконболбойци", "talesofberseria": "историиотберсерия", "bloodborne": "кървавонесен", "horizon": "хоризонт", "pathofexile": "пътятнаизгнание", "slimerancher": "сла<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "crashbandicoot": "крешбандикут", "bloodbourne": "кървавапрераждане", "uncharted": "неизследвано", "horizonzerodawn": "хоризонтнулевизор", "ps4": "пс4", "ps5": "пс5", "spyro": "спиро", "playstationplus": "плейстейшънплюс", "lastofus": "последнитенас", "infamous": "знаменит", "playstationbuddies": "плейстейшънприятели", "ps1": "пс1", "oddworld": "страненсвят", "playstation5": "плейстейшън5", "slycooper": "слайкупер", "psp": "псп", "rabbids": "рабидс", "splitgate": "сплитгейт", "persona4": "персона4", "hellletloose": "адътсеосвободи", "gta4": "гта4", "gta": "gta", "roguecompany": "бандитскакомпания", "aisomniumfiles": "айсомниумфайлове", "gta5": "гта5", "gtasanandreas": "гтасанандреас", "godofwar": "богнавойната", "gris": "грис", "trove": "съкровище", "detroitbecomehuman": "детройтставамчовек", "beatsaber": "бит<PERSON><PERSON><PERSON><PERSON><PERSON>р", "rimworld": "римсвят", "stellaris": "стелар<PERSON>с", "ps3": "пс3", "untildawn": "докатоизгреефронтът", "touristtrophy": "трофейтурист", "lspdfr": "лспдфр", "shadowofthecolossus": "сянкатанаколосите", "crashteamracing": "красчтимрейсинг", "fivepd": "петпд", "tekken7": "теккен7", "devilmaycry": "дяволътплаче", "devilmaycry3": "дяволъможеаспи3", "devilmaycry5": "дяволътплачев5", "ufc4": "ufc4", "playingstation": "плейсте<PERSON><PERSON>ън", "samuraiwarriors": "самурайскивоини", "psvr2": "псвр2", "thelastguardian": "последниятстраж", "soulblade": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5рп", "gtav": "гтав", "playstation3": "плейстейшън3", "manhunt": "ловнаумяне", "gtavicecity": "gtavicecity", "wwe2k23": "ввее2к23", "shadowhearts2covenant": "shadowhearts2клятва", "pcsx2": "pcsx2", "lastguardian": "последниятпазител", "xboxone": "xboxone", "forza": "форца", "cd": "цд", "gamepass": "игровепропуск", "armello": "арместо", "partyanimal": "партизвяр", "warharmmer40k": "warhammer40k", "fightnightchampion": "вечернапобедителите", "psychonauts": "психонавти", "mhw": "mhw", "princeofpersia": "принцътнаперсия", "theelderscrollsskyrim": "старитеизкатскyrim", "pantarhei": "пантархеи", "theelderscrolls": "възрастнитевъртения", "gxbox": "gxbox", "battlefront": "битка<PERSON>ронт", "dontstarvetogether": "неумрелиедно", "ori": "ори", "spelunky": "спелунки", "xbox1": "иксбокс1", "xbox360": "иксбокс360", "starbound": "звезднонапред", "xboxonex": "xboxonex", "forzahorizon5": "форзахоризон5", "skate3": "скейт3", "houseflipper": "превръщамкъщи", "americanmcgeesalice": "американскияалискакрива", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "лиганацарствата", "fable2": "фейбъл2", "xboxgamepass": "xboxgamepass", "undertale": "анде<PERSON><PERSON><PERSON><PERSON><PERSON>", "trashtv": "бокстелевизия", "skycotl": "небожко", "erica": "ерика", "ancestory": "предци", "cuphead": "купхед", "littlemisfortune": "малъкнещастник", "sallyface": "салифейс", "franbow": "франбоу", "monsterprom": "монстрпом", "projectzomboid": "проектзомбоида", "ddlc": "ддлц", "motos": "мото", "outerwilds": "външнидиви", "pbbg": "пббг", "anshi": "ан<PERSON>и", "cultofthelamb": "култътнабарашка", "duckgame": "патицатаигра", "thestanleyparable": "станлиеватаистория", "towerunite": "тау<PERSON><PERSON><PERSON><PERSON>т", "occulto": "окулто", "longdrive": "дългошофиране", "satisfactory": "задоволителен", "pluviophile": "плувиофил", "underearth": "подземията", "assettocorsa": "асеттоcorsа", "geometrydash": "геометриченскокотос", "kerbal": "кербал", "kerbalspaceprogram": "кербалкосмическапрограма", "kenshi": "кенши", "spiritfarer": "духоплавател", "darkdome": "тъменкупол", "pizzatower": "пицаториума", "indiegame": "индиигра", "itchio": "итчио", "golfit": "гълфит", "truthordare": "истинаилипредизвикателство", "game": "игра", "rockpaperscissors": "камъкножицихартия", "trampoline": "трамплин", "hulahoop": "<PERSON>у<PERSON><PERSON><PERSON>у<PERSON>", "dare": "предизвиквам", "scavengerhunt": "ловнасъкровища", "yardgames": "игринавората", "pickanumber": "изберичисло", "trueorfalse": "истинаилилъжа", "beerpong": "биренпингпонд", "dicegoblin": "зароведемон", "cosygames": "уютниигри", "datinggames": "игрипознанства", "freegame": "безплатнаигра", "drinkinggames": "игриопиване", "sodoku": "содоку", "juegos": "игри", "mahjong": "маджонг", "jeux": "игри", "simulationgames": "симулационниигри", "wordgames": "думениигри", "jeuxdemots": "игриспоеми", "juegosdepalabras": "игрисдуми", "letsplayagame": "хайдедаиграемгадже", "boredgames": "бoredgames", "oyun": "ойун", "interactivegames": "интерактивниигри", "amtgard": "ам<PERSON>г<PERSON><PERSON>д", "staringcontests": "конкурсизапристъпване", "spiele": "игр<PERSON><PERSON>", "giochi": "игри", "geoguessr": "геогесър", "iphonegames": "айфонигри", "boogames": "бууигри", "cranegame": "крани<PERSON><PERSON>йна", "hideandseek": "скрийсисе", "hopscotch": "скокчета", "arcadegames": "аркадниигри", "yakuzagames": "якузаигри", "classicgame": "класическаигра", "mindgames": "умствениигри", "guessthelyric": "познайтекста", "galagames": "галаигри", "romancegame": "романсигра", "yanderegames": "яндерегеймс", "tonguetwisters": "езиковиигри", "4xgames": "4xигри", "gamefi": "игрофина", "jeuxdarcades": "аркадниигри", "tabletopgames": "настолниигри", "metroidvania": "метроидвания", "games90": "игри90", "idareyou": "предизвиквамтеб", "mozaa": "мозаа", "fumitouedagames": "фумитоуедагеймс", "racinggames": "размериигри", "ets2": "етс2", "realvsfake": "истинскосрещуфалшиво", "playgames": "играйигри", "gameonline": "играйо<PERSON><PERSON><PERSON><PERSON>н", "onlinegames": "онлайнигри", "jogosonline": "он<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "писменир<PERSON><PERSON>минг", "playaballgame": "играйнавроцета", "pictionary": "пикионариум", "coopgames": "игризаедно", "jenga": "дженга", "wiigames": "виигри", "highscore": "високрезултат", "jeuxderôles": "ролеваигра", "burgergames": "бургеригри", "kidsgames": "игризадеца", "skeeball": "скибол", "nfsmwblackedition": "нфсмвблекедишън", "jeuconcour": "играенадпревара", "tcgplayer": "tcgplayer", "juegodepreguntas": "игранавъпроси", "gioco": "игр<PERSON><PERSON>", "managementgame": "управленскаигра", "hiddenobjectgame": "играскритинипредмети", "roolipelit": "рулипелит", "formula1game": "игратаформула1", "citybuilder": "градостроител", "drdriving": "докаране", "juegosarcade": "аркадниигри", "memorygames": "игрипамет", "vulkan": "вулкан", "actiongames": "игринасекунда", "blowgames": "блъскатигри", "pinballmachines": "пинбал<PERSON><PERSON><PERSON>ини", "oldgames": "стариигри", "couchcoop": "диванкопче", "perguntados": "пергунтадос", "gameo": "геймо", "lasergame": "лазернаигра", "imessagegames": "имагазиниигри", "idlegames": "безделниигри", "fillintheblank": "попълнипразното", "jeuxpc": "играчкабузи", "rétrogaming": "ретрогейминг", "logicgames": "логическиигри", "japangame": "япанигра", "rizzupgame": "играйсриз", "subwaysurf": "метрометене", "jeuxdecelebrite": "игринаславата", "exitgames": "излезигри", "5vs5": "5ср5", "rolgame": "ролигра", "dashiegames": "дашиигри", "gameandkill": "играйисмъртвай", "traditionalgames": "традиционниигри", "kniffel": "книфел", "gamefps": "игриfps", "textbasedgames": "текстовигри", "gryparagrafowe": "грипаораграфове", "fantacalcio": "фантазяка", "retrospel": "ретроспел", "thiefgame": "игратанакрадеца", "lawngames": "игрибольшогозеленото", "fliperama": "флипарама", "heroclix": "героикликс", "tablesoccer": "таблицифутбол", "tischfußball": "футболнамасичка", "spieleabende": "игравечери", "jeuxforum": "игрфорум", "casualgames": "неформалниигри", "fléchettes": "стрелички", "escapegames": "игризаизбягване", "thiefgameseries": "игриплъх", "cranegames": "игрики<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "játék": "игр<PERSON><PERSON>", "bordfodbold": "бордфутбол", "jogosorte": "играйслучайно", "mage": "магьосник", "cargames": "играчкизаавтомобили", "onlineplay": "онлайнигра", "mölkky": "мьолкки", "gamenights": "игричнивечери", "pursebingos": "чанта<PERSON><PERSON>нгос", "randomizer": "рандомизатор", "msx": "мсх", "anagrammi": "анаграми", "gamespc": "игриpc", "socialdeductiongames": "игризасоциалноопределяне", "dominos": "доминос", "domino": "домино", "isometricgames": "исометричниигри", "goodoldgames": "стари_игри", "truthanddare": "истинаизвикателство", "mahjongriichi": "махаджонгричи", "scavengerhunts": "ловнасъкровища", "jeuxvirtuel": "виртуалниигри", "romhack": "ромхак", "f2pgamer": "f2pиграч", "free2play": "безплатноидиране", "fantasygame": "фентъзийнаигра", "gryonline": "грия<PERSON><PERSON><PERSON><PERSON><PERSON>н", "driftgame": "дрифтигра", "gamesotomes": "игризатеб", "halotvseriesandgames": "халотвсерииигри", "mushroomoasis": "гъбен<PERSON><PERSON>й", "anythingwithanengine": "всичкосдвигател", "everywheregame": "игранавсякъде", "swordandsorcery": "мечовеиссамодейства", "goodgamegiving": "гаднаигравалня", "jugamos": "играем", "lab8games": "лаб8игри", "labzerogames": "лабзероигри", "grykomputerowe": "грикомпютърие", "virgogami": "виргогами", "gogame": "гогейм", "jeuxderythmes": "игриразмери", "minaturegames": "миниатюрниигри", "ridgeracertype4": "риджрейсъртип4", "selflovegaming": "любовкъмсебесявигри", "gamemodding": "играненаигри", "crimegames": "игризаубийства", "dobbelspellen": "двойниигри", "spelletjes": "игри", "spacenerf": "спейснърф", "charades": "харади", "singleplayer": "единакнаплейър", "coopgame": "играсподелено", "gamed": "геймд", "forzahorizon": "форзахоризонт", "nexus": "нексус", "geforcenow": "гефорсеноу", "maingame": "основнаигра", "kingdiscord": "кралдискорд", "scrabble": "скрабъл", "schach": "шах", "shogi": "шоги", "dandd": "гаднярскидандд", "catan": "катан", "ludo": "лудо", "backgammon": "табла", "onitama": "онитама", "pandemiclegacy": "наследствовпандемията", "camelup": "камилника", "monopolygame": "монополиигра", "brettspiele": "настолниигри", "bordspellen": "бордигри", "boardgame": "настолниигри", "sällskapspel": "игрискомпания", "planszowe": "настолниигри", "risiko": "рисико", "permainanpapan": "игранинабор", "zombicide": "зомбицид", "tabletop": "настолниигри", "baduk": "бадук", "bloodbowl": "кровноядерен", "cluedo": "клуедо", "xiangqi": "сянкци", "senet": "сенет", "goboardgame": "гобордигра", "connectfour": "свържемчетири", "heroquest": "геройскамисия", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "фаркъл", "carrom": "каром", "tablegames": "настолниигри", "dicegames": "игрискубчета", "yatzy": "ятзи", "parchis": "парчис", "jogodetabuleiro": "джогадетаблейро", "jocuridesocietate": "игриразвлечение", "deskgames": "настолниигри", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "масовиянства", "marvelcrisisprotocol": "мравелкризиспротокол", "cosmicencounter": "космическасреща", "creationludique": "създаванеподкаст", "tabletoproleplay": "настолниигри", "cardboardgames": "картонениигри", "eldritchhorror": "елдритчужасии", "switchboardgames": "игриразклучване", "infinitythegame": "безкрайнаятаигра", "kingdomdeath": "царствоболка", "yahtzee": "ямкобене", "chutesandladders": "стълбицииджумки", "társas": "търсиш", "juegodemesa": "игра<PERSON><PERSON>масa", "planszówki": "настолниигри", "rednecklife": "червеношияжертвам", "boardom": "скука", "applestoapples": "ябълкинаябълки", "jeudesociété": "играйсбъднигра", "gameboard": "игралнадъска", "dominó": "до<PERSON><PERSON><PERSON><PERSON>", "kalah": "калах", "crokinole": "крокинол", "jeuxdesociétés": "игринасъбрания", "twilightimperium": "залезнаимперия", "horseopoly": "коняшкиполия", "deckbuilding": "строителствонаколоди", "mansionsofmadness": "имениянаблудствата", "gomoku": "гомоку", "giochidatavola": "играчкинамасата", "shadowsofbrimstone": "сенкитенапясъка", "kingoftokyo": "кралятнатокио", "warcaby": "шахмати", "táblajátékok": "таблаигри", "battleship": "корабътнабой", "tickettoride": "билетзавъзходство", "deskovehry": "десковехри", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "игринасмасата", "stolníhry": "настолниигри", "xiángqi": "шах", "jeuxsociete": "игринасоциума", "gesellschaftsspiele": "настолниигри", "starwarslegion": "звезднавойналегион", "gochess": "гочес", "weiqi": "вэйки", "jeuxdesocietes": "игринастолища", "terraria": "террария", "dsmp": "дсmp", "warzone": "зоназавойна", "arksurvivalevolved": "арксурвивалеволвед", "dayz": "де<PERSON><PERSON><PERSON>", "identityv": "идентичностv", "theisle": "острова", "thelastofus": "последниятотнас", "nomanssky": "номансскай", "subnautica": "субнаутика", "tombraider": "гробокопачка", "callofcthulhu": "повикваненадкулху", "bendyandtheinkmachine": "бендиа<PERSON><PERSON><PERSON>нкм<PERSON>шината", "conanexiles": "конаневселената", "eft": "ефт", "amongus": "насреднас", "eco": "еко", "monkeyisland": "маймунскиотказ", "valheim": "вал<PERSON>ейм", "planetcrafter": "планетенмайстор", "daysgone": "изминалидни", "fobia": "фобия", "witchit": "върниго", "pathologic": "патологичен", "zomboid": "зомбоида", "northgard": "северенгард", "7dtd": "7днедощи", "thelongdark": "дългататъмнина", "ark": "арк", "grounded": "заземен", "stateofdecay2": "състояниенаразпад2", "vrising": "възходвай", "madfather": "лудататка", "dontstarve": "недейгладней", "eternalreturn": "вечновръщане", "pathoftitans": "пътятнагигантите", "frictionalgames": "фрикционалниигри", "hexen": "хексен", "theevilwithin": "злотовътре", "realrac": "истинскираци", "thebackrooms": "задкулисията", "backrooms": "задкулисията", "empiressmp": "импайърсмп", "blockstory": "блокистория", "thequarry": "кариерата", "tlou": "тлуo", "dyinglight": "умеренасянка", "thewalkingdeadgame": "играт<PERSON><PERSON>шмъртвете", "wehappyfew": "щастливималко", "riseofempires": "възходнаимперии", "stateofsurvivalgame": "игратасоциалнооцеляване", "vintagestory": "винтажнаистория", "arksurvival": "арксървайвъл", "barotrauma": "баротравма", "breathedge": "вдишвайдишка", "alisa": "alisa", "westlendsurvival": "издръжкавестленд", "beastsofbermuda": "звероветеотбермуда", "frostpunk": "мразобие", "darkwood": "тъменлес", "survivalhorror": "оцеляващужас", "residentevil": "живозлото", "residentevil2": "жизнятаужас2", "residentevil4": "заразнозло4", "residentevil3": "жителствоevil3", "voidtrain": "празенвлак", "lifeaftergame": "животследиграта", "survivalgames": "игринаоцеляване", "sillenthill": "sилинтъла", "thiswarofmine": "тазивойнавмене", "scpfoundation": "scpоснова", "greenproject": "зеленпроект", "kuon": "куон", "cryoffear": "плачиотстрах", "raft": "плоскост", "rdo": "рдо", "greenhell": "зеленаада<PERSON><PERSON>ч", "residentevil5": "residentevil5", "deadpoly": "мъртволято", "residentevil8": "резидентевил8", "onironauta": "ониронаут", "granny": "бабка", "littlenightmares2": "малкикошмари2", "signalis": "сиг<PERSON><PERSON><PERSON>з", "amandatheadventurer": "амандатагероя", "sonsoftheforest": "синоветенагора", "rustvideogame": "rustигра", "outlasttrials": "издържииспитанията", "alienisolation": "изолиранил외", "undawn": "недосета", "7day2die": "7дена2умреш", "sunlesssea": "безслънчевоморе", "sopravvivenza": "съдба", "propnight": "провидениеночта", "deadisland2": "мъртвиятостров2", "ikemensengoku": "икеменсегоку", "ikemenvampire": "икемевампир", "deathverse": "смъртниявърс", "cataclysmdarkdays": "котешкикатаклизмтъменден", "soma": "сома", "fearandhunger": "страхиглад", "stalkercieńczarnobyla": "стalkerчийцярнобил", "lifeafter": "животслед", "ageofdarkness": "векнапокритието", "clocktower3": "часовниковапирамидa3", "aloneinthedark": "самотенвтъмното", "medievaldynasty": "средновековнадинастия", "projectnimbusgame": "проектниямбусигра", "eternights": "етърнайтс", "craftopia": "крафтопия", "theoutlasttrials": "оутласттриалс", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "световноуправление", "rocketleague": "рокетлига", "tft": "тфт", "officioassassinorum": "убийциоффиции", "necron": "некрон", "wfrp": "вфрп", "dwarfslayer": "два<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "warhammer40kcrush": "влюбенвоуархамър40к", "wh40": "вх40", "warhammer40klove": "любовкъмwarhammer40k", "warhammer40klore": "вархамер40кморалийн", "warhammer": "воененчук", "warhammer30k": "войната30к", "warhammer40k": "уархамър40к", "warhammer40kdarktide": "уорхамър40кмрачнаприливнавода", "totalwarhammer3": "тоталвархамър3", "temploculexus": "темплоцулексус", "vindicare": "въздмездие", "ilovesororitas": "обичамсороритас", "ilovevindicare": "обичамвиндикаре", "iloveassasinorum": "обичамасасинорум", "templovenenum": "темпловененум", "templocallidus": "темплокалидус", "templomaerorus": "темпломаерорус", "templovanus": "темпловануs", "oficioasesinorum": "офиснаработа", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40к", "tetris": "тетрис", "lioden": "лиоден", "ageofempires": "епоханатемперии", "aoe2": "aoe2", "hoi4": "хой4", "warhammerageofsigmar": "войнакамеранасигмар", "civilizationv": "цивилизацияv", "ittakestwo": "иттакесту", "wingspan": "разпереникрила", "terraformingmars": "тераформирамарс", "heroesofmightandmagic": "героитанасилатаимагията", "btd6": "бтд6", "supremecommander": "върховенкомандир", "ageofmythology": "векнамитологията", "args": "аргс", "rime": "райм", "planetzoo": "планетнозооб", "outpost2": "пост5", "banished": "изгнан", "caesar3": "цезар3", "redalert": "червен<PERSON><PERSON>г<PERSON>л", "civilization6": "цивилизация6", "warcraft2": "warcraft2", "commandandconquer": "командвайиосвободи", "warcraft3": "варкрафт3", "eternalwar": "вечновойна", "strategygames": "стратегическиигри", "anno2070": "анно2070", "civilizationgame": "цивилизацияигра", "civilization4": "цивилизация4", "factorio": "факторио", "dungeondraft": "дънжондрафт", "spore": "спор", "totalwar": "тоталнатавойна", "travian": "травиан", "forts": "форцс", "goodcompany": "добрикомпании", "civ": "цив", "homeworld": "домашенсвят", "heidentum": "достатъчно", "aoe4": "бгмопчета", "hnefatafl": "хнефатафл", "fasterthanlight": "побързоотсветлината", "forthekings": "заценовете", "realtimestrategy": "реалновойна", "starctaft": "старктра<PERSON>т", "sidmeierscivilization": "сидмайерцивилизация", "kingdomtwocrowns": "царстводвекоронки", "eu4": "европейскоутро", "vainglory": "горделивост", "ww40k": "ww40k", "godhood": "божественост", "anno": "анно", "battletech": "биткататехника", "malifaux": "мал<PERSON><PERSON><PERSON><PERSON><PERSON>", "w40k": "w40k", "hattrick": "хеттрик", "davesfunalgebraclass": "девойкитезабавнииуравнениятанадейв", "plagueinc": "пандемияинк", "theorycraft": "теориякрафт", "mesbg": "mesbg", "civilization3": "цивилизация3", "4inarow": "4вреда", "crusaderkings3": "кръстоносци3", "heroes3": "герои3", "advancewars": "напредвойни", "ageofempires2": "епоханаимперий2", "disciples2": "ученици2", "plantsvszombies": "растениясрещузомбита", "giochidistrategia": "стратегическиигри", "stratejioyunları": "стратегийниигри", "europauniversalis4": "европейскиуниверсали4", "warhammervermintide2": "ворхаммерверминтид2", "ageofwonders": "векначудесата", "dinosaurking": "диноз<PERSON>в<PERSON>ркрал", "worldconquest": "световнозавладяване", "heartsofiron4": "сърцатамета4", "companyofheroes": "компаниянагерои", "battleforwesnoth": "битказауеснот", "aoe3": "ае3", "forgeofempires": "ковачницатанаимперии", "warhammerkillteam": "убийственотворениенавойни", "goosegooseduck": "гъскагъскапатица", "phobies": "фобии", "phobiesgame": "фобиигейм", "gamingclashroyale": "gamingсблъсъкроял", "adeptusmechanicus": "адептусмеханикус", "outerplane": "външенплан", "turnbased": "обратноплеменен", "bomberman": "бомбаджия", "ageofempires4": "векнаимперии4", "civilization5": "цивилизация5", "victoria2": "виктория2", "crusaderkings": "кръстоносците", "cultris2": "култрис2", "spellcraft": "вълшебство", "starwarsempireatwar": "звезднивойниимпериятввойна", "pikmin4": "пикмин4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "попфулпоща", "shiningforce": "светлиннасила", "masterduel": "мастър<PERSON>у<PERSON>л", "dysonsphereprogram": "дайсънсферапрограма", "transporttycoon": "транспортентикон", "unrailed": "нераилд", "magicarena": "магическаарена", "wolvesville": "вълчеград", "ooblets": "ооблетс", "planescapetorment": "бягствоотмъките", "uplandkingdoms": "висшетоземя", "galaxylife": "галаксияживот", "wolvesvilleonline": "wolvesvilleонлайн", "slaythespire": "убийспирата", "battlecats": "биткатовете", "sims3": "симс3", "sims4": "симс4", "thesims4": "симс4", "thesims": "симс", "simcity": "симсити", "simcity2000": "симсити2000", "sims2": "сими2", "iracing": "ирейсинг", "granturismo": "грантуризмо", "needforspeed": "нуждаотскорост", "needforspeedcarbon": "нуждаотскоросткарбонт", "realracing3": "истинскобегане3", "trackmania": "трекмания", "grandtourismo": "грандтуризмо", "gt7": "гт7", "simsfreeplay": "симифриигра", "ts4": "ts4", "thesims2": "сими2", "thesims3": "съсимс3", "thesims1": "симите1", "lossims4": "загубихмес4", "fnaf": "фна<PERSON>", "outlast": "издържи", "deadbydaylight": "мъртъвприсветлинатанаденя", "alicemadnessreturns": "алис<PERSON>ърналаосъбранието", "darkhorseanthology": "тъмноборецааналогия", "phasmophobia": "фазмофобия", "fivenightsatfreddys": "петнощивпредиджаджите", "saiko": "саико", "fatalframe": "фаталнарамка", "littlenightmares": "малкикошмари", "deadrising": "възкресяване", "ladydimitrescu": "даминитреску", "homebound": "вкъщи", "deadisland": "мъртвоостровче", "litlemissfortune": "малкатамисфортуна", "projectzero": "проектнуло", "horory": "хорори", "jogosterror": "йогостерор", "helloneighbor": "здравейсъседе", "helloneighbor2": "здравейсъсед2", "gamingdbd": "геймингдбд", "thecatlady": "коткористката", "jeuxhorreur": "игриу<PERSON><PERSON><PERSON>", "horrorgaming": "ужасигейминг", "magicthegathering": "магическотосъбиране", "mtg": "мейството", "tcg": "картонениигри", "cardsagainsthumanity": "картипротивчовечността", "cribbage": "криби<PERSON><PERSON>", "minnesotamtg": "минесотамтг", "edh": "едх", "monte": "монте", "pinochle": "пино<PERSON><PERSON>e", "codenames": "кодовиимена", "dixit": "диксит", "bicyclecards": "велосипедникарти", "lor": "лор", "euchre": "юкс<PERSON>р", "thegwent": "тъгвент", "legendofrunetera": "легендазарунетера", "solitaire": "солитар", "poker": "покер", "hearthstone": "хартсто<PERSON>н", "uno": "уно", "schafkopf": "шахкопф", "keyforge": "ключовафоржа", "cardtricks": "картифокуси", "playingcards": "картизаигри", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "д<PERSON><PERSON><PERSON><PERSON>карти", "netrunner": "нетранер", "gwent": "гвент", "metazoo": "метазоол", "tradingcards": "търговскикарти", "pokemoncards": "покемонкарти", "fleshandbloodtcg": "плътиикровтцг", "sportscards": "спортникарти", "cardfightvanguard": "бойсъскартивагарнд", "duellinks": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spades": "тикви", "warcry": "войнственикрик", "digimontcg": "дижимонкартите", "toukenranbu": "тукен<PERSON><PERSON><PERSON><PERSON>у", "kingofhearts": "царнасердца", "truco": "труко", "loteria": "лотария", "hanafuda": "ханафуда", "theresistance": "съпротивата", "transformerstcg": "трансформърстцг", "doppelkopf": "допелкоф", "yugiohcards": "югиохкарти", "yugiohtcg": "югиохтцг", "yugiohduel": "югиохдуел", "yugiohocg": "югиохг", "dueldisk": "дуелдиск", "yugiohgame": "югиохигра", "darkmagician": "тъменмагьосник", "blueeyeswhitedragon": "синиочиибялдракон", "yugiohgoat": "югиошище", "briscas": "брискас", "juegocartas": "играчкакарти", "burraco": "бурако", "rummy": "румми", "grawkarty": "гравкартите", "dobble": "добави", "mtgcommander": "mtgкомандир", "cotorro": "котораро", "jeuxdecartes": "игрискарти", "mtgjudge": "mtgсъдия", "juegosdecartas": "игриоказалца", "duelyst": "дю<PERSON><PERSON>ист", "mtgplanschase": "новиниотmtgпланирайигони", "mtgpreconcommander": "mtgпредизвикателенкомандир", "kartenspiel": "игралнакарта", "carteado": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sueca": "суета", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карцинките", "battlespirits": "биткидуши", "battlespiritssaga": "сагатанапредвойнскидух", "jogodecartas": "картофениидеи", "žolíky": "жолици", "facecard": "лицекарта", "cardfight": "битканакарти", "biriba": "бириба", "deckbuilders": "строителинадекове", "marvelchampions": "марвелчемпиони", "magiccartas": "магическитекарти", "yugiohmasterduel": "югиохмастърдуел", "shadowverse": "сенкосвят", "skipbo": "скипбо", "unstableunicorns": "неспокойниеднорози", "cyberse": "кибервселената", "classicarcadegames": "класическиигриотарка", "osu": "осу", "gitadora": "гитадора", "dancegames": "игризатанци", "fridaynightfunkin": "петъчнотанцуване", "fnf": "фиф", "proseka": "прозеца", "projectmirai": "проектмирая", "projectdiva": "проектдива", "djmax": "дид<PERSON><PERSON><PERSON><PERSON>а<PERSON>с", "guitarhero": "гита<PERSON><PERSON><PERSON>г<PERSON>р<PERSON>й", "clonehero": "клонгерой", "justdance": "простотанцувай", "hatsunemiku": "хацунемику", "prosekai": "прозекай", "rocksmith": "роксмит", "idolish7": "идолиш7", "rockthedead": "разклатиммъртвите", "chunithm": "чунитъм", "idolmaster": "идолмастър", "dancecentral": "танцуваленцентър", "rhythmgamer": "ритъмгеймър", "stepmania": "степмания", "highscorerythmgames": "високоскоростниритъмигри", "pkxd": "пкксд", "sidem": "страничка", "ongeki": "онгекито", "soundvoltex": "саундволтекс", "rhythmheaven": "ритъмнонебе", "hypmic": "хипмик", "adanceoffireandice": "танцсогънилед", "auditiononline": "онлайнпрослушване", "itgmania": "итгмания", "juegosderitmo": "ритъмниигри", "cryptofthenecrodancer": "криптофотанецвнекроза", "rhythmdoctor": "ритъмдоктор", "cubing": "кубинг", "wordle": "славословие", "teniz": "тенис", "puzzlegames": "пъзелигри", "spotit": "улавямго", "rummikub": "руммикуб", "blockdoku": "блокдоку", "logicpuzzles": "логическигатанки", "sudoku": "судоку", "rubik": "рубику", "brainteasers": "гатанки", "rubikscube": "рубинксовкуб", "crossword": "кръстословица", "motscroisés": "кръстословици", "krzyżówki": "кросворди", "nonogram": "нонограм", "bookworm": "книжночервейче", "jigsawpuzzles": "пъзели", "indovinello": "индиновело", "riddle": "гатанка", "riddles": "гатанки", "rompecabezas": "пъзел", "tekateki": "текатеки", "inside": "вътре", "angrybirds": "ядосанитептици", "escapesimulator": "ескапесимулатор", "minesweeper": "мин<PERSON><PERSON>р", "puzzleanddragons": "пъзелиидракони", "crosswordpuzzles": "кръстословици", "kurushi": "куроши", "gardenscapesgame": "игратаградинскиприказки", "puzzlesport": "пъзелспорт", "escaperoomgames": "игриизходнастая", "escapegame": "изплъзванеигра", "3dpuzzle": "3дпъзел", "homescapesgame": "игратаhomescapes", "wordsearch": "думопотърсене", "enigmistica": "енигмтика", "kulaworld": "кулаученце", "myst": "мист", "riddletales": "гатанковистории", "fishdom": "риболандия", "theimpossiblequiz": "невъзможнотоизпитване", "candycrush": "сладъксмачкател", "littlebigplanet": "малкобигпланета", "match3puzzle": "матч3пъзел", "huniepop": "хънипоп", "katamaridamacy": "катамаридомамата", "kwirky": "квирки", "rubikcube": "рубрикатуба", "cuborubik": "куборубик", "yapboz": "япб<PERSON>з", "thetalosprinciple": "талоспринципа", "homescapes": "домашниприключения", "puttputt": "путпут", "qbert": "кю<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddleme": "гадателствотеси", "tycoongames": "тиконигри", "cubosderubik": "кубосдерубик", "cruciverba": "кръстословници", "ciphers": "шифри", "rätselwörter": "загадъчнидуми", "buscaminas": "бускаминус", "puzzlesolving": "решаваненапъзели", "turnipboy": "репичкобой", "adivinanzashot": "адиви<PERSON><PERSON><PERSON><PERSON><PERSON>т", "nobodies": "никойси", "guessing": "отгатване", "nonograms": "нонограми", "kostkirubika": "косткирубика", "crypticcrosswords": "криптираникръстословици", "syberia2": "сибирия2", "puzzlehunt": "пъзелсреща", "puzzlehunts": "пъзелловци", "catcrime": "котешкопрестъпление", "quebracabeça": "блокчета", "hlavolamy": "главоблъсканици", "poptropica": "поптропика", "thelastcampfire": "последниятлагернийогън", "autodefinidos": "автодефинирани", "picopark": "пикопарк", "wandersong": "скитанияпесен", "carto": "картон", "untitledgoosegame": "неозаглавенагусейскаигра", "cassetête": "касетета", "limbo": "лимо", "rubiks": "рубици", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "малкикучета", "rubikovakostka": "рубиновакубче", "speedcube": "бързокуб", "pieces": "парчета", "portalgame": "порталигра", "bilmece": "билмеце", "puzzelen": "пъзелен", "picross": "пикрос", "rubixcube": "рубикс_cube", "indovinelli": "загадки", "cubomagico": "кубомагико", "mlbb": "млбб", "pubgm": "пубгм", "codmobile": "кодмобил", "codm": "кодовм", "twistedwonderland": "изкривенасветлина", "monopoly": "монопол", "futurefight": "биткавбъдещето", "mobilelegends": "мобилнилегенди", "brawlstars": "броухстарс", "brawlstar": "броулстар", "coc": "кок", "lonewolf": "самотенвълк", "gacha": "гача", "wr": "пакпишеш", "fgo": "фго", "bitlife": "бит<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "пикмин<PERSON>лум", "ff": "фк", "ensemblestars": "съставнизвезди", "asphalt9": "асфалт9", "mlb": "млб", "cookierunkingdom": "кралствобисквитка", "alchemystars": "алхимисткизвезди", "stateofsurvival": "състояниенасъществуване", "mycity": "моятград", "arknights": "аркнайтс", "colorfulstage": "цветенсцена", "bloonstowerdefense": "блунстворзащитапоядки", "btd": "бтд", "clashroyale": "клашроял", "angela": "анджела", "dokkanbattle": "док<PERSON>а<PERSON><PERSON><PERSON><PERSON>ъл", "fategrandorder": "съдбаогромнапоръчка", "hyperfront": "хиперфронт", "knightrun": "рицарскоратон", "fireemblemheroes": "огнениемблемихерои", "honkaiimpact": "хонкайимпакт", "soccerbattle": "футболнабитка", "a3": "a3", "phonegames": "игрионафон", "kingschoice": "изборътнацаря", "guardiantales": "странниразкази", "petrolhead": "бензиновглавата", "tacticool": "тактичноачи", "cookierun": "кукитечка", "pixeldungeon": "пикселдънжен", "arcaea": "аркеа", "outoftheloop": "извънцикъла", "craftsman": "занаятчия", "supersus": "суперсъмнително", "slowdrive": "бавношофиране", "headsup": "предупреждение", "wordfeud": "думивбуу", "bedwars": "легловойни", "freefire": "безплатноогън", "mobilegaming": "мобилниигри", "lilysgarden": "гради<PERSON><PERSON><PERSON><PERSON><PERSON>или", "farmville2": "farmaсело2", "animalcrossing": "анималкросинг", "bgmi": "бгми", "teamfighttactics": "отборнибитки", "clashofclans": "сблъсъкнаселенията", "pjsekai": "пж<PERSON>екъй", "mysticmessenger": "мистиченпосланик", "callofdutymobile": "callofdutymobile", "thearcana": "арканата", "8ballpool": "8балпул", "emergencyhq": "спешнацентрала", "enstars": "ензвезди", "randonautica": "рандонаутика", "maplestory": "мейпълстори", "albion": "албион", "hayday": "хей<PERSON>ай", "onmyoji": "онмьоджи", "azurlane": "азур<PERSON>е<PERSON>н", "shakesandfidget": "шаткии<PERSON><PERSON><PERSON><PERSON><PERSON>т", "ml": "мл", "bangdream": "бан<PERSON><PERSON><PERSON><PERSON><PERSON>н", "clashofclan": "сблъсъкнасъкланове", "starstableonline": "starstableonline", "dragonraja": "драконраджа", "timeprincess": "времепрингеса", "beatstar": "битстар", "dragonmanialegend": "драконколоняталегенда", "hanabi": "хана<PERSON>и", "disneymirrorverse": "дисниогледалосвят", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "андроидигри", "criminalcase": "кримина<PERSON>енслучай", "summonerswar": "войнстванасилие", "cookingmadness": "готварскапоплътност", "dokkan": "доккан", "aov": "аов", "triviacrack": "тривиакрак", "leagueofangels": "лиганаангелите", "lordsmobile": "лордсмобайл", "tinybirdgarden": "малъкптиченград", "gachalife": "gachalife", "neuralcloud": "неуралнооблако", "mysingingmonsters": "моитепеещимонстри", "nekoatsume": "некоацуме", "bluearchive": "си<PERSON><PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "войнскироботи", "mirrorverse": "огледаленвселен", "pou": "пау", "warwings": "войнстваникрила", "fifamobile": "фифамобил", "mobalegendbangbang": "мобалегендбангбанг", "evertale": "евъртейл", "futime": "футайм", "antiyoy": "антий<PERSON>й", "apexlegendmobile": "апекслегендмобил", "ingress": "вливане", "slugitout": "биткабуу", "mpl": "мпл", "coinmaster": "монетенмайстор", "punishinggrayraven": "наказващосивовран", "petpals": "петпартньори", "gameofsultans": "игранасултани", "arenabreakout": "аренабреакаут", "wolfy": "вълчо", "runcitygame": "бягамвграда", "juegodemovil": "мобилнаигра", "avakinlife": "ава<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "когамa", "mimicry": "мимикрия", "blackdesertmobile": "черенпустинемобил", "rollercoastertycoon": "ролеркостертикун", "grandchase": "голямоимане", "bombmebrasil": "бумменбразилия", "ldoe": "лдоее", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "отоменигра", "mindustry": "умственаиндустрия", "callofdragons": "призиванагабите", "shiningnikki": "светещатники", "carxdriftracing2": "каркстрайфрейсинг2", "pathtonowhere": "пътяткъмникъде", "sealm": "силино", "shadowfight3": "сънобой3", "limbuscompany": "лимбускомпания", "demolitionderby3": "демолиращдерби3", "wordswithfriends2": "думисприятели2", "soulknight": "душ<PERSON><PERSON>р<PERSON>дн<PERSON>щ", "purrfecttale": "перфектнаталанта", "showbyrock": "шоунаскалата", "ladypopular": "дамапопулярна", "lolmobile": "лоловшебно", "harvesttown": "жъдравилноградец", "perfectworldmobile": "перфектенсвятмобилно", "empiresandpuzzles": "империиипъзели", "empirespuzzles": "емпайрспъзли", "dragoncity": "драконград", "garticphone": "гарткфон", "battlegroundmobileind": "биткаполемобилновд", "fanny": "фани", "littlenightmare": "малкакошмара", "aethergazer": "етерозорец", "mudrunner": "мъдр<PERSON>envolve", "tearsofthemis": "сълзите<PERSON><PERSON>ис", "eversoul": "евърсоул", "gunbound": "гън<PERSON><PERSON><PERSON><PERSON>д", "gamingmlbb": "игралниmlbb", "dbdmobile": "dbdmobile", "arknight": "арканайти", "pristontale": "присонтейл", "zombiecastaways": "зомбираниобитатели", "eveechoes": "евекаквеци", "jogocelular": "джогоселулар", "mariokarttour": "мариокарттур", "zooba": "зуба", "mobilelegendbangbang": "мобиленлегендибамбам", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "готвачамама", "cabalmobile": "кабал<PERSON><PERSON>бил", "streetfighterduel": "уличенбиткаджиядюел", "lesecretdhenri": "тайнитенри", "gamingbgmi": "геймингбгми", "girlsfrontline": "момичеотфронта", "jurassicworldalive": "юрасическиятсвятнаживо", "soulseeker": "ду<PERSON><PERSON>вн<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "преодолявамго", "openttd": "опенттд", "onepiecebountyrush": "единпар<PERSON><PERSON>ънти<PERSON><PERSON><PERSON>ш", "moonchaistory": "лунниприключения", "carxdriftracingonline": "carxдрифтрациранеонлайн", "jogosmobile": "мобилниигри", "legendofneverland": "легендазаневърленд", "pubglite": "пабг<PERSON>айт", "gamemobilelegends": "играемобиллегендии", "timeraiders": "тимерайдъри", "gamingmobile": "gamingнаmobilen", "marvelstrikeforce": "марвелсракфорс", "thebattlecats": "биткитекотки", "dnd": "днд", "quest": "приключение", "giochidiruolo": "игривиигри", "dnd5e": "днд5е", "rpgdemesa": "rpgдемесa", "worldofdarkness": "светанамрак", "travellerttrpg": "пътешественицитрпг", "2300ad": "2300г", "larp": "лар<PERSON>", "romanceclub": "романсклуб", "d20": "d20", "pokemongames": "покемонигри", "pokemonmysterydungeon": "покемонмистериентунел", "pokemonlegendsarceus": "покемонлегендиарцеус", "pokemoncrystal": "покемонкристал", "pokemonanime": "покемонаниме", "pokémongo": "покемондохубаво", "pokemonred": "покемончервен", "pokemongo": "покемонго", "pokemonshowdown": "покемонсблъскване", "pokemonranger": "покемонтърсач", "lipeep": "лилипип", "porygon": "пориگون", "pokemonunite": "покемоновсъюз", "entai": "ентай", "hypno": "хипно", "empoleon": "емполеон", "arceus": "арце<PERSON>с", "mewtwo": "мюt<PERSON>", "paldea": "палдея", "pokemonscarlet": "покемонсарлет", "chatot": "чатоt", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "покемоновиолет", "pokemonpurpura": "покемонпурпура", "ashketchum": "ашкечъм", "gengar": "<PERSON><PERSON><PERSON>", "natu": "нату", "teamrocket": "екипракета", "furret": "фурет", "magikarp": "магика<PERSON>п", "mimikyu": "мимик<PERSON>ю", "snorlax": "снорлакс", "pocketmonsters": "джобнизвяри", "nuzlocke": "нузлок", "pokemonplush": "покемонмекаплюшки", "teamystic": "тимистик", "pokeball": "покебол", "charmander": "чар<PERSON><PERSON>ндер", "pokemonromhack": "покемонромхак", "pubgmobile": "pubgmobile", "litten": "бумтящо", "shinypokemon": "блестящипокемони", "mesprit": "месприт", "pokémoni": "покемони", "ironhands": "железниръце", "kabutops": "кабутопс", "psyduck": "псайдък", "umbreon": "умбреон", "pokevore": "покевор", "ptcg": "птицг", "piplup": "пиплуп", "pokemonsleep": "покемонсън", "heyyoupikachu": "хеййоубикачу", "pokémonmaster": "покемонтреньор", "pokémonsleep": "покемонсън", "kidsandpokemon": "децаипокемони", "pokemonsnap": "покемонснап", "bulbasaur": "бул<PERSON>а<PERSON><PERSON>вър", "lucario": "лукарио", "charizar": "чаразар", "shinyhunter": "бляскавловец", "ajedrez": "шах", "catur": "коткоарт", "xadrez": "шах", "scacchi": "шах", "schaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skak": "скак", "ajedres": "шах", "chessgirls": "шахматнидевойки", "magnuscarlsen": "магнускарлсен", "worldblitz": "световенблиц", "jeudéchecs": "шахматнаигра", "japanesechess": "японскашахмат", "chinesechess": "китайшкахарда", "chesscanada": "шахканада", "fide": "фиде", "xadrezverbal": "шахматенразговор", "openings": "открития", "rook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "калабозосидракони", "dungeonsanddragon": "подземиятаидраконите", "dungeonmaster": "мастърнаподземието", "tiamat": "тиамат", "donjonsetdragons": "донжонитеидраконите", "oxventure": "оксприключение", "darksun": "тъменслънце", "thelegendofvoxmachina": "легендазавоксмашина", "doungenoanddragons": "донгенаидраконите", "darkmoor": "тъменмор", "minecraftchampionship": "майнкрафтшампионат", "minecrafthive": "май<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftbedrock": "майнкрафтбедрок", "dreamsmp": "мечтасп", "hermitcraft": "хермитк<PERSON><PERSON><PERSON>т", "minecraftjava": "майнкрафтджава", "hypixelskyblock": "хипикселскайблок", "minetest": "минетест", "hypixel": "хипиксел", "karmaland5": "кормаланд5", "minecraftmods": "майнкрафтмодовe", "mcc": "мцц", "candleflame": "свещенпламък", "fru": "фру", "addons": "добавки", "mcpeaddons": "mcpeнадстройки", "skyblock": "небесенблок", "minecraftpocket": "minecraftджобно", "minecraft360": "майнкрафт360", "moddedminecraft": "мод<PERSON><PERSON><PERSON>craft", "minecraftps4": "майнкрафтпс4", "minecraftpc": "майнкрафтпц", "betweenlands": "междуземията", "minecraftdungeons": "майнкравтгеоيسي", "minecraftcity": "майнкрафтград", "pcgamer": "пцг<PERSON>ймър", "jeuxvideo": "игралниигри", "gambit": "гамбито", "gamers": "геймъри", "levelup": "вдигнинивото", "gamermobile": "геймърмобил", "gameover": "играта<PERSON>върши", "gg": "гг", "pcgaming": "пцгейминг", "gamen": "геймър", "oyunoynamak": "играйигри", "pcgames": "пцигри", "casualgaming": "свободнаигра", "gamingsetup": "игралназаставка", "pcmasterrace": "пкмайсторскараса", "pcgame": "пцигра", "gamerboy": "геймърскомомче", "vrgaming": "vrигри", "drdisrespect": "докторнедопустимост", "4kgaming": "4к<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "gamerbr": "геймърбр", "gameplays": "геймплейове", "consoleplayer": "конзолист", "boxi": "бокси", "pro": "профи", "epicgamers": "епикгеймъри", "onlinegaming": "онлайнигри", "semigamer": "полуси<PERSON><PERSON><PERSON>", "gamergirls": "геймърки", "gamermoms": "геймърмамите", "gamerguy": "геймърмачо", "gamewatcher": "играчнаблюдател", "gameur": "геймър", "grypc": "грипц", "rangugamer": "рангугеймър", "gamerschicas": "геймърки", "otoge": "отога", "dedsafio": "дедсфу", "teamtryhard": "отборноусилване", "mallugaming": "малуигри", "pawgers": "павгери", "quests": "куестове", "alax": "алакс", "avgn": "avgн", "oldgamer": "стар<PERSON><PERSON><PERSON><PERSON><PERSON>ър", "cozygaming": "уютноигране", "gamelpay": "gamelpay", "juegosdepc": "игрипц", "dsswitch": "dsswitch", "competitivegaming": "конкурентниигри", "minecraftnewjersey": "майнкрафтнюджърси", "faker": "фейк", "pc4gamers": "кп4геймъри", "gamingff": "игридокафе", "yatoro": "яторово", "heterosexualgaming": "хетеросексуалноигране", "gamepc": "игр<PERSON><PERSON><PERSON>ц", "girlsgamer": "момичеиграч", "fnfmods": "фнфмодове", "dailyquest": "ежедневносъстезание", "gamegirl": "играчка", "chicasgamer": "геймърки", "gamesetup": "игралнаинсталация", "overpowered": "прекаленосилен", "socialgamer": "социа<PERSON><PERSON>нгеймър", "gamejam": "игралнонашествие", "proplayer": "профиграчи", "roleplayer": "ролеплейър", "myteam": "миятим", "republicofgamers": "републиканагеймерите", "aorus": "аоръс", "cougargaming": "кугъригейминг", "triplelegend": "триуправляващи", "gamerbuddies": "геймърскиприятели", "butuhcewekgamers": "носимескамгеймъри", "christiangamer": "християнисигровец", "gamernerd": "геймърнърд", "nerdgamer": "нердгеймър", "afk": "афк", "andregamer": "анд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "casualgamer": "непринуденгеймър", "89squad": "89отбор", "inicaramainnyagimana": "иничарaмайннягимана", "insec": "неспокоен", "gemers": "геймери", "oyunizlemek": "гледамигри", "gamertag": "геймъртег", "lanparty": "ланпарти", "videogamer": "видеоигр<PERSON>ч", "wspólnegranie": "заедноиграем", "mortdog": "мортдог", "playstationgamer": "плейсте<PERSON><PERSON>ънгеймър", "justinwong": "джъстинвонг", "healthygamer": "здравигеймъри", "gtracing": "gtracing", "notebookgamer": "блокнотаплейър", "protogen": "протоген", "womangamer": "wоман<PERSON><PERSON><PERSON>мър", "obviouslyimagamer": "очевидносъмгеймър", "mario": "марио", "papermario": "хартиямарио", "mariogolf": "мариоголф", "samusaran": "само<PERSON><PERSON><PERSON><PERSON><PERSON>д", "forager": "бер<PERSON><PERSON>", "humanfallflat": "човекпадаплоско", "supernintendo": "супернидо", "nintendo64": "нинтендо64", "zeroescape": "нулевоизбягване", "waluigi": "wa<PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendoswitch": "ниндентосвич", "nintendosw": "нинтендоsw", "nintendomusic": "нинтендомузика", "sonicthehedgehog": "сониксимпатягата", "sonic": "соник", "fallguys": "падналитлонзе", "switch": "сменям", "zelda": "зелда", "smashbros": "смачкайброове", "legendofzelda": "легенданазелда", "splatoon": "сплатун", "metroid": "метроид", "pikmin": "пикмин", "ringfit": "рингфит", "amiibo": "амиибо", "megaman": "мегамен", "majorasmask": "мамзамаска", "mariokartmaster": "мариокартмайстор", "wii": "вии", "aceattorney": "асистентнаадвокат", "ssbm": "ссбм", "skychildrenofthelight": "небеснитедецанасветлината", "tomodachilife": "томодачилиф", "ahatintime": "шаватавреме", "tearsofthekingdom": "сълзитена<PERSON><PERSON><PERSON>王ството", "walkingsimulators": "ходещи_симулатори", "nintendogames": "нintendoигри", "thelegendofzelda": "легендатаназелда", "dragonquest": "драконовотърсене", "harvestmoon": "жътвеналуна", "mariobros": "мариоброс", "runefactory": "рунефактори", "banjokazooie": "банжоказуи", "celeste": "целесте", "breathofthewild": "въздухнатабалката", "myfriendpedro": "моятприятелпедро", "legendsofzelda": "легендитеназелда", "donkeykong": "доникиконг", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51игри", "earthbound": "земен", "tales": "истории", "raymanlegends": "райманлегендс", "luigismansion": "луидамата", "animalcrosssing": "животнипресичане", "taikonotatsujin": "тайконатацудзин", "nintendo3ds": "нинтендо3дс", "supermariobros": "супермериоброс", "mariomaker2": "мариомейкър2", "boktai": "бок<PERSON>ай", "smashultimate": "смачкайултиматум", "nintendochile": "nintendoчиле", "tloz": "тлоz", "trianglestrategy": "триъгълнастратегия", "supermariomaker": "супермариомейкър", "xenobladechronicles3": "ксеноблейдхроникълс3", "supermario64": "супермарио64", "conkersbadfurday": "конкерсзлошафедневие", "nintendos": "ниндентос", "new3ds": "нов3дс", "donkeykongcountry2": "донкиконгстрана2", "hyrulewarriors": "хироелскивоини", "mariopartysuperstars": "мариопартиторчета", "marioandsonic": "мариоисоник", "banjotooie": "банжотооие", "nintendogs": "нтинедогс", "thezelda": "дзелда", "palia": "палия", "marioandluigi": "мариоипаджата", "mariorpg": "мариорпг", "zeldabotw": "зълдаботв", "yuumimain": "юумимейн", "wildrift": "дивоотклонение", "riven": "ривен", "ahri": "ахри", "illaoi": "илаои", "aram": "арам", "cblol": "цблол", "leagueoflegendslas": "лиганаблегендитесл", "urgot": "ургот", "zyra": "зира", "redcanids": "червенитеканиди", "vanillalol": "ванилалол", "wildriftph": "wildriftбг", "lolph": "смехотия", "leagueoflegend": "лиганалегендите", "tốcchiến": "токчиян", "gragas": "грагас", "leagueoflegendswild": "лигаанлегендивайлд", "adcarry": "adcarry", "lolzinho": "лоловче", "leagueoflegendsespaña": "лиганадоветеиспания", "aatrox": "атрокс", "euw": "еув", "leagueoflegendseuw": "лиганад<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "кайл", "samira": "самирa", "akali": "акали", "lunari": "лунари", "fnatic": "фнатик", "lollcs": "лулзс", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "милио", "shaco": "шако", "ligadaslegendas": "свързанилегенди", "gaminglol": "играенерлол", "nasus": "насус", "teemo": "тимо", "zedmain": "зедмейн", "hexgates": "хексгейтс", "hextech": "хекстек", "fortnitegame": "фортнайтгейм", "gamingfortnite": "геймингфор<PERSON><PERSON>йт", "fortnitebr": "фортнайтбр", "retrovideogames": "ретровидеоигри", "scaryvideogames": "страшнивидеоигри", "videogamemaker": "създавайигри", "megamanzero": "мегаманзеро", "videogame": "видеоигра", "videosgame": "видеоигри", "professorlayton": "професор<PERSON>ейтън", "overwatch": "превъзходеннаблюдател", "ow2": "ow2", "overwatch2": "овервач2", "wizard101": "магьосник101", "battleblocktheater": "бойноигралище", "arcades": "аркади", "acnh": "анх", "puffpals": "пухкавиприятели", "farmingsimulator": "фермерсимулатор", "robloxchile": "роблоксчиле", "roblox": "роблокси", "robloxdeutschland": "роблоксгермания", "robloxdeutsch": "роблокспарашки", "erlc": "ерлц", "sanboxgames": "пясъчниигри", "videogamelore": "видеоигръстории", "rollerdrome": "ролердром", "parasiteeve": "параситчиви", "gamecube": "играчка", "starcraft2": "старкрафт2", "duskwood": "дъсквуд", "dreamscape": "съннаприрода", "starcitizen": "звезденгражданин", "yanderesimulator": "яндересимулатор", "grandtheftauto": "грандафтотео", "deadspace": "мъртвопространство", "amordoce": "амордоце", "videogiochi": "видеоигри", "theoldrepublic": "старатарепублика", "videospiele": "играйгейминга", "touhouproject": "touhouпроект", "dreamcast": "мечтател", "adventuregames": "приключенскиигри", "wolfenstein": "волфенщайн", "actionadventure": "екшънприключение", "storyofseasons": "историянасезоните", "retrogames": "ретрогеймс", "retroarcade": "ретроаркада", "vintagecomputing": "винтажникомпютри", "retrogaming": "ретрогейминг", "vintagegaming": "винтиджгейминг", "playdate": "игратеренде", "commanderkeen": "командиркина", "bugsnax": "бъг<PERSON><PERSON><PERSON>с", "injustice2": "несправедливост2", "shadowthehedgehog": "сянкатащитко", "rayman": "раймън", "skygame": "небеснатаигра", "zenlife": "зенживот", "beatmaniaiidx": "бийман<PERSON>йдкс", "steep": "стръмно", "mystgames": "мистигри", "blockchaingaming": "блокчейнигри", "medievil": "медиевилен", "consolegaming": "конзолигейминг", "konsolen": "конзолен", "outrun": "изпревари", "bloomingpanic": "цъфтящпаник", "tobyfox": "тобифокс", "hoyoverse": "хойоверс", "senrankagura": "сенранкагура", "gaminghorror": "геймингужасия", "monstergirlquest": "монстрогърлкуест", "supergiant": "супергигант", "disneydreamlightvalle": "диснидримлайтвалли", "farmingsims": "фермерскисимулатори", "juegosviejos": "стариигри", "bethesda": "бетесда", "jackboxgames": "jackboxgames", "interactivefiction": "интерактивнафантастика", "pso2ngs": "псо2нгс", "grimfandango": "гримфанданго", "thelastofus2": "последниятнас2", "amantesamentes": "влюбенизабавления", "visualnovel": "визуаленроман", "visualnovels": "визуалниромани", "rgg": "ргг", "shadowolf": "сени<PERSON>", "tcrghost": "тцргост", "payday": "заплата", "chatherine": "катерина", "twilightprincess": "залезнапринцеса", "jakandaxter": "д<PERSON><PERSON><PERSON><PERSON>ндакстер", "sandbox": "песъчник", "aestheticgames": "естетическитеигри", "novelavisual": "новелавизуал", "thecrew2": "екип2", "alexkidd": "алексидд", "retrogame": "ретрогейм", "tonyhawkproskater": "тонихокпроскейтър", "smbz": "съмбз", "lamento": "ла<PERSON>енти<PERSON><PERSON>м", "godhand": "божественаръка", "leafblowerrevolution": "революциянадувателязалисти", "wiiu": "вииу", "leveldesign": "уробоство", "starrail": "звезднопътуване", "keyblade": "ключоватаэзда", "aplaguetale": "аплакетейл", "fnafsometimes": "fnafsмалкорано", "novelasvisuales": "визуалниновели", "robloxbrasil": "роблоксбразилия", "pacman": "пакман", "gameretro": "геймретро", "videojuejos": "видеоигри", "videogamedates": "геймдата", "mycandylove": "моятас<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "megaten": "мега10", "mortalkombat11": "мортълкомбат11", "everskies": "евърскайс", "justcause3": "всичкоезапоради3", "hulkgames": "хулкигри", "batmangames": "батманигри", "returnofreckoning": "възвращениенаприговорa", "gamstergaming": "геймстъргейминг", "dayofthetantacle": "денятнащипалото", "maniacmansion": "маниякъщата", "crashracing": "разбиванесъстезания", "3dplatformers": "3dплатформъри", "nfsmw": "нфсмв", "kimigashine": "ким<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "oldschoolgaming": "староучилищноиграене", "hellblade": "адътепечата", "storygames": "игриразкази", "bioware": "биоваре", "residentevil6": "резидентивълк6", "soundodger": "звукоджер", "beyondtwosouls": "извъндвеуши", "gameuse": "играйсбoo", "offmortisghost": "офмортиспризрак", "tinybunny": "малъкзаек", "retroarch": "ретроаркад", "powerup": "вдигнисилата", "katanazero": "катаназеро", "famicom": "фамиком", "aventurasgraficas": "графичниприключения", "quickflash": "бързосветка", "fzero": "фзеро", "gachagaming": "гашаигри", "retroarcades": "ретроаркади", "f123": "f123", "wasteland": "пустош", "powerwashsim": "пауърошсими", "coralisland": "кораловостров", "syberia3": "сибирия3", "grymmorpg": "гриммрпг", "bloxfruit": "блоксфрут", "anotherworld": "другсвят", "metaquest": "метаквест", "animewarrios2": "анимевоини2", "footballfusion": "футболнfusion", "edithdlc": "едитхдлц", "abzu": "абзу", "astroneer": "астронер", "legomarvel": "легомарвел", "wranduin": "врандутин", "twistedmetal": "изкривенметал", "beamngdrive": "beamngdrive", "twdg": "тwdg", "pileofshame": "купчиналодижи", "simulator": "симулатор", "symulatory": "симулатори", "speedrunner": "спидр<PERSON><PERSON><PERSON>р", "epicx": "епикс", "superrobottaisen": "суперроботайзен", "dcuo": "дцуо", "samandmax": "самиима<PERSON>с", "grywideo": "гривайдео", "gaiaonline": "гайа<PERSON><PERSON><PERSON><PERSON><PERSON>н", "korkuoyunu": "кърквиюн", "wonderlandonline": "приказенсвятонлайн", "skylander": "небесенвоин", "boyfrienddungeon": "приятелскатавдънжин", "toontownrewritten": "пренаписанототунтауни", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "пвп", "urbanchaos": "градскихаос", "heavenlybodies": "небесниразкопки", "seum": "съум", "partyvideogames": "партиигри", "graveyardkeeper": "гробар_keeper", "spaceflightsimulator": "космическиполетсимулатор", "legacyofkain": "наследствотонакайн", "hackandslash": "хаквайисечи", "foodandvideogames": "хранкаивидеоигри", "oyunvideoları": "игравидеа", "thewolfamongus": "вълкътсреднасъщи", "truckingsimulator": "транспортенсимулатор", "horizonworlds": "хоризонтовиселища", "handygame": "удобнаигра", "leyendasyvideojuegos": "легендипивидеиигри", "oldschoolvideogames": "стариучилищнивидеоигри", "racingsimulator": "симулаторзад赛车", "beemov": "б<PERSON><PERSON><PERSON><PERSON>", "agentsofmayhem": "агентинамечтите", "songpop": "песенпоп", "famitsu": "фамицу", "gatesofolympus": "вратитенаолимп", "monsterhunternow": "монстърловецсега", "rebelstar": "бунтарсказвезда", "indievideogaming": "индиигри", "indiegaming": "индигейминг", "indievideogames": "индиигри", "indievideogame": "индиигравнаигра", "chellfreeman": "чел<PERSON><PERSON><PERSON><PERSON><PERSON>н", "spidermaninsomniac": "паяквлинията", "bufffortress": "буффортрес", "unbeatable": "неудържим", "projectl": "проектл", "futureclubgames": "игрибудещето", "mugman": "чашкодържача", "insomniacgames": "инсомниакгеймс", "supergiantgames": "супергигантскиигри", "henrystickman": "хенристикмън", "henrystickmin": "хенритикмин", "celestegame": "целестегейм", "aperturescience": "апертуранаука", "backlog": "задължения", "gamebacklog": "игровизостанали", "gamingbacklog": "игралнасписъканазад", "personnagejeuxvidéos": "персонажигриავтомобили", "achievementhunter": "ловецнауспехи", "cityskylines": "градскипейзажи", "supermonkeyball": "супермаймунскатопка", "deponia": "депония", "naughtydog": "непослушенпесок", "beastlord": "зверскигосподар", "juegosretro": "ретроигри", "kentuckyroutezero": "кентъкирутнуло", "oriandtheblindforest": "ориисляпатагора", "alanwake": "аланвейк", "stanleyparable": "станлиеп<PERSON><PERSON><PERSON>б<PERSON>л", "reservatoriodedopamin": "резерваторидедопамин", "staxel": "стаксъл", "videogameost": "видеоигралносаундтрак", "dragonsync": "дракон<PERSON><PERSON><PERSON><PERSON>х", "vivapiñata": "живетопината", "ilovekofxv": "обичамкофxв", "arcanum": "арканум", "neoy2k": "нео2к", "pcracing": "псирей<PERSON><PERSON>нг", "berserk": "бясно", "baki": "баги", "sailormoon": "сейлормун", "saintseiya": "сейнтсейя", "inuyasha": "инюйаша", "yuyuhakusho": "ююхакашо", "initiald": "началод", "elhazard": "елхазард", "dragonballz": "драконовоцарствоz", "sadanime": "тъженаниме", "darkerthanblack": "потъменотчерно", "animescaling": "анимескалиране", "animewithplot": "аним<PERSON><PERSON>ъссъспил", "pesci": "песци", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентая", "samuraichamploo": "самурайсамплу", "madoka": "мадока", "higurashi": "хигу<PERSON><PERSON><PERSON>и", "80sanime": "80сanime", "90sanime": "90са<PERSON><PERSON><PERSON><PERSON>", "darklord": "тъменлорд", "popeetheperformer": "попипрогласителя", "masterpogi": "мегафаворити", "samuraix": "самура<PERSON>х", "dbgt": "дбгт", "veranime": "вераниме", "2000sanime": "анимеот2000те", "lupiniii": "лупиниии", "drstoneseason1": "дрстоунсезон1", "rapanime": "рапаниме", "chargemanken": "заредимъжете", "animecover": "анимеобложка", "thevisionofescaflowne": "визият<PERSON>насъществото", "slayers": "убийци", "tokyomajin": "токйомаджин", "anime90s": "аниме90те", "animcharlotte": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "gantz": "ганд<PERSON>", "shoujo": "шоджо", "bananafish": "банановарнаха", "jujutsukaisen": "дж<PERSON><PERSON><PERSON><PERSON>цукайсен", "jjk": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "haikyu": "ха<PERSON><PERSON>ю", "toiletboundhanakokun": "тоалетносвързанханакокун", "bnha": "бнха", "hellsing": "хе<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbeatmanga": "скъпбитманга", "vanitas": "ванитас", "fireforce": "огненадесетка", "moriartythepatriot": "мориартипатриот", "futurediary": "днев<PERSON><PERSON>кнабъдещето", "fairytail": "приказенхвост", "dorohedoro": "дороходоро", "vinlandsaga": "винландскасага", "madeinabyss": "създаденовбездна", "parasyte": "паразит", "punpun": "пънпън", "shingekinokyojin": "шингеконокьоджин", "mushishi": "мушиши", "beastars": "бист<PERSON><PERSON><PERSON>", "vanitasnocarte": "ванитаснокартe", "mermaidmelody": "русалкамелодия", "kamisamakiss": "камиса<PERSON><PERSON><PERSON><PERSON>ис", "blmanga": "благозначителнаманга", "horrormanga": "ужасманга", "romancemangas": "романтичниманги", "karneval": "ка<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "драконка", "blacklagoon": "черноезеро", "kentaromiura": "кентаромиура", "mobpsycho100": "мобпсихо100", "terraformars": "терраформари", "geniusinc": "гениксинк", "shamanking": "шаманката", "kurokonobasket": "куроконобаскет", "jugo": "жунго", "bungostraydogs": "бунгострайдогс", "jujustukaisen": "джъсту<PERSON><PERSON>йсън", "jujutsu": "жужу<PERSON>у", "yurionice": "юрионаистина", "acertainmagicalindex": "определенмагическииндекс", "sao": "сао", "blackclover": "черендетелина", "tokyoghoul": "токийскигул", "onepunchman": "единиченудармъж", "hetalia": "хеталия", "kagerouproject": "кагероuproject", "haikyuu": "ха<PERSON><PERSON>ю", "toaru": "тоару", "crunchyroll": "кранчир<PERSON>л", "aot": "аот", "sk8theinfinity": "sk8безкрайността", "siriusthejaeger": "сириусдетектив", "spyxfamily": "шпионисъссемейство", "rezero": "резеро", "swordartonline": "мечовизуаленонлайн", "dororo": "дороро", "wondereggpriority": "приоритетнаяйцечудо", "angelsofdeath": "ангелитенасмъртта", "kakeguri": "какегури", "dragonballsuper": "драгонболсупер", "hypnosismic": "хипнозисмик", "goldenkamuy": "златенкамуй", "monstermusume": "монстрмусуме", "konosuba": "коносуба", "aikatsu": "aikatsu", "sportsanime": "спортаниме", "sukasuka": "суксукa", "arwinsgame": "арвинсигра", "angelbeats": "ангелскиудар", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "сагаоттанязлото", "shounenanime": "шуненаниме", "bandori": "бандори", "tanya": "таня", "durarara": "дурарара", "prettycure": "красиволечение", "theboyandthebeast": "момчетоидивотното", "fistofthenorthstar": "пл拳отсеверназвезда", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "черендупе", "towerofgod": "куланагоспода", "elfenlied": "елфенлид", "akunohana": "акунохана", "chibi": "чиби", "servamp": "сервамп", "howtokeepamummy": "какдаизмамата", "fullmoonwosagashite": "пълнолуниевосагашите", "shugochara": "шугочара", "tokyomewmew": "токйомюму", "gugurekokkurisan": "гугурекокурисан", "cuteandcreepy": "сладкоиужасно", "martialpeak": "бойнавр峰", "bakihanma": "благодарябуки", "hiscoregirl": "високреакзабавление", "orochimaru": "орочимару", "mierukochan": "мирукочан", "dabi": "да<PERSON>и", "johnconstantine": "джонконстантин", "astolfo": "астолфо", "revanantfae": "реванантфея", "shinji": "шин<PERSON><PERSON>и", "zerotwo": "нуладве", "inosuke": "иносуке", "nezuko": "незуко", "monstergirl": "монстерв女孩", "kanae": "канае", "yone": "йоне", "mitsuki": "митсуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "сайтама", "sanji": "сан<PERSON>и", "bakugo": "бакъго", "griffith": "грифит", "ririn": "риерин", "korra": "кора", "vanny": "ванички", "vegeta": "вегета", "goromi": "гороми", "luci": "луци", "reigen": "райген", "scaramouche": "скарамуш", "amiti": "амиити", "sailorsaturn": "мореплавателиsатурн", "dio": "дио", "sailorpluto": "сейлърплуто", "aloy": "алой", "runa": "руна", "oldanime": "старааниме", "chainsawman": "бензопилачовек", "bungoustraydogs": "бунгострейдога", "jogo": "жого", "franziska": "франциска", "nekomimi": "некомими", "inumimi": "инумими", "isekai": "исекай", "tokyorevengers": "токийскиотмъстители", "blackbutler": "черенслуга", "ergoproxy": "ергопрокси", "claymore": "клеймор", "loli": "лоли", "horroranime": "хорораниме", "fruitsbasket": "плодовакишка", "devilmancrybaby": "дяволътплачеобаче", "noragami": "норагами", "mangalivre": "mangalивре", "kuroshitsuji": "курошицуџи", "seinen": "сейнън", "lovelive": "жийвлюбовта", "sakuracardcaptor": "сакуракардкептър", "umibenoetranger": "умибеноупотребяваш", "owarinoseraph": "овариносераф", "thepromisedneverland": "обетованастрана", "monstermanga": "монстрманга", "yourlieinapril": "твоятлъжинааприл", "buggytheclown": "багит<PERSON><PERSON>lown", "bokunohero": "богътнагероите", "seraphoftheend": "серафнакрая", "trigun": "тригън", "cyborg009": "киборг009", "magi": "маги", "deepseaprisoner": "дълбочинензатворник", "jojolion": "джоджолион", "deadmanwonderland": "мъртвецвстрананачудесата", "bannafish": "ба<PERSON><PERSON><PERSON><PERSON>", "sukuna": "сукуна", "darwinsgame": "игра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "хъсбу", "sugurugeto": "сугуругето", "leviackerman": "левиакерман", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "пандорасърца", "yoimiya": "йоимија", "foodwars": "битказахрана", "cardcaptorsakura": "кардикаптърсакура", "stolas": "столас", "devilsline": "дяволскалиния", "toyoureternity": "твойвечност", "infpanime": "инфпанеем", "eleceed": "елисид", "akamegakill": "акамегакил", "blueperiod": "синпериод", "griffithberserk": "грифит<PERSON>е<PERSON>ен", "shinigami": "шинегамии", "secretalliance": "тайнаалианс", "mirainikki": "мироаники", "mahoutsukainoyome": "махоцукайножена", "yuki": "юки", "erased": "изтрито", "bluelock": "синякатастрофа", "goblinslayer": "гоблинубиец", "detectiveconan": "детективконан", "shiki": "шики", "deku": "дека", "akitoshinonome": "акитошинономе", "riasgremory": "риасгремори", "shojobeat": "шо<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vampireknight": "вампирскирицар", "mugi": "муги", "blueexorcist": "синияекзорцист", "slamdunk": "слъмданк", "zatchbell": "зачетбел", "mashle": "машле", "scryed": "сънгледах", "spyfamily": "шпионскосемейство", "airgear": "въздушниколела", "magicalgirl": "магическадевойка", "thesevendeadlysins": "седемтесмъртнигряха", "prisonschool": "затворноучилище", "thegodofhighschool": "богътнасредношколите", "kissxsis": "целувки_сестри", "grandblue": "грандиозносиньо", "mydressupdarling": "моятаиграчказаоблекло", "dgrayman": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "rozenmaiden": "розенмайдън", "animeuniverse": "анимевселената", "swordartonlineabridge": "мечовтоизкуствосъздадено", "saoabridged": "saoсъкратено", "hoshizora": "хошизора", "dragonballgt": "драгонболгт", "bocchitherock": "бочитехата", "kakegurui": "какегуруи", "mobpyscho100": "мобпсихо100", "hajimenoippo": "първастъпка", "undeadunluck": "недървенанещастие", "romancemanga": "романеманга", "blmanhwa": "блманхва", "kimetsunoyaba": "кимецунояба", "kohai": "кохай", "animeromance": "анимеро<PERSON><PERSON>нс", "senpai": "сенпай", "blmanhwas": "блманиваски", "animeargentina": "аниме<PERSON><PERSON><PERSON><PERSON>нтина", "lolicon": "лолитон", "demonslayertothesword": "демонслеърдосъ剑", "bloodlad": "кървавафамилия", "goodbyeeri": "сбогомери", "firepunch": "огнемация", "adioseri": "сбогомери", "tatsukifujimoto": "татсукифудзимото", "kinnikuman": "кин<PERSON>юмън", "mushokutensei": "мушкакугенсей", "shoujoai": "шоджьо<PERSON>й", "starsalign": "звездитесеподредят", "romanceanime": "романтичнианиме", "tsundere": "цундере", "yandere": "яндере", "mahoushoujomadoka": "махошоумадока", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "саоинтегралфактор", "cherrymagic": "черешовамагия", "housekinokuni": "домашнокинокуні", "recordragnarok": "записиратнарок", "oyasumipunpun": "оясумпунпун", "meliodas": "мелиодас", "fudanshi": "фудан<PERSON>и", "retromanga": "ретроманга", "highschoolofthedead": "училищезомбитата", "germantechno": "немскитехно", "oshinoko": "ошиноко", "ansatsukyoushitsu": "ансатсукияушицу", "vindlandsaga": "виндландсага", "mangaka": "мангака", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "принцанатениса", "tonikawa": "тоникава", "esdeath": "есдетх", "dokurachan": "докурачан", "bjalex": "бялекс", "assassinclassroom": "класнасасините", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "състезаниесмъртта", "shokugekinosouma": "шокугекинносоума", "japaneseanime": "японскааниме", "animespace": "анимепространство", "girlsundpanzer": "момичетаунпанцер", "akb0048": "акб0048", "hopeanuoli": "надявамесе", "animedub": "анимедуб", "animanga": "аниманга", "tsurune": "цуруне", "uqholder": "uqholder", "indieanime": "индианиме", "bungoustray": "бунгостаен", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "гундам0", "animescifi": "ани<PERSON><PERSON><PERSON><PERSON><PERSON>фай", "ratman": "плъхчо", "haremanime": "зайчоаниме", "kochikame": "котешкитекенота", "nekoboy": "некобой", "gashbell": "гашбел", "peachgirl": "прасковеномомиче", "cavalieridellozodiaco": "кавалериделозодиако", "mechamusume": "мехамусуме", "nijigasaki": "ниджигасаки", "yarichinbitchclub": "яричи<PERSON>б<PERSON>чклуб", "dragonquestdai": "драконскамисиядай", "heartofmanga": "сърцетонамanga", "deliciousindungeon": "вкусноисподземието", "manhviyaoi": "манхвияои", "recordofragnarok": "записаниваргнарок", "funamusea": "фанамуза", "hiranotokagiura": "хиранотокагира", "mangaanime": "мангааниме", "bochitherock": "бочитърок", "kamisamahajimemashita": "камисамахаджимемашта", "skiptoloafer": "пропуснилафер", "shuumatsunovalkyrie": "шуматсуновалкирия", "tutorialistoohard": "учебницитеосложни", "overgeared": "прекаленообутан", "toriko": "торико", "ravemaster": "равемастър", "kkondae": "ккондей", "chobits": "чобитс", "witchhatatelier": "ателиезафеи", "lansizhui": "ла<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангатсунолион", "kamen": "камень", "mangaislife": "манга<PERSON><PERSON>живот", "dropsofgod": "капкиготовост", "loscaballerosdelzodia": "култу<PERSON><PERSON><PERSON><PERSON>натаочки", "animeshojo": "анимесяо", "reverseharem": "обратен<PERSON>а<PERSON>ем", "saintsaeya": "сейнтсейя", "greatteacheronizuka": "великятучителонизука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "солдато", "mybossdaddy": "мояшефтатко", "gear5": "ппп5", "grandbluedreaming": "грандсинимечти", "bloodplus": "кръвплюс", "bloodplusanime": "кровплюсаниме", "bloodcanime": "кровосказки", "bloodc": "кръвта", "talesofdemonsandgods": "историиотдемониигоди", "goreanime": "гореаниме", "animegirls": "анимедевици", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "вранитехората", "splatteranime": "сплътераниме", "splatter": "плюнчица", "risingoftheshieldhero": "възходътнащитенагероя", "somalianime": "сомалийскааниме", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "успехнатаксуване", "animeyuri": "анимеюри", "animeespaña": "анимеиспания", "animeciudadreal": "анимеградреал", "murim": "мурим", "netjuunosusume": "нетюносусуме", "childrenofthewhales": "децатанакилера", "liarliar": "лъже<PERSON>лъж<PERSON>ц", "supercampeones": "суперкапанчета", "animeidols": "анимеидоли", "isekaiwasmartphone": "исекайбяхсмартфон", "midorinohibi": "мидо<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>би", "magicalgirls": "магическитемомичета", "callofthenight": "повикванетонанощта", "bakuganbrawler": "бакуганброулер", "bakuganbrawlers": "бакуганджамбъри", "natsuki": "нацуки", "mahoushoujo": "махоушоуджо", "shadowgarden": "сенчеста花花", "tsubasachronicle": "цубасачтения", "findermanga": "намериманга", "princessjellyfish": "принцесамедузка", "kuragehime": "куражехими", "paradisekiss": "райскоцелувка", "kurochan": "курочан", "revuestarlight": "ревюзведназвезда", "animeverse": "анимевселената", "persocoms": "персоналникомпютри", "omniscientreadersview": "всезнаещотопоглежданеначитателя", "animecat": "анимекотка", "animerecommendations": "анимепрепоръки", "openinganime": "анимеотваряне", "shinichirowatanabe": "шин치уроватанабе", "uzumaki": "узумаки", "myteenromanticcomedy": "моядобричкомедия", "evangelion": "евангелион", "gundam": "гандъм", "macross": "макр<PERSON>с", "gundams": "гъндъмс", "voltesv": "волтесв", "giantrobots": "гигантскироботи", "neongenesisevangelion": "неонгенасъзидание", "codegeass": "кодг<PERSON>с", "mobilefighterggundam": "мобиленбойцюбггундам", "neonevangelion": "неоневангелion", "mobilesuitgundam": "мобилнианимегуандъм", "mech": "мех", "eurekaseven": "еурекаседем", "eureka7": "еурека7", "thebigoanime": "бигопартианиме", "bleach": "белифицировка", "deathnote": "смъртназаписка", "cowboybebop": "каубойбибоп", "jjba": "jjba", "jojosbizarreadventure": "досегашнитеабсурдниприключениянаджоджо", "fullmetalalchemist": "пълнометаленалхимик", "ghiaccio": "лед", "jojobizarreadventures": "jojобизарниприключения", "kamuiyato": "камуйято", "militaryanime": "военнианиме", "greenranger": "зеленоренджер", "jimmykudo": "jimmyкудо", "tokyorev": "токйорев", "zorro": "зоро", "leonscottkennedy": "леонскоткенеди", "korosensei": "коросенсей", "starfox": "старфокс", "ultraman": "ултраман", "salondelmanga": "салондеманга", "lupinthe3rd": "лупинтрети", "animecity": "анимеград", "animetamil": "аниметамил", "jojoanime": "джоджоаниме", "naruto": "наруто", "narutoshippuden": "нарутошипуден", "onepiece": "еднопарче", "animeonepiece": "анимееднопарче", "dbz": "дб<PERSON>", "dragonball": "драгонбол", "yugioh": "югиох", "digimon": "дигимон", "digimonadventure": "дигимонприключение", "hxh": "ххх", "highschooldxd": "гимназиядд", "goku": "goku", "broly": "броули", "shonenanime": "шоненаниме", "bokunoheroacademia": "бойнаакадемия", "jujustukaitsen": "жужустукайцен", "drstone": "докторстоун", "kimetsunoyaiba": "кимецунояиба", "shonenjump": "шounenjump", "otaka": "отаку", "hunterxhunter": "ловецвловец", "mha": "мха", "demonslayer": "убиецнадемони", "hinokamikagurademonsl": "хинокамикагурадемони", "attackontitan": "атакаспротивтитаните", "erenyeager": "ерендошен", "myheroacademia": "моятгеройакадемия", "boruto": "боруто", "rwby": "рвби", "dandadan": "да<PERSON><PERSON>а<PERSON><PERSON>н", "tomodachigame": "томодачигейм", "akatsuki": "акатсуки", "surveycorps": "анкетнияотряд", "onepieceanime": "анимееднопарче", "attaquedestitans": "атакадеститаните", "theonepieceisreal": "единственатчастеистинска", "revengers": "ревенджъри", "mobpsycho": "мобпсихо", "aonoexorcist": "аоноекзорцист", "joyboyeffect": "ефектнарадост", "digimonstory": "дигимонприказка", "digimontamers": "дигимонтамери", "superjail": "суперзатвор", "metalocalypse": "металокалипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "ватамоте", "uramichioniisan": "урамичийонисан", "uruseiyatsura": "уросейяцурав", "gintama": "гинтама", "ranma": "ранма", "doraemon": "дораемон", "gto": "гто", "ouranhostclub": "уранехостклуб", "flawlesswebtoon": "перфектенвебтун", "kemonofriends": "кемонопријатели", "utanoprincesama": "безпринцеса", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юкиюна", "nichijou": "ничиджо", "yurucamp": "юрукамп", "nonnonbiyori": "ноннобийори", "flyingwitch": "летящатавещица", "wotakoi": "вотакай", "konanime": "конаниме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "самотака", "horimiya": "хоримия", "allsaintsstreet": "всякасвятаулица", "recuentosdelavida": "разказиотживота"}