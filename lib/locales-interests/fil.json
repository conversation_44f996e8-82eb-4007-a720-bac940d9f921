{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrolohiya", "cognitivefunctions": "mgacognitivefunction", "psychology": "si<PERSON>lohiya", "philosophy": "pilosopiya", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "pisika", "science": "a<PERSON>am", "culture": "kultura", "languages": "mgalengguwahe", "technology": "teknolohiya", "memes": "mgameme", "mbtimemes": "mgambtimeme", "astrologymemes": "mgamemesaastrolohiya", "enneagrammemes": "mgaenneagrammeme", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "video", "gadgets": "mgagadget", "politics": "politika", "relationshipadvice": "payotungkolsarelasyon", "lifeadvice": "payotungkolsabuhay", "crypto": "crypto", "news": "balita", "worldnews": "balitatungkolsamundo", "archaeology": "ark<PERSON>lo<PERSON>ya", "learning": "paga<PERSON>l", "debates": "mgadebate", "conspiracytheories": "teoryangkonspirasya", "universe": "sansinukob", "meditation": "pag<PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON>", "art": "sining", "crafts": "mg<PERSON><PERSON><PERSON><PERSON>ing", "dance": "pag<PERSON>aw", "design": "desenyo", "makeup": "<PERSON><PERSON><PERSON>", "beauty": "pampaganda", "fashion": "pasiyon", "singing": "p<PERSON><PERSON><PERSON>", "writing": "pagsulat", "photography": "potograpya", "cosplay": "cosplay", "painting": "p<PERSON><PERSON><PERSON><PERSON>", "drawing": "pag<PERSON><PERSON>", "books": "mgalibro", "movies": "mgapelikula", "poetry": "mgatula", "television": "telebisyon", "filmmaking": "pag<PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "animation", "anime": "anime", "scifi": "scifi", "fantasy": "pan<PERSON>ya", "documentaries": "mgadokumentaryo", "mystery": "misteryo", "comedy": "komedya", "crime": "krimen", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "katatakutan", "romance": "romansa", "realitytv": "realitytv", "action": "aksyon", "music": "musika", "blues": "k<PERSON><PERSON>", "classical": "klasikal", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronik", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latino", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "concerts": "mgakonsyerto", "festivals": "mgapagdiriwang", "museums": "mgamuseyo", "standup": "standup", "theater": "teatro", "outdoors": "labas", "gardening": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partying": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "boardgames": "mgaboardgame", "dungeonsanddragons": "dungeonsanddragons", "chess": "tses", "fortnite": "fornite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "pag<PERSON>n", "baking": "pag<PERSON><PERSON><PERSON><PERSON><PERSON>", "cooking": "nagluluto", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "mgaibon", "cats": "mgapusa", "dogs": "mgaaso", "fish": "isda", "animals": "mgahayop", "blacklivesmatter": "blacklivesmatter", "environmentalism": "environmentalism", "feminism": "feminismo", "humanrights": "mgakarapatangpantao", "lgbtqally": "kakampingmgalgbtq", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "kakampingmgatrans", "volunteering": "pagbobol<PERSON><PERSON>", "sports": "mgaisport", "badminton": "badminton", "baseball": "beisbol", "basketball": "basketbol", "boxing": "boksing", "cricket": "kriket", "cycling": "pagbibisikleta", "fitness": "eh<PERSON><PERSON><PERSON>", "football": "putbol", "golf": "golp", "gym": "dyim", "gymnastics": "<PERSON><PERSON><PERSON>", "hockey": "hockey", "martialarts": "siningngpagtatanggol", "netball": "netbol", "pilates": "pilates", "pingpong": "pingpong", "running": "pagta<PERSON><PERSON>", "skateboarding": "skateboarding", "skiing": "iski", "snowboarding": "snowboarding", "surfing": "surping", "swimming": "p<PERSON><PERSON><PERSON>", "tennis": "tenis", "volleyball": "balibol", "weightlifting": "pagbubuhat", "yoga": "yoga", "scubadiving": "eskuba", "hiking": "hiking", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "cancer", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "<PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "pangmatagalannaug<PERSON><PERSON>", "single": "single", "polyamory": "poliamor", "enm": "ethicalnawalangmonogamy", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "bakla", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisekswal", "pansexual": "pansexual", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "mgatagasubaybay", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kquestngmgahari", "soulreaver": "sungawngkaluluwa", "suikoden": "su<PERSON><PERSON>", "subverse": "subbers", "legendofspyro": "alamatngspyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "spyroangdragon", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "diyosx", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gilangdaw", "openworld": "bukasnamundo", "heroesofthestorm": "mgabayaningbagyo", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "pagtuklasngdungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "planongbuhay", "lordsoftherealm2": "mgadiyosngrealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kulayvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "mgaimmersivesim", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "nawalanggalang", "eldenring": "eldensingsing", "darksouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "pag<PERSON><PERSON>g", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "nak<PERSON>enggan<PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "pagh<PERSON><PERSON>", "ff8": "ff8", "otome": "otome", "suckerforlove": "kagigilsapagibig", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimenyon20", "gaslands": "gaslan", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "dug<PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravityrush", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "isangbuhay", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord", "yourturntodie": "yourturn<PERSON>die", "persona3": "persona3", "rpghorror": "rpgkakatakut", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonkaluluwa", "mu": "mu", "falloutshelter": "falloutshelter", "gurps": "gurps", "darkestdungeon": "pinaka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "eclipsephase", "disgaea": "disgaea", "outerworlds": "labasngmund<PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastsiwang", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartsnapanabik", "madnesscombat": "labanangbaliw", "jaggedalliance2": "jaggedalliance2", "neverwinter": "hindingwinter", "road96": "daan96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "nalimutangmgarehiyon", "dragonlance": "dragonlance", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "anak<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "pinasalangtrono", "horizonforbiddenwest": "horizonbawalkanluran", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartsm<PERSON>y", "deltagreen": "deltagreen", "diablo": "dyablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON>", "lastepoch": "hulin<PERSON><PERSON><PERSON><PERSON>", "starfinder": "tagahanapngbituin", "goldensun": "gintongaraw", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "mgatalimsadilim", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkpula", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenorder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diyablosurvivor", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "diyosngdiwa", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "mgalumaatmalungkot", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "larong<PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sikapingaraw", "talesofsymphonia": "kwentongsymponya", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "sacredunderworld", "chainedechoes": "sulyapengkadena", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "bundokatsibat", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "mgahaliging<PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "sabog", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "alamatngdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "mgaanakngmorta", "engineheart": "<PERSON><PERSON>gma<PERSON>a", "fable3": "kuwentong3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenwalanghanggan", "finalfantasy16": "pinalnahimula16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "babaliksaoldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworlds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kahariankabutihan2", "darknessdungeon": "dilimnadugtong", "juegosrpg": "larongrpg", "kingdomhearts": "kaharianngmgapuso", "kingdomheart3": "kingdomheart3", "finalfantasy6": "pinalnasiklo6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "mgabuhaynapusongwild", "bastion": "bastyon", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitngarcadia", "shadowhearts": "an<PERSON>ang<PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "hininganagap4", "mother3": "ina3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "ibangeden", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "pusongmangkukulam", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampiramasquerade", "dračák": "drachak", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kronokros", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "karat<PERSON>ngpusosump<PERSON>", "bladesoul": "buhaybladesoul", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytaktiks", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "itblackbook", "skychildrenoflight": "mgabatanglangitngliwanag", "gryrpg": "gryrpg", "sacredgoldedition": "sagradogintoedisyon", "castlecrashers": "castlecrashers", "gothicgame": "gothicgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "gawainsagh<PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "larongrpg", "prophunt": "prophunt", "starrails": "<PERSON><PERSON><PERSON><PERSON>", "cityofmist": "lungsodngmisty", "indierpg": "indierpg", "pointandclick": "pin<PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "malayanglugar", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "daanngkamatayantungosacanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "manananggalhunter", "fireemblem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "multongtsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "hackna", "ys": "ys", "souleater": "kumakainngkaluluwa", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktikalnarpg", "mahoyo": "mahoyo", "animegames": "la<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "walangkatapusanngmelodiya", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "kristal", "vcs": "vcs", "pes": "pesot", "pocketsage": "sulipocket", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valorant<PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "elaro", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligangmgapangarap", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "pangarapkaya", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "siyensport", "crazyraccoon": "sagwangraccoon", "test1test": "test1test", "fc24": "fc24", "riotgames": "la<PERSON><PERSON>", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitibo", "t3arena": "t3arena", "valorantbr": "valorantph", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "hatingbu<PERSON>", "left4dead": "left4patay", "left4dead2": "left4patay2", "valve": "balbula", "portal": "portals", "teamfortress2": "teamfortress2", "everlastingsummer": "walangkatapusangtaginit", "goatsimulator": "goatsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "kalayaanplaneta", "transformice": "transformice", "justshapesandbeats": "justshapesandbeats", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "pu<PERSON><PERSON>pal<PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "panganibngulan2", "metroidvanias": "metroidvanias", "overcooked": "saga<PERSON>a", "interplanetary": "interplanetary", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "foxhole", "stray": "ligaw", "battlefield": "<PERSON><PERSON>n", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "nakakagigil", "blackdesert": "itimnadisyerto", "tabletopsimulator": "tabletopsimulator", "partyhard": "partyhard", "hardspaceshipbreaker": "matigasnasasakyangpingkian", "hades": "hades", "gunsmith": "tagapagsanayngsanda<PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON>it<PERSON><PERSON><PERSON><PERSON>", "dinkum": "totoo", "predecessor": "naunang", "rainworld": "mund<PERSON><PERSON>n", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonis<PERSON>", "noita": "noita", "dawnofwar": "simulangdigma<PERSON>", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "soulworker", "datingsims": "datingsims", "yaga": "yaga", "cubeescape": "cubeescape", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "bagonglungsod", "citiesskylines": "kislapngsyudad", "defconheavy": "defconheavy", "kenopsia": "kenopsya", "virtualkenopsia": "virtualkenopsia", "snowrunner": "snowrunner", "libraryofruina": "libraryofruina", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "omegastrikers", "wayfinder": "tagapagtukoy", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "labanbitaan", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialbayan", "smileforme": "ngumitiakoparasayo", "catnight": "<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "tinnybunny", "cozygrove": "cozygrove", "doom": "katakutan", "callofduty": "tawagngtungkulin", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "sininnghangbansa", "pubg": "pubg", "callofdutyzombies": "tawagngmgazombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "malayongsigaw", "farcrygames": "mgalarongfarcry", "paladins": "paladins", "earthdefenseforce": "din<PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "sumamasaskwad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencybagyongbuhawi", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "bagoparasabago", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "dibisyon2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "lamignazombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "makabagongdigmaan", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "borderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "mundongmgasiyudadngdigmaan", "back4blood": "balik4dugo", "warframe": "digmaan<PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "hitman", "masseffect": "masseffect", "systemshock": "sistemas<PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "pumataynglantay2", "cavestory": "kwentongyungib", "doometernal": "doometernal", "centuryageofashes": "siyentipyudalnaabu", "farcry4": "farcry4", "gearsofwar": "mgagearngdigmaan", "mwo": "mwo", "division2": "dibisyon2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "henerasyonzero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "sausageman", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "angsakitngmulto", "warface": "muk<PERSON><PERSON>a", "crossfire": "sagitnangkrusan", "atomicheart": "atomikpuso", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "liberasyon", "battlegrounds": "<PERSON><PERSON>n", "frag": "frag", "tinytina": "tinatina", "gamepubg": "larongpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "mgaanakngkalayaanmetalgear", "juegosfps": "mgafpsnalaro", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "dugonggulo", "republiccommando": "republikakomando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "dirtbranch", "squad": "squad", "destiny1": "destiny1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "mundo_ng_tanks_blitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON>", "farlight": "malayuan", "farcry5": "malayongsigaw5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "mgahimalanitinytina", "halo2": "halo2", "payday2": "sweldo2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sabongcod", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "hinditotoongtorneo", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "mgakampeonnglindol", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON>", "remnant": "mgatira", "azurelane": "azurelane", "worldofwar": "mundongwars", "gunvolt": "gunvolt", "returnal": "balik<PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "lindol2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "pulangpata<PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "labanan3", "lostark": "nawawalangark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "da<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "kalawang", "conqueronline": "conqueronline", "dauntless": "walangtakot", "warships": "barko", "dayofdragons": "arawngmgadragon", "warthunder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flightrising": "lipadngpagangat", "recroom": "recroom", "legendsofruneterra": "mgaalamatngruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "mundongmgatangke", "crossout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "pangalawangbuhay", "aion": "aion", "toweroffantasy": "tawerongpantasiya", "netplay": "netlalaro", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "angpagbubuhol<PERSON>isaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>", "newworld": "bagongmundo", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirata101", "honorofkings": "karangalanngmgahari", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "mundongwarcraft", "warcraft": "dota", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "mgaabungpaglikha", "riotmmo": "riotmmo", "silkroad": "silikongdaan", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "propetadragons", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "mgaanghelsaonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "perpektongmundo", "riseonline": "tumaasonline", "corepunk": "corepunk", "adventurequestworlds": "pagsubokngpakikipa<PERSON>", "flyforfun": "lumipadngmasaya", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "parasakara<PERSON>lan", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smasherbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "kalsadangrage", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "walangmgahero", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "parabuhaynaドラゴン", "retrofightinggames": "mgalaruangpanglabannoon", "blasphemous": "mapaghimagsik", "rivalsofaether": "kalabanng<PERSON>in", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "mgalabanansaboo", "cyberbots": "cyberbots", "armoredwarriors": "mgamandirigma<PERSON>kot<PERSON>", "finalfight": "paghahabolfinal", "poweredgear": "poweredgear", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "labananglaro", "killerinstinct": "killerinstinct", "kingoffigthers": "haringmgamandirigma", "ghostrunner": "ghostrunner", "chivalry2": "kaliwanagan2", "demonssouls": "demonsouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightpanipunan", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "balitangsilksong", "silksong": "silksong", "undernight": "undernight", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutiontournament", "evomoment": "evomoment", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "kwentongberseria", "bloodborne": "dugongipinanganak", "horizon": "horizontagpuan", "pathofexile": "landasngpagkakatapon", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "dugoboo", "uncharted": "walangnakatagonglugar", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "mgakalarongplaystation", "ps1": "ps1", "oddworld": "oddworld", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "huwa<PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "filesngaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "diyosngdigma", "gris": "gris", "trove": "buhol", "detroitbecomehuman": "detroitmagingtao", "beatsaber": "beats<PERSON>r", "rimworld": "mundongrim", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "hanggangbukangliwayway", "touristtrophy": "trophyngtourist", "lspdfr": "lspdfr", "shadowofthecolossus": "aninongmgagiant", "crashteamracing": "crashteamracing", "fivepd": "limapd", "tekken7": "tekken7", "devilmaycry": "demonyopwedengumiiyak", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "hulingtagapangalaga", "soulblade": "kaluluwangsword", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "hulingtagapangalaga", "xboxone": "xboxone", "forza": "sigeboo", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "buhayngparty", "warharmmer40k": "warhammer40k", "fightnightchampion": "labananngchampion", "psychonauts": "psykonauts", "mhw": "mhw", "princeofpersia": "prinsipindangpersya", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON>n", "dontstarvetogether": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spel<PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "houseflipper", "americanmcgeesalice": "amerikangalicemcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "basurangtv", "skycotl": "kalangitnito", "erica": "erika", "ancestory": "ninuno", "cuphead": "cuphead", "littlemisfortune": "misisbadtrip", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultoftholanggo", "duckgame": "larongitik", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "okulto", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "sapatnasatisfying", "pluviophile": "pluviophile", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "tagapangalaga", "darkdome": "mad<PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "laban", "rockpaperscissors": "batobatopik", "trampoline": "trampolino", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "taya", "scavengerhunt": "pascavengerhunting", "yardgames": "<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "pumiling<PERSON><PERSON>", "trueorfalse": "toto<PERSON>bohang", "beerpong": "beerpong", "dicegoblin": "dicesigurist<PERSON>", "cosygames": "komportablenglaro", "datinggames": "mgalarosadate", "freegame": "librenglaro", "drinkinggames": "laronginom", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "laro", "simulationgames": "larongpagsasalo<PERSON>in", "wordgames": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "maglalarotayonglaro", "boredgames": "boredgames", "oyun": "oyun", "interactivegames": "mgainteractivegames", "amtgard": "amtgard", "staringcontests": "paligsahanngtingin", "spiele": "laro", "giochi": "laro", "geoguessr": "geoguessr", "iphonegames": "larosaiphone", "boogames": "boogames", "cranegame": "larong<PERSON><PERSON>", "hideandseek": "taguan", "hopscotch": "tatsulok", "arcadegames": "mgalarongarcade", "yakuzagames": "yakuzagames", "classicgame": "classicgame", "mindgames": "<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "h<PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "la<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xgames", "gamefi": "laroinvest", "jeuxdarcades": "larongarcade", "tabletopgames": "larongpambabae", "metroidvania": "metroidvania", "games90": "larong90", "idareyou": "sinasa<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "to<PERSON>ovspekeng", "playgames": "mag<PERSON><PERSON>", "gameonline": "larongonline", "onlinegames": "mgaonlinegames", "jogosonline": "larosangkang", "writtenroleplay": "naksulatangroleplay", "playaballgame": "magsalopanglaro", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "larongpagsasanay", "burgergames": "larongburger", "kidsgames": "larongbata", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "larongmgatanong", "gioco": "laro", "managementgame": "larongpama<PERSON>ala", "hiddenobjectgame": "larongnakatagongbagay", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1game", "citybuilder": "tagabukidngbayan", "drdriving": "drdriving", "juegosarcade": "arcadegames", "memorygames": "la<PERSON><PERSON><PERSON>", "vulkan": "buh<PERSON>i", "actiongames": "mgalarongaksyon", "blowgames": "blowgames", "pinballmachines": "mgapinballmachine", "oldgames": "lumaongames", "couchcoop": "couchcoop", "perguntados": "magaanangtanong", "gameo": "gameo", "lasergame": "<PERSON><PERSON><PERSON><PERSON>", "imessagegames": "larongimessage", "idlegames": "mgalarosawalanggawa", "fillintheblank": "kuh<PERSON><PERSON>o", "jeuxpc": "larongpc", "rétrogaming": "rétrogaming", "logicgames": "laronglohistika", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "mgalarongsikat", "exitgames": "exitgames", "5vs5": "5vs5", "rolgame": "rol<PERSON>aw", "dashiegames": "dashiegames", "gameandkill": "laroatpatay", "traditionalgames": "mgalaruangtradisyonal", "kniffel": "kniffel", "gamefps": "larongfps", "textbasedgames": "larongbataysateksto", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrospel": "retrospel", "thiefgame": "thiefgame", "lawngames": "larongbayan", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "sipasaklaw", "tischfußball": "tischfußball", "spieleabende": "spileabende", "jeuxforum": "jeuxforum", "casualgames": "pampalaglag", "fléchettes": "darts", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "thiefgameseries", "cranegames": "la<PERSON><PERSON>ne", "játék": "laro", "bordfodbold": "boordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "larongkotse", "onlineplay": "onlinegame", "mölkky": "molkky", "gamenights": "gabi<PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "<PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "larongpc", "socialdeductiongames": "mgalarongsocialdeduction", "dominos": "dominos", "domino": "domino", "isometricgames": "isometricgames", "goodoldgames": "mgalumaatmagandanglaro", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "larongvirtual", "romhack": "romhack", "f2pgamer": "f2pnyo", "free2play": "libre2lalaro", "fantasygame": "larongpantasya", "gryonline": "gryonline", "driftgame": "larongdrift", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesatgames", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "tabakatsorcery", "goodgamegiving": "magandanglabanmagbigay", "jugamos": "<PERSON><PERSON><PERSON>o", "lab8games": "lab8mgalaro", "labzerogames": "labzerogames", "grykomputerowe": "grykomputerowe", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "mgaminilarongbuwas", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "selflovegaming", "gamemodding": "paggawanggamemods", "crimegames": "larongkrimen", "dobbelspellen": "dobbelspellen", "spelletjes": "mgalaro", "spacenerf": "spacenerf", "charades": "charades", "singleplayer": "singleplayer", "coopgame": "larongcoop", "gamed": "labanna", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "maingame", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "kataan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "paman<PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON>", "monopolygame": "labanang<PERSON><PERSON><PERSON><PERSON>", "brettspiele": "<PERSON><PERSON><PERSON><PERSON>", "bordspellen": "boardgames", "boardgame": "larongpangkahoy", "sällskapspel": "larongpanlipunan", "planszowe": "planozowe", "risiko": "risiko", "permainanpapan": "larongtabla", "zombicide": "zombicide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "dugosalo", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "koneksyonapat", "heroquest": "heroquest", "giochidatavolo": "larongpansarilingmesa", "farkle": "farkle", "carrom": "karom", "tablegames": "larongtable", "dicegames": "la<PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "boojo<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "labanang<PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kosmikongpagtatagpo", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "tabletoproleplay", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "kapangyarihangnakakatakot", "switchboardgames": "larongbu<PERSON>", "infinitythegame": "walanghangganglaro", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "saga<PERSON>atsagwan", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "larongmesa", "planszówki": "larongplano", "rednecklife": "b<PERSON><PERSON><PERSON><PERSON>", "boardom": "boardom", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "juegosdesosyedad", "gameboard": "larawannglaro", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON>rong<PERSON>mpala<PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "horseopoly", "deckbuilding": "paggawangdeck", "mansionsofmadness": "mansyonngkaguluhan", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "mgaaninongbrimstone", "kingoftokyo": "ha<PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "bootáblajáték", "battleship": "battleship", "tickettoride": "ticketnaumride", "deskovehry": "deskovehry", "catán": "kataan", "subbuteo": "subbuteo", "jeuxdeplateau": "larongboard", "stolníhry": "larongmesa", "xiángqi": "<PERSON>ian<PERSON><PERSON>", "jeuxsociete": "laronglipunan", "gesellschaftsspiele": "larongpangkalahatan", "starwarslegion": "legionngmgabituin", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terarria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON>n", "arksurvivalevolved": "arksurvivalevolved", "dayz": "arawz", "identityv": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theisle": "theisle", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "walangkalawakan", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "tawa<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyattinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "kasarapan", "eco": "ekolohiya", "monkeyisland": "pulongunggoy", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "witchit", "pathologic": "patholo<PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "thelongdark", "ark": "bark", "grounded": "nak<PERSON>on", "stateofdecay2": "estadongpagkabulok2", "vrising": "vrisin", "madfather": "madama", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "walanghanggangbabalik", "pathoftitans": "daanngtitans", "frictionalgames": "mgalarongfriction", "hexen": "hexen", "theevilwithin": "masamanglabanan", "realrac": "tunaynaraket", "thebackrooms": "thebackrooms", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "blockkwento", "thequarry": "ang<PERSON><PERSON>", "tlou": "tlou", "dyinglight": "na<PERSON><PERSON>yngliwanag", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "pag<PERSON><PERSON>ngmgaimperyo", "stateofsurvivalgame": "katayuanngsurvivalgame", "vintagestory": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotramang", "breathedge": "nag<PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "pagsurvayngwestlend", "beastsofbermuda": "mgahayopngbermuda", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "survivalhor<PERSON>r", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "baktay", "lifeaftergame": "buhaypagkataposnglaro", "survivalgames": "mgalarongpagsurvive", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "proyektoberde", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "pataypoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "lola", "littlenightmares2": "littlenightmares2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "mgaanaknggubat", "rustvideogame": "rustgame", "outlasttrials": "lampasanangpagsubok", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "undawn", "7day2die": "7araw2mamatay", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "survival", "propnight": "propnight", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "deathverse", "cataclysmdarkdays": "cataclysmdarkdays", "soma": "soma", "fearandhunger": "takotatsalamat", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "buhaypagkatapo<PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "orasanatower3", "aloneinthedark": "nagi<PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "medievaldynasty", "projectnimbusgame": "projectnimbusgame", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "pagsakopsamundo", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "opisyalnagasund<PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "crushngwarhammer40k", "wh40": "wh40", "warhammer40klove": "siyentipikongpagmamahal40k", "warhammer40klore": "warhammer40kbola", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "mahalkoangsororitas", "ilovevindicare": "mahalinangvindicare", "iloveassasinorum": "ma<PERSON>nan<PERSON>", "templovenenum": "temployvenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "ofisyalesinfelino", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "panahonngmgaimperyo", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerpanahonngsigmar", "civilizationv": "sibil<PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "kailangangdalawa", "wingspan": "wingspan", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "mg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "panahonngmgaalamat", "args": "argggg", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "pinalayas", "caesar3": "caesar3", "redalert": "redalert", "civilization6": "sibilisasyon6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "walanghangganglaban", "strategygames": "mgalarongstrategiya", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "sibilisasyon4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "sagu<PERSON><PERSON>", "travian": "travian", "forts": "forts", "goodcompany": "magandangkompanya", "civ": "civ", "homeworld": "bah<PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "masmabiliskaysaalam", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "taktikangtotoo", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kaharianngdalawangkorona", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "ano", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "klasengpagsasayangalgebranidave", "plagueinc": "plagueinc", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "sibilisasyon3", "4inarow": "4natalo", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "advancewars", "ageofempires2": "panahonngmgaempire2", "disciples2": "mgaalagad2", "plantsvszombies": "plantsvszombies", "giochidistrategia": "strategygames", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "panah<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosaurking", "worldconquest": "pagka<PERSON>basagngmund<PERSON>", "heartsofiron4": "pusongbakal4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "pandayngmgaimperyo", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "bibeinabibe", "phobies": "phobies", "phobiesgame": "phobiesgame", "gamingclashroyale": "labanang<PERSON>lash<PERSON>ale", "adeptusmechanicus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outerplane": "panlabasnaeroplano", "turnbased": "pai<PERSON>tinanglabas", "bomberman": "bomberman", "ageofempires4": "panahonngmgaempire4", "civilization5": "sibilisasyon5", "victoria2": "victoria2", "crusaderkings": "mgaharingkrus", "cultris2": "kultris2", "spellcraft": "siningngspelling", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrate<PERSON>ya", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transporttycoon", "unrailed": "hind<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planosapaghalukayngtormento", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "galaxylife", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "laban<PERSON>a", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "kailanganng<PERSON><PERSON>", "needforspeedcarbon": "kailanganngbiliscarbon", "realracing3": "totoongkarera3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "naisin4", "fnaf": "fnaf", "outlast": "masp<PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "bumalik<PERSON>licesaka<PERSON>", "darkhorseanthology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "limanggabikayfreddy", "saiko": "sako", "fatalframe": "siyentipikongbalangkas", "littlenightmares": "mgamaliitnabangungot", "deadrising": "mgapataynaumaakyat", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "littlemissswerte", "projectzero": "projectzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "kamustakapitbahay", "helloneighbor2": "kamustakapitbahay2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "larongpangkatakot", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "mgacardkon<PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "mgakodigo", "dixit": "dixit", "bicyclecards": "bicyclecards", "lor": "lor", "euchre": "yuker", "thegwent": "thegwent", "legendofrunetera": "alama<PERSON><PERSON><PERSON><PERSON>a", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "mgatricksacard", "playingcards": "mgabar<PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "tradingcards", "pokemoncards": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "mgakardsasport", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "spades", "warcry": "sigawnglaban", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "ha<PERSON><PERSON>o", "truco": "truko", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "angpagsuway", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohcards", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "mad<PERSON><PERSON><PERSON><PERSON>gg<PERSON><PERSON>", "blueeyeswhitedragon": "<PERSON><PERSON>nagasawangdragon", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "kotse", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "kuwentongbattlespirits", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "boožolíky", "facecard": "facecard", "cardfight": "labanang<PERSON>", "biriba": "biriba", "deckbuilders": "mgatagabuongbangkay", "marvelchampions": "marvelchampions", "magiccartas": "magiccartas", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "hindistablenaunikornyo", "cyberse": "cyberse", "classicarcadegames": "mgaclassicarcadenalaro", "osu": "osu", "gitadora": "gitadora", "dancegames": "mgalarosasayaw", "fridaynightfunkin": "boof<PERSON><PERSON><PERSON><PERSON><PERSON>in", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proyektomirai", "projectdiva": "proyektongdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockthedead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "pagsayawcentral", "rhythmgamer": "ritmolaro", "stepmania": "stepmania", "highscorerythmgames": "highscorerythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "lang<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "auditiononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "larongtimpla", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ritmo<PERSON>", "cubing": "cubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "larongpuzzle", "spotit": "t<PERSON><PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubikscube", "crossword": "<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "krzyżówki", "nonogram": "nonogram", "bookworm": "buhaynabukbok", "jigsawpuzzles": "jigsawpuzzles", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "bugtong", "riddles": "mgabugtong", "rompecabezas": "suwatpuzzle", "tekateki": "tekateki", "inside": "saloob", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "pagtakasimulator", "minesweeper": "sweeperngmina", "puzzleanddragons": "puzzleatdragons", "crosswordpuzzles": "pag<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "labansapuzzle", "escaperoomgames": "larongescaperoom", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgame", "wordsearch": "salit<PERSON><PERSON><PERSON>", "enigmistica": "enigmista", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "kwentongmgapahulaan", "fishdom": "p<PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "theimpossiblequiz", "candycrush": "candycrush", "littlebigplanet": "maliitmalakingplaneta", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "rubikcube", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "sagu<PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "cubosderubik", "cruciverba": "kruskalawang", "ciphers": "mgasi<PERSON>s", "rätselwörter": "su<PERSON><PERSON><PERSON>ta", "buscaminas": "minesweeper", "puzzlesolving": "pagsolusyonngbugtong", "turnipboy": "turnipboy", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "walangkanto", "guessing": "hulaan", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "malasarilingcrossword", "syberia2": "syberia2", "puzzlehunt": "paghahanapngbugtong", "puzzlehunts": "paghahanapngpuzzle", "catcrime": "pusakrimen", "quebracabeça": "puzzle", "hlavolamy": "hlavolamy", "poptropica": "poptropica", "thelastcampfire": "hulin<PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "selfdefined", "picopark": "picopark", "wandersong": "naglalakbaynaawit", "carto": "carto", "untitledgoosegame": "laronggansangwala<PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON>", "limbo": "limbo", "rubiks": "rubiks", "maze": "labiri<PERSON><PERSON>", "tinykin": "mgamaliitnakaibigan", "rubikovakostka": "rubikovakostka", "speedcube": "bilisanngcube", "pieces": "piraso", "portalgame": "portalgame", "bilmece": "bilang<PERSON><PERSON>", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopolyo", "futurefight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "lonesibig", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemiststars", "stateofsurvival": "estadongpagsurvive", "mycity": "akinglungsod", "arknights": "arknights", "colorfulstage": "makulaynascene", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanglaban", "fategrandorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hyperfront": "hyperfront", "knightrun": "knightrun", "fireemblemheroes": "mgabayaningfireemblem", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "pilinghari", "guardiantales": "guardianngmgakuwento", "petrolhead": "petrolhead", "tacticool": "taktikalnacool", "cookierun": "cookiepagsak", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "walaakongalam", "craftsman": "<PERSON>gg<PERSON><PERSON>", "supersus": "supersus", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "paalala", "wordfeud": "salitanglaban", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "librenaap<PERSON>", "mobilegaming": "mobilegaming", "lilysgarden": "hardinnilily", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "labanangmgaklan", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON>and<PERSON>dget", "ml": "ml", "bangdream": "bangdream", "clashofclan": "labananngmgaklan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "prinses<PERSON><PERSON>s", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "paputok", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "pocketlove", "androidgames": "la<PERSON><PERSON><PERSON>", "criminalcase": "kasongkriminal", "summonerswar": "summonerswar", "cookingmadness": "pag<PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "liganapanghel", "lordsmobile": "lordsmobile", "tinybirdgarden": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mysingingmonsters": "mgamonstrongkumanta", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "sa<PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "digmaangmgapaa", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "pinaru<PERSON>ang<PERSON><PERSON><PERSON>", "petpals": "mgapaboritongbahay", "gameofsultans": "larongsultans", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "larongpagsasprint", "juegodemovil": "laros<PERSON>bile", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendsonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "tawa<PERSON><PERSON><PERSON>", "shiningnikki": "nagni<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtogroundzero", "sealm": "sealm", "shadowfight3": "labanangan3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "mgasalitangkasamaangmgakaibigan2", "soulknight": "kaluluwangpampangangao", "purrfecttale": "perpektongkwento", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobile", "harvesttown": "harvestbayan", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "imperyoatbugtong", "empirespuzzles": "empirespuzzles", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "<PERSON>yan", "littlenightmare": "mabaitnakakatakot", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "luh<PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON>", "eveechoes": "buh<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbababang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "bgmi<PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "frontlinegirls", "jurassicworldalive": "jurassicworldalive", "soulseeker": "tagahanapngkaluluwa", "gettingoverit": "tinatang<PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "k<PERSON>wentongbuwan", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "laroingmobile", "legendofneverland": "al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "laromobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "mgapusasalaban", "dnd": "dnd", "quest": "mgahamon", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgharapangmesa", "worldofdarkness": "mund<PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "romanceclub", "d20": "d20", "pokemongames": "mgalarongpokémon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokémonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipip", "porygon": "porygon", "pokemonunite": "pokémonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "ferret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "mgapocketmonster", "nuzlocke": "nuzlocke", "pokemonplush": "mgapokemonplush", "teamystic": "teamystik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "ironhands", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "poke<PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "mgabataatpokémon", "pokemonsnap": "pokesnap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "shinyhunter", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "saka", "schaken": "shaken", "skak": "skak", "ajedres": "chess", "chessgirls": "chessgirls", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "worldblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "haponna<PERSON>", "chinesechess": "chineseschess", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "bukas", "rook": "rookie", "chesscom": "chesscom", "calabozosydragones": "calabozosatdragones", "dungeonsanddragon": "dungeonsatdragons", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "darksun", "thelegendofvoxmachina": "alamatngvoxmachina", "doungenoanddragons": "dungenoanddragons", "darkmoor": "<PERSON><PERSON><PERSON>naimpyer<PERSON>", "minecraftchampionship": "minecraftchampionship", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "sapa<PERSON>anng<PERSON><PERSON>upa", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftbayan", "pcgamer": "pcgamer", "jeuxvideo": "larongbideo", "gambit": "gambit", "gamers": "mgagamers", "levelup": "levelup", "gamermobile": "gamermobile", "gameover": "<PERSON><PERSON>na", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "mgalarosapc", "casualgaming": "kulitanglaro", "gamingsetup": "pagsesetupnggaming", "pcmasterrace": "pcmanggagawasapc", "pcgame": "pcgame", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kboogaming", "gamerbr": "gamerbr", "gameplays": "laro", "consoleplayer": "mgamanlalarongconsole", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "onlinegaming", "semigamer": "semigamer", "gamergirls": "gamergirls", "gamermoms": "mgananaygamer", "gamerguy": "<PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON>", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerbabae", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "team<PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "mgapagsubok", "alax": "alax", "avgn": "avgn", "oldgamer": "lumagamer", "cozygaming": "komportablenggaming", "gamelpay": "gamelpay", "juegosdepc": "gamesnapc", "dsswitch": "dsswitch", "competitivegaming": "mapakabibo", "minecraftnewjersey": "minecraftnewjersey", "faker": "panggoyo", "pc4gamers": "pc4mgagamer", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "higestraightgaming", "gamepc": "gamepc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "gamegurl", "chicasgamer": "chicasgamer", "gamesetup": "setupofthegame", "overpowered": "overpowered", "socialgamer": "sosyalgamer", "gamejam": "labananglaro", "proplayer": "proplayer", "roleplayer": "roleplayer", "myteam": "teamko", "republicofgamers": "republicangmgagamer", "aorus": "aorus", "cougargaming": "cugargaming", "triplelegend": "triplelegend", "gamerbuddies": "mgakalaro", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "kristyanonglaro", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squadsaboo", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "magwatch<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "pangalannglaro", "lanparty": "lanparty", "videogamer": "mgatagahanganglaro", "wspólnegranie": "samasamanglar<PERSON>", "mortdog": "mortdog", "playstationgamer": "manlalarongplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "b<PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "syempregamerak<PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forager", "humanfallflat": "humanfallflat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonik", "fallguys": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "pags<PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wiwi", "aceattorney": "tagapagtanggolngbatas", "ssbm": "ssbm", "skychildrenofthelight": "mgabatanglangitngliwanag", "tomodachilife": "buhaykaibigan", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "mgatagalakbay", "nintendogames": "laronintendogs", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "kaibigankosipedro", "legendsofzelda": "mgaalamatngzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51lar<PERSON>", "earthbound": "nasalupa", "tales": "kwento", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "estratehiyatriangulo", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "konkersmasamangaraw", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioatsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "angzel<PERSON>", "palia": "palia", "marioandluigi": "marioatlugi", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "yu<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "pulangcanids", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligangmgalegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "eww", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadassamgaalamat", "gaminglol": "lalaropala", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "larongfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrogames", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>ag<PERSON>aro", "videogamemaker": "gumagawanglaro", "megamanzero": "megamanzero", "videogame": "larongvideojo", "videosgame": "mgalarosaboo", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "mgaarcade", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulatorngpagsasaka", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "rob<PERSON>x<PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sandboxgames", "videogamelore": "lorengvideoga<PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "pangarapna", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "pataynaspace", "amordoce": "amordoce", "videogiochi": "videogame", "theoldrepublic": "angmatandangrepublika", "videospiele": "larongvideo", "touhouproject": "touhouproject", "dreamcast": "panaginipcast", "adventuregames": "mgalarosaaksyon", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "kwentongmgabuwan", "retrogames": "mgalumalanglaro", "retroarcade": "retroarcade", "vintagecomputing": "vintagenakompyuter", "retrogaming": "retrogaming", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "petsanglaro", "commanderkeen": "komanderkeen", "bugsnax": "bugsnax", "injustice2": "booinjustice2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "larawanglangit", "zenlife": "buh<PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "matarik", "mystgames": "mystgames", "blockchaingaming": "larongblockchain", "medievil": "medievil", "consolegaming": "consolegaming", "konsolen": "konsolen", "outrun": "takas", "bloomingpanic": "namumulaklak<PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horrorsagaming", "monstergirlquest": "monstergirlquest", "supergiant": "supergiant", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "<PERSON><PERSON><PERSON>", "juegosviejos": "lumanglaro", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktibongkwento", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "visualnovel", "visualnovels": "mgavisualnovel", "rgg": "rgg", "shadowolf": "aninolobo", "tcrghost": "tcrghost", "payday": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "katherine", "twilightprincess": "prinsesangtwilight", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "aestheticgames", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "disenyongantas", "starrail": "starrail", "keyblade": "mgasusi", "aplaguetale": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "visualnovels", "robloxbrasil": "robloxpilipinas", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videojuegs", "videogamedates": "videogamedates", "mycandylove": "mylovecandies", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "arawngtantacle", "maniacmansion": "maniacmansion", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "hellblade", "storygames": "larongkuwento", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "beyondtwosouls", "gameuse": "gameuse", "offmortisghost": "offmortisghost", "tinybunny": "maliitnabunny", "retroarch": "retroarch", "powerup": "<PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "pamilya", "aventurasgraficas": "aventurasgrafika", "quickflash": "mabilis<PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxp<PERSON><PERSON>", "anotherworld": "ibangmundo", "metaquest": "mgakatanungansameta", "animewarrios2": "animewarrios2", "footballfusion": "pagsasamangfootball", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "twistedmetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tumpoknghiya", "simulator": "simulator", "symulatory": "simulasyon", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobotbattle", "dcuo": "dcuo", "samandmax": "samanatmax", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "larongkorku", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "boyfrienddungeon", "toontownrewritten": "toontownnai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "la<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "sa<PERSON><PERSON>pag<PERSON><PERSON>", "graveyardkeeper": "tagapagalaganglibingan", "spaceflightsimulator": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "saksakats<PERSON><PERSON>", "foodandvideogames": "pag<PERSON><PERSON>videoga<PERSON>", "oyunvideoları": "oyunvideo", "thewolfamongus": "anglobosatayo", "truckingsimulator": "truckingsimulator", "horizonworlds": "horizonworlds", "handygame": "handygame", "leyendasyvideojuegos": "kwentongmgalaro", "oldschoolvideogames": "lumanglarongvideo", "racingsimulator": "simu<PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bimove", "agentsofmayhem": "mgaahentengkaguluhan", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "rebelstar", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "indievideogames", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermansaboo", "bufffortress": "bufffortress", "unbeatable": "walangkapantay", "projectl": "proyektol", "futureclubgames": "mgala<PERSON><PERSON>naharap", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "mganakaipon", "gamebacklog": "larong<PERSON><PERSON>n", "gamingbacklog": "listahannglaro", "personnagejeuxvidéos": "karakternglaroaingvideo", "achievementhunter": "manghuhulingtagumpay", "cityskylines": "<PERSON><PERSON>inng<PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "naughtyaso", "beastlord": "beastlord", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriatangnagmamalimpinggubat", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatonodopamin", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "unangd", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animenamaykwento", "pesci": "isda", "retroanime": "retroanime", "animes": "animes", "supersentai": "superhero", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "angbisyonngescaflowne", "slayers": "pama<PERSON>y", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "saging<PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "fireforce", "moriartythepatriot": "moriartyangpatri<PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "fairytail", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekingmgatitans", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "mgaromansamanga", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonmaid", "blacklagoon": "itimnalagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terrafarmars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yuriontara", "acertainmagicalindex": "isangtiyaknamahirapnaindex", "sao": "sao", "blackclover": "itimnatrifo", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8angwalanghanggan", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriyoridad", "angelsofdeath": "mgaangeldes死亡", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanong", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "torreng<PERSON><PERSON>s", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "paanogawingmummy", "fullmoonwosagashite": "buo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "cuteatnakakatakot", "martialpeak": "dahilsamartialpeak", "bakihanma": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoregirlfriend", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "mgasailorsasaturn", "dio": "diyos", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "lumanganime", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "basketngprutas", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "mahalnatinangbuhay", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "malalimnagapreso", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "labanangpagkain", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blueperiod", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "lih<PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "nawala", "bluelock": "bluelock", "goblinslayer": "pumapatayngmgagoblins", "detectiveconan": "detektibconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "boobampiraknights", "mugi": "mugi", "blueexorcist": "asulnae<PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "airgear", "magicalgirl": "b<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "itongpitongnakamamata<PERSON>an", "prisonschool": "prisonschool", "thegodofhighschool": "diyosnghighschool", "kissxsis": "ha<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "akingpangarapngbihis", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "sangay<PERSON>nime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "unanghak<PERSON>", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "romansangmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromansa", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "papa<PERSON>yng<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "dugoboi", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "sunogpunch", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "nakatugmaangmgabituin", "romanceanime": "roman<PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "bansangbahay", "recordragnarok": "irecor<PERSON><PERSON><PERSON>", "oyasumipunpun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prinsi<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "klasengmgaasesino", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paradang<PERSON><PERSON><PERSON>", "shokugekinosouma": "shokugekinoboo", "japaneseanime": "haponanimes", "animespace": "animespace", "girlsundpanzer": "mgababaesapanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "dubnganime", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "dagangbabae", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "peachgirl", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "pu<PERSON>gman<PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "talangragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "sobraanggear", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierngbuwitch", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaislife", "dropsofgod": "dampsngdiyos", "loscaballerosdelzodia": "mgaginoongzodiak", "animeshojo": "animeshojo", "reverseharem": "baliktang<PERSON><PERSON>", "saintsaeya": "sainstay", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldier", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "grand<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "<PERSON><PERSON><PERSON>", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "dugoanime", "bloodc": "dugo", "talesofdemonsandgods": "kwentongdemonyoatangod", "goreanime": "goreanime", "animegirls": "mgaanimegirls", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxpinakamasama", "splatteranime": "splashanimes", "splatter": "buhos", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "napagsaluhanngslime", "animeyuri": "animeyuri", "animeespaña": "animeespanya", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusuwerte", "childrenofthewhales": "mgabatangmgabalparano", "liarliar": "sinungalingosinungaling", "supercampeones": "supercampeones", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwassmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "mgabatangmahika", "callofthenight": "tawagnggabi", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "ha<PERSON><PERSON>man<PERSON>", "princessjellyfish": "prinsesangjellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "halikngparaiso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverso", "persocoms": "persocoms", "omniscientreadersview": "omnisiyentengmambabasa", "animecat": "animepusa", "animerecommendations": "rekomendasyonnganime", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mgaromcombatako", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "malal<PERSON><PERSON>bot", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojossaktonapakikipagsapalaran", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "yelo", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "military<PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animebayan", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bukunohéroakademiya", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "atakeontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "larongkaibigan", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "korpongsondahán", "onepieceanime": "onepieceanime", "attaquedestitans": "sugodsatitans", "theonepieceisreal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "mgatagapagbigayhustisya", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffect", "digimonstory": "kwentongdigimon", "digimontamers": "digimontamers", "superjail": "superjail", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "u<PERSON><PERSON><PERSON><PERSON><PERSON>", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "walangkapintasanwebtoon", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprin<PERSON><PERSON>", "animecom": "animesaya", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "lumi<PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "lahatngsantostreet", "recuentosdelavida": "kwentongbuhay"}