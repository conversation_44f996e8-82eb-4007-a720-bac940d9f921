{"2048": "2048", "mbti": "mbti", "enneagram": "אניא<PERSON>רם", "astrology": "אסטרולוגיה", "cognitivefunctions": "תפקוד<PERSON><PERSON><PERSON>ניטיבי", "psychology": "פסיכולוגיה", "philosophy": "פילוסופיה", "history": "היסטוריה", "physics": "פיזיקה", "science": "מדע", "culture": "תרבות", "languages": "שפות", "technology": "טכנולוגיה", "memes": "ממים", "mbtimemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "ממיםאסטרולוגיה", "enneagrammemes": "ממים<PERSON><PERSON>י<PERSON><PERSON><PERSON>ם", "showerthoughts": "מחשבותמקלחת", "funny": "מצ<PERSON><PERSON><PERSON>", "videos": "סרטונים", "gadgets": "גאדגטים", "politics": "פוליטיקה", "relationshipadvice": "עצותמערכותיחסים", "lifeadvice": "עצותלחיים", "crypto": "קריפטו", "news": "חדשות", "worldnews": "חדשותעולמיות", "archaeology": "ארכיאולוגיה", "learning": "למידה", "debates": "די<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "תיאוריות<PERSON>ונספירציה", "universe": "עולם", "meditation": "מדיטציה", "mythology": "מיתולוגיה", "art": "אומנות", "crafts": "יצירה", "dance": "<PERSON><PERSON><PERSON><PERSON>", "design": "עיצוב", "makeup": "אי<PERSON>ור", "beauty": "יו<PERSON>י", "fashion": "אופנה", "singing": "שירה", "writing": "כתיבה", "photography": "צילום", "cosplay": "קוספליי", "painting": "ציור", "drawing": "ציור", "books": "ספרים", "movies": "סרטים", "poetry": "שירה", "television": "טלוויזיה", "filmmaking": "יצירתסרטים", "animation": "אנימציה", "anime": "אנימה", "scifi": "מדעבדיוני", "fantasy": "פנטזיה", "documentaries": "דוקו<PERSON>נטריה", "mystery": "מסתורין", "comedy": "קומדיה", "crime": "פשע", "drama": "דר<PERSON>ה", "bollywood": "בוליווד", "kdrama": "דרמותקוריאניות", "horror": "אימה", "romance": "רומנטיקה", "realitytv": "ריאליטי", "action": "פעולה", "music": "מוסיקה", "blues": "<PERSON><PERSON><PERSON><PERSON>", "classical": "קלא<PERSON>י", "country": "קאנט<PERSON>י", "desi": "<PERSON><PERSON><PERSON>", "edm": "edm", "electronic": "אלק<PERSON>רוני", "folk": "פולק", "funk": "פ<PERSON><PERSON><PERSON>", "hiphop": "היפהופ", "house": "האוס", "indie": "אינדי", "jazz": "גאז", "kpop": "קייפופ", "latin": "לטינית", "metal": "מטאל", "pop": "פופ", "punk": "פ<PERSON><PERSON><PERSON>", "rnb": "rnb", "rap": "ראפ", "reggae": "רגאיי", "rock": "רוק", "techno": "טכנו", "travel": "טיולים", "concerts": "קונצרטים", "festivals": "פסטיבלים", "museums": "מוזיאונים", "standup": "סטנדאפ", "theater": "תיאטרון", "outdoors": "האוירהפתוח", "gardening": "גינון", "partying": "לצאתלמסיבות", "gaming": "גיי<PERSON><PERSON>נג", "boardgames": "משחקילוח", "dungeonsanddragons": "מבוכיםו<PERSON><PERSON><PERSON>ונים", "chess": "שח<PERSON><PERSON>", "fortnite": "פורטנייט", "leagueoflegends": "leagueoflegends", "starcraft": "סטאר<PERSON><PERSON><PERSON><PERSON>ט", "minecraft": "מיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemon": "פו<PERSON><PERSON><PERSON>ון", "food": "אוכל", "baking": "אפיה", "cooking": "בישול", "vegetarian": "צמחוני", "vegan": "טבעוני", "birds": "ציפורים", "cats": "חתולים", "dogs": "כלבים", "fish": "דגים", "animals": "חיות", "blacklivesmatter": "חיישחורים<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "environmentalism": "דאג<PERSON><PERSON><PERSON><PERSON><PERSON>ותהסביבה", "feminism": "פמיניזם", "humanrights": "זכויותאדם", "lgbtqally": "בעלברית<PERSON><PERSON><PERSON>ב", "stopasianhate": "לעצורשנאהלאסייתים", "transally": "בעלבריתטרנס", "volunteering": "התנדבות", "sports": "ספורט", "badminton": "בד<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "baseball": "בייסבול", "basketball": "כד<PERSON>ר<PERSON>ל", "boxing": "איגרוף", "cricket": "קריק<PERSON>", "cycling": "רכיבהעלאופניים", "fitness": "<PERSON><PERSON><PERSON><PERSON>", "football": "כדורגל", "golf": "גוֹלף", "gym": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gymnastics": "התעמלות", "hockey": "<PERSON>ו<PERSON>י", "martialarts": "אומנויותלחימה", "netball": "כדורשת", "pilates": "פילאטיס", "pingpong": "פינגפונג", "running": "ריצה", "skateboarding": "סקייט<PERSON>ורד", "skiing": "<PERSON>קי", "snowboarding": "גלישתשלג", "surfing": "גלישה", "swimming": "שחיה", "tennis": "טניס", "volleyball": "כדורעף", "weightlifting": "הרמתמשקולות", "yoga": "יוגה", "scubadiving": "צלילה", "hiking": "טיוליהלי<PERSON>ה", "capricorn": "מזלגדי", "aquarius": "מזלדלי", "pisces": "מזלדגים", "aries": "מזלטלה", "taurus": "מזלשור", "gemini": "מזלתאומים", "cancer": "מזלסרטן", "leo": "מזלאריה", "virgo": "מזלבתולה", "libra": "מזלמאזניים", "scorpio": "מזל<PERSON><PERSON><PERSON>ב", "sagittarius": "מזלקשת", "shortterm": "קצר", "casual": "קליל", "longtermrelationship": "קשררציני", "single": "רווק", "polyamory": "פוליאמוריה", "enm": "פוליאמוריה", "lgbt": "לגבת", "lgbtq": "לגבטק", "gay": "הומו", "lesbian": "לסבית", "bisexual": "ביס<PERSON>סואל", "pansexual": "פאנסקסואל", "asexual": "א<PERSON><PERSON><PERSON>י", "reddeadredemption2": "רדדeadרימשן2", "dragonage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "assassinscreed": "אסאסינסקריד", "saintsrow": "סיינטסרו", "danganronpa": "דאנ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ה", "deltarune": "דל<PERSON><PERSON><PERSON>ן", "watchdogs": "שוערי_התערבות", "dislyte": "דיס<PERSON><PERSON><PERSON>ט", "rougelikes": "רוגל<PERSON><PERSON><PERSON><PERSON>ס", "kingsquest": "מלךהחיפוש", "soulreaver": "גוזלנשמה", "suikoden": "סויקודן", "subverse": "סאבורס", "legendofspyro": "אגדה<PERSON>לספיירו", "rouguelikes": "רוגלייקס", "syberia": "סיבריה", "rdr2": "רדר2", "spyrothedragon": "ספ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "dragonsdogma": "דרקוניםודו<PERSON><PERSON>ה", "sunsetoverdrive": "ש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "ארק<PERSON><PERSON>ם", "deusex": "אלוהימי", "fireemblemfates": "פורטלפנים", "yokaiwatch": "יו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocksteady": "רוקסטדי", "litrpg": "ליטר<PERSON>ג", "haloinfinite": "הלווינ<PERSON>יניט", "guildwars": "מלחמותגילדות", "openworld": "עולםפתוח", "heroesofthestorm": "גיבוריהסערה", "cytus": "סיתוס", "soulslike": "סולסלייק", "dungeoncrawling": "דא<PERSON><PERSON><PERSON><PERSON><PERSON>רואלים", "jetsetradio": "גטסטרדיו", "tribesofmidgard": "שבטים<PERSON>ל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד", "planescape": "תוכניתבריחה", "lordsoftherealm2": "לורדיםשלהממלכה2", "baldursgate": "בולדו<PERSON><PERSON><PERSON>ייט", "colorvore": "צבעקוור", "medabots": "מדאבוטס", "lodsoftherealm2": "עולמדלוטחיים2", "patfofexile": "פטפההגלות", "immersivesims": "משחקיםמ<PERSON><PERSON><PERSON>ים", "okage": "אוק<PERSON>י", "juegoderol": "גיי<PERSON><PERSON>נג", "witcher": "מכשף", "dishonored": "מופלא", "eldenring": "אֶלְדֶןר<PERSON>ינְג", "darksouls": "דר<PERSON><PERSON><PERSON>לס", "kotor": "קטור", "wynncraft": "ווין<PERSON><PERSON><PERSON><PERSON>ט", "witcher3": "וויצר3", "fallout": "פולאווט", "fallout3": "פולאווט3", "fallout4": "פולאוט4", "skyrim": "סקיירים", "elderscrolls": "גלגלי_הזמן", "modding": "מודינג", "charactercreation": "יצירתדמויות", "immersive": "סוחף", "falloutnewvegas": "פולאוטניווג<PERSON>ס", "bioshock": "ביוש<PERSON>ק", "omori": "אומו<PERSON>י", "finalfantasyoldschool": "פיינל<PERSON>נט<PERSON><PERSON><PERSON>שן<PERSON><PERSON>שן", "ffvii": "ffvii", "ff6": "פפ6", "finalfantasy": "פנטזיהסופית", "finalfantasy14": "פיינלפנטסי14", "finalfantasyxiv": "פיינלפנטזי14", "ff14": "ff14", "ffxiv": "פיינלפנטזי14", "ff13": "פיי_פיי_13", "finalfantasymatoya": "פיינל<PERSON>נטזימטויה", "lalafell": "ללפל", "dissidia": "דיסידיה", "finalfantasy7": "פfinalfantasy7", "ff7": "ff7", "morbidmotivation": "מוטיבציהמורבידית", "finalfantasyvii": "פיינלפנטזי7", "ff8": "ףף8", "otome": "אוטומה", "suckerforlove": "בורסהלאהבה", "otomegames": "משחקיוטומה", "stardew": "סטארדיו", "stardewvalley": "סטארדיוו<PERSON><PERSON>י", "ocarinaoftime": "אוקר<PERSON>נ<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "yiikrpg": "יאיייקרpg", "vampirethemasquerade": "ערפדים<PERSON><PERSON><PERSON><PERSON>ה", "dimension20": "דימנ<PERSON><PERSON>20", "gaslands": "גזלאנדס", "pathfinder": "מפלסדרכים", "pathfinder2ndedition": "פוֹנְדְרָשְׁנִי_בָּרוּת_שֵׁנִי", "shadowrun": "צֵלרַיצה", "bloodontheclocktower": "דם<PERSON><PERSON><PERSON><PERSON><PERSON>ון", "finalfantasy15": "פיינלפנטזי15", "finalfantasy11": "פיינל<PERSON>נטזי11", "finalfantasy8": "פיינלפנטזי8", "ffxvi": "ffxvi", "lovenikki": "לובניקי", "drakengard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gravityrush": "הזרם<PERSON><PERSON><PERSON>ד", "rpg": "רפג", "dota2": "דוטה2", "xenoblade": "זנובלייד", "oneshot": "אונשוט", "rpgmaker": "ר<PERSON><PERSON><PERSON><PERSON><PERSON>", "osrs": "או<PERSON><PERSON>ס", "overlord": "אדון", "yourturntodie": "התורשלךלמות", "persona3": "פרסונה3", "rpghorror": "האימהשלרפג", "elderscrollsonline": "אול<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ו<PERSON><PERSON><PERSON>ין", "reka": "רקה", "honkai": "<PERSON>נק<PERSON>י", "marauders": "מראודרים", "shinmegamitensei": "שין<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "epicseven": "אפיק7", "rpgtext": "rpgטקסט", "genshin": "גנ<PERSON><PERSON>ן", "eso": "אֶזוֹ", "diablo2": "דיאבלו2", "diablo2lod": "ד<PERSON><PERSON><PERSON>2<PERSON><PERSON>", "morrowind": "מורוויןד", "starwarskotor": "מלחמת<PERSON><PERSON><PERSON><PERSON><PERSON>יםקוטר", "demonsouls": "נשמותדמונים", "mu": "מו", "falloutshelter": "ביתןנ<PERSON>ילה", "gurps": "גאר<PERSON>ס", "darkestdungeon": "הצינוקהחשוך", "eclipsephase": "שלב_בדרך_האפלה", "disgaea": "דיסגי<PERSON>ה", "outerworlds": "עולמותהחוץ", "arpg": "ארפג", "crpg": "רפג", "bindingofisaac": "המאבקשלאייזק", "diabloimmortal": "דיוב<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "dynastywarriors": "מלחמותהדינסטיות", "skullgirls": "סקולג<PERSON>רלס", "nightcity": "עירהלילה", "hogwartslegacy": "המורשתשלהוגוורטס", "madnesscombat": "קרביטרוף", "jaggedalliance2": "גאגדאליאנס2", "neverwinter": "נאשכח", "road96": "דרך96", "vtmb": "וי<PERSON><PERSON><PERSON><PERSON>", "chimeraland": "ח<PERSON><PERSON><PERSON><PERSON><PERSON>ד", "homm3": "הוממ3", "fe3h": "פיה3ה", "roguelikes": "רוגלייקס", "gothamknights": "גוטהםנייטס", "forgottenrealms": "ממלכות<PERSON>כוחות", "dragonlance": "דר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "זירהשלערך", "ffxv": "ffxv", "ornarpg": "עורנרפג", "toontown": "טו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "childoflight": "ילדשלאור", "aq3d": "אקו3ד", "mogeko": "מוגקו", "thedivision2": "הדיביזיה2", "lineage2": "ליינייג2", "digimonworld": "עולםדיג<PERSON>מון", "monsterrancher": "מסתדריםעםמפלצות", "ecopunk": "אק<PERSON><PERSON><PERSON><PERSON><PERSON>", "vermintide2": "בריכתעכברית2", "xeno": "זנוא", "vulcanverse": "ולקןורס", "fracturedthrones": "כסאותמנוגדים", "horizonforbiddenwest": "או<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ב", "twewy": "טוווי", "shadowpunk": "שָׁדוֹרפּאנק", "finalfantasyxv": "פיינלפנטסי15", "everoasis": "אברו<PERSON><PERSON>יס", "hogwartmystery": "ההוגוור<PERSON><PERSON>מיסטרי", "deltagreen": "דל<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "diablo": "ש<PERSON><PERSON>", "diablo3": "דיאבלו3", "diablo4": "ד<PERSON><PERSON><PERSON>4", "smite": "סמיי<PERSON>", "lastepoch": "תקופההאחרונה", "starfinder": "מוצ<PERSON><PERSON><PERSON><PERSON><PERSON>ים", "goldensun": "שמ<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "סניחהמקורית", "bladesinthedark": "להביםבחושך", "twilight2000": "טוויטלייט2000", "sandevistan": "סנדביסטן", "cyberpunk": "סיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberpunk2077": "סייברפאנק2077", "cyberpunkred": "סייב<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד", "dragonballxenoverse2": "דרגוןבולזןאוברס2", "fallenorder": "סדרנפילה", "finalfantasyxii": "הסוף_של_פנטזיה_12", "evillands": "אדמותרעות", "genshinimact": "גנשי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "aethyr": "אֵתִיר", "devilsurvivor": "מגווןשדים", "oldschoolrunescape": "רונסקייפשלהעולםישן", "finalfantasy10": "פיינלפנטזי10", "anime5e": "אנימיה5ה", "divinity": "אלוהיות", "pf2": "pf2", "farmrpg": "חיים<PERSON><PERSON>קלאות", "oldworldblues": "געגועיםלעולםישן", "adventurequest": "מסע_הרפתקאות", "dagorhir": "דאגו<PERSON>יה", "roleplayingames": "משחקיכניסיים", "roleplayinggames": "משחק<PERSON>_תפקידים", "finalfantasy9": "פיינלפנטזי9", "sunhaven": "שמשוודיה", "talesofsymphonia": "סיפורי_סימפוניה", "honkaistarrail": "הונק<PERSON><PERSON>םסטראיל", "wolong": "וולונג", "finalfantasy13": "פיינלפנטזי13", "daggerfall": "ד<PERSON><PERSON><PERSON><PERSON>ל", "torncity": "עירקרועה", "myfarog": "הפאגשלי", "sacredunderworld": "העולםהתחתוןהקדוש", "chainedechoes": "הדechoes", "darksoul": "נשמהאפלה", "soulslikes": "סולסלייקס", "othercide": "אחרגידה", "mountandblade": "הרקשתוחרבות", "inazumaeleven": "אינזומה11", "acvalhalla": "אדוונטורה_של_העין", "chronotrigger": "כרונו<PERSON>ר<PERSON><PERSON>ר", "pillarsofeternity": "עמודינצח", "palladiumrpg": "פלדיום<PERSON><PERSON>ג", "rifts": "ריבים", "tibia": "טיביה", "thedivision": "החובה", "hellocharlotte": "שלוםשרלוט", "legendofdragoon": "אגד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ון", "xenobladechronicles2": "זנובלייד<PERSON>רוניקלס2", "vampirolamascarada": "וּמַסְכָּרָה", "octopathtraveler": "נוסעיםב<PERSON>וקטופאת", "afkarena": "אףקאראנה", "werewolftheapocalypse": "זאב_הזומבים", "aveyond": "אביונד", "littlewood": "ליטלווד", "childrenofmorta": "ילדימורתה", "engineheart": "לביבהמנוע", "fable3": "פאבל3", "fablethelostchapter": "פאבל<PERSON><PERSON><PERSON><PERSON><PERSON>וד", "hiveswap": "ייבסווא<PERSON>", "rollenspiel": "משחקתת<PERSON>קידים", "harpg": "הארפג", "baldursgates": "בולדרסגייטס", "edeneternal": "עדןנצחי", "finalfantasy16": "פיינלפנטזי16", "andyandleyley": "אנדיאנדליילי", "ff15": "פפ15", "starfield": "שדהכוכבים", "oldschoolrevival": "החייא<PERSON><PERSON>ימוז<PERSON>קה", "finalfantasy12": "פיינלפנטסי12", "ff12": "פפ12", "morkborg": "מורקבורג", "savageworlds": "עולמות<PERSON><PERSON><PERSON><PERSON>ים", "diabloiv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pve": "פב", "kingdomheart1": "ממלכתהלב1", "ff9": "ff9", "kingdomheart2": "ממלכתלב2", "darknessdungeon": "מָרְתֶּףשְׁחוֹר", "juegosrpg": "משחקיrpg", "kingdomhearts": "ממלכתהלבבות", "kingdomheart3": "ממלכתלבבות3", "finalfantasy6": "פיינלפנטזי6", "ffvi": "פפוו", "clanmalkavian": "קלן<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "harvestella": "הארוויסטללה", "gloomhaven": "גלאו<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "wildhearts": "לבבותמאודרים", "bastion": "בַּסטיוֹן", "drakarochdemoner": "דרקרעוךדמונר", "skiesofarcadia": "שמיָםשׁלארקָדיה", "shadowhearts": "צלללבבות", "nierreplicant": "ניארר<PERSON><PERSON>יקנט", "gnosia": "גנוסיה", "pennyblood": "פֵּנִיבְּלוּד", "breathoffire4": "נשימתאש4", "mother3": "אמא3", "cyberpunk2020": "סייברפאנק2020", "falloutbos": "פולאוטבוס", "anothereden": "אחרת<PERSON>ידן", "roleplaygames": "משחק<PERSON>_תפקידים", "roleplaygame": "משחקת<PERSON>קידים", "fabulaultima": "פאבלאו<PERSON>טימטיבי", "witchsheart": "לבשלמכש<PERSON>ה", "harrypottergame": "משחקה<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "pathfinderrpg": "מחפשנתיבrpg", "pathfinder2e": "פאתפיינדר2e", "vampirilamasquerade": "מסיבת_המיסוך_הערפדית", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "ספלים<PERSON><PERSON>ר", "dragonageorigins": "דרק<PERSON><PERSON><PERSON>יד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ת", "chronocross": "כרונוקרוס", "cocttrpg": "קוקטטרפג", "huntroyale": "צידמלכות", "albertodyssey": "אלברטודיסי", "monsterhunterworld": "חיותהמרדף", "bg3": "ביג3", "xenogear": "זנו<PERSON><PERSON>ר", "temtem": "טמטים", "rpgforum": "פורוםrpg", "shadowheartscovenant": "שָׁדוֹהאַרְטְסְקָובֶנָּט", "bladesoul": "נשמתהחרבות", "baldursgate3": "באלדורסגייט3", "kingdomcome": "ממלכ<PERSON>וּב", "awplanet": "אוואפלאנט", "theworldendswithyou": "העולםנגמראיתך", "dragalialost": "דרגל<PERSON><PERSON><PERSON><PERSON>וסט", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "dyinglight2": "דיינגלאיט2", "finalfantasytactics": "תקטיקותה<PERSON><PERSON><PERSON>וןפנטאזיה", "grandia": "גרנדיה", "darkheresy": "הֶעָרִיצוּתִיהַשְּׁחוֹרָה", "shoptitans": "שופtitans", "forumrpg": "פורוםרו<PERSON><PERSON>ים", "golarion": "גו<PERSON><PERSON><PERSON>ון", "earthmagic": "קסםהארץ", "blackbook": "בלא<PERSON><PERSON><PERSON>ק", "skychildrenoflight": "ילדיהשמיים<PERSON><PERSON><PERSON>ר", "gryrpg": "גריר<PERSON><PERSON>", "sacredgoldedition": "מהדורת<PERSON>ה<PERSON><PERSON>קודש", "castlecrashers": "קריסתטירות", "gothicgame": "משחקאגותי", "scarletnexus": "סק<PERSON><PERSON><PERSON><PERSON><PERSON>קסוס", "ghostwiretokyo": "הגghostwiretokyo", "fallout2d20": "פולאוט2ד20", "gamingrpg": "גיימינגrpg", "prophunt": "פרופהנט", "starrails": "מסילות<PERSON><PERSON><PERSON><PERSON>ים", "cityofmist": "עירהשל<PERSON>ר<PERSON>ל", "indierpg": "משחק<PERSON>rpgעצמאיים", "pointandclick": "נקוד<PERSON><PERSON><PERSON>יק", "emilyisawaytoo": "אימייי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "emilyisaway": "אֵמילי_הולכת_לְבַד", "indivisible": "לאניתןלחלק", "freeside": "חו<PERSON><PERSON>י", "epic7": "אפיק7", "ff7evercrisis": "פופי7ננצח", "xenogears": "זינו<PERSON><PERSON><PERSON>ס", "megamitensei": "מג<PERSON><PERSON><PERSON><PERSON><PERSON>ש", "symbaroum": "סימברום", "postcyberpunk": "פוסטסיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathroadtocanada": "גםבדרךלמותלצרפת", "palladium": "פלאדיאום", "knightjdr": "לילהשלאבירים", "monsterhunter": "צי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "אשאמגלים", "genshinimpact": "גינשיןההשפעה", "geosupremancy": "גיאויק<PERSON>רד<PERSON>ות", "persona5": "פרסונה5", "ghostoftsushima": "רווח<PERSON>לת<PERSON>ימה", "sekiro": "סקיירו", "monsterhunterrise": "ציידימ<PERSON><PERSON><PERSON>ות<PERSON>גשוג", "nier": "ניר", "dothack": "ד<PERSON><PERSON><PERSON><PERSON>", "ys": "יס", "souleater": "נשףנשמה", "fatestaynight": "פאטהסטיינייט", "etrianodyssey": "אטריא<PERSON><PERSON>ו<PERSON><PERSON>סי", "nonarygames": "משחקיםנונריים", "tacticalrpg": "טקטיקלרפג", "mahoyo": "מאהויו", "animegames": "אנימי<PERSON><PERSON><PERSON><PERSON>ס", "damganronpa": "דמג<PERSON><PERSON>ו<PERSON><PERSON>ה", "granbluefantasy": "גרנבלו<PERSON>נטסי", "godeater": "מַכְּרֵי_תָפוּחִים", "diluc": "די<PERSON><PERSON><PERSON>", "venti": "וונטי", "eternalsonata": "סונאטהנצחית", "princessconnect": "נסיכת_חיבור", "hexenzirkel": "בין<PERSON><PERSON>ישו<PERSON>ים", "cristales": "קריסטלים", "vcs": "ויב<PERSON><PERSON>", "pes": "פס", "pocketsage": "כיסחכם", "valorant": "וולורנט", "valorante": "ואלורנט", "valorantindian": "וולורנטהודי", "dota": "דוטה", "madden": "מדן", "cdl": "סדנון", "efootbal": "כדורגלדיגיטלי", "nba2k": "nba2k", "egames": "גיי<PERSON><PERSON>נג", "fifa23": "פיפא23", "wwe2k": "וויתרק", "esport": "ספורטא<PERSON><PERSON><PERSON>רוני", "mlg": "מג", "leagueofdreamers": "ליגתהחלומות", "fifa14": "פיפה14", "midlaner": "מיודיע", "efootball": "כדור<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "חלום<PERSON><PERSON>ק", "gaimin": "<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "overwatchleague": "ליגתמופלאות", "cybersport": "סייברס<PERSON>ורט", "crazyraccoon": "רטטוןמשוגע", "test1test": "בדוק1בדוק", "fc24": "fc24", "riotgames": "ריאו<PERSON><PERSON><PERSON><PERSON><PERSON>ס", "eracing": "מרוץ", "brasilgameshow": "תערוכתמשחקיםברזיל", "valorantcompetitive": "valorantתחרותי", "t3arena": "t3arena", "valorantbr": "ברוולור<PERSON>ט", "csgo": "סיאסגו", "tf2": "tf2", "portal2": "פורטל2", "halflife": "חצי_חיים", "left4dead": "שאירלחיים", "left4dead2": "שמאלי4מתים2", "valve": "אווו", "portal": "פורטל", "teamfortress2": "טימפוסטרס2", "everlastingsummer": "קיץנחנק", "goatsimulator": "גואט<PERSON>ימולטור", "garrysmod": "גאריסמוד", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "טרנספורמיס", "justshapesandbeats": "רקצורותוד<PERSON>יקות", "battlefield4": "שדהקרב4", "nightinthewoods": "לילהביער", "halflife2": "חייםחצי2", "hacknslash": "האקתשלאש", "deeprockgalactic": "דייי<PERSON><PERSON><PERSON><PERSON>גלאקטיק", "riskofrain2": "סיכוןשלגשם2", "metroidvanias": "מטרוידבניות", "overcooked": "מלו<PERSON><PERSON>מדי", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "אינס<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "7d2d": "7ד2ד", "deadcells": "ד<PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ניאראוטו<PERSON>טה", "gmod": "גמוד", "dwarffortress": "מצודתגמדים", "foxhole": "פוק<PERSON>הול", "stray": "שיט<PERSON><PERSON>ן", "battlefield": "ש<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "שדהקרב1", "swtor": "סווטור", "fallout2": "פולאוט2", "uboat": "אובוט", "eyeb": "עין", "blackdesert": "יםהכחולה", "tabletopsimulator": "סימולטורשולחנות", "partyhard": "חוגגיםקשה", "hardspaceshipbreaker": "שוברח<PERSON><PERSON><PERSON><PERSON><PERSON>ח", "hades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunsmith": "סמסטרגלריה", "okami": "או<PERSON><PERSON>י", "trappedwithjester": "לכודעםבעלידיעה", "dinkum": "דינקום", "predecessor": "קודמיו", "rainworld": "עולםהגשם", "cavesofqud": "מערותקוד", "colonysim": "קולוניסים", "noita": "נואיטה", "dawnofwar": "זריחהשלמלחמה", "minionmasters": "מאסטרימיניון", "grimdawn": "גרימ<PERSON>ון", "darkanddarker": "כההועמוק", "motox": "מוטויקס", "blackmesa": "בלקומסה", "soulworker": "עובדנשמה", "datingsims": "סימסד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "יאגה", "cubeescape": "בריחת_קוביה", "hifirush": "היי<PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "סוונקופ", "newcity": "עירחדשה", "citiesskylines": "שקיפותערים", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "קןופסיה", "virtualkenopsia": "וירטואלקנופסיה", "snowrunner": "נשירתשלג", "libraryofruina": "ספרייתהח<PERSON><PERSON><PERSON>ה", "l4d2": "ל4ד2", "thenonarygames": "המשחקיםשלבוא", "omegastrikers": "אומיגאסטרייקרס", "wayfinder": "וויי<PERSON><PERSON><PERSON><PERSON><PERSON>ר", "kenabridgeofspirits": "קנאברידגאוףספיריטס", "placidplasticduck": "ברווז<PERSON>לסטירגוע", "battlebit": "באטלביט", "ultimatechickenhorse": "האולטימטיבישלתרנגולועוף", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "תחייךלי", "catnight": "לילחתולים", "supermeatboy": "סופר<PERSON>יט<PERSON><PERSON>י", "tinnybunny": "טיני_באני", "cozygrove": "<PERSON>ו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "דום", "callofduty": "קריאתחו<PERSON>ה", "callofdutyww2": "קולמןמלחמהww2", "rainbow6": "ריינבו6", "apexlegends": "האפיקסלגנדס", "cod": "קוד", "borderlands": "בדרלנדס", "pubg": "פאבג", "callofdutyzombies": "קריאתחובותזומבים", "apex": "איי<PERSON><PERSON><PERSON>", "r6siege": "r6siege", "megamanx": "מגה<PERSON><PERSON><PERSON><PERSON>ס", "touhou": "תוּהוּוּ", "farcry": "שאגת_רחוק", "farcrygames": "משחקיפאר<PERSON><PERSON>י", "paladins": "פלדינס", "earthdefenseforce": "הגנתכדורהארץ", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "ghostrecon": "ג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "grandtheftauto5": "גניבתרכב5", "warz": "מלחמות", "sierra117": "סיירה117", "dayzstandalone": "דיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "ultrakill": "אול<PERSON><PERSON><PERSON>יל", "joinsquad": "בואו_לצוות", "echovr": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "discoelysium": "דיסקווליזיום", "insurgencysandstorm": "סופתחולודלמורתג", "farcry3": "פארקריי3", "hotlinemiami": "הוטלינמיami", "maxpayne": "מק<PERSON><PERSON><PERSON>ין", "hitman3": "היטמן3", "r6s": "r6s", "rainbowsixsiege": "ריינבוס<PERSON>קססיג", "deathstranding": "דייתסטרנדינג", "b4b": "ב4ב", "codwarzone": "קודו<PERSON><PERSON><PERSON><PERSON>ן", "callofdutywarzone": "קרבאותקודיםור<PERSON>ון", "codzombies": "זומביםלקוד", "mirrorsedge": "מראות<PERSON>צה", "divisions2": "חלוקות2", "killzone": "אזורהקטל", "helghan": "הל<PERSON>ן", "coldwarzombies": "מלחמתקרחזומבים", "metro2033": "מטרו2033", "metalgear": "מטלג<PERSON>ר", "acecombat": "איי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "קרוסקוד", "goldeneye007": "גולדןהאי007", "blackops2": "בלאקאופס2", "sniperelite": "סניי<PERSON><PERSON><PERSON><PERSON><PERSON>ט", "modernwarfare": "מלחמתמודרנית", "neonabyss": "נאוןא<PERSON>יוס", "planetside2": "פלנטסייד2", "mechwarrior": "מכונאיالحرب", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>_גבול", "owerwatch": "אוברווטש", "rtype": "ריי<PERSON>", "dcsworld": "dcsworld", "escapefromtarkov": "הצילו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ב", "metalslug": "מתכתחתוך", "primalcarnage": "קארנ<PERSON>ג<PERSON>רימל", "worldofwarships": "עולמאוניותחידתים", "back4blood": "בַּחֲרוֹת_לְדָם", "warframe": "וורפריים", "rainbow6siege": "שששש<PERSON>שריינבו6", "xcom": "זקנים", "hitman": "שח<PERSON><PERSON>", "masseffect": "מאסהאפקט", "systemshock": "שוק_המערכת", "valkyriachronicles": "ויקיריהכרוניקות", "specopstheline": "ספצופסהשורה", "killingfloor2": "קילינגפלור2", "cavestory": "סיפורמערה", "doometernal": "דוםאיתן", "centuryageofashes": "עידן<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ה", "farcry4": "פארקרי4", "gearsofwar": "גלגלימלחמה", "mwo": "מיו", "division2": "דיוויזיה2", "tythetasmaniantiger": "טי_הטסהמני_טיגר", "generationzero": "דור0", "enterthegungeon": "תיכנסלגנ<PERSON>ן", "jakanddaxter": "ג<PERSON><PERSON><PERSON><PERSON>סטר", "modernwarfare2": "מלחמהמודרנית2", "blackops1": "בלאקאופס1", "sausageman": "אי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ק", "ratchetandclank": "רצטוק<PERSON><PERSON><PERSON>ק", "chexquest": "צקסקווסט", "thephantompain": "כאבפנטום", "warface": "פניםמלחמה", "crossfire": "קְרוֹסְפַייר", "atomicheart": "לבאטומי", "blackops3": "בלאקאופס3", "vampiresurvivors": "ווֹאמְפִירִיםהַשִׁימוּשִׁים", "callofdutybatleroyale": "קריאת<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>טלרויאל", "moorhuhn": "מוּרְחוּן", "freedoom": "חופשדרים", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "שבור", "tinytina": "טיניטינה", "gamepubg": "משחקpubg", "necromunda": "נקרומונדה", "metalgearsonsoflibert": "מטלגירבניהחירות", "juegosfps": "משחקיםfps", "convertstrike": "המרתמ<PERSON>ה", "warzone2": "אזורמלחמה2", "shatterline": "שא<PERSON>ר<PERSON><PERSON>יין", "blackopszombies": "זומביםב<PERSON><PERSON>קאו<PERSON>ס", "bloodymess": "בלג<PERSON><PERSON><PERSON><PERSON>ני", "republiccommando": "רפובליקו<PERSON>נדו", "elitedangerous": "אליטאהמסכנה", "soldat": "חייל", "groundbranch": "סניףקרקע", "squad": "צוות", "destiny1": "גורל1", "gamingfps": "גיימינגfps", "redfall": "רדפל", "pubggirl": "בנותpubg", "worldoftanksblitz": "עולםשלטנקיםבליץ", "callofdutyblackops": "קולאוףדוטיבלאקאופס", "enlisted": "מגויס", "farlight": "פארלייט", "farcry5": "פארקריי5", "farcry6": "פארקריי6", "farlight84": "פארלייט84", "splatoon3": "ספלatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "פאבלובוּר", "xdefiant": "xדיפנט", "tinytinaswonderlands": "עולמותשלטיניטינה", "halo2": "הלו2", "payday2": "שכר_מינימום2", "cs16": "cs16", "pubgindonesia": "פאפגאינדונזיה", "pubgukraine": "פאבגאוקראינה", "pubgeu": "פאבגיו", "pubgczsk": "פובגצזק", "wotblitz": "וֹטבּוּליץ", "pubgromania": "פאבגברומניה", "empyrion": "אמ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "pubgczech": "פאב<PERSON><PERSON>ק", "titanfall2": "טיטנפול2", "soapcod": "סבוןקוד", "ghostcod": "רוח<PERSON><PERSON>ד", "csplay": "סיספלי", "unrealtournament": "טו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "callofdutydmz": "קולשל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ז", "gamingcodm": "ג<PERSON>י<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>מ", "borderlands2": "הקדמות2", "counterstrike": "קונטרסטרייק", "cs2": "cs2", "pistolwhip": "מכהב<PERSON>ק<PERSON>ח", "callofdutymw2": "קאלאפדוטימו2", "quakechampions": "מְנַצֵּחַבָּצָפָרוּעַ", "halo3": "הלוה3", "halo": "הלו", "killingfloor": "רצפתהרגה", "destiny2": "דסטיני2", "exoprimal": "אק<PERSON><PERSON><PERSON>רימל", "splintercell": "צלמתפצל", "neonwhite": "ניאו<PERSON><PERSON><PERSON>ה", "remnant": "שארית", "azurelane": "אזור_הים", "worldofwar": "עולםהמלחמה", "gunvolt": "גאנגולט", "returnal": "חו<PERSON><PERSON>לאל", "halo4": "האלו4", "haloreach": "הלוריץ", "shadowman": "צַלְמָוֶת", "quake2": "רעש2", "microvolts": "מיקרוולטים", "reddead": "רדדד", "standoff2": "סטנדופ2", "harekat": "הראקת", "battlefield3": "הקרב3", "lostark": "לוסטארק", "guildwars2": "גילדוורים2", "fallout76": "פולאוט76", "elsword": "אלסוורד", "seaofthieves": "יםהגנבים", "rust": "חלודה", "conqueronline": "כובשבאינטרנט", "dauntless": "לאמפעמים", "warships": "ספי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "יוםה<PERSON><PERSON>קונים", "warthunder": "רעםעםמכה", "flightrising": "עפיםעצים", "recroom": "חדרים", "legendsofruneterra": "ליגנדסאו<PERSON>רונטרע", "pso2": "פסו2", "myster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "כוכבפנטזיה2", "maidenless": "בליחברה", "ninokuni": "נינוקו<PERSON>י", "worldoftanks": "עולםשלטנקים", "crossout": "חוצה", "agario": "אגאריו", "secondlife": "חיים<PERSON>ניים", "aion": "איון", "toweroffantasy": "מגדלחלומות", "netplay": "משח<PERSON>_ברשת", "everquest": "מסע_עד_ההצלחה", "metin2": "מתי2", "gtaonline": "גטא<PERSON>ונליין", "ninokunicrossworld": "נינו_קוני_קרוס_וורלד", "reddeadonline": "רד<PERSON><PERSON><PERSON>ונליין", "superanimalroyale": "סופרבעלי<PERSON><PERSON>ים<PERSON><PERSON>יאל", "ragnarokonline": "רא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ין", "knightonline": "נשמהאו<PERSON><PERSON>יין", "gw2": "גוו2", "tboi": "טבוי", "thebindingofisaac": "הקשרשלאייזק", "dragonageinquisition": "דרגון<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>קוויזיציה", "codevein": "קודו<PERSON>ין", "eveonline": "איב<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "clubpenguin": "קלוב<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "lotro": "לוטרו", "wakfu": "וואקפו", "scum": "בּוֹאָה", "newworld": "עולםחדש", "blackdesertonline": "המדבר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "multiplayer": "רבמשתתפים", "pirate101": "בויוקי101", "honorofkings": "כבודהמלכים", "fivem": "פייבם", "starwarsbattlefront": "מלחמתהכוכביםחזית", "karmaland": "קרמלנד", "ssbu": "ססבוא", "starwarsbattlefront2": "סטארוורסבאטלפראנט2", "phigros": "פיגרוס", "mmo": "ממו", "pokemmo": "פוקימון<PERSON><PERSON>ו", "ponytown": "פו<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "3dchat": "צאטתלתימדי", "nostale": "נוסטליה", "tauriwow": "טאוריוו<PERSON>ו", "wowclassic": "וואוקלסיק", "worldofwarcraft": "עולםהלחימה", "warcraft": "ווקרא<PERSON>ט", "wotlk": "ווטלק", "runescape": "רונסקייפ", "neopets": "ניאופטים", "moba": "מוֹבָּה", "habbo": "בּוּהַבּוֹ", "archeage": "ארקי<PERSON>ג", "toramonline": "טורא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ין", "mabinogi": "מאבינוגי", "ashesofcreation": "אפריי<PERSON><PERSON>ר", "riotmmo": "ראיו<PERSON><PERSON>מאו", "silkroad": "מסלול_משי", "spiralknights": "מעליותספירלה", "mulegend": "אגדהמול", "startrekonline": "סטארטר<PERSON><PERSON><PERSON><PERSON><PERSON>ליין", "vindictus": "נקמה", "albiononline": "אלביו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ין", "bladeandsoul": "בליי<PERSON><PERSON><PERSON><PERSON><PERSON>ול", "evony": "אבו<PERSON>י", "dragonsprophet": "נשיאד<PERSON><PERSON>ונים", "grymmo": "גרימו", "warmane": "וו<PERSON><PERSON>ן", "multijugador": "מולטי<PERSON><PERSON><PERSON><PERSON>ר", "angelsonline": "מלא<PERSON><PERSON>ם<PERSON><PERSON><PERSON>נטרנט", "lunia": "לוניה", "luniaz": "לוניאס", "idleon": "אייד<PERSON><PERSON>ן", "dcuniverseonline": "יקוםd<PERSON><PERSON><PERSON>קוון", "growtopia": "גרוטופיה", "starwarsoldrepublic": "סטארוורסהגדהישנה", "grandfantasia": "גרנד<PERSON>נטזיה", "blueprotocol": "פרוטוק<PERSON><PERSON><PERSON><PERSON>ול", "perfectworld": "עולםמושלם", "riseonline": "תתמקדב<PERSON>רטים", "corepunk": "קו<PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequestworlds": "הרפתקתעולם", "flyforfun": "מעופףבווים", "animaljam": "חיות<PERSON><PERSON><PERSON>ה", "kingdomofloathing": "ממלכתהשלילה", "cityofheroes": "עירשלגיבורים", "mortalkombat": "מortalcombat", "streetfighter": "לוח<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "החיפושיתשחורה", "metalgearsolid": "מטאלגי<PERSON>רסוליד", "forhonor": "בעבורכבוד", "tekken": "<PERSON><PERSON><PERSON>", "guiltygear": "גילטי<PERSON><PERSON>ר", "xenoverse2": "זנוברס2", "fgc": "fgc", "streetfighter6": "רחובלוחם6", "multiversus": "מרובתעולמות", "smashbrosultimate": "סמאש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ימטיב", "soulcalibur": "סולקליבר", "brawlhalla": "ברוללה", "virtuafighter": "לוחם<PERSON><PERSON><PERSON>טואלי", "streetsofrage": "רחובותשלבוא", "mkdeadlyalliance": "מקדאליאליאנס", "nomoreheroes": "איןעוד<PERSON><PERSON><PERSON>ורים", "mhr": "מ<PERSON>", "mortalkombat12": "מortalkombat12", "thekingoffighters": "המלךשללוחמים", "likeadragon": "כ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "retrofightinggames": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ו", "blasphemous": "<PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "מתחרים<PERSON><PERSON><PERSON>עתי", "persona4arena": "פרסונה4זירה", "marvelvscapcom": "מרוולנג<PERSON><PERSON><PERSON>קום", "supersmash": "סופרשמוש", "mugen": "מו<PERSON>ן", "warofthemonsters": "מלחמתהמפלצות", "jogosdeluta": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "סייברבוטים", "armoredwarriors": "לוחמיםמו<PERSON>נייים", "finalfight": "קרבסופי", "poweredgear": "גאדגטיםעוצמתיים", "beatemup": "להכותאותם", "blazblue": "בלייזבלו", "mortalkombat9": "מרטלcombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "אינסטינקטיםקטלניים", "kingoffigthers": "מלךהלוחמים", "ghostrunner": "רודףרוחות", "chivalry2": "אביריות2", "demonssouls": "נפשותשדים", "blazbluecrosstag": "בלזבלוקצרוסתג", "blazbluextagbattle": "בלזבלו<PERSON><PERSON><PERSON>באטל", "blazbluextag": "בלזבלוxtag", "guiltygearstrive": "גילטי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ב", "hollowknightsequel": "המשך_הולואו_נייט", "hollowknightsilksong": "חולצות<PERSON>קמה", "silksonghornet": "סילקזונ<PERSON><PERSON>ורנט", "silksonggame": "משחקסילקסונג", "silksongnews": "חדשותסילקסונג", "silksong": "סילקסונג", "undernight": "אונדרנייט", "typelumina": "טייפלומינה", "evolutiontournament": "תחרותהתפתחות", "evomoment": "רגעתפתוח", "lollipopchainsaw": "לְכָהלְלַפֶּפֶתְשָׁואֵט", "dragonballfighterz": "דרגו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofberseria": "סיפוריברסריה", "bloodborne": "דםנשפך", "horizon": "חזית", "pathofexile": "נתיבשלגולה", "slimerancher": "סליי<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "קרשבנדיקוט", "bloodbourne": "דמושע", "uncharted": "לאבנות", "horizonzerodawn": "הורייז<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "ps4": "פס4", "ps5": "פלייסטיישן5", "spyro": "ספיירו", "playstationplus": "פלייסטיישןפלאס", "lastofus": "האחרו<PERSON><PERSON>םבנו", "infamous": "מפורסם", "playstationbuddies": "ח<PERSON><PERSON><PERSON>םל<PERSON>לייסטיישן", "ps1": "פיס1", "oddworld": "עולםמשונה", "playstation5": "פלייסטיישן5", "slycooper": "סליי<PERSON><PERSON><PERSON><PERSON>", "psp": "פספוס", "rabbids": "ראבידס", "splitgate": "ספלטגייט", "persona4": "פרסונה4", "hellletloose": "תןלקלקלה", "gta4": "גטא4", "gta": "גטה", "roguecompany": "חברתרותם", "aisomniumfiles": "קבצי_אייסומניום", "gta5": "גטא5", "gtasanandreas": "גטא<PERSON><PERSON>אנד<PERSON><PERSON>אס", "godofwar": "אלוהיהמלחמה", "gris": "גריס", "trove": "אוצר", "detroitbecomehuman": "דטרויטמתממשים", "beatsaber": "ביט<PERSON><PERSON><PERSON><PERSON><PERSON>", "rimworld": "רימוורלד", "stellaris": "סטלריס", "ps3": "ps3", "untildawn": "עדבוקר", "touristtrophy": "תעודתתיירות", "lspdfr": "לספדר", "shadowofthecolossus": "צלשלהקולוסוס", "crashteamracing": "קבוצתגזירה", "fivepd": "חמשpd", "tekken7": "טקן7", "devilmaycry": "שטןיכולל<PERSON>כות", "devilmaycry3": "שטןיכואב3", "devilmaycry5": "שטן_יכול_לבכות5", "ufc4": "אufc4", "playingstation": "מַלְכּוּת_הַשַּׂחֲקִים", "samuraiwarriors": "לוחמיהסמוראים", "psvr2": "פסיצוורי2", "thelastguardian": "השומר<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "soulblade": "להבותנ<PERSON>ש", "gta5rp": "גta5rp", "gtav": "ג<PERSON><PERSON><PERSON>", "playstation3": "פלייסטישן3", "manhunt": "צידגברים", "gtavicecity": "גטאווייסיטי", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "שְׂפַתֵיצֵל2בְּרִית", "pcsx2": "pcsx2", "lastguardian": "ה<PERSON><PERSON>_האחרון", "xboxone": "אק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "forza": "פורצה", "cd": "סד", "gamepass": "גיימ<PERSON><PERSON>ס", "armello": "ארמלו", "partyanimal": "חייתמסיבות", "warharmmer40k": "וורהמר40ק", "fightnightchampion": "לילחם<PERSON>ייטצמפיונס", "psychonauts": "פסיכונאוטים", "mhw": "ממיי<PERSON>י", "princeofpersia": "נסיךפרס", "theelderscrollsskyrim": "הזקניםוהגלימותסקיירים", "pantarhei": "פנט<PERSON><PERSON><PERSON>י", "theelderscrolls": "הגללתזקנים", "gxbox": "גיוק<PERSON><PERSON>ו<PERSON>ס", "battlefront": "חזית<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "אלתתלעצמםביחד", "ori": "אורי", "spelunky": "ספלוונקי", "xbox1": "אקסבוקס1", "xbox360": "אקסבוקס360", "starbound": "כוכביםבדרך", "xboxonex": "בּוֹאְקשורלxboxonex", "forzahorizon5": "פורצההורייזון5", "skate3": "סקייט3", "houseflipper": "משפץבתים", "americanmcgeesalice": "אמריק<PERSON><PERSON><PERSON>גיזעליס", "xboxs": "אקס<PERSON><PERSON><PERSON>ס", "xboxseriesx": "אקסבו<PERSON><PERSON>סיריוז<PERSON><PERSON>ס", "xboxseries": "אקסבוקססיריס", "r6xbox": "r6אק<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueofkingdoms": "הליגהלממלכות", "fable2": "פייבל2", "xboxgamepass": "אקסבוקסגייםפאס", "undertale": "אנדרטייל", "trashtv": "טלוויזיה_זבל", "skycotl": "סקיו<PERSON>טל", "erica": "עריקה", "ancestory": "אבותאבות", "cuphead": "קאפחד", "littlemisfortune": "ליטל<PERSON>יס<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "sallyface": "סאליפייס", "franbow": "פרנבו", "monsterprom": "פְרוֹםמוֹנֶסְטֵר", "projectzomboid": "פרויקטזומבי", "ddlc": "ד<PERSON><PERSON><PERSON>", "motos": "מוטוס", "outerwilds": "חוץמהעולם", "pbbg": "פייביג", "anshi": "אנשי", "cultofthelamb": "כתשל<PERSON><PERSON><PERSON><PERSON>", "duckgame": "משח<PERSON><PERSON>רווזים", "thestanleyparable": "הפרקשלסטנלי", "towerunite": "מגדל<PERSON>י<PERSON>וד", "occulto": "אוקולטו", "longdrive": "נסיעה<PERSON><PERSON><PERSON><PERSON>ה", "satisfactory": "סבבה", "pluviophile": "אוהב_גשם", "underearth": "מתחתלאדמה", "assettocorsa": "אסטוקורסה", "geometrydash": "גיאו<PERSON><PERSON><PERSON>ידש", "kerbal": "קרבל", "kerbalspaceprogram": "תוכניתחל<PERSON><PERSON>רבל", "kenshi": "קנשי", "spiritfarer": "נשמות_נודדות", "darkdome": "כיפהכהה", "pizzatower": "מגדלפיצה", "indiegame": "משח<PERSON><PERSON>צמאי", "itchio": "איטציו", "golfit": "גולפיט", "truthordare": "שתףאועזוב", "game": "משח<PERSON>", "rockpaperscissors": "מניירוצֶה️", "trampoline": "טרמפולינה", "hulahoop": "חולוהו<PERSON>", "dare": "אתגר", "scavengerhunt": "מרדף_אוצרות", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>_חצר", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "נכ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "ביר<PERSON><PERSON><PERSON>", "dicegoblin": "גובליןדייס", "cosygames": "משחקים<PERSON>חמדים", "datinggames": "משחקיד<PERSON><PERSON><PERSON><PERSON>נג", "freegame": "משחקחינם", "drinkinggames": "משחקישתייה", "sodoku": "סודוקו", "juegos": "משחקים", "mahjong": "מאחונג", "jeux": "גיי<PERSON><PERSON>נג", "simulationgames": "משחקי_סימולציה", "wordgames": "משחקימילים", "jeuxdemots": "משחקימילים", "juegosdepalabras": "חידות<PERSON><PERSON>ה", "letsplayagame": "בוא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "משחקיםמחממים", "oyun": "בו<PERSON><PERSON><PERSON><PERSON><PERSON>", "interactivegames": "משחקים<PERSON><PERSON><PERSON><PERSON>ראקטיביים", "amtgard": "אמת<PERSON><PERSON><PERSON>ד", "staringcontests": "תחרויותבוהן", "spiele": "שח<PERSON>", "giochi": "גוקים", "geoguessr": "גיאו<PERSON><PERSON>גל", "iphonegames": "משחק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "boogames": "משח<PERSON>י_בו", "cranegame": "משחק<PERSON><PERSON><PERSON>ן", "hideandseek": "מְחֻבָּרִיםוּמְחֻבָּרִים", "hopscotch": "חמ<PERSON><PERSON><PERSON><PERSON>ים", "arcadegames": "משחק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד", "yakuzagames": "משח<PERSON><PERSON>_יאקוזה", "classicgame": "משחקקל<PERSON><PERSON>י", "mindgames": "משח<PERSON><PERSON>_מוח", "guessthelyric": "נחשות<PERSON><PERSON>ר", "galagames": "גאלגיימס", "romancegame": "משח<PERSON><PERSON>ומנטי", "yanderegames": "משחקיייד<PERSON>ה", "tonguetwisters": "שפות<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ון", "4xgames": "4משחקים", "gamefi": "גיימ<PERSON>יי", "jeuxdarcades": "משחקיםבאולמות게임", "tabletopgames": "משחק<PERSON><PERSON>שו<PERSON><PERSON>ן", "metroidvania": "מטرويدווניה", "games90": "משחקים90", "idareyou": "אנימאתגראותך", "mozaa": "מוזע", "fumitouedagames": "פומיטו<PERSON><PERSON><PERSON>קידרום", "racinggames": "משחקימרו<PERSON>ים", "ets2": "אֵטֵס2", "realvsfake": "אמיתיומזויף", "playgames": "שחק<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "gameonline": "משחק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "onlinegames": "משחקיםב<PERSON><PERSON>נטרנט", "jogosonline": "משחקיםב<PERSON><PERSON>נטרנט", "writtenroleplay": "כתיבתמ<PERSON><PERSON><PERSON>ת<PERSON>קידים", "playaballgame": "שחקים<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "pictionary": "פי<PERSON><PERSON><PERSON><PERSON>י", "coopgames": "משח<PERSON><PERSON>_קואליציה", "jenga": "גנגה", "wiigames": "מש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "תוצאהגבוהה", "jeuxderôles": "משחקיגיבורים", "burgergames": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "kidsgames": "משחקיםלילדים", "skeeball": "סקיבול", "nfsmwblackedition": "נפסמב<PERSON><PERSON><PERSON><PERSON>ד<PERSON>שן", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "משחקשאלות", "gioco": "משח<PERSON>", "managementgame": "משח<PERSON><PERSON>יהול", "hiddenobjectgame": "משחקי_חפצים_מוסתרים", "roolipelit": "רוליפליט", "formula1game": "משחקמכוניותf1", "citybuilder": "מיב<PERSON><PERSON><PERSON><PERSON>ר", "drdriving": "דר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "מש<PERSON><PERSON><PERSON>סלון", "memorygames": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "vulkan": "וול<PERSON>ן", "actiongames": "משחקיפעולה", "blowgames": "שחקיםבום", "pinballmachines": "מכונות<PERSON><PERSON>ןבול", "oldgames": "משחקיםישנים", "couchcoop": "שיתוףספה", "perguntados": "שאלות", "gameo": "גיימו", "lasergame": "משחקחץ", "imessagegames": "מש<PERSON><PERSON><PERSON>_אימ<PERSON>ג", "idlegames": "משח<PERSON><PERSON>_שעמום", "fillintheblank": "מלאאתהריק", "jeuxpc": "משחקיpc", "rétrogaming": "רטרו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ג", "logicgames": "משח<PERSON><PERSON>_לוגיקה", "japangame": "י<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "ריזא<PERSON><PERSON>יים", "subwaysurf": "סאבווייסרף", "jeuxdecelebrite": "גיימסאשליות", "exitgames": "משחקיםלצאת", "5vs5": "5על5", "rolgame": "רולגיים", "dashiegames": "דא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "שחקותומשחיתים", "traditionalgames": "משחקיםמסורתיים", "kniffel": "קניפל", "gamefps": "משחקfps", "textbasedgames": "משחק<PERSON>_מובנים", "gryparagrafowe": "גר<PERSON>ראג<PERSON>ובו", "fantacalcio": "פנט<PERSON><PERSON><PERSON>ורגל", "retrospel": "רטרוס<PERSON>ל", "thiefgame": "משחקגניבה", "lawngames": "משחקיד<PERSON>א", "fliperama": "פליפרמה", "heroclix": "הירוקליקס", "tablesoccer": "שול<PERSON><PERSON>ובול", "tischfußball": "גולשידוד", "spieleabende": "ערבי_משחקים", "jeuxforum": "פורום<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "casualgames": "משחקיםמזדמנים", "fléchettes": "בּוּלְשֶׁט", "escapegames": "משח<PERSON><PERSON>_בריחה", "thiefgameseries": "משח<PERSON><PERSON>_גנבים", "cranegames": "משחקימנוף", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "בפורטנייט", "jogosorte": "גוגוסורטה", "mage": "מאג", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "משחקיםב<PERSON><PERSON>נטרנט", "mölkky": "מול<PERSON>י", "gamenights": "לילות<PERSON><PERSON><PERSON><PERSON><PERSON>נג", "pursebingos": "תיקבינגו", "randomizer": "מזניק", "msx": "<PERSON><PERSON><PERSON><PERSON>", "anagrammi": "אנגרמים", "gamespc": "משחקיpc", "socialdeductiongames": "משחקי_הנחה_חברתית", "dominos": "דו<PERSON><PERSON><PERSON><PERSON>ס", "domino": "דו<PERSON>ינו", "isometricgames": "משחקיםאיסומטריים", "goodoldgames": "משחקים<PERSON><PERSON><PERSON>יםטובים", "truthanddare": "אמתובח<PERSON><PERSON>ה", "mahjongriichi": "מאהונג<PERSON><PERSON><PERSON>צי", "scavengerhunts": "חידות<PERSON>חייבותלמצא", "jeuxvirtuel": "גיימינגעולם", "romhack": "רומה<PERSON><PERSON>ים", "f2pgamer": "שחקןf2p", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "משחק<PERSON>נטזיה", "gryonline": "גריא<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "driftgame": "משחקהדרי<PERSON>ט", "gamesotomes": "משחקיםעימבו", "halotvseriesandgames": "הלואיט<PERSON>הסדרותואgames", "mushroomoasis": "תעלתפטריות", "anythingwithanengine": "הכולעםמנוע", "everywheregame": "משחק<PERSON>כלמקום", "swordandsorcery": "חרבוכ<PERSON><PERSON>ים", "goodgamegiving": "נתינתמ<PERSON><PERSON><PERSON><PERSON>ם<PERSON>ובה", "jugamos": "שחקנים", "lab8games": "משח<PERSON><PERSON>_לַב8", "labzerogames": "משחק<PERSON>_לייב_אפס", "grykomputerowe": "גיי<PERSON><PERSON>נג", "virgogami": "וויר<PERSON><PERSON><PERSON><PERSON>י", "gogame": "שחקביחד", "jeuxderythmes": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "משח<PERSON><PERSON>ם<PERSON><PERSON><PERSON>ן", "ridgeracertype4": "סוג4שלרידג<PERSON><PERSON>ס", "selflovegaming": "אוהבאתעצמיגיימינג", "gamemodding": "מוודינ<PERSON><PERSON><PERSON>ימס", "crimegames": "משחקי_פשע", "dobbelspellen": "דובלספלים", "spelletjes": "משחקים", "spacenerf": "נרףבחלל", "charades": "משחקי_תחפושות", "singleplayer": "שח<PERSON><PERSON><PERSON>ו<PERSON>ד", "coopgame": "משחק<PERSON>יתו<PERSON>י", "gamed": "גיי<PERSON>ד", "forzahorizon": "פורצה<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ון", "nexus": "נקסוס", "geforcenow": "גיימינ<PERSON><PERSON><PERSON>שיו", "maingame": "מאינגיים", "kingdiscord": "מלךדיסקורד", "scrabble": "שscrabble", "schach": "שח<PERSON><PERSON>", "shogi": "שוגי", "dandd": "ד<PERSON><PERSON><PERSON><PERSON>", "catan": "ק<PERSON><PERSON>ן", "ludo": "לודו", "backgammon": "שחור_ולבן", "onitama": "אוןיטמה", "pandemiclegacy": "מורשתהמגפה", "camelup": "גמלעלא", "monopolygame": "משחקמונופול", "brettspiele": "משחק<PERSON><PERSON><PERSON><PERSON>נגו", "bordspellen": "משחקיםשולחניים", "boardgame": "משחק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "משחקיחברתיים", "planszowe": "משחק<PERSON>_קלפים", "risiko": "<PERSON>י<PERSON><PERSON><PERSON>", "permainanpapan": "משח<PERSON><PERSON>_לוח", "zombicide": "זומביצייד", "tabletop": "שול<PERSON>ן", "baduk": "ב<PERSON><PERSON><PERSON><PERSON>", "bloodbowl": "גביעדם", "cluedo": "קלודו", "xiangqi": "שי<PERSON><PERSON><PERSON><PERSON><PERSON>", "senet": "סנאט", "goboardgame": "גובורדגיים", "connectfour": "קונקט<PERSON>ור", "heroquest": "גיבורי_המסעות", "giochidatavolo": "משחק<PERSON>שול<PERSON>ן", "farkle": "פ<PERSON>ר<PERSON>ל", "carrom": "קרום", "tablegames": "משח<PERSON><PERSON>_שולחן", "dicegames": "משחקיקוביות", "yatzy": "יאצי", "parchis": "פארציס", "jogodetabuleiro": "גוגודטבולרו", "jocuridesocietate": "שחקניתחב<PERSON>ה", "deskgames": "משחקיםעלשולחן", "alpharius": "אלפלריוס", "masaoyunları": "מאס<PERSON><PERSON><PERSON><PERSON>ו<PERSON><PERSON><PERSON><PERSON>י", "marvelcrisisprotocol": "פרוקטוק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>וול", "cosmicencounter": "מ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>מי", "creationludique": "יצירתיותמגניבה", "tabletoproleplay": "משחקי_תפקידים_על_שולחן", "cardboardgames": "מש<PERSON><PERSON><PERSON>_קרטון", "eldritchhorror": "אימהאילמת", "switchboardgames": "בואו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>קיםמהחיים", "infinitythegame": "איןסוףהמשחק", "kingdomdeath": "ממלכתמוות", "yahtzee": "יאצי", "chutesandladders": "חלקיםוסולמות", "társas": "חב<PERSON>ה", "juegodemesa": "משחקשולחן", "planszówki": "לוחיות", "rednecklife": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "boardom": "שיעמום", "applestoapples": "תפוחיםלתפוחים", "jeudesociété": "משח<PERSON><PERSON>_שולחן", "gameboard": "לוחמשחק", "dominó": "דו<PERSON>ינו", "kalah": "קלה", "crokinole": "קרוקינול", "jeuxdesociétés": "משח<PERSON><PERSON>_לוח", "twilightimperium": "אימפריית<PERSON><PERSON>רביים", "horseopoly": "חור<PERSON>ו<PERSON>ולי", "deckbuilding": "בנייתדקים", "mansionsofmadness": "אחוזותשלטירוף", "gomoku": "גומוקו", "giochidatavola": "משחקיםעלשולחן", "shadowsofbrimstone": "תהומות<PERSON><PERSON><PERSON><PERSON><PERSON>טון", "kingoftokyo": "מלךטוקיו", "warcaby": "בו<PERSON><PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "טאבלגיים", "battleship": "באטלשיפ", "tickettoride": "כרטיס<PERSON><PERSON><PERSON><PERSON><PERSON>ה", "deskovehry": "משחקיםעלשולחן", "catán": "ק<PERSON><PERSON>ן", "subbuteo": "סאבוטיו", "jeuxdeplateau": "משח<PERSON><PERSON>_קבע", "stolníhry": "שולח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "xiángqi": "שי<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "משחקימבינים", "gesellschaftsspiele": "משח<PERSON><PERSON>_שולחן", "starwarslegion": "לגי<PERSON><PERSON><PERSON><PERSON><PERSON>י", "gochess": "גויתגור<PERSON>ש", "weiqi": "וויצי", "jeuxdesocietes": "משחקימשפחה", "terraria": "טרריה", "dsmp": "דסמ<PERSON>ּ", "warzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "ארקשרדותמתפתחת", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "איי<PERSON><PERSON><PERSON><PERSON>טיוי", "theisle": "האי", "thelastofus": "האחרים<PERSON><PERSON><PERSON>נינו", "nomanssky": "איןאי<PERSON><PERSON><PERSON>מיים", "subnautica": "סאבנוטיקה", "tombraider": "טו<PERSON><PERSON><PERSON><PERSON>ידר", "callofcthulhu": "קריאתקטולו", "bendyandtheinkmachine": "בנטיואנת<PERSON>נקמכינה", "conanexiles": "קונןאקסיילס", "eft": "אפט", "amongus": "בינינו", "eco": "אקולוגי", "monkeyisland": "איהקופים", "valheim": "ואליים", "planetcrafter": "מתכנני_כוכבים", "daysgone": "ימים<PERSON>ב<PERSON>ו", "fobia": "פוביה", "witchit": "מכש<PERSON>ית", "pathologic": "פתולוגי", "zomboid": "זומבוייד", "northgard": "נורתגרד", "7dtd": "7dtd", "thelongdark": "הכיףשחור", "ark": "אר<PERSON>", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "מצבשלב2", "vrising": "עלייהבם", "madfather": "אבאכועס", "dontstarve": "אלת<PERSON><PERSON><PERSON>עב", "eternalreturn": "חזרהנצחית", "pathoftitans": "שבילהתtitans", "frictionalgames": "משחקיםמוזרים", "hexen": "<PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "הדברהרעשבתוכנו", "realrac": "ריאלטעפת", "thebackrooms": "החדרים_האחוריים", "backrooms": "חדרים<PERSON><PERSON><PERSON><PERSON>יים", "empiressmp": "אימפרייתסמפ", "blockstory": "סיפורחסום", "thequarry": "המחצבה", "tlou": "טלו", "dyinglight": "אורממות", "thewalkingdeadgame": "המשחקשלהולכיםמתים", "wehappyfew": "אנחנושמחים<PERSON><PERSON><PERSON>ה", "riseofempires": "העלייהשלהאימפריות", "stateofsurvivalgame": "מצבשלשרוד<PERSON><PERSON><PERSON><PERSON>", "vintagestory": "סיפורנוסטלגי", "arksurvival": "שריד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "ברוטר<PERSON>ו<PERSON>ה", "breathedge": "נשימה", "alisa": "אליסה", "westlendsurvival": "הישרדותלנדרנבוסט", "beastsofbermuda": "חיותבבירמדה", "frostpunk": "פראו<PERSON><PERSON><PERSON><PERSON>נק", "darkwood": "יערחשוך", "survivalhorror": "אימההישרדות", "residentevil": "רזיד<PERSON>טאיוו", "residentevil2": "רזידנטאיביל2", "residentevil4": "רזידנטאיביל4", "residentevil3": "רזידנטאביל3", "voidtrain": "רכבתחלל", "lifeaftergame": "חייםא<PERSON><PERSON><PERSON><PERSON>משחק", "survivalgames": "משחקיישרדות", "sillenthill": "שק<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "המלחמתיושלי", "scpfoundation": "סקיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>וננ", "greenproject": "פרויקט_הירוק", "kuon": "קו<PERSON>ן", "cryoffear": "בכימ<PERSON>חד", "raft": "ראפט", "rdo": "רדו", "greenhell": "גיה<PERSON>ו<PERSON><PERSON><PERSON><PERSON>ק", "residentevil5": "רזידנטאביל5", "deadpoly": "דד<PERSON>ו<PERSON>י", "residentevil8": "רזיד<PERSON><PERSON><PERSON>וויל8", "onironauta": "אוניר<PERSON><PERSON><PERSON><PERSON>ט", "granny": "סבתוש", "littlenightmares2": "סיוטיםקטנים2", "signalis": "סיגנליס", "amandatheadventurer": "אמאנדאתהלוחמת", "sonsoftheforest": "בניהיער", "rustvideogame": "גanganרוסט", "outlasttrials": "לשרוד<PERSON><PERSON><PERSON>יון", "alienisolation": "בידוד<PERSON>ייזרים", "undawn": "לאמאסוף", "7day2die": "7יוםלמות", "sunlesssea": "יםבליש<PERSON>ש", "sopravvivenza": "הישרדות", "propnight": "פרופנייט", "deadisland2": "אייהמתים2", "ikemensengoku": "יק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ו", "ikemenvampire": "איי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathverse": "מות_ביקום", "cataclysmdarkdays": "יום_אפוקליפסה", "soma": "סומה", "fearandhunger": "פחדורעב", "stalkercieńczarnobyla": "ציידיםצריםבולה", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "עידןכושך", "clocktower3": "שעןמגדל3", "aloneinthedark": "גםבח<PERSON>יכה", "medievaldynasty": "אימפריהמינימי", "projectnimbusgame": "פרויקטנימבוסגיים", "eternights": "אֵיתֵרְנַייטְס", "craftopia": "קרפטופיה", "theoutlasttrials": "הניסיונות<PERSON>החצינו", "bunker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worlddomination": "כיבושהעולם", "rocketleague": "רוקטליג", "tft": "תודהשובטחבו", "officioassassinorum": "רוצחישרד", "necron": "נ<PERSON><PERSON><PERSON><PERSON>", "wfrp": "wfрп", "dwarfslayer": "רוצחגמדים", "warhammer40kcrush": "בוש<PERSON>א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ש", "wh40": "wh40", "warhammer40klove": "אהבתוורהממר40k", "warhammer40klore": "מlore40kשלwarhammer", "warhammer": "ורה<PERSON><PERSON>ר", "warhammer30k": "וורהאמר30ק", "warhammer40k": "ווארהאמר40ק", "warhammer40kdarktide": "וורהאמר<PERSON>קדרקטייד", "totalwarhammer3": "טוטלווארהממר3", "temploculexus": "טמפלוקולקסוס", "vindicare": "להתעקש", "ilovesororitas": "אוהבאתהסורוריטות", "ilovevindicare": "אניאו<PERSON><PERSON><PERSON><PERSON>יין", "iloveassasinorum": "אניאוהבאת<PERSON>ססינים", "templovenenum": "תמלוונטדם", "templocallidus": "טמפלוקלידוס", "templomaerorus": "טמפלומ<PERSON><PERSON><PERSON><PERSON><PERSON>ס", "templovanus": "טמפלובנוס", "oficioasesinorum": "מקצועהרצחנים", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40אלף", "tetris": "טטריס", "lioden": "ליא<PERSON><PERSON>ן", "ageofempires": "עידןהאמפריות", "aoe2": "אואי2", "hoi4": "הוי4", "warhammerageofsigmar": "וורהא<PERSON>רעיד<PERSON><PERSON><PERSON>גמ<PERSON>ר", "civilizationv": "ציוויל<PERSON><PERSON><PERSON><PERSON><PERSON>v", "ittakestwo": "צריךשניים", "wingspan": "אורך_כנף", "terraformingmars": "מארסיזם", "heroesofmightandmagic": "גיבור<PERSON><PERSON><PERSON><PERSON>ומאגיה", "btd6": "ביטדי6", "supremecommander": "מפקדעליון", "ageofmythology": "עידןהמיתולוגיה", "args": "ארג<PERSON>", "rime": "ריים", "planetzoo": "גני_חיות", "outpost2": "אוּטפוֹסט2", "banished": "שולחיםהביתה", "caesar3": "קיסר3", "redalert": "אזהרהאדומה", "civilization6": "ציוויליזציה6", "warcraft2": "וורקראפט2", "commandandconquer": "לשלטולולdominateandconquer", "warcraft3": "וורקראפט3", "eternalwar": "מלחמה<PERSON>ש<PERSON><PERSON><PERSON>נשמה", "strategygames": "משחקיאסטרטגיה", "anno2070": "אננו2070", "civilizationgame": "משחקהציביליזציה", "civilization4": "ציוויליזציה4", "factorio": "פקטוריו", "dungeondraft": "דנגונד<PERSON><PERSON>ט", "spore": "סְפֹור", "totalwar": "מלחמתסכום", "travian": "טרביאן", "forts": "מבצעים", "goodcompany": "חב<PERSON><PERSON><PERSON>ו<PERSON>ה", "civ": "<PERSON>י<PERSON>", "homeworld": "עולםבית", "heidentum": "היידנטום", "aoe4": "אאו4", "hnefatafl": "חנ<PERSON><PERSON><PERSON><PERSON>ל", "fasterthanlight": "מהירמעו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "forthekings": "למלכים", "realtimestrategy": "אסטרטגיהלזמןאמיתי", "starctaft": "סטאר<PERSON><PERSON><PERSON><PERSON>ט", "sidmeierscivilization": "סידמאי<PERSON><PERSON><PERSON>ציוויליזציה", "kingdomtwocrowns": "ממלכהשנייםכתרים", "eu4": "אני4", "vainglory": "גאוותנות", "ww40k": "ww40k", "godhood": "אלוהות", "anno": "אנו", "battletech": "באט<PERSON><PERSON>ק", "malifaux": "מאליפוקס", "w40k": "ו40ק", "hattrick": "הטריקהשלישי", "davesfunalgebraclass": "שיעוראל<PERSON><PERSON><PERSON><PERSON>המגניבשלהדייב", "plagueinc": "מגי<PERSON><PERSON>_איןק", "theorycraft": "תאוריהקרא<PERSON>ט", "mesbg": "מזבג", "civilization3": "ציוויליזציה3", "4inarow": "4בעמודה", "crusaderkings3": "קרוסיידרקינגס3", "heroes3": "גיבורים3", "advancewars": "מלחמותקדמה", "ageofempires2": "עידןהקהלים2", "disciples2": "תלמידים2", "plantsvszombies": "צמחיםנגד<PERSON>ומבים", "giochidistrategia": "משחקים<PERSON>ס<PERSON>רטגיים", "stratejioyunları": "משחקימסדרים", "europauniversalis4": "אירופהאוניברסלית4", "warhammervermintide2": "וארהמָרְוֶרמִינְטַייד2", "ageofwonders": "עידןנפלאות", "dinosaurking": "דינו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "כיבושעולם", "heartsofiron4": "לבבותברזל4", "companyofheroes": "חבורתגיבורים", "battleforwesnoth": "מאבקלומחים", "aoe3": "אהו3", "forgeofempires": "כּוּרֵיֵמְפִּירוֹת", "warhammerkillteam": "ווארהמרקילטים", "goosegooseduck": "גוּסהוּסדָּוּק", "phobies": "פוביות", "phobiesgame": "פוביזגיים", "gamingclashroyale": "קרבגיימ<PERSON><PERSON><PERSON>בקלשרויאל", "adeptusmechanicus": "אד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ס", "outerplane": "חוץמישור", "turnbased": "מבוססתתורנות", "bomberman": "בו<PERSON><PERSON><PERSON><PERSON>ן", "ageofempires4": "עידןהאמפריות4", "civilization5": "ציוויליזציה5", "victoria2": "ויקטוריה2", "crusaderkings": "מאבק_מלכים", "cultris2": "קולטריס2", "spellcraft": "כישוף", "starwarsempireatwar": "מלחמתהכוכביםאימפריהבמלחמה", "pikmin4": "פיקמין4", "anno1800": "אננו1800", "estratégia": "אסטרטגיה", "popfulmail": "פופפולמייל", "shiningforce": "כוחהזוהר", "masterduel": "קרב<PERSON><PERSON><PERSON><PERSON>ר", "dysonsphereprogram": "דיסונס<PERSON><PERSON><PERSON>הפרויקט", "transporttycoon": "טייקוןת<PERSON>בורה", "unrailed": "לאמתעשית", "magicarena": "מגיקרנה", "wolvesville": "וולבסוויל", "ooblets": "אובלטים", "planescapetorment": "תכנןבר<PERSON><PERSON><PERSON>מעינויים", "uplandkingdoms": "ממלכותעליון", "galaxylife": "חייםב<PERSON><PERSON>קסיה", "wolvesvilleonline": "וולבסוויל<PERSON>ונליין", "slaythespire": "שחוטאת<PERSON><PERSON>גה", "battlecats": "קרבקטים", "sims3": "סימס3", "sims4": "סימס4", "thesims4": "הסימס4", "thesims": "הסימס", "simcity": "סימסיטי", "simcity2000": "סימסיטי2000", "sims2": "סימס2", "iracing": "אייר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "granturismo": "גרא<PERSON><PERSON><PERSON><PERSON>סמו", "needforspeed": "צריךמהירות", "needforspeedcarbon": "צריךבמהירותקארבון", "realracing3": "מירוציםאמיתיים3", "trackmania": "טראקמניה", "grandtourismo": "גרנד<PERSON>וריסמו", "gt7": "גגלית7", "simsfreeplay": "סימספריפליי", "ts4": "טס4", "thesims2": "הסימס2", "thesims3": "הסימס3", "thesims1": "הסימס1", "lossims4": "לוסימס4", "fnaf": "פנאף", "outlast": "תשארי_חיה", "deadbydaylight": "דדביידיילייט", "alicemadnessreturns": "אליסחו<PERSON><PERSON><PERSON><PERSON>שיגעון", "darkhorseanthology": "אנתולוגייתהסוסהכהה", "phasmophobia": "פאזמופוביה", "fivenightsatfreddys": "חמשלילו<PERSON>אצפר<PERSON>י", "saiko": "סייקו", "fatalframe": "מסגרתקטלנית", "littlenightmares": "סיוטיםקטנים", "deadrising": "העלייההמתה", "ladydimitrescu": "ליידידי<PERSON><PERSON><PERSON>רסקו", "homebound": "בּוֹבּוּמְפָנֶה", "deadisland": "אי_המתים", "litlemissfortune": "מיסמזללת", "projectzero": "פרויק<PERSON><PERSON><PERSON><PERSON><PERSON>לון", "horory": "הו<PERSON><PERSON>י", "jogosterror": "גוג<PERSON><PERSON><PERSON>ר<PERSON><PERSON>ר", "helloneighbor": "הל<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "האחשכן2", "gamingdbd": "גיי<PERSON><PERSON>נ<PERSON><PERSON><PERSON><PERSON>י", "thecatlady": "חתולונת", "jeuxhorreur": "משח<PERSON><PERSON>_אימה", "horrorgaming": "משח<PERSON><PERSON>_אימים", "magicthegathering": "מאגיק<PERSON><PERSON>ו<PERSON>צים", "mtg": "מעלים", "tcg": "טקג", "cardsagainsthumanity": "כרטיסיםנ<PERSON><PERSON><PERSON>נושיות", "cribbage": "קריבג", "minnesotamtg": "מינסוטהמטג", "edh": "אדח", "monte": "מונטה", "pinochle": "<PERSON>ינ<PERSON><PERSON>", "codenames": "שמותקודים", "dixit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bicyclecards": "קלפי_אופניים", "lor": "לור", "euchre": "י<PERSON><PERSON><PERSON>", "thegwent": "הגוונט", "legendofrunetera": "אגדתרונטרה", "solitaire": "סוליטייר", "poker": "<PERSON><PERSON><PERSON><PERSON>", "hearthstone": "הארת<PERSON>טון", "uno": "אונו", "schafkopf": "שחףkopf", "keyforge": "קיבורג", "cardtricks": "טריקים_בקלפים", "playingcards": "קלפימ<PERSON>ח<PERSON>", "marvelsnap": "מרוולסנאפ", "ginrummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "netrunner": "נטרא<PERSON>ר", "gwent": "גוונט", "metazoo": "מטאזו", "tradingcards": "כרטיסימ<PERSON><PERSON>ר", "pokemoncards": "קלפי<PERSON>ו<PERSON><PERSON>מון", "fleshandbloodtcg": "בשרודם<PERSON><PERSON><PERSON><PERSON><PERSON>ל<PERSON>ים", "sportscards": "כרטיסיספורט", "cardfightvanguard": "קרבי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד", "duellinks": "דוו<PERSON><PERSON>נ<PERSON>ס", "spades": "אספדים", "warcry": "קריאתמלחמה", "digimontcg": "דיגימונ<PERSON><PERSON>cg", "toukenranbu": "טו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ו", "kingofhearts": "מלךהלבבות", "truco": "אמיתי", "loteria": "לוטו", "hanafuda": "הנפודה", "theresistance": "ההתנגדות", "transformerstcg": "טרנספורמסרטcg", "doppelkopf": "דוֹפֶּלְקּוֹפְף", "yugiohcards": "קארד<PERSON>ים<PERSON>ו<PERSON><PERSON><PERSON>ש", "yugiohtcg": "יוגיוגהtcg", "yugiohduel": "יוגיהודוול", "yugiohocg": "יוגיוהקלפים", "dueldisk": "דוושלוח", "yugiohgame": "יוגיוג<PERSON>ימס", "darkmagician": "מכשףחשוך", "blueeyeswhitedragon": "דרקון<PERSON><PERSON><PERSON><PERSON><PERSON>יניכחולות", "yugiohgoat": "יוגיוהגודה", "briscas": "בריס<PERSON><PERSON>", "juegocartas": "משחקקלפים", "burraco": "בור<PERSON><PERSON>ו", "rummy": "רא<PERSON>י", "grawkarty": "גר<PERSON><PERSON><PERSON><PERSON>י", "dobble": "דו<PERSON>ל", "mtgcommander": "מא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cotorro": "קוטורו", "jeuxdecartes": "משחק<PERSON>כרטיסים", "mtgjudge": "שו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdecartas": "משחקיקל<PERSON>ים", "duelyst": "דו<PERSON><PERSON><PERSON><PERSON>ט", "mtgplanschase": "רוד<PERSON>ים<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ניותמאגיק", "mtgpreconcommander": "מגיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "kartenspiel": "משחקקלפים", "carteado": "קרטיאדו", "sueca": "סווקה", "beloteonline": "בלו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ין", "karcianki": "בּוּקָרְצִים", "battlespirits": "קרבימוחות", "battlespiritssaga": "סאג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "שואגתכרטיסים", "žolíky": "זוליקים", "facecard": "כרטיספנים", "cardfight": "קרדפייט", "biriba": "בירי<PERSON>ה", "deckbuilders": "בו<PERSON><PERSON>_דק", "marvelchampions": "גיבורים_במרוול", "magiccartas": "קסמותבובות", "yugiohmasterduel": "יוגיו<PERSON><PERSON><PERSON><PERSON>טרדואל", "shadowverse": "שדרו<PERSON><PERSON><PERSON>", "skipbo": "סקיפבו", "unstableunicorns": "חד<PERSON><PERSON><PERSON>ל<PERSON>הנחיות", "cyberse": "סיי<PERSON><PERSON><PERSON>י", "classicarcadegames": "משחקיםב<PERSON>ברית", "osu": "אוסו", "gitadora": "גיטאדורה", "dancegames": "מ<PERSON><PERSON><PERSON><PERSON>_ריקוד", "fridaynightfunkin": "שעתפ<PERSON>רטי", "fnf": "פנפ", "proseka": "פרוסקה", "projectmirai": "פרויקטמ<PERSON><PERSON><PERSON>י", "projectdiva": "פרויקטדיבה", "djmax": "די<PERSON><PERSON><PERSON><PERSON><PERSON>", "guitarhero": "גיבורהגיטרה", "clonehero": "קלון<PERSON><PERSON><PERSON><PERSON>ר", "justdance": "רקלדוד", "hatsunemiku": "הצצה_חוצה_הגבולות", "prosekai": "פרוסקאיי", "rocksmith": "רוקסמית", "idolish7": "אידוליש7", "rockthedead": "רוקדיםעםהמתים", "chunithm": "צוניטם", "idolmaster": "אייד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "dancecentral": "מרכ<PERSON><PERSON><PERSON>י<PERSON>ה", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "סטפמאניה", "highscorerythmgames": "משחקיק<PERSON><PERSON><PERSON><PERSON>קודותגבוהות", "pkxd": "פיק<PERSON>ד", "sidem": "סיידם", "ongeki": "אונ<PERSON><PERSON>י", "soundvoltex": "סאונדוו<PERSON><PERSON><PERSON>ס", "rhythmheaven": "גן<PERSON><PERSON><PERSON>ב", "hypmic": "<PERSON>י<PERSON><PERSON><PERSON><PERSON><PERSON>", "adanceoffireandice": "ריקו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>צתמאתחלש", "auditiononline": "אודישן<PERSON><PERSON><PERSON>נטרנט", "itgmania": "מלחמתיגמניה", "juegosderitmo": "בואולמוזיקה", "cryptofthenecrodancer": "קרי<PERSON><PERSON>ו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>נקומי", "rhythmdoctor": "רביעתדופק", "cubing": "קובינג", "wordle": "וורדל", "teniz": "<PERSON><PERSON><PERSON><PERSON>", "puzzlegames": "משחקיםמ<PERSON><PERSON><PERSON><PERSON>ים", "spotit": "תפוסאותו", "rummikub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockdoku": "ברוקדוקו", "logicpuzzles": "חיד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "sudoku": "סודוקו", "rubik": "רו<PERSON><PERSON><PERSON>", "brainteasers": "חידות", "rubikscube": "קוביההנचनיים", "crossword": "חידון_מילים", "motscroisés": "מו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ז", "krzyżówki": "חידות", "nonogram": "נונוגרם", "bookworm": "חובב<PERSON><PERSON>רים", "jigsawpuzzles": "פאזלים", "indovinello": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddle": "חידה", "riddles": "חידות", "rompecabezas": "פאזל", "tekateki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inside": "בפנים", "angrybirds": "ציפוריזעם", "escapesimulator": "בריח<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "פאזליםו<PERSON><PERSON><PERSON>ונים", "crosswordpuzzles": "חידות_לועזיות", "kurushi": "קורושִי", "gardenscapesgame": "gameגינות", "puzzlesport": "חידכבית", "escaperoomgames": "משחקיבריחתחדרים", "escapegame": "משחקב<PERSON>י<PERSON>ה", "3dpuzzle": "פאזל3d", "homescapesgame": "המשחקהבית", "wordsearch": "חיפושמילים", "enigmistica": "חידות", "kulaworld": "עולמללה", "myst": "<PERSON><PERSON><PERSON><PERSON>", "riddletales": "רידלטיילס", "fishdom": "פישדום", "theimpossiblequiz": "החידו<PERSON><PERSON><PERSON><PERSON>תיאפשרי", "candycrush": "סוכריותמכות", "littlebigplanet": "ליטלביג<PERSON>לנט", "match3puzzle": "פאזל3התאמה", "huniepop": "הוניפופ", "katamaridamacy": "קטמרידמייסי", "kwirky": "קו<PERSON><PERSON><PERSON><PERSON>י", "rubikcube": "קוביהההורית", "cuborubik": "קוביההונגרית", "yapboz": "יָפְבּוּז", "thetalosprinciple": "עקרוןטלוס", "homescapes": "מיזוגים", "puttputt": "פטפט", "qbert": "קווירט", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "tycoongames": "מש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "cubosderubik": "קוביותרוביק", "cruciverba": "קרו<PERSON>י", "ciphers": "צפנים", "rätselwörter": "מילתחידות", "buscaminas": "בו<PERSON><PERSON><PERSON>ינ<PERSON>ס", "puzzlesolving": "פתירתחידות", "turnipboy": "טורני<PERSON><PERSON>וי", "adivinanzashot": "חיד<PERSON><PERSON><PERSON>_אדיביננזה", "nobodies": "נכים", "guessing": "ניחושים", "nonograms": "נוןוגרמים", "kostkirubika": "קוסטקרוב<PERSON>קה", "crypticcrosswords": "חידותקריפטיות", "syberia2": "סיבריה2", "puzzlehunt": "מרדף_פאזלים", "puzzlehunts": "חידותמרדף", "catcrime": "פשעי_חתולים", "quebracabeça": "שוב<PERSON><PERSON><PERSON>ש", "hlavolamy": "הבעיותשלהבוא", "poptropica": "פופטרופיקה", "thelastcampfire": "המדורההא<PERSON><PERSON>ונה", "autodefinidos": "אוטודיפינידים", "picopark": "פיק<PERSON><PERSON><PERSON><PERSON><PERSON>", "wandersong": "שירנודד", "carto": "קרטו", "untitledgoosegame": "משחקה<PERSON>רווזהלאמותג", "cassetête": "קאסטטה", "limbo": "לימבו", "rubiks": "רוביקס", "maze": "מבוך", "tinykin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "קוביותרובי", "speedcube": "ס<PERSON>י<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "חלקים", "portalgame": "משח<PERSON><PERSON>ורטל", "bilmece": "בלמיץ", "puzzelen": "פאזלים", "picross": "פיקרוס", "rubixcube": "קוביהרובה", "indovinelli": "חידות", "cubomagico": "קובו<PERSON><PERSON><PERSON>י", "mlbb": "מיליא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ש", "pubgm": "פאבגאם", "codmobile": "קוד<PERSON>וביל", "codm": "קודם", "twistedwonderland": "עידןעקום", "monopoly": "מונופול", "futurefight": "קרב_של_העתיד", "mobilelegends": "אגדותנייד", "brawlstars": "בrawlstars", "brawlstar": "בר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "coc": "קוק", "lonewolf": "זאבבודד", "gacha": "גאצה", "wr": "ויר", "fgo": "פגו", "bitlife": "ביטלייף", "pikminbloom": "פיק<PERSON>יןבלום", "ff": "פפ", "ensemblestars": "כו<PERSON><PERSON><PERSON>_הלהקה", "asphalt9": "אספלט9", "mlb": "בייסבול", "cookierunkingdom": "ממלכתעוגיות", "alchemystars": "כוכביאלכימיה", "stateofsurvival": "מצבהישרדות", "mycity": "העירשלי", "arknights": "ארקנייטס", "colorfulstage": "במהצבעונית", "bloonstowerdefense": "הגנהעלבלונים", "btd": "ביטד", "clashroyale": "קלשרויאל", "angela": "אנגלה", "dokkanbattle": "ד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "fategrandorder": "פיית<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "hyperfront": "היי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ט", "knightrun": "אבירמרוץ", "fireemblemheroes": "אשמגנים<PERSON><PERSON>בורים", "honkaiimpact": "הונקאיימפ<PERSON>ט", "soccerbattle": "קרבכ<PERSON>ורגל", "a3": "איי3", "phonegames": "מש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ון", "kingschoice": "בחירתהמלך", "guardiantales": "שומרי_סיפורים", "petrolhead": "מכו<PERSON>לתדלק", "tacticool": "<PERSON><PERSON><PERSON><PERSON>קול", "cookierun": "מרוץעוגיות", "pixeldungeon": "מכלאת<PERSON>יקסלים", "arcaea": "ארק<PERSON>ה", "outoftheloop": "מחוץללוופ", "craftsman": "בוסבידוד", "supersus": "סופרסס", "slowdrive": "נסיעהאיטית", "headsup": "הִסתָכֵּללְמַעלָה", "wordfeud": "וורדפוד", "bedwars": "מלחמתמזרנים", "freefire": "פריפי<PERSON>ר", "mobilegaming": "מש<PERSON><PERSON><PERSON>_נייד", "lilysgarden": "גינתהלילה", "farmville2": "פארמביל2", "animalcrossing": "חיו<PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "ביג<PERSON>י", "teamfighttactics": "טקטיקותקרבצוות", "clashofclans": "קרבשלבпридерживается", "pjsekai": "pjsekai", "mysticmessenger": "שליחה<PERSON>סתורין", "callofdutymobile": "קריאתשלdutymobile", "thearcana": "הארק<PERSON>ה", "8ballpool": "8בולפול", "emergencyhq": "חדר_חירום", "enstars": "אנסט<PERSON>רס", "randonautica": "רנדונאו<PERSON><PERSON><PERSON>ה", "maplestory": "מיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "albion": "אלביון", "hayday": "היידי", "onmyoji": "אונימוגי", "azurlane": "אזולליין", "shakesandfidget": "שייק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ט", "ml": "מיל", "bangdream": "באנגדרים", "clashofclan": "קרבשלשכות", "starstableonline": "סטארסטיי<PERSON><PERSON><PERSON>ונליין", "dragonraja": "דרגו<PERSON><PERSON><PERSON><PERSON>ה", "timeprincess": "נסיכת_הזמן", "beatstar": "בי<PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "אגדת<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "hanabi": "מבעדל<PERSON><PERSON>בּו", "disneymirrorverse": "מרא<PERSON><PERSON><PERSON><PERSON>סני", "pocketlove": "אהבהבכיס", "androidgames": "משחק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד", "criminalcase": "מקרהפלילי", "summonerswar": "מלחמתמאגרים", "cookingmadness": "שיגעו<PERSON><PERSON><PERSON>שול", "dokkan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aov": "אוב", "triviacrack": "משחקידיעות", "leagueofangels": "ליגתהמלאכים", "lordsmobile": "לור<PERSON><PERSON>מוביל", "tinybirdgarden": "גןהציפורונים", "gachalife": "גאצלייף", "neuralcloud": "ענןעצבי", "mysingingmonsters": "מפחידשיריםשלי", "nekoatsume": "נקהאצומה", "bluearchive": "אר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "raidshadowlegends": "רדשדווליגנדס", "warrobots": "מלחמתרובוטים", "mirrorverse": "מַשֶׁקֶףֹ֙", "pou": "פאו", "warwings": "כנפי_מלחמה", "fifamobile": "פי<PERSON><PERSON><PERSON>וביל", "mobalegendbangbang": "מובלא<PERSON><PERSON><PERSON><PERSON><PERSON>גבנג", "evertale": "אברטייל", "futime": "זמן<PERSON>וט", "antiyoy": "אנטייווי", "apexlegendmobile": "אפ<PERSON><PERSON>לגנדסנייד", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "נלחמיםעדהסוף", "mpl": "אמפל", "coinmaster": "מאסטרהמטבעות", "punishinggrayraven": "ענששחרחר", "petpals": "ח<PERSON><PERSON>יםל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "gameofsultans": "משחקזלוכים", "arenabreakout": "אarenabreakout", "wolfy": "ווֹלְפִי", "runcitygame": "רנגעפ<PERSON>רי", "juegodemovil": "משח<PERSON><PERSON>ל<PERSON>ון", "avakinlife": "אבאקיןלייף", "kogama": "קוגמה", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "בלא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ביל", "rollercoastertycoon": "רכבת<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "grandchase": "גראנדצייס", "bombmebrasil": "בומב<PERSON><PERSON><PERSON>ברזיל", "ldoe": "לדו", "legendonline": "אינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ב", "otomegame": "אוטומגיים", "mindustry": "מיי<PERSON><PERSON><PERSON><PERSON><PERSON>י", "callofdragons": "קריאתה<PERSON><PERSON>קונים", "shiningnikki": "שִׁנִּיןְנִיקִי", "carxdriftracing2": "קרxדריפטראסינג2", "pathtonowhere": "מעברלשום<PERSON><PERSON>ום", "sealm": "שלם", "shadowfight3": "שדוטפייט3", "limbuscompany": "לימבוסקום", "demolitionderby3": "דמולישןדרבי3", "wordswithfriends2": "מיליםעםחברים2", "soulknight": "נשמתה<PERSON>ו<PERSON>ן", "purrfecttale": "סיפורמעולה", "showbyrock": "שוא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "ליידי<PERSON><PERSON><PERSON>ו<PERSON>ר", "lolmobile": "לואלבייל", "harvesttown": "עיר_הקציר", "perfectworldmobile": "פלאניידהמושלם", "empiresandpuzzles": "אימפריות<PERSON><PERSON><PERSON><PERSON><PERSON>s", "empirespuzzles": "פאזליםבעלי_האמיצים", "dragoncity": "עירדרקונים", "garticphone": "ג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "battlegroundmobileind": "באטל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>וביילהודו", "fanny": "פא<PERSON>י", "littlenightmare": "חלום<PERSON>טן", "aethergazer": "אֵיתֵרְגֵיזֶר", "mudrunner": "רוכבבוץ", "tearsofthemis": "דמעותהמיס", "eversoul": "אברסול", "gunbound": "גנבוּנד", "gamingmlbb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dbdmobile": "דיבידימוביל", "arknight": "ארקנייט", "pristontale": "פריסטונטייל", "zombiecastaways": "זומביםבבריחה", "eveechoes": "הדונשקרובים", "jogocelular": "גוגוצלולר", "mariokarttour": "מריו_קט_טור", "zooba": "זובה", "mobilelegendbangbang": "מוביילל<PERSON><PERSON><PERSON>בנגבנג", "gachaclub": "גא<PERSON>קלוב", "v4": "ו4", "cookingmama": "אמאשףבישול", "cabalmobile": "קב<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "streetfighterduel": "קרב_רחוב", "lesecretdhenri": "הלסודשלהנרי", "gamingbgmi": "גיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "girlsfrontline": "קְצוּרֵי_בַּקוֹר", "jurassicworldalive": "עולםהדינוז<PERSON>וריםחי", "soulseeker": "מח<PERSON><PERSON><PERSON><PERSON>מה", "gettingoverit": "מתגבריםעלזה", "openttd": "אופןטידי", "onepiecebountyrush": "אחד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ש", "moonchaistory": "מהקג<PERSON><PERSON>ילן", "carxdriftracingonline": "קרקסדריפרייסינגליינה", "jogosmobile": "גיימינ<PERSON><PERSON>למובייל", "legendofneverland": "אגדותנברלנד", "pubglite": "פאבגלאיט", "gamemobilelegends": "משחקטלפוניםאגדות", "timeraiders": "<PERSON><PERSON><PERSON><PERSON>_ז<PERSON>ן", "gamingmobile": "גיי<PERSON><PERSON><PERSON><PERSON><PERSON>ובייל", "marvelstrikeforce": "כו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "thebattlecats": "הקרבחתולים", "dnd": "<PERSON><PERSON><PERSON>", "quest": "משימה", "giochidiruolo": "משחק<PERSON>_תפקידים", "dnd5e": "dnd5e", "rpgdemesa": "רפגדמסה", "worldofdarkness": "עולםה<PERSON><PERSON>כה", "travellerttrpg": "מטייליttrpg", "2300ad": "שנת2300", "larp": "לאר<PERSON>", "romanceclub": "מועדו<PERSON><PERSON><PERSON><PERSON>n", "d20": "ד20", "pokemongames": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ון", "pokemonmysterydungeon": "חדרהל<PERSON><PERSON>ד<PERSON><PERSON><PERSON>לפוקימון", "pokemonlegendsarceus": "פוקימון<PERSON>גדותארסيوس", "pokemoncrystal": "פוקימו<PERSON><PERSON><PERSON><PERSON>סטל", "pokemonanime": "פוקימו<PERSON><PERSON><PERSON>ימה", "pokémongo": "פוקימוןגו", "pokemonred": "פוקימון<PERSON><PERSON><PERSON>ם", "pokemongo": "פו<PERSON><PERSON><PERSON>ון", "pokemonshowdown": "שוא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>מון", "pokemonranger": "פיקא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "ליפיפ", "porygon": "פוריגון", "pokemonunite": "פוקימו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ט", "entai": "הנט<PERSON>י", "hypno": "היפנוט", "empoleon": "אמפוליאון", "arceus": "ארקיא<PERSON>ס", "mewtwo": "מיווטו", "paldea": "פלדאה", "pokemonscarlet": "פוקימון<PERSON><PERSON><PERSON><PERSON>ט", "chatot": "שיחת<PERSON>ק", "pikachu": "פיקאצו", "roxie": "רוקסיה", "pokemonviolet": "פוֹקֵימוֹןְווִיּוֹלֶט", "pokemonpurpura": "פוקימו<PERSON><PERSON>גול", "ashketchum": "אשקצאם", "gengar": "<PERSON><PERSON><PERSON><PERSON>", "natu": "נטו", "teamrocket": "צוותרוקט", "furret": "פוּרֶט", "magikarp": "מא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mimikyu": "מימיקיו", "snorlax": "סנורליקס", "pocketmonsters": "מפלצות<PERSON>כיס", "nuzlocke": "נוזלוק", "pokemonplush": "פוקימו<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "טי<PERSON><PERSON><PERSON><PERSON>יק", "pokeball": "פוקבול", "charmander": "צאר<PERSON><PERSON><PERSON>ר", "pokemonromhack": "פוקימו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "pubgmobile": "פאב<PERSON><PERSON>מוביל", "litten": "חתולה", "shinypokemon": "פּוֹקֵימוֹןשָׁרוּת", "mesprit": "מֵסְפְרִיט", "pokémoni": "פוקימונים", "ironhands": "ידייםברזליות", "kabutops": "קבוטופס", "psyduck": "פסידק", "umbreon": "או<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "pokevore": "פוּקֵבוֹר", "ptcg": "פטצג", "piplup": "פיפלאפ", "pokemonsleep": "שנת<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "heyyoupikachu": "היימים<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "מאס<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>מון", "pokémonsleep": "פוק<PERSON>וס<PERSON><PERSON>ון", "kidsandpokemon": "ילדיםו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "pokemonsnap": "פוקימו<PERSON><PERSON><PERSON><PERSON><PERSON>", "bulbasaur": "בולב<PERSON><PERSON><PERSON>ר", "lucario": "לוק<PERSON>ריו", "charizar": "צרי<PERSON><PERSON>", "shinyhunter": "ציידיםמנצנצים", "ajedrez": "שח<PERSON><PERSON>", "catur": "חתול", "xadrez": "שח<PERSON><PERSON>", "scacchi": "שח<PERSON><PERSON>", "schaken": "שקנים", "skak": "סקק", "ajedres": "שח<PERSON><PERSON>", "chessgirls": "שחמטיות", "magnuscarlsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldblitz": "עולםבלאיצ", "jeudéchecs": "שחק<PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "שחמ<PERSON><PERSON><PERSON>ן", "chinesechess": "שחמט<PERSON>יני", "chesscanada": "שחמטקנדה", "fide": "פידי", "xadrezverbal": "שח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "openings": "הזדמנויות", "rook": "רוק", "chesscom": "שחמטקום", "calabozosydragones": "קלבוזוסיםוד<PERSON>גונים", "dungeonsanddragon": "מאב<PERSON>יםובחינות", "dungeonmaster": "מאסטרשלד<PERSON>נגונים", "tiamat": "תיא<PERSON><PERSON>", "donjonsetdragons": "דוֹנְגוֹןוּהַדרָקונים", "oxventure": "אוק<PERSON>וונ<PERSON>ר", "darksun": "שמשא<PERSON>לה", "thelegendofvoxmachina": "האגדהשלבוקסמכינה", "doungenoanddragons": "דונ<PERSON><PERSON>ו<PERSON>טודנרגלים", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "אליפותמיינקראפט", "minecrafthive": "מיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ב", "minecraftbedrock": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamsmp": "חלומותסמפ", "hermitcraft": "הרמ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftjava": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON>גאווה", "hypixelskyblock": "היי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ק", "minetest": "מיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypixel": "היי<PERSON><PERSON><PERSON><PERSON>ל", "karmaland5": "קרמלנד5", "minecraftmods": "מדריך_מציאות", "mcc": "מכנסיים", "candleflame": "להבתנריה", "fru": "פרו", "addons": "תוספות", "mcpeaddons": "תוספותמיניקרפט", "skyblock": "סקיי<PERSON><PERSON><PERSON>ק", "minecraftpocket": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ובייל", "minecraft360": "מיינקראפט360", "moddedminecraft": "מיינ<PERSON><PERSON><PERSON><PERSON>טמותאם", "minecraftps4": "מיינקרפטפס4", "minecraftpc": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "betweenlands": "ביןה<PERSON><PERSON>צות", "minecraftdungeons": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>נגונס", "minecraftcity": "עירמיינ<PERSON>רא<PERSON>ט", "pcgamer": "גיימרpc", "jeuxvideo": "משח<PERSON><PERSON><PERSON>יד<PERSON>ו", "gambit": "גמ<PERSON>יט", "gamers": "גיימרים", "levelup": "שדרגו", "gamermobile": "גיי<PERSON><PERSON><PERSON><PERSON><PERSON>יל", "gameover": "סוףהמשחק", "gg": "גג", "pcgaming": "גיימ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunoynamak": "בו<PERSON>_ונשחק", "pcgames": "משחקיpc", "casualgaming": "גיימינגבכיף", "gamingsetup": "סטאפמ<PERSON><PERSON><PERSON><PERSON>ם", "pcmasterrace": "מא<PERSON><PERSON>ר<PERSON><PERSON><PERSON>ר", "pcgame": "משחקיpc", "gamerboy": "גיי<PERSON><PERSON><PERSON><PERSON>י", "vrgaming": "גיימינ<PERSON><PERSON><PERSON>ציאותוירטואלית", "drdisrespect": "דרדי<PERSON><PERSON><PERSON><PERSON><PERSON>", "4kgaming": "4ק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameplays": "גיימ<PERSON><PERSON>ייז", "consoleplayer": "שחק<PERSON><PERSON>ונסולה", "boxi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pro": "פרו", "epicgamers": "גיימרים_אפיים", "onlinegaming": "משחקיםב<PERSON><PERSON>נטרנט", "semigamer": "סמי<PERSON><PERSON><PERSON><PERSON>ר", "gamergirls": "גיימריות", "gamermoms": "אמהות<PERSON><PERSON><PERSON>מינג", "gamerguy": "גיי<PERSON><PERSON><PERSON><PERSON>י", "gamewatcher": "צופהבמ<PERSON><PERSON><PERSON><PERSON>ם", "gameur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grypc": "גר<PERSON><PERSON><PERSON>", "rangugamer": "רנו<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerschicas": "גיימרסציקות", "otoge": "אוֹטוֹגֵי", "dedsafio": "דד<PERSON><PERSON><PERSON>ו", "teamtryhard": "צוותמנסיםקשות", "mallugaming": "מאלו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ג", "pawgers": "פאוגרס", "quests": "משימות", "alax": "אללאקס", "avgn": "אוונגון", "oldgamer": "גי<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "cozygaming": "גיימינגמנחם", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "ד<PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "משחקיםתחרותיים", "minecraftnewjersey": "מיינ<PERSON><PERSON><PERSON><PERSON><PERSON>ניוגרזי", "faker": "מזויף", "pc4gamers": "מחשבנים4גיימרים", "gamingff": "גיימינגף", "yatoro": "יאטורו", "heterosexualgaming": "גיימרים<PERSON><PERSON><PERSON><PERSON>סקסואלים", "gamepc": "גיי<PERSON><PERSON><PERSON><PERSON>י", "girlsgamer": "בנות<PERSON><PERSON><PERSON><PERSON>ר", "fnfmods": "מודסףנף", "dailyquest": "משימתיום", "gamegirl": "בִּינָהשָׁלבָּזוֹהַמַּשְׂחָקָה", "chicasgamer": "בנות<PERSON><PERSON><PERSON><PERSON>ר", "gamesetup": "הכנותמשחק", "overpowered": "מוגזם", "socialgamer": "גיימ<PERSON><PERSON>נתי", "gamejam": "גיימגאם", "proplayer": "פרופלייר", "roleplayer": "שחקן<PERSON><PERSON><PERSON>ידים", "myteam": "הקבוצהשלי", "republicofgamers": "רפובליקתהגיימרים", "aorus": "האורס", "cougargaming": "משחקימילפיות", "triplelegend": "שלקושל<PERSON><PERSON>ן", "gamerbuddies": "חברי<PERSON><PERSON>ימ<PERSON>ים", "butuhcewekgamers": "צריךיותגיימרים", "christiangamer": "גיימר_נוצרי", "gamernerd": "גי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "גיימר<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "afk": "לאבא", "andregamer": "אדרי<PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgamer": "גיימרלארוף", "89squad": "צוות89", "inicaramainnyagimana": "התחלתםאת<PERSON>חייםשלכם", "insec": "פגיע", "gemers": "גמירים", "oyunizlemek": "בו<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "שֵׁםגָּבָשׁ", "lanparty": "לנפארטי", "videogamer": "גייז<PERSON><PERSON>יד", "wspólnegranie": "משחקיםביחד", "mortdog": "מורטלוג", "playstationgamer": "גיימר<PERSON>לייסטיישן", "justinwong": "גסטיןוונג", "healthygamer": "גיימר<PERSON><PERSON>יא", "gtracing": "גיטריי<PERSON><PERSON>נג", "notebookgamer": "גיימר<PERSON><PERSON><PERSON><PERSON>ת", "protogen": "פרוטוגן", "womangamer": "נשיםשחקניות", "obviouslyimagamer": "ברור<PERSON><PERSON><PERSON><PERSON>ג<PERSON><PERSON><PERSON>ר", "mario": "מריו", "papermario": "פיי<PERSON>רמריו", "mariogolf": "מריוגולף", "samusaran": "סמ<PERSON><PERSON>ן", "forager": "איסוף", "humanfallflat": "הומנפולפלט", "supernintendo": "סופרנינטנדו", "nintendo64": "נינטנדו64", "zeroescape": "אפסב<PERSON><PERSON><PERSON>ה", "waluigi": "ואלואיגי", "nintendoswitch": "נינטנדוסוויץ", "nintendosw": "נינטנדוסו", "nintendomusic": "מוזיק<PERSON><PERSON>לנינטנדו", "sonicthehedgehog": "סוניקהחז<PERSON><PERSON><PERSON><PERSON><PERSON>ר", "sonic": "סוניק", "fallguys": "גבריםעונים", "switch": "מחליפים", "zelda": "זלדה", "smashbros": "סמאש<PERSON><PERSON><PERSON>ס", "legendofzelda": "אגדתזלדה", "splatoon": "ספלטון", "metroid": "מטرويد", "pikmin": "פיקמינים", "ringfit": "רינ<PERSON><PERSON>יט", "amiibo": "א<PERSON><PERSON><PERSON><PERSON>", "megaman": "<PERSON><PERSON><PERSON><PERSON>", "majorasmask": "מאז<PERSON><PERSON><PERSON>ק", "mariokartmaster": "מאסטרמ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ט", "wii": "ווּי", "aceattorney": "עורךדיןעליונה", "ssbm": "ססבם", "skychildrenofthelight": "ילדיהשמייםשלאור", "tomodachilife": "חייםשלחב<PERSON>ה", "ahatintime": "אחתי_בת_ז<PERSON>ן", "tearsofthekingdom": "דמעותהממלכה", "walkingsimulators": "משחקיה<PERSON><PERSON><PERSON>ה", "nintendogames": "משחק<PERSON>יננידו", "thelegendofzelda": "האגדהשלזלדה", "dragonquest": "חיפוש<PERSON><PERSON><PERSON>ונים", "harvestmoon": "יר<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "מריו_ברוס", "runefactory": "רונת<PERSON><PERSON><PERSON><PERSON>רי", "banjokazooie": "בא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "celeste": "שמים", "breathofthewild": "נשימהשלהפרא", "myfriendpedro": "ח<PERSON><PERSON><PERSON>ליפדרו", "legendsofzelda": "אגדותזלדה", "donkeykong": "ד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokart": "מאקו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "kirby": "קיר<PERSON>י", "51games": "51משחקים", "earthbound": "קשור_לאדמה", "tales": "סיפורים", "raymanlegends": "ראימן<PERSON><PERSON>מנויות", "luigismansion": "מטענילואי", "animalcrosssing": "מעברבעולםשלחיות", "taikonotatsujin": "טייקונו<PERSON><PERSON><PERSON>טרה", "nintendo3ds": "נינטנדו3ds", "supermariobros": "סופרמריו<PERSON><PERSON>וס", "mariomaker2": "מאירומייקר2", "boktai": "בו<PERSON><PERSON><PERSON>י", "smashultimate": "סמאשאולימייט", "nintendochile": "נינטנדואילה", "tloz": "לזת", "trianglestrategy": "אסטרטגייתמשולשים", "supermariomaker": "סופרמ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles3": "זנובלייד<PERSON>רוניקלס3", "supermario64": "סופרמריו64", "conkersbadfurday": "בוציםלאכפתלך", "nintendos": "נינטנדו", "new3ds": "נאו3דיאס", "donkeykongcountry2": "דונ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>קאנטרי2", "hyrulewarriors": "ממלכתהמלחמה", "mariopartysuperstars": "מאריופרטיסופרסטרס", "marioandsonic": "מריוו<PERSON><PERSON><PERSON><PERSON>ניק", "banjotooie": "בנגוטוּי", "nintendogs": "נינטנדוגס", "thezelda": "הזילדה", "palia": "פלאיה", "marioandluigi": "מאיר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "mariorpg": "מאריו<PERSON><PERSON><PERSON><PERSON>", "zeldabotw": "זלדהבוטוו", "yuumimain": "יומי<PERSON>ין", "wildrift": "ווילדריפט", "riven": "<PERSON><PERSON><PERSON><PERSON>", "ahri": "אהרי", "illaoi": "אילו<PERSON>י", "aram": "ערם", "cblol": "קבלול", "leagueoflegendslas": "ליגתהאגדותלאס", "urgot": "אורגוט", "zyra": "זי<PERSON>ה", "redcanids": "כלביםאדומים", "vanillalol": "וָנִילוֹלוֹל", "wildriftph": "ווֹלדרי<PERSON><PERSON><PERSON><PERSON>אצ", "lolph": "lol<PERSON>", "leagueoflegend": "ליגתהאגדים", "tốcchiến": "תוק<PERSON>ין", "gragas": "גרגאס", "leagueoflegendswild": "הליגה<PERSON>גדותפראי", "adcarry": "עושה_פרסומות", "lolzinho": "לוצקו", "leagueoflegendsespaña": "ליגהתאגידיםבישראל", "aatrox": "אטור<PERSON>ס", "euw": "אוי", "leagueoflegendseuw": "ליגה<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "קאיל", "samira": "סמירא", "akali": "אקלית", "lunari": "לו<PERSON><PERSON>י", "fnatic": "פנאטיק", "lollcs": "לוליונטש", "akshan": "<PERSON><PERSON><PERSON><PERSON>", "milio": "מיליו", "shaco": "שאק<PERSON>ו", "ligadaslegendas": "ליגדותהאגדות", "gaminglol": "גיי<PERSON><PERSON><PERSON>גלול", "nasus": "נסוס", "teemo": "טימו", "zedmain": "זלד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "hexgates": "שערי_הקסם", "hextech": "הק<PERSON><PERSON><PERSON>", "fortnitegame": "משחק<PERSON>ורטנייט", "gamingfortnite": "גיימינ<PERSON><PERSON>ורטנייט", "fortnitebr": "פורט<PERSON>י<PERSON><PERSON><PERSON><PERSON>שראל", "retrovideogames": "משחקיו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>סטלגיים", "scaryvideogames": "משחקיםמ<PERSON>חידים", "videogamemaker": "יוצרימ<PERSON><PERSON><PERSON><PERSON>וידיאו", "megamanzero": "מגהא<PERSON>ס", "videogame": "משחק<PERSON>ידאו", "videosgame": "משח<PERSON>יםבוידיאו", "professorlayton": "פרופסו<PERSON><PERSON><PERSON><PERSON><PERSON>ון", "overwatch": "מאב<PERSON>", "ow2": "או2", "overwatch2": "אוברוואטש2", "wizard101": "וויזארד101", "battleblocktheater": "באטלבלוקתיאטרון", "arcades": "ארק<PERSON><PERSON>", "acnh": "אקנה", "puffpals": "חבריפוף", "farmingsimulator": "סימולטור<PERSON>קלאות", "robloxchile": "רובלוקסהצילה", "roblox": "רובלוקס", "robloxdeutschland": "robloxגרמניה", "robloxdeutsch": "רובלוקסדויטש", "erlc": "ארל<PERSON>", "sanboxgames": "סנפוקסגיימס", "videogamelore": "המון<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "rollerdrome": "רולרדרום", "parasiteeve": "פרזי<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamecube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcraft2": "סטארקרפט2", "duskwood": "ד<PERSON><PERSON><PERSON><PERSON>ד", "dreamscape": "חלוםנוף", "starcitizen": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderesimulator": "ימsimulator", "grandtheftauto": "גניבת_רכב", "deadspace": "דד<PERSON><PERSON><PERSON>", "amordoce": "אהבהמתוקה", "videogiochi": "משח<PERSON><PERSON><PERSON>יד<PERSON>ו", "theoldrepublic": "הגל<PERSON>ימ<PERSON>ן", "videospiele": "משח<PERSON><PERSON><PERSON>יד<PERSON>ו", "touhouproject": "טוהו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ט", "dreamcast": "חלוםקסט", "adventuregames": "משחקימסעות", "wolfenstein": "ואל<PERSON>נשטין", "actionadventure": "הרפתקאותפעולה", "storyofseasons": "סיפורהשעונות", "retrogames": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "רטרואקדיה", "vintagecomputing": "מחשביםישנים", "retrogaming": "גיימינ<PERSON><PERSON><PERSON>רו", "vintagegaming": "משחקיםישנים", "playdate": "דייט<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "commanderkeen": "קו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "bugsnax": "בא<PERSON><PERSON><PERSON><PERSON>", "injustice2": "אִיצֶדֶק2", "shadowthehedgehog": "שָׁדוֹוְהַכְּשָׁפָה", "rayman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skygame": "שחקןשמיים", "zenlife": "חייםבזרימה", "beatmaniaiidx": "ביטמאניהiidx", "steep": "עולה", "mystgames": "המפתחותשלי", "blockchaingaming": "משחקימבוסתן", "medievil": "מֵדִיוּוּל", "consolegaming": "משח<PERSON><PERSON>_קונסולה", "konsolen": "קונסולות", "outrun": "לברוח", "bloomingpanic": "חרדהפורחת", "tobyfox": "טוביפוx", "hoyoverse": "הוֹיוֹוֵרס", "senrankagura": "סנרנקגורה", "gaminghorror": "אימה<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>נג", "monstergirlquest": "מסע_הבנות_המפלצות", "supergiant": "סופרג<PERSON>ינט", "disneydreamlightvalle": "דיסני<PERSON><PERSON>ו<PERSON><PERSON><PERSON>בא<PERSON>ר", "farmingsims": "חקלאותסימס", "juegosviejos": "משחקיםישנים", "bethesda": "בתסדה", "jackboxgames": "גיימ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ס", "interactivefiction": "סיפורתאינטראקטיבית", "pso2ngs": "פסקודואיngs", "grimfandango": "גרימ<PERSON><PERSON>דנגו", "thelastofus2": "האחרוןמנינו2", "amantesamentes": "אמאמנטס", "visualnovel": "רומן<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "visualnovels": "נובלותוויזואליות", "rgg": "רגע", "shadowolf": "צללית<PERSON><PERSON>ב", "tcrghost": "רוח_הטק_שלי", "payday": "תאריך_שכר", "chatherine": "קייט<PERSON>ין", "twilightprincess": "נסיכתדמדומים", "jakandaxter": "ג<PERSON><PERSON><PERSON><PERSON>סטר", "sandbox": "חושךבמסsandbox", "aestheticgames": "משחקיםאסתטיים", "novelavisual": "בו<PERSON><PERSON><PERSON><PERSON><PERSON>זואל", "thecrew2": "הקאבלים2", "alexkidd": "אלכ<PERSON>קיד", "retrogame": "משחקים<PERSON><PERSON><PERSON>יס", "tonyhawkproskater": "טוני<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>יטר", "smbz": "סמבהז", "lamento": "למנטו", "godhand": "ידאללה", "leafblowerrevolution": "מהפכתמניפיולים", "wiiu": "וייו", "leveldesign": "עיצוברמות", "starrail": "מסלולכו<PERSON><PERSON>ים", "keyblade": "מפתחחסד", "aplaguetale": "אפלגותייל", "fnafsometimes": "פנספונעמים", "novelasvisuales": "סרטיםויזואליים", "robloxbrasil": "רובלו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "pacman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameretro": "גיי<PERSON><PERSON><PERSON>רו", "videojuejos": "משחקונים", "videogamedates": "דייטים<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "mycandylove": "אהבתסוכרשלי", "megaten": "מגתן", "mortalkombat11": "מortalקומבט11", "everskies": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justcause3": "justcause3", "hulkgames": "משחק<PERSON><PERSON><PERSON><PERSON>ק", "batmangames": "באט<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "returnofreckoning": "החזרהשלחישובים", "gamstergaming": "גיימינ<PERSON><PERSON><PERSON><PERSON><PERSON>ים", "dayofthetantacle": "יוםהא<PERSON>יסים", "maniacmansion": "מני<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "crashracing": "מרוץצונאמי", "3dplatformers": "פלטפורמות3d", "nfsmw": "נפשלימרוץ", "kimigashine": "כמי<PERSON><PERSON><PERSON>ן", "oldschoolgaming": "משח<PERSON>ים<PERSON><PERSON>ן", "hellblade": "גיה<PERSON>ו<PERSON><PERSON><PERSON>ב", "storygames": "משח<PERSON><PERSON>_סיפור", "bioware": "בי<PERSON><PERSON><PERSON>", "residentevil6": "רזיד<PERSON><PERSON><PERSON>יביל6", "soundodger": "סאונד<PERSON><PERSON><PERSON>ר", "beyondtwosouls": "מעברלשנינשמות", "gameuse": "<PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "מייליםשל<PERSON><PERSON><PERSON><PERSON>מות", "tinybunny": "ארנבו<PERSON><PERSON><PERSON>ן", "retroarch": "רטרוארך", "powerup": "תחזק", "katanazero": "קאטנאזירו", "famicom": "פאמיקום", "aventurasgraficas": "הרפתקאותגרפיות", "quickflash": "שמ<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "פיזרו", "gachagaming": "גאצ<PERSON><PERSON><PERSON><PERSON><PERSON>נג", "retroarcades": "רטרוארקדיות", "f123": "f123", "wasteland": "ערבתחרבות", "powerwashsim": "מקצועןבשטיפה", "coralisland": "אי<PERSON><PERSON><PERSON><PERSON>", "syberia3": "סיבריה3", "grymmorpg": "גריממור<PERSON>ג", "bloxfruit": "בloxfruit", "anotherworld": "עולם<PERSON><PERSON>ר", "metaquest": "מטא<PERSON><PERSON><PERSON><PERSON>ט", "animewarrios2": "אנימהוריירס2", "footballfusion": "פוטבול<PERSON>וזן", "edithdlc": "אדית<PERSON><PERSON><PERSON><PERSON>י", "abzu": "אבזו", "astroneer": "אסטרוניר", "legomarvel": "לגו<PERSON>רבל", "wranduin": "ורנדו<PERSON>ין", "twistedmetal": "מתכתמעוותת", "beamngdrive": "<PERSON>י<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twdg": "טוודג", "pileofshame": "ערמתבושה", "simulator": "סימול<PERSON>ור", "symulatory": "סימול<PERSON>ו<PERSON>י", "speedrunner": "ספיד<PERSON><PERSON><PERSON><PERSON>", "epicx": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superrobottaisen": "סופררתו<PERSON><PERSON>ן", "dcuo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samandmax": "סאםומאקס", "grywideo": "גרידו", "gaiaonline": "גאי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "korkuoyunu": "בובתצמרמרת", "wonderlandonline": "וגנלנד<PERSON><PERSON><PERSON><PERSON>יין", "skylander": "סקי<PERSON><PERSON><PERSON><PERSON>ר", "boyfrienddungeon": "בןזוגמאורתן", "toontownrewritten": "טונשט<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "simracing": "סימריי<PERSON><PERSON><PERSON>ג", "simrace": "מירוץסימולטור", "pvp": "פvp", "urbanchaos": "חאו<PERSON>_עירוני", "heavenlybodies": "גופיםשודים", "seum": "סם", "partyvideogames": "משחקי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ות", "graveyardkeeper": "שומרותקברות", "spaceflightsimulator": "סימולטורטיסותחלל", "legacyofkain": "מורש<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "hackandslash": "בלא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ש", "foodandvideogames": "אוכלומ<PERSON><PERSON><PERSON><PERSON>ם", "oyunvideoları": "בו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ם", "thewolfamongus": "הזאבשבינינו", "truckingsimulator": "סימולטור<PERSON>שאיות", "horizonworlds": "עולמותאופק", "handygame": "באנ<PERSON><PERSON><PERSON><PERSON>ס", "leyendasyvideojuegos": "אגדותו<PERSON><PERSON><PERSON>ים", "oldschoolvideogames": "משחקיםישנים", "racingsimulator": "סימולטור<PERSON><PERSON>וצים", "beemov": "<PERSON>י<PERSON><PERSON><PERSON>", "agentsofmayhem": "סוכניהמיהום", "songpop": "שיר<PERSON>ו<PERSON>", "famitsu": "פאמיטסו", "gatesofolympus": "שעריולימ<PERSON>וס", "monsterhunternow": "ציידיםמ<PERSON>לצותעכשיו", "rebelstar": "כו<PERSON><PERSON><PERSON><PERSON>ד", "indievideogaming": "גיי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>נדי", "indiegaming": "משחקים<PERSON>צמאיים", "indievideogames": "משחקי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>צמאיים", "indievideogame": "משחקי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>צמאיים", "chellfreeman": "צלפ<PERSON>י<PERSON>ן", "spidermaninsomniac": "ספיידרמןבבואבלהקיץ", "bufffortress": "מבצרביף", "unbeatable": "בלתימנוצח", "projectl": "<PERSON>ר<PERSON><PERSON><PERSON><PERSON><PERSON>", "futureclubgames": "משחקיהמועדוןשלהעתיד", "mugman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "משחקיאינסומניה", "supergiantgames": "סופרג<PERSON><PERSON>נטגיימס", "henrystickman": "הנרי_המלך", "henrystickmin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "celestegame": "משחקצלסטה", "aperturescience": "מדעפרצות", "backlog": "בקלאג", "gamebacklog": "רשימת<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "gamingbacklog": "ביצועי_משחקים", "personnagejeuxvidéos": "דמויות<PERSON><PERSON><PERSON><PERSON><PERSON>ם", "achievementhunter": "ציידיהצלחות", "cityskylines": "קווי_הנוף_של_העיר", "supermonkeyball": "סופר<PERSON><PERSON><PERSON><PERSON><PERSON>ול", "deponia": "דיפוניה", "naughtydog": "נaughtydog", "beastlord": "בא<PERSON>טלורד", "juegosretro": "גיימזרטרו", "kentuckyroutezero": "קנטאק<PERSON>ר<PERSON><PERSON><PERSON>זירו", "oriandtheblindforest": "אוריוה<PERSON><PERSON><PERSON>העיוור", "alanwake": "אלא<PERSON><PERSON><PERSON><PERSON><PERSON>", "stanleyparable": "סטנליפרנציפי", "reservatoriodedopamin": "ריזר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ד<PERSON>פ<PERSON><PERSON>ן", "staxel": "סטקסל", "videogameost": "מוזיקת<PERSON><PERSON><PERSON>מינג", "dragonsync": "דרג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "חיים<PERSON>ינייה", "ilovekofxv": "אניאוהבkofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "ניוy2k", "pcracing": "מרוציpcr", "berserk": "בֶּרְזֶרְק", "baki": "בָּקִי", "sailormoon": "סייל<PERSON><PERSON><PERSON><PERSON>ן", "saintseiya": "סיינטססיייאה", "inuyasha": "אינויא<PERSON>ה", "yuyuhakusho": "יויוהקושו", "initiald": "אינישיאלד", "elhazard": "אלח<PERSON>רד", "dragonballz": "דרג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "sadanime": "אני<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "כההמאפך", "animescaling": "אני<PERSON>סקלינג", "animewithplot": "אנימהעםעלילה", "pesci": "דגים", "retroanime": "אנימהנוסטלגית", "animes": "אנטים", "supersentai": "סופרsentai", "samuraichamploo": "<PERSON><PERSON><PERSON>o", "madoka": "מדוקה", "higurashi": "היג<PERSON><PERSON><PERSON><PERSON>י", "80sanime": "אנימהשלשנות80", "90sanime": "90sאני<PERSON>ה", "darklord": "האד<PERSON><PERSON>_האפל", "popeetheperformer": "בוּההופעתן", "masterpogi": "מַאסְטֶרפּוֹגִי", "samuraix": "סמור<PERSON>יקס", "dbgt": "דיב<PERSON><PERSON><PERSON><PERSON>י", "veranime": "לראותאנימה", "2000sanime": "אנימהשנות2000", "lupiniii": "לופיניייי", "drstoneseason1": "דרסטוןעונה1", "rapanime": "רפאנימה", "chargemanken": "מחויביםב<PERSON><PERSON><PERSON>ה", "animecover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "החזון<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ואן", "slayers": "סליירס", "tokyomajin": "<PERSON>ו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "anime90s": "אנימה90", "animcharlotte": "אנימ<PERSON><PERSON><PERSON><PERSON><PERSON>ט", "gantz": "גנטץ", "shoujo": "שוגו", "bananafish": "דג_בננה", "jujutsukaisen": "גוגוט<PERSON>וקייסן", "jjk": "גק", "haikyu": "הייקיו", "toiletboundhanakokun": "טויל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "bnha": "בְּנְהַא", "hellsing": "הלסינג", "skipbeatmanga": "סקיפביטמנגה", "vanitas": "ואניטס", "fireforce": "כו<PERSON><PERSON><PERSON><PERSON>ן", "moriartythepatriot": "מוריארטיהפטриот", "futurediary": "יו<PERSON><PERSON>_של_עתיד", "fairytail": "אגדה", "dorohedoro": "דורוהדורה", "vinlandsaga": "וינלנד<PERSON>אגה", "madeinabyss": "עשויבאבעס", "parasyte": "פרזיט", "punpun": "פונפונים", "shingekinokyojin": "שינ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushishi": "מושיושי", "beastars": "בִּיסְטָרְס", "vanitasnocarte": "ואניט<PERSON>נוקרדי", "mermaidmelody": "מרמיידמלו<PERSON>י", "kamisamakiss": "נשיקה<PERSON>שלבּו", "blmanga": "בלמנגה", "horrormanga": "מנגהאימה", "romancemangas": "מנגותרומנטיות", "karneval": "קרנבל", "dragonmaid": "דרקונית", "blacklagoon": "בלאקלגונ", "kentaromiura": "קנטארומיורה", "mobpsycho100": "מובפציעו100", "terraformars": "טרא<PERSON><PERSON>ומ<PERSON><PERSON>ס", "geniusinc": "גeniusinc", "shamanking": "שמא<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "קבוצ<PERSON><PERSON>דורסל", "jugo": "גוגו", "bungostraydogs": "בנגוסטריידו<PERSON>ס", "jujustukaisen": "יוג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>סן", "jujutsu": "גוגיטסו", "yurionice": "יוריונהייס", "acertainmagicalindex": "מדדמעצמה<PERSON><PERSON><PERSON>ר", "sao": "סאו", "blackclover": "קַשָּׁחַה", "tokyoghoul": "<PERSON>ו<PERSON><PERSON><PERSON>גול", "onepunchman": "אישהמכהאחת", "hetalia": "הטליה", "kagerouproject": "פרויק<PERSON>כגרו", "haikyuu": "הייקיו", "toaru": "טוּאֲרוּ", "crunchyroll": "קרנצירול", "aot": "אוט", "sk8theinfinity": "סקייט<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ניטי", "siriusthejaeger": "סיריו<PERSON><PERSON><PERSON><PERSON><PERSON>ר", "spyxfamily": "ספייxfamily", "rezero": "רזירו", "swordartonline": "אומנות<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>נטרנט", "dororo": "דודו", "wondereggpriority": "ביצתפלאעדיפות", "angelsofdeath": "מלא<PERSON>יהמוות", "kakeguri": "קק<PERSON><PERSON><PERSON><PERSON>י", "dragonballsuper": "דרגו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "hypnosismic": "היפנוזימיק", "goldenkamuy": "גו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "monstermusume": "מונסטרמוזום", "konosuba": "קונוסובה", "aikatsu": "אייקאצע", "sportsanime": "אנימיס<PERSON>ו<PERSON>ט", "sukasuka": "סוקסוקה", "arwinsgame": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "angelbeats": "אנגלביטס", "isekaianime": "איסלנציהאנימה", "sagaoftanyatheevil": "סאגהשלתניההרעה", "shounenanime": "שונן<PERSON><PERSON>י<PERSON>ה", "bandori": "בנד<PERSON>י", "tanya": "טניה", "durarara": "דוג<PERSON><PERSON>ה", "prettycure": "פרטיק<PERSON><PERSON>ר", "theboyandthebeast": "הילדוהחיה", "fistofthenorthstar": "אגרוףשלצפון", "mazinger": "מזינ<PERSON>ר", "blackbuttler": "ברווז<PERSON><PERSON><PERSON>ר", "towerofgod": "מגדלשלאלוהים", "elfenlied": "אלפנליד", "akunohana": "אקאנוֹהָנא", "chibi": "ציבי", "servamp": "סרפמפ", "howtokeepamummy": "איךשומריםעלאמא", "fullmoonwosagashite": "פולמונו<PERSON><PERSON><PERSON>שיוטה", "shugochara": "שוגוצרה", "tokyomewmew": "טוקיומיומיו", "gugurekokkurisan": "גוגו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "cuteandcreepy": "חמו<PERSON>ו<PERSON>ו<PERSON>ר", "martialpeak": "פסגתמרושלים", "bakihanma": "בכ<PERSON>הנמא", "hiscoregirl": "ה<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "orochimaru": "אורו<PERSON><PERSON>מ<PERSON>רו", "mierukochan": "מיר<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "dabi": "ד<PERSON>י", "johnconstantine": "גו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>טין", "astolfo": "אסטולפו", "revanantfae": "רבננט<PERSON>יי", "shinji": "שי<PERSON><PERSON><PERSON>י", "zerotwo": "זיר<PERSON><PERSON>ו", "inosuke": "אינוסוקה", "nezuko": "נזוקו", "monstergirl": "מפלצתית", "kanae": "קנה", "yone": "יוון", "mitsuki": "מיט<PERSON><PERSON><PERSON>י", "kakashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lenore": "לנור", "benimaru": "בֵּינִיָּרוּ", "saitama": "סייט<PERSON>ה", "sanji": "סאנ<PERSON>י", "bakugo": "בקאגו", "griffith": "גריפית", "ririn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "korra": "קורה", "vanny": "ונדי", "vegeta": "ווגטה", "goromi": "גו<PERSON>ו<PERSON>י", "luci": "לוצי", "reigen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scaramouche": "סק<PERSON>ר<PERSON><PERSON>ש", "amiti": "אמי<PERSON>י", "sailorsaturn": "מלחיםבשתיים", "dio": "דיו", "sailorpluto": "סיילורפלוטו", "aloy": "אלוי", "runa": "רונה", "oldanime": "אנימ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "chainsawman": "משפחתהסרטים", "bungoustraydogs": "בונגו_סטראי_דוגס", "jogo": "בוא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ג", "franziska": "פרנציסקה", "nekomimi": "נקומימי", "inumimi": "אינומימי", "isekai": "איזק<PERSON><PERSON>י", "tokyorevengers": "טוקיור<PERSON><PERSON><PERSON><PERSON><PERSON>ס", "blackbutler": "בל<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ארגו<PERSON><PERSON><PERSON><PERSON><PERSON>י", "claymore": "קליימור", "loli": "לולי", "horroranime": "אנימותהחלמה", "fruitsbasket": "סלסלתפירות", "devilmancrybaby": "דא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>יבייבי", "noragami": "נוראדמי", "mangalivre": "מנג<PERSON><PERSON><PERSON><PERSON><PERSON>י", "kuroshitsuji": "קרושיצוצי", "seinen": "סיים", "lovelive": "לחבבלחיות", "sakuracardcaptor": "קָפְּטוּרַיָּהסָקוּרָה", "umibenoetranger": "אומי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "owarinoseraph": "אווארינוסרף", "thepromisedneverland": "הארץשהובטתהמעולם", "monstermanga": "מנגהמפלצות", "yourlieinapril": "השקרשלךבאפריל", "buggytheclown": "בו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bokunohero": "בקו<PERSON><PERSON><PERSON>בורשלי", "seraphoftheend": "סוף_העולם_בושר", "trigun": "טריגון", "cyborg009": "סייבורג009", "magi": "מאגי", "deepseaprisoner": "אסירבחלוקיוחשוך", "jojolion": "גוגוליון", "deadmanwonderland": "בּוֹאנָגִידיוֹםמָוֶתפִּיּוּת", "bannafish": "ברווז<PERSON>יש", "sukuna": "סוקונה", "darwinsgame": "משח<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "husbu": "בוסבּו", "sugurugeto": "סוגרוגטו", "leviackerman": "ל<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "sanzu": "סנזו", "sarazanmai": "סרא<PERSON><PERSON><PERSON><PERSON><PERSON>י", "pandorahearts": "פנדורההארטס", "yoimiya": "יומיה", "foodwars": "מלחמות<PERSON><PERSON><PERSON>ל", "cardcaptorsakura": "כובשי_הקלפים_סאקורה", "stolas": "סטולס", "devilsline": "קו_השטן", "toyoureternity": "אלנצחשלך", "infpanime": "אינפנימה", "eleceed": "אלאסיד", "akamegakill": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "blueperiod": "תקופת_הכחול", "griffithberserk": "גרי<PERSON>יתברסר<PERSON>", "shinigami": "שיניגامي", "secretalliance": "בריתסודית", "mirainikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "מאחוץוקאינוימה", "yuki": "יו<PERSON>י", "erased": "מוגלה", "bluelock": "בלוקבוצות", "goblinslayer": "שוחףגובלינים", "detectiveconan": "די<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "shiki": "שי<PERSON><PERSON>", "deku": "<PERSON><PERSON><PERSON>", "akitoshinonome": "אקיטושינונומה", "riasgremory": "ריא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "shojobeat": "שוא<PERSON><PERSON><PERSON>יט", "vampireknight": "בּוּהערפד_האבירים", "mugi": "מוגי", "blueexorcist": "אקסור<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ול", "slamdunk": "סלאם<PERSON><PERSON><PERSON><PERSON>", "zatchbell": "זא<PERSON><PERSON>בל", "mashle": "מאשל", "scryed": "זיה<PERSON>י", "spyfamily": "משפחתמרגלים", "airgear": "א<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "גברתקסמים", "thesevendeadlysins": "החטאים<PERSON><PERSON>עתיים", "prisonschool": "ביתספרהכלא", "thegodofhighschool": "אלוהיתיכולים", "kissxsis": "נשיקותותאחיות", "grandblue": "גרנדבלו", "mydressupdarling": "אֲבוּת_שַׁלּוּםהַבּוּעָר", "dgrayman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rozenmaiden": "רוז<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "animeuniverse": "יקום<PERSON>נימה", "swordartonlineabridge": "סדרתחרבותברשתמותאם", "saoabridged": "סאו_מקוצר", "hoshizora": "הושיזורה", "dragonballgt": "דרגון<PERSON>ו<PERSON><PERSON><PERSON>טי", "bocchitherock": "בוק<PERSON><PERSON><PERSON><PERSON><PERSON>ק", "kakegurui": "קא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "mobpyscho100": "מבול<PERSON>סיכו100", "hajimenoippo": "צעדים<PERSON><PERSON><PERSON><PERSON><PERSON>ים", "undeadunluck": "לאמתיםו<PERSON><PERSON><PERSON><PERSON>ים", "romancemanga": "רומנצהמנגה", "blmanhwa": "בל<PERSON><PERSON>hwa", "kimetsunoyaba": "קימצוּנוּיָבָּה", "kohai": "קואהי", "animeromance": "אנימרו<PERSON><PERSON>ס", "senpai": "סנפאי", "blmanhwas": "בלמנגה", "animeargentina": "אנימה<PERSON><PERSON><PERSON><PERSON>טינה", "lolicon": "לו<PERSON><PERSON><PERSON><PERSON>ן", "demonslayertothesword": "שוחרת<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "ד<PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "להתראותאֶרי", "firepunch": "לֵהַבָּה", "adioseri": "ביי_סרי", "tatsukifujimoto": "טצוקי<PERSON>וגימוטו", "kinnikuman": "<PERSON>י<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushokutensei": "מוש<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "shoujoai": "שוגו<PERSON>י", "starsalign": "הכוכביםמסודרים", "romanceanime": "אנימה<PERSON><PERSON><PERSON><PERSON>ים", "tsundere": "צונדרה", "yandere": "יא<PERSON><PERSON><PERSON>י", "mahoushoujomadoka": "מאחושר<PERSON>שו<PERSON>ד<PERSON><PERSON>ה", "kenganashura": "קנגאנשורה", "saointegralfactor": "המרכ<PERSON>בהשלואי", "cherrymagic": "קוסם<PERSON>וב<PERSON><PERSON>ן", "housekinokuni": "בית<PERSON><PERSON>נו<PERSON>וני", "recordragnarok": "רק<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "אויהסומי<PERSON>ו<PERSON><PERSON>ון", "meliodas": "מליודאס", "fudanshi": "פודנשי", "retromanga": "רטרומנגה", "highschoolofthedead": "בתיהספרשלהמתים", "germantechno": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "oshinoko": "אושינוקו", "ansatsukyoushitsu": "אנצatsuכיתה", "vindlandsaga": "וינדלנד<PERSON><PERSON>גה", "mangaka": "מנגקה", "dbsuper": "די<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princeoftennis": "נסיךהטניס", "tonikawa": "טוניקאו<PERSON>ה", "esdeath": "אֵסְדֵּאת", "dokurachan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bjalex": "ביא<PERSON><PERSON><PERSON>", "assassinclassroom": "כיתתרצח", "animemanga": "אנימהמנגה", "bakuman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathparade": "תהלוכדתמות", "shokugekinosouma": "שוקו<PERSON>קינוסומה", "japaneseanime": "אנימי<PERSON><PERSON><PERSON>יי", "animespace": "אנימהספייס", "girlsundpanzer": "בנותואנ<PERSON><PERSON><PERSON>ס", "akb0048": "אקב0048", "hopeanuoli": "הופענולי", "animedub": "דא<PERSON><PERSON><PERSON><PERSON><PERSON>ה", "animanga": "אנימנגה", "tsurune": "צורונה", "uqholder": "מנוי", "indieanime": "אינדיא<PERSON>י<PERSON>ה", "bungoustray": "בונ<PERSON><PERSON>סטריי", "dagashikashi": "ד<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "גנדהם0", "animescifi": "אנסי<PERSON>י", "ratman": "אישהרחובות", "haremanime": "חראמנימיה", "kochikame": "קוציקמה", "nekoboy": "נֵקֹובּוּי", "gashbell": "גא<PERSON><PERSON>ל", "peachgirl": "בּוּפִּיצָה", "cavalieridellozodiaco": "קַבָּלִיֵּרִידֵּלּוֹזוֹדִיָּק", "mechamusume": "מֵקָהַמּוּסוּמֵה", "nijigasaki": "ניגי<PERSON><PERSON><PERSON><PERSON>י", "yarichinbitchclub": "יאריציןביץקלאב", "dragonquestdai": "דרג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "heartofmanga": "ליבמנגה", "deliciousindungeon": "טעים<PERSON><PERSON><PERSON><PERSON>ן", "manhviyaoi": "מנחביוי", "recordofragnarok": "הקלטתשלר<PERSON>גנר<PERSON>ק", "funamusea": "מוזיאוןכיף", "hiranotokagiura": "היר<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>א<PERSON>גורה", "mangaanime": "מנגהאנימה", "bochitherock": "בואחיתיה<PERSON>rock", "kamisamahajimemashita": "קאמיסמה<PERSON><PERSON>גימשיטה", "skiptoloafer": "סקי<PERSON><PERSON>ול<PERSON><PERSON>ер", "shuumatsunovalkyrie": "שומעתסנוולקריה", "tutorialistoohard": "המדריךקשהמדי", "overgeared": "מדי_מצויד", "toriko": "טוריקו", "ravemaster": "ריי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "קןדאי", "chobits": "צוביץ", "witchhatatelier": "בּוֹא_מעבּדת_כּובע_המכשפה", "lansizhui": "לנסיזהוי", "sangatsunolion": "מאוד<PERSON>ונו<PERSON>יון", "kamen": "<PERSON><PERSON><PERSON><PERSON>", "mangaislife": "מאנגהזהחיים", "dropsofgod": "טיפותאלוהים", "loscaballerosdelzodia": "הנסיכיםשל<PERSON>אסטרולוגיה", "animeshojo": "אנימהשוגו", "reverseharem": "הרספוסד", "saintsaeya": "סיינטסאיה", "greatteacheronizuka": "מורהמצוייןאוניזוקה", "gridman": "ג<PERSON><PERSON><PERSON><PERSON>ן", "kokorone": "קוקורונה", "soldato": "חייל", "mybossdaddy": "הבוסשלידדי", "gear5": "גיר5", "grandbluedreaming": "חלומותגדולים", "bloodplus": "דם", "bloodplusanime": "דםוענימה", "bloodcanime": "דםאניום", "bloodc": "ד<PERSON><PERSON>", "talesofdemonsandgods": "סיפוריר<PERSON><PERSON><PERSON>דמונים", "goreanime": "אנימהאימה", "animegirls": "בנותאנימה", "sharingan": "ש<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "crowsxworst": "עורביםxהגמנים", "splatteranime": "ספלאטר<PERSON>נימה", "splatter": "נשפך", "risingoftheshieldhero": "עלייתה<PERSON><PERSON><PERSON><PERSON>גן", "somalianime": "סומליאנימה", "riodejaneiroanime": "אנימריו", "slimedattaken": "סליימ<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "animeyuri": "אנימי<PERSON><PERSON><PERSON>י", "animeespaña": "אנימ<PERSON><PERSON><PERSON><PERSON><PERSON>ל", "animeciudadreal": "אני<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ל", "murim": "מורימ", "netjuunosusume": "נטגוונוסוסום", "childrenofthewhales": "ילדיםשלהדול<PERSON>ינים", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "סופרקמ<PERSON>יוןס", "animeidols": "איידו<PERSON>י<PERSON><PERSON><PERSON>י<PERSON>ה", "isekaiwasmartphone": "איזא<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>עםסמארטפון", "midorinohibi": "ימיו_של_מידורי", "magicalgirls": "בנותמגנטיות", "callofthenight": "קריאתהלילה", "bakuganbrawler": "באקו<PERSON>ןל<PERSON>חמים", "bakuganbrawlers": "בייבו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ס", "natsuki": "נטסוקי", "mahoushoujo": "מאחושوجו", "shadowgarden": "גןהצללים", "tsubasachronicle": "צוהרייב<PERSON>ק", "findermanga": "מנגהבואי", "princessjellyfish": "שרימ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ה", "kuragehime": "קאורגהימה", "paradisekiss": "נשיקתגןעדן", "kurochan": "קור<PERSON><PERSON>ן", "revuestarlight": "רבוא<PERSON>ט<PERSON>רלייט", "animeverse": "אנימ<PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "פרסוקומס", "omniscientreadersview": "מבט<PERSON>ל<PERSON><PERSON><PERSON><PERSON><PERSON>ד<PERSON>ן", "animecat": "אנימקת", "animerecommendations": "המלצותאנהימה", "openinganime": "פתיחת<PERSON>נימה", "shinichirowatanabe": "שיניצ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>י", "uzumaki": "אוזו<PERSON><PERSON><PERSON>י", "myteenromanticcomedy": "קומדיהרו<PERSON>נטיתשלבנינוער", "evangelion": "אוונג<PERSON>יון", "gundam": "גנדם", "macross": "מקרא<PERSON>ס", "gundams": "גונדמים", "voltesv": "וולטסוי", "giantrobots": "רובוטים<PERSON><PERSON>קיים", "neongenesisevangelion": "ניאונג<PERSON><PERSON><PERSON><PERSON><PERSON>בנגליונ", "codegeass": "קודגיאס", "mobilefighterggundam": "ניידמכותלאנדום", "neonevangelion": "ניאו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "mobilesuitgundam": "הנחותגנדום", "mech": "מֵק", "eurekaseven": "אֶדוּרָהָלְבּוֹרובָשֶֽׁי", "eureka7": "בואו7", "thebigoanime": "הביגוא<PERSON>ימה", "bleach": "בליץ", "deathnote": "יומן_מוות", "cowboybebop": "קאובויביפ", "jjba": "גוגבא", "jojosbizarreadventure": "ההרפתקההמשוגעתשלגוגו", "fullmetalalchemist": "אלכימאימת<PERSON>תיבעלמים", "ghiaccio": "קרח", "jojobizarreadventures": "יוגוויז<PERSON><PERSON><PERSON><PERSON>רפתקאות", "kamuiyato": "קמוישאטו", "militaryanime": "אנימימיליטריה", "greenranger": "ג<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "גימי_קודו", "tokyorev": "טו<PERSON>יו<PERSON><PERSON><PERSON><PERSON>י", "zorro": "זורו", "leonscottkennedy": "ליאו<PERSON><PERSON><PERSON>וטקנדי", "korosensei": "קורוסנסאי", "starfox": "סטאר<PERSON>וקס", "ultraman": "אול<PERSON>ר<PERSON>ן", "salondelmanga": "סלוןדהמנגה", "lupinthe3rd": "לופין3", "animecity": "עיראנימה", "animetamil": "אנימתמיל", "jojoanime": "גוגואנאימֶה", "naruto": "נארוטו", "narutoshippuden": "נארוטו<PERSON><PERSON><PERSON>ודן", "onepiece": "ונפיס", "animeonepiece": "אנימהא<PERSON><PERSON><PERSON><PERSON>ס", "dbz": "חיים_בזמן_הדרגון_בול", "dragonball": "דר<PERSON><PERSON><PERSON><PERSON>ול", "yugioh": "יוגיוג", "digimon": "די<PERSON><PERSON><PERSON><PERSON>ן", "digimonadventure": "דיגימו<PERSON><PERSON><PERSON><PERSON>ת<PERSON>ה", "hxh": "חח<PERSON>", "highschooldxd": "תיכוןדxd", "goku": "גוקו", "broly": "ברו<PERSON>י", "shonenanime": "שונן<PERSON><PERSON>י<PERSON>ה", "bokunoheroacademia": "בוקונ<PERSON><PERSON>ר<PERSON><PERSON>קדמיה", "jujustukaitsen": "שוטותעםבּו", "drstone": "דר<PERSON><PERSON><PERSON>ן", "kimetsunoyaiba": "קימצונוי<PERSON><PERSON><PERSON>ה", "shonenjump": "שונ<PERSON><PERSON><PERSON><PERSON><PERSON>", "otaka": "אוטקה", "hunterxhunter": "הא<PERSON><PERSON><PERSON>x<PERSON><PERSON>", "mha": "מחה", "demonslayer": "שו<PERSON><PERSON>ן", "hinokamikagurademonsl": "הינו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>הדמוןסל", "attackontitan": "מתק<PERSON>תטיטאן", "erenyeager": "העיקרשהיהלנוכיף", "myheroacademia": "מאקדמייתג<PERSON>בורשלי", "boruto": "בורוטו", "rwby": "רובי", "dandadan": "ד<PERSON><PERSON><PERSON>", "tomodachigame": "משחקח<PERSON><PERSON>תי", "akatsuki": "אקתסוקי", "surveycorps": "חבורת_הסקרים", "onepieceanime": "אחדח<PERSON><PERSON><PERSON><PERSON>נו", "attaquedestitans": "התקפתהטיטאנים", "theonepieceisreal": "העלילההאמיתית", "revengers": "נקמה", "mobpsycho": "מוֹבּפְּסִיכוֹ", "aonoexorcist": "אקסור<PERSON><PERSON>סטהאונו", "joyboyeffect": "אפ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ח", "digimonstory": "די<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ר", "digimontamers": "דיגימונטרים", "superjail": "סופרגייל", "metalocalypse": "מטאלוקליפסה", "shinchan": "שי<PERSON><PERSON><PERSON>ן", "watamote": "וואטמוטה", "uramichioniisan": "אורמי<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן", "uruseiyatsura": "אורסיאייצורה", "gintama": "גינט<PERSON>ה", "ranma": "רנמה", "doraemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gto": "גטו", "ouranhostclub": "מועדוןההומורשלנו", "flawlesswebtoon": "אתר_חסר_פגמים", "kemonofriends": "קמוןו<PERSON>רנדס", "utanoprincesama": "אוטנופרינצסמה", "animecom": "אנימהקום", "bobobobobobobo": "בובובובובובו", "yuukiyuuna": "יוקייונה", "nichijou": "נישיגו", "yurucamp": "יוּרוּקָאמפּ", "nonnonbiyori": "נונונבּיורי", "flyingwitch": "מכשפהעפה", "wotakoi": "וואו<PERSON><PERSON><PERSON>י", "konanime": "קונאנימה", "clannad": "קלננד", "justbecause": "סתםככה", "horimiya": "הורימיה", "allsaintsstreet": "רחובכלקדושים", "recuentosdelavida": "סיפוריםשל<PERSON>יים"}