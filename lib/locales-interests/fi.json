{"2048": "2048", "mbti": "mbti", "enneagram": "enneagrammi", "astrology": "astrologia", "cognitivefunctions": "kogni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "psykologia", "philosophy": "filosofia", "history": "historia", "physics": "fysiikka", "science": "tiede", "culture": "<PERSON><PERSON><PERSON><PERSON>", "languages": "kielet", "technology": "tekniikka", "memes": "meemit", "mbtimemes": "mbtimeemit", "astrologymemes": "astrolog<PERSON><PERSON><PERSON>", "enneagrammemes": "enneagrammimeemit", "showerthoughts": "suihkuajatuksia", "funny": "hassut", "videos": "videot", "gadgets": "vemp<PERSON><PERSON>", "politics": "politiikka", "relationshipadvice": "suhdeneuvot", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "krypto", "news": "<PERSON><PERSON><PERSON>", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "arkeologia", "learning": "opiskelu", "debates": "keskustelut", "conspiracytheories": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meditation": "meditaatio", "mythology": "mytologia", "art": "taide", "crafts": "käsityöt", "dance": "tan<PERSON>", "design": "design", "makeup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "muoti", "singing": "laulaminen", "writing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "photography": "valoku<PERSON>us", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "books": "kir<PERSON>t", "movies": "el<PERSON><PERSON>", "poetry": "runous", "television": "televisio", "filmmaking": "el<PERSON><PERSON><PERSON><PERSON>inen", "animation": "animaatio", "anime": "anime", "scifi": "scifi", "fantasy": "fantasia", "documentaries": "dokumentit", "mystery": "mysteeri", "comedy": "komedia", "crime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drama": "d<PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "kauhu", "romance": "romantiikka", "realitytv": "tositv", "action": "toiminta", "music": "musiikki", "blues": "blues", "classical": "k<PERSON><PERSON>", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektroniikka", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latino", "metal": "metalli", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tekno", "travel": "mat<PERSON><PERSON>n", "concerts": "konsertit", "festivals": "festarit", "museums": "museot", "standup": "standup", "theater": "<PERSON><PERSON><PERSON>", "outdoors": "<PERSON><PERSON><PERSON><PERSON>", "gardening": "pu<PERSON><PERSON><PERSON><PERSON><PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "pelaaminen", "boardgames": "lautapelit", "dungeonsanddragons": "dungeonsanddragons", "chess": "shakki", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ruoka", "baking": "leivonta", "cooking": "r<PERSON><PERSON><PERSON><PERSON>", "vegetarian": "kasvisruoka", "vegan": "ve<PERSON><PERSON>", "birds": "linnut", "cats": "kissat", "dogs": "koirat", "fish": "kala", "animals": "eläimet", "blacklivesmatter": "mustienelämätmerkitsevät", "environmentalism": "ympäristönsuojelu", "feminism": "feminismi", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqliitto", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "transliitto", "volunteering": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "sports": "ur<PERSON><PERSON><PERSON>", "badminton": "<PERSON><PERSON><PERSON><PERSON>", "baseball": "baseball", "basketball": "koripallo", "boxing": "nyrkkeily", "cricket": "kriketti", "cycling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "<PERSON><PERSON><PERSON><PERSON>", "golf": "golf", "gym": "kuntosali", "gymnastics": "voimist<PERSON><PERSON>", "hockey": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "verkkopallo", "pilates": "pilates", "pingpong": "pöyt<PERSON><PERSON><PERSON>", "running": "juoksu", "skateboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiing": "<PERSON><PERSON><PERSON>", "snowboarding": "lumi<PERSON><PERSON><PERSON>", "surfing": "la<PERSON><PERSON><PERSON><PERSON>", "swimming": "uinti", "tennis": "tennis", "volleyball": "<PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON>", "yoga": "jooga", "scubadiving": "laites<PERSON>llus", "hiking": "pat<PERSON><PERSON><PERSON>", "capricorn": "<PERSON><PERSON>s", "aquarius": "vesimies", "pisces": "kalat", "aries": "oinas", "taurus": "<PERSON>ärkä", "gemini": "kaks<PERSON>t", "cancer": "rapu", "leo": "lei<PERSON><PERSON>", "virgo": "<PERSON><PERSON><PERSON>", "libra": "vaaka", "scorpio": "skor<PERSON><PERSON>", "sagittarius": "jousimies", "shortterm": "lyhy<PERSON>kainen", "casual": "rento", "longtermrelationship": "pitkäaikaistakumppania", "single": "<PERSON><PERSON>", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "eimono", "lgbt": "hlbt", "lgbtq": "lgbtq", "gay": "homo", "lesbian": "<PERSON><PERSON>", "bisexual": "bi<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "lohikäärmeikä", "assassinscreed": "var<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "vahtikoirat", "dislyte": "dislyte", "rougelikes": "rupelikes", "kingsquest": "kuninkaanseikkailu", "soulreaver": "sielunharavoija", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "karunatiivistelmät", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "lohikärmeensaatana", "sunsetoverdrive": "auring<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "tulipe<PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "kiltasodat", "openworld": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "stormisankarit", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dungeonkraavail<PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "midgardinheimot", "planescape": "suunnitelmatiet", "lordsoftherealm2": "valtiaat2", "baldursgate": "<PERSON><PERSON><PERSON><PERSON>", "colorvore": "värivore", "medabots": "medabotit", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "noita", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "tum<PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "pudotus", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "<PERSON><PERSON><PERSON><PERSON>", "modding": "modaus", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersiivinen", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyvanhanakouluna", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidiinspiration", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "a<PERSON>kehto", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampyyritunnelmat", "dimension20": "dimension20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "polkujenetsijä", "pathfinder2ndedition": "polkuopas2editiota", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "verikellontornissa", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "rak<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "<PERSON><PERSON><PERSON><PERSON>", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "yksiotoksella", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "ylipäällikkö", "yourturntodie": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgh<PERSON><PERSON><PERSON>i", "elderscrollsonline": "vanhaskalariksetonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseitsemän", "rpgtext": "rpgteksti", "genshin": "genshin", "eso": "e<PERSON><PERSON>", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "tähdissodatkotor", "demonsouls": "demonisielut", "mu": "mu", "falloutshelter": "pakopai<PERSON>", "gurps": "gurps", "darkestdungeon": "pimeäluola", "eclipsephase": "eclipsivaihe", "disgaea": "disgaea", "outerworlds": "ulkomaailmat", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "isaacinsiteet", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "ka<PERSON>tytöt", "nightcity": "yönkaupunki", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "hammasliitto2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON>", "road96": "tie96", "vtmb": "vtmb", "chimeraland": "chime<PERSON><PERSON>", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "<PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "unohdetutalueet", "dragonlance": "lohikärmespisteet", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornalpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "linja2", "digimonworld": "digimon<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ekopunkki", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "murtune<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horisontin<PERSON>uta<PERSON>ik<PERSON>", "twewy": "twewy", "shadowpunk": "varjopunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "osallistujavihreä", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "tähdenetsijä", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "terävyyksiäpimeässä", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "kyberpunk", "cyberpunk2077": "kyberpunk2077", "cyberpunkred": "kyberpunkpunainen", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "kaatunutjärjestys", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "paholaiseneloonjääjä", "oldschoolrunescape": "vanhakoulurunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "roolipeliseikkailut", "roleplayinggames": "r<PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "symphonianseikkailut", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "tornikaupunki", "myfarog": "munk<PERSON><PERSON>", "sacredunderworld": "pyhäalasarja", "chainedechoes": "ketjurefleksit", "darksoul": "p<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON><PERSON>", "othercide": "toinenmurha", "mountandblade": "vuo<PERSON>jak<PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "ikuisuudenpilarit", "palladiumrpg": "palladiumrpg", "rifts": "raot", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "moi<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "loota<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "pik<PERSON><PERSON><PERSON>ä", "childrenofmorta": "mortan<PERSON><PERSON>", "engineheart": "moot<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "satuomenkadonnutchapter", "hiveswap": "pahoin<PERSON>int<PERSON>", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "tähtikenttä", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworlds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kuningaskuntasydän1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "pimeyd<PERSON><PERSON><PERSON>", "juegosrpg": "r<PERSON><PERSON><PERSON><PERSON>", "kingdomhearts": "kuningaskatset", "kingdomheart3": "kuningaskheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "malkavianheimo", "harvestella": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "villisydämet", "bastion": "bastioni", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "taivaanarcadia", "shadowhearts": "varjoperheet", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "penniverta", "breathoffire4": "tultahengityksessä4", "mother3": "äiti3", "cyberpunk2020": "kyberpunk2020", "falloutbos": "ka<PERSON><PERSON><PERSON><PERSON>", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "roolipelikamoot", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "noidansydän", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "polkujenlöytörpg", "pathfinder2e": "polkuetsijä2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "boo", "spelljammer": "taik<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonagealkuperät", "chronocross": "<PERSON>ikasiirtymä", "cocttrpg": "cocttrpg", "huntroyale": "metsästysota", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "sielunterä", "baldursgate3": "baldursgate3", "kingdomcome": "kuningaskuntaeiolossa", "awplanet": "awplanet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "kuolevalamppu2", "finalfantasytactics": "finalfantasytaktikat", "grandia": "grandia", "darkheresy": "tum<PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "foorumirpg", "golarion": "golarion", "earthmagic": "ma<PERSON>llon<PERSON>agi<PERSON>", "blackbook": "<PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "taivaanlapsia", "gryrpg": "gryrpg", "sacredgoldedition": "pyhäkultaversio", "castlecrashers": "linnaromah<PERSON>", "gothicgame": "goot<PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "haamukytkentätokyo", "fallout2d20": "fallout2d20", "gamingrpg": "peli<PERSON>g", "prophunt": "prophunt", "starrails": "tähtirautatiet", "cityofmist": "sumukaupunki", "indierpg": "indierpg", "pointandclick": "näppäilejaklikkaa", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "tähäsyhdessä", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7iikri<PERSON>", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "jälkisyberpunk", "deathroadtocanada": "kuolemanreittiikaantaan", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "hirviönmetsästäjä", "fireemblem": "tulisy<PERSON>li", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "sielunsyöjä", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarypelit", "tacticalrpg": "taktinenrpg", "mahoyo": "mahoyo", "animegames": "animepelit", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "herkkujakansalle", "diluc": "diluc", "venti": "<PERSON>ti", "eternalsonata": "ikuisenodysseia", "princessconnect": "prinses<PERSON><PERSON><PERSON><PERSON>", "hexenzirkel": "hexenzirkel", "cristales": "kristallit", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantintia", "dota": "dota", "madden": "maddenfi", "cdl": "cdl", "efootbal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esportti", "mlg": "mlg", "leagueofdreamers": "unelmienliga", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootballfi", "dreamhack": "unelmabrändi", "gaimin": "gaimin", "overwatchleague": "overwatchliiga", "cybersport": "kybersportti", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON>", "test1test": "testi1testi", "fc24": "fc24", "riotgames": "riotpelit", "eracing": "eracing", "brasilgameshow": "brasilipelishow", "valorantcompetitive": "valorant<PERSON>lpail<PERSON>", "t3arena": "t3arena", "valorantbr": "valorantfi", "csgo": "csgo", "tf2": "tf2", "portal2": "portti2", "halflife": "puolielämä", "left4dead": "left4dead", "left4dead2": "kuollutjäänne2", "valve": "valve", "portal": "port<PERSON>", "teamfortress2": "tiimivahvistus2", "everlastingsummer": "ikuisetkesät", "goatsimulator": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "vapausplaneetta", "transformice": "mu<PERSON><PERSON><PERSON><PERSON>", "justshapesandbeats": "vainmuotojajarask<PERSON><PERSON>jä", "battlefield4": "taistelukenttä4", "nightinthewoods": "yömetsässä", "halflife2": "puolielämää2", "hacknslash": "hacknslash", "deeprockgalactic": "syvänkivilöydöt", "riskofrain2": "riskisateesta2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON>lihyvä<PERSON><PERSON><PERSON><PERSON>", "interplanetary": "välisyntynyt", "helltaker": "helvetinottaja", "inscryption": "inscryption", "7d2d": "7p2p", "deadcells": "kuolleetsolut", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "kääpiölinnake", "foxhole": "kettukolo", "stray": "ka<PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "taistelukenttä1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "u<PERSON><PERSON>", "eyeb": "katso", "blackdesert": "<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "pöytäpelisimulatori", "partyhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "kovaspacebreakeria", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "loukussajesterinkanssa", "dinkum": "dinkum", "predecessor": "edeltäjä", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "qudi<PERSON><PERSON><PERSON><PERSON>", "colonysim": "koloniasimulaattori", "noita": "noita", "dawnofwar": "sodanalku", "minionmasters": "minionmasters", "grimdawn": "synkkiaamu", "darkanddarker": "pimeätjasynkät", "motox": "motoxfi", "blackmesa": "<PERSON><PERSON>i", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ä<PERSON>", "datingsims": "deittisimulaattorit", "yaga": "yaga", "cubeescape": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "uusikaupunki", "citiesskylines": "kaupunkitaivaat", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtuaalikenopsia", "snowrunner": "lumijuoksija", "libraryofruina": "kirjastoruotsia", "l4d2": "l4d2", "thenonarygames": "eirahapelejä", "omegastrikers": "omegaiskijät", "wayfinder": "suuntanäyttäjä", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "r<PERSON><PERSON><PERSON>muov<PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatiivinenkanahevonen", "dialtown": "dialkaupunki", "smileforme": "hym<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "kissanight", "supermeatboy": "superlihamies", "tinnybunny": "<PERSON><PERSON><PERSON>", "cozygrove": "kotoisagrove", "doom": "doom", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "raja<PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombit", "apex": "huippu", "r6siege": "r6valloitus", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "loittopako", "farcrygames": "farcrypelit", "paladins": "paladins", "earthdefenseforce": "maapallonpuolustusvoimat", "huntshowdown": "metsästysnäytös", "ghostrecon": "haamujoukkue", "grandtheftauto5": "grandtheftauto5", "warz": "wars", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "liityjengiin", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "kuolematasonkossa", "b4b": "b4b", "codwarzone": "codsota", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombit", "mirrorsedge": "peilienreuna", "divisions2": "divisions2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "<PERSON><PERSON><PERSON><PERSON>", "coldwarzombies": "kylmätaisteluzombit", "metro2033": "metro2033", "metalgear": "metallikitti", "acecombat": "taistelulentäjät", "crosscode": "risteyskoodeja", "goldeneye007": "kultasilmä007", "blackops2": "mustatoperaatiot2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernisodankä<PERSON><PERSON>", "neonabyss": "neonabyss", "planetside2": "planeettojenpuolella2", "mechwarrior": "mechwarrior", "boarderlands": "raja<PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "pakoontar<PERSON><PERSON>", "metalslug": "metallinen<PERSON><PERSON><PERSON>", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "worldofwarshipsfi", "back4blood": "takaisinverinäyttäjä", "warframe": "<PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON>alkkas<PERSON><PERSON>", "masseffect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "systemshock": "järjestelmäshokki", "valkyriachronicles": "valkyriachronicles", "specopstheline": "er<PERSON>isoperaati<PERSON>lin<PERSON>", "killingfloor2": "killingfloor2", "cavestory": "luolatarina", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "sodanpyörät", "mwo": "mwo", "division2": "jakso2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "sukupolvi0", "enterthegungeon": "astukagungeoniin", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernisodas2", "blackops1": "mustatwistit1", "sausageman": "ma<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "sotakasvo", "crossfire": "rist<PERSON><PERSON><PERSON>", "atomicheart": "atomisydän", "blackops3": "mustasotakolmonen", "vampiresurvivors": "vampyyriyllätykset", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "vapaus", "battlegrounds": "taistelukenttä", "frag": "frags", "tinytina": "pienitina", "gamepubg": "pelipubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearvapaudenpojat", "juegosfps": "fpspelit", "convertstrike": "mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "sotaalue2", "shatterline": "murtoviiva", "blackopszombies": "mustatoperaatiotzombit", "bloodymess": "verinenlöysä", "republiccommando": "tasavallankomentaja", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON>", "groundbranch": "<PERSON><PERSON><PERSON><PERSON>", "squad": "jengi", "destiny1": "kohtalo1", "gamingfps": "pelifps", "redfall": "<PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubgtyttö", "worldoftanksblitz": "tankkimaailmanräjähdys", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "pienettinanihmemaat", "halo2": "halo2", "payday2": "palkkapäivä2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "epärealistikilpailu", "callofdutydmz": "callofdutydmz", "gamingcodm": "pelitcodm", "borderlands2": "rajaalueet2", "counterstrike": "vastahyökkäys", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "codmw2", "quakechampions": "tärinämestarit", "halo3": "halo3", "halo": "halo", "killingfloor": "tappolattia", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonwhite": "<PERSON><PERSON><PERSON><PERSON>", "remnant": "jäänne", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "<PERSON><PERSON><PERSON>", "returnal": "pala<PERSON>us", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "tärinä2", "microvolts": "mikrovoltit", "reddead": "punainenkuolle", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "taistelukenttä3", "lostark": "lostark", "guildwars2": "kiltaedustajat2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "ruostekäytäntö", "conqueronline": "valloitusverkkopeli", "dauntless": "peloton", "warships": "<PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "lohikäärmeidenpäivä", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON>ukset", "recroom": "recroom", "legendsofruneterra": "runeterranlegendat", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasiatähtiverkko2", "maidenless": "maaton", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "tankkienmaailma", "crossout": "yliviiva", "agario": "agario", "secondlife": "toinenelämä", "aion": "aion", "toweroffantasy": "towerifantasioit<PERSON>", "netplay": "nettipeli", "everquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>mat<PERSON>", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "punasangonline", "superanimalroyale": "supereläinroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ritariverkossa", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "iisakinsitojat", "dragonageinquisition": "dragonageinquisition", "codevein": "kood<PERSON><PERSON><PERSON>", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "sika", "newworld": "uusimaail<PERSON>", "blackdesertonline": "mustahiekkaonline", "multiplayer": "monipelaaminen", "pirate101": "piraatti101", "honorofkings": "kuninkaidenkunnia", "fivem": "fivem", "starwarsbattlefront": "tähtisotabattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostaboo", "tauriwow": "tauriwow", "wowclassic": "wowklassikko", "worldofwarcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "luo<PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON>", "spiralknights": "spi<PERSON><PERSON><PERSON><PERSON>", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "kosto", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "lohikäärmeentieto", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "<PERSON><PERSON><PERSON><PERSON>", "angelsonline": "enkeliäverkkossa", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverse<PERSON><PERSON>", "growtopia": "kasvutopia", "starwarsoldrepublic": "tähtisotakirjasota", "grandfantasia": "<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>ma<PERSON><PERSON>", "riseonline": "nouseverkossa", "corepunk": "ydinfunk", "adventurequestworlds": "seikkailuquestimaailmat", "flyforfun": "lennähuvikseen", "animaljam": "eläinjam", "kingdomofloathing": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "sankarikaupunki", "mortalkombat": "mortalombat", "streetfighter": "katu<PERSON><PERSON><PERSON>", "hollowknight": "tyhjäkavalieri", "metalgearsolid": "metalgearsolid", "forhonor": "kunnianhuomio", "tekken": "tekken", "guiltygear": "syyllinenviihde", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "katukamppailija6", "multiversus": "moniversumi", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuaalitaistelija", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkkuolettavasyhteistyö", "nomoreheroes": "eii<PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "taistelujenkuningas", "likeadragon": "kuinka<PERSON>uluu", "retrofightinggames": "retrotaistelupelit", "blasphemous": "<PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "monsteriensota", "jogosdeluta": "taistel<PERSON><PERSON>", "cyberbots": "kyberrobotit", "armoredwarriors": "varaustoaïrwarriors", "finalfight": "viimeinentaistelu", "poweredgear": "voima<PERSON><PERSON><PERSON><PERSON>", "beatemup": "lyövoitto", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "taistel<PERSON><PERSON>", "killerinstinct": "tappajainstinkti", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "haamukävelijä", "chivalry2": "ritarillisuus2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "syyllinenmättö", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silksonghornet": "silksonghornet", "silksonggame": "silksongpel<PERSON>", "silksongnews": "silksonguutiset", "silksong": "silksong", "undernight": "yönalaiset", "typelumina": "typelum<PERSON>", "evolutiontournament": "evoluutiokilpailu", "evomoment": "evomoment", "lollipopchainsaw": "tikkariviholliset", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "<PERSON><PERSON><PERSON><PERSON>", "horizon": "<PERSON><PERSON><PERSON><PERSON>", "pathofexile": "<PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodbourne": "verinous", "uncharted": "tunt<PERSON><PERSON>", "horizonzerodawn": "horizonzeroaamu", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "infamous", "playstationbuddies": "playstationkaverit", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "kapinayhtiö", "aisomniumfiles": "aisomniumtiedostot", "gta5": "gta5", "gtasanandreas": "gtasanandreaska", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "<PERSON><PERSON><PERSON><PERSON>", "rimworld": "<PERSON><PERSON><PERSON><PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turistim<PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "varjotkolossista", "crashteamracing": "kolaritiimiajot", "fivepd": "viisipd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "demonituskraatu3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samuraiwarriors", "psvr2": "psvr2", "thelastguardian": "viimeinengardiani", "soulblade": "sielulöhö", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "varjosydämet2liitto", "pcsx2": "pcsx2", "lastguardian": "viimeinenvartija", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "peli<PERSON><PERSON>", "armello": "armello", "partyanimal": "juh<PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "taisteluyönmestari", "psychonauts": "psykonautit", "mhw": "mhw", "princeofpersia": "prinsessapurskaja", "theelderscrollsskyrim": "vanhempienkelatskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "vanhatsivut", "gxbox": "gxboksi", "battlefront": "taist<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "äläkuoleyksinyhdessä", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skeitt3", "houseflipper": "taloistatuloa", "americanmcgeesalice": "amerikkalaisenmcgeesalicen", "xboxs": "xboxit", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "kuningaskuntienliiga", "fable2": "<PERSON><PERSON><PERSON><PERSON>", "xboxgamepass": "xboxpelipassi", "undertale": "undertale", "trashtv": "roskatelevisio", "skycotl": "<PERSON><PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "esiisät", "cuphead": "kuppipää", "littlemisfortune": "pienetkatastrofit", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motoilua", "outerwilds": "ulkomaailmat", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "torniyhdistys", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "pitk<PERSON><PERSON><PERSON>", "satisfactory": "tyydyttävä", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometriahyppy", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "henkivartija", "darkdome": "t<PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatorni", "indiegame": "indiepeli", "itchio": "itchio", "golfit": "golfit", "truthordare": "totuusvaihaaste", "game": "peli", "rockpaperscissors": "kivipaperisakset", "trampoline": "trampoliini", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "haaste", "scavengerhunt": "aarteenetsintä", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "valitselukumer<PERSON>", "trueorfalse": "totuusva<PERSON><PERSON><PERSON>", "beerpong": "beerosahtu", "dicegoblin": "noppagoblin", "cosygames": "kotoisatpelit", "datinggames": "deittipelit", "freegame": "ilmainenpeli", "drinkinggames": "juomapelit", "sodoku": "sudoku", "juegos": "pelit", "mahjong": "mahjong", "jeux": "peli", "simulationgames": "simulaatiopelit", "wordgames": "sanapelit", "jeuxdemots": "sanaleikit", "juegosdepalabras": "sanaleikit", "letsplayagame": "pelataanpeliä", "boredgames": "tylsäpelit", "oyun": "peli", "interactivegames": "interaktiivisetpelit", "amtgard": "amtgard", "staringcontests": "tuijotuskilpailut", "spiele": "peli", "giochi": "peli", "geoguessr": "geoguessr", "iphonegames": "iphonepelit", "boogames": "boop<PERSON>t", "cranegame": "k<PERSON><PERSON>i", "hideandseek": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "ruut<PERSON><PERSON><PERSON>", "arcadegames": "arkadipelit", "yakuzagames": "yakuzapelit", "classicgame": "klassikkopeli", "mindgames": "mielipelat", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galapelit", "romancegame": "r<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "kielikuvastimet", "4xgames": "4xpelit", "gamefi": "pelifi", "jeuxdarcades": "pelipelejä", "tabletopgames": "pöytäpelit", "metroidvania": "metroidvania", "games90": "pelit90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON>umi<PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "kilpaajapelit", "ets2": "ets2", "realvsfake": "oikeatvsvaleet", "playgames": "p<PERSON>apelejä", "gameonline": "pelionline", "onlinegames": "verkkopelit", "jogosonline": "verkkopelit", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "pelaapallopeliä", "pictionary": "pictionary", "coopgames": "yhteispelit", "jenga": "jenga", "wiigames": "wiipelit", "highscore": "<PERSON>ui<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "r<PERSON><PERSON><PERSON><PERSON>", "burgergames": "purgerspelit", "kidsgames": "lastenpelit", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwmustaversio", "jeuconcour": "jeuconcours", "tcgplayer": "tcgplayer", "juegodepreguntas": "kysymuspelit", "gioco": "peli", "managementgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "piilopelinpelit", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1peli", "citybuilder": "kaupunkirakentaja", "drdriving": "drdriving", "juegosarcade": "peli<PERSON>i", "memorygames": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "puhaltaapelit", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "vanhapelit", "couchcoop": "sohvakoop", "perguntados": "kys<PERSON><PERSON>", "gameo": "<PERSON><PERSON><PERSON><PERSON>", "lasergame": "lasergame", "imessagegames": "imessagepelit", "idlegames": "aikapelit", "fillintheblank": "täytätyhjät", "jeuxpc": "pc<PERSON>i", "rétrogaming": "retropelejä", "logicgames": "logiikkapelit", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "metromelonta", "jeuxdecelebrite": "julkkispelit", "exitgames": "exitpelit", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "peliävägkill", "traditionalgames": "perinteisetpelit", "kniffel": "kniffel", "gamefps": "pelifps", "textbasedgames": "tekstipohjaisetpelit", "gryparagrafowe": "gryparagrafit", "fantacalcio": "futisfantasia", "retrospel": "retropelejä", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "nurkkapelit", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "pöyt<PERSON><PERSON><PERSON>", "tischfußball": "pöytä<PERSON><PERSON><PERSON><PERSON>", "spieleabende": "<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "peli<PERSON><PERSON><PERSON>", "casualgames": "rentopelit", "fléchettes": "nuolipelikuviot", "escapegames": "pak<PERSON><PERSON>", "thiefgameseries": "varkauspelisa<PERSON><PERSON>", "cranegames": "nostopelit", "játék": "peli", "bordfodbold": "b<PERSON><PERSON><PERSON>", "jogosorte": "pelivalinta", "mage": "mage", "cargames": "autopelit", "onlineplay": "verkkopelit", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "laukkubingot", "randomizer": "<PERSON>un<PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "pelitpc", "socialdeductiongames": "sosiaalinenseduktiopelit", "dominos": "dominos", "domino": "domino", "isometricgames": "isometrisetpelit", "goodoldgames": "hyvövanhatpelit", "truthanddare": "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "aarteenmetsästys", "jeuxvirtuel": "virtuaalipelit", "romhack": "romhack", "f2pgamer": "f2ppelaaja", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "fantas<PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON>", "gamesotomes": "pelitomies", "halotvseriesandgames": "halotvsarjatjakonsolit", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "ka<PERSON>ssapeli", "swordandsorcery": "miekkaajamagia", "goodgamegiving": "hyväpeliantaminen", "jugamos": "pela<PERSON><PERSON>", "lab8games": "lab8pelit", "labzerogames": "labzeropelit", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gogame": "go<PERSON>i", "jeuxderythmes": "rytmispelejä", "minaturegames": "miniatyyripelejä", "ridgeracertype4": "kilparatasankari4", "selflovegaming": "itsearvostuspelit", "gamemodding": "peli<PERSON><PERSON><PERSON>", "crimegames": "rikospelit", "dobbelspellen": "dobbelspellen", "spelletjes": "pelit", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "elefant<PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON>pelaa<PERSON>", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "pääpelissä", "kingdiscord": "kuninkaidiscord", "scrabble": "r<PERSON><PERSON>ukkopeli", "schach": "shakkia", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "ta<PERSON><PERSON>i", "onitama": "onitama", "pandemiclegacy": "pandemianperintö", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "<PERSON><PERSON><PERSON>", "brettspiele": "lautapelit", "bordspellen": "lautapelise<PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "sällskapspel", "planszowe": "lautapelit", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "pöytäpelit", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "yhteydenneljä", "heroquest": "sankareidenseikkailu", "giochidatavolo": "laudanpelit", "farkle": "farkle", "carrom": "carrom", "tablegames": "pöytäpelit", "dicegames": "noppapelit", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "laudanjump<PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "työpöytäpelit", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosminenkohtaaminen", "creationludique": "luo<PERSON><PERSON><PERSON>", "tabletoproleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "pahvilautapelit", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "vaihtopelithelit", "infinitythegame": "in<PERSON><PERSON><PERSON>", "kingdomdeath": "kuninga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "j<PERSON><PERSON><PERSON>", "chutesandladders": "liukumäkiäjaköysiä", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "pöytäpelit", "planszówki": "pelit", "rednecklife": "punaniskaelämä", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "porukkapeli", "gameboard": "pelilauta", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "hämäränimperium", "horseopoly": "heppopoly", "deckbuilding": "korttipakanrakentaminen", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "laudapelit", "shadowsofbrimstone": "hämärätbrimstonet", "kingoftokyo": "tokyonkuningas", "warcaby": "warcaby", "táblajátékok": "pöytäpeli", "battleship": "taistelulaiva", "tickettoride": "liputkyytiin", "deskovehry": "työskentelyvehkeet", "catán": "<PERSON><PERSON><PERSON><PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "lautapelit", "stolníhry": "lautapelit", "xiángqi": "shakkia", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "seurapelit", "starwarslegion": "starwarslegioona", "gochess": "gosha<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "seurapelit", "terraria": "terrakenttä", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "päiväz", "identityv": "identiteettiv", "theisle": "saari", "thelastofus": "viimeinenmeistä", "nomanssky": "eiukkomaata", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyjainkikone", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amongus", "eco": "ekologinen", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planeettorakentaja", "daysgone": "päivät<PERSON><PERSON>", "fobia": "fobia", "witchit": "noita<PERSON><PERSON>a", "pathologic": "patologinen", "zomboid": "zomboid", "northgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "7dtd": "7pdtd", "thelongdark": "pitkähämy", "ark": "a<PERSON><PERSON>", "grounded": "maadoitettu", "stateofdecay2": "lahjakäyrä2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "äläkuolenälkään", "eternalreturn": "ikuinenpaluu", "pathoftitans": "titaanitietä", "frictionalgames": "kitkapelejä", "hexen": "noidat", "theevilwithin": "pah<PERSON>ta", "realrac": "oikeatrac", "thebackrooms": "takahuoneet", "backrooms": "taustahuoneet", "empiressmp": "empiressmp", "blockstory": "lohikaarmehistorii", "thequarry": "kiv<PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "kuo<PERSON>alossa", "thewalkingdeadgame": "eläväkuollutpeli", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "imperiennousu", "stateofsurvivalgame": "eloonjäämispeli", "vintagestory": "vintagestory", "arksurvival": "arkitopia", "barotrauma": "barotrauma", "breathedge": "hengitysvoima", "alisa": "alisa", "westlendsurvival": "länneneloonjääminen", "beastsofbermuda": "bermudanbeastit", "frostpunk": "frostpunk", "darkwood": "pimeämetsä", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil2": "asukaspaha2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tyh<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "survivalpelit", "sillenthill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "tätäsotaaminun", "scpfoundation": "scpperustaminen", "greenproject": "vihreäprojekti", "kuon": "kuon", "cryoffear": "itkepelosta", "raft": "raft", "rdo": "rdo", "greenhell": "vihreäteatteri", "residentevil5": "residentevil5", "deadpoly": "kuollutpoly", "residentevil8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onironauta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "granny": "mummo", "littlenightmares2": "pieniyönpainajaiset2", "signalis": "signalis", "amandatheadventurer": "amandantheadventurer", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "kestäpidempään", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "af<PERSON><PERSON><PERSON>", "7day2die": "7päivääkuollaksesi", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propnight": "propööönight", "deadisland2": "kuollutsaaria2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampyyri", "deathverse": "kuolemanverse", "cataclysmdarkdays": "katastrofaalisetpimeätpäivät", "soma": "soma", "fearandhunger": "p<PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkericześczarnobyla", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "kellotorni3", "aloneinthedark": "<PERSON>ksinpimeässä", "medievaldynasty": "keskiaikadynastia", "projectnimbusgame": "projektinimbuspeli", "eternights": "ikiyöt", "craftopia": "käsityömaailma", "theoutlasttrials": "theoutlasttrials", "bunker": "bunkeri", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "kääpiöslayer", "warhammer40kcrush": "warhammer<PERSON><PERSON><PERSON><PERSON>", "wh40": "wh40", "warhammer40klove": "warhammer40<PERSON>rak<PERSON>us", "warhammer40klore": "warhammer40kmyytit", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarkaallokko", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "raka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "rakastanvindicarea", "iloveassasinorum": "rakastanassassinorum", "templovenenum": "templovenenum", "templocallidus": "temlocallidus", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "mur<PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40t", "tetris": "tetris", "lioden": "lioden", "ageofempires": "imperiuminaika", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerikäsioukolle", "civilizationv": "<PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "ittakestkaks", "wingspan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "voimanjajoukkovoimat", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "mytologianaika", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "ulkopaikka2", "banished": "kark<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "sivilisaatio6", "warcraft2": "warcraft2", "commandandconquer": "komentajaajakotisota", "warcraft3": "warcraft3", "eternalwar": "ikuisotsota", "strategygames": "strategiapelit", "anno2070": "anno2070", "civilizationgame": "si<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "kulttuuri4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "sato", "totalwar": "koko<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "fortsit", "goodcompany": "hyväseura", "civ": "civ", "homeworld": "kotiuniversumi", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "nopeamp<PERSON><PERSON><PERSON>", "forthekings": "kuningaksille", "realtimestrategy": "oikeastrategia<PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sid<PERSON><PERSON>sivili<PERSON>atio", "kingdomtwocrowns": "kuningaskuntakaksik<PERSON>unua", "eu4": "eu4", "vainglory": "itsekeskeisyys", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "annoa", "battletech": "taistelutekniikka", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "<PERSON>venhausmatikanluok<PERSON>", "plagueinc": "pärjäätaudilla", "theorycraft": "teoriaa", "mesbg": "mesbg", "civilization3": "sivilisaatio3", "4inarow": "4ri<PERSON><PERSON>", "crusaderkings3": "ristiretkeläiset3", "heroes3": "sankarit3", "advancewars": "edistys<PERSON>da<PERSON>", "ageofempires2": "imperiumit2", "disciples2": "oppilaat2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "strategiapelit", "stratejioyunları": "strategiapelit", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "ihm<PERSON>den_aika", "dinosaurking": "dinosaurustenkuningas", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "rautasydämiä4", "companyofheroes": "sankariperhe", "battleforwesnoth": "taisteluwesnothis<PERSON>", "aoe3": "aoe3", "forgeofempires": "imperiumientaontaa", "warhammerkillteam": "warhammerkuolevatiimi", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "foobiat", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "pelatainklassikkroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "ulkoplaneetta", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "pom<PERSON><PERSON><PERSON>", "ageofempires4": "imperiumienajan4", "civilization5": "sivilisaatio5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "kultris2", "spellcraft": "loitsutaikuus", "starwarsempireatwar": "t<PERSON>ht<PERSON>otaim<PERSON>iumisodassa", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategia", "popfulmail": "popfulmail", "shiningforce": "hohtovoima", "masterduel": "mester<PERSON>li", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "kuljetusjätkä", "unrailed": "juna<PERSON><PERSON><PERSON>", "magicarena": "taikakenttä", "wolvesville": "sudenkylä", "ooblets": "ooblets", "planescapetorment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "ylängönvaltakunnat", "galaxylife": "galaksielämä", "wolvesvilleonline": "sudenkyläonline", "slaythespire": "slaythespire", "battlecats": "taistelikatit", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "simssit", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "tarve<PERSON><PERSON><PERSON><PERSON><PERSON>a", "needforspeedcarbon": "tarvettaajyvyyteen", "realracing3": "oikearacing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "hävioops4", "fnaf": "fnaf", "outlast": "kestäpitempään", "deadbydaylight": "kuollutpäivänvalossa", "alicemadnessreturns": "alice<PERSON><PERSON>pal<PERSON>", "darkhorseanthology": "pimeähevosenantologia", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "viisiltaaatfreddys", "saiko": "saiko", "fatalframe": "kuo<PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "kuolleenherääminen", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "kotikaranteenissa", "deadisland": "<PERSON>uo<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "pienimissattuma", "projectzero": "proje<PERSON><PERSON><PERSON>", "horory": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosterror": "jogosterror", "helloneighbor": "tervenaapuri", "helloneighbor2": "moikkanaapuri2", "gamingdbd": "pelibdb", "thecatlady": "<PERSON><PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magickokoontuminen", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "korttejaihmiskuntaava<PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "koodinimet", "dixit": "dixit", "bicyclecards": "polkupyöräkortit", "lor": "lor", "euchre": "euchre", "thegwent": "gwent", "legendofrunetera": "runeterralegendit", "solitaire": "<PERSON><PERSON><PERSON><PERSON>", "poker": "pokeri", "hearthstone": "tulipakanat", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "avainalue", "cardtricks": "korttitemput", "playingcards": "pelikortit", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunnerfi", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON>kor<PERSON><PERSON>", "pokemoncards": "pokemonkortit", "fleshandbloodtcg": "lihajaver<PERSON><PERSON><PERSON><PERSON>", "sportscards": "urheilukortit", "cardfightvanguard": "korttitaisteluvanguard", "duellinks": "duellinks", "spades": "hertat", "warcry": "<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "lottoa", "hanafuda": "hana<PERSON>da", "theresistance": "vast<PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkortit", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON>hottel<PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "taistelulevy", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "p<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "sinisetsilmätnvalkoinenlohi", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "k<PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomentaja", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "korttipelit", "mtgjudge": "mtgtuomari", "juegosdecartas": "korttipelit", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "k<PERSON><PERSON><PERSON>", "carteado": "cartetettu", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "taisteluhengitykset", "battlespiritssaga": "taisteluhengensaga", "jogodecartas": "k<PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "kort<PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "taikakortteja", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "varjoverse", "skipbo": "skip<PERSON>", "unstableunicorns": "epävakaathärät", "cyberse": "kyberse", "classicarcadegames": "klassisetpelihallit", "osu": "o<PERSON>u", "gitadora": "gitadora", "dancegames": "tanssipelit", "fridaynightfunkin": "perjantaiyöfunkka", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektim<PERSON>i", "projectdiva": "projektidiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON>a", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockatahaamojen", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "rytmipelejä", "stepmania": "stepmania", "highscorerythmgames": "korkeapisteitärytmipeleissä", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "tanssitulestajajäästä", "auditiononline": "kutsuvideo", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "rytmipelejä", "cryptofthenecrodancer": "kryptosielunkeiju", "rhythmdoctor": "rytmi<PERSON>ita<PERSON>", "cubing": "ku<PERSON>oi<PERSON>n", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puzzlepelit", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logiikkapulmat", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "pähkinät", "rubikscube": "rubikinpuzzle", "crossword": "ristisanatehtävä", "motscroisés": "rist<PERSON>a", "krzyżówki": "ristikkotehtävät", "nonogram": "nonogrammi", "bookworm": "kirjakissa", "jigsawpuzzles": "palapelit", "indovinello": "arvaus", "riddle": "arvoitus", "riddles": "arvoitukset", "rompecabezas": "pala<PERSON><PERSON>", "tekateki": "tekateki", "inside": "dentro", "angrybirds": "vihaisetlinnut", "escapesimulator": "pak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "ristisanatehtävät", "kurushi": "k<PERSON>hi", "gardenscapesgame": "puuta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "pakohuonepelit", "escapegame": "pakopeli", "3dpuzzle": "3d<PERSON><PERSON><PERSON>", "homescapesgame": "homescapespeli", "wordsearch": "sanahaku", "enigmistica": "<PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kula<PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "arvoitustarinat", "fishdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "karkkijyrä", "littlebigplanet": "pienisuuriplaneetta", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kquirkkia", "rubikcube": "rubik<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "kotihaasteet", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "a<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoonpelit", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "ristisanatehtävät", "ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "rätselwörter", "buscaminas": "minesweeper", "puzzlesolving": "pala<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "naurispoika", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "<PERSON><PERSON><PERSON><PERSON>", "guessing": "arvaaminen", "nonograms": "nonogrammit", "kostkirubika": "kostkirubika", "crypticcrosswords": "salaperäisetristikot", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "palapelihaastetehtävät", "catcrime": "<PERSON><PERSON><PERSON>", "quebracabeça": "p<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "puzzlepähkinät", "poptropica": "poptropica", "thelastcampfire": "viimeinentulenleiri", "autodefinidos": "autodefinioidut", "picopark": "picopark", "wandersong": "vaellussävel", "carto": "kartta", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "ka<PERSON>ette", "limbo": "limbo", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON>", "tinykin": "pikkukaverit", "rubikovakostka": "rubikovakostka", "speedcube": "<PERSON><PERSON>ak<PERSON><PERSON>", "pieces": "palaset", "portalgame": "porttipeli", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON>", "indovinelli": "arvoitukset", "cubomagico": "ku<PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoli", "futurefight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobiilimlegendat", "brawlstars": "taistelutähdet", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "yksinäinensusihukka", "gacha": "gacha", "wr": "nyt", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblekiekkopojat", "asphalt9": "asfaltti9", "mlb": "mlb", "cookierunkingdom": "cookiekuningaskunta", "alchemystars": "alchemystars", "stateofsurvival": "eloonjääminen", "mycity": "munkaupunki", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hyperfront": "hyperfront", "knightrun": "r<PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "tulipatsaattekijät", "honkaiimpact": "honkaiimpact", "soccerbattle": "jalkapallotaitoje<PERSON>telu", "a3": "a3", "phonegames": "puhelinpelit", "kingschoice": "kuni<PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "bensakallon", "tacticool": "<PERSON>kt<PERSON><PERSON>", "cookierun": "<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pik<PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "käsityöläinen", "supersus": "supersus", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "huomio", "wordfeud": "wordfeud", "bedwars": "sängytappelut", "freefire": "<PERSON><PERSON><PERSON><PERSON>", "mobilegaming": "mobiilipelaaminen", "lilysgarden": "lilysp<PERSON><PERSON>r<PERSON>", "farmville2": "farmville2", "animalcrossing": "eläinyl<PERSON>s", "bgmi": "bgmi", "teamfighttactics": "tiimitaistelutaktiikat", "clashofclans": "kylätaistelu", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8pallopooli", "emergencyhq": "hätäkeskus", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON>inäpä<PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tärinätjaheijastukset", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "tähtitalliverkossa", "dragonraja": "lohikaarme", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beätstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON>", "androidgames": "androidpelit", "criminalcase": "rikosjuttu", "summonerswar": "kutsujasota", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "enkeliliiga", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mylaulavihreätსოციალურ", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "sotarobotit", "mirrorverse": "peiliversumi", "pou": "pou", "warwings": "sotasiivet", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "rahapäällikkö", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "lemmikkikaverit", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "<PERSON><PERSON><PERSON><PERSON>", "wolfy": "wolfy", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "mobiilipeli", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollercoastertycoon": "vuo<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandchase": "suurimetsästys", "bombmebrasil": "bombaaemibrasiliassa", "ldoe": "ldoe", "legendonline": "legendaarinenonline", "otomegame": "<PERSON><PERSON><PERSON><PERSON>", "mindustry": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdragons": "lohenkutsu", "shiningnikki": "kimaltelevanikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "varjotaistelu3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "sanojasenwithfriends2", "soulknight": "sieluritarit", "purrfecttale": "purrfect<PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "sadonkylä", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>ma<PERSON>ili", "empiresandpuzzles": "imperiumejajapalapelejä", "empirespuzzles": "empirespuzzleja", "dragoncity": "lohipaikka", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilefi", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mudrunner": "mudjuoksija", "tearsofthemis": "kyyneltesyksistä", "eversoul": "eversoul", "gunbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "peliml<PERSON>", "dbdmobile": "dbdmobile", "arknight": "arknightfi", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombisa<PERSON>n<PERSON><PERSON>", "eveechoes": "<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "<PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaklubi", "v4": "v4", "cookingmama": "kokkausäiti", "cabalmobile": "cabalimobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "peli<PERSON><PERSON><PERSON>", "girlsfrontline": "tyttöjen<PERSON><PERSON>a", "jurassicworldalive": "jurassicworldelävänä", "soulseeker": "sielunetsijä", "gettingoverit": "ylämäestäylöspäin", "openttd": "avoinnttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "kuunch<PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobiilipelit", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "pelimobiililegendat", "timeraiders": "<PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marve<PERSON><PERSON><PERSON><PERSON>", "thebattlecats": "taistelukissat", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "r<PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "matkailijatrpg", "2300ad": "2300jkr", "larp": "larp", "romanceclub": "romanssikerho", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysteeriluola", "pokemonlegendsarceus": "poké<PERSON>leg<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "pokémonvahti", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatit", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "pokémonpurppura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "luonto", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "taskupe<PERSON>kon<PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "tiimimystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "littent", "shinypokemon": "kiiltävätpokemonit", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "rautakädet", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmestari", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "lapsetjakpokémonit", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "kiiltokalastaja", "ajedrez": "shakki", "catur": "<PERSON>ur", "xadrez": "shakki", "scacchi": "shakki", "schaken": "shakkia", "skak": "skak", "ajedres": "shakki", "chessgirls": "shakkitytöt", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "kiinalainenshakki", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "shakkipelaaminen", "openings": "a<PERSON><PERSON><PERSON>", "rook": "rookki", "chesscom": "shakkicom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "vankiloitajalohikäärmeitä", "dungeonmaster": "dungeonisännöitsijä", "tiamat": "tiamat", "donjonsetdragons": "donjonitjalohikäärmeet", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "voxmachina<PERSON><PERSON><PERSON><PERSON>", "doungenoanddragons": "doungenoandtrollit", "darkmoor": "p<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftmestaruus", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "unelmasmp", "hermitcraft": "erakontaito", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodit", "mcc": "mcc", "candleflame": "kynttiläliekki", "fru": "fru", "addons": "lisäosat", "mcpeaddons": "mc<PERSON><PERSON><PERSON><PERSON>", "skyblock": "<PERSON><PERSON><PERSON><PERSON>", "minecraftpocket": "minecrafttasku", "minecraft360": "minecraft360", "moddedminecraft": "modattuminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "ma<PERSON>man<PERSON><PERSON><PERSON><PERSON>ä", "minecraftdungeons": "minecraftluolat", "minecraftcity": "minecraftkaupunki", "pcgamer": "pcpelaaja", "jeuxvideo": "pelivideo", "gambit": "gambit", "gamers": "pelurit", "levelup": "tason<PERSON>u", "gamermobile": "gamermob<PERSON>li", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "peli<PERSON><PERSON><PERSON><PERSON>", "gamen": "gamen", "oyunoynamak": "pelipäivä", "pcgames": "pelitpc", "casualgaming": "rentopelaaminen", "gamingsetup": "pelikamat", "pcmasterrace": "pcmasterrace", "pcgame": "pc<PERSON>i", "gamerboy": "peli<PERSON><PERSON>", "vrgaming": "v<PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerfi", "gameplays": "pelivideot", "consoleplayer": "konsolipelaaja", "boxi": "boksi", "pro": "profi", "epicgamers": "epicpelaa<PERSON><PERSON>", "onlinegaming": "verkkopelaaminen", "semigamer": "puolipeleilijä", "gamergirls": "gamergirlit", "gamermoms": "p<PERSON><PERSON><PERSON><PERSON>", "gamerguy": "pelijätkä", "gamewatcher": "pelivalvoja", "gameur": "peli<PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "tiimiyritetään", "mallugaming": "mall<PERSON>la<PERSON>n", "pawgers": "pawgers", "quests": "<PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "mukavapelaaminen", "gamelpay": "gamelpay", "juegosdepc": "pcpelit", "dsswitch": "dsswitch", "competitivegaming": "kilpailupelaaminen", "minecraftnewjersey": "minecraftuusijuhta", "faker": "teeskentelijä", "pc4gamers": "pc4<PERSON><PERSON><PERSON><PERSON>", "gamingff": "pelikaverit", "yatoro": "yatoro", "heterosexualgaming": "heteroseksuaalinenpelaaminen", "gamepc": "pelikone", "girlsgamer": "tytötsyndat", "fnfmods": "fnfmods", "dailyquest": "päivittäinenhaaste", "gamegirl": "p<PERSON><PERSON>tt<PERSON>", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "peli<PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "sosiaalipelaaja", "gamejam": "pelijam", "proplayer": "prop<PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "muntiimi", "republicofgamers": "pelikansalais", "aorus": "aorus", "cougargaming": "koukkupelejä", "triplelegend": "kolminkertainenlegenda", "gamerbuddies": "pelikaverit", "butuhcewekgamers": "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "christiangamer": "krist<PERSON>inen<PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "poissa", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "gemers", "oyunizlemek": "pelitreenit", "gamertag": "peli<PERSON><PERSON>", "lanparty": "lanjuhlat", "videogamer": "pelaaja", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationpelaaja", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "vihkotietokonepelaaja", "protogen": "protogen", "womangamer": "naispel<PERSON><PERSON>", "obviouslyimagamer": "ilmisel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "metsäretkeilijä", "humanfallflat": "<PERSON><PERSON><PERSON>lankatt<PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nollapakenemista", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusiikki", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "syksypojat", "switch": "v<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "zeldalegenda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "oikeusjuttu", "ssbm": "ssbm", "skychildrenofthelight": "taivaanlapsetval<PERSON>", "tomodachilife": "tomodachielämä", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "kuninkaankyyneleet", "walkingsimulators": "kävely_simulaattorit", "nintendogames": "nintendopelit", "thelegendofzelda": "zeldalegendat", "dragonquest": "lohkodemonit", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runeviljelmä", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "villiluonto", "myfriendpedro": "munkaver<PERSON>ed<PERSON>", "legendsofzelda": "zeldalegendat", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51peliä", "earthbound": "maanpäällä", "tales": "tarinat", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "eläinylityksiä", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "kolmiostrategia", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "mäntysuokasöinänpäivä", "nintendos": "nintendonelit", "new3ds": "uudet3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstarit", "marioandsonic": "mariojas<PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslasfi", "urgot": "urgot", "zyra": "zyra", "redcanids": "punakaniidit", "vanillalol": "vanillalol", "wildriftph": "wildriftfi", "lolph": "naurulol", "leagueoflegend": "legendienliiga", "tốcchiến": "töskämpö", "gragas": "gragas", "leagueoflegendswild": "legendojenliigaerä", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespanja", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaminglol": "pelataanlol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexportit", "hextech": "hextech", "fortnitegame": "fortnitepeli", "gamingfortnite": "pelataanfortnitea", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideopelit", "scaryvideogames": "p<PERSON>tta<PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "pelinkehittäjä", "megamanzero": "megamanzero", "videogame": "<PERSON>peli", "videosgame": "pelivideot", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "y<PERSON><PERSON>", "ow2": "ow2", "overwatch2": "ylivartio2", "wizard101": "velho101", "battleblocktheater": "taistelublokkiteatteri", "arcades": "pelihallit", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sandboxpelit", "videogamelore": "pelivie<PERSON>ät<PERSON>", "rollerdrome": "rollerdrama", "parasiteeve": "loiselämä", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "hämärämetsä", "dreamscape": "unima<PERSON><PERSON>", "starcitizen": "tähdenkansalainen", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "grandtheftauto", "deadspace": "kuo<PERSON><PERSON><PERSON><PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videopelit", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "pelit", "touhouproject": "touhouprojekti", "dreamcast": "unelmakastike", "adventuregames": "seikkailupelit", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "kausientarina", "retrogames": "retropelit", "retroarcade": "retroarvokas", "vintagecomputing": "vintagetietokoneet", "retrogaming": "retrogaming", "vintagegaming": "vintagepelit", "playdate": "leikkitreffit", "commanderkeen": "komentajakeen", "bugsnax": "bugsnax", "injustice2": "epäoikeudenmukaisuus2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "taiva<PERSON><PERSON>", "zenlife": "zenelämä", "beatmaniaiidx": "beatmaniaiidx", "steep": "jyrkkä", "mystgames": "my<PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "lohkopelimaailma", "medievil": "k<PERSON><PERSON><PERSON><PERSON>nen", "consolegaming": "konsolepelit", "konsolen": "konsoli", "outrun": "irti", "bloomingpanic": "kukkapaniikki", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "p<PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "superjätski", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "maataloussims", "juegosviejos": "vanhankoulunpelit", "bethesda": "bethesda", "jackboxgames": "jackboxpelit", "interactivefiction": "interaktiivinenfiktio", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "vis<PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrhaamu", "payday": "palkkapäivä", "chatherine": "katherin", "twilightprincess": "hämä<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "hiekkalaatikko", "aestheticgames": "aistillisetpelit", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "tiimi2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retro<PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "ta<PERSON><PERSON><PERSON><PERSON>", "starrail": "tähtirata", "keyblade": "a<PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsillointällöin", "novelasvisuales": "vis<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "peli<PERSON>ro", "videojuejos": "videopelit", "videogamedates": "pelipäivätreffit", "mycandylove": "munkark<PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkkipelit", "batmangames": "batmankesäpelejä", "returnofreckoning": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamsterpelit", "dayofthetantacle": "tentakkelipäivä", "maniacmansion": "maniak<PERSON><PERSON>o", "crashracing": "romukilpailu", "3dplatformers": "3dalustapelit", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "vanhakoulupelaaminen", "hellblade": "helvetinmiekka", "storygames": "tarinapelit", "bioware": "bioware", "residentevil6": "asukasmikro6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "ylikaksisielua", "gameuse": "pelikäyttö", "offmortisghost": "offmortisghost", "tinybunny": "pieni_kanin", "retroarch": "retroarch", "powerup": "voimaviritä", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gach<PERSON><PERSON><PERSON><PERSON>", "retroarcades": "retrohuoneet", "f123": "f123", "wasteland": "auti<PERSON>a", "powerwashsim": "voima<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "korallisaaria", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON>maail<PERSON>", "metaquest": "metaquest", "animewarrios2": "animevaikuttajat2", "footballfusion": "jalk<PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON><PERSON>", "legomarvel": "legomurmelit", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "vääntödemo", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simu<PERSON><PERSON><PERSON>", "symulatory": "sym<PERSON><PERSON><PERSON>", "speedrunner": "nopeusjuoksija", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "ihmemaaonline", "skylander": "taivaanmies", "boyfrienddungeon": "poikaystäväjengi", "toontownrewritten": "toontownuudelleenkirjoitettu", "simracing": "simuracing", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "kaupunkikaaaos", "heavenlybodies": "taivaallisetkehot", "seum": "seum", "partyvideogames": "bilepelit", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "pelivideot", "thewolfamongus": "susiourmeidänkeskuudessamme", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horisonttiworlds", "handygame": "käteväpeli", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "vanhakoulupelithätään", "racingsimulator": "kilpa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "maailmanloppulähetit", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "olympushuoneet", "monsterhunternow": "monsterihuntijananyt", "rebelstar": "kapinakuningas", "indievideogaming": "indiespelit", "indiegaming": "indiepelaaminen", "indievideogames": "indiepelihaaste", "indievideogame": "indiepeli", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "hämähäkkimiesinsomniac", "bufffortress": "buff<PERSON><PERSON><PERSON><PERSON>", "unbeatable": "voittamaton", "projectl": "projektil", "futureclubgames": "tulevaisuuspelit", "mugman": "mukimies", "insomniacgames": "unettomatpelit", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "cele<PERSON><PERSON><PERSON>", "aperturescience": "aukotieteet", "backlog": "taakka", "gamebacklog": "pelilista", "gamingbacklog": "pelivar<PERSON>o", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "saavutuksenmetsästäjä", "cityskylines": "kaupunkisilhuetit", "supermonkeyball": "superapinalpallo", "deponia": "deponia", "naughtydog": "rajakäytä<PERSON><PERSON><PERSON>ira", "beastlord": "beastlordi", "juegosretro": "retropelit", "kentuckyroutezero": "kentuckyreitt<PERSON><PERSON>", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "lohkokerho", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "rakastankofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "raivohätä", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "sadanime", "darkerthanblack": "tum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animeskaalaus", "animewithplot": "juonianime", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "p<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "mestariboo", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "lataajamies", "animecover": "animekansi", "thevisionofescaflowne": "escaflownenvisio", "slayers": "te<PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90luvuilla", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletissaolevathanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "<PERSON><PERSON><PERSON>", "fireforce": "tulisa<PERSON><PERSON>", "moriartythepatriot": "moria<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "tulevaisuuspäiväkirja", "fairytail": "satukirja", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "tehtyabyssissä", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "mermaidmelody", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON>", "romancemangas": "romanssisarjakuvia", "karneval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "nerofirmo", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "y<PERSON><PERSON>a", "acertainmagicalindex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "mustate<PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "ykköspommi", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "aot": "aot", "sk8theinfinity": "sk8taikuisuus", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "vakoiluperhe", "rezero": "rezero", "swordartonline": "miekkataideverkossa", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioriteetti", "angelsofdeath": "k<PERSON><PERSON>ane<PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "kultakamu", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "ur<PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "enkeliävät", "isekaianime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "poi<PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kuinkapitäämumminvireänä", "fullmoonwosagashite": "täysikuuhätiin", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "söpöjäjajaikseen", "martialpeak": "taisteluhuippu", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hui<PERSON><PERSON>laajatyttö", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monsterityttö", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "merimie<PERSON><PERSON>_saturnus", "dio": "<PERSON><PERSON><PERSON>", "sailorpluto": "merikarhu", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "moottorisahamies", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "hedelmäkori", "devilmancrybaby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "rakastanelä", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "lupauks<PERSON><PERSON><PERSON>", "monstermanga": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "valhesiappil", "buggytheclown": "böönklovni", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "kyborgi009", "magi": "magi", "deepseaprisoner": "syvämerevanki", "jojolion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadmanwonderland": "kuolleenmiehenihmemaa", "bannafish": "<PERSON><PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "r<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "paholaisenviiva", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON>i", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "salajainenyhteisö", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "poiste<PERSON>u", "bluelock": "bluelock", "goblinslayer": "goblinmetsästäjä", "detectiveconan": "detektiivikonan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "sininenexorcisti", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "vakoiluperhe", "airgear": "ilmavehkeet", "magicalgirl": "taikatyttö", "thesevendeadlysins": "seitsemänkuolettavasyyntiä", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "korkeakoulunjumala", "kissxsis": "suukotsisko", "grandblue": "<PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "munasukkaanystävä", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniversumi", "swordartonlineabridge": "miekkaistartaverkossaabridge", "saoabridged": "saopohjattu", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "romanssimanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromanssi", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayermie<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "veripoika", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romanssiannaime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "mahouvärityttömadoka", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "kirsikkapaku", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "kuolemankorkeakoulu", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tennis<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "mangaboo", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanianime", "animespace": "animespotti", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedubi", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "rottamies", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochiikame", "nekoboy": "nekopojat", "gashbell": "gashbell", "peachgirl": "persikkatyttö", "cavalieridellozodiaco": "zodiakinritarit", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "lohikäärmeentehtävädai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON>ungeonissa", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "hauskamuseo", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "ylivarroissa", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "noitamummonhattustudio", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaiselämää", "dropsofgod": "jumalankastelut", "loscaballerosdelzodia": "zodiakinritarit", "animeshojo": "animefifi", "reverseharem": "kääntöharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "mun<PERSON><PERSON><PERSON><PERSON>", "gear5": "varuste5", "grandbluedreaming": "supertummansinisiäunelmia", "bloodplus": "veriplus", "bloodplusanime": "veriplusanime", "bloodcanime": "bloodcanime", "bloodc": "veri", "talesofdemonsandgods": "demonienjajumaltentarinoita", "goreanime": "goreanime", "animegirls": "animetytöt", "sharingan": "<PERSON><PERSON>", "crowsxworst": "korppijengivärkkää", "splatteranime": "rois<PERSON><PERSON><PERSON>", "splatter": "rois<PERSON>", "risingoftheshieldhero": "kilviherranherääminen", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespanja", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "valkoistenvalastenlapset", "liarliar": "valehtelijavalehtelija", "supercampeones": "supermestarit", "animeidols": "animeidolit", "isekaiwasmartphone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON>ika<PERSON>ttöjä", "callofthenight": "yönkutsu", "bakuganbrawler": "bakuganmurskaaja", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "prinsessajelly<PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paratiisilento", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeuniversumi", "persocoms": "persocomit", "omniscientreadersview": "omniscientlukijoidennäkymä", "animecat": "animekissa", "animerecommendations": "animevinkit", "openinganime": "avausanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "munnuorisoromcom", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamit", "voltesv": "voltesv", "giantrobots": "järjestäjärobotit", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobiilitaistelijaggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilisuitgundam", "mech": "meka", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "v<PERSON><PERSON><PERSON>", "deathnote": "kuolemankirja", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "j<PERSON><PERSON><PERSON><PERSON>eikkail<PERSON>", "fullmetalalchemist": "täydellinenmetallialkemisti", "ghiaccio": "j<PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "military<PERSON><PERSON>", "greenranger": "vihreäritari", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "tähtikettu", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupin3rd", "animecity": "animekaupunki", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonseikkailu", "hxh": "hxh", "highschooldxd": "lukiodxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonienmetsästäjä", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "titanienhyökkäys", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "munsankariakademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kyselyjou<PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "hyökkäävuorenpäällä", "theonepieceisreal": "theonepieceonaito", "revengers": "kostajat", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "ilojäbäefekti", "digimonstory": "digimonstory", "digimontamers": "digimonkämppäät", "superjail": "supervankila", "metalocalypse": "metal<PERSON><PERSON><PERSON><PERSON>", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramiboo", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "virheetönwebtoon", "kemonofriends": "kemonokaverit", "utanoprincesama": "ilmanprinsessaa", "animecom": "animesnadi", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "arkielämä", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "elämänlaskentaa"}