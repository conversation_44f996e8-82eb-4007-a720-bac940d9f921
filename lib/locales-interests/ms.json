{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "fungsikognitif", "psychology": "psikologi", "philosophy": "fals<PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON>", "physics": "fizik", "science": "sains", "culture": "budaya", "languages": "bahasa", "technology": "teknologi", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "memeastrologi", "enneagrammemes": "enneagrammemes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "kela<PERSON>", "videos": "video", "gadgets": "gajet", "politics": "politik", "relationshipadvice": "nasihatperhubungan", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "berita", "worldnews": "beritadunia", "archaeology": "arkeologi", "learning": "<PERSON><PERSON><PERSON>", "debates": "bahas", "conspiracytheories": "teorikonspirasi", "universe": "alamsemesta", "meditation": "meditasi", "mythology": "mitologi", "art": "seni", "crafts": "kraf", "dance": "menari", "design": "re<PERSON><PERSON><PERSON>", "makeup": "<PERSON><PERSON><PERSON><PERSON>", "beauty": "kecantikan", "fashion": "fesyen", "singing": "<PERSON><PERSON><PERSON>", "writing": "<PERSON><PERSON>", "photography": "fotografi", "cosplay": "kosplay", "painting": "lukisan", "drawing": "melukis", "books": "buku", "movies": "filem", "poetry": "puisi", "television": "televisyen", "filmmaking": "pembikinanfilem", "animation": "animasi", "anime": "anime", "scifi": "scifi", "fantasy": "fantasi", "documentaries": "dokumentari", "mystery": "misteri", "comedy": "komedi", "crime": "jena<PERSON>", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "seram", "romance": "romantik", "realitytv": "realitytv", "action": "aksi", "music": "lagu", "blues": "sedih", "classical": "klasikal", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronik", "folk": "rakyat", "funk": "funk", "hiphop": "hiphop", "house": "rumah", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "melancong", "concerts": "konsert", "festivals": "<PERSON><PERSON><PERSON>", "museums": "muzium", "standup": "standup", "theater": "teater", "outdoors": "diluar", "gardening": "berkebun", "partying": "berpesta", "gaming": "gaming", "boardgames": "permainanpapan", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ur", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ma<PERSON>an", "baking": "baking", "cooking": "memasak", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "burung", "cats": "kucing", "dogs": "anjing", "fish": "ikan", "animals": "haiwan", "blacklivesmatter": "blacklivesmatter", "environmentalism": "environmentalism", "feminism": "feminism", "humanrights": "hakkemanusiaan", "lgbtqally": "rakanlgbt", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "rakantrans", "volunteering": "<PERSON><PERSON><PERSON>", "sports": "sukan", "badminton": "badminton", "baseball": "baseball", "basketball": "bolakeranjang", "boxing": "tinju", "cricket": "kriket", "cycling": "berbasikal", "fitness": "k<PERSON><PERSON><PERSON><PERSON>", "football": "bolasepak", "golf": "golf", "gym": "gym", "gymnastics": "gymnastik", "hockey": "hoki", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "bolajaring", "pilates": "pilates", "pingpong": "pingpong", "running": "berl<PERSON>", "skateboarding": "skateboarding", "skiing": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snowboarding", "surfing": "lun<PERSON><PERSON>r", "swimming": "berenang", "tennis": "tenis", "volleyball": "bolatampar", "weightlifting": "angkatberat", "yoga": "yoga", "scubadiving": "menyelamskuba", "hiking": "hiking", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "cancer", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "<PERSON><PERSON><PERSON><PERSON>", "casual": "santai", "longtermrelationship": "hubunganjangkapanjang", "single": "j<PERSON><PERSON>", "polyamory": "polia<PERSON><PERSON>", "enm": "cintaberbilang", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbian", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "zamannaga", "assassinscreed": "pembunuhxcreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "anjingpengawas", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "pencariankings", "soulreaver": "pen<PERSON><PERSON>ji<PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "na<PERSON><PERSON><PERSON>", "sunsetoverdrive": "senjaoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "feuemas命运", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloabadi", "guildwars": "perangguild", "openworld": "duniaterbuka", "heroesofthestorm": "wiraalamribut", "cytus": "cytus", "soulslike": "jiwa", "dungeoncrawling": "menjelajahdungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "pela<PERSON>lanska<PERSON>", "lordsoftherealm2": "tuansihatdalamrealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "banyakjiwarealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "per<PERSON><PERSON><PERSON><PERSON>", "witcher": "<PERSON><PERSON><PERSON>", "dishonored": "terhormatcontainer", "eldenring": "elden<PERSON>", "darksouls": "jiwadark", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "jatuh", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "<PERSON>ci<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasysekolahlama", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "fantas<PERSON><PERSON><PERSON><PERSON>", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivasimorbid", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "tergodadeng<PERSON><PERSON><PERSON>", "otomegames": "permainanotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimensi20", "gaslands": "gaslands", "pathfinder": "penjelajah", "pathfinder2ndedition": "penjejak<PERSON><PERSON><PERSON><PERSON>", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "satuambil", "rpgmaker": "pembuatrpg", "osrs": "osrs", "overlord": "penguasa", "yourturntodie": "gilaturnbawakmati", "persona3": "persona3", "rpghorror": "horrorrpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "perompak", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgteks", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "gelapdungeon", "eclipsephase": "fasaek<PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "ikatanisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "peperangandynasty", "skullgirls": "skullgirls", "nightcity": "bandmalam", "hogwartslegacy": "warisanhogwarts", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "jalan96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "permainan<PERSON><PERSON><PERSON>", "gothamknights": "gothamknights", "forgottenrealms": "dunia<PERSON><PERSON><PERSON>", "dragonlance": "<PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "anakcahaya", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "garisan2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonbara<PERSON>lar<PERSON>", "twewy": "twewy", "shadowpunk": "bayangpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "misterihogwarts", "deltagreen": "<PERSON><PERSON><PERSON><PERSON>", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "pukul", "lastepoch": "zamanak<PERSON>", "starfinder": "pencaristarter", "goldensun": "mataharimentari", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "pedangdalamgelap", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "siberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkmerah", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenorder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "dunianjahat", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "pensenjagat", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "ketuhanan", "pf2": "pf2", "farmrpg": "permainanfarm", "oldworldblues": "bluesdunialama", "adventurequest": "pencarianpen<PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "permainanberperanan", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "kisahsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "kotaloyang", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "gemaberantai", "darksoul": "jiwakegelap", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "laincide", "mountandblade": "gunungdandagu", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "jurang", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "hellosarlotte", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfbangkit", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "permainankarak<PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "kebangkitanoldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "duniaben<PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kerajaanhati1", "ff9": "ff9", "kingdomheart2": "kerajakingdom2", "darknessdungeon": "gelapcave", "juegosrpg": "permainanrpg", "kingdomhearts": "kera<PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "kerajaanhati3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "panduanberhawa", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON>la", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitarcadia", "shadowhearts": "<PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "nafasapi4", "mother3": "ibu3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "j<PERSON><PERSON><PERSON>", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "permaina<PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "perma<PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "hatiwitch", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "duniamonsterhunter", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "<PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "kera<PERSON>ansampai", "awplanet": "awplanet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "cahayamatibaru2", "finalfantasytactics": "taktikfinalfantasi", "grandia": "grandia", "darkheresy": "ketahankegelapan", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magis<PERSON><PERSON>", "blackbook": "b<PERSON><PERSON><PERSON>", "skychildrenoflight": "anaklangitcahayaboo", "gryrpg": "gryrpg", "sacredgoldedition": "edisiemasberkat", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "permainangotik", "scarletnexus": "nexusmerah", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "kotaselubung", "indierpg": "indierpg", "pointandclick": "tunjukklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "tidakterpisahkan", "freeside": "sidesuka", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "pascasiberpunk", "deathroadtocanada": "jalankematiankekanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "p<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "apiwajik", "genshinimpact": "genshinimpact", "geosupremancy": "geosuperiority", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "pemburudemonbangkit", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtaktikal", "mahoyo": "mahokey", "animegames": "permainananime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "makangoda", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON>ata", "princessconnect": "put<PERSON><PERSON><PERSON>", "hexenzirkel": "b<PERSON>ng<PERSON><PERSON>", "cristales": "kristal", "vcs": "vcs", "pes": "pes", "pocketsage": "p<PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "permainanengames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligapeminatimpian", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "impian<PERSON>", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "<PERSON><PERSON><PERSON><PERSON>", "crazyraccoon": "rakanrakungila", "test1test": "ujian1<PERSON><PERSON>an", "fc24": "fc24", "riotgames": "permainanribut", "eracing": "eracing", "brasilgameshow": "brazilsukanpertunjukan", "valorantcompetitive": "kompetitifvalorant", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4mati", "left4dead2": "left4dead2", "valve": "valve", "portal": "portal", "teamfortress2": "timpertahanan2", "everlastingsummer": "mus<PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simulatorkambing", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetkebebasan", "transformice": "transformice", "justshapesandbeats": "justshapesandbeats", "battlefield4": "medanpertempuran4", "nightinthewoods": "malamdihu<PERSON>", "halflife2": "setengahhidup2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risikahunbuhkan2", "metroidvanias": "metroidvanias", "overcooked": "terlalumasak", "interplanetary": "interplanetari", "helltaker": "helltaker", "inscryption": "inskripsi", "7d2d": "7h2h", "deadcells": "selma<PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "lubang<PERSON>a", "stray": "terbiar", "battlefield": "medanpertempuran", "battlefield1": "medanpertempuran1", "swtor": "swtор", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "eyeb", "blackdesert": "<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "simulatortabletop", "partyhard": "pestaker<PERSON>", "hardspaceshipbreaker": "pelang<PERSON><PERSON>ang<PERSON><PERSON>", "hades": "hades", "gunsmith": "tuka<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "terperangkapdenganjester", "dinkum": "dinkum", "predecessor": "pendahulu", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "guaq<PERSON>", "colonysim": "simkoloni", "noita": "noita", "dawnofwar": "fajarperang", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "gelapandgelap", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "permainandating", "yaga": "yaga", "cubeescape": "escapekotak", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "bandarbaru", "citiesskylines": "pemandangankota", "defconheavy": "defconberat", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiavirtual", "snowrunner": "sal<PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "permainannonary", "omegastrikers": "omegastrikers", "wayfinder": "pencar<PERSON>lan", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "bebikkatakplastik", "battlebit": "pertarunganbit", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "dialtown", "smileforme": "seny<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "malamkucing", "supermeatboy": "supermeatboy", "tinnybunny": "k<PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "<PERSON><PERSON><PERSON>", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "pelangi6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "tanahsempadan", "pubg": "pubg", "callofdutyzombies": "panggil<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "pertahankan<PERSON><PERSON><PERSON>", "huntshowdown": "perburuanpertarungan", "ghostrecon": "penampakanperang", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "sertairombongan", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "pasisandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "pelangi6serangan", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codzonaperang", "callofdutywarzone": "callofdutywarzone", "codzombies": "zombicod", "mirrorsedge": "tepicermin", "divisions2": "divisions2", "killzone": "zonbunuh", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "perangmoden", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "p<PERSON>angan<PERSON><PERSON>", "boarderlands": "boobu<PERSON><PERSON><PERSON>", "owerwatch": "kuasapandangan", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "logamslug", "primalcarnage": "primalcarnage", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "back4blood", "warframe": "perangbingkai", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "kesanmasse", "systemshock": "kejutansistem", "valkyriachronicles": "kronikelvalkyria", "specopstheline": "specopstheline", "killingfloor2": "lantaibunuh2", "cavestory": "ceri<PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "abadseribuabu", "farcry4": "farcry4", "gearsofwar": "mesinkeperang", "mwo": "mwo", "division2": "pembahagian2", "tythetasmaniantiger": "tythelulutempat", "generationzero": "generasizero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "perangmoden2", "blackops1": "blackops1", "sausageman": "lelakisasuasausage", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "sakitbengong", "warface": "wajahperang", "crossfire": "pertempuran", "atomicheart": "hat<PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "bebas", "battlegrounds": "medanpertempuran", "frag": "fraggers", "tinytina": "<PERSON><PERSON>", "gamepubg": "tembakpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearanjingkemerdekaan", "juegosfps": "permainanfps", "convertstrike": "tukardarurat", "warzone2": "zonperang2", "shatterline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "zombibelakangoperasi", "bloodymess": "kacaubilau", "republiccommando": "komandorepublik", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "cabangtanah", "squad": "geng", "destiny1": "takdir1", "gamingfps": "permainanfps", "redfall": "redfall", "pubggirl": "gurlpubg", "worldoftanksblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "panggilanperangblackops", "enlisted": "terdaftar", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "in<PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "duniakeciltina", "halo2": "halo2", "payday2": "payeday2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ha<PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "turnamenbeyondreal", "callofdutydmz": "callofdutydmz", "gamingcodm": "permainancodm", "borderlands2": "borderlands2", "counterstrike": "melawanserangan", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "juarakegemparan", "halo3": "halo3", "halo": "halo", "killingfloor": "la<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "selongsongpecah", "neonwhite": "neonputih", "remnant": "sisa", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "kembali", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "gempabumi2", "microvolts": "<PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON>i", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "medanpertempuran3", "lostark": "lostark", "guildwars2": "<PERSON><PERSON><PERSON><PERSON>", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "karat", "conqueronline": "tawanonline", "dauntless": "berani", "warships": "kapalperang", "dayofdragons": "harinaga", "warthunder": "warthunder", "flightrising": "terbangnaik", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "legendaruneter<PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "duniasenjatank", "crossout": "calonpendout", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "menarafantasi", "netplay": "mainjaring", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "ikatanisaac", "dragonageinquisition": "inquisis<PERSON><PERSON><PERSON>", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpenyu", "lotro": "lotro", "wakfu": "wakfu", "scum": "bodoh", "newworld": "duniahbaru", "blackdesertonline": "blackdesertonline", "multiplayer": "berbilang<PERSON><PERSON><PERSON>", "pirate101": "lanun101", "honorofkings": "hormatratu", "fivem": "fivem", "starwarsbattlefront": "pertempuranstarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "pertempuranbintang2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "chat3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "duniaofwarcraft", "warcraft": "perangcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "jalan<PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "nakhodadragon", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "angelsonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversetarungonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepubliklama", "grandfantasia": "fantasia<PERSON><PERSON>", "blueprotocol": "protokolbiru", "perfectworld": "duniapus<PERSON><PERSON>", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "pengembaraanquestdunia", "flyforfun": "terbanguntukseronok", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "bandarkahheroes", "mortalkombat": "pertempuranmortal", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "sepanjangkehormatan", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "pahlawanjalank6", "multiversus": "multiverse", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "brawlhalla", "virtuafighter": "pahlawanvirtu<PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "tidaklagihero", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "r<PERSON><PERSON>arunga<PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "pertempuranretro", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "permainanpertarungan", "cyberbots": "siberbots", "armoredwarriors": "peperangberperisa<PERSON>", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "kuasaperalatan", "beatemup": "<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "boomortalkombat9", "fightgames": "perlawangame", "killerinstinct": "instinkpembunuh", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "pelayanggghost", "chivalry2": "kesatria2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sekuelhollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "permainansilksong", "silksongnews": "beritasilksong", "silksong": "silksong", "undernight": "ma<PERSON><PERSON><PERSON>a", "typelumina": "typelum<PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON>ane<PERSON><PERSON><PERSON>", "evomoment": "evomomen", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "ceritacer<PERSON><PERSON>", "bloodborne": "<PERSON><PERSON><PERSON><PERSON>", "horizon": "horizon", "pathofexile": "jalanexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "terkenal", "playstationbuddies": "kawankonsolplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "failaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "tuankuperang", "gris": "griss", "trove": "harta", "detroitbecomehuman": "detroitmenjadimanusia", "beatsaber": "beats<PERSON>r", "rimworld": "duniaring", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "bayangkolosal", "crashteamracing": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivepd": "limapd", "tekken7": "tekken7", "devilmaycry": "syaitanbolehmenangis", "devilmaycry3": "devilmaycry3", "devilmaycry5": "jeritaniblis5", "ufc4": "ufc4", "playingstation": "permainanps", "samuraiwarriors": "pahlawansamurai", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "haiwanpes<PERSON>", "warharmmer40k": "warhammerr40k", "fightnightchampion": "juaraknightfight", "psychonauts": "psikonaut", "mhw": "mhw", "princeofpersia": "rajapersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "barisanpertempuran", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "bintangmenuju", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "aliceamericanmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligaker<PERSON><PERSON>", "fable2": "cerita2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tvsampah", "skycotl": "langitcotl", "erica": "erica", "ancestory": "sa<PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "littlemisfortune", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "prommonster", "projectzomboid": "projekzomboid", "ddlc": "ddlc", "motos": "moto", "outerwilds": "<PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "k<PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "oculto", "longdrive": "pemanduanpanjang", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviophile", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "programruangkerbal", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "domegelap", "pizzatower": "pizzatower", "indiegame": "permainanindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON>", "game": "permainan", "rockpaperscissors": "batusthikertasissors", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "perma<PERSON><PERSON><PERSON><PERSON>", "datinggames": "permainandating", "freegame": "permainan<PERSON><PERSON>a", "drinkinggames": "permainandrink", "sodoku": "sodoku", "juegos": "permainan", "mahjong": "mahjong", "jeux": "permainan", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "perma<PERSON><PERSON><PERSON>", "jeuxdemots": "perma<PERSON><PERSON><PERSON>", "juegosdepalabras": "perma<PERSON><PERSON><PERSON>", "letsplayagame": "jom<PERSON>inper<PERSON><PERSON><PERSON>", "boredgames": "permainansombong", "oyun": "permainan", "interactivegames": "permainanterhu<PERSON>ng", "amtgard": "amtgard", "staringcontests": "per<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "spil", "giochi": "permainan", "geoguessr": "geoguessr", "iphonegames": "permainaniphone", "boogames": "permainanboo", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "semb<PERSON><PERSON><PERSON><PERSON>", "hopscotch": "boolompatbalik", "arcadegames": "perma<PERSON><PERSON><PERSON>", "yakuzagames": "permainanyakuza", "classicgame": "permainanklasik", "mindgames": "perma<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "tekalirik", "galagames": "perma<PERSON><PERSON><PERSON>", "romancegame": "per<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "p<PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xgame", "gamefi": "gamefi", "jeuxdarcades": "perma<PERSON><PERSON><PERSON>", "tabletopgames": "permainantabletop", "metroidvania": "metroidvania", "games90": "permainan90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "permainanper<PERSON><PERSON>an", "ets2": "ets2", "realvsfake": "realvsfake", "playgames": "mainpermainan", "gameonline": "permainanonline", "onlinegames": "perma<PERSON><PERSON><PERSON>", "jogosonline": "perma<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "mainballgame", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "permainanwiigames", "highscore": "skor<PERSON><PERSON>", "jeuxderôles": "perma<PERSON><PERSON><PERSON>an", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "permainankids", "skeeball": "skibol", "nfsmwblackedition": "nfsmwbeditiblack", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "permainantanya", "gioco": "bermain", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "permaina<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "permainanf1", "citybuilder": "pembangunkot", "drdriving": "drdriving", "juegosarcade": "permainanarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "mesinpinball", "oldgames": "permaina<PERSON><PERSON>a", "couchcoop": "<PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "permainanlaser", "imessagegames": "permainanimessage", "idlegames": "permaina<PERSON><PERSON>", "fillintheblank": "isitempatkosong", "jeuxpc": "jeuxpc", "rétrogaming": "permainanklasik", "logicgames": "permainanlogik", "japangame": "japanga<PERSON>", "rizzupgame": "permainanriz<PERSON>p", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "permainanterkenal", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "rol<PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "gameandkill", "traditionalgames": "permainantradisional", "kniffel": "kniffel", "gamefps": "permainanfps", "textbasedgames": "permainanberdasarkanteks", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantakalio", "retrospel": "retrospel", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "perma<PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "sepakbolaatasmeja", "tischfußball": "bolameja", "spieleabende": "malamspil", "jeuxforum": "jeuxforum", "casualgames": "permaina<PERSON><PERSON>al", "fléchettes": "dartz", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "permainancurangkredit", "cranegames": "permaina<PERSON><PERSON>a", "játék": "booja<PERSON><PERSON>", "bordfodbold": "bordafootball", "jogosorte": "jogosorte", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "perma<PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "malamgame", "pursebingos": "pursebingos", "randomizer": "pemejalacak", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "permainanpc", "socialdeductiongames": "per<PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "permainanisometrik", "goodoldgames": "permainan<PERSON><PERSON><PERSON><PERSON>", "truthanddare": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "gamerf2p", "free2play": "percuma2main", "fantasygame": "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "perma<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "permainansotomes", "halotvseriesandgames": "halotvseriesandgames", "mushroomoasis": "oasiscendawan", "anythingwithanengine": "apaajayangbermotor", "everywheregame": "permainandimanamana", "swordandsorcery": "pedangdanilmu", "goodgamegiving": "permainangoodgame", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "permainanlab8", "labzerogames": "permainanlabzero", "grykomputerowe": "permainangame", "virgogami": "virgogami", "gogame": "gole<PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "permainankecil", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "sayangdirigaming", "gamemodding": "moddagaim", "crimegames": "permainankejahatan", "dobbelspellen": "perma<PERSON><PERSON><PERSON><PERSON>", "spelletjes": "permainan", "spacenerf": "spacenerf", "charades": "charades", "singleplayer": "p<PERSON>ins<PERSON><PERSON><PERSON>", "coopgame": "per<PERSON><PERSON>nkooperatif", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "mainpermainan", "kingdiscord": "rajadiscord", "scrabble": "seronokscrabble", "schach": "<PERSON>ur", "shogi": "shogi", "dandd": "dandd", "catan": "catanmalaysia", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "legasipandemik", "camelup": "un<PERSON><PERSON>", "monopolygame": "permainanmon<PERSON><PERSON>", "brettspiele": "permainanmeja", "bordspellen": "permainanmeja", "boardgame": "permainanmeja", "sällskapspel": "permainanberkumpulan", "planszowe": "perancanganbilik", "risiko": "risiko", "permainanpapan": "permainanpapan", "zombicide": "zombicide", "tabletop": "permaina<PERSON>us", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "sambungempat", "heroquest": "heroquest", "giochidatavolo": "permainantabletop", "farkle": "farkle", "carrom": "karom", "tablegames": "permainantable", "dicegames": "perma<PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "mainbersamaboo", "deskgames": "permainandesk", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "protokolkrisismarvel", "cosmicencounter": "pertemuankosmik", "creationludique": "k<PERSON>sihebatsсо<PERSON>ен", "tabletoproleplay": "permainantabletop", "cardboardgames": "permainankadbod", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "permainanswitchboard", "infinitythegame": "infinitythegame", "kingdomdeath": "kera<PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "tangg<PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "permainanfamili", "planszówki": "perancan<PERSON><PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON>", "boardom": "boredom", "applestoapples": "epalkeepal", "jeudesociété": "permainansosial", "gameboard": "papanpermainan", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "permainansocial", "twilightimperium": "twilightimperium", "horseopoly": "kudaopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "bayanganbrimstone", "kingoftokyo": "r<PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "perma<PERSON><PERSON><PERSON><PERSON>", "battleship": "kapalperang", "tickettoride": "tike<PERSON><PERSON><PERSON>", "deskovehry": "permainandesk", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "permainanmeja", "stolníhry": "permainankad", "xiángqi": "<PERSON>ur", "jeuxsociete": "permainansosial", "gesellschaftsspiele": "permainansocial", "starwarslegion": "legionstarwars", "gochess": "permaincatur", "weiqi": "weiqi", "jeuxdesocietes": "permainansosial", "terraria": "terrarum", "dsmp": "dsmp", "warzone": "zonaperang", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON>", "identityv": "identitiv", "theisle": "pulau", "thelastofus": "ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "pembur<PERSON><PERSON>", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "ant<PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "pulau<PERSON>et", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON>", "pathologic": "patologi", "zomboid": "zomboid", "northgard": "utara", "7dtd": "7hbd", "thelongdark": "gelappanjang", "ark": "ark", "grounded": "terju<PERSON>at", "stateofdecay2": "keadaanroboh2", "vrising": "vbangkit", "madfather": "bapaktakbetul", "dontstarve": "j<PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "kem<PERSON><PERSON><PERSON>", "pathoftitans": "jalantitan", "frictionalgames": "frictionalgames", "hexen": "heks", "theevilwithin": "keburukandalam", "realrac": "realrac", "thebackrooms": "bilikbelakang", "backrooms": "backrooms", "empiressmp": "sempiressmp", "blockstory": "ceritablok", "thequarry": "tanjungbat<PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "permaina<PERSON><PERSON>walkers", "wehappyfew": "kitedapebanyak", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "permainanstatesurvival", "vintagestory": "ceritaklasik", "arksurvival": "<PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "<PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "survivallendselatan", "beastsofbermuda": "binatangber<PERSON>da", "frostpunk": "frostpunk", "darkwood": "kayuhgelap", "survivalhorror": "ketahananseram", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "keretakosong", "lifeaftergame": "hidupselepaspermainan", "survivalgames": "permainansurvive", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "peranginiadalahmilikaku", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "tangiskeranaingatan", "raft": "rakit", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "mati2poly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nenek", "littlenightmares2": "mimpimalamkecil2", "signalis": "<PERSON><PERSON><PERSON>", "amandatheadventurer": "amandadijelajah", "sonsoftheforest": "<PERSON>ak<PERSON><PERSON>", "rustvideogame": "permainanrust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "undawn", "7day2die": "7hari2mati", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "survivor", "propnight": "malamprop", "deadisland2": "pulaumati2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "hariharihitamcataclysm", "soma": "soma", "fearandhunger": "taku<PERSON>danlapar", "stalkercieńczarnobyla": "p<PERSON><PERSON>hatićieńczarnobyla", "lifeafter": "hidupselepas", "ageofdarkness": "zamangelap", "clocktower3": "menarajam3", "aloneinthedark": "sendiridalamgelap", "medievaldynasty": "dinastimediaval", "projectnimbusgame": "projeknimbusgame", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "ujiantersisa", "bunker": "bunker", "worlddomination": "penguasadunia", "rocketleague": "roketliga", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "pem<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "booperanghammer40k", "wh40": "wh40", "warhammer40klove": "cintawarhammer40k", "warhammer40klore": "warhammer40kkisah", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "sayehloversororitas", "ilovevindicare": "sukavindicare", "iloveassasinorum": "sayacintaka<PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficiosesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilizationv": "peradabanv", "ittakestwo": "ittakestwo", "wingspan": "panajangkepak", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "wirakekuatandansihir", "btd6": "btd6", "supremecommander": "ketuaterhebat", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON>", "args": "argg", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "dibuang", "caesar3": "caesar3", "redalert": "awasmerah", "civilization6": "peradaban6", "warcraft2": "warcraft2", "commandandconquer": "perintahdanpergi", "warcraft3": "warcraft3", "eternalwar": "perangabadi", "strategygames": "permainanstrategi", "anno2070": "anno2070", "civilizationgame": "permainansivilisasi", "civilization4": "peradaban4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "perangtotal", "travian": "travian", "forts": "forts", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "le<PERSON><PERSON>cepatdarijalur", "forthekings": "untukra<PERSON>", "realtimestrategy": "strategirealtim", "starctaft": "starcraft", "sidmeierscivilization": "sidmeiersperadaban", "kingdomtwocrowns": "kerajaanduamahkota", "eu4": "eu4", "vainglory": "k<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "dewa", "anno": "anno", "battletech": "teknobattles", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "kelasalgebrasidaveyangbest", "plagueinc": "plagueinc", "theorycraft": "teorirencan<PERSON>", "mesbg": "mesbg", "civilization3": "peradaban3", "4inarow": "4<PERSON><PERSON><PERSON>s", "crusaderkings3": "pahlawanraja3", "heroes3": "wira3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "zamanempayar2", "disciples2": "pengikut2", "plantsvszombies": "tanamanvszombie", "giochidistrategia": "permainanstrategi", "stratejioyunları": "permainanstrategi", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "zamankeajaiban", "dinosaurking": "rajadinosaurus", "worldconquest": "<PERSON>ak<PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "hatibesi4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "pertempura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerpasuka<PERSON>bunuh", "goosegooseduck": "bebekbebekgans", "phobies": "phobies", "phobiesgame": "permainanphobies", "gamingclashroyale": "pertembunganpermainanroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "berbasisgiliran", "bomberman": "bomberman", "ageofempires4": "zamanempayar4", "civilization5": "peradaban5", "victoria2": "victoria2", "crusaderkings": "rajapeper<PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "se<PERSON><PERSON>", "starwarsempireatwar": "perangbintangkandiberperang", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulmail", "shiningforce": "kekuatanberkilau", "masterduel": "duelmaster", "dysonsphereprogram": "<PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "tuanpengangkutan", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "rancangpelariandari逼", "uplandkingdoms": "kerajaantinggi", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "perangkucing", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "perlucepat", "needforspeedcarbon": "perludikecepatancarbon", "realracing3": "perlumbaansejati3", "trackmania": "trekmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "hilangims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "kembalin<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologikudahitam", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "bangkitmati", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "cikdukacita", "projectzero": "projekkosong", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "haijiran", "helloneighbor2": "haijiran2", "gamingdbd": "gamingdbd", "thecatlady": "g<PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "permainanhor<PERSON>r", "horrorgaming": "permainanhor<PERSON>r", "magicthegathering": "sihirmengumpul", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "namakod", "dixit": "dixit", "bicyclecards": "kadbasikal", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "lagendadarirunetera", "solitaire": "solitaire", "poker": "poker", "hearthstone": "<PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kuncia<PERSON>", "cardtricks": "trik<PERSON><PERSON><PERSON><PERSON>", "playingcards": "kartugame", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "pemandu", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kadperdagangan", "pokemoncards": "kad<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "da<PERSON><PERSON><PERSON><PERSON>", "sportscards": "kadspor", "cardfightvanguard": "pertempurankadvanguard", "duellinks": "duellinks", "spades": "spades", "warcry": "seruanperang", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "r<PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kadyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "duelyugioh", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "penyihirdark", "blueeyeswhitedragon": "naga<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "permainankad", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "permainankad", "mtgjudge": "hakimmtg", "juegosdecartas": "permainankad", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "carimtgplan", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "perma<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "sagakejohananruhpertarungan", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "wajahkredit", "cardfight": "pertempurankad", "biriba": "biriba", "deckbuilders": "pembinadeck", "marvelchampions": "marvelchampions", "magiccartas": "magiccartas", "yugiohmasterduel": "duelmaster<PERSON><PERSON><PERSON>", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornyangtakstabil", "cyberse": "si<PERSON>e", "classicarcadegames": "permainanklasikarcade", "osu": "osu", "gitadora": "gitadora", "dancegames": "permainandans", "fridaynightfunkin": "malamjumaatfunksion", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projekmirai", "projectdiva": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "wiragitar", "clonehero": "klonhero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "gem<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolemasternesia", "dancecentral": "<PERSON><PERSON><PERSON>dek<PERSON>", "rhythmgamer": "permainanirama", "stepmania": "langkahman<PERSON>", "highscorerythmgames": "permainankadarhighscore", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "s<PERSON>rangendang", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "permaina<PERSON><PERSON>ma", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "dokterrhythm", "cubing": "<PERSON><PERSON><PERSON><PERSON>", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "permainanteka", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "tekatekilogik", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "tekatekiotak", "rubikscube": "kubusrubik", "crossword": "tekakata", "motscroisés": "tiksilang", "krzyżówki": "tekateka", "nonogram": "nonogram", "bookworm": "cacingbuku", "jigsawpuzzles": "puzzlejigsaw", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "tekateki", "riddles": "tekateki", "rompecabezas": "puzzles", "tekateki": "<PERSON><PERSON><PERSON><PERSON>", "inside": "dalam", "angrybirds": "angrybirds", "escapesimulator": "simulatorlarik", "minesweeper": "penyodokmines", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "tekasilang", "kurushi": "k<PERSON>hi", "gardenscapesgame": "permainangardenscapes", "puzzlesport": "puzzlesport", "escaperoomgames": "permainanbilikpenuhcabaran", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "permainanhomescapes", "wordsearch": "carikata", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "riddletales", "fishdom": "ikanbudi", "theimpossiblequiz": "kuizmustahil", "candycrush": "candycrush", "littlebigplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "teka3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kubusrubik", "cuborubik": "kubusrubik", "yapboz": "yapboz", "thetalosprinciple": "prinsi<PERSON><PERSON><PERSON><PERSON>", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON>and<PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "kubusrubik", "cruciverba": "kruciverba", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "perkataanbengang", "buscaminas": "perburuanminas", "puzzlesolving": "mencaripuzzle", "turnipboy": "turnipboy", "adivinanzashot": "aduan<PERSON>", "nobodies": "ta<PERSON><PERSON><PERSON>apa", "guessing": "teka", "nonograms": "nonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "tekasilangrahsia", "syberia2": "syberia2", "puzzlehunt": "puzzlehunt", "puzzlehunts": "puzzlehunts", "catcrime": "j<PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "peca<PERSON><PERSON><PERSON><PERSON>a", "hlavolamy": "puzzle", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefinisi", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "gamaburungtakbernama", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "kubikrubik", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "portalpermainan", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "duniabengkok", "monopoly": "monopoli", "futurefight": "pertempuranmasadepan", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "serigala<PERSON>orang", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "hidup<PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "bintangensemble", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "kerajinanbiskutkingdom", "alchemystars": "bintangalkimia", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "pentasberwarna", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "pertempuranroyale", "angela": "angela", "dokkanbattle": "pertempurandokkan", "fategrandorder": "fategrandorder", "hyperfront": "hyperdepan", "knightrun": "larikesatria", "fireemblemheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "pertarungansepakbola", "a3": "a3", "phonegames": "permainantelefonn", "kingschoice": "p<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "guardianpetualangan", "petrolhead": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikcool", "cookierun": "la<PERSON>ue", "pixeldungeon": "jalanpixel", "arcaea": "arcaea", "outoftheloop": "keluardaripadaloop", "craftsman": "tukang", "supersus": "supersus", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "perangkatil", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "taman<PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "taktikpertempurpasukan", "clashofclans": "pertempuranclan", "pjsekai": "pjsekai", "mysticmessenger": "pembawaapa", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "ibu<PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "goyangdanbergoyang", "ml": "ml", "bangdream": "bangdream", "clashofclan": "boopert<PERSON>puranclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "puteriwaktu", "beatstar": "beatstar", "dragonmanialegend": "legend<PERSON>iang<PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "cermin<PERSON><PERSON>", "pocketlove": "cintadalampoket", "androidgames": "permainanandroid", "criminalcase": "kesjenayah", "summonerswar": "perangpanggil", "cookingmadness": "keg<PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaanggels", "lordsmobile": "lordsmobile", "tinybirdgarden": "tamanburungkecil", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "robotperang", "mirrorverse": "cerminvers", "pou": "pou", "warwings": "sayapperang", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "waktufu", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "masuk", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "p惩罚灰色乌鸦", "petpals": "<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "permainansultans", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "runcitygame", "juegodemovil": "permainanmobile", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "meniru", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmesabah", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "permainanotome", "mindustry": "mindustry", "callofdragons": "panggildragon", "shiningnikki": "berkilaunik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "syarikatlimbus", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "kata2dengankawan2", "soulknight": "jiwawarrior", "purrfecttale": "purrfecttale", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobile", "harvesttown": "bandartua", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "empayarandtekateki", "empirespuzzles": "puzzleeimpires", "dragoncity": "kotanaga", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileid", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "airmatabulan", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "<PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmudah", "streetfighterduel": "pertempuranjalan", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "barisanperempuan", "jurassicworldalive": "du<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulseeker": "<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "move<PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "bountyrushonepiece", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "legendaneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "permainanmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "perangkucing", "dnd": "dnd", "quest": "pencarian", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "duniakegelapan", "travellerttrpg": "travellerttrpg", "2300ad": "2300tahun", "larp": "larp", "romanceclub": "klubromantik", "d20": "d20", "pokemongames": "perma<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "animepokemon", "pokémongo": "pokémongo", "pokemonred": "pokemonmerah", "pokemongo": "pokemongo", "pokemonshowdown": "pertembu<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "pengembarapoké<PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonbersatu", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonungu", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monsterpoket", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonberbulu", "teamystic": "teammistik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "hack<PERSON><PERSON><PERSON><PERSON>", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokemincemerlang", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "tangan<PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "tid<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "masterpokémon", "pokémonsleep": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "snappoke<PERSON>", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "pembur<PERSON><PERSON>a", "ajedrez": "<PERSON>ur", "catur": "<PERSON>ur", "xadrez": "<PERSON>ur", "scacchi": "<PERSON>ur", "schaken": "schaken", "skak": "skak", "ajedres": "<PERSON>ur", "chessgirls": "g<PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "caturjepun", "chinesechess": "<PERSON><PERSON><PERSON>a", "chesscanada": "caturcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rook": "ruak", "chesscom": "caturcom", "calabozosydragones": "kalabozodanpeladang", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "penguasalpenjara", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "lagendavoxmachina", "doungenoanddragons": "dungeonanddragons", "darkmoor": "gelapmoor", "minecraftchampionship": "juaraminecraft", "minecrafthive": "sarangminecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsminecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON>", "skyblock": "langitblok", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodded", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "antaraalam", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "bandarminecraft", "pcgamer": "gamerpc", "jeuxvideo": "permainanvideo", "gambit": "gambit", "gamers": "permain", "levelup": "tingkatkan", "gamermobile": "gamermobile", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "permainan", "oyunoynamak": "permainoyun", "pcgames": "permainanpc", "casualgaming": "permainansantai", "gamingsetup": "setuppermainan", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "budakgaming", "vrgaming": "permainanvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "permainan", "consoleplayer": "p<PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "gamerepik", "onlinegaming": "perma<PERSON><PERSON><PERSON>", "semigamer": "semigamer", "gamergirls": "gamerperempuan", "gamermoms": "ibugamers", "gamerguy": "gamerguy", "gamewatcher": "pemerhatiper<PERSON><PERSON><PERSON>", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerbabe", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "misi", "alax": "alax", "avgn": "puraverage", "oldgamer": "gamerlama", "cozygaming": "permainancomfy", "gamelpay": "gamelpay", "juegosdepc": "permainanpc", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "<PERSON><PERSON><PERSON>", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "permainanhertidak", "gamepc": "gamepc", "girlsgamer": "gamerperempuan", "fnfmods": "fnfmods", "dailyquest": "pencarianharian", "gamegirl": "gamerperempuan", "chicasgamer": "gamerchicas", "gamesetup": "per<PERSON><PERSON><PERSON><PERSON>", "overpowered": "terlampaukuat", "socialgamer": "permainansosial", "gamejam": "permainanjam", "proplayer": "proplayer", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "myteam", "republicofgamers": "republikpemain", "aorus": "aorus", "cougargaming": "permainancougar", "triplelegend": "triplelegend", "gamerbuddies": "kawanpeluanggamers", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON>", "gamernerd": "permainpintar", "nerdgamer": "pemudagamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "pema<PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "mainos", "gamertag": "namape<PERSON><PERSON>", "lanparty": "pestalan", "videogamer": "pemainvideogame", "wspólnegranie": "mainbersama", "mortdog": "mortdog", "playstationgamer": "permainplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamersihat", "gtracing": "gtracing", "notebookgamer": "gamernotebook", "protogen": "protogen", "womangamer": "gamer<PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "k<PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "pengembara", "humanfallflat": "man<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "muziknintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonik", "fallguys": "j<PERSON><PERSON><PERSON><PERSON>", "switch": "tukar", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "topengmajoras", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "peguamb<PERSON>k", "ssbm": "ssbm", "skychildrenofthelight": "anaklangitcahaya", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simulator<PERSON><PERSON>", "nintendogames": "permainannintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "bulanpanen", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "na<PERSON>salamliar", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51permainan", "earthbound": "b<PERSON><PERSON><PERSON>i", "tales": "kisah", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategitiga", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "pahlawanhyrule", "mariopartysuperstars": "superstar<PERSON><PERSON><PERSON><PERSON>", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligalegendboo", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftmy", "lolph": "lolph", "leagueoflegend": "ligalegenda", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligalegendsganas", "adcarry": "bawa<PERSON>a", "lolzinho": "lolz", "leagueoflegendsespaña": "ligaudilegendspanyol", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "permainanlol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "pintuhex", "hextech": "hextech", "fortnitegame": "permainanfortnite", "gamingfortnite": "permainanfotnite", "fortnitebr": "fortnitebr", "retrovideogames": "permainanvideoretro", "scaryvideogames": "permainanscakscary", "videogamemaker": "pembuatvideogame", "megamanzero": "megamanzero", "videogame": "permainanvideo", "videosgame": "permainanvideo", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "awatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arked", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulatorpertanian", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxmalaysia", "robloxdeutsch": "robloxmalaysia", "erlc": "erlc", "sanboxgames": "permainansandbox", "videogamelore": "lorepermainanvideo", "rollerdrome": "rollerdrome", "parasiteeve": "parasitivi", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "duniacamdream", "starcitizen": "bintangwarganegara", "yanderesimulator": "yanderesimulator", "grandtheftauto": "pencurianbesarotomobil", "deadspace": "<PERSON><PERSON><PERSON><PERSON>", "amordoce": "cintaman<PERSON>", "videogiochi": "permainangame", "theoldrepublic": "republiklama", "videospiele": "permainansideo", "touhouproject": "proje<PERSON><PERSON><PERSON>", "dreamcast": "rancanganim<PERSON>", "adventuregames": "permainanpetualangan", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "permainanret<PERSON>", "retroarcade": "arka<PERSON><PERSON>", "vintagecomputing": "komputervintaj", "retrogaming": "permainanklasik", "vintagegaming": "permainanvintage", "playdate": "pertemuanpermainan", "commanderkeen": "komanderkeen", "bugsnax": "bugsnax", "injustice2": "ketidakadilan2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "steep", "mystgames": "mystgames", "blockchaingaming": "permainanblokrantai", "medievil": "medievil", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "lari", "bloomingpanic": "k<PERSON><PERSON><PERSON>mekar", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horrorpermainan", "monstergirlquest": "monstergirlquest", "supergiant": "supergiant", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "permainanfarming", "juegosviejos": "permaina<PERSON><PERSON>a", "bethesda": "bethesda", "jackboxgames": "permainanjackbox", "interactivefiction": "fiksyoninteraktif", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "pencinta<PERSON>s", "visualnovel": "novelvisual", "visualnovels": "novelvisual", "rgg": "rgg", "shadowolf": "bayangserigala", "tcrghost": "tcrhantu", "payday": "<PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "puterisenja", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "permainanaestetik", "novelavisual": "novelavisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "permainanklasik", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "tan<PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolus<PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "pedang<PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "novelavisual", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "permainanvideo", "videogamedates": "temujanjipermainanvideo", "mycandylove": "c<PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "permainanhulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "permainangamster", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "maniacmansion", "crashracing": "perlumbaancrash", "3dplatformers": "platform3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "permainantua", "hellblade": "boohellblade", "storygames": "permainanhistoria", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "melampauiduasjiwa", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "mat<PERSON>gg<PERSON><PERSON>", "tinybunny": "k<PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "kuasakan", "katanazero": "katakanzero", "famicom": "famicom", "aventurasgraficas": "aventuragarafik", "quickflash": "cepatflash", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "arkaderetro", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "<PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animepejuang2", "footballfusion": "perpaduanfutbol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "logamberputar", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tim<PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simu<PERSON>i", "speedrunner": "pantaslari", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "alamajaibonline", "skylander": "skylander", "boyfrienddungeon": "ladangboy<PERSON>", "toontownrewritten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "perlum<PERSON><PERSON><PERSON>bahagia", "simrace": "perlumbasim", "pvp": "pvp", "urbanchaos": "kekacauanbandar", "heavenlybodies": "badanheaven", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "penjagakubur", "spaceflightsimulator": "simulatorpenerbanganangkasal", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "tebasdanpotong", "foodandvideogames": "makananandpermainvideo", "oyunvideoları": "permainanvideo", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulat<PERSON>", "horizonworlds": "duniahorizon", "handygame": "perma<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "permainantempohulu", "racingsimulator": "simulas<PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "agenkekacauan", "songpop": "lagu<PERSON>", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "bintangrebel", "indievideogaming": "permainanvideoindie", "indiegaming": "permainanindie", "indievideogames": "permainanvideoindie", "indievideogame": "permainanvideoindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bentengkuat", "unbeatable": "takt<PERSON><PERSON><PERSON>", "projectl": "projekl", "futureclubgames": "permainanklubmasadepan", "mugman": "mugman", "insomniacgames": "permainans<PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "permainanceleste", "aperturescience": "sainsapertur", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "senaraipermainantunggu", "personnagejeuxvidéos": "watakpermainanvideogame", "achievementhunter": "pem<PERSON><PERSON><PERSON><PERSON>", "cityskylines": "langit<PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "anjingnakal", "beastlord": "r<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "permainanret<PERSON>", "kentuckyroutezero": "jalanrutekentuckyzero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "ostpermainanvideo", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "sukakofxv", "arcanum": "arcanum", "neoy2k": "neo2k", "pcracing": "perlumbapc", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesedih", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animescaling", "animewithplot": "animeyangberisi", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "anime90an", "darklord": "rajakegelapan", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000an", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "musim1drstone", "rapanime": "rapanime", "chargemanken": "casanmerah", "animecover": "animecover", "thevisionofescaflowne": "visiescaflowne", "slayers": "pem<PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90an", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "ikanpisang", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "tent<PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "di<PERSON><PERSON><PERSON><PERSON>", "fairytail": "ceri<PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "sagavinland", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodimermaid", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON>", "romancemangas": "romanceman<PERSON>", "karneval": "karnival", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indeksajaibtertentu", "sao": "sao", "blackclover": "cloverhitam", "tokyoghoul": "tokyoghoul", "onepunchman": "satehsatup<PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "senjataartdalamtersebut", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "keutamaanbooegg", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeolahraga", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "<PERSON>shounen", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "budakdanbinatang", "fistofthenorthstar": "tangan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON>", "martialpeak": "puncakpahlawan", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amitik天", "sailorsaturn": "pelayarsaturn", "dio": "dio", "sailorpluto": "pelautpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animelama", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animehorror", "fruitsbasket": "buah<PERSON><PERSON>l", "devilmancrybaby": "devilmanmen<PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "cint<PERSON><PERSON>p", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "booyour<PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "penjaraalamdeepsea", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "g<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "untukmuabadi", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "periodebiru", "griffithberserk": "g<PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON>", "bluelock": "boobluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detektifconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "kelua<PERSON><PERSON>o", "airgear": "gearair", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "sekolahpenjara", "thegodofhighschool": "tuh<PERSON>ek<PERSON>htinggi", "kissxsis": "c<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "bajukost<PERSON>rin<PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "alamanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "lang<PERSON><PERSON><PERSON><PERSON>", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "mangacinta", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "pembunuhsyaitanmenujipesalah", "bloodlad": "<PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "animecinta", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "faktorsalehintegral", "cherrymagic": "ma<PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekodragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "sekolahtinggaldarah", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "kela<PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "raja<PERSON>is", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejepun", "animespace": "ruang<PERSON>nime", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "dubanime", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeindie", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "nekoabang", "gashbell": "gashbell", "peachgirl": "gadispeach", "cavalieridellozodiaco": "kesatriazodiak", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "kelabperem<PERSON><PERSON><PERSON>in", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "rekorderagnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "overgear", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ateliertopibuas", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaislife", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "haremterbalik", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "cikgubaikonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "bosbapa<PERSON>", "gear5": "gear5", "grandbluedreaming": "impianbiruagung", "bloodplus": "<PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "animedarah", "bloodc": "da<PERSON><PERSON>", "talesofdemonsandgods": "ceri<PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "girs<PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "splatter<PERSON><PERSON>", "splatter": "cecer", "risingoftheshieldhero": "kebangkitanpahlawanjanggu", "somalianime": "animesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "sarana<PERSON><PERSON><PERSON>", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "penip<PERSON><PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "panggilanmalam", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "brawlersbakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "kebunbayangan", "tsubasachronicle": "tsubasachronicle", "findermanga": "pencarimanga", "princessjellyfish": "puterijellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "ciumanparadise", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "pandanganpembacutahu", "animecat": "k<PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "cadangananime", "openinganime": "pem<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediteenaromantikaku", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotgergasi", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "pembunuhobileggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "peluntur", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "alchemist<PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "ais", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animeketenteraan", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "bintang<PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "kotaanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "satupecahan", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "pet<PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "sekolahmenengahdxd", "goku": "goku", "broly": "broly", "shonenanime": "animelelaki", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "pemburupemburu", "mha": "mha", "demonslayer": "pembunuhsyaitan", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "serangtitan", "erenyeager": "erenyeager", "myheroacademia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "perma<PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kor<PERSON><PERSON><PERSON>i", "onepieceanime": "<PERSON><PERSON><PERSON>", "attaquedestitans": "attakdetitan", "theonepieceisreal": "theonepieceisreal", "revengers": "pembalasқа", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "k<PERSON><PERSON><PERSON>", "digimonstory": "ceritad<PERSON><PERSON>", "digimontamers": "digimon<PERSON><PERSON>ta", "superjail": "superjail", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonsempurna", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecomms", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "seba<PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "ceri<PERSON><PERSON><PERSON><PERSON>"}