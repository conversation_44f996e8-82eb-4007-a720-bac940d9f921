{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "fungsikognitif", "psychology": "psikologi", "philosophy": "filos<PERSON><PERSON>", "history": "riwayat", "physics": "<PERSON><PERSON>a", "science": "sains", "culture": "budaya", "languages": "bahasa", "technology": "teknologi", "memes": "meme", "mbtimemes": "mbtimeme", "astrologymemes": "astrologimeme", "enneagrammemes": "enneagrammeme", "showerthoughts": "piki<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "lucu", "videos": "video", "gadgets": "gawai", "politics": "politik", "relationshipadvice": "nasihathubungan", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "kripto", "news": "berita", "worldnews": "beritadunia", "archaeology": "arkeologi", "learning": "<PERSON><PERSON><PERSON>", "debates": "debat", "conspiracytheories": "teorikonspirasi", "universe": "universal", "meditation": "meditasi", "mythology": "mitologi", "art": "seni", "crafts": "kera<PERSON>an", "dance": "menari", "design": "desain", "makeup": "makeup", "beauty": "beauty", "fashion": "fashion", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "<PERSON><PERSON>", "photography": "fotografi", "cosplay": "cosplay", "painting": "melukis", "drawing": "menggambar", "books": "buku", "movies": "film", "poetry": "puisi", "television": "televisi", "filmmaking": "pembuatanfilm", "animation": "animasi", "anime": "anime", "scifi": "scifi", "fantasy": "fantasi", "documentaries": "filmdoku<PERSON>er", "mystery": "misteri", "comedy": "komedi", "crime": "kejahatan", "drama": "drama", "bollywood": "bollywood", "kdrama": "dramakorea", "horror": "horor", "romance": "romantis", "realitytv": "acararealita", "action": "aksi", "music": "musik", "blues": "blues", "classical": "klasik", "country": "negara", "desi": "desi", "edm": "edm", "electronic": "elektronik", "folk": "lagurak<PERSON>t", "funk": "funk", "hiphop": "hiphop", "house": "housemusik", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "travel", "concerts": "konser", "festivals": "festival", "museums": "museum", "standup": "standup", "theater": "teater", "outdoors": "outdoors", "gardening": "berkebun", "partying": "berpesta", "gaming": "gaming", "boardgames": "boardgames", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ur", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ma<PERSON>an", "baking": "memanggang", "cooking": "memasak", "vegetarian": "vegetarian", "vegan": "vege", "birds": "burung", "cats": "kucing", "dogs": "anjing", "fish": "ikan", "animals": "binatang", "blacklivesmatter": "blacklivesmatter", "environmentalism": "lingkunganhidup", "feminism": "feminisme", "humanrights": "hakasasimanusia", "lgbtqally": "lgbtqally", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "relawan", "sports": "olahraga", "badminton": "bulutangkis", "baseball": "baseball", "basketball": "bolabasket", "boxing": "tinju", "cricket": "kriket", "cycling": "bersepeda", "fitness": "fitness", "football": "sepakbola", "golf": "golf", "gym": "gym", "gymnastics": "senam", "hockey": "hoki", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "bolajaring", "pilates": "pilates", "pingpong": "pingpong", "running": "berl<PERSON>", "skateboarding": "skateboard", "skiing": "mainski", "snowboarding": "mainseluncuransal<PERSON>", "surfing": "<PERSON><PERSON><PERSON><PERSON>", "swimming": "berenang", "tennis": "tenis", "volleyball": "bolavoli", "weightlifting": "angkatbeban", "yoga": "yoga", "scubadiving": "selamscuba", "hiking": "men<PERSON>i", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "cancer", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "hubunganjangkapendek", "casual": "santai", "longtermrelationship": "hubunganjangkapanjang", "single": "j<PERSON><PERSON>", "polyamory": "polia<PERSON><PERSON>", "enm": "hubunganterbuka", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbian", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "kode<PERSON><PERSON>h", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "anjingpengawas", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "pen<PERSON><PERSON>ji<PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gild<PERSON><PERSON>", "openworld": "duniterbuka", "heroesofthestorm": "p<PERSON><PERSON><PERSON>artempest", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "pertempurandungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "jalurperencanaan", "lordsoftherealm2": "rajadunia2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabot", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "<PERSON><PERSON><PERSON>", "dishonored": "terhina", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "jat<PERSON><PERSON>a", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "pembuatankarakter", "immersive": "<PERSON>gal<PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivasimenger<PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "tergila<PERSON><PERSON><PERSON><PERSON>", "otomegames": "permainanotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimensi20", "gaslands": "gaslands", "pathfinder": "penjelajah", "pathfinder2ndedition": "pathfinder2ed", "shadowrun": "bayangansih", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "cintabook", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gerakanberat", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "sekaliambil", "rpgmaker": "pembuatrpg", "osrs": "osrs", "overlord": "penguasa", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "horrorrpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "perompak", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "teksrpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "penampungan<PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "bengkelkelam", "eclipsephase": "fasegerhana", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "kotamalam", "hogwartslegacy": "warisanhogwarts", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "jalan96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "du<PERSON><PERSON><PERSON>", "dragonlance": "tajukna<PERSON>", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "kotakkartun", "childoflight": "anakcahaya", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "garisketurunan2", "digimonworld": "dunia<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanverse", "fracturedthrones": "tahtayangpecah", "horizonforbiddenwest": "horizonbaratdilarang", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "<PERSON><PERSON><PERSON><PERSON>", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "ha<PERSON><PERSON><PERSON>", "lastepoch": "zamanak<PERSON>", "starfinder": "pencaristar", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "dosaaslidivinity", "bladesinthedark": "p<PERSON><PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "siberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkmerah", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON>", "oldschoolrunescape": "runes<PERSON>ja<PERSON>l", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "bluesduniatulang", "adventurequest": "pet<PERSON>anga<PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "perma<PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "permainanperan", "finalfantasy9": "finalfantasy9", "sunhaven": "surgamatahari", "talesofsymphonia": "kisahsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "kotaobrakabrik", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "gematerhubung", "darksoul": "<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "laincide", "mountandblade": "gunungdanpedang", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "pilarkeabadian", "palladiumrpg": "palladiumrpg", "rifts": "celah", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fabelbabterhil<PERSON>", "hiveswap": "hiveswap", "rollenspiel": "permainanperan", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "bintanglapangan", "oldschoolrevival": "kebangkitanoldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kerajaanhati1", "ff9": "ff9", "kingdomheart2": "kerajaanhati2", "darknessdungeon": "penjar<PERSON>_kegelapan", "juegosrpg": "gamerpg", "kingdomhearts": "kera<PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "panenharvestella", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON>la", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitarcadia", "shadowhearts": "<PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "hembusanapi4", "mother3": "ibu3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "permainanperan", "roleplaygame": "permainanperan", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "gameharrypotter", "pathfinderrpg": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2e": "penjelajah2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "asaldragontua", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "perbur<PERSON><PERSON>ale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "kovenhatibayangan", "bladesoul": "jiwayang<PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "kerajaanakandatang", "awplanet": "awplanet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "taktikfinalfantasy", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magis<PERSON><PERSON>", "blackbook": "blackbook", "skychildrenoflight": "anaklangitcahaya", "gryrpg": "gryrpg", "sacredgoldedition": "edisiemasagung", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "permainangothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gaminrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "kotakabut", "indierpg": "indierpg", "pointandclick": "titikdanklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "takterpisahkan", "freeside": "<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "k<PERSON><PERSON>bisa", "monsterhunter": "pem<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "<PERSON><PERSON><PERSON><PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremasi", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "pemburumonsterbangkit", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "p<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "permainananime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "penyantapgod", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataabadi", "princessconnect": "princessconnect", "hexenzirkel": "<PERSON>ing<PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "kristal", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindonesia", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "egamesid", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaparapencitaimpian", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "si<PERSON><PERSON>hra<PERSON>", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "uji<PERSON>uji", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "kompetisivalorant", "t3arena": "t3arena", "valorantbr": "valorantid", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "tinggal4mati", "left4dead2": "left4dead2", "valve": "katup", "portal": "portal", "teamfortress2": "timpertahanan2", "everlastingsummer": "musimpanjang", "goatsimulator": "simulatege<PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetkebebasan", "transformice": "transformice", "justshapesandbeats": "hanyabentukdantakatan", "battlefield4": "battlefield4", "nightinthewoods": "malamdihu<PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risikemurujadidua", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "selam<PERSON>data<PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "l<PERSON><PERSON><PERSON><PERSON>", "stray": "terasing", "battlefield": "medanpertempuran", "battlefield1": "medanpertempuran1", "swtor": "swtorr", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "eyeb", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "simulatormeja", "partyhard": "pestaterus", "hardspaceshipbreaker": "pemecahkapalberat", "hades": "hades", "gunsmith": "tuka<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "terjebakdenganjester", "dinkum": "dinkum", "predecessor": "pendahulu", "rainworld": "duniarain", "cavesofqud": "guaqad", "colonysim": "simkoloni", "noita": "noita", "dawnofwar": "fajarperang", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "gelapdanlebihgelap", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "simdating", "yaga": "yaga", "cubeescape": "k<PERSON><PERSON><PERSON><PERSON><PERSON>_diri", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "kotaseru", "citiesskylines": "pemandangankota", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "snorer<PERSON>ner", "libraryofruina": "perpustakaanruina", "l4d2": "l4d2", "thenonarygames": "permainannonary", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "be<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "ayamkudapalingkeren", "dialtown": "dialtown", "smileforme": "tersenyumuntukku", "catnight": "malamkucing", "supermeatboy": "supermeatboy", "tinnybunny": "k<PERSON><PERSON><PERSON><PERSON>l", "cozygrove": "kampung<PERSON><PERSON>", "doom": "doom", "callofduty": "panggiltugas", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kodi", "borderlands": "<PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "panggildutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "pas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "perburuanpertarungan", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "gabung<PERSON>m", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "perangpemanggilan", "codzombies": "zombiecod", "mirrorsedge": "tepicermin", "divisions2": "divisions2", "killzone": "zonakill", "helghan": "hel<PERSON>", "coldwarzombies": "zombie<PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "kode<PERSON>as", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "perangmoderen", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "balik4<PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "massafek", "systemshock": "guncangansystem", "valkyriachronicles": "kronikvalkyria", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "ceri<PERSON><PERSON>a", "doometernal": "doometernal", "centuryageofashes": "zamanab<PERSON>", "farcry4": "farcry4", "gearsofwar": "gair<PERSON>ang", "mwo": "mwo", "division2": "divisi2", "tythetasmaniantiger": "tyharimautasmania", "generationzero": "generasihilang", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "perangmodernelit2", "blackops1": "blackops1", "sausageman": "sausageman", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "nyeriapa", "warface": "wajahperang", "crossfire": "bakutembak", "atomicheart": "<PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "kebeb<PERSON>n", "battlegrounds": "medanpertempuran", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "permainanfps", "convertstrike": "konversiserangan", "warzone2": "warzone2", "shatterline": "shatterline", "blackopszombies": "zombiesblackops", "bloodymess": "berantakanbanget", "republiccommando": "republikkomando", "elitedangerous": "bahayasukabintang", "soldat": "soldat", "groundbranch": "cabanglapangan", "squad": "squad", "destiny1": "destiny1", "gamingfps": "gamingsantai", "redfall": "redfall", "pubggirl": "cewekpubg", "worldoftanksblitz": "duniatankblitz", "callofdutyblackops": "panggildutytentarahitam", "enlisted": "terdaftar", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "dunia<PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "gajipanggih2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgceh", "titanfall2": "titanfall2", "soapcod": "sabu<PERSON>d", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "turnamentga<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "juaragempa", "halo3": "halo3", "halo": "halo", "killingfloor": "lantaidua", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "peca<PERSON><PERSON>", "neonwhite": "neonputih", "remnant": "sisa", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "kembali", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "manbayangan", "quake2": "gempa2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "medanpertempuran3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "keropos", "conqueronline": "taklukkankota", "dauntless": "berani", "warships": "kapalperang", "dayofdragons": "harinaga", "warthunder": "warthunder", "flightrising": "naikpesawat", "recroom": "ruangrehat", "legendsofruneterra": "legend<PERSON>run<PERSON><PERSON>", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "fantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON>", "crossout": "coret", "agario": "agario", "secondlife": "kehid<PERSON><PERSON>ked<PERSON>", "aion": "aion", "toweroffantasy": "menarafantasi", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "ikatanisaac", "dragonageinquisition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "sampah", "newworld": "<PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirate101", "honorofkings": "keh<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "pertempuranstarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "pertempuranbintangwars2", "phigros": "phi<PERSON>s", "mmo": "mmorpg", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "chat3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasik", "worldofwarcraft": "duniametawars", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "abukreator", "riotmmo": "riotmmo", "silkroad": "jalanseiden", "spiralknights": "pahlawanspiral", "mulegend": "mullegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "nabinaga", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "malaikatonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikternama", "grandfantasia": "fantasiagrande", "blueprotocol": "blueprotocol", "perfectworld": "dunia<PERSON><PERSON><PERSON><PERSON>", "riseonline": "bangkitonline", "corepunk": "corepunk", "adventurequestworlds": "petualanganquestdunia", "flyforfun": "terbanguntukseru", "animaljam": "animaljam", "kingdomofloathing": "kerajaankekonyolan", "cityofheroes": "kotapahlawan", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "untukkehormatan", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "pejuang<PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "tidakadakebayangan", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "rajapejuang", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "permainantempurretro", "blasphemous": "nista", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "permainantempur", "cyberbots": "botsiber", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "pertarunganterakhir", "poweredgear": "peralatankekuatan", "beatemup": "<PERSON>ggelamkan", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "pertempurankompetitif", "killerinstinct": "instingpembunuh", "kingoffigthers": "rajapejuang", "ghostrunner": "ghostrunner", "chivalry2": "kesopanan2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sekuelhollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "beritasilksong", "silksong": "silksong", "undernight": "undernight", "typelumina": "typelum<PERSON>", "evolutiontournament": "turnamenevolusi", "evomoment": "evomoment", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "kisahberseria", "bloodborne": "bloodborne", "horizon": "horizon", "pathofexile": "jalankekehidupanexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "takberujung", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "terkenalburuk", "playstationbuddies": "temanplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "boogakragu", "gta4": "gta4", "gta": "gta", "roguecompany": "perusahaanmalapetaka", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "dewa<PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitmenjadimanusia", "beatsaber": "beats<PERSON>r", "rimworld": "duniakrim", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "tropywisata", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "timtabrakanbalap", "fivepd": "limapd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "mainplaystation", "samuraiwarriors": "samuraiwarriors", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "pencarianpria", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2<PERSON><PERSON>", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warharmmer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psikonot", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "medanperang", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "bintangtakterbatas", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "perombak<PERSON><PERSON>", "americanmcgeesalice": "alice<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligaker<PERSON><PERSON>", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tvsampah", "skycotl": "langitboo", "erica": "erica", "ancestory": "kakeknenek", "cuphead": "cuphead", "littlemisfortune": "sedihkecil", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "<PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultusdomba", "duckgame": "permainanbebek", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "longdrive", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviophile", "underearth": "underearth", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "kubanggelap", "pizzatower": "pizzatower", "indiegame": "gameindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "kebenaranatautantangan", "game": "permainan", "rockpaperscissors": "gunting<PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "berani", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "pilihangangka", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "permainansantai", "datinggames": "permainandating", "freegame": "perma<PERSON><PERSON><PERSON>is", "drinkinggames": "permainanminum", "sodoku": "sudoku", "juegos": "permainan", "mahjong": "mahjong", "jeux": "permainan", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "perma<PERSON><PERSON><PERSON>", "jeuxdemots": "perma<PERSON><PERSON><PERSON>", "juegosdepalabras": "perma<PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "permainaninteraktif", "amtgard": "amtgard", "staringcontests": "pertandingantatapan", "spiele": "main", "giochi": "game", "geoguessr": "geoguessr", "iphonegames": "permainaniphone", "boogames": "boogames", "cranegame": "permainanakcrane", "hideandseek": "petak<PERSON>et", "hopscotch": "ular_acha", "arcadegames": "permainanklasik", "yakuzagames": "permainanyakuza", "classicgame": "permainanklasik", "mindgames": "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "guessthe<PERSON><PERSON>", "galagames": "galagames", "romancegame": "per<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "perma<PERSON><PERSON><PERSON>", "4xgames": "4xgame", "gamefi": "gamefi", "jeuxdarcades": "permainanarcade", "tabletopgames": "permainantabletop", "metroidvania": "metroidvania", "games90": "game90an", "idareyou": "sayatantangkamu", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "permainanbalap", "ets2": "ets2", "realvsfake": "asliv<PERSON><PERSON><PERSON>", "playgames": "mainkanpermainan", "gameonline": "gameonline", "onlinegames": "permainanonline", "jogosonline": "permainanonline", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "perma<PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiigames", "highscore": "skortertinggi", "jeuxderôles": "permainanperan", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "permainantanya", "gioco": "permainan", "managementgame": "per<PERSON>ina<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "per<PERSON><PERSON>nobjtersem<PERSON>i", "roolipelit": "ruli<PERSON>rung", "formula1game": "permainanformula1", "citybuilder": "pembuatkota", "drdriving": "drdriving", "juegosarcade": "permainanarcade", "memorygames": "per<PERSON>ina<PERSON><PERSON>mor<PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "permainanpinball", "oldgames": "permaina<PERSON><PERSON>a", "couchcoop": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "pertanyaansan<PERSON>", "gameo": "gameo", "lasergame": "permainanlasers", "imessagegames": "permainanimessage", "idlegames": "permainantakberdaya", "fillintheblank": "isititiktitik", "jeuxpc": "permainanpc", "rétrogaming": "rétrogaming", "logicgames": "permainanlogika", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "permainantradisional", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "permainanberbasistext", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "permainanmaling", "lawngames": "permainalanggarang", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "sepakbolameja", "tischfußball": "mejafootball", "spieleabende": "malamspil", "jeuxforum": "permainanforya", "casualgames": "permainancasual", "fléchettes": "<PERSON><PERSON>", "escapegames": "permainanpelarian", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "perma<PERSON><PERSON><PERSON>", "játék": "boo", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "permainanmobil", "onlineplay": "permainanonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "ma<PERSON>ber<PERSON>in", "pursebingos": "dompetbingo", "randomizer": "acak", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gimpc", "socialdeductiongames": "per<PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "gameisometrik", "goodoldgames": "permaina<PERSON><PERSON>a", "truthanddare": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "permainanvirtual", "romhack": "romhack", "f2pgamer": "gamerf2p", "free2play": "gratis2main", "fantasygame": "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "permainandrifts", "gamesotomes": "gamesotomes", "halotvseriesandgames": "halotvseriesandgames", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "apaajadenganmesin", "everywheregame": "permainandimanamana", "swordandsorcery": "pedangdanilmu", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON>ikber<PERSON><PERSON>", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "permainanpc", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "permainantempo", "minaturegames": "permainan<PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "cintadiribermain", "gamemodding": "moddinggame", "crimegames": "permainankriminal", "dobbelspellen": "dobbelspellen", "spelletjes": "permainan", "spacenerf": "spacenerf", "charades": "tebakgerak", "singleplayer": "p<PERSON><PERSON><PERSON><PERSON>", "coopgame": "per<PERSON><PERSON>nkooperatif", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "mainbikin", "kingdiscord": "rajadiscord", "scrabble": "scrabble", "schach": "<PERSON>ur", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "warisanpandemi", "camelup": "k<PERSON><PERSON><PERSON><PERSON>", "monopolygame": "permainanmon<PERSON><PERSON>", "brettspiele": "perma<PERSON><PERSON><PERSON><PERSON>", "bordspellen": "permainanmeja", "boardgame": "permainanpapan", "sällskapspel": "permainansosial", "planszowe": "renc<PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "permainanpapan", "zombicide": "zombicide", "tabletop": "mejapermainan", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "sambungkempat", "heroquest": "heroquest", "giochidatavolo": "permainanmeja", "farkle": "farkle", "carrom": "carrom", "tablegames": "permainantable", "dicegames": "perma<PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridisosial", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "protokol<PERSON><PERSON>ismarvel", "cosmicencounter": "pertemuankosmik", "creationludique": "kreasiasyik", "tabletoproleplay": "permainanmeja", "cardboardgames": "perma<PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "horo<PERSON><PERSON><PERSON>", "switchboardgames": "permainanswitchboard", "infinitythegame": "infinitythegame", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "jalanturundannaik", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "juegodemesa", "planszówki": "permainankartu", "rednecklife": "<PERSON><PERSON><PERSON><PERSON>", "boardom": "bosan", "applestoapples": "<PERSON><PERSON>ke<PERSON><PERSON>", "jeudesociété": "booper<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "papanpermainan", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "permainansosial", "twilightimperium": "twilightimperium", "horseopoly": "kudaopoly", "deckbuilding": "membangundek", "mansionsofmadness": "rumahbesarkehilangan", "gomoku": "gomoku", "giochidatavola": "perma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "bayanganbrimstone", "kingoftokyo": "r<PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "táblajátékok", "battleship": "kapalperang", "tickettoride": "tike<PERSON><PERSON><PERSON>", "deskovehry": "mejaboo", "catán": "catatan", "subbuteo": "subbuteo", "jeuxdeplateau": "permainansendi", "stolníhry": "<PERSON><PERSON><PERSON><PERSON>rten", "xiángqi": "<PERSON>ur", "jeuxsociete": "permainansosial", "gesellschaftsspiele": "permainansosial", "starwarslegion": "legionstarwars", "gochess": "gocatur", "weiqi": "weiqi", "jeuxdesocietes": "permainansosial", "terraria": "teraria", "dsmp": "dsmp", "warzone": "zonaperang", "arksurvivalevolved": "arksurvivalevolved", "dayz": "hariz", "identityv": "identitiv", "theisle": "<PERSON><PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "penjagakubur", "callofcthulhu": "panggilancthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "pulau<PERSON>et", "valheim": "valheim", "planetcrafter": "penjagaplanet", "daysgone": "<PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologis", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "gelappanjang", "ark": "ark", "grounded": "tenang", "stateofdecay2": "keadaankeadaan2", "vrising": "vrising", "madfather": "madfather", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "jalanturuntitan", "frictionalgames": "permaina<PERSON><PERSON><PERSON><PERSON>", "hexen": "hexen", "theevilwithin": "kejahatandidalam", "realrac": "realrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "belakangruang", "empiressmp": "empiressmp", "blockstory": "ceritablok", "thequarry": "tebing", "tlou": "tlou", "dyinglight": "cahayahilang", "thewalkingdeadgame": "gamethewalkingdead", "wehappyfew": "kitacumasedikit", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "kehidupanstateofsurvival", "vintagestory": "ceritavintage", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathege", "alisa": "alisa", "westlendsurvival": "bertahandiwestlend", "beastsofbermuda": "binatangber<PERSON>da", "frostpunk": "frostpunk", "darkwood": "kayugelap", "survivalhorror": "hororsur<PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "keretakosong", "lifeaftergame": "hidupsetelahgame", "survivalgames": "permainansurvival", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "perangku", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "menangiscaritakut", "raft": "<PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nenek", "littlenightmares2": "mimpiburukkecil2", "signalis": "tandai", "amandatheadventurer": "mandatheadventurer", "sonsoftheforest": "<PERSON>ak<PERSON><PERSON>", "rustvideogame": "permainanrust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "isolasialien", "undawn": "undawn", "7day2die": "7hari2mati", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "boosurvive", "propnight": "malamprop", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "kematianverse", "cataclysmdarkdays": "hariharihitamcataclysm", "soma": "soma", "fearandhunger": "taku<PERSON>danlapar", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "zamankegelapan", "clocktower3": "clocktower3", "aloneinthedark": "sendiridikegelapan", "medievaldynasty": "dinastiabadpertengahan", "projectnimbusgame": "projectnimbusgame", "eternights": "eternights", "craftopia": "kerajinankota", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "dominasidunia", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "pem<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "cintawarhammer40k", "wh40": "wh40", "warhammer40klove": "cintawarhammer40k", "warhammer40klore": "lorewarhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdesertgelap", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "su<PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "sukavindicare", "iloveassasinorum": "akusukaassasinorum", "templovenenum": "templovenenum", "templocallidus": "temlocallidus", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "zamanperadaban", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerusiaatsigmar", "civilizationv": "peradabanv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "rentangsayap", "terraformingmars": "mengubahmars", "heroesofmightandmagic": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "komandersuprême", "ageofmythology": "zamanmitologi", "args": "argumen", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "di<PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON>", "civilization6": "peradaban6", "warcraft2": "warcraft2", "commandandconquer": "perintahkansekuatnya", "warcraft3": "warcraft3", "eternalwar": "perangabadi", "strategygames": "permainanstrategi", "anno2070": "anno2070", "civilizationgame": "permainansivilisasi", "civilization4": "peradaban4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totalwar", "travian": "travian", "forts": "forts", "goodcompany": "<PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "lebihcepatdaricahaya", "forthekings": "untukra<PERSON>", "realtimestrategy": "strategirealwaktu", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kerajaanduakulon", "eu4": "eu4", "vainglory": "kesombongan", "ww40k": "ww40k", "godhood": "keilahianku", "anno": "anno", "battletech": "teknobattle", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "kelasalgebraserudave", "plagueinc": "plagueinc", "theorycraft": "teorimembuat", "mesbg": "mesbg", "civilization3": "peradaban3", "4inarow": "4dalamrentang", "crusaderkings3": "crusaderkings3", "heroes3": "pahlawan3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "zamanimperium2", "disciples2": "murid2", "plantsvszombies": "plantavszombie", "giochidistrategia": "permainanstrategi", "stratejioyunları": "strate<PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "zamankeajaiban", "dinosaurking": "rajadinosaurus", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "hatibaja4", "companyofheroes": "perusahaanher<PERSON>", "battleforwesnoth": "perang<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "bookotaimpianku", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "bebekgoosegoose", "phobies": "phobies", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "berbasisgiliran", "bomberman": "bomberman", "ageofempires4": "zamanimperium4", "civilization5": "peradaban5", "victoria2": "victoria2", "crusaderkings": "rajapejuang", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "perangbintangeim<PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulmail", "shiningforce": "kekuatanbersinar", "masterduel": "duelmaster", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transporttycoon", "unrailed": "unrailed", "magicarena": "arenaajaib", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "kerajaanupland", "galaxylife": "keh<PERSON><PERSON><PERSON>laksi", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "bunuhpuncak", "battlecats": "kucingpertarungan", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "butuhkecepatan", "needforspeedcarbon": "perlucepatspeedcarbon", "realracing3": "balapannyata3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "losims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "kembalin<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologikudahitam", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "limalamdifreddy", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "malammimpianggap", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "littlemissfortune", "projectzero": "projectzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "haite<PERSON>gga", "helloneighbor2": "haitetangga2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "permainanteror", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartuuntukmanusia", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "namakode", "dixit": "dixit", "bicyclecards": "kartusepeda", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legend<PERSON><PERSON><PERSON>a", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kuncipembangunan", "cardtricks": "<PERSON><PERSON><PERSON><PERSON>", "playingcards": "kartugames", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kardutrading", "pokemoncards": "kartupokemon", "fleshandbloodtcg": "dagingdanhormontcg", "sportscards": "kartusport", "cardfightvanguard": "pertempurankartuvanguard", "duellinks": "duellinks", "spades": "sekop", "warcry": "teriakanperang", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteri", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kart<PERSON>ugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "duelyugioh", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "penyihirdark", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "buraco", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomandan", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "permainankartu", "mtgjudge": "mtgjudge", "juegosdecartas": "permainankartu", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "permainankartu", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "batangspirit", "battlespiritssaga": "sagaperjuanganbattlespirits", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "zoliky", "facecard": "facecard", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "pembangunkartu", "marvelchampions": "marvelchampions", "magiccartas": "magiccartas", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicorntidakstabil", "cyberse": "si<PERSON>e", "classicarcadegames": "permainanarkadeklasik", "osu": "osu", "gitadora": "gitadora", "dancegames": "perma<PERSON><PERSON><PERSON>", "fridaynightfunkin": "fridaynightfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proyekmirai", "projectdiva": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockthedead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "gamerirama", "stepmania": "stepmania", "highscorerythmgames": "permainanrhythmketingtinggi", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "surgaritme", "hypmic": "hypmic", "adanceoffireandice": "tariantalapida<PERSON><PERSON>", "auditiononline": "audisionline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "permaina<PERSON>us", "cryptofthenecrodancer": "kriptonekrodancer", "rhythmdoctor": "dokterirama", "cubing": "kubus", "wordle": "wordle", "teniz": "tenis", "puzzlegames": "permainanpuzzle", "spotit": "<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "tebaklogika", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "tekatekipikiran", "rubikscube": "kubusrubik", "crossword": "katasilang", "motscroisés": "puzzlekata", "krzyżówki": "tebakka<PERSON>", "nonogram": "nonogram", "bookworm": "cintab<PERSON>", "jigsawpuzzles": "puzzlenasik", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "tekateki", "riddles": "tebakan", "rompecabezas": "puzzler", "tekateki": "tekateka", "inside": "<PERSON><PERSON>m", "angrybirds": "b<PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "simulasibreakout", "minesweeper": "pajanganminesweeper", "puzzleanddragons": "tebakandragons", "crosswordpuzzles": "tebakgambard<PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "permainangardenscapes", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "permainanpelarian", "3dpuzzle": "3dtebak", "homescapesgame": "permainanhomescapes", "wordsearch": "pencariankata", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "c<PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "k<PERSON>syangmustahil", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "puzzlecocok3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kubikrubik", "cuborubik": "kubusrubik", "yapboz": "yapboz", "thetalosprinciple": "prinsi<PERSON><PERSON><PERSON><PERSON>", "homescapes": "homescapesindonesia", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "permainantycoon", "cubosderubik": "kubusrubik", "cruciverba": "tanyapuzzle", "ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "katatebak", "buscaminas": "bajakmines", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON>", "turnipboy": "turnipboy", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "nggakadaapaapanya", "guessing": "tebak", "nonograms": "nonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "tebaktebaksilang", "syberia2": "syberia2", "puzzlehunt": "perburuanpuzzle", "puzzlehunts": "perburuanpuzzle", "catcrime": "kucingajaib", "quebracabeça": "puzzle", "hlavolamy": "hlavolamy", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON>rak<PERSON>", "autodefinidos": "autodefinisi", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON>", "carto": "kartun", "untitledgoosegame": "gamangs<PERSON>oo", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "kubusrubik", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "portalgame", "bilmece": "boobilmeces", "puzzelen": "<PERSON><PERSON>", "picross": "picross", "rubixcube": "kubusrubik", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoli", "futurefight": "pertempuranmasadepan", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "serig<PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "kerajaancookierun", "alchemystars": "bintangalkimia", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "kotasaya", "arknights": "arknightid", "colorfulstage": "panggungberwarna", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON>", "fireemblemheroes": "pahlawanfireemblem", "honkaiimpact": "honkaiimpact", "soccerbattle": "pertandinganfutbal", "a3": "a3", "phonegames": "permainanhandphone", "kingschoice": "p<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "ceritapenja<PERSON>", "petrolhead": "pecintamobil", "tacticool": "taktikeren", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "k<PERSON><PERSON><PERSON>ling<PERSON><PERSON>", "craftsman": "pengrajin", "supersus": "supercuriga", "slowdrive": "perjalanandeket", "headsup": "siapsiap", "wordfeud": "wordfeud", "bedwars": "perangkasur", "freefire": "freefire", "mobilegaming": "permainanmobil", "lilysgarden": "taman<PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "taktikteampilbertempur", "clashofclans": "pertempurandesa", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "goyangdanbersembunyi", "ml": "ml", "bangdream": "bangdream", "clashofclan": "perangklan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "legendnagamania", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "cintadalamkantong", "androidgames": "gameandroid", "criminalcase": "kasuskriminal", "summonerswar": "perangpanggil", "cookingmadness": "keg<PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaangel", "lordsmobile": "lordsmobile", "tinybirdgarden": "kebunburungkecil", "gachalife": "gachalife", "neuralcloud": "awan<PERSON><PERSON>", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "robotperang", "mirrorverse": "cerminverse", "pou": "pou", "warwings": "sayapperang", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "waktufun", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "masuk", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "hukumanabuawكثيرة", "petpals": "<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "serigala", "runcitygame": "runcitygame", "juegodemovil": "permainanhandphone", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimik", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "panggildragon", "shiningnikki": "nikkiceor", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "pertarunganbayangan3", "limbuscompany": "perusahaanlimbus", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "katadengansahabat2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "kisahsempurna", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobile", "harvesttown": "<PERSON><PERSON><PERSON>", "perfectworldmobile": "duniasempurnamobile", "empiresandpuzzles": "kerajaandansudoku", "empirespuzzles": "tebakpuzzle", "dragoncity": "kotanaga", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileid", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "airmatameme", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "g<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "mama<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldhidup", "soulseeker": "<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ceritamoon<PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "legendarineverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegend", "timeraiders": "timeraiders", "gamingmobile": "gamingmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "pertarungankucing", "dnd": "dnd", "quest": "misi", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "duniakegelapan", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "klubromance", "d20": "d20", "pokemongames": "perma<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonmerah", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "tim<PERSON>t", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monsterkantong", "nuzlocke": "nuzlocke", "pokemonplush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "timmystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "rom<PERSON><PERSON><PERSON><PERSON>", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokemondazzle", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "tangan<PERSON>i", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "tid<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "masterpokémon", "pokémonsleep": "tid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "pemburuberkilau", "ajedrez": "<PERSON>ur", "catur": "<PERSON>ur", "xadrez": "<PERSON>ur", "scacchi": "<PERSON>ur", "schaken": "skak", "skak": "skak", "ajedres": "<PERSON>ur", "chessgirls": "c<PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzdunia", "jeudéchecs": "<PERSON>ur", "japanesechess": "cat<PERSON>jepang", "chinesechess": "<PERSON><PERSON><PERSON>a", "chesscanada": "caturcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "kesempatan", "rook": "rook", "chesscom": "caturcom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "pemandudungeon", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "legendavoxmachina", "doungenoanddragons": "dengendra<PERSON><PERSON>", "darkmoor": "gelapmoor", "minecraftchampionship": "juarakontesminecraft", "minecrafthive": "sarangminecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modminecraft", "mcc": "mcc", "candleflame": "nyalacahaya", "fru": "fru", "addons": "tambahan", "mcpeaddons": "addonmcpe", "skyblock": "bloklangit", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodifikasi", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "kotaminecraft", "pcgamer": "pcgamer", "jeuxvideo": "permainanvideo", "gambit": "gambit", "gamers": "gamers", "levelup": "tingkatkanlevelmu", "gamermobile": "gamermobile", "gameover": "gameover", "gg": "gg", "pcgaming": "gamingpc", "gamen": "gamen", "oyunoynamak": "oyunnyaasik", "pcgames": "gamempc", "casualgaming": "permainansantai", "gamingsetup": "setuppermainan", "pcmasterrace": "penguasapc", "pcgame": "pcgame", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "gamingvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerid", "gameplays": "permainan", "consoleplayer": "pemainkonsol", "boxi": "boxi", "pro": "pro", "epicgamers": "gamerepik", "onlinegaming": "perma<PERSON><PERSON><PERSON>", "semigamer": "semigamer", "gamergirls": "gamercewek", "gamermoms": "ibugamer", "gamerguy": "gamerbek", "gamewatcher": "penontongame", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamercewe", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "t<PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "misi", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerlansia", "cozygaming": "gaming<PERSON>man", "gamelpay": "gamelpay", "juegosdepc": "gamepc", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "palsu", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "permainanhidupnormal", "gamepc": "permainanpc", "girlsgamer": "gamercewek", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON>", "gamegirl": "gamercewek", "chicasgamer": "gamercewek", "gamesetup": "persiapangame", "overpowered": "terlalukuat", "socialgamer": "gamersosial", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "timku", "republicofgamers": "republikgamer", "aorus": "aorus", "cougargaming": "permainancougar", "triplelegend": "triplelegend", "gamerbuddies": "temangamer", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "gamerkristen", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamercasual", "89squad": "89tim", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "pemaingame", "wspólnegranie": "mainbareng", "mortdog": "mortdog", "playstationgamer": "gamerplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamersehat", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "gamercewek", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON>", "humanfallflat": "jatuhmanusia", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musiknintendo", "sonicthehedgehog": "son<PERSON><PERSON><PERSON><PERSON>", "sonic": "sonik", "fallguys": "fallguys", "switch": "be<PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON>ar", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "penga<PERSON><PERSON><PERSON>a", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "airmataraja", "walkingsimulators": "simu<PERSON><PERSON><PERSON>", "nintendogames": "gametnintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "mi<PERSON><PERSON>", "harvestmoon": "bulanpanen", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "na<PERSON>salamliar", "myfriendpedro": "temanped<PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51game", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "cerita", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategitriangular", "supermariomaker": "pembuatsupermario", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "kanidmerah", "vanillalol": "vanillalol", "wildriftph": "wildriftid", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "beperang", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "lo<PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsindonesia", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcuy", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "ger<PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "permainanfortnite", "gamingfortnite": "gamingselamafortnite", "fortnitebr": "fortnitebr", "retrovideogames": "permaina<PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "gamehoror", "videogamemaker": "pembuatvideogame", "megamanzero": "megamanzero", "videogame": "permainanvideo", "videosgame": "video게임", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcades": "arena_game", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulatorpertanian", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxindonesia", "robloxdeutsch": "robloxindonesia", "erlc": "erlc", "sanboxgames": "permainansandbox", "videogamelore": "loregamevideo", "rollerdrome": "rollerskate", "parasiteeve": "parasitiseve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "landskapmim<PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON>", "amordoce": "cintaman<PERSON>", "videogiochi": "permainanvideo", "theoldrepublic": "republiklama", "videospiele": "permainanvideo", "touhouproject": "proyektouhou", "dreamcast": "mimpicast", "adventuregames": "permainanakpetualangan", "wolfenstein": "wolfenstein", "actionadventure": "aks<PERSON>etualangan", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "permainanret<PERSON>", "retroarcade": "arcaderetro", "vintagecomputing": "komputervintage", "retrogaming": "permainanret<PERSON>", "vintagegaming": "permainanvintage", "playdate": "mainbareng", "commanderkeen": "komandankeen", "bugsnax": "bugsnax", "injustice2": "keadilan2", "shadowthehedgehog": "bayanganbajinganjari", "rayman": "rayman", "skygame": "per<PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "curam", "mystgames": "mystgamez", "blockchaingaming": "permainanblok<PERSON>in", "medievil": "medievil", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "outrun", "bloomingpanic": "kepanikanmeledak", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "hororgaming", "monstergirlquest": "quest<PERSON><PERSON><PERSON><PERSON>", "supergiant": "supergiant", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "simpertanian", "juegosviejos": "permaina<PERSON><PERSON>a", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "novelvisual", "visualnovels": "novelvisual", "rgg": "rgg", "shadowolf": "senyumserigala", "tcrghost": "tcrhantu", "payday": "<PERSON><PERSON><PERSON><PERSON>", "chatherine": "catherine", "twilightprincess": "putrise<PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "kotakpasir", "aestheticgames": "permainanaestetik", "novelavisual": "novelavisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "permainanret<PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolusip<PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "kuncipedang", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsesekali", "novelasvisuales": "novelavisual", "robloxbrasil": "robloxindonesia", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "permainanvideo", "videogamedates": "kencanvideogame", "mycandylove": "cint<PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "haritanga<PERSON><PERSON><PERSON>", "maniacmansion": "maniacmansion", "crashracing": "balapan<PERSON>rash", "3dplatformers": "platformer3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "per<PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "gamepakai", "offmortisghost": "offmortisghost", "tinybunny": "k<PERSON><PERSON><PERSON><PERSON>l", "retroarch": "retroarch", "powerup": "tingkat<PERSON><PERSON>a", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "cepatflash", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkade", "f123": "f123", "wasteland": "padanggundul", "powerwashsim": "simbersihkanpowerwash", "coralisland": "<PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "dunia<PERSON>us", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "fusisepakbola", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "logamterpelintir", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simu<PERSON>i", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "samdan<PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "duniaajaibonline", "skylander": "skylander", "boyfrienddungeon": "ruang<PERSON><PERSON>", "toontownrewritten": "toontownditulisulang", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "kekacauanperkotaan", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "videopartygame", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "simulasispasangkang", "legacyofkain": "warisankain", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "makananandgamevideo", "oyunvideoları": "videogame", "thewolfamongus": "se<PERSON>ala<PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulatransportasi", "horizonworlds": "duniahorizon", "handygame": "permainan<PERSON><PERSON>is", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "gametua", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "agenkekacauan", "songpop": "lagu<PERSON>", "famitsu": "famitsu", "gatesofolympus": "gerbangolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "bintangpemberontak", "indievideogaming": "gameindie", "indiegaming": "indiegaming", "indievideogames": "gameindie", "indievideogame": "permainanindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "proyekl", "futureclubgames": "permainanfutureclub", "mugman": "mugman", "insomniacgames": "gamersinsomnia", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "daftartugas", "gamebacklog": "daftargame", "gamingbacklog": "daftargame", "personnagejeuxvidéos": "karaktergamevideo", "achievementhunter": "pencarikemenangan", "cityskylines": "cakrawalakota", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "anjingnakal", "beastlord": "rajabinatang", "juegosretro": "permainanret<PERSON>", "kentuckyroutezero": "jalankentuckyzero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "ostvideogame", "dragonsync": "sinkrondragon", "vivapiñata": "vivapinata", "ilovekofxv": "sukakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "gila", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesedih", "darkerthanblack": "lebihgelapdarilaut", "animescaling": "animescaling", "animewithplot": "animeyangkeren", "pesci": "pesci", "retroanime": "animevintage", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80an", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "darklord", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000an", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonemusim1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "coveranime", "thevisionofescaflowne": "visiescaflowne", "slayers": "pem<PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90an", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "ikanpisang", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "apiangkatan", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "di<PERSON><PERSON><PERSON><PERSON>", "fairytail": "fairytail", "dorohedoro": "dorohedoro", "vinlandsaga": "sagavinland", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasit", "punpun": "punpun", "shingekinokyojin": "serangantitans", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodimermaid", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "manghoror", "romancemangas": "mangacinta", "karneval": "karnaval", "dragonmaid": "<PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "j<PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indeksajaibtertentu", "sao": "sao", "blackclover": "semangatzaitun", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioritaswonderegg", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosimik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeolahraga", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyaevil", "shounenanime": "animepria", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "anaklakidanmakhluk", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "caramenjagabunda", "fullmoonwosagashite": "fullmoonwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "cewekhiscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "gadolgirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amitii", "sailorsaturn": "lauta<PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animejadul", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animehorror", "fruitsbasket": "keranjangbuah", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "cint<PERSON><PERSON>p", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thepromisedneverland", "monstermanga": "monstermanga", "yourlieinapril": "kamuberdodipadab<PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "buku<PERSON><PERSON>u", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "penjaralautdalam", "jojolion": "jojo<PERSON>", "deadmanwonderland": "k<PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "perma<PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "pencap<PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "garis<PERSON><PERSON>", "toyoureternity": "untukkeabadianmu", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blueperiod", "griffithberserk": "g<PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "<PERSON>se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "di<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detektifconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "kesatriavampir", "mugi": "mugi", "blueexorcist": "pem<PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "g<PERSON><PERSON><PERSON>", "thesevendeadlysins": "tu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "sekolahpenjara", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "ciumanxadik", "grandblue": "grandblue", "mydressupdarling": "dressupdarlingku", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "al<PERSON><PERSON><PERSON>", "swordartonlineabridge": "pedanga<PERSON><PERSON><PERSON><PERSON>", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "lang<PERSON><PERSON><PERSON><PERSON>", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "roman<PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "pembunuhsetanuntukpedang", "bloodlad": "da<PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "bintangsejalan", "romanceanime": "animeromantis", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekorragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "saga<PERSON>dland", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON>is", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "pesta<PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejepang", "animespace": "ruang<PERSON>nime", "girlsundpanzer": "cewekundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "dubanime", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "ksatriahzodiak", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "boodariyarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "jatungmanga", "deliciousindungeon": "lezatdi<PERSON>ngeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "catatanragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "overgear", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ateliertopipenyihir", "lansizhui": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loskabayanzodiac", "animeshojo": "animeshojo", "reverseharem": "reverseharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "guruhebatoonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "bossbapakku", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "<PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "bloodplusanime", "bloodcanime": "animedarah", "bloodc": "da<PERSON><PERSON>", "talesofdemonsandgods": "ceritadariiblisdandewa", "goreanime": "goreanime", "animegirls": "girani<PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxterburuk", "splatteranime": "splaateranime", "splatter": "ciprat", "risingoftheshieldhero": "bangkitnyapahlawanperisai", "somalianime": "animesomalia", "riodejaneiroanime": "animerio<PERSON><PERSON><PERSON>", "slimedattaken": "slimedidedap", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "liarliar", "supercampeones": "superchampions", "animeidols": "idolanime", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "panggilanmalam", "bakuganbrawler": "brawler<PERSON><PERSON><PERSON>", "bakuganbrawlers": "brawler<PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON>", "shadowgarden": "shadowgarden", "tsubasachronicle": "tsubasachronicle", "findermanga": "pencarimanga", "princessjellyfish": "putijellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "ciumsurga", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevers", "persocoms": "persocom", "omniscientreadersview": "pandanganpembacabisa", "animecat": "k<PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "rekomendasianime", "openinganime": "mangaopening", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "kome<PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotraksa<PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "es", "jojobizarreadventures": "petualanganjojobizarre", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemiliter", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "kotaanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "pet<PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "sekolahmenengahdxd", "goku": "goku", "broly": "broly", "shonenanime": "animecowok", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "perma<PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kor<PERSON><PERSON><PERSON>i", "onepieceanime": "animeonepiece", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "one<PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "pembalasdendam", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efekjoyboy", "digimonstory": "ceritad<PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalokalips", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonsempurna", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecomid", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "penyihirterbang", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "cumana<PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "jalanallsaints", "recuentosdelavida": "ceritaseharihari"}