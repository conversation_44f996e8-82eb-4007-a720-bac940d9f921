{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "kognitivefunksjoner", "psychology": "psykologi", "philosophy": "filos<PERSON><PERSON>", "history": "historie", "physics": "fysikk", "science": "vitenskap", "culture": "kultu", "languages": "språk", "technology": "teknologi", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologimemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "morsom", "videos": "videoer", "gadgets": "gadgets", "politics": "politikk", "relationshipadvice": "relasjonsråd", "lifeadvice": "livsråd", "crypto": "krypto", "news": "nyheter", "worldnews": "internasjonalenyheter", "archaeology": "arkeologi", "learning": "<PERSON><PERSON><PERSON>", "debates": "debatterer", "conspiracytheories": "konspirasjonsteorier", "universe": "univers", "meditation": "meditasjon", "mythology": "mytologi", "art": "kunst", "crafts": "håndverk", "dance": "dans", "design": "design", "makeup": "sminke", "beauty": "skjønnhet", "fashion": "mote", "singing": "synge", "writing": "skriving", "photography": "fotografi", "cosplay": "kostymespill", "painting": "maleri", "drawing": "tegning", "books": "<PERSON><PERSON><PERSON>", "movies": "filmer", "poetry": "poesi", "television": "<PERSON><PERSON><PERSON><PERSON>", "filmmaking": "filmproduksjon", "animation": "animasjon", "anime": "anime", "scifi": "scifi", "fantasy": "fantasi", "documentaries": "dokumentarer", "mystery": "mysterie", "comedy": "komedie", "crime": "kriminalitet", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "skrekk", "romance": "romanse", "realitytv": "realitytv", "action": "action", "music": "musikk", "blues": "blues", "classical": "klassisk", "country": "land", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latinsk", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rapp", "reggae": "reggae", "rock": "rockmusikk", "techno": "tekno", "travel": "reise", "concerts": "konserter", "festivals": "festivaler", "museums": "museer", "standup": "standup", "theater": "teater", "outdoors": "<PERSON><PERSON><PERSON><PERSON>", "gardening": "hagear<PERSON>d", "partying": "fester", "gaming": "gaming", "boardgames": "bordspill", "dungeonsanddragons": "dungeonsanddragons", "chess": "dungeonsanddragons", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "mat", "baking": "baking", "cooking": "matlaging", "vegetarian": "vegetarianer", "vegan": "veganer", "birds": "fugler", "cats": "katter", "dogs": "hunder", "fish": "fisker", "animals": "dyr", "blacklivesmatter": "blacklivesmatter", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminisme", "humanrights": "menneskerettigheter", "lgbtqally": "lgbtalliert", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transallierte", "volunteering": "frivilligarbeid", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "baskettball", "boxing": "boksing", "cricket": "cricket", "cycling": "sykling", "fitness": "kondisjon", "football": "fotball", "golf": "golf", "gym": "gym", "gymnastics": "gymnast<PERSON><PERSON>", "hockey": "ishockey", "martialarts": "kampsport", "netball": "nettball", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON><PERSON>", "skateboarding": "rull<PERSON>rettkjøring", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snowboard", "surfing": "surfing", "swimming": "<PERSON><PERSON><PERSON><PERSON>", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "dykking", "hiking": "vandring", "capricorn": "steinbukken", "aquarius": "<PERSON>nmann<PERSON>", "pisces": "fiskene", "aries": "væren", "taurus": "tyren", "gemini": "t<PERSON><PERSON><PERSON>", "cancer": "krepsen", "leo": "<PERSON><PERSON><PERSON>", "virgo": "jomfru", "libra": "vekten", "scorpio": "skorpionen", "sagittarius": "skytten", "shortterm": "kortsiktig", "casual": "avslappet", "longtermrelationship": "langtidsforhold", "single": "singel", "polyamory": "polyamori", "enm": "etiskikkemonogamt", "lgbt": "lhbt", "lgbtq": "lgbtq", "gay": "homofil", "lesbian": "lesbisk", "bisexual": "biseksuell", "pansexual": "panseksuell", "asexual": "asek<PERSON>ell", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "vaktbikkjer", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "soulreaver", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiv", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "solnedgangsoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "åpenverden", "heroesofthestorm": "helterifstormen", "cytus": "cytus", "soulslike": "sjelereise", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "stam<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "lordeneiriket2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "fargevore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "juegoderol", "witcher": "<PERSON><PERSON><PERSON><PERSON>", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksoulz", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "konsekvenser", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "karakterlag", "immersive": "<PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasygamleklassikere", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivasjon", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "suckerforlove", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampyrernemaskeparade", "dimension20": "dimensjon20", "gaslands": "gassland", "pathfinder": "stifinnere", "pathfinder2ndedition": "banelegende2utgave", "shadowrun": "<PERSON><PERSON><PERSON>", "bloodontheclocktower": "blodpå<PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "tyngdekraftstorm", "rpg": "rollespill", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "énskudd", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overhode", "yourturntodie": "dinturtilådø", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "k<PERSON>llermiljø", "gurps": "gurps", "darkestdungeon": "mørkestehule", "eclipsephase": "eclipsefase", "disgaea": "disgaea", "outerworlds": "ytreverdener", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingavisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastikriderne", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "nattbyen", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madnesskamp", "jaggedalliance2": "jaggedalliance2", "neverwinter": "aldriv<PERSON>er", "road96": "vei96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamriddere", "forgottenrealms": "glemteverdener", "dragonlance": "dragehåndsverk", "arenaofvalor": "arena<PERSON>rverdi", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "barnavlyset", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "linje2", "digimonworld": "digimonverden", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "økopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanvers", "fracturedthrones": "brutte_troner", "horizonforbiddenwest": "horisontforbudtvest", "twewy": "twewy", "shadowpunk": "skyggestil", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "lasteepoke", "starfinder": "stjernefinder", "goldensun": "gullsol", "divinityoriginalsin": "guddommelighetensoriginalsynd", "bladesinthedark": "bladenimørket", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrød", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "faltorden", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ondeland", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "gammelskolerunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "gudd<PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "gårdrollespill", "oldworldblues": "gamleverdenblues", "adventurequest": "eventyrjakt", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rollespillspill", "roleplayinggames": "rollespillspill", "finalfantasy9": "finalfantasy9", "sunhaven": "solhavn", "talesofsymphonia": "fortellingerfrasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "dagsmørke", "torncity": "torncity", "myfarog": "minfarog", "sacredunderworld": "sacredunderverden", "chainedechoes": "kjededechoes", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "søkkespill", "othercide": "<PERSON><PERSON><PERSON><PERSON>", "mountandblade": "fjellogblad", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "søyleneavevighet", "palladiumrpg": "palladiumrpg", "rifts": "riftser", "tibia": "tibia", "thedivision": "divisjonen", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "ulvenapokaly<PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "lille<PERSON><PERSON>", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorhjertet", "fable3": "fable3", "fablethelostchapter": "fabeldetaptekapittel", "hiveswap": "hiveswap", "rollenspiel": "rollespill", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "stjernemyr", "oldschoolrevival": "gammelskolenfølesrett", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savage<PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kongedømmehjertet1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "mørketsdungeon", "juegosrpg": "rollespillspill", "kingdomhearts": "kjærlighetsslåsskamp", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "villhjerteslag", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "skyeravarcadia", "shadowhearts": "<PERSON>ggehjerter", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennydritt", "breathoffire4": "breatheild4", "mother3": "mor3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "rollespillspill", "roleplaygame": "rollespillspill", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON>ksehjertet", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "veiviserrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampyrlamaskerade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "jak<PERSON>yal<PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterjegerverden", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "skyggehjertecovenant", "bladesoul": "<PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "rikekommer", "awplanet": "awplaneten", "theworldendswithyou": "verdenavsluttermeddeg", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "døendelys2", "finalfantasytactics": "finalfantasytaktikk", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "svartbok", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "sacredgoldutgave", "castlecrashers": "slottkra<PERSON><PERSON>", "gothicgame": "gothicspill", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "spøkelsetrengertokyo", "fallout2d20": "fallout2d20", "gamingrpg": "spillrpg", "prophunt": "prophunt", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "byensdis", "indierpg": "indierpg", "pointandclick": "pekeogklikk", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "udelbar", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7everkrise", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "dødsveientilcanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "monstermelder", "fireemblem": "ildsymbol", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "spøkelsefråtsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterjegeropp", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonaryspill", "tacticalrpg": "taktiskrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "evigsonate", "princessconnect": "prinsessekontakt", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON>", "cristales": "krystaller", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketlæring", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valorantindisk", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efotball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "drømmernesliga", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efotball", "dreamhack": "d<PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "sybersport", "crazyraccoon": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotsjefene", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkonkurranse", "t3arena": "t3arena", "valorantbr": "valorantno", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "halvliv", "left4dead": "venstrefor_død", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "geitesimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "frihetsplaneten", "transformice": "transformice", "justshapesandbeats": "bareformerogslag", "battlefield4": "battleground4", "nightinthewoods": "nattibeskogen", "halflife2": "halflife2", "hacknslash": "hacknslå", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "fareforregn2", "metroidvanias": "metroidvanias", "overcooked": "overkokt", "interplanetary": "interplanetarisk", "helltaker": "he<PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dvergtfestning", "foxhole": "revvegg", "stray": "stray", "battlefield": "slagsfelt", "battlefield1": "battlefield1", "swtor": "swtornorge", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON>", "eyeb": "øye", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "bordspillsimulator", "partyhard": "festhardt", "hardspaceshipbreaker": "hardromskipbryter", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "fangetmedjester", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON>", "rainworld": "regnverden", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "krigensdawn", "minionmasters": "minionmestere", "grimdawn": "grimdawn", "darkanddarker": "mørkereogmørkere", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "sjelensarbeider", "datingsims": "datingsims", "yaga": "yaga", "cubeescape": "kubeflukt", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nyby", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "virtuellkenopsia", "snowrunner": "sn<PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "ruinabibliotek", "l4d2": "l4d2", "thenonarygames": "nonaryspillene", "omegastrikers": "omegastrikers", "wayfinder": "veiviser", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "roligplastikkand", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatkyllinghest", "dialtown": "dialbyen", "smileforme": "smileformeg", "catnight": "kattekveld", "supermeatboy": "supermeatboy", "tinnybunny": "tinny<PERSON><PERSON>", "cozygrove": "koseligskog", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "regnbue6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "jordforsvarsstyrke", "huntshowdown": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "spøkelsesgjennombrudd", "grandtheftauto5": "grandtheftauto5", "warz": "krigz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "sluttilaget", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "opprørsandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codk<PERSON>ssone", "callofdutywarzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codzombies": "codzombier", "mirrorsedge": "speilkan<PERSON>", "divisions2": "divisions2", "killzone": "drapssonen", "helghan": "<PERSON><PERSON><PERSON><PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "krysskode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernekrigføring", "neonabyss": "neonavgrunn", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalsnegl", "primalcarnage": "primalcarnage", "worldofwarships": "verdenavkrigsskip", "back4blood": "back4blood", "warframe": "krigsram<PERSON>", "rainbow6siege": "rainbow6belagering", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "systemsjokk", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "hulehistorie", "doometernal": "doometernal", "centuryageofashes": "århundretsbrenning", "farcry4": "farcry4", "gearsofwar": "krigsgirder", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tydentasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "gåinnibygningen", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernekrigføring2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetogclank", "chexquest": "chexquest", "thephantompain": "spøkelsessmerte", "warface": "krigsansikt", "crossfire": "krysseld", "atomicheart": "atomhjertet", "blackops3": "blackops3", "vampiresurvivors": "vampyroverlevende", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "fri<PERSON>t", "battlegrounds": "bataljefelt", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON>", "gamepubg": "spillpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsoflibert", "juegosfps": "spillfps", "convertstrike": "konverterstreik", "warzone2": "krigssone2", "shatterline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "blackopszombies", "bloodymess": "bloodyrot", "republiccommando": "republikkommandør", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "gjeng", "destiny1": "destiny1", "gamingfps": "spillfps", "redfall": "rø<PERSON><PERSON>", "pubggirl": "<PERSON><PERSON><PERSON><PERSON>", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "innsatt", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "panserkjerne", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinasfantasiverden", "halo2": "halo2", "payday2": "betalingsdag2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubnor", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgnorge", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "spøkelseskode", "csplay": "cslek", "unrealtournament": "uvirkeligtturnering", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "grenseområder2", "counterstrike": "motangrep", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "skjelvchampions", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonwhite", "remnant": "rester", "azurelane": "azurelane", "worldofwar": "krigsenverden", "gunvolt": "gunvolt", "returnal": "returall", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "slagmark3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rust", "conqueronline": "erobreonline", "dauntless": "utenbange", "warships": "krigsskip", "dayofdragons": "dragonsdag", "warthunder": "warthunder", "flightrising": "flygoppløft", "recroom": "recrommet", "legendsofruneterra": "legenderavruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "j<PERSON><PERSON>n", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "tankverden", "crossout": "kry<PERSON><PERSON>", "agario": "agario", "secondlife": "andreliv", "aion": "aion", "toweroffantasy": "toweravfantasi", "netplay": "nettspill", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superdyrroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ridderonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "bindingenavisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "kodeåre", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>", "newworld": "nyverden", "blackdesertonline": "blackdesertonline", "multiplayer": "flermedspillere", "pirate101": "pirat101", "honorofkings": "ærentilkongene", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmorpg", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassisk", "worldofwarcraft": "worldofwarcraft", "warcraft": "krigcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "mobaspill", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "ka<PERSON><PERSON>o", "silkroad": "silkeveien", "spiralknights": "spiralknightz", "mulegend": "mulegende", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "dragensprofet", "grymmo": "grymmo", "warmane": "varmane", "multijugador": "flerspiller", "angelsonline": "engelsonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversenettspill", "growtopia": "veksttopia", "starwarsoldrepublic": "starwarseldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "perfektverden", "riseonline": "b<PERSON><PERSON><PERSON><PERSON>", "corepunk": "kjernehvisk", "adventurequestworlds": "eventyrjaktverden", "flyforfun": "flyforfun", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "r<PERSON><PERSON><PERSON>latter", "cityofheroes": "byenshelter", "mortalkombat": "mortalkombat", "streetfighter": "gatekjempe", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "skyldiggear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "gatekriger6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "gatepårage", "mkdeadlyalliance": "mkdødeligallianse", "nomoreheroes": "nomerehel<PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kongenavkjempere", "likeadragon": "likenadrage", "retrofightinggames": "retrospillfighting", "blasphemous": "blasfemisk", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "krigenmotmonstrene", "jogosdeluta": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberroboter", "armoredwarriors": "rustningskrigere", "finalfight": "sistekamp", "poweredgear": "kraftutstyr", "beatemup": "slåssespill", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "killerinstinkt", "kingoffigthers": "kongenavkrigere", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ridderlig2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongspill", "silksongnews": "silksongnytt", "silksong": "silksong", "undernight": "undernatt", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolusjonsturnering", "evomoment": "evomoment", "lollipopchainsaw": "kjærestepinnesaks", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "historierfraberseria", "bloodborne": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizon": "horisont", "pathofexile": "veienutavexil", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "blodbåren", "uncharted": "ukjentewegs", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "the<PERSON><PERSON><PERSON>", "infamous": "beryktet", "playstationbuddies": "playstationvenner", "ps1": "ps1", "oddworld": "merkeland", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "rogueselskap", "aisomniumfiles": "aisomniumfiler", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "g<PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "skattkiste", "detroitbecomehuman": "detroitblirmenneske", "beatsaber": "beats<PERSON>r", "rimworld": "rimverden", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turisttrofé", "lspdfr": "lspdfr", "shadowofthecolossus": "skyggenavkolossene", "crashteamracing": "crashteamracing", "fivepd": "fempd", "tekken7": "tekken7", "devilmaycry": "djævlenkangråte", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "spillerstation", "samuraiwarriors": "samuraiogkrigere", "psvr2": "psvr2", "thelastguardian": "densistevokteren", "soulblade": "s<PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "menneskejakt", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "skyggehjerter2pakt", "pcsx2": "pcsx2", "lastguardian": "sistevokter", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "spillpass", "armello": "armello", "partyanimal": "festdyr", "warharmmer40k": "krigshammer40k", "fightnightchampion": "kampkveldshelt", "psychonauts": "psykonauter", "mhw": "mhw", "princeofpersia": "prinsavpersia", "theelderscrollsskyrim": "elderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "slagside", "dontstarvetogether": "ikkesultannsamme", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stjernereise", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "husflipper", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "kongedømmenesliga", "fable2": "fabel2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "søppeltv", "skycotl": "skycotl", "erica": "erica", "ancestory": "forfedre", "cuphead": "cuphead", "littlemisfortune": "lilleulykke", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterkjærlighet", "projectzomboid": "prosjektzomboid", "ddlc": "ddlc", "motos": "motorer", "outerwilds": "ytterverden", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultavlambet", "duckgame": "<PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "towerunite": "tårnforene", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "langtur", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviophile", "underearth": "underjordisk", "assettocorsa": "assettocorsa", "geometrydash": "geometriutfordring", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalromprogram", "kenshi": "kenshi", "spiritfarer": "åndefarer", "darkdome": "mørkdome", "pizzatower": "pizzatårn", "indiegame": "indiespill", "itchio": "itchio", "golfit": "golfit", "truthordare": "sannhetel<PERSON><PERSON><PERSON>ring", "game": "spill", "rockpaperscissors": "stein<PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolino", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "v<PERSON>g", "scavengerhunt": "skat<PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "santellerfalsk", "beerpong": "beerpong", "dicegoblin": "terninggoblin", "cosygames": "koseligespill", "datinggames": "datingspill", "freegame": "grat<PERSON><PERSON><PERSON>", "drinkinggames": "drikkelek", "sodoku": "sodoku", "juegos": "spill", "mahjong": "mahjong", "jeux": "spill", "simulationgames": "simuleringsspill", "wordgames": "ordspill", "jeuxdemots": "ordspill", "juegosdepalabras": "ordspill", "letsplayagame": "laosspelettspill", "boredgames": "k<PERSON><PERSON><PERSON>leker", "oyun": "spill", "interactivegames": "interaktivespill", "amtgard": "amtgard", "staringcontests": "blikkonkurranser", "spiele": "spill", "giochi": "spill", "geoguessr": "geoguessr", "iphonegames": "i<PERSON>nespill", "boogames": "boospill", "cranegame": "kranespillet", "hideandseek": "kattogmus", "hopscotch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "arkadespill", "yakuzagames": "yakuzaspill", "classicgame": "klassiskspill", "mindgames": "tankespill", "guessthelyric": "gjettsangtekst", "galagames": "galagames", "romancegame": "romansespill", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "tungvridninger", "4xgames": "4xspill", "gamefi": "spillfin", "jeuxdarcades": "arkadespill", "tabletopgames": "bordspill", "metroidvania": "metroidvania", "games90": "spill90", "idareyou": "jegutfordrerdeg", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "racer<PERSON>ill", "ets2": "ets2", "realvsfake": "ektevsfalsk", "playgames": "spillspill", "gameonline": "<PERSON><PERSON><PERSON><PERSON>", "onlinegames": "nettspill", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "skrevetrollespill", "playaballgame": "spillaballspill", "pictionary": "tegneleken", "coopgames": "coopspill", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "rollespill", "burgergames": "burgerleker", "kidsgames": "barn<PERSON>ker", "skeeball": "skiball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "konkurranse", "tcgplayer": "tcgspiller", "juegodepreguntas": "spørsmålsleken", "gioco": "spill", "managementgame": "lederspill", "hiddenobjectgame": "skjultobjektspill", "roolipelit": "rulleg<PERSON>s", "formula1game": "formel1spill", "citybuilder": "<PERSON><PERSON><PERSON><PERSON>", "drdriving": "drivingdrift", "juegosarcade": "arcadespill", "memorygames": "minnes<PERSON>ll", "vulkan": "vulkan", "actiongames": "actionspill", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "flipperspill", "oldgames": "gammelspill", "couchcoop": "sofacoop", "perguntados": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameo": "spillob", "lasergame": "laserspill", "imessagegames": "imessagegames", "idlegames": "idlespill", "fillintheblank": "fyllut<PERSON><PERSON><PERSON>", "jeuxpc": "pcspill", "rétrogaming": "rétrogaming", "logicgames": "logikkspill", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "kjendisleker", "exitgames": "utgangsspill", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "spilleogdrepe", "traditionalgames": "t<PERSON><PERSON><PERSON><PERSON><PERSON>ker", "kniffel": "terningkast", "gamefps": "spillfps", "textbasedgames": "tekstbasertespill", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospill", "thiefgame": "tyvspill", "lawngames": "<PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "bordfotball", "tischfußball": "bordfotball", "spieleabende": "spillkvelder", "jeuxforum": "spillforum", "casualgames": "casualspill", "fléchettes": "dartskudd", "escapegames": "flyktspill", "thiefgameseries": "tyvspillserien", "cranegames": "kranespill", "játék": "spill", "bordfodbold": "bordfutbol", "jogosorte": "spillvalg", "mage": "mage", "cargames": "bilspill", "onlineplay": "nettleik", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "spillkvelder", "pursebingos": "veskebingo", "randomizer": "slump<PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammer", "gamespc": "spillpc", "socialdeductiongames": "sosialdeduksjonsspill", "dominos": "dominoer", "domino": "domino", "isometricgames": "isometriskespill", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "santellerutfordring", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "j<PERSON><PERSON><PERSON>", "jeuxvirtuel": "spillvirtuelt", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "free2play", "fantasygame": "fantasispill", "gryonline": "gryonline", "driftgame": "driftspill", "gamesotomes": "spillsåmye", "halotvseriesandgames": "halotvserierogspill", "mushroomoasis": "mushroomoase", "anythingwithanengine": "altmedenmotor", "everywheregame": "overaltspill", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "godtspillegging", "jugamos": "spiller", "lab8games": "lab8spill", "labzerogames": "labzerospill", "grykomputerowe": "spillkomputer", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "minispill", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "selvkjærlighetgaming", "gamemodding": "spillmodding", "crimegames": "kriminalspill", "dobbelspellen": "dobbelspellen", "spelletjes": "spill", "spacenerf": "romnerf", "charades": "skuespill", "singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "coopgame": "coopspill", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "hovedspill", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "sjakk", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemiarv", "camelup": "kamelspring", "monopolygame": "monopolspill", "brettspiele": "spillbrett", "bordspellen": "brettspillene", "boardgame": "brettspill", "sällskapspel": "selskapsspill", "planszowe": "brettspill", "risiko": "risiko", "permainanpapan": "brettspill", "zombicide": "zombicide", "tabletop": "brettspill", "baduk": "baduk", "bloodbowl": "blodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON>rettspill", "connectfour": "koblefire", "heroquest": "he<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "spillpådatan", "farkle": "farkle", "carrom": "carrom", "tablegames": "bordspill", "dicegames": "terningspill", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "pultspill", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmiskmøte", "creationludique": "kreativlek", "tabletoproleplay": "bretterollespill", "cardboardgames": "p<PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "byttebrettspill", "infinitythegame": "<PERSON><PERSON><PERSON>gspillet", "kingdomdeath": "kongsdød", "yahtzee": "yatzy", "chutesandladders": "rutsjebanerogstiger", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "bordspill", "planszówki": "boospill", "rednecklife": "redneckliv", "boardom": "k<PERSON><PERSON><PERSON>", "applestoapples": "eplertilepler", "jeudesociété": "societyspill", "gameboard": "spillbrett", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "spillkvelder", "twilightimperium": "twilightimperium", "horseopoly": "<PERSON><PERSON><PERSON>", "deckbuilding": "<PERSON>gg<PERSON><PERSON>", "mansionsofmadness": "herskapshusavgalskap", "gomoku": "gomoku", "giochidatavola": "spillpådekkbordet", "shadowsofbrimstone": "skyggeravbringe", "kingoftokyo": "kongenavtokyo", "warcaby": "krigsgater", "táblajátékok": "brettspill", "battleship": "battleship", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "kontorleker", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "brettspill", "stolníhry": "bretspill", "xiángqi": "sjakk", "jeuxsociete": "spillforeningen", "gesellschaftsspiele": "brettspill", "starwarslegion": "starwarslegion", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "spillkvelder", "terraria": "terraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arkoverlevelsesutviklet", "dayz": "dagz", "identityv": "identitetv", "theisle": "øya", "thelastofus": "de<PERSON><PERSON><PERSON><PERSON>", "nomanssky": "ingenmännerhimmel", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyogblekkmaskinen", "conanexiles": "conanexiles", "eft": "eft", "amongus": "b<PERSON><PERSON>", "eco": "øko", "monkeyisland": "aperøya", "valheim": "valheim", "planetcrafter": "planetkreatør", "daysgone": "da<PERSON><PERSON>ver", "fobia": "fobi", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologisk", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "bua", "grounded": "<PERSON>an<PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "galep<PERSON><PERSON>", "dontstarve": "ikkesultne", "eternalreturn": "evigt<PERSON><PERSON><PERSON>", "pathoftitans": "titansveien", "frictionalgames": "frictionalgames", "hexen": "heks", "theevilwithin": "detondeinne", "realrac": "realrac", "thebackrooms": "b<PERSON><PERSON><PERSON><PERSON>", "backrooms": "bakrommet", "empiressmp": "empiressmp", "blockstory": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "viherlykkeligefew", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "tilstandoverlevelsespill", "vintagestory": "vintagehistorie", "arksurvival": "arksurvivalkos", "barotrauma": "barotraume", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "vestlendsurvival", "beastsofbermuda": "be<PERSON><PERSON>mons<PERSON>e", "frostpunk": "frostpunk", "darkwood": "<PERSON>ø<PERSON><PERSON>", "survivalhorror": "overlevelsesskrekk", "residentevil": "residentondska<PERSON>", "residentevil2": "residentondød2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tom<PERSON>g", "lifeaftergame": "livetetterspill", "survivalgames": "overlevelsesspill", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "thiswarofmine", "scpfoundation": "scpfoundation", "greenproject": "grø<PERSON>prosjekt", "kuon": "kuon", "cryoffear": "gråtavfrykt", "raft": "<PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "grønnhelvete", "residentevil5": "residentevil5", "deadpoly": "dødpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "bestemor", "littlenightmares2": "littlenightmares2", "signalis": "signalis", "amandatheadventurer": "amandatheadventurer", "sonsoftheforest": "skogenesønner", "rustvideogame": "rustspill", "outlasttrials": "overlevprøvene", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "undawn", "7day2die": "7dager2dø", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON>j<PERSON>", "sopravvivenza": "overlevelse", "propnight": "propnatt", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampyr", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "katak<PERSON><PERSON><PERSON>ortedager", "soma": "soma", "fearandhunger": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "livetetter", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "klokketår3", "aloneinthedark": "aleneimørket", "medievaldynasty": "middelalderslektskap", "projectnimbusgame": "projectnimbusspill", "eternights": "evigekvelder", "craftopia": "håndverksland", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "verdensherredømme", "rocketleague": "rakettliga", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dvergmorder", "warhammer40kcrush": "warhammer40kforelskelse", "wh40": "wh40", "warhammer40klove": "warhammer40kelsker", "warhammer40klore": "warhammer40klore", "warhammer": "k<PERSON><PERSON><PERSON>", "warhammer30k": "krigshammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "jegelskersororitas", "ilovevindicare": "jegelskervindicare", "iloveassasinorum": "j<PERSON>lskerassasinorum", "templovenenum": "templovenenum", "templocallidus": "templa<PERSON><PERSON><PERSON>", "templomaerorus": "te<PERSON><PERSON><PERSON><PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "yrkeskillerne", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "krigsherremalstrid", "civilizationv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ittakestwo": "detsk<PERSON>bar<PERSON>", "wingspan": "vingespenn", "terraformingmars": "terraformeringmars", "heroesofmightandmagic": "helterog<PERSON><PERSON>", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "mytologienesalder", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "utpost2", "banished": "forvist", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "sivilisasjon6", "warcraft2": "warcraft2", "commandandconquer": "befalogsupre", "warcraft3": "warcraft3", "eternalwar": "e<PERSON><PERSON><PERSON><PERSON>", "strategygames": "strategispill", "anno2070": "anno2070", "civilizationgame": "sivilisasjonsspill", "civilization4": "sivilisasjon4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "sporing", "totalwar": "totalkrig", "travian": "travian", "forts": "forts", "goodcompany": "godtselskap", "civ": "civ", "homeworld": "hjemverden", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "tilkongene", "realtimestrategy": "sanntidsstrategi", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kongedømmetvåkroner", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "gudestatus", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesgøyalgebraklasse", "plagueinc": "plagueinc", "theorycraft": "teoribygging", "mesbg": "mesbg", "civilization3": "sivilisasjon3", "4inarow": "4<PERSON><PERSON><PERSON>", "crusaderkings3": "korsfarerkongene3", "heroes3": "helter3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "disipler2", "plantsvszombies": "plantervsmonstre", "giochidistrategia": "strategispill", "stratejioyunları": "strategispill", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "krigshammervermintide2", "ageofwonders": "alderavundere", "dinosaurking": "dinosaurkong", "worldconquest": "verdensherredømme", "heartsofiron4": "hjertenijern4", "companyofheroes": "helteneselskap", "battleforwesnoth": "kamp<PERSON>for<PERSON>not<PERSON>", "aoe3": "aoe3", "forgeofempires": "imperiene", "warhammerkillteam": "warhammerdrabteam", "goosegooseduck": "gåsegåsand", "phobies": "fobier", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingklashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "ytterbanelek", "turnbased": "turbase", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "sivilisasjon5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "trollformel", "starwarsempireatwar": "stjer<PERSON>rig<PERSON><PERSON>ietkriger", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulmail", "shiningforce": "strålendekraft", "masterduel": "masterduell", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportimperiet", "unrailed": "avsporet", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "planefluktfrahelvete", "uplandkingdoms": "uplandrikene", "galaxylife": "galaxyliv", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "battlekatter", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "trenger<PERSON><PERSON><PERSON>", "needforspeedcarbon": "tren<PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "tapets4", "fnaf": "fnaf", "outlast": "overleve", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON>mmertilba<PERSON>", "darkhorseanthology": "mørkeka<PERSON>", "phasmophobia": "fob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "saiko", "fatalframe": "<PERSON><PERSON>me", "littlenightmares": "littlenightmares", "deadrising": "dødsgjørningen", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "hjemmebunden", "deadisland": "dødisland", "litlemissfortune": "lille<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "pros<PERSON><PERSON><PERSON><PERSON>", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON>", "helloneighbor2": "heinabo2", "gamingdbd": "gamingdbd", "thecatlady": "kattfruen", "jeuxhorreur": "skrekkspill", "horrorgaming": "horrorspill", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kortmotmenneskeheten", "cribbage": "kribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodeord", "dixit": "dixit", "bicyclecards": "sykkelkort", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "kortspill", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "nøkkesmed", "cardtricks": "korttriks", "playingcards": "spillkort", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "<PERSON><PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "handelskort", "pokemoncards": "pokemonkort", "fleshandbloodtcg": "kjøttogblodtcg", "sportscards": "sportskort", "cardfightvanguard": "kortkamprikets", "duellinks": "duellinks", "spades": "spader", "warcry": "krigsrop", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "hjertenkonge", "truco": "trucos", "loteria": "loteri", "hanafuda": "hana<PERSON>da", "theresistance": "motstandskraften", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkort", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON><PERSON><PERSON>ll", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yug<PERSON>hspill", "darkmagician": "mörkemagiker", "blueeyeswhitedragon": "blåøynehvitedrage", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kortspill", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkommandør", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kortspill", "mtgjudge": "mtgdommer", "juegosdecartas": "kortspill", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanskart", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kortspill", "carteado": "kortspillet", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "kortspill", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "zolički", "facecard": "ansiktskort", "cardfight": "kortkamp", "biriba": "biriba", "deckbuilders": "deckby<PERSON>e", "marvelchampions": "marvelchampions", "magiccartas": "magis<PERSON><PERSON>", "yugiohmasterduel": "yug<PERSON><PERSON><PERSON><PERSON>ll", "shadowverse": "skyggevers", "skipbo": "skip<PERSON>", "unstableunicorns": "ustabileunikorn", "cyberse": "cyberse", "classicarcadegames": "klassiskearkadespill", "osu": "osu", "gitadora": "gitadora", "dancegames": "danselekene", "fridaynightfunkin": "fredagskveldsfurore", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "prosjektmirai", "projectdiva": "prosjektdiva", "djmax": "djmax", "guitarhero": "g<PERSON><PERSON><PERSON><PERSON>", "clonehero": "k<PERSON><PERSON>t", "justdance": "baredans", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockdeaddei", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON>ter", "dancecentral": "dansemiljø", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "høyscorerytmspill", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "dansmedildogis", "auditiononline": "auditiononline", "itgmania": "itgmaniac", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kryptofthenecrodancer", "rhythmdoctor": "r<PERSON><PERSON><PERSON><PERSON>", "cubing": "kubing", "wordle": "ordlek", "teniz": "teniz", "puzzlegames": "puslespill<PERSON>ill", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logikkpuslespill", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "hjernetrim", "rubikscube": "rubik<PERSON><PERSON>", "crossword": "kryssord", "motscroisés": "kreuzord", "krzyżówki": "kryssord", "nonogram": "nonogram", "bookworm": "bokmal", "jigsawpuzzles": "pusle<PERSON><PERSON>", "indovinello": "gjettekonkurranse", "riddle": "g<PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON>", "rompecabezas": "puzzler", "tekateki": "tekateki", "inside": "inni", "angrybirds": "sinnefugler", "escapesimulator": "escapesimulator", "minesweeper": "minesveiper", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "hagespill<PERSON>", "puzzlesport": "pu<PERSON><PERSON><PERSON><PERSON>o", "escaperoomgames": "escaperoomspill", "escapegame": "fluktskyspill", "3dpuzzle": "3dpuslespill", "homescapesgame": "homescapesgame", "wordsearch": "ordsøking", "enigmistica": "enigmistisk", "kulaworld": "kulaverden", "myst": "myst", "riddletales": "gåtehistorier", "fishdom": "fiskedom", "theimpossiblequiz": "detumuligequizen", "candycrush": "candycrush", "littlebigplanet": "litenstorbakke", "match3puzzle": "match3puslespill", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kvirky", "rubikcube": "rubik<PERSON><PERSON>", "cuborubik": "kubekubik", "yapboz": "yapboz", "thetalosprinciple": "thetalosp<PERSON><PERSON><PERSON>t", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "gåtemeg", "tycoongames": "tykongen2023", "cubosderubik": "rubik<PERSON><PERSON>", "cruciverba": "kryssord", "ciphers": "koder", "rätselwörter": "gåteord", "buscaminas": "minesvein", "puzzlesolving": "puslespillløsning", "turnipboy": "rappløker<PERSON><PERSON>", "adivinanzashot": "adivanansjontur", "nobodies": "ingen", "guessing": "gjette", "nonograms": "nonogrammer", "kostkirubika": "kostkuruba", "crypticcrosswords": "kryptiskequi<PERSON>rd", "syberia2": "syberia2", "puzzlehunt": "puzzlejakt", "puzzlehunts": "puzzlejakter", "catcrime": "kattekriminalitet", "quebracabeça": "knusehode", "hlavolamy": "klønete", "poptropica": "poptropica", "thelastcampfire": "detsisteleketeltet", "autodefinidos": "selvdefinerte", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "karton", "untitledgoosegame": "untitledgoosegame", "cassetête": "kassettete", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrint", "tinykin": "tinykin", "rubikovakostka": "rubik<PERSON><PERSON>", "speedcube": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "biter", "portalgame": "portalspill", "bilmece": "bilmystikk", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubik<PERSON><PERSON>", "indovinelli": "gjettestykke", "cubomagico": "kubemagisk", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobil", "codm": "codm", "twistedwonderland": "vriddtrollskog", "monopoly": "monopol", "futurefight": "fremtidskamp", "mobilelegends": "mobillegender", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "kok", "lonewolf": "aleneulv", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitliv", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON>ner", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkongeriket", "alchemystars": "alchemystars", "stateofsurvival": "tilstandforyndelse", "mycity": "minby", "arknights": "arknight<PERSON>", "colorfulstage": "fargerikscene", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "k<PERSON><PERSON>ale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "skjebnegrandordre", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemhelter", "honkaiimpact": "honkaiimpact", "soccerbattle": "fotballkamp", "a3": "a3", "phonegames": "telefonspill", "kingschoice": "kongensvalg", "guardiantales": "guardiantales", "petrolhead": "<PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktiskstylish", "cookierun": "<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "uteavsløret", "craftsman": "h<PERSON>ndverker", "supersus": "supersuspicious", "slowdrive": "roligkjøring", "headsup": "headsup", "wordfeud": "wordfeud", "bedwars": "seng<PERSON><PERSON>", "freefire": "grat<PERSON><PERSON>", "mobilegaming": "mobilspill", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "k<PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "mystiskbudbringer", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "nødsentral", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayedag", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdrøm", "clashofclan": "clashofclan", "starstableonline": "stjernehestonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON>", "timeprincess": "tidprinsesse", "beatstar": "beatstar", "dragonmanialegend": "dragemanialegenden", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "lommekjærlighet", "androidgames": "androidspill", "criminalcase": "kriminalsak", "summonerswar": "invoke<PERSON><PERSON>", "cookingmadness": "matlagingsgalenskap", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "engelensliga", "lordsmobile": "lordsmobile", "tinybirdgarden": "litenfuglhage", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "minesyngendemonstre", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "speiluniverset", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futid", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobil", "ingress": "inngang", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "<PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "straffendegråhavn", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "spillavsultanene", "arenabreakout": "arenabreakout", "wolfy": "ulven", "runcitygame": "runcitygame", "juegodemovil": "mobiltspill", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimikk", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "bergogdalbaneimperiet", "grandchase": "storstafett", "bombmebrasil": "bombmegbrasil", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomeg<PERSON>", "mindustry": "tankemylder", "callofdragons": "<PERSON><PERSON><PERSON>etildrager", "shiningnikki": "skinnendenikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "veientilingenting", "sealm": "sealm", "shadowfight3": "skygnekamp3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionslaukd3", "wordswithfriends2": "ordmedvenner2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfekthistorie", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobil", "harvesttown": "h<PERSON>stingbyen", "perfectworldmobile": "perfectworldmobil", "empiresandpuzzles": "imperierog<PERSON>lespill", "empirespuzzles": "empirespuzzles", "dragoncity": "dragebyen", "garticphone": "gartictelefon", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "littlenightmare", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "evigsjelالرئيس", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobil", "arknight": "arknights", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiekastaway", "eveechoes": "<PERSON><PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "mariokartturneringen", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaklubb", "v4": "v4", "cookingmama": "kok<PERSON><PERSON><PERSON>", "cabalmobile": "klartmobil", "streetfighterduel": "gatekrigerduell", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "komigjennomdet", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "må<PERSON>kinnhist<PERSON><PERSON>", "carxdriftracingonline": "bilxdriftkjøringonline", "jogosmobile": "mobilspill", "legendofneverland": "legendofneverland", "pubglite": "pubglite", "gamemobilelegends": "spillmobillegender", "timeraiders": "tidstyver", "gamingmobile": "spillmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "battlekattane", "dnd": "dnd", "quest": "oppdrag", "giochidiruolo": "ruletspill", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "verdenavmørket", "travellerttrpg": "reisendettrpg", "2300ad": "2300efølgmed", "larp": "larp", "romanceclub": "romanseklubb", "d20": "d20", "pokemongames": "poke<PERSON><PERSON>ll", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkrystall", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "p<PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "poke<PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "pelskraft", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON><PERSON>", "teamystic": "lagmystisk", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "liten", "shinypokemon": "skinnen<PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psydduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmester", "pokémonsleep": "pokémonsøvn", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "sjakk", "catur": "ka<PERSON><PERSON><PERSON>", "xadrez": "sjakk", "scacchi": "sjakk", "schaken": "sjakkbaren", "skak": "skak", "ajedres": "sjakk", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "verdensblitz", "jeudéchecs": "s<PERSON><PERSON><PERSON>", "japanesechess": "japanskjakk", "chinesechess": "kinesisksjakk", "chesscanada": "sjakkcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "sjakkcom", "calabozosydragones": "kalabofengsler", "dungeonsanddragon": "dungeon<PERSON>g<PERSON>ger", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventyr", "darksun": "dark<PERSON><PERSON>", "thelegendofvoxmachina": "legendeneomvoxmachina", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmoor": "mørkemyra", "minecraftchampionship": "minecraftmesterskap", "minecrafthive": "minesveve", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dr<PERSON><PERSON><PERSON><PERSON>", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodder", "mcc": "mcc", "candleflame": "klyn<PERSON><PERSON>", "fru": "fru", "addons": "tillegg", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "<PERSON><PERSON><PERSON><PERSON>", "minecraftpocket": "minecraftlomme", "minecraft360": "minecraft360", "moddedminecraft": "moddetminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "mellomland", "minecraftdungeons": "minecrafthuler", "minecraftcity": "minecraftby", "pcgamer": "pcgamer", "jeuxvideo": "videospill", "gambit": "gambit", "gamers": "spillere", "levelup": "heveniveauet", "gamermobile": "gamermobil", "gameover": "gameover", "gg": "gg", "pcgaming": "pcspill", "gamen": "gamen", "oyunoynamak": "spillespill", "pcgames": "pcspill", "casualgaming": "casualgaming", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmaster<PERSON>", "pcgame": "pcspill", "gamerboy": "gamerkutt", "vrgaming": "vrspill", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "konsollspiller", "boxi": "boksi", "pro": "pro", "epicgamers": "episkegamere", "onlinegaming": "nettspill", "semigamer": "semigamer", "gamergirls": "<PERSON><PERSON><PERSON>", "gamermoms": "gamermoms", "gamerguy": "<PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "spillvakt", "gameur": "spiller", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerjentene", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "oppdrag", "alax": "alax", "avgn": "avgn", "oldgamer": "gammelgamer", "cozygaming": "koseligspill", "gamelpay": "gamelpay", "juegosdepc": "pcspill", "dsswitch": "dsswitch", "competitivegaming": "kompetitivspill", "minecraftnewjersey": "minecraftnyjersey", "faker": "falsk", "pc4gamers": "pc4gamere", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksuellgaming", "gamepc": "spillpc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "dagligjakten", "gamegirl": "<PERSON><PERSON><PERSON>", "chicasgamer": "gamer<PERSON><PERSON>", "gamesetup": "<PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "spilljam", "proplayer": "prospiller", "roleplayer": "rollespiller", "myteam": "mittl<PERSON>", "republicofgamers": "republikkenforspruttere", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "triplelegend", "gamerbuddies": "gamerkompiser", "butuhcewekgamers": "trengerjentespillere", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON>", "afk": "borte", "andregamer": "<PERSON><PERSON><PERSON><PERSON>", "casualgamer": "a<PERSON><PERSON><PERSON>tspiller", "89squad": "89gjengen", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "spillefilm", "gamertag": "spillernavn", "lanparty": "lanparty", "videogamer": "spillnerd", "wspólnegranie": "fellesspill", "mortdog": "morthund", "playstationgamer": "playstationspiller", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "helseygamer", "gtracing": "gtracing", "notebookgamer": "notatboksgamer", "protogen": "protogen", "womangamer": "dameg<PERSON><PERSON>", "obviouslyimagamer": "åpenbartjegergamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "innsamler", "humanfallflat": "menneskefallflatt", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nullflukt", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusikk", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonisk", "fallguys": "fallgutter", "switch": "bytt", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "essadvokat", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tårenetilkongeriket", "walkingsimulators": "g<PERSON>simulator<PERSON>", "nintendogames": "nintendospill", "thelegendofzelda": "thelegendofzelda", "dragonquest": "drag<PERSON><PERSON><PERSON>", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "pusteniødemarken", "myfriendpedro": "minvennpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "eselkong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spill", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "historier", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "triangelstrategi", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kastanjebæsjdag", "nintendos": "nintendos", "new3ds": "ny3ds", "donkeykongcountry2": "eselkjongland2", "hyrulewarriors": "hyr<PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioogsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "rødeulver", "vanillalol": "vanillalol", "wildriftph": "wildriftno", "lolph": "lol<PERSON>", "leagueoflegend": "lagavlegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendsvill", "adcarry": "annonsebarnskap", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsnorge", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "koblettillegendene", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexporter", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrospill", "scaryvideogames": "skumlevideospill", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videospill", "videosgame": "videospill", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "trollmann101", "battleblocktheater": "battleblockteater", "arcades": "spillehaller", "acnh": "acnh", "puffpals": "puff<PERSON><PERSON>", "farmingsimulator": "landbrukssimulator", "robloxchile": "robloxnorge", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxnorge", "robloxdeutsch": "robloxnorsk", "erlc": "erlc", "sanboxgames": "sanboxspill", "videogamelore": "videospillskatt", "rollerdrome": "rollerdrommet", "parasiteeve": "parasi<PERSON><PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "d<PERSON>ø<PERSON>land", "starcitizen": "sterci<PERSON><PERSON>", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "videospill", "theoldrepublic": "dengamlerepublikken", "videospiele": "videospill", "touhouproject": "touhouprosjekt", "dreamcast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "eventyrspill", "wolfenstein": "wolfenstein", "actionadventure": "actioneventyr", "storyofseasons": "historienomårstidene", "retrogames": "retrogames", "retroarcade": "retroarkade", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "lekedate", "commanderkeen": "kommandosugen", "bugsnax": "bugsnax", "injustice2": "urettferdighet2", "shadowthehedgehog": "skyggehoggehog", "rayman": "rayman", "skygame": "skygame", "zenlife": "zenlivet", "beatmaniaiidx": "beatmaniaiidx", "steep": "bratt", "mystgames": "mystspill", "blockchaingaming": "blockkjedelek", "medievil": "medieval", "consolegaming": "<PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "l<PERSON><PERSON><PERSON>", "bloomingpanic": "blomstrendepanikk", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "spillskrekk", "monstergirlquest": "monstergirlquest", "supergiant": "supergigant", "disneydreamlightvalle": "disneydrømlysdal", "farmingsims": "jordbrukssimuleringsspill", "juegosviejos": "g<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxspill", "interactivefiction": "interaktivfiksjon", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastebus2", "amantesamentes": "k<PERSON><PERSON><PERSON><PERSON>r<PERSON>mmer", "visualnovel": "visuellnovel", "visualnovels": "visualnoveller", "rgg": "rgg", "shadowolf": "<PERSON>ggehund", "tcrghost": "tcrspøkelse", "payday": "lønningsdag", "chatherine": "katherine", "twilightprincess": "skumringsprinsesse", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandkasse", "aestheticgames": "aestheticspill", "novelavisual": "novelavi<PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "klaging", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "bladblåserrevolusjon", "wiiu": "wiiu", "leveldesign": "nivådesign", "starrail": "stjernereise", "keyblade": "nøkkelblader", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsomeganger", "novelasvisuales": "visualserier", "robloxbrasil": "robloxnorge", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospill", "videogamedates": "<PERSON><PERSON><PERSON><PERSON>", "mycandylove": "minkaramelkelove", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "barefordi3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "tilbakekomstavberegning", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON>g<PERSON><PERSON><PERSON>", "maniacmansion": "maniacmansion", "crashracing": "crashracing", "3dplatformers": "3dplattformspill", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "gammelskolengaming", "hellblade": "hellblade", "storygames": "historiefortelling", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "lydspiller", "beyondtwosouls": "beyondtofferter", "gameuse": "spillbruk", "offmortisghost": "avlivetspøkelset", "tinybunny": "lille<PERSON>in", "retroarch": "retroarch", "powerup": "kraftopp", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafiskeeventyr", "quickflash": "rasklys", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkader", "f123": "f123", "wasteland": "ødeland", "powerwashsim": "powerwashsim", "coralisland": "korallisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfrukt", "anotherworld": "enannenverden", "metaquest": "metaquest", "animewarrios2": "animekrigere2", "footballfusion": "fotballfusjon", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "vriddmetall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "skammekolleksjon", "simulator": "simulator", "symulatory": "simulator", "speedrunner": "<PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superrobotteisen", "dcuo": "dcuo", "samandmax": "samogmax", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "kjærestefengsel", "toontownrewritten": "toontownskrevetpånytt", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "by<PERSON><PERSON>", "heavenlybodies": "himmelskeformer", "seum": "seum", "partyvideogames": "festvideospill", "graveyardkeeper": "graveyardkeeper", "spaceflightsimulator": "romfartssimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hack<PERSON>hugge", "foodandvideogames": "matogvideospill", "oyunvideoları": "spillvideoer", "thewolfamongus": "ulvenblantoss", "truckingsimulator": "lastesimulator", "horizonworlds": "horizonverdener", "handygame": "håndverkslek", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON>ll", "oldschoolvideogames": "gammeldagsevideospill", "racingsimulator": "racingsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentersomka<PERSON>", "songpop": "sangpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterjegernå", "rebelstar": "rebellstjerne", "indievideogaming": "indievideospill", "indiegaming": "indiegaming", "indievideogames": "indievideospill", "indievideogame": "indievideospill", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffortress", "unbeatable": "<PERSON><PERSON><PERSON><PERSON>", "projectl": "prosjektl", "futureclubgames": "framtidsklubbespill", "mugman": "<PERSON><PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestespill", "aperturescience": "åpningsvitenskap", "backlog": "forsinkelse", "gamebacklog": "spillbaklogg", "gamingbacklog": "gamingryggsekk", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "prestasjonsjeger", "cityskylines": "by<PERSON>er", "supermonkeyball": "superapaball", "deponia": "deponia", "naughtydog": "sinthund", "beastlord": "beastlord", "juegosretro": "retrospill", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriogblindeeskogen", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "videospillost", "dragonsync": "dragensynk", "vivapiñata": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovekofxv": "jegelskerkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "tristanime", "darkerthanblack": "mørkereennsort", "animescaling": "animeklatring", "animewithplot": "animemedhandling", "pesci": "fisk", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "mørkef<PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesesong1", "rapanime": "rapanime", "chargemanken": "lademannskapet", "animecover": "animecover", "thevisionofescaflowne": "visjonenavescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90tallet", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "jenteanime", "bananafish": "bananefisk", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toalettbundethanak<PERSON>n", "bnha": "bnhanorge", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "brannstyrke", "moriartythepatriot": "moriartypatrioten", "futurediary": "fremtidsdagbok", "fairytail": "eventyrslutt", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "laget<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "skrekkmanga", "romancemangas": "romantiskemanga", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragekjæledyr", "blacklagoon": "s<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terrafarmor", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "enmagiskind<PERSON>s", "sao": "sao", "blackclover": "svartkløver", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "krunchyroll", "aot": "aot", "sk8theinfinity": "sk8infinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamilie", "rezero": "rezero", "swordartonline": "sverd<PERSON><PERSON>nett", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioritet", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "en<PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "guttenogbeistet", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "s<PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "hvordanholdeøvelsenøyet", "fullmoonwosagashite": "fullmoonwosagashite", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "søtogsartet", "martialpeak": "kampenesvannskap", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "gjenferdsfe", "shinji": "shinji", "zerotwo": "nullto", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "mineliv", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "seilereonsatur", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "gam<PERSON><PERSON><PERSON>", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "spill", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "s<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "fruktkurv", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangaliv", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "kjærlighetsliv", "sakuracardcaptor": "sakurakortfanger", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "detlovedealdersland", "monstermanga": "monstermanga", "yourlieinapril": "dinløgneriapril", "buggytheclown": "buggyklovnen", "bokunohero": "bokunohelt", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "dyphavsfange", "jojolion": "jojo<PERSON>", "deadmanwonderland": "dødsmannsland", "bannafish": "bannafisk", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "h<PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON>", "cardcaptorsakura": "kortfan<PERSON>ak<PERSON>", "stolas": "stolas", "devilsline": "djevelenslinje", "toyoureternity": "tildeeneternitet", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blåperiode", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "hemmeligallianse", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "utradert", "bluelock": "bluelock", "goblinslayer": "go<PERSON><PERSON><PERSON>", "detectiveconan": "detektivconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampyrkjempe", "mugi": "mugi", "blueexorcist": "blåeksorsist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spionfamilien", "airgear": "luftutstyr", "magicalgirl": "magis<PERSON><PERSON><PERSON>", "thesevendeadlysins": "desyvdødeligesyndene", "prisonschool": "fengselsskole", "thegodofhighschool": "gudenforvideregående", "kissxsis": "kyssogsøstre", "grandblue": "grandblå", "mydressupdarling": "min<PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverset", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "<PERSON><PERSON><PERSON>ep<PERSON>sørga", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "romantiskmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentia", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demondrepertilsverdet", "bloodlad": "blodl<PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "brenneknyttneve", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stjerneneordnerseg", "romanceanime": "romanseanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfaktor", "cherrymagic": "cherrymagic", "housekinokuni": "husk<PERSON><PERSON><PERSON>", "recordragnarok": "spillragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tenniskongen", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "morderklassen", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "dødspar<PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskanime", "animespace": "animespace", "girlsundpanzer": "jenterundpanzer", "akb0048": "akb0048", "hopeanuoli": "håpenepil", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON>mann", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON>", "gashbell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "peachgirl": "ferskenjente", "cavalieridellozodiaco": "ka<PERSON>reneiforhold", "mechamusume": "mekaniskjente", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklubb", "dragonquestdai": "dragejaktdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "deiligidungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokkensopptak", "funamusea": "moroamuseum", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "manganime", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialerforvanskelig", "overgeared": "overgiret", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemester", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "hekselueatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaerlivet", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "<PERSON><PERSON><PERSON>v<PERSON><PERSON><PERSON>", "animeshojo": "animeshojo", "reverseharem": "omvendtharem", "saintsaeya": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "fantastisklæreroninazuka", "gridman": "<PERSON><PERSON><PERSON><PERSON>", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "sjefenminpappa", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "blodpluss", "bloodplusanime": "blodplussanime", "bloodcanime": "blodkanime", "bloodc": "blodc", "talesofdemonsandgods": "demonerogguder", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON>je<PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kråkerxverst", "splatteranime": "sø<PERSON><PERSON><PERSON>", "splatter": "sprute", "risingoftheshieldhero": "risingoftheshieldhero", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimetattaken", "animeyuri": "animeyuri", "animeespaña": "animenorge", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "barnavhvalene", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeonerne", "animeidols": "anime<PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magis<PERSON><PERSON><PERSON>", "callofthenight": "nattensinnkalling", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "magis<PERSON><PERSON><PERSON>", "shadowgarden": "skyggehage", "tsubasachronicle": "tsubasachronicle", "findermanga": "finnermanga", "princessjellyfish": "prinsesseblekksprut", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekyss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverden", "persocoms": "persocommer", "omniscientreadersview": "allvitendeslesersynspunkt", "animecat": "animekatt", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON>", "openinganime": "åpninganime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minungdomsromantiskekomedie", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamene", "voltesv": "voltesv", "giantrobots": "gigantroboter", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilkrigerggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilsuitgundam", "mech": "mek", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "dødsnotat", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojossæreventyr", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "iskald", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militæ<PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animebyen", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "drageball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonventyr", "hxh": "hxh", "highschooldxd": "videregåendedxd", "goku": "goku", "broly": "brorlig", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonjeger", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "angrepontitan", "erenyeager": "erenyeager", "myheroacademia": "minheltakademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "ve<PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "undersøkelseskorps", "onepieceanime": "onepieceanime", "attaquedestitans": "angrepsdeistitaner", "theonepieceisreal": "denettsidenervir<PERSON>ig", "revengers": "<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mob<PERSON><PERSON>", "aonoexorcist": "aonoeksorsist", "joyboyeffect": "gledesgutteeffekt", "digimonstory": "digimonhistorie", "digimontamers": "digimontamers", "superjail": "superfengsel", "metalocalypse": "metalofkalypsen", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "feilfriwebtoon", "kemonofriends": "kemonovenninner", "utanoprincesama": "utenoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "hverdagsliv", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "flyvendeheks", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsjelenesgate", "recuentosdelavida": "livetsfortellinger"}