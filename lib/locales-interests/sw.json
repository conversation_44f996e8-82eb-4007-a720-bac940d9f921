{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "<PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON>utamb<PERSON><PERSON>", "psychology": "sa<PERSON>lojia", "philosophy": "filosofia", "history": "historia", "physics": "fizikia", "science": "<PERSON><PERSON><PERSON>", "culture": "<PERSON><PERSON><PERSON><PERSON>", "languages": "lugha", "technology": "teknolojia", "memes": "meme", "mbtimemes": "memezambti", "astrologymemes": "memeza<PERSON>jimu", "enneagrammemes": "memezaenneagram", "showerthoughts": "ma<PERSON><PERSON><PERSON>pooga", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "video", "gadgets": "gajeti", "politics": "siasa", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "news": "habari", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "aikolojia", "learning": "kujifunza", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "<PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "sanaa", "crafts": "ufundi", "dance": "densi", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "mapambo", "beauty": "urembo", "fashion": "mitindo", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "kuandika", "photography": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosplay": "u<PERSON><PERSON>_kwa_mavazi", "painting": "kupakarangi", "drawing": "<PERSON><PERSON><PERSON><PERSON>", "books": "vitabu", "movies": "sinema", "poetry": "<PERSON><PERSON><PERSON>", "television": "televisheni", "filmmaking": "uta<PERSON><PERSON><PERSON><PERSON>_wa_filamu", "animation": "<PERSON><PERSON><PERSON><PERSON>", "anime": "vibonzo", "scifi": "ubu<PERSON><PERSON>_wa_k<PERSON><PERSON>si", "fantasy": "kutunga_taswira", "documentaries": "filamuzakumbukumbu", "mystery": "mamboyaaja<PERSON>", "comedy": "<PERSON><PERSON><PERSON><PERSON>", "crime": "<PERSON><PERSON><PERSON>", "drama": "vituko", "bollywood": "sinema_za_kihindi", "kdrama": "sinema_za_korea", "horror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romance": "mahaba", "realitytv": "vipindi_vya_maisha_halisi", "action": "sinema_za_vita", "music": "muziki", "blues": "muziki_wa_blues", "classical": "mseto_wa_muziki_wa_zamani", "country": "muzikiwacountry", "desi": "desi", "edm": "edm", "electronic": "muziki_wa_electronic", "folk": "mseto_wa_muziki_wa_jadi", "funk": "mseto_wa_muziki_wa_funk", "hiphop": "mseto_wa_muziki_wa_hiphop", "house": "mseto_wa_muziki_wa_house", "indie": "muziki_wa_indie", "jazz": "muziki_wa_jazz", "kpop": "muziki_wa_kpop", "latin": "muziki_wa_kilatini", "metal": "muziki_wa_metal", "pop": "mseto_wa_muziki_wa_pop", "punk": "muziki_wa_punk", "rnb": "muziki_wa_rnb", "rap": "muziki_wa_rap", "reggae": "muziki_wa_reggae", "rock": "muziki_wa_rock", "techno": "muziki_wa_techno", "travel": "<PERSON><PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON>", "festivals": "tamasha", "museums": "ma<PERSON><PERSON><PERSON>", "standup": "v<PERSON><PERSON><PERSON>_juk<PERSON>", "theater": "ukumbi_wa_sinema", "outdoors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partying": "sherehe", "gaming": "michezo", "boardgames": "michezo_ya_bao", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "chakula", "baking": "kuoka", "cooking": "kup<PERSON>", "vegetarian": "mlaji_mboga", "vegan": "<PERSON><PERSON><PERSON><PERSON>_nyama", "birds": "ndege", "cats": "paka", "dogs": "mbwa", "fish": "samaki", "animals": "wanyama", "blacklivesmatter": "maisha_ya_watu_weusi_ni_muhimu", "environmentalism": "u<PERSON><PERSON><PERSON><PERSON>_wa_mazing<PERSON>", "feminism": "ufeministi", "humanrights": "haki_za_binadamu", "lgbtqally": "mshi<PERSON>_wa_lgbtq", "stopasianhate": "acha_chuki_dhidi_ya_waasia", "transally": "wasiojitambulisha_kijinsia", "volunteering": "kujit<PERSON>a", "sports": "michezo", "badminton": "mpira_wa_vinyoya", "baseball": "<PERSON><PERSON><PERSON><PERSON>", "basketball": "mpira_wa_kikapu", "boxing": "ndondi", "cricket": "kriketi", "cycling": "kuendes<PERSON>_baiskeli", "fitness": "siha", "football": "kandanda", "golf": "gofu", "gym": "mazoezi_ya_viungo", "gymnastics": "sarak<PERSON>", "hockey": "mpira_wa_ma<PERSON>o", "martialarts": "sanaa_ya_vita", "netball": "netiboli", "pilates": "mazoezi_ya_misuli", "pingpong": "tenisi_ya_mezani", "running": "mbio", "skateboarding": "mchezo_wa_kuteleza", "skiing": "mchezo_wa_skii", "snowboarding": "kute<PERSON><PERSON>_kwenye_theluji", "surfing": "kutele<PERSON>_katika_mawimbi", "swimming": "kuo<PERSON><PERSON>", "tennis": "tenisi", "volleyball": "voliboli", "weightlifting": "kun<PERSON><PERSON><PERSON>_u<PERSON>i", "yoga": "yoga", "scubadiving": "up<PERSON><PERSON>_mbizi_wa_scuba", "hiking": "kup<PERSON>_milima", "capricorn": "mbuzi", "aquarius": "ndoo", "pisces": "samaki", "aries": "kondoo", "taurus": "ngombe", "gemini": "mapacha", "cancer": "kaa", "leo": "simba", "virgo": "mashuke", "libra": "<PERSON><PERSON><PERSON>", "scorpio": "nge", "sagittarius": "mshale", "shortterm": "<PERSON><PERSON><PERSON><PERSON>", "casual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "m<PERSON>e", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "uwazi", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "shoga", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "<PERSON><PERSON><PERSON><PERSON>", "assassinscreed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsrow": "<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "wa<PERSON><PERSON>i", "dislyte": "dislyte", "rougelikes": "mchezorougelikes", "kingsquest": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "mbwa_wanyama", "sunsetoverdrive": "juaazizikieni", "arkham": "arkham", "deusex": "mungumtu", "fireemblemfates": "moto<PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "jaz<PERSON><PERSON>", "litrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "haloinfinite": "halosihisi", "guildwars": "vitaavikundi", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "r<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "kuchunguliazindani", "jetsetradio": "jetsetradio", "tribesofmidgard": "kabilazamidgard", "planescape": "mpangowakuondoka", "lordsoftherealm2": "wababezaeneo2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medaboti", "lodsoftherealm2": "malkiawausiku2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "mchezowaroll", "witcher": "wa<PERSON><PERSON>", "dishonored": "imepatwanaibu", "eldenring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksouls": "kuz<PERSON><PERSON>l", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "mkataba", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "wazeezaedscrolls", "modding": "kuba<PERSON><PERSON><PERSON>", "charactercreation": "umbajiwausika", "immersive": "kuli<PERSON>", "falloutnewvegas": "kuangukalasvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "mchezowamwisho", "finalfantasy14": "mchezofinalfantasy14", "finalfantasyxiv": "mchezofinalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "mpenziwaupendo", "otomegames": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocar<PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "mwelekeo2ndedition", "shadowrun": "kimbiakivuli", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "mchezofainali11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "mkweli", "rpgmaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "osrs": "osrs", "overlord": "b<PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "zamuakoikufe", "persona3": "mtindo3", "rpghorror": "<PERSON><PERSON><PERSON>", "elderscrollsonline": "wanaumewaidadionline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demonsouls": "rohozaijaziliko", "mu": "boot<PERSON><PERSON>", "falloutshelter": "shelteryaanguk<PERSON>", "gurps": "gurps", "darkestdungeon": "jifinyenyezi", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON>wen<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "kifungochaisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "hashtag<PERSON><PERSON><PERSON>", "skullgirls": "<PERSON><PERSON><PERSON>jiwakich<PERSON>", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "<PERSON><PERSON><PERSON>wahog<PERSON>s", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON>", "road96": "barabara96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "michuano", "gothamknights": "v<PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "pangataka", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "kusanyarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "mtotowamwangaza", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "mgawanyiko2", "lineage2": "ukiwa2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "vitivilivyovunjika", "horizonforbiddenwest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "shatan", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "piga", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "mchana2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "<PERSON><PERSON><PERSON><PERSON>", "evillands": "inchizenzo", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "m<PERSON><PERSON><PERSON><PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "mungu", "pf2": "pf2", "farmrpg": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON>", "adventurequest": "safariyaadventure", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "michezoyakushiriki", "roleplayinggames": "michezoyakuigiza", "finalfantasy9": "finalfantasy9", "sunhaven": "juahewa", "talesofsymphonia": "hadithizasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON>ji<PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "sacredunderworld", "chainedechoes": "milangoimepigwakavu", "darksoul": "roh<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "moy<PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON>", "mountandblade": "milimanajumba", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "sarawakichat", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "mapengo", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "menejambokwenyesafari", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "mbwa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "mdogo_wood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "moyoengine", "fable3": "hadithi3", "fablethelostchapter": "hadithisurailiyopotea", "hiveswap": "hiveswap", "rollenspiel": "mchezowakuigiza", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "mchezomwisho16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "ufalme_moyo1", "ff9": "ff9", "kingdomheart2": "ufalmewaakili2", "darknessdungeon": "pangozaukov<PERSON>", "juegosrpg": "michezoyarpg", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "ufalme3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "kikundimalkavian", "harvestella": "mvu<PERSON>_n<PERSON>ri", "gloomhaven": "gloomhaven", "wildhearts": "moyopori", "bastion": "ngome", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "angazakiarcadia", "shadowhearts": "moyoz<PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "kupumzikakwamoto4", "mother3": "mama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "basiboo", "anothereden": "king<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "mchezowakuigiza", "fabulaultima": "fabulault<PERSON>", "witchsheart": "moyow<PERSON>", "harrypottergame": "mchezo<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "mchezo<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "b<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "speliujam", "dragonageorigins": "hadizakichwaýyş", "chronocross": "mambochronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "ziwaazamajiwa", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "jiraniyarpg", "shadowheartscovenant": "mkatabawamoyowakivuli", "bladesoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "ufuatiliajiwaenantumwambie", "awplanet": "awplanet", "theworldendswithyou": "du<PERSON>inamalizikwana<PERSON>", "dragalialost": "dragalialost", "elderscroll": "wazeezozulia", "dyinglight2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasytactics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "foramurpg", "golarion": "golarion", "earthmagic": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "watotoawinguwaanga", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "mchezogothic", "scarletnexus": "k<PERSON><PERSON><PERSON>sca<PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "michezoyarpg", "prophunt": "<PERSON><PERSON>", "starrails": "nyotasiri", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "mchezoindie", "pointandclick": "kit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "emilyanendambalipia", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "hatuwezikugawanyika", "freeside": "palebure", "epic7": "epic7", "ff7evercrisis": "ff7milelekrizi", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "barabarayausikilizajiacanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "motoish<PERSON>jaa", "genshinimpact": "genshinimpact", "geosupremancy": "ufalmewamaeneo", "persona5": "persona5", "ghostoftsushima": "mzukawatsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "mi<PERSON><PERSON>yakistratejia", "mahoyo": "mahoyo", "animegames": "michezoanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "sa<PERSON><PERSON><PERSON><PERSON>", "princessconnect": "mfalmeconnect", "hexenzirkel": "kikundichawachawi", "cristales": "kivuli", "vcs": "vcs", "pes": "pes", "pocketsage": "mfukon<PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "sokaelektroniki", "nba2k": "nba2k", "egames": "michezoyakielektroniki", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "katikati", "efootball": "sokaelektroniki", "dreamhack": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "ligiyaoverwatch", "cybersport": "mi<PERSON><PERSON><PERSON><PERSON>asa", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON>", "test1test": "jarib<PERSON><PERSON><PERSON><PERSON>", "fc24": "fc24", "riotgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eracing": "kuondoa", "brasilgameshow": "mchezowabrazil", "valorantcompetitive": "mpangowavalorant", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "nus<PERSON>o", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valve", "portal": "mlango", "teamfortress2": "timethika2", "everlastingsummer": "kiangazikisichokwisha", "goatsimulator": "mifugosimulators", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "uhuruplanet", "transformice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justshapesandbeats": "niumbozatakuwa<PERSON>", "battlefield4": "vita4", "nightinthewoods": "usiku_ma<PERSON>ima", "halflife2": "nusumaisha2", "hacknslash": "hacknslash", "deeprockgalactic": "maweamboni深淵", "riskofrain2": "hatariyamvua2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "mchukuziwakuzimu", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "hashtagizizizizizizizizizizizizizizizizाउनेuxизτούданных", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarfngome", "foxhole": "shim<PERSON>mbwe<PERSON>", "stray": "kipotofu", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "vitani1", "swtor": "swtor", "fallout2": "kuanguka2", "uboat": "<PERSON><PERSON><PERSON>", "eyeb": "ewe", "blackdesert": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "simulaterem<PERSON><PERSON>", "partyhard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "ktrappedwithjester", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "ulim<PERSON><PERSON><PERSON>_mvua", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonisim", "noita": "si<PERSON>i", "dawnofwar": "alfajiray<PERSON><PERSON>", "minionmasters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "gizanajiza", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconmzito", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "mwana<PERSON><PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "mche<PERSON><PERSON><PERSON>", "omegastrikers": "wa<PERSON><PERSON><PERSON><PERSON>", "wayfinder": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "buba<PERSON><PERSON><PERSON><PERSON>", "battlebit": "vitaavikosi", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "ni<PERSON><PERSON><PERSON>", "catnight": "usikuwapaka", "supermeatboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "krabbitakibunny", "cozygrove": "cozygrove", "doom": "dumu", "callofduty": "<PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "vimbunga6", "apexlegends": "mitegoapex", "cod": "kodi", "borderlands": "nafasizampaka", "pubg": "pubg", "callofdutyzombies": "witowavikosizombies", "apex": "pamoja", "r6siege": "r6siege", "megamanx": "mwanamegamx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "michezoyafarcry", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "kufukuziaghost", "grandtheftauto5": "ganditheftauto5", "warz": "wa<PERSON><PERSON><PERSON><PERSON>", "sierra117": "sierra117", "dayzstandalone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultrakill": "ultrauawa", "joinsquad": "jiungekikosi", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "mapinduzinadhoruba", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "muwindaji3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "kuh<PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "mchezowakikosicod", "callofdutywarzone": "kuitaanjeshahari<PERSON>", "codzombies": "zombizakodi", "mirrorsedge": "mkingowavioo", "divisions2": "sehemu2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "m<PERSON><PERSON>", "goldeneye007": "jichozaurora007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "vita<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "neonabyss", "planetside2": "sayari2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "kurudi<PERSON><PERSON>u", "warframe": "vita<PERSON>ta", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "mshtukowamfumo", "valkyriachronicles": "hadithizavalkyria", "specopstheline": "specopstheline", "killingfloor2": "sakafukuu2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "karneyamajivu", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "sehemu2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "ingiekatikaika<PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "vitavyakisasa2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "usowa<PERSON><PERSON>", "crossfire": "mi<PERSON>i", "atomicheart": "moy<PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "kuitanagundumapambano", "moorhuhn": "kien<PERSON>ji", "freedoom": "uhuru", "battlegrounds": "vitani", "frag": "<PERSON><PERSON><PERSON>", "tinytina": "<PERSON><PERSON>", "gamepubg": "mchezopubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearwanawauhuru", "juegosfps": "mchezofps", "convertstrike": "geuzaandamano", "warzone2": "eneo2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "ma<PERSON><PERSON><PERSON><PERSON>", "republiccommando": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "tawiëtarafa", "squad": "ku<PERSON><PERSON>a", "destiny1": "hatima1", "gamingfps": "michezofps", "redfall": "moto<PERSON><PERSON>", "pubggirl": "msichanap<PERSON>g", "worldoftanksblitz": "ulimwenguwevituzaanga", "callofdutyblackops": "simuliziyauda<PERSON>blackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON>", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "kotaimeza", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "nchizatin<PERSON><PERSON>", "halo2": "halo2", "payday2": "malipo2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgukrainia", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "safidokod", "ghostcod": "k<PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "miche<PERSON><PERSON>", "borderlands2": "mipakani2", "counterstrike": "kupambana", "cs2": "cs2", "pistolwhip": "kupiganabunduki", "callofdutymw2": "simuliziyakazimw2", "quakechampions": "washindiwaquake", "halo3": "halo3", "halo": "hhalo", "killingfloor": "<PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "<PERSON><PERSON><PERSON><PERSON>", "neonwhite": "neonwhite", "remnant": "mabaki", "azurelane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwar": "<PERSON><PERSON>mash<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "kurudi", "halo4": "halo4", "haloreach": "halofi<PERSON>a", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "tetea2", "microvolts": "microvolts", "reddead": "reddead", "standoff2": "standoff2", "harekat": "<PERSON><PERSON><PERSON>", "battlefield3": "vijijavita3", "lostark": "lostark", "guildwars2": "vituviamavita2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "<PERSON><PERSON>da", "conqueronline": "shindaonline", "dauntless": "hawagandamizi", "warships": "mashua", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "vita<PERSON><PERSON><PERSON>", "flightrising": "kuinukangaanga", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "siri", "phantasystaronline2": "nyotayafantasiamtandaoni2", "maidenless": "bilamke", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "ulimwenguwapanzers", "crossout": "<PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "ma<PERSON><PERSON>ili", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "mchezoonline", "everquest": "mwelekeo", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadonline": "reddeadonline", "superanimalroyale": "mnyamasuperroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "kufungwakwaisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "mashujaaniwaкодvein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON>ja<PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirate101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "vita<PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "vitaavifadhiwanyota2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "gumzo3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft": "vitakwamikakati", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "knightizaspiral", "mulegend": "mulegend", "startrekonline": "startrakmtandao", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "kisuonyoka", "evony": "evony", "dragonsprophet": "mtum<PERSON>wamibu<PERSON>", "grymmo": "<PERSON><PERSON><PERSON><PERSON>", "warmane": "motojanja", "multijugador": "mchezomkubwa", "angelsonline": "malai<PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "mchawiwakikosi", "growtopia": "growtopia", "starwarsoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandfantasia": "n<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "inukam<PERSON>daoni", "corepunk": "moyopunk", "adventurequestworlds": "safarizaulimwen<PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "mchezozaeneo", "kingdomofloathing": "ufalmewakuchukia", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "<PERSON><PERSON><PERSON><PERSON>", "forhonor": "kwaheshima", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "mchalambiri6", "multiversus": "multiversus", "smashbrosultimate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulcalibur": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlhalla": "brawlhalla", "virtuafighter": "mpigan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "haku<PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "kama<PERSON><PERSON>", "retrofightinggames": "mi<PERSON><PERSON>yakalezak<PERSON><PERSON><PERSON>", "blasphemous": "matusi", "rivalsofaether": "hashtag<PERSON><PERSON>wa<PERSON>er", "persona4arena": "mtindo4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "vitavy<PERSON>ster", "jogosdeluta": "mache<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberbots", "armoredwarriors": "vita<PERSON><PERSON><PERSON><PERSON>", "finalfight": "mapambanomwisho", "poweredgear": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "wapigebas<PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ushujaa2", "demonssouls": "rohozaumwa", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelhayahollowknights", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "mchezoboosilksong", "silksongnews": "habarizasilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "hadithizaberseria", "bloodborne": "<PERSON><PERSON><PERSON>", "horizon": "horizon", "pathofexile": "njiayaexile", "slimerancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "plustheplaystation", "lastofus": "lastofus", "infamous": "ma<PERSON><PERSON>", "playstationbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "mchezokwenyeps5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "<PERSON><PERSON><PERSON><PERSON>", "persona4": "mimi4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "kampanichafuu", "aisomniumfiles": "failifaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "mung<PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "hazina", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "<PERSON><PERSON><PERSON><PERSON>", "rimworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "<PERSON>uz<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivepd": "tanopd", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "mwanadamuayalia3", "devilmaycry5": "shetaniwanalilia5", "ufc4": "ufc4", "playingstation": "mchezoplaystation", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "mlinziwamwisho", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "moyozavivuli2makubaliano", "pcsx2": "pcsx2", "lastguardian": "mlinziwakatifu", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "mpangowamchezo", "armello": "armello", "partyanimal": "mwanaparty", "warharmmer40k": "warhammer40k", "fightnightchampion": "mchwaakampioni", "psychonauts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "mfalmewapersia", "theelderscrollsskyrim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "usif<PERSON><PERSON>jaapa<PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "nyo<PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "hadithi2", "xboxgamepass": "xboxgamepass", "undertale": "hadithiyaunderground", "trashtv": "televisheniyajunk", "skycotl": "anga<PERSON>o", "erica": "erica", "ancestory": "v<PERSON><PERSON>", "cuphead": "kapuhead", "littlemisfortune": "mdogobahatimbaya", "sallyface": "<PERSON><PERSON><PERSON><PERSON>", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "mradiwazomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "mbaozainje", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "dungulakondoo", "duckgame": "mchezowakiboko", "thestanleyparable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "towersasa", "occulto": "occulto", "longdrive": "safarirefu", "satisfactory": "kuri<PERSON><PERSON>", "pluviophile": "wa<PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "programuyangumi", "kenshi": "kenshi", "spiritfarer": "m<PERSON>sher<PERSON>", "darkdome": "kivuli", "pizzatower": "m<PERSON><PERSON><PERSON><PERSON>", "indiegame": "mchezoindie", "itchio": "itchio", "golfit": "golfit", "truthordare": "k<PERSON>liamakali", "game": "mchezo", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolini", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "jar<PERSON>u", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "michezokwenyeuli", "pickanumber": "chagu<PERSON><PERSON><PERSON>", "trueorfalse": "kweliauuwongo", "beerpong": "beerpong", "dicegoblin": "go<PERSON><PERSON><PERSON><PERSON>", "cosygames": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "michezoyakuchumb<PERSON>", "freegame": "mchezobure", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "michezo", "mahjong": "majiang", "jeux": "mchezo", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "tuche<PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "mchezo", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "spieli", "giochi": "michezo", "geoguessr": "geoguessr", "iphonegames": "michezoyaiphone", "boogames": "miche<PERSON><PERSON>", "cranegame": "mchezowacrane", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "zikazikamatuko", "arcadegames": "michezoarcade", "yakuzagames": "mchezoyakuza", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "michezoyaboa", "romancegame": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "mchezozayandere", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "michezo4x", "gamefi": "mchezo<PERSON>", "jeuxdarcades": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "miche<PERSON><PERSON>le", "metroidvania": "metroidvania", "games90": "mchezo90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "halisivsbandia", "playgames": "chezamichezo", "gameonline": "mchezoonline", "onlinegames": "mchezoza_mtandaoni", "jogosonline": "michezoyaonline", "writtenroleplay": "kuandikakoruhusa", "playaballgame": "chez<PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "alichopata最高得分", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwtoleojeusi", "jeuconcour": "michuano", "tcgplayer": "m<PERSON><PERSON><PERSON><PERSON>", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "mchezo", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "mchezowavituvilivyofichwa", "roolipelit": "rulipelit", "formula1game": "mchezoformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "kuendesha", "juegosarcade": "michezoarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "michezoyausik<PERSON>", "blowgames": "m<PERSON>zo<PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON>g<PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "maswali", "gameo": "gameo", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "michezoyaimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "michezapc", "rétrogaming": "mchezowakale", "logicgames": "michezoyakihisia", "japangame": "japanga<PERSON>", "rizzupgame": "mchezom<PERSON><PERSON><PERSON>", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "mchezoziwanje", "5vs5": "5dhidiya5", "rolgame": "mchezowakuigiza", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "m<PERSON>zon<PERSON><PERSON>a", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "mchezofps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "pichaingstagram", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrospel": "kurudim<PERSON><PERSON><PERSON>", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "mezaesoka", "tischfußball": "mezaamakabumbu", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "michezojukwaa", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "n<PERSON><PERSON>a", "escapegames": "michezoyaukimbizi", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "michezoyacrane", "játék": "cheza", "bordfodbold": "m<PERSON><PERSON><PERSON>", "jogosorte": "m<PERSON><PERSON><PERSON>a", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "mchezomkoja", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "kitengo_random", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "mchezocomp", "socialdeductiongames": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>chung<PERSON><PERSON>ja<PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "michezoyaisometric", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "kweli_na_challenges", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "mi<PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "free2play": "bure2cheza", "fantasygame": "mchezowa<PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "mchezowadrift", "gamesotomes": "mche<PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesandgames", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "chochotechnengine", "everywheregame": "mchezoovoteote", "swordandsorcery": "upanganachakralia", "goodgamegiving": "mchezomzurik<PERSON><PERSON>", "jugamos": "tunateka", "lab8games": "mchezozalab8", "labzerogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "chezam<PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "mchezozamchongoo", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "kupendakijamii", "gamemodding": "kubadilamagemu", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "michezo", "spacenerf": "spacenerf", "charades": "michezo", "singleplayer": "mchezajipekee", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "mchezo", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "mchezomkuu", "kingdiscord": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrabble": "scrabble", "schach": "mchezowakichwali", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>pindupindu", "camelup": "kamelnakutok<PERSON>", "monopolygame": "hashtagmonopoly", "brettspiele": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "mchezo<PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "michezo<PERSON>fu<PERSON><PERSON>ta", "zombicide": "zombicide", "tabletop": "meza", "baduk": "bad<PERSON>q", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "unganishefour", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "carrom", "tablegames": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "mkutanozaangaliko", "creationludique": "umbajiwakiufund<PERSON>", "tabletoproleplay": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "mchezowabilamwisho", "kingdomdeath": "u<PERSON>lmewak<PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "ka<PERSON><PERSON>", "juegodemesa": "meza<PERSON><PERSON><PERSON>", "planszówki": "mezai<PERSON><PERSON>zo", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "ucho<PERSON>", "applestoapples": "applestoapples", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "meza<PERSON><PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "mwangazaimperium", "horseopoly": "farasiopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "majengoyawazimu", "gomoku": "gomoku", "giochidatavola": "furaharadakule", "shadowsofbrimstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoftokyo": "mfalmewatokyo", "warcaby": "wartrendy", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "ma<PERSON><PERSON>iz<PERSON>", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "<PERSON>ian<PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "jeshilagalaktika", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "teraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "mwanamiziwama<PERSON>", "dayz": "<PERSON><PERSON><PERSON>", "identityv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "hakuna<PERSON>", "subnautica": "subnautica", "tombraider": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "ka<PERSON>tu", "eco": "ekolojia", "monkeyisland": "kisiwachakiboko", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON>mepita", "fobia": "fobia", "witchit": "witchit", "pathologic": "pathologi<PERSON>", "zomboid": "zomboid", "northgard": "nordgard", "7dtd": "7dtd", "thelongdark": "thelongdark", "ark": "ark", "grounded": "kuwai<PERSON>", "stateofdecay2": "haliyaanguka2", "vrising": "vrising", "madfather": "baba<PERSON>", "dontstarve": "usikufejaa", "eternalreturn": "kuru<PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "halisirac", "thebackrooms": "nyumba<PERSON><PERSON><PERSON>", "backrooms": "nyumbazamoyo", "empiressmp": "empiressmp", "blockstory": "hadith<PERSON><PERSON>", "thequarry": "ushanga", "tlou": "tlou", "dyinglight": "mwangazaunaosh<PERSON><PERSON>", "thewalkingdeadgame": "mchezowathewalkingdead", "wehappyfew": "tunakauwenyewe", "riseofempires": "kuinukakwafalme", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wa<PERSON><PERSON>", "breathedge": "hewa", "alisa": "alisa", "westlendsurvival": "kuishiwestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "mbaozagiza", "survivalhorror": "kutokasurvivehorror", "residentevil": "<PERSON><PERSON><PERSON><PERSON>", "residentevil2": "mtandaomuu", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tren<PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "kilis<PERSON><PERSON><PERSON><PERSON>", "raft": "mkuza", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "bibi", "littlenightmares2": "ndotochache2", "signalis": "ishara", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "watotowamsitu", "rustvideogame": "mchezowarust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "kuishikandonaaliens", "undawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "siku7kufa", "sunlesssea": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "kuishi", "propnight": "usikuwaweza", "deadisland2": "kisiwachamauti2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "kif<PERSON><PERSON>", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "hofu_njaa", "stalkercieńczarnobyla": "msakocieńczarnobyla", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "kipindichausiku", "clocktower3": "munarasaat3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "nasabazamkato", "projectnimbusgame": "mchezowaprojectnimbus", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "michezoshutayarocket", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dwarfslayer", "warhammer40kcrush": "mwanzowawarhammer40k", "wh40": "wh40", "warhammer40klove": "upendowarhammer40k", "warhammer40klore": "hadithizawarhammer40k", "warhammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "templaculexus", "vindicare": "vindicare", "ilovesororitas": "nawapendasororitas", "ilovevindicare": "napendavindicare", "iloveassasinorum": "napendaassasinorum", "templovenenum": "templevenenum", "templocallidus": "templa<PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "vizuizya<PERSON>nzi", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "zamantimuempire", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "vitaajabuwakombozi", "civilizationv": "ustaarabuv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "upanawamabawa", "terraformingmars": "kuba<PERSON><PERSON>aji<PERSON><PERSON><PERSON>", "heroesofmightandmagic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "maswala", "rime": "shai<PERSON>", "planetzoo": "zoolimwengu", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "kangalau", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "amri_na_kush<PERSON>", "warcraft3": "warcraft3", "eternalwar": "vita<PERSON><PERSON><PERSON><PERSON>", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "mchezo<PERSON><PERSON>aar<PERSON><PERSON>", "civilization4": "ustaarabu4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "v<PERSON><PERSON><PERSON>oni", "travian": "travian", "forts": "ngome", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "nyumbani", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "mkakatiwawakati", "starctaft": "starctaft", "sidmeierscivilization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomtwocrowns": "u<PERSON>lmewakutawanyo<PERSON>i", "eu4": "yae4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "u<PERSON>u", "anno": "mwaka", "battletech": "vitatech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plagueinc": "plagueinc", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "ustaarabu3", "4inarow": "4mfuatano", "crusaderkings3": "wapiganajiwanne3", "heroes3": "mashujaa3", "advancewars": "mad<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "kipindichavikosi2", "disciples2": "hashtagizawanafunzi2", "plantsvszombies": "mimeavszombies", "giochidistrategia": "giochidistrategia", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "mioyoyachuma4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "vitakwawesnoth", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goosegooseduck": "ngombeamejiyenga", "phobies": "hofu", "phobiesgame": "mchezo<PERSON>", "gamingclashroyale": "mchezoclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "njeplane", "turnbased": "zilizokaa", "bomberman": "bomberman", "ageofempires4": "wakatiwavamizi4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON>", "starwarsempireatwar": "vita<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "mkakati", "popfulmail": "popfulmail", "shiningforce": "nguvazakiangaza", "masterduel": "mashindanoyakustadi", "dysonsphereprogram": "<PERSON>uy<PERSON><PERSON><PERSON>", "transporttycoon": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "<PERSON>ku<PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "pangahewa<PERSON>", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvillem<PERSON><PERSON><PERSON>", "slaythespire": "slaythespire", "battlecats": "palaumpiga", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "wa<PERSON>zi", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "kuendesha", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "nahitajikasicarbon", "realracing3": "mashindanohalisi3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "michezobilama<PERSON><PERSON>", "ts4": "ts4", "thesims2": "simu2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "potezasims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "kufak<PERSON><PERSON><PERSON>um", "alicemadnessreturns": "aliceanarejeakwawazimu", "darkhorseanthology": "mkusanyikowapanyamwenyegiza", "phasmophobia": "kug<PERSON><PERSON><PERSON>", "fivenightsatfreddys": "usikutanokwafreddy", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "kuinukakufa", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nyumbani", "deadisland": "kisiwachakufa", "litlemissfortune": "mdogomwanam<PERSON><PERSON>ati", "projectzero": "mradi0", "horory": "horozy", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "mchokozijirani2", "gamingdbd": "mchezo<PERSON>", "thecatlady": "mwanamkatakataka", "jeuxhorreur": "mi<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "kibra", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "sokoban", "poker": "poker", "hearthstone": "<PERSON><PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "<PERSON><PERSON><PERSON><PERSON>", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "kadizakucheza", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "m<PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kadizakutrade", "pokemoncards": "kardi<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "mwa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duellinks": "duellinks", "spades": "spade", "warcry": "keleleyavikosi", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "mfalmewamioyo", "truco": "truco", "loteria": "bah<PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "kupambana", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "ka<PERSON><PERSON>gio<PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "mchezo<PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "mab<PERSON>eyeswazung<PERSON>", "yugiohgoat": "yuugioganda", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cotorro": "kotorro", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mpango<PERSON><PERSON><PERSON><PERSON>", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "karteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "zoliki", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "kup<PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "wabunifuwadeck", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "kadizamagic", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "skipaboo", "unstableunicorns": "unicornszisizonautulivu", "cyberse": "cyberse", "classicarcadegames": "michezoyakukumbukayaarcades", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "jumamosif<PERSON>in", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "mradiwako", "projectdiva": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "kloneshujaa", "justdance": "tucheze", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "pigawezawafu", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dancecentral": "dancecentral", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "michezoyakufanyawewekuji<PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "saoundvoltex", "rhythmheaven": "m<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "sikilizaonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "da<PERSON><PERSON><PERSON><PERSON>", "cubing": "k<PERSON><PERSON>a", "wordle": "neno", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "kibandikocharakamu", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "vivu", "nonogram": "nonogramu", "bookworm": "m<PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON>", "indovinello": "swali", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "puzzle", "tekateki": "tekateki", "inside": "n<PERSON>i", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "m<PERSON><PERSON>i", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "mchezowakilimo", "puzzlesport": "michezoyapuzzle", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "mchezowakukimbia", "3dpuzzle": "3dpuzzle", "homescapesgame": "mchezowahomescapes", "wordsearch": "u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmia", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "sa<PERSON>ani", "theimpossiblequiz": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "duniaikubwa<PERSON>غيرة", "match3puzzle": "puzzle3mic<PERSON>zo", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kubarubik", "cuborubik": "kubarubik", "yapboz": "yapboz", "thetalosprinciple": "kanunizabola", "homescapes": "nyumbanikwetu", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "ni<PERSON>bie<PERSON><PERSON>", "tycoongames": "mi<PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "m<PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "kuh<PERSON><PERSON>", "nonograms": "nonogramu", "kostkirubika": "kostkirubika", "crypticcrosswords": "vichocheo", "syberia2": "syberia2", "puzzlehunt": "uvirusiwapuzzle", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "uhalifuwakcats", "quebracabeça": "kichocheo", "hlavolamy": "v<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefinishi", "picopark": "picopark", "wandersong": "wandezaimbo", "carto": "katuni", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "kassette", "limbo": "limbo", "rubiks": "rubiks", "maze": "maze", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "kubezaidi", "pieces": "vipande", "portalgame": "mchezoportal", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "maswaliහدم보", "cubomagico": "kubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "ajabuzaimegeuka", "monopoly": "monopoly", "futurefight": "mfuturefight", "mobilelegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "koc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "nyotaensemble", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "nyotaalchemist", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "ji<PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "j<PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd": "btd", "clashroyale": "mashindan<PERSON><PERSON>", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hypefront", "knightrun": "m<PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "mashujaawakat<PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "mpambanowasoka", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "uchaguziwamfalme", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "mpenziwaganzi", "tacticool": "tacticool", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supersus": "supersus", "slowdrive": "kuendeshampole", "headsup": "tayari", "wordfeud": "ninasema", "bedwars": "vita<PERSON><PERSON><PERSON><PERSON>a", "freefire": "motozaibuka", "mobilegaming": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "kuvukamifugo", "bgmi": "bgmi", "teamfighttactics": "mpambanomakundi", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "witochamichezomobili", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "nyota_en", "randonautica": "randonautica", "maplestory": "mchezomdudu", "albion": "albioni", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "shakesnafiget", "ml": "ml", "bangdream": "n<PERSON><PERSON><PERSON><PERSON>", "clashofclan": "<PERSON><PERSON><PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "j<PERSON><PERSON><PERSON>", "timeprincess": "malkiawakati", "beatstar": "k<PERSON><PERSON><PERSON>", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "motoziwa", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "upend<PERSON><PERSON><PERSON><PERSON>", "androidgames": "michezoandroid", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "v<PERSON><PERSON><PERSON>", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "l<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "m<PERSON><PERSON><PERSON><PERSON>", "tinybirdgarden": "bustaniyandegewadogo", "gachalife": "gachalife", "neuralcloud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "vitaavijakazi", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "vikosivyandege", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiwa<PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "kuingia", "slugitout": "<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "kutesagreybaba", "petpals": "marafikimpwa", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenavunjanje", "wolfy": "mbwa_mwitu", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "m<PERSON><PERSON><PERSON>", "grandchase": "safarikubwa", "bombmebrasil": "bomuabrazil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "mchezootome", "mindustry": "mindustry", "callofdragons": "itofthemapepo", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "mtaaa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o", "sealm": "sealm", "shadowfight3": "mapiganoyakivuli3", "limbuscompany": "kampanilimbus", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "manenosokana2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobile", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "mi<PERSON><PERSON><PERSON><PERSON>laempires", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "ndo<PERSON><PERSON><PERSON>o", "aethergazer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "ma<PERSON>un", "gamingmlbb": "mchezomlbb", "dbdmobile": "dbdmobile", "arknight": "sanjakaboo", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "simu<PERSON><PERSON>", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "mchezobgmifamu", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulseeker": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "burettd", "onepiecebountyrush": "bountyrushyaonepiece", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeraiders": "wavamiziwawakati", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "shambushimarvel", "thebattlecats": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "safari", "giochidiruolo": "mchezowamtwaro", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "ulimwenguwagiza", "travellerttrpg": "mchezowakusafiri", "2300ad": "2300ad", "larp": "larp", "romanceclub": "klabuyauda<PERSON>", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokémonchumbahekalu", "pokemonlegendsarceus": "pokémon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémoncrystal", "pokemonanime": "p<PERSON><PERSON><PERSON><PERSON>", "pokémongo": "pokémongo", "pokemonred": "pokemonmwekundu", "pokemongo": "pokemongo", "pokemonshowdown": "kuo<PERSON><PERSON><PERSON><PERSON>k<PERSON><PERSON>", "pokemonranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pika", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "ma<PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "timystic", "pokeball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokémonmwangaza", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "muwind<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "totochess", "catur": "katur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "chess", "schaken": "schaken", "skak": "skak", "ajedres": "malkia", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "sakataya<PERSON><PERSON><PERSON><PERSON>", "chinesechess": "shaghalayakichina", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrez<PERSON><PERSON>ng<PERSON><PERSON>", "openings": "ufu<PERSON><PERSON><PERSON>", "rook": "mpishi", "chesscom": "chesscom", "calabozosydragones": "kalabozosnanyoka", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "jua<PERSON><PERSON>i", "thelegendofvoxmachina": "hadithiyavoxmachina", "doungenoanddragons": "dungeonsnamaj<PERSON>i", "darkmoor": "g<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "michuanoyaminecraft", "minecrafthive": "mchangowaminecraft", "minecraftbedrock": "bedrockyaminecraft", "dreamsmp": "ndotosmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modiza<PERSON>inecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "ziada", "mcpeaddons": "miche<PERSON><PERSON><PERSON><PERSON>", "skyblock": "angavifaa", "minecraftpocket": "minecraftkiganja", "minecraft360": "minecraft360", "moddedminecraft": "minecraftimebadilishwa", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "mi<PERSON><PERSON><PERSON>yaminecraft", "minecraftcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgamer": "mchezajiwapc", "jeuxvideo": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "levelup": "inuakiwango", "gamermobile": "m<PERSON>za<PERSON><PERSON><PERSON>", "gameover": "mchezo<PERSON><PERSON>a", "gg": "gg", "pcgaming": "michezoyapc", "gamen": "mchezo", "oyunoynamak": "chezaboo", "pcgames": "mchezozapc", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmastaweka", "pcgame": "michezoyapc", "gamerboy": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "mchezozavr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "mchezadjuegos", "gameplays": "michezo", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "m<PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "m<PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerschicas": "gamershadz", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "misheni", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "michanzicomputer", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "feki", "pc4gamers": "kom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "m<PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "mchezoheterosexual", "gamepc": "mchezopc", "girlsgamer": "wasichanigamer", "fnfmods": "modszafnf", "dailyquest": "changamotokilasiku", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "was<PERSON><PERSON><PERSON><PERSON>", "gamesetup": "mpangom<PERSON>zo", "overpowered": "kush<PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "mchezojam", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "tim<PERSON><PERSON><PERSON>", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "<PERSON><PERSON><PERSON>", "gamerbuddies": "wapenziwazing<PERSON>", "butuhcewekgamers": "natakawakikewachezamchezo", "christiangamer": "mchezajiwak<PERSON>", "gamernerd": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "andregamer": "andregamer", "casualgamer": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "kuangaliamchezo", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "p<PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "mchezajiwaplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "mchumi", "humanfallflat": "wa<PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "muzika<PERSON><PERSON>", "sonicthehedgehog": "sanjaththehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikipiki", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "m<PERSON>sher<PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "wa<PERSON>toawaangamwezi", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "ookkatuawakati", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simulateramtembezi", "nintendogames": "michezoyanintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "kiwandachakabaka", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "raf<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON>atikazel<PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "michezo51", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "hadithi", "raymanlegends": "raymanlegends", "luigismansion": "nyumba<PERSON><PERSON><PERSON>", "animalcrosssing": "wanyamaonline", "taikonotatsujin": "mfalmewataitokaja", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "wapiganajihyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioansonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "yum<PERSON><PERSON><PERSON>a", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "urgot": "na<PERSON><PERSON>i", "zyra": "zyra", "redcanids": "mbwawekundu", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftke", "lolph": "lolph", "leagueoflegend": "ligi<PERSON><PERSON><PERSON><PERSON>", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ligaoyashujaawild", "adcarry": "kubebaad", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "ligadeleyendasespana", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "kichekeshokidogo", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "michezolol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "malangohex", "hextech": "hextech", "fortnitegame": "mchezowafortnite", "gamingfortnite": "kuchezafortnite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "mi<PERSON><PERSON><PERSON>ogo<PERSON>", "videogamemaker": "mtengenezajiwai<PERSON>zo", "megamanzero": "megamanzero", "videogame": "mchezowavideo", "videosgame": "michezoyakvideo", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "kuf<PERSON><PERSON>ia", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "mchawi101", "battleblocktheater": "vitaedukubataboo", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxswahili", "erlc": "erlc", "sanboxgames": "mchezo<PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "ku<PERSON><PERSON><PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "michezostarcraft2", "duskwood": "g<PERSON>ku<PERSON>das<PERSON>", "dreamscape": "n<PERSON><PERSON><PERSON><PERSON>", "starcitizen": "n<PERSON><PERSON><PERSON><PERSON>", "yanderesimulator": "yanderesimulator", "grandtheftauto": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON>", "videogiochi": "michezoyalakumbukumb<PERSON>", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "michezoyaklavye", "touhouproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamcast": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "michezoyaadventure", "wolfenstein": "wolfenstein", "actionadventure": "ushindanaubunifu", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "mchezozamoyo", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "kompyutazakale", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "mchezozam<PERSON>zo", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "kamandakeen", "bugsnax": "<PERSON><PERSON><PERSON>", "injustice2": "udhalilishaji2", "shadowthehedgehog": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "mchezowangavuti", "zenlife": "<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "mtelezi", "mystgames": "mystic<PERSON><PERSON>", "blockchaingaming": "miche<PERSON><PERSON><PERSON><PERSON>in", "medievil": "medieval", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "tukimbie", "bloomingpanic": "kuota_wasi<PERSON>i", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "mchezowahofu", "monstergirlquest": "<PERSON>wan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "supergiant", "disneydreamlightvalle": "ndotozidizneyvalley", "farmingsims": "simuli<PERSON><PERSON>limo", "juegosviejos": "michezoyakale", "bethesda": "bethesda", "jackboxgames": "michezojackbox", "interactivefiction": "hadith<PERSON><PERSON><PERSON><PERSON><PERSON>a", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "wapendawapendwa", "visualnovel": "<PERSON>ith<PERSON>chora", "visualnovels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "mbwawakivuli", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "katherine", "twilightprincess": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON>", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "timu2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "mkonokubwa", "leafblowerrevolution": "mapinduziyavifaav<PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "kuundakikundi", "starrail": "nyota", "keyblade": "funguomkata", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsikosiwakati", "novelasvisuales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "chez<PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videojuezo", "videogamedates": "mchezonadate", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "mi<PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "kurudikwakukumbuka", "gamstergaming": "mchezowawanahewa", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "jumbalawazimu", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "michezoya3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "<PERSON><PERSON><PERSON><PERSON>", "storygames": "mchezozitaleso", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON>rohombil<PERSON>", "gameuse": "tumi<PERSON><PERSON><PERSON>", "offmortisghost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "kui<PERSON><PERSON>a", "katanazero": "katana0", "famicom": "famimkali", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anotherworld": "ul<PERSON><PERSON><PERSON><PERSON>e", "metaquest": "metaquest", "animewarrios2": "wapiganajiwanime2", "footballfusion": "m<PERSON>ung<PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "leg<PERSON><PERSON><PERSON>", "wranduin": "wanduin", "twistedmetal": "metalamchafuko", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "m<PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simuladori", "speedrunner": "<PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "njiayaajabuonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "miilizanayopendeza", "seum": "seum", "partyvideogames": "michezoyavideozasherehe", "graveyardkeeper": "mleziwamakaburi", "spaceflightsimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "chakula_na_michezo", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "mbwakatiyetuu", "truckingsimulator": "simu<PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "mchezowamkono", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "simu<PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "wimbozapop", "famitsu": "famitsu", "gatesofolympus": "milangoyaolimpus", "monsterhunternow": "mmonsterhuntersasa", "rebelstar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "mchezoindie", "indiegaming": "michezoyaindie", "indievideogames": "michezoyaindie", "indievideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanwaimara", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "hawezikushindika", "projectl": "mradi", "futureclubgames": "michezoyaajabu2023", "mugman": "mtuwakikombe", "insomniacgames": "michezoyakukosausingizi", "supergiantgames": "michezoyasupergiant", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "mchezoceleste", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "or<PERSON>ha", "gamebacklog": "mi<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "mpumbavikikombe", "deponia": "deponia", "naughtydog": "mbwa<PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "muziki<PERSON><PERSON><PERSON>", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "namapendakofxv", "arcanum": "arcanum", "neoy2k": "neo2k", "pcracing": "mbiozapc", "berserk": "kichaa", "baki": "baki", "sailormoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintseiya": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "wanzo", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "huz<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "gizakulikomweusi", "animescaling": "animescaling", "animewithplot": "animekamaimepangwa", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "animeza80s", "90sanime": "animeza90", "darklord": "mfalme_weusi", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "mangaza2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "msimu1wadaktaristone", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "animejalada", "thevisionofescaflowne": "maonoyaescaflowne", "slayers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "mchorocharlotte", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "sa<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "juju<PERSON><PERSON><PERSON>n", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "kipitumpiga漫画", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "sagayavinland", "madeinabyss": "tengwakatikabara", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangazardiquo", "romancemangas": "mangaszaura<PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "msaidizidragon", "blacklagoon": "boolago<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraforms", "geniusinc": "mwanamkaliinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "mbwaorwabungo", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "boon<PERSON><PERSON><PERSON>", "acertainmagicalindex": "orodhaimechangaishia", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "mtum<PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "mradiwakagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamilia", "rezero": "rezero", "swordartonline": "sana<PERSON><PERSON><PERSON><PERSON>", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "kipaumbelechawonderegg", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "spotianime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "ma<PERSON><PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyamalevil", "shounenanime": "animeyakijana", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "mkonokaskazini", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "ma<PERSON><PERSON>", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "ji<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON><PERSON>wosagashit<PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "mrembowakutisha", "martialpeak": "k<PERSON><PERSON>dan<PERSON>sanaa", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "mrembopluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "klemore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "mfukoniwamatunda", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuroshitsuji": "b<PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakurakapturaکارت", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "<PERSON><PERSON><PERSON>", "yourlieinapril": "uwongoakoapril", "buggytheclown": "boomcho<PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "mfu<PERSON><PERSON>_wasikani_mchanga", "jojolion": "j<PERSON><PERSON><PERSON><PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "vitamb<PERSON>shovyacha<PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "kwakoeternity", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "muunganofichika", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "imefutwa", "bluelock": "bluelock", "goblinslayer": "mtengenezajiwaboga", "detectiveconan": "muh<PERSON><PERSON><PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "knightwavampire", "mugi": "mugi", "blueexorcist": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "familiaya<PERSON><PERSON><PERSON>", "airgear": "<PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "hizi7dhambihatari", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "munguwashulekubwa", "kissxsis": "bususelafunga", "grandblue": "<PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "mpenziwamavazi", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "k<PERSON><PERSON><PERSON>", "animeuniverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "angavibilivya", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "map<PERSON><PERSON><PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentini", "lolicon": "lo<PERSON>on", "demonslayertothesword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "dam<PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "motoapiga", "adioseri": "kibumba", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "bintizikufuata", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "ma<PERSON>_shoujo_madoka", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "uchawiwacherry", "housekinokuni": "nyumbakwen<PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON>ko<PERSON><PERSON>rok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "hadith<PERSON>vindland", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "b<PERSON><PERSON><PERSON>", "deathparade": "<PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeyakijapan", "animespace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "m<PERSON><PERSON><PERSON><PERSON>", "animedub": "<PERSON><PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamwanamke", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "moyowamanga", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "rekodiyarag<PERSON>ok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "mfalmewarave", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelier<PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "wajusiwaastronomia", "animeshojo": "animeshojo", "reverseharem": "haremreverse", "saintsaeya": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greatteacheronizuka": "m<PERSON><PERSON><PERSON>_m<PERSON><PERSON>_on<PERSON>uka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gearnitano5", "grandbluedreaming": "ndotozagrandblue", "bloodplus": "<PERSON><PERSON>i", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "<PERSON>uhuu", "bloodc": "damu", "talesofdemonsandgods": "hadith<PERSON><PERSON><PERSON><PERSON><PERSON>iu<PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxmbaya", "splatteranime": "mwangazaanime", "splatter": "<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "kuinukakwakegarilangao", "somalianime": "animeyasomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedatakez", "animeyuri": "animeyuri", "animeespaña": "animehispania", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "mamboyanetjuu", "childrenofthewhales": "watotowabaloo", "liarliar": "mwongomwongo", "supercampeones": "supercampeones", "animeidols": "ibadazaanime", "isekaiwasmartphone": "isekaiwanako<PERSON>lew<PERSON>", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "kipcallofusiku", "bakuganbrawler": "brawler<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "wapiganajib<PERSON>", "natsuki": "natsuki", "mahoushoujo": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "t<PERSON><PERSON>akumbukumb<PERSON>", "findermanga": "patamanga", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "malkiawawewe", "paradisekiss": "pu<PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "ulimwenguanime", "persocoms": "persocoms", "omniscientreadersview": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "p<PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "mapendekez<PERSON><PERSON><PERSON>", "openinganime": "ufunguzianime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "kipigajiwasimuggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "sutiajamobilegundam", "mech": "mech", "eurekaseven": "eurekasaba", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "mchungajibebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "al<PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "<PERSON><PERSON><PERSON>", "jojobizarreadventures": "ajabujambozajojo", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "mshamban<PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "nyotafox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "m<PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goku": "goku", "broly": "broly", "shonenanime": "animeyavijana", "bokunoheroacademia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "shambulizakatiba", "erenyeager": "erenyeager", "myheroacademia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON><PERSON>", "tomodachigame": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kikosiuchunguzi", "onepieceanime": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "attaquedestitans": "shambulizipepo", "theonepieceisreal": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "wa<PERSON><PERSON><PERSON>sh<PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superjail": "superjail", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flawlesswebtoon": "<PERSON><PERSON><PERSON>", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecomics", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "si<PERSON>zakawaida", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "tuk<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "mambo<PERSON><PERSON><PERSON>"}