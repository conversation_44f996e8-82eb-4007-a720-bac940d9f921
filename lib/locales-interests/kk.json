{"2048": "2048", "mbti": "mbti", "enneagram": "эннеаграмма", "astrology": "астрология", "cognitivefunctions": "когнитивтікфункциялар", "psychology": "психология", "philosophy": "пәлсапа", "history": "тарих", "physics": "физика", "science": "ғылым", "culture": "мәдениет", "languages": "тіл<PERSON><PERSON>р", "technology": "технология", "memes": "мемдер", "mbtimemes": "mbtiмемдері", "astrologymemes": "астрологиямемдері", "enneagrammemes": "энеаграммамемдері", "showerthoughts": "душтағыойлар", "funny": "күлкілі", "videos": "видеолар", "gadgets": "гадже<PERSON><PERSON><PERSON>р", "politics": "саясат", "relationshipadvice": "қарымқатынастаркеңестері", "lifeadvice": "өміркеңестері", "crypto": "крипто", "news": "жаңалықтар", "worldnews": "әлемжаңалықтары", "archaeology": "археология", "learning": "оқу", "debates": "пік<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "қастандықтеориялары", "universe": "ғалам", "meditation": "медитация", "mythology": "мифология", "art": "өнер", "crafts": "қолөнер", "dance": "би", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "макияж", "beauty": "сұлулық", "fashion": "сән", "singing": "әнайту", "writing": "жазу", "photography": "фото", "cosplay": "косплей", "painting": "кескіндеме", "drawing": "суретсалу", "books": "кіта<PERSON><PERSON><PERSON>р", "movies": "кино", "poetry": "поэзия", "television": "теледидар", "filmmaking": "кин<PERSON><PERSON><PERSON><PERSON><PERSON>у", "animation": "анимация", "anime": "анимэ", "scifi": "ғылымифантастика", "fantasy": "фэнтези", "documentaries": "деректіфильм", "mystery": "мистика", "comedy": "комедия", "crime": "қылмыстық", "drama": "драма", "bollywood": "болливуд", "kdrama": "кдрама", "horror": "хоррор", "romance": "романтика", "realitytv": "реалити", "action": "экшн", "music": "музыка", "blues": "блюз", "classical": "классика", "country": "кантри", "desi": "деси", "edm": "электрондықмузыка", "electronic": "электрондықмузыка", "folk": "фолк", "funk": "фанк", "hiphop": "хипхоп", "house": "<PERSON><PERSON><PERSON><PERSON>", "indie": "инди", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "кпоп", "latin": "латын", "metal": "металл", "pop": "поп", "punk": "панк", "rnb": "rnb", "rap": "рэп", "reggae": "регги", "rock": "рок", "techno": "техно", "travel": "саяхат", "concerts": "концерт", "festivals": "фестиваль", "museums": "музей", "standup": "стэндап", "theater": "театр", "outdoors": "ашықауа", "gardening": "бақша", "partying": "кеш", "gaming": "ойын", "boardgames": "үстелойыны", "dungeonsanddragons": "dungeonsanddragons", "chess": "шахмат", "fortnite": "фортнайт", "leagueoflegends": "аңыздарлигасы", "starcraft": "старкр<PERSON><PERSON>т", "minecraft": "майнкра<PERSON>т", "pokemon": "покемон", "food": "тамақ", "baking": "нан", "cooking": "пісіру", "vegetarian": "вегетариан", "vegan": "веган", "birds": "құстар", "cats": "мысықтар", "dogs": "иттер", "fish": "балықтар", "animals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "blm", "environmentalism": "қоршағанорта", "feminism": "феминизм", "humanrights": "адамқұқықтары", "lgbtqally": "лгбт", "stopasianhate": "нәсілшілдіккежолжоқ", "transally": "тарнссексуал", "volunteering": "ерік<PERSON><PERSON><PERSON><PERSON>к", "sports": "спорт", "badminton": "бадминтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велосипед", "fitness": "фитнес", "football": "футбол", "golf": "гольф", "gym": "спортзалы", "gymnastics": "гимнастика", "hockey": "хоккей", "martialarts": "жекпежек", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "жүгіру", "skateboarding": "скейтбординг", "skiing": "шаңғы", "snowboarding": "сноуборд", "surfing": "серфинг", "swimming": "жүзу", "tennis": "теннис", "volleyball": "волейбол", "weightlifting": "ауыратлетика", "yoga": "йога", "scubadiving": "да<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "хайкинг", "capricorn": "тауешкі", "aquarius": "суқұйғыш", "pisces": "балықтар", "aries": "тоқты", "taurus": "торпақ", "gemini": "егіздер", "cancer": "шаян", "leo": "арыстан", "virgo": "бик<PERSON><PERSON>", "libra": "таразы", "scorpio": "сарышаян", "sagittarius": "мерген", "shortterm": "қысқамерзімді", "casual": "бейресми", "longtermrelationship": "ұзақмерзімдіқарымқатынас", "single": "жалғыз", "polyamory": "көпмахаббат", "enm": "көпмахаббат", "lgbt": "лгбт", "lgbtq": "лгбтқ", "gay": "гей", "lesbian": "лесби", "bisexual": "бисексуал", "pansexual": "пансексуал", "asexual": "асексуал", "reddeadredemption2": "reddeadredemption2", "dragonage": "жолбар<PERSON>сжасы", "assassinscreed": "асассиндердіңсенімі", "saintsrow": "сентсроу", "danganronpa": "данганронпа", "deltarune": "дэл<PERSON><PERSON><PERSON><PERSON>н", "watchdogs": "бақылаушылар", "dislyte": "дислите", "rougelikes": "қызылтәрізділер", "kingsquest": "корольдықсапар", "soulreaver": "жаныжердемдер", "suikoden": "суикоден", "subverse": "субверс", "legendofspyro": "бизнестіңлегендасы", "rouguelikes": "рогулықтар", "syberia": "сиберия", "rdr2": "rdr2", "spyrothedragon": "спайротегергон", "dragonsdogma": "айдаһарлардоктринасı", "sunsetoverdrive": "күншығыстынжоғарғысы", "arkham": "арқам", "deusex": "деусекс", "fireemblemfates": "отбасыбатырлары", "yokaiwatch": "йокайсағат", "rocksteady": "тасжолмен", "litrpg": "литрпг", "haloinfinite": "халоин<PERSON>инит", "guildwars": "гильдияларсоғысы", "openworld": "ашықәлем", "heroesofthestorm": "батырларштормы", "cytus": "ситус", "soulslike": "soulslike", "dungeoncrawling": "жертөлеарқылыжүру", "jetsetradio": "джетсеттеррадио", "tribesofmidgard": "midgardдіңтайпалары", "planescape": "жоспарлауәлемі", "lordsoftherealm2": "realm2діңлордалары", "baldursgate": "бальдурсқақпасы", "colorvore": "түстеңіз", "medabots": "медаботтар", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "пәтфофэксайл", "immersivesims": "иммерсивтісымдар", "okage": "окаге", "juegoderol": "рөлойынынойын", "witcher": "сиқыршы", "dishonored": "құрметсіз", "eldenring": "elden<PERSON>", "darksouls": "қаражүрек", "kotor": "котар", "wynncraft": "вwynncraft", "witcher3": "ведьмак3", "fallout": "фоллаут", "fallout3": "фоллаут3", "fallout4": "фоллаут4", "skyrim": "скрим", "elderscrolls": "elderscrolls", "modding": "моддинг", "charactercreation": "персонажжасау", "immersive": "белсенді", "falloutnewvegas": "фоллаутжаңаүйvegas", "bioshock": "биошок", "omori": "омори", "finalfantasyoldschool": "фinalfantasyескімектеп", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "соңғыфантазия", "finalfantasy14": "фуналфэнтези14", "finalfantasyxiv": "финалфантазияxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "фф13", "finalfantasymatoya": "финалфантазияматоя", "lalafell": "лалефелл", "dissidia": "диссидия", "finalfantasy7": "соңғыфантазия7", "ff7": "фф7", "morbidmotivation": "морталмотиватор", "finalfantasyvii": "финалфантазияvii", "ff8": "фф8", "otome": "отоме", "suckerforlove": "сүйіспеншіліккеәлсіз", "otomegames": "отомеойындары", "stardew": "жұлдыздыүнемдеу", "stardewvalley": "stardewvalley", "ocarinaoftime": "уақыттыңокаринасы", "yiikrpg": "йиикр<PERSON><PERSON>", "vampirethemasquerade": "вампирлеркешілук", "dimension20": "dimension20", "gaslands": "газже<PERSON><PERSON><PERSON>р", "pathfinder": "жол<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>і", "pathfinder2ndedition": "pathfinder2нұсқа", "shadowrun": "көлеңкежүгіру", "bloodontheclocktower": "сағаттағықан", "finalfantasy15": "соңғыфантазия15", "finalfantasy11": "финалфантазия11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "дра<PERSON><PERSON><PERSON>га<PERSON>д", "gravityrush": "гравитацияж<PERSON><PERSON>ылуы", "rpg": "рпг", "dota2": "дота2", "xenoblade": "ксеноблейд", "oneshot": "бірсоққы", "rpgmaker": "rpgmaker", "osrs": "осрс", "overlord": "үстемдік", "yourturntodie": "сеніңкезеңкележатыр", "persona3": "персона3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "река", "honkai": "һонкай", "marauders": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "shinmegamitensei": "шинмэгамитэнсэй", "epicseven": "эпикжеті", "rpgtext": "рпгтекст", "genshin": "ген<PERSON>ин", "eso": "eso", "diablo2": "диабло2", "diablo2lod": "диабло2лод", "morrowind": "морровинд", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "дұшпанжандар", "mu": "му", "falloutshelter": "падениебосқынжайы", "gurps": "гурпс", "darkestdungeon": "қараңғыжерзіреткіші", "eclipsephase": "күнтұтылуфазасы", "disgaea": "дисгаэа", "outerworlds": "сыртқыәлемдер", "arpg": "арпг", "crpg": "рпг", "bindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "diabloimmortal": "диаблоиммортал", "dynastywarriors": "үйсінармистары", "skullgirls": "скелетқыздар", "nightcity": "түнгіқала", "hogwartslegacy": "гогвартсмұрасы", "madnesscombat": "жындыкүрес", "jaggedalliance2": "жыртылғанодақ2", "neverwinter": "солтүстікнneverwinter", "road96": "road96", "vtmb": "втмб", "chimeraland": "чемералан", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "рогульдар", "gothamknights": "gothamnightтар", "forgottenrealms": "ұмытылғанәлемдер", "dragonlance": "драконшоқы", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "тоунтаун", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "могеко", "thedivision2": "thedivision2", "lineage2": "линейдж2", "digimonworld": "digitalmonstersәлемі", "monsterrancher": "монстрфермасы", "ecopunk": "экопанк", "vermintide2": "vermintide2", "xeno": "ксено", "vulcanverse": "вулканверсе", "fracturedthrones": "бұзылғантрондар", "horizonforbiddenwest": "хоризонттыжабықбатыс", "twewy": "twewy", "shadowpunk": "көлеңкелікпанк", "finalfantasyxv": "соңғысапар15", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "хогвартсжұмбақ", "deltagreen": "жасылкарта", "diablo": "диабло", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "смыть", "lastepoch": "соңғыдәуір", "starfinder": "жұлдызтауыпалушы", "goldensun": "алтынкүн", "divinityoriginalsin": "құдайдыңбастапқыкүнәсі", "bladesinthedark": "блэйдтерқараңғыда", "twilight2000": "түнгі2000", "sandevistan": "сандевистан", "cyberpunk": "киб<PERSON><PERSON><PERSON>анк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "киберпункқызыл", "dragonballxenoverse2": "драгонболлксеноврс2", "fallenorder": "құлағантәртіп", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "зұлымелдердіңжері", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "шайтаннанқолжеттік", "oldschoolrunescape": "ескімектепрүнскейп", "finalfantasy10": "соңғыфантазия10", "anime5e": "anime5e", "divinity": "кепіл<PERSON><PERSON>к", "pf2": "pf2", "farmrpg": "фермалықойын", "oldworldblues": "ескідүниеқайғысы", "adventurequest": "сапарістемі", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "республикароллаплей", "roleplayinggames": "рөлдікойындар", "finalfantasy9": "финалдықфантазия9", "sunhaven": "күнжайлау", "talesofsymphonia": "симфонияныңәңгімелері", "honkaistarrail": "хонкайстар<PERSON>ейл", "wolong": "волонг", "finalfantasy13": "финалфантазия13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "меніңфарагым", "sacredunderworld": "құпияәлем", "chainedechoes": "тізбектегіэха", "darksoul": "қаражүрек", "soulslikes": "жанұқсастар", "othercide": "басқаөлтіру", "mountandblade": "таужәнежебе", "inazumaeleven": "иназумаваннары", "acvalhalla": "acvalhalla", "chronotrigger": "хронотрегістр", "pillarsofeternity": "мәңгілікбағандар", "palladiumrpg": "палладиу<PERSON>рпг", "rifts": "жар<PERSON>лыс", "tibia": "тәбия", "thedivision": "бөлім", "hellocharlotte": "сәлемшарлотта", "legendofdragoon": "айдахарлегендасы", "xenobladechronicles2": "ксеноблейдхроникалары2", "vampirolamascarada": "вампироламаскарада", "octopathtraveler": "сегізжолкөшпелі", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "бөріқасқалардыңапокалипсі", "aveyond": "авейонд", "littlewood": "кішкентайорман", "childrenofmorta": "mortaбалалары", "engineheart": "engineheart", "fable3": "ертегі3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "рөлдікойын", "harpg": "harpg", "baldursgates": "бальдурсқақпалары", "edeneternal": "бақытсыздық", "finalfantasy16": "финалфэнтези16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "жф15", "starfield": "жұлдыздыойын", "oldschoolrevival": "ескімектепқайтаояну", "finalfantasy12": "соңғыфантазия12", "ff12": "ff12", "morkborg": "моркборг", "savageworlds": "жасандылықдүниесі", "diabloiv": "ди<PERSON><PERSON><PERSON><PERSON><PERSON>", "pve": "pve", "kingdomheart1": "хандықжүрек1", "ff9": "ff9", "kingdomheart2": "королевствожүрек2", "darknessdungeon": "қараңғыүрдіс", "juegosrpg": "rpgойындар", "kingdomhearts": "королевствосердец", "kingdomheart3": "патшалықжүрек3", "finalfantasy6": "finalfantasy6", "ffvi": "ффви", "clanmalkavian": "кланмалкавиан", "harvestella": "жинауканикулы", "gloomhaven": "глумхавен", "wildhearts": "жабайыжүректер", "bastion": "бастион", "drakarochdemoner": "дракарочдемонер", "skiesofarcadia": "arcadiaаспандары", "shadowhearts": "көлеңкежүректер", "nierreplicant": "ниеррепликант", "gnosia": "гносия", "pennyblood": "пеникровь", "breathoffire4": "оттегінірмен4", "mother3": "ана3", "cyberpunk2020": "киберпанк2020", "falloutbos": "фоллаутабос", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "рөлдікойындар", "roleplaygame": "рөлдікойын", "fabulaultima": "фабулаутима", "witchsheart": "сұлуардыңжүрегі", "harrypottergame": "гаррипоттеройыны", "pathfinderrpg": "жолашарrpg", "pathfinder2e": "pathfinder2е", "vampirilamasquerade": "вампириламаскарад", "dračák": "дра<PERSON><PERSON>к", "spelljammer": "спеллджаммер", "dragonageorigins": "драконжасынымкөздері", "chronocross": "хронокросс", "cocttrpg": "коктрпг", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "монстрлардыжойушыдүние", "bg3": "bg3", "xenogear": "ксеногар", "temtem": "темтем", "rpgforum": "rpgфорум", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "жүрекөлшегі", "baldursgate3": "baldursgate3", "kingdomcome": "корольдіккелер", "awplanet": "awplanet", "theworldendswithyou": "бүкіләлемсенменаяқталады", "dragalialost": "драгалялост", "elderscroll": "қарттарайналымы", "dyinglight2": "жандышам2", "finalfantasytactics": "finalfantasytactics", "grandia": "грандиа", "darkheresy": "қараңғыеретік", "shoptitans": "дүкентитандары", "forumrpg": "форумрпг", "golarion": "golarion", "earthmagic": "жерсиқырлары", "blackbook": "қаракітап", "skychildrenoflight": "аспанбалаларыжарқырау", "gryrpg": "грырпг", "sacredgoldedition": "киеліалтыннұқсан", "castlecrashers": "қамалжарушылары", "gothicgame": "gothicойын", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwireтокио", "fallout2d20": "fallout2d20", "gamingrpg": "ойынрпг", "prophunt": "профант", "starrails": "жұлдыздыпоездер", "cityofmist": "тұманкала", "indierpg": "индиярпг", "pointandclick": "нұқыпшерт", "emilyisawaytoo": "эмилиякетіпқалды", "emilyisaway": "эмилижоқ", "indivisible": "бөлінбес", "freeside": "босжақ", "epic7": "эпик7", "ff7evercrisis": "ff7永恒危机", "xenogears": "ксеногирс", "megamitensei": "мегамитенси", "symbaroum": "симбароум", "postcyberpunk": "посткиберпанк", "deathroadtocanada": "канадағаөлімжолы", "palladium": "палладио", "knightjdr": "рыцарждрэ", "monsterhunter": "монстржойғыш", "fireemblem": "оттысимвол", "genshinimpact": "genshinimpact", "geosupremancy": "геосупремантия", "persona5": "персона5", "ghostoftsushima": "потустанғанцусима", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "монстрлардыжету", "nier": "ниер", "dothack": "дотхак", "ys": "ys", "souleater": "рухжег<PERSON>ш", "fatestaynight": "фэтестэйнайт", "etrianodyssey": "etrianoдиясы", "nonarygames": "нoнaримeндер", "tacticalrpg": "тактикалықrpg", "mahoyo": "mahoyo", "animegames": "анимеойындар", "damganronpa": "дамғанронпа", "granbluefantasy": "гранблюфантазия", "godeater": "асқартаушы", "diluc": "дилук", "venti": "венти", "eternalsonata": "мәңгісеренада", "princessconnect": "принцессақосылу", "hexenzirkel": "некромансерлер", "cristales": "кристал<PERSON>ар", "vcs": "вкс", "pes": "пес", "pocketsage": "pocketsage", "valorant": "валора́нт", "valorante": "валоранте", "valorantindian": "валоран<PERSON>ин<PERSON><PERSON>ктер", "dota": "дота", "madden": "мэдден", "cdl": "cdl", "efootbal": "eфутбол", "nba2k": "nba2k", "egames": "еойындар", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "киберспорт", "mlg": "mlg", "leagueofdreamers": "арма<PERSON><PERSON><PERSON><PERSON>арлигалары", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "ефутбол", "dreamhack": "арманкету", "gaimin": "гейминг", "overwatchleague": "овервотчлига", "cybersport": "киберспорт", "crazyraccoon": "жындытысқанжануар", "test1test": "тест1тест", "fc24": "фк24", "riotgames": "riotgames", "eracing": "эразинг", "brasilgameshow": "бразилияойындарыкөрмесі", "valorantcompetitive": "валораптқасиби", "t3arena": "t3arena", "valorantbr": "валорентбразилия", "csgo": "csgo", "tf2": "тф2", "portal2": "портал2", "halflife": "жартыөмір", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "клапан", "portal": "портал", "teamfortress2": "командалыққорғау2", "everlastingsummer": "мәңгіжаз", "goatsimulator": "қойсимулятор", "garrysmod": "гаррисмод", "freedomplanet": "босдықжер", "transformice": "трансформис", "justshapesandbeats": "текпішіндерменырғақтар", "battlefield4": "батлфилд4", "nightinthewoods": "түнортасында", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "жаңбыртосы2", "metroidvanias": "метроидваниялар", "overcooked": "асыптасыпкетті", "interplanetary": "аралдыжераралық", "helltaker": "тозақалушы", "inscryption": "инскрипция", "7d2d": "7d2d", "deadcells": "өліклеткалар", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "гмод", "dwarffortress": "гd<PERSON><PERSON><PERSON><PERSON>", "foxhole": "түлкітоқсан", "stray": "көшедегі", "battlefield": "соғысалаңы", "battlefield1": "долбарыс1", "swtor": "swtор", "fallout2": "фоллаут2", "uboat": "убот", "eyeb": "көзбұрыш", "blackdesert": "қарашөлдер", "tabletopsimulator": "столойымсымуляторы", "partyhard": "қайратжаса", "hardspaceshipbreaker": "қаттығарышкемесішруушы", "hades": "<PERSON>а<PERSON><PERSON><PERSON>", "gunsmith": "бұйрықшы", "okami": "оками", "trappedwithjester": "тұтқынбопжатқаншутник", "dinkum": "досу", "predecessor": "алдыңғы", "rainworld": "жаңбырдүниесі", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "колониясим", "noita": "noita", "dawnofwar": "соғысбастамасы", "minionmasters": "мин<PERSON><PERSON><PERSON>мастерлері", "grimdawn": "қаратаңертен", "darkanddarker": "қаражәнеқараңғы", "motox": "мотох", "blackmesa": "қаражер", "soulworker": "рухжұмысшысы", "datingsims": "дати<PERSON><PERSON><PERSON>и<PERSON><PERSON>", "yaga": "яга", "cubeescape": "кубтанқашу", "hifirush": "х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "свенкооп", "newcity": "жаңақала", "citiesskylines": "қаласыларжоспарлары", "defconheavy": "defconау<PERSON>р", "kenopsia": "кенопсия", "virtualkenopsia": "виртуалкенопсия", "snowrunner": "қаржүгірткіш", "libraryofruina": "ruinaкітапханасы", "l4d2": "l4d2", "thenonarygames": "нонарыойындары", "omegastrikers": "омегастрайкерлер", "wayfinder": "жол<PERSON><PERSON><PERSON><PERSON>ы", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "бейбітпластикүйрек", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "мағанкүлесің", "catnight": "мысықтүн", "supermeatboy": "суперетінбала", "tinnybunny": "тинин<PERSON>ани", "cozygrove": "комфорттықорлын", "doom": "долыңыз", "callofduty": "соғысдөгем", "callofdutyww2": "callofdutyww2", "rainbow6": "r6", "apexlegends": "апекслегендалар", "cod": "код", "borderlands": "шекара<PERSON><PERSON>с", "pubg": "пубг", "callofdutyzombies": "callofdutyzombies", "apex": "апекс", "r6siege": "r6белгішесі", "megamanx": "мегаман<PERSON>с", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "алысқашықтық", "farcrygames": "farcryойындары", "paladins": "палади<PERSON><PERSON><PERSON>р", "earthdefenseforce": "жердіқорғаушыкүштер", "huntshowdown": "хан<PERSON><PERSON><PERSON><PERSON>да<PERSON>н", "ghostrecon": "бууghostrecon", "grandtheftauto5": "грандұрлыгаавто5", "warz": "соғыс", "sierra117": "сиерра117", "dayzstandalone": "dayzstandalone", "ultrakill": "ультракіл", "joinsquad": "топқақосыл", "echovr": "эховр", "discoelysium": "дискосиясы", "insurgencysandstorm": "бунтжәнедауыл", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "макспейн", "hitman3": "хитмен3", "r6s": "r6s", "rainbowsixsiege": "кемпірқосақалтырғыш", "deathstranding": "жазылғанқаралу", "b4b": "b4b", "codwarzone": "кодсоғыс", "callofdutywarzone": "callofdutywarzone", "codzombies": "кодзомби", "mirrorsedge": "айнашеансары", "divisions2": "divisions2", "killzone": "өлтіруаймағы", "helghan": "hel<PERSON>", "coldwarzombies": "суықсоғысзомби", "metro2033": "метро2033", "metalgear": "мета<PERSON><PERSON><PERSON>р", "acecombat": "асқарұшақ", "crosscode": "кросскод", "goldeneye007": "алтынкөз007", "blackops2": "blackops2", "sniperelite": "снайперэлит", "modernwarfare": "қазіргісоғыс", "neonabyss": "неонтереңдігі", "planetside2": "planetside2", "mechwarrior": "мехсоғыс", "boarderlands": "шекар<PERSON><PERSON><PERSON>р", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "tarkovтанқашу", "metalslug": "металслуг", "primalcarnage": "праймалықжануарлар", "worldofwarships": "соғыскемелеріәлемі", "back4blood": "back4blood", "warframe": "соғысбелгісі", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "жалдамалыкісі", "masseffect": "массеэффект", "systemshock": "жүйекшок", "valkyriachronicles": "валкирияхроникалары", "specopstheline": "спецоперациясысызық", "killingfloor2": "killingfloor2", "cavestory": "жертеви<PERSON><PERSON>гі", "doometernal": "долгоөмір", "centuryageofashes": "ғасыркүлзаманы", "farcry4": "фаркрай4", "gearsofwar": "жауынгерлертіңкүштері", "mwo": "mwo", "division2": "дивизия2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "поколениеноль", "enterthegungeon": "қарудыкіргіз", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "заманауиұрыс2", "blackops1": "blackops1", "sausageman": "колбасаадам", "ratchetandclank": "ratchetandclank", "chexquest": "чексжорығы", "thephantompain": "елесауруы", "warface": "соғыслицасы", "crossfire": "кроссфаер", "atomicheart": "атомдықжүрек", "blackops3": "қараоперациялар3", "vampiresurvivors": "вампирамнанқұтылушылар", "callofdutybatleroyale": "callofdutyбэтлеройал", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "свобода", "battlegrounds": "соғысалаңы", "frag": "фраг", "tinytina": "<PERSON><PERSON>", "gamepubg": "ойынpubg", "necromunda": "некромунда", "metalgearsonsoflibert": "металгерұлыбостандық", "juegosfps": "жекпежекойындар", "convertstrike": "конвертацияшабуылы", "warzone2": "соғысалаңы2", "shatterline": "шашырайтынсызық", "blackopszombies": "қараоперацияларзомби", "bloodymess": "қандыбұзақ", "republiccommando": "республиканаркомандосы", "elitedangerous": "elitedangerous", "soldat": "сsoldат", "groundbranch": "жербөлімі", "squad": "брутим", "destiny1": "destiny1", "gamingfps": "ойынфпс", "redfall": "қызылтүскен", "pubggirl": "pubggirl", "worldoftanksblitz": "танкәлеміблиц", "callofdutyblackops": "callofdutyblackops", "enlisted": "енгізілген", "farlight": "фар<PERSON><PERSON><PERSON><PERSON>т", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "фарлайт84", "splatoon3": "splatoon3", "armoredcore": "қабыққабыл", "pavlovvr": "павловвр", "xdefiant": "xде<PERSON><PERSON>с", "tinytinaswonderlands": "kішкентайтенаңарлардыңәлемі", "halo2": "halo2", "payday2": "жалақы2", "cs16": "cs16", "pubgindonesia": "pubgиндонезия", "pubgukraine": "пубгукраина", "pubgeu": "пабгекел", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgромания", "empyrion": "эмпирион", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "титанжарғы2", "soapcod": "сабынкод", "ghostcod": "жынблок", "csplay": "csplay", "unrealtournament": "нағызтурнир", "callofdutydmz": "callofdutydmz", "gamingcodm": "ойнаукодм", "borderlands2": "шекаралар2", "counterstrike": "контрстрайк", "cs2": "cs2", "pistolwhip": "пистолетпенұрып", "callofdutymw2": "callofdutymw2", "quakechampions": "жерастыбелгімен", "halo3": "halo3", "halo": "сәлем", "killingfloor": "өлтіріпжатып", "destiny2": "destiny2", "exoprimal": "эксопримал", "splintercell": "сплинтерселл", "neonwhite": "неонақ", "remnant": "қалдық", "azurelane": "azurelane", "worldofwar": "соғысәлемі", "gunvolt": "гунвольт", "returnal": "қайтыпкелу", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "көлеңкекісі", "quake2": "жерсілкінісі2", "microvolts": "микровольттар", "reddead": "қызылөлім", "standoff2": "стендофф2", "harekat": "әрекет", "battlefield3": "аланда3", "lostark": "lostark", "guildwars2": "гильдиялықсоғыстар2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "теңізқазынасы", "rust": "ржақ", "conqueronline": "conqueronline", "dauntless": "қалмасаңдар", "warships": "соғыскемелері", "dayofdragons": "жолбарыстаркүні", "warthunder": "жарылғышнайза", "flightrising": "ұшужігер", "recroom": "рекрум", "legendsofruneterra": "runeterraлегендалары", "pso2": "pso2", "myster": "мистика", "phantasystaronline2": "фантасистаронлайн2", "maidenless": "қызсыз", "ninokuni": "нинокуни", "worldoftanks": "бүкілдүниежүксалғыштар", "crossout": "қфрәндер", "agario": "агарио", "secondlife": "екіншіөмір", "aion": "айон", "toweroffantasy": "fantasyқаласы", "netplay": "нетплей", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "нинокуникроссәлемдесіз", "reddeadonline": "reddeadonline", "superanimalroyale": "суперанималройял", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "рыцар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "gw2": "g2", "tboi": "tboi", "thebindingofisaac": "isaacтыңбайланысы", "dragonageinquisition": "драконийзорлық", "codevein": "кодеманыңжаны", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпингвин", "lotro": "лотро", "wakfu": "вақфу", "scum": "сұмдық", "newworld": "жаңасвет", "blackdesertonline": "қарашөлмеконлайн", "multiplayer": "көпойыншы", "pirate101": "қайсар101", "honorofkings": "ханадардыңқұрметі", "fivem": "fivem", "starwarsbattlefront": "starwarsкүресалаңы", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "фигрос", "mmo": "ммо", "pokemmo": "покеммо", "ponytown": "понита<PERSON>н", "3dchat": "3dсөйлесу", "nostale": "nostale", "tauriwow": "тауривау", "wowclassic": "ваухлассик", "worldofwarcraft": "соғысәлемі", "warcraft": "соғысжәнешеберлік", "wotlk": "wotlk", "runescape": "рунескеп", "neopets": "неопетстер", "moba": "моба", "habbo": "хаббо", "archeage": "архейдж", "toramonline": "торамо<PERSON><PERSON><PERSON><PERSON>н", "mabinogi": "мэбэноғи", "ashesofcreation": "жараныңкешігі", "riotmmo": "шаттықммо", "silkroad": "ж<PERSON><PERSON><PERSON>кжолы", "spiralknights": "спиральдырыцарьлар", "mulegend": "мульегенд", "startrekonline": "startrеkонл<PERSON><PERSON>н", "vindictus": "вендиктус", "albiononline": "албиононлайн", "bladeandsoul": "bladeandsoul", "evony": "евони", "dragonsprophet": "жолбарыстарпророк", "grymmo": "грыммо", "warmane": "жылыжүрек", "multijugador": "көпойыншы", "angelsonline": "періштеле<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "lunia": "лунья", "luniaz": "лунияz", "idleon": "<PERSON>on", "dcuniverseonline": "дкуниверсон<PERSON><PERSON><PERSON>н", "growtopia": "гровтопия", "starwarsoldrepublic": "жұлдыздысоғысескіреспубликa", "grandfantasia": "ұлыфантазия", "blueprotocol": "көкпротокол", "perfectworld": "тәуірәлем", "riseonline": "онлайнкөтерілу", "corepunk": "corepunk", "adventurequestworlds": "атқамінуорабзылықтар", "flyforfun": "ұшыпойнау", "animaljam": "animaljam", "kingdomofloathing": "шаршата<PERSON>ынмемлекет", "cityofheroes": "батырларқаласы", "mortalkombat": "морталкомбат", "streetfighter": "көшебойы́шолушы", "hollowknight": "босрыцарь", "metalgearsolid": "металгирсолид", "forhonor": "арнаулыпочет", "tekken": "теккен", "guiltygear": "гильти<PERSON><PERSON><PERSON>р", "xenoverse2": "ксеновёрс2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "мультиевразия", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "жүрегініңсабыры", "brawlhalla": "бравлхалла", "virtuafighter": "виртуалұрыс", "streetsofrage": "көшелердегіжасауна", "mkdeadlyalliance": "mkөлімдісеріктестік", "nomoreheroes": "ке<PERSON><PERSON>н<PERSON><PERSON><PERSON>е<PERSON><PERSON><PERSON><PERSON>р", "mhr": "мхр", "mortalkombat12": "mortalkombat12", "thekingoffighters": "боксерлерпатшасы", "likeadragon": "жолбарысша", "retrofightinggames": "ретрофайтингойындар", "blasphemous": "бақытсыз", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvsсapcom", "supersmash": "суперсүйек", "mugen": "мүген", "warofthemonsters": "монстрлардыңсоғысы", "jogosdeluta": "күресойындары", "cyberbots": "киберботтар", "armoredwarriors": "қорғанысжауынгерлері", "finalfight": "соңғысұрыптасу", "poweredgear": "күшжабдықтары", "beatemup": "бөтенмін", "blazblue": "блазблу", "mortalkombat9": "морталкомбат9", "fightgames": "күресойындар", "killerinstinct": "килл<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "kingoffigthers": "корольжекпежек", "ghostrunner": "призракжүгіруші", "chivalry2": "рыцарлық2", "demonssouls": "демонсоулс", "blazbluecrosstag": "блазблюкросстаг", "blazbluextagbattle": "блазблуэкстагбаттл", "blazbluextag": "blazbluextag", "guiltygearstrive": "кінәмшілкүрес", "hollowknightsequel": "hollowknightсиквелі", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongойыны", "silksongnews": "silksongжаңалықтары", "silksong": "silksong", "undernight": "түнделік", "typelumina": "typelum<PERSON>", "evolutiontournament": "эволюциятурниры", "evomoment": "евомомент", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON>е<PERSON>ек", "dragonballfighterz": "драгонболлфайтерз", "talesofberseria": "бершерияныңәңгімелері", "bloodborne": "қанышыл", "horizon": "көрініс", "pathofexile": "жолықамту", "slimerancher": "слаймеренчер", "crashbandicoot": "крэшбандикут", "bloodbourne": "қанжорғалақ", "uncharted": "бел<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonнольтаңсәрісы", "ps4": "пс4", "ps5": "ps5", "spyro": "спиро", "playstationplus": "playstationплюс", "lastofus": "соңғыбіздің", "infamous": "атақты", "playstationbuddies": "плейстейшндосттар", "ps1": "пс1", "oddworld": "бөтенәлем", "playstation5": "плейстейшн5", "slycooper": "слайкупер", "psp": "псп", "rabbids": "раббидтер", "splitgate": "splitgate", "persona4": "персона4", "hellletloose": "тозақашылады", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumфайлы", "gta5": "gta5", "gtasanandreas": "гтасанандреас", "godofwar": "құдайдыңсоғысы", "gris": "грис", "trove": "жинақ", "detroitbecomehuman": "детройтадамғаайналу", "beatsaber": "биі<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rimworld": "римәлем", "stellaris": "стелл<PERSON><PERSON><PERSON>с", "ps3": "пс3", "untildawn": "таңғадейін", "touristtrophy": "туристиктрофи", "lspdfr": "лспдфр", "shadowofthecolossus": "колосскөлеңкесі", "crashteamracing": "топжарыс", "fivepd": "беспд", "tekken7": "tekken7", "devilmaycry": "шайтандаржылайды", "devilmaycry3": "шайтанжылай33", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "плейстейшн", "samuraiwarriors": "самур<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нгерлер", "psvr2": "psvr2", "thelastguardian": "соңғықорғаушы", "soulblade": "жүрекқылыш", "gta5rp": "gta5rp", "gtav": "гтава", "playstation3": "плейстейшен3", "manhunt": "адау<PERSON><PERSON><PERSON>л", "gtavicecity": "гтавайсити", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2пакт", "pcsx2": "pcsx2", "lastguardian": "соңғықорғаушы", "xboxone": "xboxone", "forza": "форца", "cd": "цд", "gamepass": "ойынпасс", "armello": "армелло", "partyanimal": "парти<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "warharmmer40k": "warhammer40k", "fightnightchampion": "түнжекпілосы", "psychonauts": "психонавттар", "mhw": "mhw", "princeofpersia": "принцперсии", "theelderscrollsskyrim": "қарттаржаттылығыскайрим", "pantarhei": "пан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "соғысалдыңалдында", "dontstarvetogether": "ешкімдіашықтауғаболмайды", "ori": "ори", "spelunky": "спелунки", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "жұлдыздарғажетеміз", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "скейт3", "houseflipper": "үйайналдырғыш", "americanmcgeesalice": "америкалықмэгигеалисі", "xboxs": "xboxтар", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "корольдерлігініңлигада", "fable2": "fable2", "xboxgamepass": "xboxойынпакеті", "undertale": "undertale", "trashtv": "қоқыств", "skycotl": "асманкотл", "erica": "ерика", "ancestory": "туыстық", "cuphead": "капхед", "littlemisfortune": "кішібақытсыздық", "sallyface": "саллиб<PERSON>т<PERSON>", "franbow": "франбоу", "monsterprom": "монстрсобысы", "projectzomboid": "проектзомбойд", "ddlc": "ddlc", "motos": "мото", "outerwilds": "сыртқыжерлер", "pbbg": "пббг", "anshi": "ан<PERSON>и", "cultofthelamb": "қойлардыңқұпиясы", "duckgame": "үйрекойыны", "thestanleyparable": "станлипарабелдері", "towerunite": "towerunite", "occulto": "оккультно", "longdrive": "ұзынсапар", "satisfactory": "қанағаттанарлық", "pluviophile": "жылдамқұйылған", "underearth": "жерасты", "assettocorsa": "assettocorsa", "geometrydash": "геометриялықжарылыс", "kerbal": "керкал", "kerbalspaceprogram": "кербалғарышбағдарламасы", "kenshi": "кен<PERSON>і", "spiritfarer": "рухтынжүргішісі", "darkdome": "қарашаршы", "pizzatower": "пицзавысы", "indiegame": "индиойын", "itchio": "itchio", "golfit": "golfit", "truthordare": "шындықпасынба", "game": "ойын", "rockpaperscissors": "тасқақайшықағаз", "trampoline": "трамплин", "hulahoop": "<PERSON>у<PERSON><PERSON><PERSON>у<PERSON>", "dare": "қаймықпа", "scavengerhunt": "қазынаіздеу", "yardgames": "аулаойындары", "pickanumber": "сантаңда", "trueorfalse": "нақтыжалған", "beerpong": "бस्थкпонт", "dicegoblin": "dicegoblin", "cosygames": "ыңғайлыойындар", "datinggames": "танысуойындары", "freegame": "тегінойын", "drinkinggames": "ішімдікойындары", "sodoku": "содоку", "juegos": "ойындар", "mahjong": "мах<PERSON>он", "jeux": "ойындар", "simulationgames": "симуляцияойындары", "wordgames": "сөзойындар", "jeuxdemots": "сөздеройыны", "juegosdepalabras": "сөзжұмбақтар", "letsplayagame": "ойнайықойын", "boredgames": "ұстарайлар", "oyun": "ойын", "interactivegames": "интерактивтіойындар", "amtgard": "amtgard", "staringcontests": "көзқарасбәсекесі", "spiele": "спилле", "giochi": "ойындар", "geoguessr": "геогесер", "iphonegames": "iphoneойындары", "boogames": "boogames", "cranegame": "түккішойын", "hideandseek": "жасырыныпіздейік", "hopscotch": "секіртпе", "arcadegames": "аркадаойындар", "yakuzagames": "якүзагеймс", "classicgame": "классикалықойын", "mindgames": "ақылойындар", "guessthelyric": "гесстхелирик", "galagames": "галаквесттер", "romancegame": "романтикалықойын", "yanderegames": "йандерегеймс", "tonguetwisters": "тілбасқақтар", "4xgames": "4б<PERSON>ройын", "gamefi": "gamefi", "jeuxdarcades": "ойынзалдары", "tabletopgames": "столдықойындар", "metroidvania": "метроидвания", "games90": "ойындар90", "idareyou": "сенемінсаған", "mozaa": "мозаа", "fumitouedagames": "фумитоуэдэгеймс", "racinggames": "жарысойындар", "ets2": "ets2", "realvsfake": "нақтыжәнежалған", "playgames": "ойнаойындар", "gameonline": "ойнақұралы", "onlinegames": "онлайнойындар", "jogosonline": "онлайнойындар", "writtenroleplay": "жазбаша<PERSON>йын", "playaballgame": "шарикойна", "pictionary": "пиктография", "coopgames": "коопойындар", "jenga": "жнга", "wiigames": "wiigames", "highscore": "жоғарыбалл", "jeuxderôles": "рөлдікойындар", "burgergames": "бургеройындар", "kidsgames": "балалардыңойындары", "skeeball": "скибал", "nfsmwblackedition": "nfsmwқаранұсқа", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "сұрақойыны", "gioco": "ойын", "managementgame": "басқаруойыны", "hiddenobjectgame": "жасырынзатойыны", "roolipelit": "рулыпелит", "formula1game": "формула1ойыны", "citybuilder": "қалабелгішісі", "drdriving": "дрдр<PERSON><PERSON><PERSON><PERSON>нг", "juegosarcade": "аркадалықойындар", "memorygames": "естесақтауойындары", "vulkan": "вулкан", "actiongames": "экшнойындар", "blowgames": "блоуойындар", "pinballmachines": "пинболаппараттары", "oldgames": "ескіойындар", "couchcoop": "диванменбірге", "perguntados": "сұраулар", "gameo": "gameo", "lasergame": "лазеройын", "imessagegames": "imessageойындары", "idlegames": "босойындар", "fillintheblank": "босорындардытолтырыңыз", "jeuxpc": "pcойындары", "rétrogaming": "рэ́троойын", "logicgames": "логикалықойындар", "japangame": "жапанүиын", "rizzupgame": "rizzupgame", "subwaysurf": "метроларжүру", "jeuxdecelebrite": "жұлдыздаройыны", "exitgames": "шығуойындары", "5vs5": "5ке5", "rolgame": "рольдікойын", "dashiegames": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>с", "gameandkill": "ойнапөлтір", "traditionalgames": "дәстүрліойындар", "kniffel": "книффел", "gamefps": "ойынfps", "textbasedgames": "мәтіннегіздегіойындар", "gryparagrafowe": "грыпараграфовые", "fantacalcio": "фантакальчо", "retrospel": "ретроспел", "thiefgame": "ұрлықойын", "lawngames": "лауңойындар", "fliperama": "флиперама", "heroclix": "героиклик", "tablesoccer": "столдықфутбол", "tischfußball": "столфутбол", "spieleabende": "ойынкештер", "jeuxforum": "jeuxforum", "casualgames": "кездейсоқойындар", "fléchettes": "стрелялка", "escapegames": "қашуойындары", "thiefgameseries": "ұрлықойынсериясы", "cranegames": "қуыршақойындар", "játék": "жүйе", "bordfodbold": "бордфутбол", "jogosorte": "жогосортесі", "mage": "мейдж", "cargames": "автогимнастика", "onlineplay": "онлайнойын", "mölkky": "мөлккі", "gamenights": "ойынтүндері", "pursebingos": "қапшықпарақтар", "randomizer": "кездейсоқшашу", "msx": "msx", "anagrammi": "анаграммдар", "gamespc": "ойындарпк", "socialdeductiongames": "әлеуметтікалдамашылықойындары", "dominos": "доминостер", "domino": "домино", "isometricgames": "изометриялықойындар", "goodoldgames": "жақсыларынойындар", "truthanddare": "шындықжәнесынау", "mahjongriichi": "ма<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "аңшыойындары", "jeuxvirtuel": "виртуалдықойындар", "romhack": "ромхак", "f2pgamer": "f2pойыншы", "free2play": "тегінойнау", "fantasygame": "фантазияойыны", "gryonline": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "driftgame": "дрифктойы", "gamesotomes": "ойынсотамыз", "halotvseriesandgames": "halotvseriesandgames", "mushroomoasis": "грибжартығы", "anythingwithanengine": "акаппаратпенбәрі", "everywheregame": "бәріжердеойын", "swordandsorcery": "қылышжәнесұмдық", "goodgamegiving": "жақсыойынберу", "jugamos": "ойнаймыз", "lab8games": "lab8ойындары", "labzerogames": "лабнольойындары", "grykomputerowe": "компьютерлікойындар", "virgogami": "виргогами", "gogame": "гогейм", "jeuxderythmes": "ритмдықойындар", "minaturegames": "кишіойындар", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "өзімсүйгемейін", "gamemodding": "ойынмодификациясы", "crimegames": "құқықбұзушылықойындары", "dobbelspellen": "дубльойындар", "spelletjes": "ойындар", "spacenerf": "spacenerf", "charades": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "жекеойыншы", "coopgame": "коопойын", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "нексус", "geforcenow": "geforcenow", "maingame": "негізгіойын", "kingdiscord": "корольдискорд", "scrabble": "скрэббл", "schach": "шахмат", "shogi": "шоги", "dandd": "dandd", "catan": "катан", "ludo": "лудо", "backgammon": "нардылық", "onitama": "онитама", "pandemiclegacy": "пандемиямұрасы", "camelup": "жылқыдауда", "monopolygame": "монополияойыны", "brettspiele": "настольныеигры", "bordspellen": "таратпалар", "boardgame": "ойынкестесі", "sällskapspel": "салдықойын", "planszowe": "планжоғары", "risiko": "риск", "permainanpapan": "партиялықойын", "zombicide": "зомбицид", "tabletop": "столдықойма", "baduk": "бадук", "bloodbowl": "қанкалаушы", "cluedo": "клюдо", "xiangqi": "сянци", "senet": "сенет", "goboardgame": "гобордойын", "connectfour": "төртбайланыс", "heroquest": "батырлықсапар", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "фаркл", "carrom": "карром", "tablegames": "үстелойындары", "dicegames": "дискіойындар", "yatzy": "ятзи", "parchis": "парчис", "jogodetabuleiro": "дж<PERSON>год<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "жұртпенжұмыс", "deskgames": "столойындар", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "масалықойындар", "marvelcrisisprotocol": "марвелдағакризиспротоколы", "cosmicencounter": "космостықкездесу", "creationludique": "ойын<PERSON>ыжасау", "tabletoproleplay": "настольдықрөлдікойын", "cardboardgames": "картондықойындар", "eldritchhorror": "елдритшқорқыныш", "switchboardgames": "бақылауойындары", "infinitythegame": "infinityойыны", "kingdomdeath": "королдікөлім", "yahtzee": "яштызақ", "chutesandladders": "тартпаларменжарқыраулар", "társas": "тұрғанбиз", "juegodemesa": "үстелдікойын", "planszówki": "пландаройсы", "rednecklife": "қызылмойынөмірі", "boardom": "бордомен", "applestoapples": "алмаданалма", "jeudesociété": "бөлімойын", "gameboard": "ойындоскасы", "dominó": "домино", "kalah": "kalah", "crokinole": "крокенол", "jeuxdesociétés": "ойынсүйерлер", "twilightimperium": "түнгіимперия", "horseopoly": "атополиия", "deckbuilding": "декбил<PERSON>инг", "mansionsofmadness": "бұлбақшаныңбақыты", "gomoku": "гомоку", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "brimstoneдағь阴影лар", "kingoftokyo": "tokyonyñpatşası", "warcaby": "warcaby", "táblajátékok": "табағойындар", "battleship": "бизбасынанбастап", "tickettoride": "билеталу", "deskovehry": "столойындар", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "настольдыойындар", "stolníhry": "үстелойындар", "xiángqi": "шахмат", "jeuxsociete": "күрделіойындар", "gesellschaftsspiele": "қоғамдықойындар", "starwarslegion": "жұлдыздысоғыслегионы", "gochess": "гочесс", "weiqi": "вэйци", "jeuxdesocietes": "ойынсүйерлер", "terraria": "террария", "dsmp": "dsmp", "warzone": "соғысаймағы", "arksurvivalevolved": "arksurvivalevolved", "dayz": "күндер", "identityv": "идентичность", "theisle": "арал", "thelastofus": "соңғыбіз", "nomanssky": "адамсызкөк", "subnautica": "субнаутика", "tombraider": "тумбрайдер", "callofcthulhu": "күлтегіншептерихаты", "bendyandtheinkmachine": "bendyжәнебояумашина", "conanexiles": "конанэксайлс", "eft": "ефт", "amongus": "біздегі", "eco": "эко", "monkeyisland": "маймылдараралы", "valheim": "вал<PERSON>ейм", "planetcrafter": "планетақұраушы", "daysgone": "күндерөткен", "fobia": "фобия", "witchit": "сұлукетwitchit", "pathologic": "патологиялық", "zomboid": "зомбойд", "northgard": "солтүстікваряг", "7dtd": "7dtd", "thelongdark": "ұзаққараңғы", "ark": "арк", "grounded": "тұрақтылық", "stateofdecay2": "decay2шstate", "vrising": "vrising", "madfather": "жынтектіана", "dontstarve": "аштықтанқаттама", "eternalreturn": "мәңгіауадаққайту", "pathoftitans": "titандаржолы", "frictionalgames": "фрикционалдыойындар", "hexen": "хексен", "theevilwithin": "зұлымдықішінде", "realrac": "реалрак<PERSON>і", "thebackrooms": "артқыбөлмелер", "backrooms": "артқыбөлмелер", "empiressmp": "империясымп", "blockstory": "блокәңгіме", "thequarry": "таскар<PERSON><PERSON>р", "tlou": "tlou", "dyinglight": "жарықтҥалып", "thewalkingdeadgame": "жүргенөлтірішекойыны", "wehappyfew": "бізбасқашамыс", "riseofempires": "империялардыңкөтерілуі", "stateofsurvivalgame": "тіршіліктіңжағдайыойыны", "vintagestory": "винтаждықс故事", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "баротравма", "breathedge": "breathedge", "alisa": "алиса", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "бермудалықайбандар", "frostpunk": "фростпункт", "darkwood": "қараорман", "survivalhorror": "тіршілікқорқыныш", "residentevil": "тұрғынжамандық", "residentevil2": "тұрғынжамандық2", "residentevil4": "резидентевил4", "residentevil3": "резидентзұлымдық3", "voidtrain": "боспоезд", "lifeaftergame": "ойыннанкейінгіөмір", "survivalgames": "тіріқалуғаойындар", "sillenthill": "тиынсубақа", "thiswarofmine": "бұлсоғысменің", "scpfoundation": "scpfoundation", "greenproject": "жа<PERSON><PERSON>лж<PERSON>ба", "kuon": "куон", "cryoffear": "қорқыныштанжылау", "raft": "рафт", "rdo": "rdo", "greenhell": "жасылтозақ", "residentevil5": "резидентзло5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "оныронаута", "granny": "апа", "littlenightmares2": "кішкентайқорқыныштар2", "signalis": "signalis", "amandatheadventurer": "аманада<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "sonsoftheforest": "орманныңбалалары", "rustvideogame": "rustойыны", "outlasttrials": "outlastсынақтары", "alienisolation": "жанқарауызisolation", "undawn": "ұйқысыз", "7day2die": "7күндеөлу", "sunlesssea": "күнсізтеңіз", "sopravvivenza": "тір<PERSON><PERSON><PERSON><PERSON>к", "propnight": "пропнощь", "deadisland2": "deadisland2", "ikemensengoku": "икеменсегоку", "ikemenvampire": "икемеменвампир", "deathverse": "deathverse", "cataclysmdarkdays": "катаклизмқаракүндер", "soma": "сома", "fearandhunger": "қорқынышжәнеаштық", "stalkercieńczarnobyla": "сталькерсіненццернобыль", "lifeafter": "өмірденкейін", "ageofdarkness": "қараңғылықдәуірі", "clocktower3": "сағатмұнарасы3", "aloneinthedark": "қаранғыдажайдау", "medievaldynasty": "ортағасырлықәлем", "projectnimbusgame": "жобанимбусойыны", "eternights": "мәңгінощылар", "craftopia": "креативия", "theoutlasttrials": "theoutlasttrials", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "әлемдібилепалу", "rocketleague": "рокетлига", "tft": "тфт", "officioassassinorum": "офисassassinorum", "necron": "некрон", "wfrp": "wfrp", "dwarfslayer": "шүберекжеңуші", "warhammer40kcrush": "warhammer40kжүрекайнау", "wh40": "wh40", "warhammer40klove": "warhammer40kкұштары", "warhammer40klore": "warhammer40kескілері", "warhammer": "wahrammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kқаратеңіз", "totalwarhammer3": "totalwarhammer3", "temploculexus": "темплоцулексуста", "vindicare": "vindicare", "ilovesororitas": "менсорориталардыжақсыкөремін", "ilovevindicare": "менвиндикеардысүйемин", "iloveassasinorum": "менассасинорлардысүйлеймін", "templovenenum": "темпловененум", "templocallidus": "templocall<PERSON>us", "templomaerorus": "темпломаерорус", "templovanus": "темплованус", "oficioasesinorum": "убиствосы", "tarkov": "теков", "40k": "40k", "tetris": "тетрис", "lioden": "лиоден", "ageofempires": "империялерзаманы", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerсигмарзаманы", "civilizationv": "цивилизацияv", "ittakestwo": "екугетеді", "wingspan": "қанатұзындығы", "terraformingmars": "марстытүрлендіру", "heroesofmightandmagic": "күшжәнесиқырбатырлары", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "мифологиялықдәуір", "args": "аргс", "rime": "риме", "planetzoo": "планет<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ар", "outpost2": "алдынала2", "banished": "жоқетілді", "caesar3": "цезарь3", "redalert": "қызылдабыл", "civilization6": "цивилизация6", "warcraft2": "warcraft2", "commandandconquer": "билікжәнежеңіс", "warcraft3": "warcraft3", "eternalwar": "мәңгісоғыс", "strategygames": "стратегиялықойындар", "anno2070": "anno2070", "civilizationgame": "цивилизацияойыны", "civilization4": "цивилизация4", "factorio": "факторио", "dungeondraft": "dungeondraft", "spore": "спор", "totalwar": "жалпысоғыс", "travian": "травиан", "forts": "форты", "goodcompany": "жақсыкомпания", "civ": "сив", "homeworld": "үйәлем", "heidentum": "хайдентум", "aoe4": "aoe4", "hnefatafl": "нәпітатабл", "fasterthanlight": "жарықтанжылдам", "forthekings": "патшаларүшін", "realtimestrategy": "нағызуақыттакерітүрі", "starctaft": "старккафт", "sidmeierscivilization": "сидмейердіңцивилизациясы", "kingdomtwocrowns": "корольдікекітаңбалар", "eu4": "eu4", "vainglory": "өзімшілдік", "ww40k": "жұмыс40к", "godhood": "құдайлық", "anno": "anno", "battletech": "белбеутехника", "malifaux": "мал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "w40k": "w40к", "hattrick": "хеттрик", "davesfunalgebraclass": "дэйвтіңкүлкіліалгебрасабағы", "plagueinc": "плешекинк", "theorycraft": "теорияқұру", "mesbg": "месбг", "civilization3": "цивилизация3", "4inarow": "4қатар", "crusaderkings3": "crusaderkings3", "heroes3": "батыстар3", "advancewars": "алғажорықтар", "ageofempires2": "империялардыңуақыты2", "disciples2": "дисциплдер2", "plantsvszombies": "өсімдіктермензомби", "giochidistrategia": "giochidistrategia", "stratejioyunları": "стратегиялықойындар", "europauniversalis4": "европауниверсалис4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "ғажайыптардәуірі", "dinosaurking": "диноцарь", "worldconquest": "әлемдібағындыру", "heartsofiron4": "теміржүректер4", "companyofheroes": "батырларкомпаниясы", "battleforwesnoth": "бұзылғанвеснотқакүрес", "aoe3": "бадминтон3", "forgeofempires": "империялартемірлеу", "warhammerkillteam": "warhammerклиматобы", "goosegooseduck": "гусакшабақбосамар", "phobies": "фобиялар", "phobiesgame": "фобияларойыны", "gamingclashroyale": "геймингклашрояль", "adeptusmechanicus": "адептусмеканикус", "outerplane": "сыртқыұшақ", "turnbased": "айналымдық", "bomberman": "бамберман", "ageofempires4": "империялардыңжасы4", "civilization5": "цивилизация5", "victoria2": "victoria2", "crusaderkings": "құдіреттіпатшалар", "cultris2": "cultris2", "spellcraft": "сөзшеберлік", "starwarsempireatwar": "жұлдыздысоғысимпериясұрауында", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "попфулмейл", "shiningforce": "жарықкүш", "masterduel": "масте<PERSON><PERSON>l", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "транспорттықтикун", "unrailed": "unrailed", "magicarena": "сиқырлыарена", "wolvesville": "волкостолице", "ooblets": "ooblets", "planescapetorment": "ұшақпеназапқаісу", "uplandkingdoms": "таскөлелдіктер", "galaxylife": "галактикаланым", "wolvesvilleonline": "қасқырларауылонлайн", "slaythespire": "слэйтэспайр", "battlecats": "соғысмысықтары", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "сеймс", "simcity": "симсити", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "ирейсинг", "granturismo": "грантуризм", "needforspeed": "жылдамдыққажет", "needforspeedcarbon": "жылдамдыққажеткөмір", "realracing3": "нағызжарыстар3", "trackmania": "trackmania", "grandtourismo": "грандтуризмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "ссимс1", "lossims4": "соғымым4", "fnaf": "фна<PERSON>", "outlast": "outlast_kz", "deadbydaylight": "кешкіқұмырсқа", "alicemadnessreturns": "алисажанғақайтыпкеледі", "darkhorseanthology": "қаражорғалыларантологиясы", "phasmophobia": "фазмофобия", "fivenightsatfreddys": "бестүнфреддидіңқасында", "saiko": "сайко", "fatalframe": "фатаалдышеңбер", "littlenightmares": "кішкенеқорқыныштар", "deadrising": "жандыжандану", "ladydimitrescu": "ледидимитреску", "homebound": "үйдемін", "deadisland": "өлтірілгенарал", "litlemissfortune": "кішірекбақытсыздық", "projectzero": "жоба0", "horory": "қорқынышты", "jogosterror": "жогостеррор", "helloneighbor": "сәлемғ邻гер", "helloneighbor2": "сәлемкөрші2", "gamingdbd": "gamingdbd", "thecatlady": "мысықтарқыздары", "jeuxhorreur": "қорқыныштыойындар", "horrorgaming": "қорқыныштыойындар", "magicthegathering": "магия<PERSON><PERSON><PERSON><PERSON>тын", "mtg": "mtg", "tcg": "тсг", "cardsagainsthumanity": "адамдарғақарсыкарточкалар", "cribbage": "криби<PERSON><PERSON>", "minnesotamtg": "миннесотамтг", "edh": "едh", "monte": "монте", "pinochle": "пино<PERSON>л", "codenames": "кодтықаттар", "dixit": "дұрысбарасың", "bicyclecards": "велосипедкарталары", "lor": "лор", "euchre": "екер", "thegwent": "гвент", "legendofrunetera": "runeterraлегендасы", "solitaire": "солоарт", "poker": "покер", "hearthstone": "отбасыжүрегі", "uno": "унодосы", "schafkopf": "шахмат", "keyforge": "кілтқұраушы", "cardtricks": "карточкаларменалаяқтық", "playingcards": "ойынкарточкалары", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "гинрумми", "netrunner": "нетраннер", "gwent": "гвент", "metazoo": "метазоо", "tradingcards": "саудакарточкалары", "pokemoncards": "покемонкарталары", "fleshandbloodtcg": "қаныменжәнеетіменбұлкарта", "sportscards": "спорттықкарточкалар", "cardfightvanguard": "карталықсоғысвандард", "duellinks": "дуэльсілтемелері", "spades": "белбеу", "warcry": "соғысдауысы", "digimontcg": "дигимонт<PERSON>г", "toukenranbu": "токенрани<PERSON>у", "kingofhearts": "жүректіңкоролі", "truco": "труко", "loteria": "лотерея", "hanafuda": "ханафуда", "theresistance": "қарсылық", "transformerstcg": "трансформерстцг", "doppelkopf": "доппельkopf", "yugiohcards": "югиохкарталары", "yugiohtcg": "югиохтг", "yugiohduel": "югиохдуэль", "yugiohocg": "югиохкг", "dueldisk": "дуэльдиск", "yugiohgame": "югиохойыны", "darkmagician": "қарасиқыршы", "blueeyeswhitedragon": "көккөзаққұстар", "yugiohgoat": "югиохгату", "briscas": "брискас", "juegocartas": "карта<PERSON>йыны", "burraco": "бур<PERSON><PERSON>o", "rummy": "румми", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "дагбы", "mtgcommander": "mtgкомандалық", "cotorro": "которро", "jeuxdecartes": "карточкалықойындар", "mtgjudge": "mtgсудья", "juegosdecartas": "картаойындары", "duelyst": "дуэлист", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "карточнаяойын", "carteado": "картетао", "sueca": "сүека", "beloteonline": "белотеон<PERSON><PERSON><PERSON>н", "karcianki": "қаржылықойындар", "battlespirits": "бөлімжарастар", "battlespiritssaga": "бұлттаррухтарыэпопеясы", "jogodecartas": "карточкаларменжүгіріп", "žolíky": "жолықы", "facecard": "беткар<PERSON><PERSON><PERSON>ы", "cardfight": "карточкаларкүресі", "biriba": "бірібә", "deckbuilders": "декбилдер<PERSON>ер", "marvelchampions": "марвелчемпиондары", "magiccartas": "сиқырлыкарталар", "yugiohmasterduel": "югиохмастердойысы", "shadowverse": "түбегейліәлем", "skipbo": "skip<PERSON>", "unstableunicorns": "тұрақсызединороги", "cyberse": "киберсе", "classicarcadegames": "классикалықойынзалдары", "osu": "osu", "gitadora": "гитадора", "dancegames": "билойындар", "fridaynightfunkin": "жұмакинофункін", "fnf": "фнф", "proseka": "проуфка", "projectmirai": "ж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectdiva": "жобадива", "djmax": "djmax", "guitarhero": "гитаралыбатыр", "clonehero": "клонгерой", "justdance": "жала<PERSON>у<PERSON>ыт", "hatsunemiku": "хацунэмику", "prosekai": "просякай", "rocksmith": "роксимит", "idolish7": "idolish7", "rockthedead": "өліктердісілкіндір", "chunithm": "чунитм", "idolmaster": "идолмейстер", "dancecentral": "билеорталық", "rhythmgamer": "ритмгеймер", "stepmania": "степмания", "highscorerythmgames": "жоғарыбаллыритмойындар", "pkxd": "pkxd", "sidem": "сайдем", "ongeki": "онгекі", "soundvoltex": "саундволтекс", "rhythmheaven": "ритмжұма", "hypmic": "гипмик", "adanceoffireandice": "отшашуотжалынменмұзбен", "auditiononline": "он<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ұстаздарбизнесі", "cryptofthenecrodancer": "криптофтанекродансер", "rhythmdoctor": "ритмдоктор", "cubing": "кубинг", "wordle": "вордл", "teniz": "теңіз", "puzzlegames": "пазлойындар", "spotit": "spotit", "rummikub": "руммикуб", "blockdoku": "блокдоку", "logicpuzzles": "логикалықпазлдар", "sudoku": "судоку", "rubik": "рубиик", "brainteasers": "тапсырмалар", "rubikscube": "рубикскуб", "crossword": "кроссворд", "motscroisés": "кроссворд", "krzyżówki": "кроссвордтар", "nonogram": "ненограмма", "bookworm": "кітапқұмар", "jigsawpuzzles": "пазлдар", "indovinello": "инковиное", "riddle": "жұмбақ", "riddles": "жұмбақтар", "rompecabezas": "пазл", "tekateki": "текатекі", "inside": "ішінде", "angrybirds": "ашулықұстар", "escapesimulator": "қашусимуляторы", "minesweeper": "минсызғыш", "puzzleanddragons": "пазлжәнеайдаһарлер", "crosswordpuzzles": "кроссвордтар", "kurushi": "күруші", "gardenscapesgame": "бакшамашыойыны", "puzzlesport": "пазлспорт", "escaperoomgames": "қашуойындары", "escapegame": "қашуойыны", "3dpuzzle": "3dпазл", "homescapesgame": "homescapesойыны", "wordsearch": "сөзтабушы", "enigmistica": "энігмистика", "kulaworld": "кулалыдүние", "myst": "мист", "riddletales": "дүрбелеңәңгімелер", "fishdom": "балықәлемі", "theimpossiblequiz": "мүмкінеместест", "candycrush": "candycrush", "littlebigplanet": "кішіүлкенпайда", "match3puzzle": "үштікойын", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "катамаридамасы", "kwirky": "квирки", "rubikcube": "рубиккубы", "cuborubik": "куборусики", "yapboz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "талоcпринципі", "homescapes": "үйсахна", "puttputt": "путпут", "qbert": "qbert", "riddleme": "жұмбақшеш", "tycoongames": "tycoongames", "cubosderubik": "кубосдерубик", "cruciverba": "кроссворд", "ciphers": "шиф<PERSON><PERSON><PERSON>р", "rätselwörter": "жұмбақсөздер", "buscaminas": "бускамина<PERSON>с", "puzzlesolving": "пазлшығарушы", "turnipboy": "картопбала", "adivinanzashot": "адиви<PERSON><PERSON><PERSON><PERSON><PERSON>т", "nobodies": "ешкімдер", "guessing": "бол<PERSON>ау", "nonograms": "нонограммалар", "kostkirubika": "қосткірбік", "crypticcrosswords": "криптиккроссвордтар", "syberia2": "сиберия2", "puzzlehunt": "пазлқұмар", "puzzlehunts": "пазлжhuntтары", "catcrime": "мысыққұқықбұзу", "quebracabeça": "шаршыбұлшықеттер", "hlavolamy": "хлаволамы", "poptropica": "поптропика", "thelastcampfire": "соңғыотшашу", "autodefinidos": "автодефинирленгендер", "picopark": "пикопарк", "wandersong": "а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "картон", "untitledgoosegame": "атаусызқазойыны", "cassetête": "кассетете", "limbo": "лимбо", "rubiks": "рубинка", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "рубиковастка", "speedcube": "жылдамкуб", "pieces": "тұстар", "portalgame": "порталойыны", "bilmece": "билмесе", "puzzelen": "пазлдер", "picross": "пикрос", "rubixcube": "рубитекуб", "indovinelli": "индовинелли", "cubomagico": "кубомагико", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "кодмобил", "codm": "codm", "twistedwonderland": "бұралғанерліктер", "monopoly": "монополия", "futurefight": "болашаққатөбелес", "mobilelegends": "мобильділегендалар", "brawlstars": "brawlstars", "brawlstar": "броудстар", "coc": "кок", "lonewolf": "жекежандық", "gacha": "gacha", "wr": "жұмыс", "fgo": "fgo", "bitlife": "бит<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "пикмин<PERSON>лум", "ff": "фф", "ensemblestars": "ансамбльжұлдыздары", "asphalt9": "асфальт9", "mlb": "млб", "cookierunkingdom": "печенькесырларәлемі", "alchemystars": "алхимиялықжұлдыздар", "stateofsurvival": "тір<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ы", "mycity": "меніңқалам", "arknights": "арканights", "colorfulstage": "түрлітүстісахна", "bloonstowerdefense": "bloonstowerdefense", "btd": "бтд", "clashroyale": "клашройал", "angela": "анджела", "dokkanbattle": "докканбеттер", "fategrandorder": "бақытұлытарorder", "hyperfront": "гиперфронт", "knightrun": "рыцарлықжүгіру", "fireemblemheroes": "отпенрыцарьлар", "honkaiimpact": "хонкайәсер", "soccerbattle": "футболкүресі", "a3": "а3", "phonegames": "телефонойындар", "kingschoice": "ханныңтаңдауы", "guardiantales": "қорғаншыәписодтары", "petrolhead": "бензи<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "tacticool": "тактикалық", "cookierun": "печенькежүгіру", "pixeldungeon": "пиксельтұңғиығы", "arcaea": "arcaea", "outoftheloop": "күтпегенде", "craftsman": "шебер", "supersus": "суперкүдікті", "slowdrive": "баяуотындар", "headsup": "хабардарболыңыз", "wordfeud": "сөзсоғысы", "bedwars": "төсектенсоғыс", "freefire": "freefire", "mobilegaming": "ұялыойындар", "lilysgarden": "lilysgarden", "farmville2": "фармвилл2", "animalcrossing": "жа<PERSON>у<PERSON>р<PERSON><PERSON>рдыасу", "bgmi": "bgmi", "teamfighttactics": "командалықсоғысы", "clashofclans": "кланаққарсысоғыс", "pjsekai": "pjsekai", "mysticmessenger": "мистикалықхатшы", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8шарпул", "emergencyhq": "шұғылмәселеhq", "enstars": "енстарс", "randonautica": "randonautica", "maplestory": "кленб<PERSON>ршігі", "albion": "альбион", "hayday": "хайдай", "onmyoji": "онмёдзи", "azurlane": "азурлан", "shakesandfidget": "шайқауықжәнеқұртқыш", "ml": "мл", "bangdream": "bangdream", "clashofclan": "кланқарамақайшылық", "starstableonline": "жұлдыздысәттікіонлайн", "dragonraja": "драконраджа", "timeprincess": "уақытпринцесса", "beatstar": "битстар", "dragonmanialegend": "жолбарысбатырлегенда", "hanabi": "хана<PERSON>и", "disneymirrorverse": "диснейайзаларәлемі", "pocketlove": "кілтқұшақ", "androidgames": "андроидойындар", "criminalcase": "криминалдықіс", "summonerswar": "суммонерлерсоғысы", "cookingmadness": "аспаздықбастырылу", "dokkan": "доккан", "aov": "аов", "triviacrack": "тривиацарак", "leagueofangels": "періштелерлигада", "lordsmobile": "lordsmobile", "tinybirdgarden": "ккішкентайқұстарбағы", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "меніңәншеймонстрларым", "nekoatsume": "неокоцуме", "bluearchive": "көгілдіркеңес", "raidshadowlegends": "raidshadowlegends", "warrobots": "соғысроботтары", "mirrorverse": "айнаәлем", "pou": "поу", "warwings": "соғысқанаттары", "fifamobile": "fifamobile", "mobalegendbangbang": "мобаледжендбэнбэн", "evertale": "evertale", "futime": "футайм", "antiyoy": "анти<PERSON>й", "apexlegendmobile": "apexlegendmobile", "ingress": "ингресс", "slugitout": "слугитау", "mpl": "mpl", "coinmaster": "монетамастер", "punishinggrayraven": "жазағабаскүші", "petpals": "жа<PERSON><PERSON><PERSON><PERSON>лардостары", "gameofsultans": "sultandynoynuygy", "arenabreakout": "arenabreakout", "wolfy": "вольфий", "runcitygame": "runcitygame", "juegodemovil": "мобильдіойын", "avakinlife": "авакенл<PERSON>н<PERSON>ф", "kogama": "когама", "mimicry": "мимика", "blackdesertmobile": "қарашұңқырмобил", "rollercoastertycoon": "роллеркостертикон", "grandchase": "грандчейс", "bombmebrasil": "бомбимебразил", "ldoe": "ldoe", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "отомегейм", "mindustry": "индустрияныңақылдарының", "callofdragons": "айдаһарларғадауысбер", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "паттанокеремежині", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "бұзужеңісі3", "wordswithfriends2": "достарымменсөздер2", "soulknight": "soulnight", "purrfecttale": "purrfecttale", "showbyrock": "showbyrock", "ladypopular": "әйелдертанымал", "lolmobile": "ллолмобил", "harvesttown": "жинауқаланы", "perfectworldmobile": "keremetjurnaldamobile", "empiresandpuzzles": "империяларменжұмбақтар", "empirespuzzles": "эмпиреспазлдар", "dragoncity": "драконайлымы", "garticphone": "гартікфон", "battlegroundmobileind": "базалықсаладаұрыс", "fanny": "фанни", "littlenightmare": "кіш<PERSON><PERSON><PERSON><PERSON>ан", "aethergazer": "aethergazer", "mudrunner": "балшықайыршы", "tearsofthemis": "жұлдызжасы", "eversoul": "eversoul", "gunbound": "гу<PERSON><PERSON><PERSON><PERSON><PERSON>д", "gamingmlbb": "геймингмлбб", "dbdmobile": "dbdmobile", "arknight": "арк<PERSON><PERSON>т", "pristontale": "пристонтале", "zombiecastaways": "зомбиқашыпкетушілер", "eveechoes": "eveechoes", "jogocelular": "жоғарылатақұлып", "mariokarttour": "mariokartжарысы", "zooba": "зооба", "mobilelegendbangbang": "мобайллегендбангбанг", "gachaclub": "гашаклуб", "v4": "v4", "cookingmama": "аспазшыана妈", "cabalmobile": "каба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "streetfighterduel": "көшежекпежекдодасы", "lesecretdhenri": "лесекретдэнри", "gamingbgmi": "ойнаубгми", "girlsfrontline": "қыздаралдыңжалында", "jurassicworldalive": "jurassicworldтірі", "soulseeker": "жанпазшы", "gettingoverit": "жоғалтыпжөндеу", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "айшағынтарқысы", "carxdriftracingonline": "кархдрифтерейсингонлайн", "jogosmobile": "жабдықтармобиль", "legendofneverland": "әлемсізлегендия", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "тимерұсталушылар", "gamingmobile": "ұялыойын", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "бақылаушымыкөлдер", "dnd": "днд", "quest": "сапар", "giochidiruolo": "giochиди<PERSON><PERSON><PERSON><PERSON>о", "dnd5e": "dnd5e", "rpgdemesa": "rpgдемеса", "worldofdarkness": "қараңғылықәлемі", "travellerttrpg": "саяхатшыtrpg", "2300ad": "2300жыл", "larp": "лар<PERSON>", "romanceclub": "романтиклуб", "d20": "d20", "pokemongames": "покемонойындары", "pokemonmysterydungeon": "покемондүниетайындалысы", "pokemonlegendsarceus": "покемонлегендаларсаркеус", "pokemoncrystal": "покемоносындағыкристал", "pokemonanime": "покемонаниме", "pokémongo": "покемончо", "pokemonred": "покемонтасым", "pokemongo": "покемончо", "pokemonshowdown": "покемонсайысы", "pokemonranger": "покемонсақшы", "lipeep": "липи<PERSON>п", "porygon": "поригон", "pokemonunite": "покемонунаит", "entai": "энтай", "hypno": "гипно", "empoleon": "эмполион", "arceus": "арце<PERSON>с", "mewtwo": "мьюту", "paldea": "палдея", "pokemonscarlet": "покемонқызыл", "chatot": "чатот", "pikachu": "пикачу", "roxie": "roxie", "pokemonviolet": "покемонсиренгін", "pokemonpurpura": "покемонтасқан", "ashketchum": "ашкечум", "gengar": "генгар", "natu": "нату", "teamrocket": "командаракета", "furret": "фуррет", "magikarp": "магика<PERSON>п", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "снорлакс", "pocketmonsters": "кишеніқарақшылар", "nuzlocke": "нузлок", "pokemonplush": "покемонаппақ", "teamystic": "тимистик", "pokeball": "покебол", "charmander": "шармандыр", "pokemonromhack": "покемонтрампинг", "pubgmobile": "pubgmobile", "litten": "латтен", "shinypokemon": "жылтырпокемон", "mesprit": "месприт", "pokémoni": "покемони", "ironhands": "темірқолдар", "kabutops": "кабутопс", "psyduck": "псайдък", "umbreon": "умбреон", "pokevore": "покевор", "ptcg": "птцг", "piplup": "пиплап", "pokemonsleep": "покемонсүғысы", "heyyoupikachu": "хеййопикачу", "pokémonmaster": "покемонмайстеры", "pokémonsleep": "покемонсұлу", "kidsandpokemon": "балаларменпокемон", "pokemonsnap": "покемонснайп", "bulbasaur": "бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lucario": "люкарио", "charizar": "шар<PERSON><PERSON><PERSON>р", "shinyhunter": "шынайықаңшы", "ajedrez": "шахмат", "catur": "сату", "xadrez": "шахмат", "scacchi": "шахмат", "schaken": "шақен", "skak": "скәк", "ajedres": "шахмат", "chessgirls": "шахматқыздары", "magnuscarlsen": "магнускарлсен", "worldblitz": "әлемблиц", "jeudéchecs": "дослыққолдау", "japanesechess": "жапоншахматы", "chinesechess": "қытайшахмат", "chesscanada": "шах<PERSON>атка<PERSON>дa", "fide": "фиде", "xadrezverbal": "сөздестегіш", "openings": "ашылу<PERSON>ар", "rook": "бұт", "chesscom": "шахматком", "calabozosydragones": "калабозосыдрагондар", "dungeonsanddragon": "себетжәнеайлақ", "dungeonmaster": "жерсүйгіш", "tiamat": "тиамат", "donjonsetdragons": "донжонжәнеайдаһарлар", "oxventure": "оксунventure", "darksun": "қаракүн", "thelegendofvoxmachina": "voxmachinaаңызы", "doungenoanddragons": "дungeonжәнеайдахарлар", "darkmoor": "қарабел", "minecraftchampionship": "майнкрафтчемпионаты", "minecrafthive": "minecrafthive", "minecraftbedrock": "майнкрафтбэдрок", "dreamsmp": "арма<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ы", "hermitcraft": "гермитк<PERSON><PERSON><PERSON>т", "minecraftjava": "майнк<PERSON><PERSON><PERSON><PERSON>жаба", "hypixelskyblock": "hypixelskyblock", "minetest": "минетест", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "майнкрафтмодтары", "mcc": "mcc", "candleflame": "шамталшағы", "fru": "фру", "addons": "қосымшалар", "mcpeaddons": "mcpeқосымшалары", "skyblock": "аспанблок", "minecraftpocket": "майнкрафтсашығы", "minecraft360": "minecraft360", "moddedminecraft": "модификацияланғанмайнкрафт", "minecraftps4": "minecraftps4", "minecraftpc": "майнкрафтпк", "betweenlands": "аралдарарасында", "minecraftdungeons": "майнкрафттұңғыштары", "minecraftcity": "майнкрафтқала", "pcgamer": "пцгеймер", "jeuxvideo": "ойынжұмыстары", "gambit": "gambit", "gamers": "геймерлер", "levelup": "деңгейкөтер", "gamermobile": "геймермобиль", "gameover": "ойынсақталған", "gg": "gg", "pcgaming": "компьютерлікойындар", "gamen": "gamen", "oyunoynamak": "ойнайық", "pcgames": "компьютерлікойындар", "casualgaming": "кездейсоқойындар", "gamingsetup": "ойынорналастыру", "pcmasterrace": "компьютерұстаушылары", "pcgame": "компьютерлікойын", "gamerboy": "геймербой", "vrgaming": "выргейминг", "drdisrespect": "докторнедопустимость", "4kgaming": "4к<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "gamerbr": "gamerbr", "gameplays": "ойыннегіздері", "consoleplayer": "консолойыншысы", "boxi": "boxi", "pro": "профи", "epicgamers": "эпикалықгеймерлер", "onlinegaming": "онлайнойындар", "semigamer": "семигеймер", "gamergirls": "геймерқыздар", "gamermoms": "гейме<PERSON><PERSON><PERSON><PERSON><PERSON>р", "gamerguy": "гейме<PERSON><PERSON><PERSON><PERSON><PERSON>т", "gamewatcher": "ойынкөрер", "gameur": "геймер", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "геймерқыздар", "otoge": "отоге", "dedsafio": "дедсфайо", "teamtryhard": "командақарқынды", "mallugaming": "мәллугейминг", "pawgers": "пауғарлар", "quests": "тапсырмалар", "alax": "алах", "avgn": "avgn", "oldgamer": "ескігеймер", "cozygaming": "жайлыойындар", "gamelpay": "gamelpay", "juegosdepc": "компьютерлікойындар", "dsswitch": "dsswitch", "competitivegaming": "бәсекелікойындар", "minecraftnewjersey": "минек<PERSON><PERSON>фтжерси", "faker": "фейкер", "pc4gamers": "pc4ойыншылар", "gamingff": "гейминг<PERSON>ф", "yatoro": "ятороу", "heterosexualgaming": "гетеросексуалдаройыны", "gamepc": "ойынпк", "girlsgamer": "қыздаргеймер", "fnfmods": "fnfмодтары", "dailyquest": "күнделіктімиссия", "gamegirl": "ойынқыз", "chicasgamer": "қызықгеймерлер", "gamesetup": "ойынжабдықтары", "overpowered": "артықкүш", "socialgamer": "әлеуметтікгеймер", "gamejam": "ойынжам", "proplayer": "проигрок", "roleplayer": "рөлдемеші", "myteam": "меніңкомандам", "republicofgamers": "геймерлерреспубликасы", "aorus": "аорустың", "cougargaming": "куграйындар", "triplelegend": "үшlegend", "gamerbuddies": "геймердостар", "butuhcewekgamers": "бұтұқызгеймерлер", "christiangamer": "христианжақсыойыншы", "gamernerd": "геймерпәлсапа", "nerdgamer": "нердгеймер", "afk": "afk", "andregamer": "андрег<PERSON>ймер", "casualgamer": "кездейсоқойыншы", "89squad": "89сұңқар", "inicaramainnyagimana": "іникармайынняғимина", "insec": "инсек", "gemers": "гемерлер", "oyunizlemek": "ойыначығу", "gamertag": "геймердизайны", "lanparty": "ланпартия", "videogamer": "видеогеймер", "wspólnegranie": "біргебарамыз", "mortdog": "мортдок", "playstationgamer": "плейстейшнойыншысы", "justinwong": "жастинвонг", "healthygamer": "денсаулықдосы", "gtracing": "gtracing", "notebookgamer": "ноутбукгеймер", "protogen": "протоген", "womangamer": "әйелгеймер", "obviouslyimagamer": "әкелеаянғымдегенжәнегеймер", "mario": "марио", "papermario": "қағазмария", "mariogolf": "мариогольф", "samusaran": "шаму<PERSON>а<PERSON>ан", "forager": "жина<PERSON><PERSON>ы", "humanfallflat": "адамжарғасалмақ", "supernintendo": "супернитендо", "nintendo64": "nintendo64", "zeroescape": "нөлқашу", "waluigi": "валуиджи", "nintendoswitch": "nintendoswitch", "nintendosw": "нинтендосв", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "сониктошқаны", "sonic": "соник", "fallguys": "күзгіназарлар", "switch": "ауыстыру", "zelda": "зелда", "smashbros": "smashbros", "legendofzelda": "zeldaаңыздары", "splatoon": "сплатун", "metroid": "метроид", "pikmin": "пикмин", "ringfit": "саптаспен", "amiibo": "амиибо", "megaman": "мегаман", "majorasmask": "мажорасмаска", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>б<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "асқатқұқыққорғаушы", "ssbm": "ssbm", "skychildrenofthelight": "аспанбалаларжарықтың", "tomodachilife": "томодачилоlife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "патшалықтыңжылауы", "walkingsimulators": "жүріптұрусимуляторлары", "nintendogames": "nintendоойындар", "thelegendofzelda": "zeldaаңыздарыныңаңыз", "dragonquest": "айдаһарквест", "harvestmoon": "жинауқарасы", "mariobros": "мариобратандар", "runefactory": "рунефабрика", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "селесте", "breathofthewild": "wildтыңтынысы", "myfriendpedro": "меніңдосымпедро", "legendsofzelda": "zeldaаңыздары", "donkeykong": "досымконг", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51ойын", "earthbound": "жердег<PERSON>м", "tales": "ертегілер", "raymanlegends": "рэймэнлегендтары", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "жа<PERSON><PERSON><PERSON><PERSON>ларкесігі", "taikonotatsujin": "тайконатадзин", "nintendo3ds": "nintendo3ds", "supermariobros": "супермариброс", "mariomaker2": "mariomaker2", "boktai": "бок<PERSON>ай", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "үшбұрышстратегиясы", "supermariomaker": "супермериомейкер", "xenobladechronicles3": "ксеноблейдхроникалары3", "supermario64": "супермарио64", "conkersbadfurday": "конкерсжаманшашкүндері", "nintendos": "nintendos", "new3ds": "жаңа3ds", "donkeykongcountry2": "донкиконгелемі2", "hyrulewarriors": "гидулексарбаздары", "mariopartysuperstars": "мариопартиясуперзвездалар", "marioandsonic": "marioжәнеsonic", "banjotooie": "бан<PERSON><PERSON><PERSON><PERSON>й", "nintendogs": "ниндогтар", "thezelda": "thezelda", "palia": "палиа", "marioandluigi": "мариожәнелуиджи", "mariorpg": "mariorpg", "zeldabotw": "зельдабатыңыз", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "жабайытолқын", "riven": "ривен", "ahri": "ахри", "illaoi": "иллаои", "aram": "арам", "cblol": "cbжынды", "leagueoflegendslas": "лигаеслегендалар", "urgot": "urgot", "zyra": "зыра", "redcanids": "redcanids", "vanillalol": "ван<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftkk", "lolph": "лолпх", "leagueoflegend": "л宣传аныжасау", "tốcchiến": "токчиен", "gragas": "грагас", "leagueoflegendswild": "лигааңыздаржабайы", "adcarry": "adcarry", "lolzinho": "лолзиньо", "leagueoflegendsespaña": "leagueoflegendsқазақстан", "aatrox": "аатрокс", "euw": "еуи", "leagueoflegendseuw": "лигаегендердіеуропа", "kayle": "кайлы", "samira": "самира", "akali": "акали", "lunari": "лунари", "fnatic": "fnatic", "lollcs": "лолкөп", "akshan": "ақшам", "milio": "milio", "shaco": "шако", "ligadaslegendas": "лигадәслеґендалар", "gaminglol": "гейминглол", "nasus": "насус", "teemo": "тимо", "zedmain": "зедмейн", "hexgates": "гексқақпалары", "hextech": "хектех", "fortnitegame": "фортнайтойыны", "gamingfortnite": "фортнайтойнау", "fortnitebr": "фортнайтбр", "retrovideogames": "ретровидеойындар", "scaryvideogames": "қорқыныштыойындар", "videogamemaker": "видеойнамашы", "megamanzero": "мегаманзеро", "videogame": "видеоигра", "videosgame": "видеоигра", "professorlayton": "профессорлейтон", "overwatch": "бақылау", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "сұңқар101", "battleblocktheater": "battleblockтеатры", "arcades": "аркада<PERSON><PERSON>р", "acnh": "acnh", "puffpals": "пухпалылар", "farmingsimulator": "агросимулятор", "robloxchile": "robloxchilekk", "roblox": "роблокс", "robloxdeutschland": "robloxқазақстан", "robloxdeutsch": "robloxқазақ", "erlc": "erlc", "sanboxgames": "сандбоксойындар", "videogamelore": "видеойынджанр", "rollerdrome": "роллердром", "parasiteeve": "паразитеве", "gamecube": "геймкуб", "starcraft2": "старкрафт2", "duskwood": "кешқұрақ", "dreamscape": "арманшаңғы", "starcitizen": "жұлдызазаматы", "yanderesimulator": "yanderesimulator", "grandtheftauto": "грантфетауто", "deadspace": "жалғызбосорын", "amordoce": "амордоце", "videogiochi": "видеойындар", "theoldrepublic": "ескіреспубликa", "videospiele": "ойынкөріністер", "touhouproject": "touhouproject", "dreamcast": "арманжүргізу", "adventuregames": "приключенияойындары", "wolfenstein": "вольфенштейн", "actionadventure": "әрекетжәнетаңдау", "storyofseasons": "маусымдардарысы", "retrogames": "ретрогеймдер", "retroarcade": "ретроаркейд", "vintagecomputing": "жұртжақсыстарату", "retrogaming": "ретрогейминг", "vintagegaming": "көнеойындар", "playdate": "ойынкүні", "commanderkeen": "командир<PERSON>ин", "bugsnax": "bugsnax", "injustice2": "әділетсіздік2", "shadowthehedgehog": "теньқояншық", "rayman": "райман", "skygame": "аспанойын", "zenlife": "зенөмір", "beatmaniaiidx": "битманияиidx", "steep": "тік<PERSON><PERSON>к", "mystgames": "mystgames", "blockchaingaming": "блокчейнойындары", "medievil": "ортағасырға", "consolegaming": "консольойындар", "konsolen": "консолен", "outrun": "жүгіріпжету", "bloomingpanic": "бұлақтарбұзылады", "tobyfox": "tobyfox", "hoyoverse": "хойоверс", "senrankagura": "сенранкагура", "gaminghorror": "ойынқорқынышты", "monstergirlquest": "монстрқызсапары", "supergiant": "супергигант", "disneydreamlightvalle": "диснейармандарыжарығыдаласы", "farmingsims": "аграрлықсимс", "juegosviejos": "ескіойындар", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "интерактивтіәдебиет", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "соңғынықашан2", "amantesamentes": "сүюдеболар", "visualnovel": "визуалдықроман", "visualnovels": "көркемромандар", "rgg": "ргг", "shadowolf": "сұрқасқа", "tcrghost": "тcrghost", "payday": "жалақы", "chatherine": "катерин", "twilightprincess": "батысханшайымы", "jakandaxter": "<PERSON><PERSON>к<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "sandbox": "песочница", "aestheticgames": "эстетикалықойындар", "novelavisual": "жазбашығысөз", "thecrew2": "топ2", "alexkidd": "alex<PERSON><PERSON><PERSON>", "retrogame": "ретрогейм", "tonyhawkproskater": "тонихоукпрофскейтинг", "smbz": "снімз", "lamento": "ларментос", "godhand": "құдайдыңқолы", "leafblowerrevolution": "жапырақсорғышреволюция", "wiiu": "вииу", "leveldesign": "деңгейдизайны", "starrail": "жұлдызтемір", "keyblade": "кілтжүзік", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "визуалдықромандар", "robloxbrasil": "roboxqazaq", "pacman": "пакман", "gameretro": "ойынретро", "videojuejos": "видеойындар", "videogamedates": "видеойынкештер", "mycandylove": "меніңконфетсүйемді", "megaten": "мегатен", "mortalkombat11": "морталкомбат11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "хулькойындар", "batmangames": "батманойындары", "returnofreckoning": "қайтақайтару", "gamstergaming": "гамстеройын", "dayofthetantacle": "пентеклекүні", "maniacmansion": "маньякмэншн", "crashracing": "құлатыпжарыс", "3dplatformers": "3dплатформерлер", "nfsmw": "nfsmw", "kimigashine": "киміг<PERSON><PERSON>ыны", "oldschoolgaming": "ескігейминг", "hellblade": "тозақтыңиесі", "storygames": "әңгімеойындары", "bioware": "биовари", "residentevil6": "резидентзло6", "soundodger": "дыбыстапқыш", "beyondtwosouls": "beyondtwosouls", "gameuse": "ойнайық", "offmortisghost": "offmortisghost", "tinybunny": "кишілікқазбала", "retroarch": "ретроарх", "powerup": "қуаттандыру", "katanazero": "катаназеро", "famicom": "фамикон", "aventurasgraficas": "графикалықприключения", "quickflash": "жыл<PERSON>а<PERSON><PERSON><PERSON><PERSON>у", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "ретроаркейлер", "f123": "f123", "wasteland": "терістік", "powerwashsim": "қуатжуусим", "coralisland": "коралдыарал", "syberia3": "syberia3", "grymmorpg": "гримморпг", "bloxfruit": "bloxfruit", "anotherworld": "басқаәлем", "metaquest": "мета<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>к", "animewarrios2": "ани<PERSON><PERSON><PERSON><PERSON>нкешілері2", "footballfusion": "футболфьюжн", "edithdlc": "edithdlc", "abzu": "абзу", "astroneer": "астронер", "legomarvel": "легомарвел", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "бұралғанметал", "beamngdrive": "beamngdrive", "twdg": "тwdг", "pileofshame": "ұялыпжатқанжинақ", "simulator": "симулятор", "symulatory": "симуляторлар", "speedrunner": "жылдамжүгіруші", "epicx": "эпикс", "superrobottaisen": "суперроботпаясен", "dcuo": "дцуо", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "гая<PERSON><PERSON><PERSON><PERSON><PERSON>н", "korkuoyunu": "қорқынышойыны", "wonderlandonline": "wonderlandonline", "skylander": "аспанжорға", "boyfrienddungeon": "бойфрендтүзегі", "toontownrewritten": "тоунтаунқайтажазылды", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "pvp", "urbanchaos": "қалалықшошқалық", "heavenlybodies": "аспандағыденелер", "seum": "сүйікті", "partyvideogames": "партиойындар", "graveyardkeeper": "зиратқоралы", "spaceflightsimulator": "ғарышұшақсимуляторы", "legacyofkain": "kainніңмұрасы", "hackandslash": "бөліпалыпжой", "foodandvideogames": "тамақжәнеойындар", "oyunvideoları": "ойынвидеолары", "thewolfamongus": "біздіңарамыздақасқыр", "truckingsimulator": "жүктасымалсимуляторы", "horizonworlds": "horizonworlds", "handygame": "үйреншіктіойын", "leyendasyvideojuegos": "ойындардыңлегендалары", "oldschoolvideogames": "ескімектепойындары", "racingsimulator": "жарыссимуляторы", "beemov": "bee<PERSON>v", "agentsofmayhem": "жындарменкүрескерлер", "songpop": "әнсайыс", "famitsu": "фамитсу", "gatesofolympus": "олимпдересі", "monsterhunternow": "монстраңшыендіқазір", "rebelstar": "бунтаржұлдыз", "indievideogaming": "независимаяойыниндустрия", "indiegaming": "индиойындар", "indievideogames": "индиойындар", "indievideogame": "индиойын", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "спайдерменинсомниак", "bufffortress": "бұрқыратыпқорған", "unbeatable": "жеңілмейтін", "projectl": "проекция", "futureclubgames": "болашақклубойындары", "mugman": "бұтман", "insomniacgames": "ұйқысызойыншылар", "supergiantgames": "суперүлкенойындар", "henrystickman": "хенристикмен", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "целестегейм", "aperturescience": "бұрыштықғылым", "backlog": "бэклог", "gamebacklog": "ойынжинақ", "gamingbacklog": "ойынтергенк<PERSON>ш", "personnagejeuxvidéos": "персонажойындар", "achievementhunter": "жетістікаңшылаушы", "cityskylines": "қаланыңкөріністері", "supermonkeyball": "супермаймылдомалағы", "deponia": "депония", "naughtydog": "бұзақыитен", "beastlord": "жыртқышбилік", "juegosretro": "ескіойындар", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "аланжарық", "stanleyparable": "стенлиповести", "reservatoriodedopamin": "дофаминдересурсы", "staxel": "staxel", "videogameost": "видеойынсаундтрек", "dragonsync": "драго<PERSON><PERSON><PERSON>нк", "vivapiñata": "вивапиньята", "ilovekofxv": "кокфхгужем", "arcanum": "арканум", "neoy2k": "нео2000", "pcracing": "пц<PERSON>а<PERSON>ыс", "berserk": "бершерк", "baki": "бақы", "sailormoon": "сейлорөлуя", "saintseiya": "сэйнтсэйя", "inuyasha": "инуюаша", "yuyuhakusho": "ююхакишо", "initiald": "бастама<PERSON>ы", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "драгонбол<PERSON>з", "sadanime": "бозарғананиме", "darkerthanblack": "қараданқұлақы", "animescaling": "анимешкалинг", "animewithplot": "анимеақпаратпен", "pesci": "песчи", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентаи", "samuraichamploo": "самурайшампу", "madoka": "мадока", "higurashi": "хигу<PERSON><PERSON><PERSON>и", "80sanime": "80аниме", "90sanime": "90сәніме", "darklord": "қаралорд", "popeetheperformer": "попиperformer", "masterpogi": "мәртебелипоги", "samuraix": "самурайx", "dbgt": "дбгт", "veranime": "жасырынбасыңыз", "2000sanime": "2000аниме", "lupiniii": "лупиниии", "drstoneseason1": "доктортасмаseason1", "rapanime": "рэпаниме", "chargemanken": "зарядтапалдың", "animecover": "анимеобложка", "thevisionofescaflowne": "эскафлоуннегезі", "slayers": "өлтірушілер", "tokyomajin": "токио_мажын", "anime90s": "90жылғыаниме", "animcharlotte": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>та", "gantz": "ганд<PERSON>", "shoujo": "шоудзё", "bananafish": "бананбалық", "jujutsukaisen": "джу<PERSON>утсукайсен", "jjk": "жжк", "haikyu": "ха<PERSON><PERSON>ю", "toiletboundhanakokun": "дәретханағабайланғанханакокун", "bnha": "бнха", "hellsing": "хел<PERSON><PERSON><PERSON><PERSON>г", "skipbeatmanga": "скепбитманга", "vanitas": "ванитаس", "fireforce": "оттыкүш", "moriartythepatriot": "мориартипатриот", "futurediary": "болашақкүнделігі", "fairytail": "ертегітоқыны", "dorohedoro": "дороходоро", "vinlandsaga": "вийнландсагасы", "madeinabyss": "abyssтажасалған", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "шинганнокиодзин", "mushishi": "мүшісіш", "beastars": "биста<PERSON><PERSON>т<PERSON>р", "vanitasnocarte": "ванитаснокарте", "mermaidmelody": "балыққыздыңәндері", "kamisamakiss": "камисамақұшақ", "blmanga": "бломанга", "horrormanga": "хоррорманга", "romancemangas": "романтикмангалар", "karneval": "карнаваль", "dragonmaid": "драконқыз", "blacklagoon": "қарабалық", "kentaromiura": "кентароимура", "mobpsycho100": "мобпсихо100", "terraformars": "терраформарс", "geniusinc": "геніу<PERSON><PERSON><PERSON>к", "shamanking": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "жүго", "bungostraydogs": "бунгострайдогс", "jujustukaisen": "жужұстукайсен", "jujutsu": "жүзжүсту", "yurionice": "юриб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а", "acertainmagicalindex": "классикалықсиқырлыиндекс", "sao": "sao", "blackclover": "қарасоеу", "tokyoghoul": "токийскийгул", "onepunchman": "бірсоққымен", "hetalia": "геталия", "kagerouproject": "kagerouproject", "haikyuu": "хай<PERSON><PERSON>у", "toaru": "тауру", "crunchyroll": "crunchyroll", "aot": "аот", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "шпионотбасы", "rezero": "резеро", "swordartonline": "мечта<PERSON><PERSON><PERSON><PERSON>н<PERSON>лайн", "dororo": "дороро", "wondereggpriority": "wondereggбасымдық", "angelsofdeath": "өлтіргішперіште", "kakeguri": "какеғұри", "dragonballsuper": "драгонболлсупер", "hypnosismic": "гипносмик", "goldenkamuy": "алтынкамуй", "monstermusume": "монстрпəкелер", "konosuba": "коносуба", "aikatsu": "aikatsu", "sportsanime": "спортаниме", "sukasuka": "сукәсука", "arwinsgame": "арвинніңойыны", "angelbeats": "періштеырлары", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "зұлымдықтыжою", "shounenanime": "шуненаниме", "bandori": "бандори", "tanya": "таня", "durarara": "дюрарара", "prettycure": "красимизорлық", "theboyandthebeast": "ұлменжануар", "fistofthenorthstar": "северлікжұлдыздыңсақинасы", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "қарақалпақ", "towerofgod": "құдайдыңманына", "elfenlied": "елфенлид", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "чи<PERSON><PERSON>", "servamp": "сервамп", "howtokeepamummy": "ананықалайұстау", "fullmoonwosagashite": "толықайкосагашитэ", "shugochara": "шугочара", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "сүйкімдіжәнеқатерлі", "martialpeak": "жауынгердіңшыңы", "bakihanma": "бақиханма", "hiscoregirl": "жоғарыбалдар", "orochimaru": "орочимару", "mierukochan": "мие<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "dabi": "да<PERSON>и", "johnconstantine": "джонконстантин", "astolfo": "астолфо", "revanantfae": "реванантфея", "shinji": "шин<PERSON>и", "zerotwo": "нөлекі", "inosuke": "иносукэ", "nezuko": "незуко", "monstergirl": "монстрқыз", "kanae": "канае", "yone": "жақсы", "mitsuki": "митсуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "меніңару", "saitama": "сайтама", "sanji": "санд<PERSON>и", "bakugo": "бакуго", "griffith": "гриффит", "ririn": "ри<PERSON><PERSON><PERSON>н", "korra": "көрра", "vanny": "вани", "vegeta": "вегета", "goromi": "гороми", "luci": "лүчи", "reigen": "райген", "scaramouche": "сқарамушты", "amiti": "амити", "sailorsaturn": "саул<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "dio": "дио", "sailorpluto": "сайлорплуто", "aloy": "алой", "runa": "runa", "oldanime": "ескіаниме", "chainsawman": "chainsawman", "bungoustraydogs": "бунгострайдогтар", "jogo": "жоғу", "franziska": "франциска", "nekomimi": "некомими", "inumimi": "инумими", "isekai": "исекай", "tokyorevengers": "tokyorevengers", "blackbutler": "қарашұбаркүйеу", "ergoproxy": "ергорекси", "claymore": "клеймор", "loli": "лоли", "horroranime": "хоррораниме", "fruitsbasket": "жемісалаңы", "devilmancrybaby": "шайтанменжылапжатсың", "noragami": "норагами", "mangalivre": "мангаливре", "kuroshitsuji": "кюрошитсудзи", "seinen": "сейнен", "lovelive": "сүйемінтірілу", "sakuracardcaptor": "сакуракарткаптер", "umibenoetranger": "умібенойотранжер", "owarinoseraph": "оваринозераф", "thepromisedneverland": "уәдееткенжер", "monstermanga": "монстрмандар", "yourlieinapril": "сенінақысыңдаапрель", "buggytheclown": "баггицир<PERSON><PERSON>ы", "bokunohero": "бокуногерой", "seraphoftheend": "соңыныңсерігі", "trigun": "тригун", "cyborg009": "кибер009", "magi": "мәсі", "deepseaprisoner": "тереңтеңізтұтқыны", "jojolion": "jojo<PERSON>", "deadmanwonderland": "өліпқалғыштардыңжерім", "bannafish": "bannafish", "sukuna": "сукүнә", "darwinsgame": "дарвинніңойыны", "husbu": "хусбу", "sugurugeto": "сигурудепутат", "leviackerman": "левиаккерман", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "пандоражүректер", "yoimiya": "йомийя", "foodwars": "асжарысы", "cardcaptorsakura": "карточкалифтерсакура", "stolas": "столас", "devilsline": "жындардыңсызығы", "toyoureternity": "сеніңмәңгілігіңе", "infpanime": "инфпаниме", "eleceed": "elec<PERSON>", "akamegakill": "акомегакилл", "blueperiod": "көккезең", "griffithberserk": "гриффитберсерк", "shinigami": "шинигами", "secretalliance": "жасырынодақ", "mirainikki": "мир<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "mahoutsukainoyome": "махотсукайнойоме", "yuki": "юки", "erased": "кетілген", "bluelock": "блюлок", "goblinslayer": "гоблинжоюшы", "detectiveconan": "детективконан", "shiki": "шики", "deku": "деку", "akitoshinonome": "акитоүшінанемес", "riasgremory": "риасгремори", "shojobeat": "шож<PERSON>бит", "vampireknight": "вампирдіңрыцары", "mugi": "муги", "blueexorcist": "көкэкзорцист", "slamdunk": "слэмданк", "zatchbell": "за<PERSON><PERSON><PERSON><PERSON>л", "mashle": "машле", "scryed": "сқиған", "spyfamily": "шпионотбасы", "airgear": "аэрод<PERSON><PERSON><PERSON><PERSON>р", "magicalgirl": "сиқырлықыз", "thesevendeadlysins": "осыжетіспеушіліктер", "prisonschool": "тұтқындымектеп", "thegodofhighschool": "мектептегибоггаршы", "kissxsis": "поце<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "грандблю", "mydressupdarling": "меніңкиімқойылымым", "dgrayman": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "rozenmaiden": "розенмайден", "animeuniverse": "анимеғаламы", "swordartonlineabridge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saoabridged": "saoабриджд", "hoshizora": "хошизора", "dragonballgt": "драгонболлтг", "bocchitherock": "боччитерок", "kakegurui": "какеґуруи", "mobpyscho100": "мобпсихо100", "hajimenoippo": "хаджименоайппо", "undeadunluck": "жандыжоғарлық", "romancemanga": "романтикманга", "blmanhwa": "блманхва", "kimetsunoyaba": "киметсунояба", "kohai": "<PERSON><PERSON><PERSON><PERSON>", "animeromance": "анимеромантика", "senpai": "сенпай", "blmanhwas": "бл<PERSON><PERSON><PERSON><PERSON><PERSON>ас", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "демонслайтернегебару", "bloodlad": "қанық_балалар", "goodbyeeri": "сауболери", "firepunch": "отша<PERSON>у", "adioseri": "адьосери", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "кинни<PERSON>у<PERSON>ан", "mushokutensei": "мүшокутенсей", "shoujoai": "шодзё<PERSON>й", "starsalign": "жұлдыздаржолықпайды", "romanceanime": "романтикааниме", "tsundere": "цундере", "yandere": "яндере", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "кенгенаашура", "saointegralfactor": "саоинтегралфактор", "cherrymagic": "шиесиқырығы", "housekinokuni": "үйкинокүні", "recordragnarok": "жазбарамрагнарок", "oyasumipunpun": "оясумпунпун", "meliodas": "мелиодас", "fudanshi": "фуданьши", "retromanga": "ретроманга", "highschoolofthedead": "жоғарымектептегіөліктер", "germantechno": "герман테хно", "oshinoko": "ошиноко", "ansatsukyoushitsu": "ансатсүкіүшті", "vindlandsaga": "vindlandsaga", "mangaka": "меңкакалар", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "теннистіңпринці", "tonikawa": "тоникава", "esdeath": "esdeath", "dokurachan": "докурачан", "bjalex": "бжәлек", "assassinclassroom": "ассасинсынып", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "өлімшеру", "shokugekinosouma": "шокугекиносоума", "japaneseanime": "жапонаниме", "animespace": "анимесфера", "girlsundpanzer": "қыздарджонпанцер", "akb0048": "акб0048", "hopeanuoli": "үмітжолында", "animedub": "анимедаб", "animanga": "аниманга", "tsurune": "цуруне", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "индианиме", "bungoustray": "бунгострай", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "гундом0", "animescifi": "анимеғзи", "ratman": "тырысқақадам", "haremanime": "haremanime", "kochikame": "кочикаме", "nekoboy": "некобой", "gashbell": "gashbell", "peachgirl": "терекешка", "cavalieridellozodiaco": "кавалериделозодиако", "mechamusume": "мэчамусуме", "nijigasaki": "ниджигасаки", "yarichinbitchclub": "яри<PERSON><PERSON><PERSON><PERSON><PERSON>чклуб", "dragonquestdai": "драгонквестдай", "heartofmanga": "манганыңжүрегі", "deliciousindungeon": "бөлмедегідәмділік", "manhviyaoi": "манхвияои", "recordofragnarok": "рагнарокжазбасы", "funamusea": "фуномусеа", "hiranotokagiura": "хирадкөкшесі", "mangaanime": "mangaаниме", "bochitherock": "бочтыррок", "kamisamahajimemashita": "камиса<PERSON><PERSON>х<PERSON>жимемашита", "skiptoloafer": "скіптолафер", "shuumatsunovalkyrie": "шуматсуновалькирие", "tutorialistoohard": "оқулыққатты", "overgeared": "overgeared", "toriko": "торико", "ravemaster": "равемастер", "kkondae": "ккондае", "chobits": "чобитс", "witchhatatelier": "сукерекшеберханасы", "lansizhui": "лан<PERSON><PERSON><PERSON><PERSON>и", "sangatsunolion": "сагатсунолион", "kamen": "камень", "mangaislife": "mangaөмір", "dropsofgod": "құдайсудыңтамшылары", "loscaballerosdelzodia": "зодиакардынжигиттері", "animeshojo": "анимеқыз", "reverseharem": "реверсхарем", "saintsaeya": "сентсая", "greatteacheronizuka": "кереметмұғалімонизука", "gridman": "gridman", "kokorone": "кокороне", "soldato": "солдат", "mybossdaddy": "меніңбоссәкем", "gear5": "gear5", "grandbluedreaming": "үлкенкөкармандар", "bloodplus": "кровьплюс", "bloodplusanime": "қанплюсаниме", "bloodcanime": "қананиме", "bloodc": "қантүкіру", "talesofdemonsandgods": "шайтанжарларыжәнеқұдайлар", "goreanime": "горуаниме", "animegirls": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "ғауһар", "crowsxworst": "қарғаxжаман", "splatteranime": "сплэттераниме", "splatter": "сараптау", "risingoftheshieldhero": "қалқаныңбатырларыныңкөтерілуі", "somalianime": "сомалианиме", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "slimedattaken", "animeyuri": "анимеюри", "animeespaña": "анимеиспания", "animeciudadreal": "анимеқалареал", "murim": "мури<PERSON>м", "netjuunosusume": "нетжууносума", "childrenofthewhales": "бухторлықбалалар", "liarliar": "жалғанжалған", "supercampeones": "суперчемпиондар", "animeidols": "анимеидолдар", "isekaiwasmartphone": "исекайменсмартфондыбілді", "midorinohibi": "мидориногиби", "magicalgirls": "сиқырлықыздар", "callofthenight": "түнніңшақыруы", "bakuganbrawler": "бакуганбравлер", "bakuganbrawlers": "бакуга́<PERSON><PERSON>абуылдаушылар", "natsuki": "натсуки", "mahoushoujo": "махошоуджо", "shadowgarden": "көлеңкедәуіт", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "принцессажабықжәнебалық", "kuragehime": "курожихиме", "paradisekiss": "жұмақтыпоцелуй", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "ревюзвездалыңы", "animeverse": "анимеәлемі", "persocoms": "персоокомдар", "omniscientreadersview": "барлықбілушылыларкөзқарасы", "animecat": "анимемысық", "animerecommendations": "анимеұсыныстары", "openinganime": "<PERSON><PERSON><PERSON>", "shinichirowatanabe": "шинчир<PERSON><PERSON><PERSON><PERSON><PERSON>набе", "uzumaki": "узумакі", "myteenromanticcomedy": "меніңжасөспірімромантикалықкомедиям", "evangelion": "евангелион", "gundam": "gundam", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гундомдар", "voltesv": "волтесв", "giantrobots": "гигантроботтар", "neongenesisevangelion": "неонгенезисевангелион", "codegeass": "кодгеас", "mobilefighterggundam": "мобильныйкүресggundam", "neonevangelion": "неоневангелион", "mobilesuitgundam": "мобилsuitгундом", "mech": "мех", "eurekaseven": "еурекажеті", "eureka7": "еурека7", "thebigoanime": "биганиме", "bleach": "блич", "deathnote": "өлімжазбасы", "cowboybebop": "ковбойбебоп", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "полнеметаллалхимик", "ghiaccio": "мұз", "jojobizarreadventures": "жожобизарептері", "kamuiyato": "камүйято", "militaryanime": "әскерианиме", "greenranger": "жасылбүркіт", "jimmykudo": "жимикудо", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "зорро", "leonscottkennedy": "леонскотткенниди", "korosensei": "коросенсей", "starfox": "жұлдыздыбүлік", "ultraman": "ультраман", "salondelmanga": "салонделманга", "lupinthe3rd": "лүпин3рд", "animecity": "анимеқала", "animetamil": "аниметамил", "jojoanime": "jojoаниме", "naruto": "наруто", "narutoshippuden": "нарутошиппуден", "onepiece": "бірбөлік", "animeonepiece": "анимеописы", "dbz": "dbz", "dragonball": "драгонбол", "yugioh": "югио", "digimon": "дидимон", "digimonadventure": "дигимонприключение", "hxh": "hxх", "highschooldxd": "мектепдxd", "goku": "гоку", "broly": "броны", "shonenanime": "шоненаниме", "bokunoheroacademia": "бокуножігіттеракадемиясы", "jujustukaitsen": "жүзжүзшоқысың", "drstone": "дрстоун", "kimetsunoyaiba": "кимецунояиба", "shonenjump": "шонендзаһар", "otaka": "отака", "hunterxhunter": "ханте<PERSON><PERSON><PERSON><PERSON>", "mha": "mha", "demonslayer": "демонжойғыш", "hinokamikagurademonsl": "хинокамикагурадемондар", "attackontitan": "титанғашабуыл", "erenyeager": "еретапсырып", "myheroacademia": "меніңгероемакадемиям", "boruto": "боруто", "rwby": "rwby", "dandadan": "да<PERSON><PERSON><PERSON>н", "tomodachigame": "томодачигейм", "akatsuki": "акацуки", "surveycorps": "сұрақтарбригадсы", "onepieceanime": "бүтінбірнәрсеаниме", "attaquedestitans": "аттакедеститанс", "theonepieceisreal": "бірбөлшекнақты", "revengers": "报复者", "mobpsycho": "мобпсихо", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyәсері", "digimonstory": "дидимонтарихы", "digimontamers": "цифрлықмонстрлар", "superjail": "суперкүнәханасы", "metalocalypse": "металокалипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "ватамоте", "uramichioniisan": "урамичионисан", "uruseiyatsura": "урүсейятсура", "gintama": "гинтама", "ranma": "ранма", "doraemon": "дораэмон", "gto": "гто", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "тегісвебтун", "kemonofriends": "кемонодостар", "utanoprincesama": "утараппринцесама", "animecom": "анимекимпер", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юкиийуна", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "ннонбёри", "flyingwitch": "ұшатынсүтеміз", "wotakoi": "вотакои", "konanime": "конаниме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "простоосылай", "horimiya": "хоримия", "allsaintsstreet": "барsaintsкөшесі", "recuentosdelavida": "өмірдіңесептері"}