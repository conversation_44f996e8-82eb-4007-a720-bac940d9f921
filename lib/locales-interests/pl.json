{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologia", "cognitivefunctions": "funkcjepoznawcze", "psychology": "psychologia", "philosophy": "filozofia", "history": "historia", "physics": "<PERSON><PERSON>ka", "science": "nauka", "culture": "kultura", "languages": "<PERSON><PERSON><PERSON><PERSON>", "technology": "technologia", "memes": "memy", "mbtimemes": "mbtime<PERSON>", "astrologymemes": "astrologiam<PERSON>y", "enneagrammemes": "enneagrammemy", "showerthoughts": "myśliprysznicowe", "funny": "zabawne", "videos": "filmiki", "gadgets": "gadżety", "politics": "polityka", "relationshipadvice": "radydotyczącezwiązku", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "krypto", "news": "nowoś<PERSON>", "worldnews": "wiadomoś<PERSON><PERSON>ś<PERSON><PERSON>", "archaeology": "archeologia", "learning": "uczeniesię", "debates": "debaty", "conspiracytheories": "teoriespiskowe", "universe": "wszechświat", "meditation": "medyta<PERSON>ja", "mythology": "mitologia", "art": "sztuka", "crafts": "rzemiosło", "dance": "<PERSON>ie<PERSON>", "design": "projektowanie", "makeup": "<PERSON><PERSON><PERSON><PERSON>", "beauty": "piękno", "fashion": "moda", "singing": "śpiewanie", "writing": "pisanie", "photography": "fotografia", "cosplay": "cosplay", "painting": "ma<PERSON>anie", "drawing": "rys<PERSON><PERSON>", "books": "książki", "movies": "filmy", "poetry": "poezja", "television": "telewizja", "filmmaking": "produkcjafilmu", "animation": "animacja", "anime": "anime", "scifi": "fantastykanaukowa", "fantasy": "fantastyka", "documentaries": "filmydokumentalne", "mystery": "<PERSON><PERSON><PERSON><PERSON>", "comedy": "komedie", "crime": "kry<PERSON>ł", "drama": "dramat", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romans", "realitytv": "programrozrywkowy", "action": "a<PERSON><PERSON><PERSON>", "music": "muzyka", "blues": "blues", "classical": "klasyczna", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektroniczna", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "dom", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latynoska", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "podróże", "concerts": "koncerty", "festivals": "festiwale", "museums": "muzea", "standup": "standup", "theater": "kino", "outdoors": "zajęcianazewnątrz", "gardening": "pracaw<PERSON><PERSON><PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "graniewgry", "boardgames": "gryplanszowe", "dungeonsanddragons": "dungeonsidragons", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "j<PERSON><PERSON><PERSON>", "baking": "pieczenie", "cooking": "got<PERSON>nie", "vegetarian": "<PERSON>get<PERSON>ński", "vegan": "<PERSON><PERSON><PERSON>", "birds": "ptaki", "cats": "koty", "dogs": "psy", "fish": "ryba", "animals": "zwierzęta", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ekologia", "feminism": "feminizm", "humanrights": "prawaczłowieka", "lgbtqally": "lgbt", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "wolontariat", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "koszykówka", "boxing": "boks", "cricket": "krykiet", "cycling": "jazdanarowerze", "fitness": "fitness", "football": "piłkanożna", "golf": "golf", "gym": "siłownia", "gymnastics": "gimnast<PERSON><PERSON>", "hockey": "hokej", "martialarts": "sztukiwalki", "netball": "piłkasiatkowa", "pilates": "pilates", "pingpong": "pingpong", "running": "bi<PERSON>ie", "skateboarding": "jazdanadeskorolce", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snowboard", "surfing": "serfowanie", "swimming": "pływanie", "tennis": "tenis", "volleyball": "siatkówka", "weightlifting": "podnoszenieciężarów", "yoga": "joga", "scubadiving": "nurkowanie", "hiking": "wędrówki", "capricorn": "koziorożec", "aquarius": "wodnik", "pisces": "ryby", "aries": "baran", "taurus": "byk", "gemini": "b<PERSON><PERSON><PERSON>ę<PERSON>", "cancer": "rak", "leo": "lew", "virgo": "panna", "libra": "waga", "scorpio": "skorpion", "sagittarius": "strzelec", "shortterm": "krótkiterminowo", "casual": "luźnyzwiązek", "longtermrelationship": "związeknadłuższy", "single": "singiel", "polyamory": "poliamoria", "enm": "etycznapoliamoria", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gej", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "biseksualny", "pansexual": "panseksualny", "asexual": "<PERSON>ek<PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "smokizysku", "assassinscreed": "assassinscreed", "saintsrow": "świę<PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "strażnicy", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "krolewskazadanie", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subwersja", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "sunsetoverdrive": "zachódsłońcaoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "ogniemble<PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guildwars": "gil<PERSON><PERSON><PERSON>n", "openworld": "otwartyświat", "heroesofthestorm": "bohaterowburzy", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "plemionamid<PERSON><PERSON>", "planescape": "planepasja", "lordsoftherealm2": "lordzysfery2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "koloroweństwo", "medabots": "medabots", "lodsoftherealm2": "lodzimarzenia2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersywn<PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "gryfabularne", "witcher": "w<PERSON><PERSON><PERSON>", "dishonored": "zhańbiony", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotory", "wynncraft": "wynn<PERSON>", "witcher3": "wiedzmin3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modding": "modding", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "zanurzone", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidalnamotywa<PERSON>ja", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "zakochanywmiłości", "otomegames": "otomegry", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "wampiryprzebranie", "dimension20": "wymiar20", "gaslands": "gaslands", "pathfinder": "ścieżkowiec", "pathfinder2ndedition": "szlakowcy2edycja", "shadowrun": "shadowrun", "bloodontheclocktower": "krwawnanowaczasie", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "grawitacyjnyzrywnap", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "w<PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "ręka", "honkai": "honkai", "marauders": "mara<PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "schronienieapokaliptyczne", "gurps": "gurps", "darkestdungeon": "najciemniejszyzlozki", "eclipsephase": "fazaec<PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "zewnętrzneświaty", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "wojo<PERSON>icydyna<PERSON>ii", "skullgirls": "czaszkidziewczyny", "nightcity": "nocnemiasto", "hogwartslegacy": "dziedzictwohogwartów", "madnesscombat": "szaleństwopolowania", "jaggedalliance2": "postrzepionedojazdy2", "neverwinter": "nigdywinter", "road96": "droga96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelik<PERSON>", "gothamknights": "zwiastunrycer<PERSON>", "forgottenrealms": "zapomnianeświaty", "dragonlance": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "dzieckoswiatła", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonświat", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "wulkanowyświat", "fracturedthrones": "pękniętetronów", "horizonforbiddenwest": "ho<PERSON>zontzakazan<PERSON>ód", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltazielony", "diablo": "<PERSON><PERSON><PERSON>", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON>", "lastepoch": "ostatniaepoka", "starfinder": "odkrywcaswiatów", "goldensun": "złotysłońce", "divinityoriginalsin": "boskizmarnotrawstwo", "bladesinthedark": "ostrza_w_cien<PERSON>h", "twilight2000": "zmierzch2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkczerwony", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "upadłezamówienie", "finalfantasyxii": "finalfantasy12", "evillands": "złeziemie", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diabłosurwajec", "oldschoolrunescape": "stareklasyczneosrs", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "stareświatowebluesy", "adventurequest": "wyprawa_przygoda", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "gryfabularne", "roleplayinggames": "gryfabularne", "finalfantasy9": "finalfantasy9", "sunhaven": "słonecznazednia", "talesofsymphonia": "opowieściodsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "zniszczonemiasto", "myfarog": "moja<PERSON><PERSON>", "sacredunderworld": "sacrednyświat", "chainedechoes": "łańcuchoweecho", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslike", "othercide": "innycyd", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "szcz<PERSON>y", "tibia": "tibia", "thedivision": "dywizja", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendyodragonów", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "wampirowamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "wilkolakizapokalipsa", "aveyond": "aveyond", "littlewood": "małeszewy", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "sercesi<PERSON><PERSON>", "fable3": "bajka3", "fablethelostchapter": "fablezagubionyrozdział", "hiveswap": "hiveswap", "rollenspiel": "gryfabularne", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenwieczny", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "gwiezdnepole", "oldschoolrevival": "odkrywanieklasyki", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "królestwoserc1", "ff9": "ff9", "kingdomheart2": "królestwowserca2", "darknessdungeon": "ciemnepodziemie", "juegosrpg": "gryrpg", "kingdomhearts": "królestwodzieci", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "żniwna", "gloomhaven": "gloomhaven", "wildhearts": "dzikie_serca", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowhearts": "cienieserce", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "oddechwładzy4", "mother3": "matka3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "upadekbosów", "anothereden": "kolejnyeden", "roleplaygames": "gryfabularne", "roleplaygame": "grawrole", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harrypot<PERSON><PERSON>", "pathfinderrpg": "szlakiemgracza", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "wamp<PERSON><PERSON><PERSON><PERSON>zie", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "polowaniemistrzów", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "łowcymonstrówświat", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "covenantcieniowejserca", "bladesoul": "<PERSON><PERSON>ao<PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "królestwospełnienia", "awplanet": "awplanet", "theworldendswithyou": "światkończyztoyb1", "dragalialost": "dragalialost", "elderscroll": "starszarolka", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytaktyka", "grandia": "grandia", "darkheresy": "ciemnapraktyka", "shoptitans": "sklepowetitanowe", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ziemskamagia", "blackbook": "czarnaksiążka", "skychildrenoflight": "niebardziedzieciategowyn<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "sacredgoldedition", "castlecrashers": "zderzeniewzamku", "gothicgame": "<PERSON><PERSON><PERSON><PERSON>ry", "scarletnexus": "czerwonynamalowanyświat", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gryrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "miastomgły", "indierpg": "indierpg", "pointandclick": "kliknijizobacz", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "niezłomny", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7nazawsze", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "śmierćdrogadokanady", "palladium": "palladium", "knightjdr": "rycerzjdr", "monsterhunter": "łowcamonstrów", "fireemblem": "ogieńemblemat", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacja", "persona5": "persona5", "ghostoftsushima": "duchtsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "polowanieznamonstra", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "poż<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "animegry", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "żernik", "diluc": "diluc", "venti": "venti", "eternalsonata": "wiecznasonata", "princessconnect": "księżniczkowełączenie", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "kryształy", "vcs": "vcs", "pes": "psy", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorant", "valorantindian": "valorantindie", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaśniących", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "cybersport", "crazyraccoon": "szalonybandyta", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantzawodowo", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "połówżycia", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "zawór", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "wie<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "symulatorzamruczenia", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "wolneplanetki", "transformice": "transformice", "justshapesandbeats": "justkształtyizbijaki", "battlefield4": "polebitwy4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "ryzykodeszczu2", "metroidvanias": "metroidvanie", "overcooked": "prz<PERSON>palone", "interplanetary": "międzyplanetarny", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "martwekomórki", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "forteczkadwarfów", "foxhole": "l<PERSON><PERSON><PERSON>", "stray": "zbłąkany", "battlefield": "pola_walki", "battlefield1": "polabitwy1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oczka", "blackdesert": "czarnapustynia", "tabletopsimulator": "symulatorstolików", "partyhard": "bawsięmocno", "hardspaceshipbreaker": "trudnyzłomiarzstatków", "hades": "hades", "gunsmith": "kowa<PERSON>broni", "okami": "<PERSON>ami", "trappedwithjester": "uwięzionyzjesterem", "dinkum": "dinkum", "predecessor": "poprzednik", "rainworld": "deszczowyswiat", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonijnas<PERSON>ula<PERSON>ja", "noita": "noita", "dawnofwar": "świtwojny", "minionmasters": "minion<PERSON><PERSON>", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "ciemnoijasniej", "motox": "motox", "blackmesa": "czarnamesa", "soulworker": "soulworker", "datingsims": "symulacjedate", "yaga": "yaga", "cubeescape": "odkryjkostkę", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "now<PERSON>ist<PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconci<PERSON><PERSON><PERSON>", "kenopsia": "kenopsja", "virtualkenopsia": "wirtualnekkenopsja", "snowrunner": "śnieżnybiegacz", "libraryofruina": "bibliotekaruiny", "l4d2": "l4d2", "thenonarygames": "ne<PERSON><PERSON><PERSON>", "omegastrikers": "omegastrażnicy", "wayfinder": "znajdźdrogę", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "spokojnaplastikowakaczuszka", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "uśmiechnijsiędlamnie", "catnight": "kocurnoc", "supermeatboy": "supermięsnychłopak", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "przytulnygaj", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "tęczowe6", "apexlegends": "apexlegends", "cod": "codzio", "borderlands": "graniczneziemie", "pubg": "pubg", "callofdutyzombies": "callofdutyzombie", "apex": "apex", "r6siege": "r6oblężenie", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "gryfar<PERSON><PERSON>", "paladins": "pala<PERSON>ni", "earthdefenseforce": "obronaziemskiej", "huntshowdown": "polowaniezshowdown", "ghostrecon": "duchoweoswobodzenie", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "dołóżdoekipy", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "powstanieburzapiaskowa", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "zabójca3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwojna", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "krawęd<PERSON><PERSON><PERSON>", "divisions2": "divisions2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "zimowezombiaki", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "asownewalki", "crosscode": "krzywokod", "goldeneye007": "złotyoko007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "nowoczesnaprowa", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwojownik", "boarderlands": "granice", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "uciekajnatarkowie", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "światodestatków", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6obrona", "xcom": "xcom", "hitman": "zabójca", "masseffect": "masseffect", "systemshock": "systemwstrząs", "valkyriachronicles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "specopstheline": "spec<PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "killingfloor2", "cavestory": "historiask<PERSON>wan<PERSON>", "doometernal": "doometernal", "centuryageofashes": "wiekpopiołów", "farcry4": "farcry4", "gearsofwar": "kółkawojny", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "pokoleniezero", "enterthegungeon": "wejdźdoinneguny", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "nowewojny2", "blackops1": "czarnadruszka1", "sausageman": "kiełbaskowyfacet", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "phantom<PERSON><PERSON><PERSON>", "warface": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossfire": "krzyżowyogień", "atomicheart": "atomoweserce", "blackops3": "czarnadziałania3", "vampiresurvivors": "wampirzyprzeżywcy", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "p<PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "małapiękna", "gamepubg": "grajpubg", "necromunda": "nekromunda", "metalgearsonsoflibert": "metalgearwnukówwolności", "juegosfps": "gryfps", "convertstrike": "konwertujuderzenie", "warzone2": "strefawojny2", "shatterline": "shatterline", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "krwawybałagan", "republiccommando": "komandoserepubliki", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groundbranch": "boobranch", "squad": "ekipa", "destiny1": "destiny1", "gamingfps": "grani<PERSON><PERSON>", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubggirl", "worldoftanksblitz": "światczołgówblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "zaangażowany", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "opancerzonecentrum", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinasprzygody", "halo2": "halo2", "payday2": "pieniądze2", "cs16": "cs16", "pubgindonesia": "pubgindonez<PERSON>", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "duchkod", "csplay": "csplay", "unrealtournament": "niesamowi<PERSON><PERSON><PERSON>j", "callofdutydmz": "callofdutydmz", "gamingcodm": "graniecodm", "borderlands2": "borderlands2", "counterstrike": "kontratak", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "mistrzowietrzęsieńziemi", "halo3": "halo3", "halo": "halo", "killingfloor": "zabijamfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "rozpadkomórkowy", "neonwhite": "neonbiałe", "remnant": "reszt<PERSON>", "azurelane": "azurelane", "worldofwar": "światwojny", "gunvolt": "gunvolt", "returnal": "p<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "czerwonypadły", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "polewalki3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "morzezłodzieji", "rust": "rdza", "conqueronline": "podbijinternety", "dauntless": "nieustraszony", "warships": "okrętyruikowe", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "lotniczewzloty", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "bezpanny", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "światczołgów", "crossout": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "wieżawfantazji", "netplay": "netgra", "everquest": "wiecznaprogressja", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superzwierzakowa", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "rycerzonejonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "związanieizaka", "dragonageinquisition": "dragonageinkwizy<PERSON><PERSON>", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "bandadebili", "newworld": "nowyswiat", "blackdesertonline": "czarnapustynainteraktywnie", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "honorowykrólów", "fivem": "fivem", "starwarsbattlefront": "gwiezdnewojnybitwafront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "kuc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dczat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "światwojnyzcrafta", "warcraft": "wojnaświatów", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopety", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "pro<PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "szlakjedwabny", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "w<PERSON><PERSON><PERSON><PERSON>", "angelsonline": "aniołowieonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starewojnystararepublika", "grandfantasia": "grandfantaz<PERSON>", "blueprotocol": "niebieskiprotokół", "perfectworld": "idealnyświat", "riseonline": "wzniesienieonline", "corepunk": "corepunk", "adventurequestworlds": "przygodaquestświaty", "flyforfun": "lećzafun", "animaljam": "animaljam", "kingdomofloathing": "królestwopogardy", "cityofheroes": "miastoherosów", "mortalkombat": "mortalkombat", "streetfighter": "ulicznywojownik", "hollowknight": "pustyrycerz", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "winogradniemożliwy", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "wojownikwirtualny", "streetsofrage": "uliceraport<PERSON>", "mkdeadlyalliance": "mkśmiercion<PERSON>ś<PERSON>przym<PERSON>ze", "nomoreheroes": "koniecbohaterów", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "królemwojowników", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightinggames", "blasphemous": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "rywalowieetażu", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmasz", "mugen": "mugen", "warofthemonsters": "wojnapotworów", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberboty", "armoredwarriors": "zbrojniwojownicy", "finalfight": "ostatecznawalka", "poweredgear": "mocneakcesoria", "beatemup": "<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "g<PERSON>wal<PERSON>zne", "killerinstinct": "zabójczyinstynkt", "kingoffigthers": "królwalki", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "rycerskość2", "demonssouls": "duszeklimbo", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelhollowknighta", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggra", "silksongnews": "silksongnews", "silksong": "silksong", "undernight": "podno<PERSON>ą", "typelumina": "typelum<PERSON>", "evolutiontournament": "turniejewolucji", "evomoment": "ewomomenty", "lollipopchainsaw": "lollipopdziewczynka", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "opowieściberserii", "bloodborne": "krwawienie", "horizon": "ho<PERSON>zont", "pathofexile": "ścieżkawygnania", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "nieodkryte", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "ostatniznas", "infamous": "słynny", "playstationbuddies": "przyjacieleplaystation", "ps1": "ps1", "oddworld": "dziwnyświat", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "b<PERSON>żewojny", "gris": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trove": "<PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitstajeszludzki", "beatsaber": "beatsbybeatsbybeatsaber", "rimworld": "światrimów", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "nagrodaturystyczna", "lspdfr": "lspdfr", "shadowofthecolossus": "cieńkolosa", "crashteamracing": "crashteamracing", "fivepd": "pięćpd", "tekken7": "tekken7", "devilmaycry": "diabłomniepłacz", "devilmaycry3": "diablomogępłakać3", "devilmaycry5": "devilmogąpłakać5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samurajskibojevnicy", "psvr2": "psvr2", "thelastguardian": "ostatnistrzeżca", "soulblade": "dusznasblade", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "polowanienafacetów", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2przymierze", "pcsx2": "pcsx2", "lastguardian": "ostatnibezpiecznik", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "zwierzakimprezowy", "warharmmer40k": "warhammer40k", "fightnightchampion": "nocwalkiboo", "psychonauts": "psychonautzi", "mhw": "mhw", "princeofpersia": "książępersji", "theelderscrollsskyrim": "<PERSON><PERSON>z<PERSON><PERSON><PERSON>cyskyrima", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "starożytnezkrolestwa", "gxbox": "gxbox", "battlefront": "frontbitwy", "dontstarvetogether": "nieglodzijeszczerazem", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "gwiezdnykierunek", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxy", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "leagueofkingdoms", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertalepolska", "trashtv": "śmieciowetv", "skycotl": "skycotl", "erica": "erica", "ancestory": "ancestory", "cuphead": "cuphead", "littlemisfortune": "małezłyklopoty", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstroznybal", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motozajawka", "outerwilds": "dzikiekosmosy", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kulturnaprzeciwkołękom", "duckgame": "kaczkowagry", "thestanleyparable": "stanleyporadnik", "towerunite": "towerunite", "occulto": "okultyzm", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "satysfakcjonująco", "pluviophile": "pluwiofil", "underearth": "podziemnie", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "duchowyzwycieżca", "darkdome": "ciemnakopuła", "pizzatower": "pizzawieżowa", "indiegame": "indiegram", "itchio": "itchio", "golfit": "golfit", "truthordare": "prawdaalbowyzwanie", "game": "gra", "rockpaperscissors": "kamieńpapiernożyce", "trampoline": "trampolina", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "wyzwanie", "scavengerhunt": "polo<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "grywpodwórku", "pickanumber": "wybierzliczb<PERSON>", "trueorfalse": "prawdaczyfałsz", "beerpong": "piw<PERSON>pong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "przyjemnegry", "datinggames": "g<PERSON><PERSON><PERSON><PERSON>", "freegame": "darmowagierka", "drinkinggames": "gry<PERSON><PERSON><PERSON>", "sodoku": "sudoku", "juegos": "gry", "mahjong": "mahjong", "jeux": "gry", "simulationgames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "g<PERSON><PERSON>razowe", "jeuxdemots": "zabawyzesłowami", "juegosdepalabras": "igrzysłow", "letsplayagame": "zagrajmywgrę", "boredgames": "nudnegryplanszowe", "oyun": "gra", "interactivegames": "gryinteraktywne", "amtgard": "amtgard", "staringcontests": "konkursypatrzenia", "spiele": "granie", "giochi": "gry", "geoguessr": "geoguessr", "iphonegames": "gryiphone", "boogames": "boogry", "cranegame": "grazłotówka", "hideandseek": "bawsięwchowanego", "hopscotch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "gryarkadowe", "yakuzagames": "grywyakuzie", "classicgame": "klasycznagry", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "zgadnijtekst", "galagames": "galagames", "romancegame": "graromantyczna", "yanderegames": "yander<PERSON>ry", "tonguetwisters": "kąsaniejęzyków", "4xgames": "4xgry", "gamefi": "gamefi", "jeuxdarcades": "gry<PERSON>o", "tabletopgames": "gryplanszowe", "metroidvania": "metroidvania", "games90": "gry90", "idareyou": "wyskakujezwyzwania", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "grywyścigowe", "ets2": "ets2", "realvsfake": "prawdziwevsfałszywe", "playgames": "g<PERSON><PERSON><PERSON><PERSON>", "gameonline": "graonline", "onlinegames": "gier<PERSON>ee<PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "pisanieroleplay", "playaballgame": "g<PERSON><PERSON><PERSON><PERSON>", "pictionary": "kalambury", "coopgames": "grykooperacyjne", "jenga": "jenga", "wiigames": "wiigames", "highscore": "wys<PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "gryfabularne", "burgergames": "gryburgersowe", "kidsgames": "gierkidów", "skeeball": "darta", "nfsmwblackedition": "nfsmwedycjaczarny", "jeuconcour": "zabawakonkursowa", "tcgplayer": "tcgplayer", "juegodepreguntas": "grazapytania", "gioco": "gra", "managementgame": "grazarządzająca", "hiddenobjectgame": "grazukrytymiobiektami", "roolipelit": "rolkipelity", "formula1game": "graformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "g<PERSON><PERSON><PERSON>", "memorygames": "grywspomnienia", "vulkan": "wulkan", "actiongames": "g<PERSON><PERSON><PERSON><PERSON>", "blowgames": "blowgames", "pinballmachines": "maszynypinballowe", "oldgames": "<PERSON><PERSON>ry", "couchcoop": "kanapowezrzuty", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "graj<PERSON>", "lasergame": "lasergame", "imessagegames": "gryimessage", "idlegames": "g<PERSON><PERSON>le", "fillintheblank": "uzupełnijpustkę", "jeuxpc": "gryzpc", "rétrogaming": "retrogranie", "logicgames": "grylogiczne", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurfing", "jeuxdecelebrite": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "wyjściowegry", "5vs5": "5na5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "grajiubij", "traditionalgames": "tradycyjnegry", "kniffel": "kniffel", "gamefps": "gryfps", "textbasedgames": "grytekstowe", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalciopl", "retrospel": "retrospelen", "thiefgame": "zlodziejskagry", "lawngames": "grasowegry", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "p<PERSON>ł<PERSON><PERSON><PERSON>", "tischfußball": "p<PERSON>ł<PERSON><PERSON><PERSON>", "spieleabende": "spoilki", "jeuxforum": "forumgier", "casualgames": "gry<PERSON><PERSON><PERSON><PERSON>", "fléchettes": "r<PERSON><PERSON><PERSON>", "escapegames": "grywyjścia", "thiefgameseries": "złodziejskagryserie", "cranegames": "gryżurawiów", "játék": "zabawa", "bordfodbold": "bordfodbold", "jogosorte": "gierka", "mage": "<PERSON><PERSON><PERSON><PERSON>", "cargames": "grymotoryzacyjne", "onlineplay": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "nocygier", "pursebingos": "torbowebingo", "randomizer": "<PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagramy", "gamespc": "gierkipc", "socialdeductiongames": "gryzdecyzjespołeczne", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrycznegry", "goodoldgames": "dobregryzoldy", "truthanddare": "praw<PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "polowanie_na_skrytkę", "jeuxvirtuel": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "graczf2p", "free2play": "free2play", "fantasygame": "grafantasyczna", "gryonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "driftgame": "driftgram", "gamesotomes": "gryztemomentami", "halotvseriesandgames": "haloserialeigry", "mushroomoasis": "grzybowaoaza", "anythingwithanengine": "wszystkoznaszymsilnikiem", "everywheregame": "grawszędzie", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "dobrazabawaoddawania", "jugamos": "gramy", "lab8games": "lab8gry", "labzerogames": "labzerogames", "grykomputerowe": "grykomputerowe", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "samospełnieniegranie", "gamemodding": "modowaniegier", "crimegames": "gryprzestę<PERSON>cze", "dobbelspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "gry", "spacenerf": "spacenerf", "charades": "kalambury", "singleplayer": "singleplayer", "coopgame": "gręwspółpracy", "gamed": "zagrany", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "głównagry", "kingdiscord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrabble": "scribble", "schach": "<PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "bule", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON>ctwopandemii", "camelup": "<PERSON><PERSON><PERSON>", "monopolygame": "monopolygra", "brettspiele": "gryplanszowe", "bordspellen": "gryplanszowe", "boardgame": "graplanszowa", "sällskapspel": "graizabawy", "planszowe": "planszowe", "risiko": "risiko", "permainanpapan": "gryplanszowe", "zombicide": "zombicide", "tabletop": "stolikowy", "baduk": "baduk", "bloodbowl": "krwawemisyjki", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goplanagram", "connectfour": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "heroquest", "giochidatavolo": "stołygraczowe", "farkle": "farkle", "carrom": "karambol", "tablegames": "grystołowe", "dicegames": "grydices", "yatzy": "jat<PERSON>", "parchis": "parchis", "jogodetabuleiro": "jogipodłogowe", "jocuridesocietate": "zabawydlatoż<PERSON>", "deskgames": "grydeskowe", "alpharius": "alpharius", "masaoyunları": "masowegry", "marvelcrisisprotocol": "marvelowskiekryzysmistrzostwo", "cosmicencounter": "kosmicznespotkanie", "creationludique": "tw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "grazykartonowe", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>nykoszmar", "switchboardgames": "switchboardgier", "infinitythegame": "nieskończonośćgrę", "kingdomdeath": "k<PERSON><PERSON><PERSON>skasmier<PERSON>", "yahtzee": "yahtzee", "chutesandladders": "schodyizjezdzalnie", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "grapeczingos", "planszówki": "planszów<PERSON>", "rednecklife": "życiewieśniaka", "boardom": "znudzenie", "applestoapples": "jabłkaodjabłek", "jeudesociété": "jeudespołeczne", "gameboard": "planszówka", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "gramywręczówki", "twilightimperium": "zmierzchimperium", "horseopoly": "koniopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "mansjonyszaleństwa", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "cieniebrimstone", "kingoftokyo": "królewskitokio", "warcaby": "warcaby", "táblajátékok": "gryplanszowe", "battleship": "łajba", "tickettoride": "biletdojazdy", "deskovehry": "biurkowegry", "catán": "katana", "subbuteo": "subbuteo", "jeuxdeplateau": "gryplanszowe", "stolníhry": "stolníhry", "xiángqi": "<PERSON>ian<PERSON><PERSON>", "jeuxsociete": "grydl<PERSON><PERSON><PERSON>", "gesellschaftsspiele": "gryplanszowe", "starwarslegion": "legiongwiezdnychwojen", "gochess": "<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "gryplanszowe", "terraria": "terraria", "dsmp": "dsmp", "warzone": "strefawojny", "arksurvivalevolved": "arkprzeżycieewolucji", "dayz": "dni", "identityv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theisle": "wyspa", "thelastofus": "ostatniznas", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyziatka", "conanexiles": "conanexiles", "eft": "eft", "amongus": "wśr<PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "małpiwyspa", "valheim": "valheim", "planetcrafter": "planetowytwórca", "daysgone": "dniogone", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologiczny", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "długaodchłań", "ark": "ark", "grounded": "stąpamzziemi", "stateofdecay2": "stanopadnięcia2", "vrising": "vrising", "madfather": "wkurzonydad", "dontstarve": "niegł<PERSON><PERSON>", "eternalreturn": "wiecznypowrót", "pathoftitans": "ścieżkatytanów", "frictionalgames": "frictionalgames", "hexen": "czarownice", "theevilwithin": "złewewnątrz", "realrac": "prawdziwirac", "thebackrooms": "backrooms", "backrooms": "backroomy", "empiressmp": "empiresmp", "blockstory": "blokowahistorie", "thequarry": "kamieniołom", "tlou": "tlou", "dyinglight": "umarlświateł<PERSON>", "thewalkingdeadgame": "grategozmartwychchodu", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "wzrostimperiów", "stateofsurvivalgame": "stanprzetrwania", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "oddech", "alisa": "alisa", "westlendsurvival": "przetrwaniewestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "ciem<PERSON>las", "survivalhorror": "survivalhor<PERSON>r", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "pustykolej", "lifeaftergame": "życiepogeimie", "survivalgames": "gryprzeżycia", "sillenthill": "cichagóra", "thiswarofmine": "tawnaszejwojnie", "scpfoundation": "fundacjascp", "greenproject": "zielonyprojekt", "kuon": "kuon", "cryoffear": "płaczzprzerażenia", "raft": "tratwa", "rdo": "rdo", "greenhell": "zielonypiekło", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "baba", "littlenightmares2": "małekoszmary2", "signalis": "sygnalis", "amandatheadventurer": "amandawgłowieprzygodnicy", "sonsoftheforest": "synowieforestu", "rustvideogame": "rustgierwideo", "outlasttrials": "przetrwaćwyzwania", "alienisolation": "<PERSON><PERSON>lacja<PERSON><PERSON><PERSON>", "undawn": "niedosłoneczny", "7day2die": "7dni<PERSON><PERSON><PERSON><PERSON><PERSON>", "sunlesssea": "bezsłonecznymorze", "sopravvivenza": "przetrwanie", "propnight": "propnoc", "deadisland2": "martwawysp2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenwampir", "deathverse": "śmierciwersum", "cataclysmdarkdays": "kataklizmciemnedni", "soma": "soma", "fearandhunger": "strachigłód", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "zyciepotem", "ageofdarkness": "epo<PERSON><PERSON>rok<PERSON>", "clocktower3": "zegarowawieza3", "aloneinthedark": "samotnywciemności", "medievaldynasty": "średniowiecznadygninacja", "projectnimbusgame": "projektchmurki", "eternights": "wieczności", "craftopia": "craftopia", "theoutlasttrials": "outlasttrials", "bunker": "bunkier", "worlddomination": "globalneopanowanie", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "biurozabójców", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "zabójcakrasnoludów", "warhammer40kcrush": "zawroty40k", "wh40": "wh40", "warhammer40klove": "miłośćdowarhammera40k", "warhammer40klore": "warhammer40klore", "warhammer": "wojnamłotków", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ilovesororitas", "ilovevindicare": "kochamvindicare", "iloveassasinorum": "kochamasasynów", "templovenenum": "templovenenum", "templocallidus": "templokozak", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "zawodzabójców", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires": "epokaimperiów", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "wieżawojnywiekufinał", "civilizationv": "cywilizacjav", "ittakestwo": "ittakestwo", "wingspan": "roz<PERSON>ę<PERSON>śćskrzydeł", "terraformingmars": "terraformowaniemar<PERSON>", "heroesofmightandmagic": "bohaterowiemo<PERSON>ima<PERSON>i", "btd6": "btd6", "supremecommander": "supremekomandor", "ageofmythology": "epokamitycznych", "args": "a<PERSON><PERSON>", "rime": "<PERSON><PERSON><PERSON><PERSON>", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "wygnany", "caesar3": "césar3", "redalert": "czerwonyalarm", "civilization6": "cywilizacja6", "warcraft2": "warcraft2", "commandandconquer": "rozkazujizwyci<PERSON>żaj", "warcraft3": "warcraft3", "eternalwar": "wiecznawojna", "strategygames": "grystrategiczne", "anno2070": "anno2070", "civilizationgame": "gracypodcywilizację", "civilization4": "cywilizacja4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "<PERSON>rz<PERSON>ś<PERSON><PERSON><PERSON>", "totalwar": "totalwar", "travian": "travian", "forts": "forty", "goodcompany": "dobratowarzystwo", "civ": "civ", "homeworld": "światdomowy", "heidentum": "pogaństwo", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "szybszyniżświatło", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "strategiazczasurzeczywistego", "starctaft": "starctaft", "sidmeierscivilization": "cywi<PERSON><PERSON><PERSON><PERSON>", "kingdomtwocrowns": "królestwodwóchkoron", "eu4": "eu4", "vainglory": "przechwałki", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "zajęciaalgebrydavea", "plagueinc": "plagainc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "cywilizacja3", "4inarow": "4<PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "królowiekrucjaty3", "heroes3": "bohaterzy3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON>ewojn<PERSON>", "ageofempires2": "wiekimperiów2", "disciples2": "uczniowie2", "plantsvszombies": "roslinyvszybyzombie", "giochidistrategia": "grystrategiczne", "stratejioyunları": "stratgames", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "epokawyzwań", "dinosaurking": "dinozaurowykról", "worldconquest": "podbójświata", "heartsofiron4": "sercażelaza4", "companyofheroes": "towarzystwobohaterów", "battleforwesnoth": "bitwaoprowincjęwesnoth", "aoe3": "aoe3", "forgeofempires": "kuźniacycywilizacji", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gęśgęsducka", "phobies": "fobie", "phobiesgame": "grazph<PERSON><PERSON>", "gamingclashroyale": "grykonfrontacjar<PERSON>le", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "zewnętrznejpłaszczyzny", "turnbased": "kolejkovany", "bomberman": "bomberman", "ageofempires4": "epochyimperiów4", "civilization5": "cywilizacja5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON>pra<PERSON>", "cultris2": "cultris2", "spellcraft": "czarodziejskitalent", "starwarsempireatwar": "wojnystarwarswychimperiów", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategia", "popfulmail": "popfulmail", "shiningforce": "błyszczącaenergia", "masterduel": "mistrzowskapojedynek", "dysonsphereprogram": "dysonspherprogram", "transporttycoon": "tycoontransportowy", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wilczawioska", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "królewstwoupland", "galaxylife": "zyciewgalaktyce", "wolvesvilleonline": "wilczemiasteczkoonline", "slaythespire": "zabijajwierzchołek", "battlecats": "battlecats", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potrzebawysokiejprędkości", "needforspeedcarbon": "potrzebujeszybkoscicarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "losims4", "fnaf": "fnaf", "outlast": "przetrwaj", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "powrotnadozpieńalice", "darkhorseanthology": "darkhorseantologia", "phasmophobia": "fobiatechnosfery", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "saiko", "fatalframe": "śmiertelnaklatka", "littlenightmares": "małekoszmary", "deadrising": "martwapowstanie", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "wdomu", "deadisland": "mart<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "małabieda", "projectzero": "projektzero", "horory": "horrorow", "jogosterror": "jogosterror", "helloneighbor": "czescsasiad", "helloneighbor2": "cześćsąsiad2", "gamingdbd": "gamingdbd", "thecatlady": "kocicciara", "jeuxhorreur": "gry<PERSON><PERSON>", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartyprzeciwkoludzkości", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenames", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON>", "solitaire": "soliterka", "poker": "poker", "hearthstone": "sercekamienia", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kluczowepieczęcie", "cardtricks": "sztuczkikartkowe", "playingcards": "karty返点", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "kart<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "słowaicztcg", "sportscards": "kartysportowe", "cardfightvanguard": "walkarcievanguarda", "duellinks": "duellinks", "spades": "kierki", "warcry": "hasztagwojenny", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "królewkserc", "truco": "truko", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "opór", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kartyyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "duelwyugio<PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldysk", "yugiohgame": "yugiohgra", "darkmagician": "ciemnymagik", "blueeyeswhitedragon": "niebieskiewczykibiałysmok", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkomander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "sędziamtg", "juegosdecartas": "grykarciane", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "graw<PERSON><PERSON>", "carteado": "karte<PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "zoliqki", "facecard": "twarzykówka", "cardfight": "walkakart", "biriba": "biriba", "deckbuilders": "budowniczowiedecków", "marvelchampions": "marvelowimistrzowie", "magiccartas": "magicznekarte", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "cieniowewersje", "skipbo": "skip<PERSON>", "unstableunicorns": "niestabilnejednorożce", "cyberse": "cyberse", "classicarcadegames": "klasycznegierarkadowe", "osu": "osu", "gitadora": "gitadora", "dancegames": "tańczogry", "fridaynightfunkin": "fridaynightfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "tancedance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockujmartwych", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tańczcentralnie", "rhythmgamer": "rytmowygamer", "stepmania": "stepmania", "highscorerythmgames": "wysokiepunktacjegryrytmiczne", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rytmowezniebo", "hypmic": "hypmic", "adanceoffireandice": "taniecogni<PERSON>", "auditiononline": "castingonline", "itgmania": "<PERSON><PERSON>", "juegosderitmo": "gierzmu<PERSON>ką", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "rytmodoktor", "cubing": "układaniekostek", "wordle": "wordle", "teniz": "<PERSON>ń<PERSON>", "puzzlegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "zagadkinalogikę", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "łamańceumysłowe", "rubikscube": "kostkarubika", "crossword": "krzyżówka", "motscroisés": "krzyżówki", "krzyżówki": "krzyżówki", "nonogram": "nonogramy", "bookworm": "mólksiążkowy", "jigsawpuzzles": "układanki", "indovinello": "zagadka", "riddle": "zagadka", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "łamańce", "tekateki": "tekateki", "inside": "wśrodku", "angrybirds": "<PERSON><PERSON><PERSON>", "escapesimulator": "sym<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "minesweeper", "puzzleanddragons": "układankizsmokami", "crosswordpuzzles": "krzyżówki", "kurushi": "k<PERSON>hi", "gardenscapesgame": "ogródmar<PERSON><PERSON><PERSON>", "puzzlesport": "puzzlestar", "escaperoomgames": "grywescape", "escapegame": "grawzycieństwa", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgra", "wordsearch": "krzyżówka", "enigmistica": "zagadkowa", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "riddletales", "fishdom": "rybkowyzamki", "theimpossiblequiz": "niemożliwytest", "candycrush": "cukierkowewybuchy", "littlebigplanet": "mały<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "puzzle3dopasowanie", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kostkazarubika", "cuborubik": "kub<PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "zasa<PERSON><PERSON><PERSON>", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "zagadkamnie", "tycoongames": "grytycoon", "cubosderubik": "kostkizarubika", "cruciverba": "krzyżówki", "ciphers": "s<PERSON><PERSON><PERSON>", "rätselwörter": "zagwozdki", "buscaminas": "buszkaminas", "puzzlesolving": "rozwiązywaniełamigłówek", "turnipboy": "rzeżuchachłopak", "adivinanzashot": "zgadnijzrzutu", "nobodies": "<PERSON><PERSON><PERSON>", "guessing": "zgadywanie", "nonograms": "nonogramy", "kostkirubika": "kostkirubika", "crypticcrosswords": "krzyżówkiztajemnicą", "syberia2": "syberia2", "puzzlehunt": "polowaniespuzzli", "puzzlehunts": "polowanienapuzzle", "catcrime": "kotekczyniaczynów", "quebracabeça": "złamgłowę", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "ostatnizacampfire", "autodefinidos": "autodefiniujący", "picopark": "picopark", "wandersong": "wandersong", "carto": "carto", "untitledgoosegame": "niety<PERSON><PERSON><PERSON><PERSON><PERSON>ła", "cassetête": "kasztanowa", "limbo": "limbo", "rubiks": "rub<PERSON><PERSON>", "maze": "<PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "kostkarubika", "speedcube": "speedcube", "pieces": "kawałki", "portalgame": "portalgram", "bilmece": "bilmeca", "puzzelen": "<PERSON><PERSON><PERSON>", "picross": "pikros", "rubixcube": "kostkarubika", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagiczny", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "kręconyświat", "monopoly": "monopol", "futurefight": "p<PERSON><PERSON>zławalka", "mobilelegends": "mobilnelegendy", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "samotnywilk", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "gwiazdynaszpicy", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "ciasteczkowekrólestwo", "alchemystars": "alchemystars", "stateofsurvival": "stanprzeżycia", "mycity": "moje<PERSON><PERSON>", "arknights": "arknighty", "colorfulstage": "k<PERSON><PERSON><PERSON><PERSON>na", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "losgrandzamówienia", "hyperfront": "hiperfront", "knightrun": "knightbieg", "fireemblemheroes": "zepchnijbohaterówogniem", "honkaiimpact": "honkaiimpact", "soccerbattle": "bitwafutbolowa", "a3": "a3", "phonegames": "grywfonie", "kingschoice": "krolewskiewybory", "guardiantales": "opowieścig<PERSON>", "petrolhead": "motomaniak", "tacticool": "taktikool", "cookierun": "biegcookie", "pixeldungeon": "pixeldungeon", "arcaea": "<PERSON>zea", "outoftheloop": "pozaobiegiem", "craftsman": "rzemieślnik", "supersus": "supersus", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "łóżkowewojny", "freefire": "freefire", "mobilegaming": "mobilnegaming", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "strategiezespołowe", "clashofclans": "starcieklanów", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "centrumawaryjne", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON>and<PERSON>dget", "ml": "ml", "bangdream": "bangdream", "clashofclan": "klątwaklanów", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON>", "timeprincess": "czasowaksiężniczka", "beatstar": "beatstar", "dragonmanialegend": "legend<PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "kieszonkowamilosc", "androidgames": "gryandroid", "criminalcase": "sprawak<PERSON>inal<PERSON>", "summonerswar": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "gotowaniefuria", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviakrack", "leagueofangels": "ligaaniołów", "lordsmobile": "lordsmobile", "tinybirdgarden": "małaptańskagarden", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mojeśpiewającepotwory", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "wojenneroboty", "mirrorverse": "mirrorwersja", "pou": "pou", "warwings": "skrz<PERSON>laświata", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "ewertale", "futime": "futime", "antiyoy": "<PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobilne", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "walczjakiem", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "zwierzę<PERSON><PERSON><PERSON>", "gameofsultans": "gra<PERSON><PERSON><PERSON>", "arenabreakout": "arenawypuszczenie", "wolfy": "wolfy", "runcitygame": "runcitygame", "juegodemovil": "grazmobilna", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON>", "blackdesertmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>iam<PERSON><PERSON><PERSON>", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "wielkapogoni", "bombmebrasil": "bombmienabrazylia", "ldoe": "ldoe", "legendonline": "legendyonline", "otomegame": "otomegra", "mindustry": "mindustry", "callofdragons": "wezwaniedragons", "shiningnikki": "świecącanik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtodonikąd", "sealm": "zwiń", "shadowfight3": "cieniowalka3", "limbuscompany": "limbuscompany", "demolitionderby3": "derbymanic3", "wordswithfriends2": "słowakiemizaprzyjaciółmi2", "soulknight": "dusznarycerz", "purrfecttale": "purrfectaneczka", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobilnie", "harvesttown": "zbierzmiastecz<PERSON>", "perfectworldmobile": "perfektnyswiatmobilny", "empiresandpuzzles": "imperiaiklocki", "empirespuzzles": "puzzlemiastempire", "dragoncity": "smoko<PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fajny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "brud<PERSON>biegacz", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobilnie", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilnelegendybangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "ulicznikopterwalka", "lesecretdhenri": "sekrethenriego", "gamingbgmi": "gamingbgmi", "girlsfrontline": "front<PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldnażywo", "soulseeker": "poszuki<PERSON><PERSON>zy", "gettingoverit": "przechodzimyztym", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchajestory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "grymobilne", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "timeraiders", "gamingmobile": "grywmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bataliekotów", "dnd": "dnd", "quest": "misja", "giochidiruolo": "giochidrużynowe", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "światciemności", "travellerttrpg": "podróżniczettrpg", "2300ad": "2300rne", "larp": "larp", "romanceclub": "klubromansowy", "d20": "d20", "pokemongames": "pokemongry", "pokemonmysterydungeon": "pokemonowazagadkawedungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonowymstarciu", "pokemonranger": "pokemonowyrykownik", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "poke<PERSON><PERSON>e", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "c<PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>t", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "drużynarocket", "furret": "<PERSON><PERSON><PERSON><PERSON>", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonstery", "nuzlocke": "nuzlocke", "pokemonplush": "pluszakizpokemonów", "teamystic": "teamystyk", "pokeball": "pokebola", "charmander": "charmander", "pokemonromhack": "pokemonnarobota", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "błyszczą<PERSON>pokemon", "mesprit": "mesprit", "pokémoni": "pokemonki", "ironhands": "żelazneręce", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokewore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "dzieciakiipokemony", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "błyszczą<PERSON>łowca", "ajedrez": "<PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON><PERSON>", "schaken": "<PERSON><PERSON><PERSON>", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "szachowekobiety", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "świato<PERSON>blitz", "jeudéchecs": "rzucićszachy", "japanesechess": "japońskiszachy", "chinesechess": "chińskiszachy", "chesscanada": "szac<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "szachoweklimaty", "openings": "otwarcia", "rook": "kruk", "chesscom": "chesscom", "calabozosydragones": "kalabozosyidragony", "dungeonsanddragon": "podziemiaidragony", "dungeonmaster": "mistrzjednożeńców", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxprzygoda", "darksun": "ciemnesł<PERSON><PERSON>", "thelegendofvoxmachina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doungenoanddragons": "dungeonoiksmoków", "darkmoor": "ciemnybór", "minecraftchampionship": "minecraftowemistrzostwa", "minecrafthive": "minetrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modyminecraft", "mcc": "mcc", "candleflame": "płomienniczek", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "zmienionyminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftnapc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftowedungeony", "minecraftcity": "miastominecraft", "pcgamer": "graczpc", "jeuxvideo": "gry<PERSON>o", "gambit": "gambit", "gamers": "gracze", "levelup": "poziomwyżej", "gamermobile": "gamermobilny", "gameover": "koniecgry", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "g<PERSON><PERSON><PERSON>", "pcgames": "grypc", "casualgaming": "casualnegranie", "gamingsetup": "ustawieniegamingowe", "pcmasterrace": "wyścigmasterówpc", "pcgame": "grypc", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "gryvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "<PERSON><PERSON><PERSON>", "gameplays": "<PERSON>e", "consoleplayer": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "epickigrajzerzy", "onlinegaming": "gryonline", "semigamer": "półamaturgamer", "gamergirls": "gamergirls", "gamermoms": "mumskigamerki", "gamerguy": "gamerfacet", "gamewatcher": "graczobserwator", "gameur": "gracz", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerskiaduszki", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "drużynawkręcie", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "zadania", "alax": "alax", "avgn": "<PERSON><PERSON><PERSON><PERSON>", "oldgamer": "starygracz", "cozygaming": "przytulnegry", "gamelpay": "gamelpay", "juegosdepc": "grywnet", "dsswitch": "dsswitch", "competitivegaming": "g<PERSON><PERSON><PERSON><PERSON><PERSON>jne", "minecraftnewjersey": "minecraftnowyjork", "faker": "fejk", "pc4gamers": "pc4<PERSON><PERSON>y", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksualnegranie", "gamepc": "grajpc", "girlsgamer": "dziewczynygracze", "fnfmods": "fnfmodsy", "dailyquest": "codziennyquest", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "dziewczynygramerki", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "graczspolecznosciowy", "gamejam": "gamejam", "proplayer": "<PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "mójteam", "republicofgamers": "republikagamerów", "aorus": "aorus", "cougargaming": "kociczegranie", "triplelegend": "potrójnalegenda", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "potrzebujędziewczynygracza", "christiangamer": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON><PERSON>_gracz", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON>", "89squad": "89ek<PERSON><PERSON>", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "gemerzy", "oyunizlemek": "oglądaniegry", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "graczvideo", "wspólnegranie": "wspólnegranie", "mortdog": "mortdog", "playstationgamer": "graczplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "zdrowygamer", "gtracing": "gtracing", "notebookgamer": "notebookowygracz", "protogen": "protogen", "womangamer": "graczka<PERSON>", "obviouslyimagamer": "oczywiściejestemgraczem", "mario": "mario", "papermario": "pap<PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "zbieracz", "humanfallflat": "człowiekupadnijpłasko", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zerowyjście", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "muzykanintendo", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonicz<PERSON>", "fallguys": "zgułki", "switch": "przeł<PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "asadorca", "ssbm": "ssbm", "skychildrenofthelight": "niebodzieciświatła", "tomodachilife": "życieprzyja<PERSON><PERSON>ł", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "łzykrólestwa", "walkingsimulators": "symulatorycho<PERSON>zenia", "nintendogames": "gramynintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "smokowiązanie", "harvestmoon": "zbiorkalunowa", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "oddechdzikiejprzyrody", "myfriendpedro": "mójprzyjacielpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "<PERSON><PERSON><PERSON><PERSON>", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51gier", "earthbound": "ziemskie", "tales": "bajki", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategiatrojkątów", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "koniaczkiźlepracują", "nintendos": "nintendos", "new3ds": "nowe3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "wojnahyrule", "mariopartysuperstars": "mariopartysupergwiazdy", "marioandsonic": "marioizsynem", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON>zerwoneka<PERSON><PERSON>", "vanillalol": "wani<PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftpl", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendsdziki", "adcarry": "adcarry", "lolzinho": "lolzinek", "leagueoflegendsespaña": "leagueoflegendshiszpania", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadalegend", "gaminglol": "grani<PERSON><PERSON>a", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hex<PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegra", "gamingfortnite": "gramywfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrowideogry", "scaryvideogames": "s<PERSON>zneg<PERSON><PERSON>", "videogamemaker": "twórcavideogier", "megamanzero": "megamanzero", "videogame": "gry<PERSON>o", "videosgame": "gramywideo", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "przykrywanie", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "czarodziej101", "battleblocktheater": "battleblocktheater", "arcades": "automaty", "acnh": "acnh", "puffpals": "puffprzyjaciele", "farmingsimulator": "symulatorrolnictwa", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxpolska", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sandboxowegry", "videogamelore": "loregierów", "rollerdrome": "rolerodrom", "parasiteeve": "pasożytnice", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "zmierz<PERSON>lasu", "dreamscape": "dreamland", "starcitizen": "gwiezdnyobywatel", "yanderesimulator": "yanderesymulator", "grandtheftauto": "grandtheftauto", "deadspace": "martwaprzestrzeń", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "gry<PERSON>o", "theoldrepublic": "stararepublika", "videospiele": "gry<PERSON>o", "touhouproject": "touhouproject", "dreamcast": "marzeniakast", "adventuregames": "gryprzygodowe", "wolfenstein": "wolfenstein", "actionadventure": "akcjaiprzygoda", "storyofseasons": "historiepórroku", "retrogames": "retrogry", "retroarcade": "retrokawi<PERSON><PERSON>", "vintagecomputing": "vintagedotyk", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "zabawa", "commanderkeen": "komandorkeen", "bugsnax": "robaczkizprzekąsek", "injustice2": "niesprawiedliwość2", "shadowthehedgehog": "shadowjeżowca", "rayman": "rayman", "skygame": "skygame", "zenlife": "życiewzenie", "beatmaniaiidx": "beatmaniaiidx", "steep": "stroma", "mystgames": "mystgier", "blockchaingaming": "g<PERSON><PERSON><PERSON>kchaino<PERSON>", "medievil": "mediewel", "consolegaming": "graniewkonsole", "konsolen": "konsole", "outrun": "uciekaj", "bloomingpanic": "kwitnącypanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horrorgry", "monstergirlquest": "monstergirlquest", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "farmingsims", "juegosviejos": "<PERSON><PERSON>ry", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktywnafikcja", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "ostatniznas2", "amantesamentes": "kochaćzmił<PERSON>ą", "visualnovel": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "powieścidowizualne", "rgg": "rgg", "shadowolf": "cieńwilka", "tcrghost": "tcrduch", "payday": "pieniądzynawieście", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "księżniczkaświtu", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "piaskownica", "aestheticgames": "estetycznegry", "novelavisual": "nowoaktywist", "thecrew2": "załoga2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogra", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamentuję", "godhand": "boskądłoń", "leafblowerrevolution": "rewolucjawiatrak<PERSON>", "wiiu": "wiiu", "leveldesign": "projektowaniepoziomów", "starrail": "starrail", "keyblade": "kluczowska", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "nowelawizualne", "robloxbrasil": "robloxpolska", "pacman": "pacman", "gameretro": "g<PERSON><PERSON><PERSON>", "videojuejos": "gier<PERSON>", "videogamedates": "randkizgrami", "mycandylove": "mojalubiedziewczyna", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangry", "returnofreckoning": "powrótnazemstę", "gamstergaming": "gamstersgaming", "dayofthetantacle": "dziennoktantalonów", "maniacmansion": "maniakalnyzmian", "crashracing": "wyścigyłomów", "3dplatformers": "3dplatformówki", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON>ry", "hellblade": "piekielnyostrze", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "game<PERSON>j<PERSON><PERSON>", "offmortisghost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybunny": "małykrólic<PERSON>k", "retroarch": "retroarch", "powerup": "mocnapodkładka", "katanazero": "katana0", "famicom": "famicom", "aventurasgraficas": "przygodygraficzne", "quickflash": "szybkiflash", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcady", "f123": "f123", "wasteland": "pust<PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "koralowawyspa", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxow<PERSON>", "anotherworld": "innyswiat", "metaquest": "metaquest", "animewarrios2": "animewojownicy2", "footballfusion": "fusionfutbolu", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomagic<PERSON><PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "kręconametal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "stoswstydu", "simulator": "symulator", "symulatory": "symulatory", "speedrunner": "speedrunner", "epicx": "epickx", "superrobottaisen": "superrobotwalenie", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "korkugry", "wonderlandonline": "krajczaronline", "skylander": "skylander", "boyfrienddungeon": "chłopakwięzienie", "toontownrewritten": "toontownprzerobiony", "simracing": "symulacjewyścigowe", "simrace": "si<PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "nieziemskieciala", "seum": "seum", "partyvideogames": "imprezowegry", "graveyardkeeper": "opiekuncmentarza", "spaceflightsimulator": "symulatorlotówkosmicznych", "legacyofkain": "d<PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackslash", "foodandvideogames": "jedzenieizgierki", "oyunvideoları": "nagraniazgryw", "thewolfamongus": "wilkwśródnas", "truckingsimulator": "symulator<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "grazwykłejręki", "leyendasyvideojuegos": "legendyigier", "oldschoolvideogames": "stareklasykiwideo", "racingsimulator": "symulatorwyścigów", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentimayskiejzagła<PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "wrótdoolimpu", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "buntownicztar", "indievideogaming": "indiewideozabawa", "indiegaming": "indiegaming", "indievideogames": "indiewi<PERSON><PERSON>", "indievideogame": "indiegieravideo", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffortress", "unbeatable": "niedopokonania", "projectl": "projektl", "futureclubgames": "p<PERSON><PERSON><PERSON><PERSON><PERSON>śćklubugier", "mugman": "kubkowyfacet", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "naukawen<PERSON>la<PERSON>ji", "backlog": "zaległ<PERSON><PERSON><PERSON>", "gamebacklog": "zaległościwgrze", "gamingbacklog": "zaległościwgrania", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "łowcazdobycz", "cityskylines": "widokinamiasto", "supermonkeyball": "supermałpka", "deponia": "deponia", "naughtydog": "<PERSON>eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "bestialsenior", "juegosretro": "gry<PERSON>ro", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriizniewidomymlesie", "alanwake": "alanwake", "stanleyparable": "stanleyporadnik", "reservatoriodedopamin": "rezerwacjadopaminy", "staxel": "staxel", "videogameost": "muzykazgier", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "wyścigipcr", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "świ<PERSON><PERSON><PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "smutneanime", "darkerthanblack": "ciemniejszyodczerni", "animescaling": "animeskalowanie", "animewithplot": "animezklimatem", "pesci": "pe<PERSON>ci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "ciemnywładca", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "kurdupel", "veranime": "veranime", "2000sanime": "animez2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezon1", "rapanime": "rapanime", "chargemanken": "ładowanieleniebieskim", "animecover": "animeokład<PERSON>", "thevisionofescaflowne": "wizjapodniebnejprzygody", "slayers": "zabójcy", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananowafryška", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletowezwiązanyhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "wanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartyodpatriota", "futurediary": "dziennikprzys<PERSON>ł<PERSON><PERSON>ci", "fairytail": "bajko<PERSON>love", "dorohedoro": "dorohedoro", "vinlandsaga": "sagawinlandzka", "madeinabyss": "zrobionewotchłani", "parasyte": "para<PERSON>ta", "punpun": "punpun", "shingekinokyojin": "ataktytanów", "mushishi": "mushishi", "beastars": "bestars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "syrenomelodia", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "mangiromansów", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "czarnalaguna", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "włóczącedogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "aczarownyindeks", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON>koniczyna", "tokyoghoul": "tokyoghoul", "onepunchman": "jednymocnymciosie", "hetalia": "hetalia", "kagerouproject": "projektkagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "szpiegrodzina", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "priorytetowajajońka", "angelsofdeath": "aniołowieśmierci", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeosportowe", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "gra<PERSON><PERSON>a", "angelbeats": "aniołki", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyazłe", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "ładnoscudownie", "theboyandthebeast": "chłopakizbestii", "fistofthenorthstar": "pietnaswiatlowodu", "mazinger": "mazinger", "blackbuttler": "czar<PERSON><PERSON><PERSON>", "towerofgod": "wieżaboga", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "serwa<PERSON>", "howtokeepamummy": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "pełniaksiężycamojitayaj", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "słodkieizjadowate", "martialpeak": "szczytmistrzów", "bakihanma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "dziewczynawysokiegowyniku", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "rewanzowafae", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "potworniczką", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanjii", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "s<PERSON><PERSON><PERSON>z", "amiti": "bądźmyzajebiste", "sailorsaturn": "żeglarzesaturn", "dio": "dio", "sailorpluto": "żeglarkapluto", "aloy": "aloy", "runa": "biegaj", "oldanime": "<PERSON><PERSON><PERSON>", "chainsawman": "człowiekzpiłą", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "toky<PERSON>wengers", "blackbutler": "czarnylokaj", "ergoproxy": "ergoproxy", "claymore": "k<PERSON><PERSON>", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "koszowowoców", "devilmancrybaby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "żyjmilą", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "obiecanażeństwo", "monstermanga": "monstermanga", "yourlieinapril": "twojakołcanakwietniu", "buggytheclown": "buggyklown", "bokunohero": "mojbokunohero", "seraphoftheend": "seraphkońca", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "głębinowywięzień", "jojolion": "jojo<PERSON>", "deadmanwonderland": "martwyczłowiekkraina", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "sercapandory", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "wojnyjedzeniowe", "cardcaptorsakura": "łapa<PERSON><PERSON>rtsak<PERSON>", "stolas": "stolas", "devilsline": "diabelskilimit", "toyoureternity": "dotwojejwiecz<PERSON>ści", "infpanime": "infpanime", "eleceed": "elenkawka", "akamegakill": "akamegakill", "blueperiod": "niebieskiokres", "griffithberserk": "griffithszaleje", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "wymazany", "bluelock": "bluelock", "goblinslayer": "zabójcagoblinów", "detectiveconan": "detektywconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "wamp<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "niebieskieg<PERSON>ści", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "szpiecrodzina", "airgear": "powietrznekoła", "magicalgirl": "<PERSON>znapanna", "thesevendeadlysins": "siedemgrzechówgłównych", "prisonschool": "szkoławwięzieniu", "thegodofhighschool": "bogszkoł<PERSON>wy<PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "wielkiebłękitne", "mydressupdarling": "mójubranionawspólnie", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "uniwersumanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saozaoszczędzone", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nieumarłyzłagodni", "romancemanga": "romantycznamanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromans", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentyna", "lolicon": "lolikon", "demonslayertothesword": "zabójcademonówdosłowa", "bloodlad": "krwiak", "goodbyeeri": "żegnajeri", "firepunch": "ognistyciosak", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "gwiazdyukładająsię", "romanceanime": "romansanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "czynnikkosmiczny", "cherrymagic": "czereśniamagia", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "nagrywajragnarok", "oyasumipunpun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "szkołazmartwychwstałych", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "klasałowców", "vindlandsaga": "sagawindlandzka", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "książętenisa", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "klasazabójców", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paradzedlanieżywych", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animespace": "animesfera", "girlsundpanzer": "dziew<PERSON><PERSON>yundpanzer", "akb0048": "akb0048", "hopeanuoli": "nieste<PERSON>znowu", "animedub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "brzoskwiniowadziewczyna", "cavalieridellozodiaco": "cavalier<PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "smokowieczdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "zabawnaamuzeum", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON>", "overgeared": "przegearowany", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "czapka_czarownicy_atelier", "lansizhui": "lansiżuj", "sangatsunolion": "sangatsunolion", "kamen": "kamień", "mangaislife": "mangażyciem", "dropsofgod": "kropelkiboga", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeszajonki", "reverseharem": "odwrotneharrem", "saintsaeya": "świętopełzów", "greatteacheronizuka": "świetnynauczyçonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "bloodplus", "bloodplusanime": "krewplusanime", "bloodcanime": "krwawakanime", "bloodc": "krwi<PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "opowieścidemonówiigodów", "goreanime": "goreanime", "animegirls": "dziewczynyanime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "wronyxnajgorsze", "splatteranime": "splatteranime", "splatter": "plama", "risingoftheshieldhero": "wschodziszyleczboka", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedoadjęte", "animeyuri": "animeyuri", "animeespaña": "animehiszpania", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "dzieciwielorybów", "liarliar": "kłamczuchkłamczuch", "supercampeones": "superbohaterzy", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartfonem", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magicznedziewczyny", "callofthenight": "wezwanienocy", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "brawler<PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "mahoujuszowezaczarowanedziewczyny", "shadowgarden": "shadowgarden", "tsubasachronicle": "tsubasachronik", "findermanga": "znajdzmanga", "princessjellyfish": "ksiażęcagąbka", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeświat", "persocoms": "persocomy", "omniscientreadersview": "omnisensownypunktwidzenia", "animecat": "kotyanime", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "otwierająceanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mojasteenowaromantycznakomedia", "evangelion": "<PERSON><PERSON><PERSON><PERSON>", "gundam": "gundam", "macross": "macross", "gundams": "gundamy", "voltesv": "voltesv", "giantrobots": "gigantyczneroboty", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilnywojownikggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "<PERSON><PERSON><PERSON>lacz", "deathnote": "z<PERSON>zytśmierci", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "dziwaczneprzygodyjojo", "fullmetalalchemist": "pełnometalowyalchemik", "ghiaccio": "lód", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animewojskowe", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupin3", "animecity": "animemiasto", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonprzygoda", "hxh": "hxh", "highschooldxd": "liceumdxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "zabójcademonów", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "już<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "mojaheroakademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "korpusankietowy", "onepieceanime": "onepieceanime", "attaquedestitans": "atakujtytanów", "theonepieceisreal": "j<PERSON>ynakawałekjestprawdziwy", "revengers": "mś<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcysta", "joyboyeffect": "efektchłopakaszczęścia", "digimonstory": "historied<PERSON><PERSON>", "digimontamers": "digimontamer<PERSON>", "superjail": "superwięzienie", "metalocalypse": "metalokalipsa", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uratakulturystwo", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "bezbłędnywebtoon", "kemonofriends": "kumplezwanów", "utanoprincesama": "bezwładniefryzura", "animecom": "animekom", "bobobobobobobo": "bababababababa", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "latająca_czarownica", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "takpoprostu", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "wszystkichświętychulica", "recuentosdelavida": "opowieściżycia"}