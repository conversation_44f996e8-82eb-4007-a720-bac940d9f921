{"2048": "2048", "mbti": "mbti", "enneagram": "eneagrama", "astrology": "astrología", "cognitivefunctions": "funcionescognitivas", "psychology": "psicología", "philosophy": "filosofía", "history": "historia", "physics": "física", "science": "ciencia", "culture": "cultura", "languages": "idiomas", "technology": "tecnología", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "memesdeastrología", "enneagrammemes": "memesdeeneagrama", "showerthoughts": "pensamientosenladucha", "funny": "divertido", "videos": "vídeos", "gadgets": "gadgets", "politics": "política", "relationshipadvice": "consejopararelaciones", "lifeadvice": "consejodevida", "crypto": "cripto", "news": "noticias", "worldnews": "notici<PERSON><PERSON><PERSON><PERSON>", "archaeology": "arqueología", "learning": "aprendizaje", "debates": "debates", "conspiracytheories": "teoriasconspirativas", "universe": "universo", "meditation": "meditacion", "mythology": "mitología", "art": "arte", "crafts": "artesanías", "dance": "baile", "design": "diseño", "makeup": "ma<PERSON><PERSON><PERSON>", "beauty": "belleza", "fashion": "moda", "singing": "canto", "writing": "escritura", "photography": "fotografía", "cosplay": "cosplay", "painting": "pintura", "drawing": "dibujo", "books": "libros", "movies": "películas", "poetry": "poesía", "television": "televisión", "filmmaking": "cine", "animation": "animación", "anime": "anime", "scifi": "cienciaficción", "fantasy": "fantasía", "documentaries": "documentales", "mystery": "misterio", "comedy": "comedia", "crime": "crimen", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romance", "realitytv": "realitytv", "action": "acción", "music": "música", "blues": "blues", "classical": "músicaclásica", "country": "country", "desi": "desi", "edm": "edm", "electronic": "electrónica", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latina", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tecno", "travel": "via<PERSON>s", "concerts": "conciertos", "festivals": "festivales", "museums": "museos", "standup": "standup", "theater": "teatro", "outdoors": "elairelibre", "gardening": "jardinería", "partying": "defiesta", "gaming": "gaming", "boardgames": "juegosdemesa", "dungeonsanddragons": "calabozosydragones", "chess": "<PERSON><PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "comida", "baking": "repostería", "cooking": "cocinar", "vegetarian": "vegetariano", "vegan": "vegano", "birds": "aves", "cats": "gatos", "dogs": "perros", "fish": "pescado", "animals": "animales", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ambientalismo", "feminism": "feminismo", "humanrights": "derech<PERSON><PERSON><PERSON>", "lgbtqally": "aliadelgbtq", "stopasianhate": "altoalodioalosasiaticos", "transally": "aliadetrans", "volunteering": "voluntariado", "sports": "deportes", "badminton": "badminton", "baseball": "béisbol", "basketball": "baloncesto", "boxing": "boxeo", "cricket": "cricket", "cycling": "ciclismo", "fitness": "fitness", "football": "fútbol", "golf": "golf", "gym": "gimnasio", "gymnastics": "gimnasia", "hockey": "hockey", "martialarts": "artesmarciales", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "correr", "skateboarding": "skateboarding", "skiing": "esquí", "snowboarding": "snowboarding", "surfing": "surfear", "swimming": "natación", "tennis": "tenis", "volleyball": "voleibol", "weightlifting": "levantamientodepesas", "yoga": "yoga", "scubadiving": "buceo", "hiking": "senderismo", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "acuario", "pisces": "piscis", "aries": "aries", "taurus": "tauro", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "escorpio", "sagittarius": "sagitario", "shortterm": "cort<PERSON><PERSON><PERSON><PERSON>", "casual": "informal", "longtermrelationship": "relaciónseria", "single": "soltero", "polyamory": "poliamor", "enm": "amornomonógamo", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbiana", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "santos<PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "perrosguardianes", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "buscadordepríncipes", "soulreaver": "cazadordealmas", "suikoden": "su<PERSON><PERSON>", "subverse": "subverso", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonesyjusticia", "sunsetoverdrive": "puestadesolconestilo", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloin<PERSON>ito", "guildwars": "guerre<PERSON>delag<PERSON><PERSON>", "openworld": "mundoabierto", "heroesofthestorm": "héroesdeltormenta", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "explorandodalas", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribusdemidgard", "planescape": "planodejuego", "lordsoftherealm2": "lordsofalrealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabotas", "lodsoftherealm2": "lolostrelm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simsinmersivos", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "caída", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "creacióndepersonajes", "immersive": "inmersivo", "falloutnewvegas": "fallout<PERSON><PERSON><PERSON>ve<PERSON>", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivaciónmacabra", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "locoporelamor", "otomegames": "jue<PERSON>otome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaodeltiempo", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiroelmascarade", "dimension20": "dimensión20", "gaslands": "tierrasdegas", "pathfinder": "explorador", "pathfinder2ndedition": "pathfinder2ed", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON>", "rpg": "jdr", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "unasolavez", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "señorsupremo", "yourturntodie": "tuvidatuextrañas", "persona3": "persona3", "rpghorror": "horrorrpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "textorpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonialmas", "mu": "mu", "falloutshelter": "refugiodecaída", "gurps": "gurps", "darkestdungeon": "dungeonmásoscuro", "eclipsephase": "faseeclipse", "disgaea": "disgaea", "outerworlds": "mundosex<PERSON>os", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloinmortal", "dynastywarriors": "guerrerosdeldinastía", "skullgirls": "chicascalaveras", "nightcity": "ciudadnoche", "hogwartslegacy": "legadodehogwarts", "madnesscombat": "locuraconcombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "nuncaantes", "road96": "camino96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "caballerosdegotham", "forgottenrealms": "reinosolvidados", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "ciudaddelosmuñecos", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "ladivisión2", "lineage2": "lineage2", "digimonworld": "mund<PERSON><PERSON><PERSON>", "monsterrancher": "ranchomonstruo", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "tronosrotos", "horizonforbiddenwest": "horizonteoesteprohibido", "twewy": "twewy", "shadowpunk": "sombrafuturista", "finalfantasyxv": "finalfantasyxv", "everoasis": "everosaico", "hogwartmystery": "misteriodehog<PERSON>s", "deltagreen": "deltaverde", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "destruir", "lastepoch": "últimaépoca", "starfinder": "buscadordeestrellas", "goldensun": "soldeoro", "divinityoriginalsin": "divinidadpecadooriginal", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "ciberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "ciberpunkrojo", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordencaída", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "sobrevivientedeldemonio", "oldschoolrunescape": "runescapeantiguo", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinidad", "pf2": "pf2", "farmrpg": "granjaju<PERSON>", "oldworldblues": "trist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "aventurabuscadora", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "juegosderol", "roleplayinggames": "juegosderol", "finalfantasy9": "finalfantasy9", "sunhaven": "solhaven", "talesofsymphonia": "cuentosdesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "ciudadtornada", "myfarog": "miboo", "sacredunderworld": "sacredunderworld", "chainedechoes": "ecosencadenados", "darksoul": "almaoscura", "soulslikes": "soulslikes", "othercide": "otrocidio", "mountandblade": "montañayespada", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "cronotrigger", "pillarsofeternity": "pillarsofeternidad", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "ladivisión", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "leyendadeldragón", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampiroenmascarada", "octopathtraveler": "viajebina<PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "lobodelaapocalipsis", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "corazónmotor", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rol<PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "ed<PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "renaceroldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mundosalvajes", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "reinoacorazones1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "oscuridadmazmorras", "juegosrpg": "juegosrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "corazoneslibres", "bastion": "bastión", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "cielosdearcadia", "shadowhearts": "corazonesenlasombra", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennysangre", "breathoffire4": "alientosdelfuego4", "mother3": "madre3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "caídadeboo", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "juegosderol", "roleplaygame": "juegoderol", "fabulaultima": "fab<PERSON><PERSON><PERSON><PERSON><PERSON>", "witchsheart": "corazóndelahechicera", "harrypottergame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "boorpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilaenmascarada", "dračák": "dragón", "spelljammer": "navegandoconboo", "dragonageorigins": "dragonageorígenes", "chronocross": "cronocross", "cocttrpg": "cocttrpg", "huntroyale": "cazatorreales", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "cazadoresterribles", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "fororpg", "shadowheartscovenant": "elcovendeshadowheart", "bladesoul": "almafilo", "baldursgate3": "baldursgate3", "kingdomcome": "reinoviene", "awplanet": "awplaneta", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragaliaenlost", "elderscroll": "elescroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "tácticasfinalfantasía", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "fororpg", "golarion": "golarion", "earthmagic": "magia<PERSON><PERSON><PERSON><PERSON>", "blackbook": "libronegro", "skychildrenoflight": "ni<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "edicióndoradosagrado", "castlecrashers": "castillofalladores", "gothicgame": "jue<PERSON>g<PERSON><PERSON><PERSON>", "scarletnexus": "nexussangriento", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "juegorpg", "prophunt": "cazaprotagonistas", "starrails": "starrails", "cityofmist": "ciudaddelabruma", "indierpg": "indierpg", "pointandclick": "pointandclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON>lejosde<PERSON>ás", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisible", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7siemprecrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "caminodelamuerteacanadá", "palladium": "pali", "knightjdr": "caballerojdr", "monsterhunter": "cazamons<PERSON><PERSON>s", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacía", "persona5": "persona5", "ghostoftsushima": "fantasmadetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "cazadmonstruosrise", "nier": "<PERSON><PERSON><PERSON>", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "juegosnonarios", "tacticalrpg": "rpgtáctico", "mahoyo": "mahoyo", "animegames": "j<PERSON><PERSON>deanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeterna", "princessconnect": "princessconnect", "hexenzirkel": "circulodebrujas", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketsage", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindia", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esports", "mlg": "mlg", "leagueofdreamers": "ligadesoñadores", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "futbolvirtual", "dreamhack": "sue<PERSON>", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "ciberdeporte", "crazyraccoon": "r<PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitivo", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "mit<PERSON><PERSON>a", "left4dead": "dejadopor<PERSON><PERSON><PERSON>", "left4dead2": "izquierda4muerto2", "valve": "vál<PERSON>la", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "veranomás", "goatsimulator": "simuladordecabras", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetalibertad", "transformice": "transformice", "justshapesandbeats": "sóloformasytonos", "battlefield4": "campodebatalla4", "nightinthewoods": "nocheenlosbosques", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riesgodelluvia2", "metroidvanias": "metroidvanias", "overcooked": "sobrecocinado", "interplanetary": "interplanetario", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "celd<PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortalezadeenanos", "foxhole": "foxhole", "stray": "perdido", "battlefield": "campodebatall<PERSON>", "battlefield1": "campodebatalla1", "swtor": "sw<PERSON>mania", "fallout2": "caída2", "uboat": "uboat", "eyeb": "ojos", "blackdesert": "desiertonegro", "tabletopsimulator": "simuladordem<PERSON>as", "partyhard": "fiestacontodo", "hardspaceshipbreaker": "rom<PERSON><PERSON><PERSON><PERSON>", "hades": "hadess", "gunsmith": "armero", "okami": "<PERSON>ami", "trappedwithjester": "atrapadoconjester", "dinkum": "dinkum", "predecessor": "predecesor", "rainworld": "mundodelluvia", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "simdelacolonia", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "maestrosminions", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "oscuroydarkero", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "trabajordalma", "datingsims": "simuladoresdecita", "yaga": "yaga", "cubeescape": "escapeenelcubo", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "paisajesurbanos", "defconheavy": "defconpesado", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiavirtual", "snowrunner": "snowrunner", "libraryofruina": "bibliotecaderuina", "l4d2": "l4d2", "thenonarygames": "juegosdelanonario", "omegastrikers": "omegastrikers", "wayfinder": "buscador", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "pollitomaximo", "dialtown": "dialpueblo", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "nochedegatos", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "<PERSON><PERSON><PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "tierrafronteriza", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "gritolejano", "farcrygames": "j<PERSON>gosfar<PERSON>ry", "paladins": "paladins", "earthdefenseforce": "fuerzadefensadelatierra", "huntshowdown": "ca<PERSON><PERSON><PERSON>za", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "guerrasz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "únetealcombo", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "tormentadearenainsurgente", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzomb<PERSON>", "mirrorsedge": "espejosdellímite", "divisions2": "divisiones2", "killzone": "zona<PERSON><PERSON>a", "helghan": "hel<PERSON>", "coldwarzombies": "guerrafríazomb<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "combateace", "crosscode": "cruzacódigo", "goldeneye007": "ojodeoro007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "guerramoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "guerre<PERSON><PERSON>", "boarderlands": "tierrafronteriza", "owerwatch": "boooverwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>cap<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carniceríaprimal", "worldofwarships": "mundodebar<PERSON>deguerrav", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "asesino", "masseffect": "masseffect", "systemshock": "shockdelasistemas", "valkyriachronicles": "crónicasdevalkyria", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cuevahistoria", "doometernal": "doometernal", "centuryageofashes": "sigloedelast<PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "engranajesdelgue<PERSON>", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tyelt<PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "generacioncero", "enterthegungeon": "entra<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "guerramoderna2", "blackops1": "blackops1", "sausageman": "hombredesalchicha", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "eldolorphantom", "warface": "carasdeguerray", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "corazóntóxico", "blackops3": "blackops3", "vampiresurvivors": "vampirossobreviviendo", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "gallinaseca", "freedoom": "libertad", "battlegrounds": "camposdebatalla", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON>", "gamepubg": "juegopubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearhijosdelalibert", "juegosfps": "juegosfps", "convertstrike": "conviertetubombazo", "warzone2": "zonadeguerra2", "shatterline": "quiebracader<PERSON>", "blackopszombies": "zombisblackops", "bloodymess": "desastresangriento", "republiccommando": "comandodelarepública", "elitedangerous": "elitepeligroso", "soldat": "soldadito", "groundbranch": "ramapiso", "squad": "escuadrón", "destiny1": "destino1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "chicapubg", "worldoftanksblitz": "mund<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "enlistado", "farlight": "l<PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "coreblindado", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "lasmaravillasdetinytina", "halo2": "halo2", "payday2": "díadepago2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "jabóncod", "ghostcod": "codfantasma", "csplay": "csplay", "unrealtournament": "torneodeincreíble", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "contrataque", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "campeon<PERSON>del<PERSON>b<PERSON>", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "celdasplinter", "neonwhite": "neonwhite", "remnant": "residuo", "azurelane": "azurelane", "worldofwar": "mundodeguerra", "gunvolt": "gunvolt", "returnal": "retornales", "halo4": "halo4", "haloreach": "saludohalo", "shadowman": "hombredeunasombra", "quake2": "quake2", "microvolts": "microvoltios", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "campodebatalla3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "caída76", "elsword": "els<PERSON>", "seaofthieves": "mardelladrones", "rust": "óxido", "conqueronline": "conquersonline", "dauntless": "valiente", "warships": "barcosdegue<PERSON>boo", "dayofdragons": "díadelosdragones", "warthunder": "guerraenlatuerca", "flightrising": "vueloascendente", "recroom": "recroom", "legendsofruneterra": "legend<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "misterio", "phantasystaronline2": "phantasystaronline2", "maidenless": "sinnovia", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "mundotanques", "crossout": "tachar", "agario": "agario", "secondlife": "segundavid<PERSON>", "aion": "aion", "toweroffantasy": "torredelafantasía", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "caballeronline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "elvinculode<PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpingüino", "lotro": "lotro", "wakfu": "wakfu", "scum": "escoria", "newworld": "nuevomundo", "blackdesertonline": "blackdesertonline", "multiplayer": "multijugador", "pirate101": "pirata101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "batalladeestrellas", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "chat3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclásico", "worldofwarcraft": "mundodeguerrasymagia", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cenizasdecreación", "riotmmo": "riotmmo", "silkroad": "silkroad", "spiralknights": "caballerosdelaspirales", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragonesprofeta", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "<PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "universodconline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepúblicavieja", "grandfantasia": "granfantasía", "blueprotocol": "protocoloblue", "perfectworld": "mundoperfecto", "riseonline": "elevaonline", "corepunk": "núcleopunk", "adventurequestworlds": "aventurabuscandomundos", "flyforfun": "volarportdiversión", "animaljam": "animaljam", "kingdomofloathing": "reinadodela<PERSON>ez<PERSON>", "cityofheroes": "ciudaddeshéroes", "mortalkombat": "mortalkombat", "streetfighter": "luchadorcallejero", "hollowknight": "caballerodelavacía", "metalgearsolid": "metalgearsolid", "forhonor": "porhonor", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "guerrerovirtual", "streetsofrage": "callesdelagracia", "mkdeadlyalliance": "mkalianzaletal", "nomoreheroes": "nomáshéroes", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "elreydefighters", "likeadragon": "comosaldragón", "retrofightinggames": "juegosdefighting<PERSON>ro", "blasphemous": "blasfemo", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "guerradebestias", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "ciberbots", "armoredwarriors": "guerrerosblindados", "finalfight": "peleafinal", "poweredgear": "pot<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "gol<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "juegosdepelea", "killerinstinct": "instinto<PERSON><PERSON>", "kingoffigthers": "reydelosluchadores", "ghostrunner": "correveletas", "chivalry2": "caballerosidad2", "demonssouls": "demoniosalmas", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "secuelaboo", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "noticiasdeboosilksong", "silksong": "silksong", "undernight": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "torneodeevolución", "evomoment": "evomomento", "lollipopchainsaw": "p<PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "historiasdeberseria", "bloodborne": "sangrevaliente", "horizon": "horizonte", "pathofexile": "caminoalexilio", "slimerancher": "ranchoeslimer", "crashbandicoot": "crashbandicoot", "bloodbourne": "sangrevuelta", "uncharted": "noexplorado", "horizonzerodawn": "horizonteceroamanecer", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "infame", "playstationbuddies": "amigosdeplaystation", "ps1": "ps1", "oddworld": "mundoex<PERSON>ño", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "compañiarouge", "aisomniumfiles": "archivosaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "griso", "trove": "trovazo", "detroitbecomehuman": "detroitconvertirseenhumano", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "trophydeturistas", "lspdfr": "lspdfr", "shadowofthecolossus": "sombradelcoloso", "crashteamracing": "crashteamracing", "fivepd": "fivepd", "tekken7": "tekken7", "devilmaycry": "eldiaboloq<PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "demoniosnolloran5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "elúltimoguardián", "soulblade": "<PERSON>pad<PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "cacería", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "últimoguardián", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON>o", "warharmmer40k": "warhammer40k", "fightnightchampion": "nochedefightnight", "psychonauts": "psiconautas", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "losestudiosdelosancianosskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gxbox": "gxbóx", "battlefront": "frent<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "nodessincompartir", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "haciaestrellas", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "reformadoras", "americanmcgeesalice": "alicedeamericanmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligadereinos", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telebasura", "skycotl": "skycotl", "erica": "erika", "ancestory": "ancestoria", "cuphead": "cuphead", "littlemisfortune": "pobremisfortunas", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "promdemonstruos", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultodelcordero", "duckgame": "juegodepatos", "thestanleyparable": "eljuegodes<PERSON><PERSON>", "towerunite": "towerunite", "occulto": "oculto", "longdrive": "drivelejano", "satisfactory": "satisfactorio", "pluviophile": "pluviophilia", "underearth": "subsuelo", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "espíritusnavegantes", "darkdome": "cúpulatene<PERSON><PERSON>", "pizzatower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "verdadoreso", "game": "juego", "rockpaperscissors": "piedrapapeltijera", "trampoline": "trampolín", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "desafío", "scavengerhunt": "caza_del_tesoro", "yardgames": "juegosdejardín", "pickanumber": "elijunúmero", "trueorfalse": "verdader<PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "goblinludico", "cosygames": "j<PERSON><PERSON>acoged<PERSON>", "datinggames": "juegosdedate", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "juegosdedrinking", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "juegos", "simulationgames": "juegosdesimulación", "wordgames": "juegosdepalab<PERSON>", "jeuxdemots": "juegosdemots", "juegosdepalabras": "juegosdepalab<PERSON>", "letsplayagame": "jug<PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "j<PERSON><PERSON><PERSON>", "interactivegames": "juegosinteractivos", "amtgard": "amtgard", "staringcontests": "concurs<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "jue<PERSON><PERSON><PERSON>", "giochi": "juegos", "geoguessr": "geoguessr", "iphonegames": "juegoseniphone", "boogames": "boojuegos", "cranegame": "juegodegrúas", "hideandseek": "escondite", "hopscotch": "<PERSON><PERSON><PERSON>", "arcadegames": "juegosdearcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "juegoclásico", "mindgames": "juegosdementales", "guessthelyric": "adiv<PERSON><PERSON><PERSON>", "galagames": "galajuegos", "romancegame": "juegodromance", "yanderegames": "jue<PERSON>yander<PERSON>", "tonguetwisters": "trabalenguas", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "juegosdearcade", "tabletopgames": "juegosedetabla", "metroidvania": "metroidvania", "games90": "juegos90", "idareyou": "tereto", "mozaa": "mozaa", "fumitouedagames": "fumitouedajuegos", "racinggames": "juegosderacing", "ets2": "ets2", "realvsfake": "realvsfalso", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "juegonline", "onlinegames": "juegosonline", "jogosonline": "juegosonline", "writtenroleplay": "rol<PERSON><PERSON><PERSON>", "playaballgame": "juegobalón", "pictionary": "pictionary", "coopgames": "juegoscoop", "jenga": "jenga", "wiigames": "wiijuegos", "highscore": "mejormarcador", "jeuxderôles": "jue<PERSON>der<PERSON><PERSON>", "burgergames": "juegosdehamburguesas", "kidsgames": "juegosdechicos", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "concursito", "tcgplayer": "tcgplayer", "juegodepreguntas": "bromadadepreguntas", "gioco": "juego", "managementgame": "juegodegestión", "hiddenobjectgame": "juegodeobjetosocultos", "roolipelit": "rolitodivertido", "formula1game": "juegodef1", "citybuilder": "constructoradecidades", "drdriving": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "juegosdearcade", "memorygames": "juegosdelamemoria", "vulkan": "vulkan", "actiongames": "juegosdeacción", "blowgames": "jue<PERSON>deviento", "pinballmachines": "máquinasdepinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "cooperaplay", "perguntados": "preguntados", "gameo": "gameo", "lasergame": "j<PERSON><PERSON><PERSON><PERSON>", "imessagegames": "jue<PERSON><PERSON>", "idlegames": "jue<PERSON>par<PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "completalapesa", "jeuxpc": "jeuxpc", "rétrogaming": "retrojugando", "logicgames": "juegosdelogicismo", "japangame": "japanga<PERSON>", "rizzupgame": "juegodeflow", "subwaysurf": "surfenelmetro", "jeuxdecelebrite": "juegosdecelebridades", "exitgames": "juegosdeescape", "5vs5": "5vs5", "rolgame": "rol<PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "jue<PERSON><PERSON><PERSON>", "traditionalgames": "juegostradicionales", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "juegosdetexto", "gryparagrafowe": "graparagra<PERSON><PERSON>", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "j<PERSON><PERSON>dell<PERSON>ron", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "futbolin", "tischfußball": "futbolin", "spieleabende": "nochesdejuego", "jeuxforum": "forodejuegos", "casualgames": "juegoscasuales", "fléchettes": "dardos", "escapegames": "escaperooms", "thiefgameseries": "seriejuegodelladron", "cranegames": "juegosdegrúa", "játék": "juego", "bordfodbold": "bordfodbold", "jogosorte": "j<PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "juegosenlín<PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "nochesdejuego", "pursebingos": "pursebingos", "randomizer": "aleatorizador", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "juegospc", "socialdeductiongames": "juegosdededucciónsocial", "dominos": "dominos", "domino": "domino", "isometricgames": "jue<PERSON>isomé<PERSON><PERSON>", "goodoldgames": "buenosjuegosdecampaña", "truthanddare": "verdadyreto", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "ca<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "jeuxvirtuales", "romhack": "romhack", "f2pgamer": "jugadorf2p", "free2play": "libreparajugar", "fantasygame": "juegodefantasía", "gryonline": "gryonline", "driftgame": "juegodedrift", "gamesotomes": "juegosparatí", "halotvseriesandgames": "halotvseriesyjuegos", "mushroomoasis": "oasisdemushrooms", "anythingwithanengine": "cualquiercosaquanconmotor", "everywheregame": "juegoportodaspartes", "swordandsorcery": "espadasyhechicería", "goodgamegiving": "buenjuegovirtual", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8juegos", "labzerogames": "labzerogames", "grykomputerowe": "videosjuegos", "virgogami": "virgogami", "gogame": "j<PERSON><PERSON><PERSON>", "jeuxderythmes": "juegosderitmos", "minaturegames": "juegosdeminatura", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "amorpropionatupantalla", "gamemodding": "modding<PERSON><PERSON><PERSON><PERSON>", "crimegames": "juegosdecrimen", "dobbelspellen": "dobbelju<PERSON><PERSON>", "spelletjes": "juegos", "spacenerf": "espaciopoli", "charades": "charadas", "singleplayer": "jugadorindividual", "coopgame": "juegocoop", "gamed": "jugado", "forzahorizon": "forzahorizon", "nexus": "nexo", "geforcenow": "geforcenow", "maingame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "<PERSON><PERSON><PERSON><PERSON>", "scrabble": "scrabble", "schach": "jaque", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "legadopandémico", "camelup": "camellosarriba", "monopolygame": "juegomonopoly", "brettspiele": "juegosdebrote", "bordspellen": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "juegodemesa", "sällskapspel": "juegosdecompañía", "planszowe": "juegosdecar<PERSON>", "risiko": "risiko", "permainanpapan": "juegosdepapel", "zombicide": "zombicidio", "tabletop": "mesa_de_juegos", "baduk": "baduk", "bloodbowl": "bowldesangre", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "gobardelejuegos", "connectfour": "conéctatecuatro", "heroquest": "heroquest", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "carrom", "tablegames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "jue<PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesociedad", "deskgames": "juegosdeescritorio", "alpharius": "alpharius", "masaoyunları": "masaboo", "marvelcrisisprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "encuentrocosmico", "creationludique": "creaciónlúdica", "tabletoproleplay": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "juegosdecartón", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "juegosenlínia", "infinitythegame": "infinidadeljuego", "kingdomdeath": "muer<PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "escuelayescaleras", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "buenosjuegos", "planszówki": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "vidaderedneck", "boardom": "aburrimiento", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "juegodemesa", "gameboard": "tablerodejuego", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jue<PERSON>ediver<PERSON><PERSON>", "twilightimperium": "imperiottomanoche", "horseopoly": "caballopoly", "deckbuilding": "construccióndemazos", "mansionsofmadness": "mansionesdelamalocura", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "sombrasdebrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "juegosdemesa", "battleship": "barcosdebatalla", "tickettoride": "ticketparavolar", "deskovehry": "escritoriosdivertidos", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "juegosdemesa", "stolníhry": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "juegosdesociedad", "gesellschaftsspiele": "juegosdemesa", "starwarslegion": "legiondeestrellas", "gochess": "jugarchess", "weiqi": "weiqi", "jeuxdesocietes": "juegosdeempresa", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonadeguerra", "arksurvivalevolved": "arksupervivienteexpandido", "dayz": "días", "identityv": "identidadv", "theisle": "laisla", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyylamachin<PERSON><PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eatsleepfortnite", "amongus": "entrenosotros", "eco": "eco", "monkeyisland": "islademonos", "valheim": "valheim", "planetcrafter": "planetcrafters", "daysgone": "díasquehanpasado", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patológico", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "bodega", "grounded": "conectado", "stateofdecay2": "estadodedescomposición2", "vrising": "vrising", "madfather": "madmadre", "dontstarve": "nodersurfes", "eternalreturn": "retornoeterno", "pathoftitans": "caminodelostitanes", "frictionalgames": "juegosfrictional", "hexen": "hechiceras", "theevilwithin": "el<PERSON>int<PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "loscuartosdetras", "backrooms": "salasocultas", "empiressmp": "empiresmp", "blockstory": "historiabloque", "thequarry": "lacantera", "tlou": "tlou", "dyinglight": "luzmortal", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "somospocosfelices", "riseofempires": "elas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "estadodesupervivenciagame", "vintagestory": "historiasvintage", "arksurvival": "supervivenciadeark", "barotrauma": "barotrauma", "breathedge": "respirando", "alisa": "alisa", "westlendsurvival": "supervivenciaenwestlend", "beastsofbermuda": "bestiasdebermuda", "frostpunk": "frostpunk", "darkwood": "maderos<PERSON><PERSON>", "survivalhorror": "horrorconsurvival", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentesmal4", "residentevil3": "residentevil3", "voidtrain": "trenvacío", "lifeaftergame": "la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "juegosdesupervivencia", "sillenthill": "silentebu", "thiswarofmine": "estaguerradeyo", "scpfoundation": "fundacionscp", "greenproject": "proyectoverde", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "balsa", "rdo": "rdo", "greenhell": "infiernoverde", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "abuelita", "littlenightmares2": "pequeñoscabosdepesadilla2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "hijosedelbosque", "rustvideogame": "juegor<PERSON>", "outlasttrials": "sobrevivirlosretos", "alienisolation": "aislacionalienígena", "undawn": "<PERSON><PERSON><PERSON>", "7day2die": "7días2morir", "sunlesssea": "marisolsinsol", "sopravvivenza": "supervivencia", "propnight": "nochedeprop", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampiro", "deathverse": "muerte<PERSON><PERSON>", "cataclysmdarkdays": "catastróficosdíasoscuros", "soma": "soma", "fearandhunger": "temoryhambre", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "vidadespués", "ageofdarkness": "eradeoscuridad", "clocktower3": "relojdelatorre3", "aloneinthedark": "solosenlasombra", "medievaldynasty": "dinastiam<PERSON><PERSON><PERSON>", "projectnimbusgame": "proyectonimbusjuego", "eternights": "eternocitas", "craftopia": "craftopia", "theoutlasttrials": "losensayosdeoutlast", "bunker": "bunker", "worlddomination": "dominacionmundial", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficinadeasesinos", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "matadordever<PERSON><PERSON>", "warhammer40kcrush": "aplastamientowarhammer40k", "wh40": "wh40", "warhammer40klove": "amorwarhammer40k", "warhammer40klore": "warhammer40kleyenda", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kmareanegra", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicarse", "ilovesororitas": "meencantanlasororitas", "ilovevindicare": "amalavindicarse", "iloveassasinorum": "meencantanlosasesinorum", "templovenenum": "templovenenú", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeredicionadesigmar", "civilizationv": "civilizaciónv", "ittakestwo": "senecesitandos", "wingspan": "envergadura", "terraformingmars": "terraformandonmarte", "heroesofmightandmagic": "héroesdelafuerzaylamagia", "btd6": "btd6", "supremecommander": "comandantesupremo", "ageofmythology": "épocadellamito", "args": "args", "rime": "rima", "planetzoo": "planetazoo", "outpost2": "outpost2", "banished": "desterrado", "caesar3": "césar3", "redalert": "alert<PERSON><PERSON>", "civilization6": "civilización6", "warcraft2": "warcraft2", "commandandconquer": "man<PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "guerradeeternidad", "strategygames": "juegosdeestrategia", "anno2070": "anno2070", "civilizationgame": "juegodcivilización", "civilization4": "civilización4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "esporas", "totalwar": "guerratotal", "travian": "travian", "forts": "forts", "goodcompany": "buenacompañía", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "paralos<PERSON>es", "realtimestrategy": "estrategiarealtime", "starctaft": "starctaft", "sidmeierscivilization": "civilización<PERSON><PERSON><PERSON><PERSON>", "kingdomtwocrowns": "reinadodos<PERSON>zos", "eu4": "yo4", "vainglory": "vanidad", "ww40k": "ww40k", "godhood": "divinidad", "anno": "annnno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "clasedeálgebradivertidadedave", "plagueinc": "plagueinc", "theorycraft": "teoríadeju<PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilización3", "4inarow": "<PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "héroes3", "advancewars": "advancewars", "ageofempires2": "edaddeimperios2", "disciples2": "discípulos2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "juegodetáctica", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "edaddelasmaravillas", "dinosaurking": "reyreptil", "worldconquest": "conquistamundial", "heartsofiron4": "corazonesdehierro4", "companyofheroes": "compañia<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "batallaporwesnoth", "aoe3": "aoe3", "forgeofempires": "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansoeleganzanito", "phobies": "fobias", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "porturnos", "bomberman": "bomberman", "ageofempires4": "edaddeemperadores4", "civilization5": "civilización5", "victoria2": "victoria2", "crusaderkings": "reyesencruzad<PERSON>", "cultris2": "cultris2", "spellcraft": "hechicería", "starwarsempireatwar": "imperioenlaguerradelasestrellas", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrategia", "popfulmail": "popfulmail", "shiningforce": "fuerzaluminosa", "masterduel": "duelama<PERSON>ro", "dysonsphereprogram": "programadysonsphere", "transporttycoon": "transportetronos", "unrailed": "desvía2", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetormenta", "uplandkingdoms": "reinosuplandia", "galaxylife": "vidagaláctica", "wolvesvilleonline": "lobosvilleonline", "slaythespire": "do<PERSON><PERSON><PERSON><PERSON>", "battlecats": "gatosdebatalla", "sims3": "sims3", "sims4": "sims4", "thesims4": "losims4", "thesims": "losims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "necesitasvelocidad", "needforspeedcarbon": "necesitopararapido", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "losims2", "thesims3": "losims3", "thesims1": "losims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "sobrevivir", "deadbydaylight": "muertoporeld<PERSON>", "alicemadnessreturns": "elreg<PERSON>ode<PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologíadecaballososcuros", "phasmophobia": "fasmofobia", "fivenightsatfreddys": "cinconochesenfred<PERSON>s", "saiko": "saiko", "fatalframe": "marcadademuerte", "littlenightmares": "pesadillaspequeñas", "deadrising": "muertoselevanta", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "encasa", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "pequeñamisalfortuna", "projectzero": "proyectocero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "holapvecino", "helloneighbor2": "holavecinode2", "gamingdbd": "gamingdbd", "thecatlady": "lacatrina", "jeuxhorreur": "juegosdehor<PERSON>res", "horrorgaming": "horrorjuegos", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartascontraelhumano", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "nombresclave", "dixit": "dixit", "bicyclecards": "bicicletacards", "lor": "lor", "euchre": "euchre", "thegwent": "elgwent", "legendofrunetera": "leyendaderunetera", "solitaire": "solitario", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "forjaclaves", "cardtricks": "trucosdecartas", "playingcards": "cartasjugando", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "runnerdered", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartascoleccionables", "pokemoncards": "cartasdepokemon", "fleshandbloodtcg": "carneysyangretcg", "sportscards": "cartasdeportivas", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "picas", "warcry": "gritodeguerra", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "reydelcorazón", "truco": "truco", "loteria": "lotería", "hanafuda": "hana<PERSON>da", "theresistance": "laresistencia", "transformerstcg": "transformerstcg", "doppelkopf": "doblecara", "yugiohcards": "cartasdeyuigio", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "ma<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "ojosazulesdragónblanco", "yugiohgoat": "yugiograndesgorra", "briscas": "briscas", "juegocartas": "boocar<PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "juegosdecar<PERSON>", "mtgjudge": "juezdemtg", "juegosdecartas": "juegosdecar<PERSON>", "duelyst": "duelist", "mtgplanschase": "mtgplanescaza", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "juegodecar<PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "sagadeespirit<PERSON><PERSON><PERSON>", "jogodecartas": "juegodecar<PERSON>", "žolíky": "bolitas", "facecard": "tarjetafacial", "cardfight": "peleadecar<PERSON>", "biriba": "biriba", "deckbuilders": "constructorasdebarajas", "marvelchampions": "campeonesmarvel", "magiccartas": "cartasmágicas", "yugiohmasterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornioinestable", "cyberse": "ciberse", "classicarcadegames": "juegosdearcadeclásicos", "osu": "osu", "gitadora": "gitadora", "dancegames": "jue<PERSON><PERSON><PERSON>", "fridaynightfunkin": "viernesnochefunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "proyectodiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "bailacon<PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "sacudiendolosemuertos", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "bailacentral", "rhythmgamer": "gamerdelritmo", "stepmania": "stepmania", "highscorerythmgames": "juegosderitmosconaltosciones", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "cielorítmico", "hypmic": "hypmic", "adanceoffireandice": "bailadefuegoyhielo", "auditiononline": "audiciónonline", "itgmania": "itgmanía", "juegosderitmo": "juegosderitmo", "cryptofthenecrodancer": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmdoctor": "<PERSON><PERSON><PERSON>", "cubing": "cubing", "wordle": "palabrería", "teniz": "tenis", "puzzlegames": "juegosdepuzzle", "spotit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "puzleslógicos", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "acertijos", "rubikscube": "cubor<PERSON>k", "crossword": "so<PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "crucigramas", "nonogram": "nonograma", "bookworm": "ratondebiblioteca", "jigsawpuzzles": "puzzlejigsaw", "indovinello": "adivinanza", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "acertijos", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "dentro", "angrybirds": "pájaroscabreados", "escapesimulator": "simuladordeescape", "minesweeper": "buscaminas", "puzzleanddragons": "puzzleydragones", "crosswordpuzzles": "sudokussorpreendentes", "kurushi": "k<PERSON>hi", "gardenscapesgame": "juegodejardinescapes", "puzzlesport": "puzzlemania", "escaperoomgames": "juegosdeescaperoom", "escapegame": "juegodeescapatoria", "3dpuzzle": "puzzle3d", "homescapesgame": "juegohomescapes", "wordsearch": "buscadordepalabras", "enigmistica": "enigmística", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "cuentosconadivinanzas", "fishdom": "fishdom", "theimpossiblequiz": "elquizimposible", "candycrush": "candycrush", "littlebigplanet": "pequeñograndonube", "match3puzzle": "rompecabezas3enraya", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "curioso", "rubikcube": "cubor<PERSON>k", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "elprincipiotalos", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "juegosdetycoon", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "cifras", "rätselwörter": "palabrasen<PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "resolviendoenigmas", "turnipboy": "<PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "nadie", "guessing": "adivinando", "nonograms": "nonogramas", "kostkirubika": "kostkirubika", "crypticcrosswords": "crucigramascrípticos", "syberia2": "syberia2", "puzzlehunt": "cazadelrompecabezas", "puzzlehunts": "cazapuzles", "catcrime": "delitodegatos", "quebracabeça": "quebracabeza", "hlavolamy": "boodesaf<PERSON>s", "poptropica": "poptropica", "thelastcampfire": "elúltimobonfire", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "canciónerrante", "carto": "carto", "untitledgoosegame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "laberinto", "tinykin": "tinykin", "rubikovakostka": "cubor<PERSON>k", "speedcube": "speedcube", "pieces": "piezas", "portalgame": "juegodeportal", "bilmece": "bilmece", "puzzelen": "resuelve", "picross": "picross", "rubixcube": "cubosrubik", "indovinelli": "adivinanzas", "cubomagico": "cubomágico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "maravilladistorsionada", "monopoly": "monopolio", "futurefight": "luch<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstars", "coc": "boococ", "lonewolf": "lobosolitario", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "estrellasensemble", "asphalt9": "asfalto9", "mlb": "mlb", "cookierunkingdom": "reinodegalletas", "alchemystars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "estadoenelquesobrevivir", "mycity": "mi<PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "escenacolorida", "bloonstowerdefense": "defensatowerbloo", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "correrconcaballeros", "fireemblemheroes": "héroesdefuegoemblema", "honkaiimpact": "honkaiimpact", "soccerbattle": "batallafutbol", "a3": "a3", "phonegames": "juegosdelmovil", "kingschoice": "elecciondelrey", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "amantesdelmotor", "tacticool": "tacticool", "cookierun": "corrercongalletas", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "fueradeltema", "craftsman": "artesano", "supersus": "supersospechoso", "slowdrive": "conducciónlenta", "headsup": "atento", "wordfeud": "wordfeud", "bedwars": "guerraenlacamita", "freefire": "freefire", "mobilegaming": "jue<PERSON>enmovimie<PERSON>", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "choquedeclanes", "pjsekai": "pjsekai", "mysticmessenger": "mensajeromístico", "callofdutymobile": "callofdutymóvil", "thearcana": "thearcana", "8ballpool": "bola8pool", "emergencyhq": "emergenciahq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albión", "hayday": "díasdeoro", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tiri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "choquedeclanes", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "princesadeltiempo", "beatstar": "beatstar", "dragonmanialegend": "legendadelhombredragón", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "amordebolso", "androidgames": "j<PERSON><PERSON><PERSON><PERSON>", "criminalcase": "casocriminal", "summonerswar": "guerradeinvocadores", "cookingmadness": "locuracocinera", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligadeángeles", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "nubeneural", "mysingingmonsters": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "espejoverse", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamóvil", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmóvil", "ingress": "ingreso", "slugitout": "peleahastaelfinal", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "castigandoravengrise", "petpals": "compasdemascotas", "gameofsultans": "jue<PERSON><PERSON>ult<PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "loboso", "runcitygame": "juegodecorrerciudad", "juegodemovil": "juegodemóvil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimética", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "leyendaenlared", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "llamadadedragones", "shiningnikki": "nikkishining", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "caminoa<PERSON><PERSON><PERSON>", "sealm": "sello", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "derbydemolicion3", "wordswithfriends2": "palabrasconamigos2", "soulknight": "caballeroalma", "purrfecttale": "historiaperfecta", "showbyrock": "showbyrock", "ladypopular": "chicapopular", "lolmobile": "lolmovil", "harvesttown": "pueblocose<PERSON>", "perfectworldmobile": "mundoperfectoboo", "empiresandpuzzles": "imperiosypuzzles", "empirespuzzles": "empirepuzzles", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "pequeñacosaoscura", "aethergazer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mudrunner": "atrevidoboo", "tearsofthemis": "lágrimasdesuami", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmovil", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zomb<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "ecoedeve", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "cocinand<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "luchadorcallejero", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "chicasfrentealínea", "jurassicworldalive": "jurassicworldvivo", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "superándolo", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carrerasdriftboonline", "jogosmobile": "juegosmóviles", "legendofneverland": "legendadeneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "cazadoresdetiempo", "gamingmobile": "juegosenmóvil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "losgatosdelabatalla", "dnd": "dnd", "quest": "misi<PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "mundoenlaclaridad", "travellerttrpg": "viajerottrpg", "2300ad": "2300dc", "larp": "larp", "romanceclub": "clubderomance", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "dungeonmisteriopokémon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "animepokemon", "pokémongo": "pokémongo", "pokemonred": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "<PERSON><PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipnóticos", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "charlas<PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON>leta", "pokemonpurpura": "pokemonpúrpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "peluchesp<PERSON><PERSON>", "teamystic": "equipomístico", "pokeball": "pokebola", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokémonbrillan<PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "man<PERSON>dehier<PERSON>", "kabutops": "kabutops", "psyduck": "psydog", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "maestropokémon", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "cazadorbrillante", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON><PERSON><PERSON>", "schaken": "schaken", "skak": "skak", "ajedres": "<PERSON><PERSON><PERSON><PERSON>", "chessgirls": "chicas<PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzmundial", "jeudéchecs": "jeudéchecs", "japanesechess": "ajedrezjaponés", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "ajedrezcanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "aperturas", "rook": "novato", "chesscom": "ajedrezcom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "mazmorrasydragones", "dungeonmaster": "maestrodeca<PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsydragones", "oxventure": "oxaventura", "darksun": "soltétrico", "thelegendofvoxmachina": "laleyendadevocemachina", "doungenoanddragons": "jugandodungeonsydragones", "darkmoor": "darkmoor", "minecraftchampionship": "campeonatominecraft", "minecrafthive": "mielocohive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "sueñosmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "llamadevela", "fru": "fru", "addons": "complementos", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodificado", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "entretierras", "minecraftdungeons": "dungeonsdeminecraft", "minecraftcity": "ciudadminecraft", "pcgamer": "jugadorpc", "jeuxvideo": "juegosvideo", "gambit": "gambito", "gamers": "jugones", "levelup": "subedenivel", "gamermobile": "gamermóvil", "gameover": "findeljuego", "gg": "gg", "pcgaming": "gamingenpc", "gamen": "jue<PERSON><PERSON><PERSON>", "oyunoynamak": "j<PERSON><PERSON>y<PERSON><PERSON>", "pcgames": "juegosdepc", "casualgaming": "juegosinformales", "gamingsetup": "configuracióndejuegos", "pcmasterrace": "maestronotebooks", "pcgame": "juegodepc", "gamerboy": "gamerchico", "vrgaming": "juegovr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gameres", "gameplays": "gameplays", "consoleplayer": "jugadordeconsolas", "boxi": "boxi", "pro": "pro", "epicgamers": "epicosgamers", "onlinegaming": "gamingenlínia", "semigamer": "semigamer", "gamergirls": "chicasgamer", "gamermoms": "mamisgamer", "gamerguy": "chicogamer", "gamewatcher": "observadorjuego", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "chicasgamer", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "equipointenso", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "misiones", "alax": "alaax", "avgn": "promedio", "oldgamer": "gamerviejo", "cozygaming": "gamingacogedor", "gamelpay": "gamelpay", "juegosdepc": "juegosdepc", "dsswitch": "cambiods", "competitivegaming": "juegocompetitivo", "minecraftnewjersey": "minecraftnuevajersey", "faker": "falso", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "gamingheterosexual", "gamepc": "juegopc", "girlsgamer": "chicasgamer", "fnfmods": "fnfmods", "dailyquest": "misióndiaria", "gamegirl": "chicadejuegos", "chicasgamer": "chicasgamer", "gamesetup": "preparandolajugada", "overpowered": "sobredimensionado", "socialgamer": "gamerensocial", "gamejam": "jamdejuegos", "proplayer": "proplayer", "roleplayer": "rolero", "myteam": "mi<PERSON><PERSON><PERSON>", "republicofgamers": "republicadejugadores", "aorus": "aorus", "cougargaming": "gamingdecougar", "triplelegend": "<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "amigosgamer", "butuhcewekgamers": "necesitochicasgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdju<PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamerinformal", "89squad": "89squad", "inicaramainnyagimana": "iniciaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "ver<PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "fiestalan", "videogamer": "videojugador", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationjugador", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "gamerdelcuaderno", "protogen": "protogen", "womangamer": "mujergamer", "obviouslyimagamer": "obviamenteunjugador", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forajido", "humanfallflat": "humanocaeplano", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "escapezero", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musicanintendo", "sonicthehedgehog": "sonicelerizo", "sonic": "sonico", "fallguys": "chicossaltadores", "switch": "camb<PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "spla2oon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "máscarademajora", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "abo<PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "niñosdel<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "vidamigos", "ahatintime": "unsombreroeneltiempo", "tearsofthekingdom": "lágrimasdel<PERSON><PERSON>", "walkingsimulators": "simuladoresdeandar", "nintendogames": "juegosdenintendo", "thelegendofzelda": "<PERSON>leg<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "lunaenlagranja", "mariobros": "mario<PERSON>s", "runefactory": "factoryderunas", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "respirarlasalvaje", "myfriendpedro": "mipriendopedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51juegos", "earthbound": "tierraadentro", "tales": "cuentos", "raymanlegends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochileno", "tloz": "tloz", "trianglestrategy": "triángulotáctico", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "lascastañassonmalas", "nintendos": "nintendos", "new3ds": "nuevo3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerrerosdehy<PERSON>le", "mariopartysuperstars": "mariopartysuperestrellas", "marioandsonic": "marioysonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendoguitos", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "yu<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "redcanids", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegends", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendssalvaje", "adcarry": "adcarry", "lolzinho": "lolsito", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendarias", "gaminglol": "gamernocreo", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hex<PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitejuego", "gamingfortnite": "jugandofortnite", "fortnitebr": "fortnitebr", "retrovideogames": "videojuegosretro", "scaryvideogames": "videojuegosdemiedo", "videogamemaker": "creadordev<PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videojuegos", "videosgame": "videosjuegos", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "batallaenelteatro", "arcades": "arcades", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simuladoragr<PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxespañol", "erlc": "erlc", "sanboxgames": "juegosensandbox", "videogamelore": "videogamelore", "rollerdrome": "rollerdrome", "parasiteeve": "parásitaveve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "simulador_<PERSON>_yandere", "grandtheftauto": "grandtheftauto", "deadspace": "espaciomuerte", "amordoce": "amordulce", "videogiochi": "videogames", "theoldrepublic": "laantiguarepública", "videospiele": "videojuegos", "touhouproject": "<PERSON>ye<PERSON><PERSON><PERSON><PERSON>", "dreamcast": "sueñocast", "adventuregames": "juegosdeaventura", "wolfenstein": "wolfenstein", "actionadventure": "aventuraacción", "storyofseasons": "historia<PERSON><PERSON><PERSON>", "retrogames": "retrogames", "retroarcade": "retroarcade", "vintagecomputing": "computaciónvintage", "retrogaming": "retrogaming", "vintagegaming": "jue<PERSON>vintage", "playdate": "citadejuego", "commanderkeen": "comandantekeen", "bugsnax": "bagnax", "injustice2": "injusticia2", "shadowthehedgehog": "sombraalerizo", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "vidazen", "beatmaniaiidx": "beatmaniaiidx", "steep": "empinado", "mystgames": "my<PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "gamingenblockchain", "medievil": "medievil", "consolegaming": "juegoconconsola", "konsolen": "konsolen", "outrun": "esquivar", "bloomingpanic": "pánicoenfloración", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horrorjuegos", "monstergirlquest": "monstergirlquest", "supergiant": "supergigante", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "simuladoresdeagricultura", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ficcióninteractiva", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "elúltimodenosotros2", "amantesamentes": "amantesmentales", "visualnovel": "novelavisual", "visualnovels": "novelasgráficas", "rgg": "rgg", "shadowolf": "sombrae<PERSON><PERSON>", "tcrghost": "tcrfantasmín", "payday": "díadepago", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "juegosestéticos", "novelavisual": "novelavisual", "thecrew2": "elgrupo2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolucióndelaspasadoras", "wiiu": "wiiu", "leveldesign": "diseñodeniveles", "starrail": "starrail", "keyblade": "espadaclave", "aplaguetale": "apl<PERSON>gue<PERSON>", "fnafsometimes": "fnafsiempre", "novelasvisuales": "novelasvisuales", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videojueguitos", "videogamedates": "citasvideojuegos", "mycandylove": "<PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "vuelvenlosreckoning", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON>deltent<PERSON><PERSON><PERSON>", "maniacmansion": "mansiónmaníaca", "crashracing": "carreracrazy", "3dplatformers": "plataformas3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "juegosenlospolvorientos", "hellblade": "cuchillodemoníaco", "storygames": "juegosdehistoria", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "jugadores", "offmortisghost": "fueraeles<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybunny": "pequeñitoconejito", "retroarch": "retroarch", "powerup": "potenciatupeor", "katanazero": "katanazero", "famicom": "famicon", "aventurasgraficas": "aventurasgráficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "tierraarrasada", "powerwashsim": "simuladordehydropulido", "coralisland": "isladecoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "otromundo", "metaquest": "metaquest", "animewarrios2": "guerrerosanime2", "footballfusion": "fusiónfutbol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON><PERSON>", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalretorcido", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "montóndevergüenza", "simulator": "simulador", "symulatory": "simulador", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "maravillastodavía", "skylander": "skylander", "boyfrienddungeon": "noviodecavernas", "toontownrewritten": "toontownreescrito", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "caosurbano", "heavenlybodies": "cuerposcelestiales", "seum": "seum", "partyvideogames": "videojuegosfiesteros", "graveyardkeeper": "cuidadordecementerios", "spaceflightsimulator": "simuladorvuelosespaciales", "legacyofkain": "leg<PERSON><PERSON><PERSON>", "hackandslash": "hackeydesliza", "foodandvideogames": "comidayvideojuegos", "oyunvideoları": "videosdebjuegos", "thewolfamongus": "elloboentrenosotros", "truckingsimulator": "simuladordecamiones", "horizonworlds": "horizonmundos", "handygame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "videojuegosoldschool", "racingsimulator": "simuladordecarreras", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentesdelcaos", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "puertasdelolimpocraft", "monsterhunternow": "cazadordemoniosahora", "rebelstar": "estrellarebelde", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "indievideogames", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "imbatible", "projectl": "proyectol", "futureclubgames": "juegosdelclubfuturo", "mugman": "taza", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "cienciadeaperturas", "backlog": "atraso", "gamebacklog": "listadejuegos", "gamingbacklog": "pendientesdegaming", "personnagejeuxvidéos": "personajesjuegosvideo", "achievementhunter": "cazadordelogros", "cityskylines": "paisajesurbanos", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "perritonaughty", "beastlord": "se<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "j<PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "rutakentuckycero", "oriandtheblindforest": "oriymelbosquedeciegos", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamina", "staxel": "staxel", "videogameost": "bandasonorasdevideojuegos", "dragonsync": "sincronizacertificado", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "meencantakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "p<PERSON><PERSON><PERSON>", "berserk": "bersek", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "caballerosdelzodiaco", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "iniciald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animetriste", "darkerthanblack": "másnegroquesombra", "animescaling": "escaladeanime", "animewithplot": "animeconhistoria", "pesci": "peces", "retroanime": "animevintage", "animes": "animes", "supersentai": "superpoderes", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "animedelos90", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "papitaelperformer", "masterpogi": "maestropog<PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonetemporada1", "rapanime": "rapanime", "chargemanken": "cargamancos", "animecover": "animecover", "thevisionofescaflowne": "lavisióndeescaflowne", "slayers": "cazadores", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "anima<PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "pezbanana", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "fuerzafuego", "moriartythepatriot": "moriartyelpatri<PERSON>", "futurediary": "diariodelfuturo", "fairytail": "cuento<PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON>enelabis<PERSON>", "parasyte": "parásito", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodíadelasirenitas", "kamisamakiss": "besodeldios", "blmanga": "blmanga", "horrormanga": "mangaespanto", "romancemangas": "mangasrománticos", "karneval": "carnaval", "dragonmaid": "dragonaes<PERSON><PERSON>", "blacklagoon": "lagunanegra", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsicop100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "perroscallejerosbungo", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "uníndicemágicoespecífico", "sao": "sao", "blackclover": "trébolnegro", "tokyoghoul": "tokyoghoul", "onepunchman": "ungolpeunhombre", "hetalia": "hetalia", "kagerouproject": "proyectokager<PERSON>", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8<PERSON><PERSON><PERSON>", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "espíasyfamilia", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioridadwonderegg", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosímico", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animededeportes", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagadelmalotanyabte", "shounenanime": "animejuvenil", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "elchicoyelbestia", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "comote<PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "lind<PERSON><PERSON><PERSON>", "martialpeak": "picoenmártires", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "chicadelpu<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerodós", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "chica<PERSON><PERSON>uo", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "marinerossaturno", "dio": "dio", "sailorpluto": "marinaplutón", "aloy": "aloy", "runa": "runa", "oldanime": "animesdeantes", "chainsawman": "chainsawman", "bungoustraydogs": "perrosperdidosenbungo", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "canastadef<PERSON>tas", "devilmancrybaby": "diablitollorababy", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "amavive", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "umibenoextranjero", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "elpaísdenuncajamás", "monstermanga": "monstronovela", "yourlieinapril": "tumentiraenabril", "buggytheclown": "buggyelpayaso", "bokunohero": "bokunoheroe", "seraphoftheend": "serapintodelfin", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magia", "deepseaprisoner": "prisionerodelaprofundeza", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mundode<PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "corazonespandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerradecomer", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "lineadelosdemonios", "toyoureternity": "tuyeternidad", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "periodoazul", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "borrado", "bluelock": "bloqueazul", "goblinslayer": "matagoblins", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "caballerosdelavampiro", "mugi": "mugi", "blueexorcist": "exorcistazul", "slamdunk": "mate", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "veo", "spyfamily": "familiaespía", "airgear": "airegear", "magicalgirl": "chicamágica", "thesevendeadlysins": "los7pecadosmortales", "prisonschool": "escueladeprisión", "thegodofhighschool": "eldiosdelcolegioaltos", "kissxsis": "be<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandeazul", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universodeanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "primerpaso", "undeadunluck": "nocheradirecta", "romancemanga": "mangadeamor", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonioslayertoaelsable", "bloodlad": "chicosdesangre", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "pu<PERSON>defuego", "adioseri": "adioserí", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "lasestrellassealine", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "magiacherry", "housekinokuni": "<PERSON><PERSON><PERSON>", "recordragnarok": "grabaragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "escueladevidasmuertas", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "saladeentrenamiento", "vindlandsaga": "sagadevindland", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "desfiledelamuerte", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponés", "animespace": "espacioanime", "girlsundpanzer": "chicasundpanzer", "akb0048": "akb0048", "hopeanuoli": "esperanzanoli", "animedub": "animeconvoz", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeindie", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "hombrerata", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "chicocato", "gashbell": "gashbell", "peachgirl": "chicadeldurazno", "cavalieridellozodiaco": "caballerosdelzodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "clubdelasperrasyarichin", "dragonquestdai": "dragónquestdai", "heartofmanga": "corazóndelmanga", "deliciousindungeon": "delicioso<PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordderagnarok", "funamusea": "diversiónamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "saltatealofe", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "sobrealimentado", "toriko": "<PERSON><PERSON>o", "ravemaster": "ma<PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierdelasquechas", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunoleón", "kamen": "kamen", "mangaislife": "elmangai<PERSON><PERSON><PERSON>", "dropsofgod": "gotasdegod", "loscaballerosdelzodia": "loscaballerosdelzodíaco", "animeshojo": "animeshojo", "reverseharem": "hareminvertido", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "granmaestroonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldado", "mybossdaddy": "mipapájefe", "gear5": "gear5", "grandbluedreaming": "sueñosgrandblue", "bloodplus": "sangreplus", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "sangreanime", "bloodc": "sangre", "talesofdemonsandgods": "cuentosdedemoniosydioses", "goreanime": "goreanime", "animegirls": "chicasanime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "cuervosxpeor", "splatteranime": "animepintoresco", "splatter": "sal<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "elaumentodelh<PERSON><PERSON>edeles<PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedateneída", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supertitulares", "animeidols": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiestabienconelmóvil", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "chicasmágicas", "callofthenight": "llamadaalanoche", "bakuganbrawler": "brawlerdebakugan", "bakuganbrawlers": "brawlersdebakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "sombrafeliz", "tsubasachronicle": "crónicatsubasa", "findermanga": "encuentramanga", "princessjellyfish": "princesajellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "besoenelparaiso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revistaestelar", "animeverse": "animeverso", "persocoms": "persocoms", "omniscientreadersview": "vistadelectoresomniscientes", "animecat": "gatoanime", "animerecommendations": "recomendacionesanime", "openinganime": "abriendolanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "miromanticacomediadeteens", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotsgigantes", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "gundam<PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekasiete", "eureka7": "eureka7", "thebigoanime": "elbigoanime", "bleach": "blanqueador", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventurasbizarrasdejojo", "fullmetalalchemist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "hielo", "jojobizarreadventures": "aventurasjojobizarre", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitar", "greenranger": "rangerverde", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salóndelman<PERSON>", "lupinthe3rd": "lupinthe3rd", "animecity": "ciudadanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "aventuradigimon", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "ca<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ataquealgigante", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "miheroeacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "<PERSON><PERSON><PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "cuerpodeencuestas", "onepieceanime": "onepieceanime", "attaquedestitans": "atacaalostitanes", "theonepieceisreal": "elonepieceesreal", "revengers": "vengadores", "mobpsycho": "mobpsico", "aonoexorcist": "anoexorcista", "joyboyeffect": "efectochicofeliz", "digimonstory": "digimonhistoria", "digimontamers": "digimontamers", "superjail": "superjail", "metalocalypse": "metalocalipsis", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "nuestroclubdehostes", "flawlesswebtoon": "webtoonperfecto", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "brujavoladora", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "soloporque", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "calletodosantos", "recuentosdelavida": "cuentodelavida"}