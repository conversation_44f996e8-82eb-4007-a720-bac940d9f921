{"2048": "2048", "mbti": "тыпасобы", "enneagram": "энэаграма", "astrology": "астралягія", "cognitivefunctions": "кагнітыўныяфункцыі", "psychology": "псіхалогія", "philosophy": "філасофія", "history": "гісторыя", "physics": "physics", "science": "навука", "culture": "культура", "languages": "languages", "technology": "тэхналогіі", "memes": "мемы", "mbtimemes": "мемыпамбті", "astrologymemes": "мемыастралёгіі", "enneagrammemes": "мемыэнэаграмы", "showerthoughts": "думкіпаддушам", "funny": "смешнае", "videos": "відэа", "gadgets": "гаджэты", "politics": "палітыка", "relationshipadvice": "парадыпаадносінах", "lifeadvice": "жыццёвыяпарады", "crypto": "крипта", "news": "навіны", "worldnews": "сусветныянавіны", "archaeology": "археалогія", "learning": "learning", "debates": "debates", "conspiracytheories": "тэорыізагавору", "universe": "ўсесвет", "meditation": "медытацыя", "mythology": "міфалогія", "art": "мастацтва", "crafts": "crafts", "dance": "тан<PERSON><PERSON>", "design": "design", "makeup": "мак<PERSON><PERSON><PERSON>", "beauty": "прыгажосць", "fashion": "fashion", "singing": "спевы", "writing": "writing", "photography": "photography", "cosplay": "косплэй", "painting": "painting", "drawing": "drawing", "books": "кнігі", "movies": "movies", "poetry": "poetry", "television": "тэлебачанне", "filmmaking": "кінематаграфія", "animation": "анімацыя", "anime": "анімэ", "scifi": "фантастыка", "fantasy": "фэнтэзі", "documentaries": "дакументальныяфільмы", "mystery": "mystery", "comedy": "comedy", "crime": "крым<PERSON>нал", "drama": "драма", "bollywood": "балівуд", "kdrama": "кдрама", "horror": "ужасы", "romance": "меладрама", "realitytv": "рэалі<PERSON><PERSON><PERSON><PERSON>у", "action": "боевікі", "music": "музыка", "blues": "блюз", "classical": "кла<PERSON><PERSON>чнаямузыка", "country": "кантры", "desi": "індыйская", "edm": "эдм", "electronic": "электроннаямузыка", "folk": "фолк", "funk": "фанк", "hiphop": "хі<PERSON>хоп", "house": "<PERSON><PERSON><PERSON><PERSON>", "indie": "індзі", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "кпоп", "latin": "лацінская", "metal": "метал", "pop": "поп", "punk": "панк", "rnb": "рнб", "rap": "рэп", "reggae": "рэгі", "rock": "рок", "techno": "тэхна", "travel": "travel", "concerts": "concerts", "festivals": "фестывалі", "museums": "музеі", "standup": "стандап", "theater": "тэатр", "outdoors": "outdoors", "gardening": "gardening", "partying": "partying", "gaming": "геймінг", "boardgames": "настольныягульні", "dungeonsanddragons": "падземельяідраконы", "chess": "шахматы", "fortnite": "фортнайт", "leagueoflegends": "лігалегенд", "starcraft": "старкр<PERSON><PERSON>т", "minecraft": "майнкра<PERSON>т", "pokemon": "покемоны", "food": "food", "baking": "выпечка", "cooking": "cooking", "vegetarian": "вегетарыянства", "vegan": "веганства", "birds": "птушкі", "cats": "коткі", "dogs": "сабакі", "fish": "рыбы", "animals": "animals", "blacklivesmatter": "жыццёчорныхмаезначэнне", "environmentalism": "экалагічныактывізм", "feminism": "фемінізм", "humanrights": "правычалавека", "lgbtqally": "лгбт", "stopasianhate": "спыніцьазіяцкуюненавісць", "transally": "транссэксуальнасць", "volunteering": "volunteering", "sports": "спорт", "badminton": "бад<PERSON><PERSON>нтон", "baseball": "бэйсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крыкет", "cycling": "веласпорт", "fitness": "фітнес", "football": "футбол", "golf": "гольф", "gym": "трэнажорнаязала", "gymnastics": "гімнастыка", "hockey": "хак<PERSON><PERSON>", "martialarts": "баявыямайстэрствы", "netball": "нэтбол", "pilates": "пілатэс", "pingpong": "настольнытэніс", "running": "бег", "skateboarding": "скейтбординг", "skiing": "горныялыжы", "snowboarding": "сноўборд", "surfing": "серфінг", "swimming": "плаванне", "tennis": "тэніс", "volleyball": "валейбол", "weightlifting": "цяжкаяатлетыка", "yoga": "йога", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "паходы", "capricorn": "козераг", "aquarius": "вадалей", "pisces": "рыбы", "aries": "авен", "taurus": "ця<PERSON><PERSON><PERSON>", "gemini": "блізняты", "cancer": "рак", "leo": "леў", "virgo": "дзева", "libra": "вагі", "scorpio": "скорпіён", "sagittarius": "стралец", "shortterm": "shortterm", "casual": "casual", "longtermrelationship": "longtermrelationship", "single": "single", "polyamory": "polyamory", "enm": "enm", "lgbt": "лгбт", "lgbtq": "лг<PERSON><PERSON>к", "gay": "гей", "lesbian": "лесбіянка", "bisexual": "бісексуальны", "pansexual": "пансэксуал", "asexual": "асексуальны", "reddeadredemption2": "чырвоныдпадзел2", "dragonage": "драконячывек", "assassinscreed": "асассінскрыд", "saintsrow": "святыярадкі", "danganronpa": "данганронпа", "deltarune": "дэл<PERSON><PERSON><PERSON><PERSON>н", "watchdogs": "глядзізаімі", "dislyte": "ди<PERSON><PERSON><PERSON><PERSON>т", "rougelikes": "rougelikes", "kingsquest": "каралеўскаепадарожжа", "soulreaver": "душачоўнік", "suikoden": "суікодэн", "subverse": "субверс", "legendofspyro": "легендаспайра", "rouguelikes": "руґлікі", "syberia": "сіберыя", "rdr2": "rdr2", "spyrothedragon": "спайрадракон", "dragonsdogma": "драконыдоказы", "sunsetoverdrive": "заходсонцавога", "arkham": "аркхэм", "deusex": "дэўсэкс", "fireemblemfates": "агоньзнаменіфэйтс", "yokaiwatch": "ёкайг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>к", "rocksteady": "рокстэдзі", "litrpg": "літрпг", "haloinfinite": "гала<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "guildwars": "гільдывайнары", "openworld": "адкрытысвят", "heroesofthestorm": "героішторму", "cytus": "цытус", "soulslike": "soulslike", "dungeoncrawling": "доследваннепадзямелля", "jetsetradio": "джэтсетрадыё", "tribesofmidgard": "плямёнымідугарда", "planescape": "плянавычка", "lordsoftherealm2": "лордызадзяржавы2", "baldursgate": "бальдурсврата", "colorvore": "колерадуга", "medabots": "медаботы", "lodsoftherealm2": "лодыўсвяткоў2", "patfofexile": "патфофэкзыль", "immersivesims": "іммерсіўныясімсы", "okage": "окаге", "juegoderol": "ролеваягра", "witcher": "відзьмак", "dishonored": "пазбаўлены", "eldenring": "эльденига", "darksouls": "цёмныядушы", "kotor": "катор", "wynncraft": "вінкрафт", "witcher3": "відьмак3", "fallout": "фал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout3": "фоллаўт3", "fallout4": "фаллаутапатруль4", "skyrim": "скайрым", "elderscrolls": "старыяскролы", "modding": "мадд<PERSON><PERSON>г", "charactercreation": "стварэннеперсанажаў", "immersive": "імэрсивнае", "falloutnewvegas": "фаллаутавагас", "bioshock": "бíoshock", "omori": "оморы", "finalfantasyoldschool": "фінальнаяфантазіяўшкола", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "фінальнаяфантазія", "finalfantasy14": "фінальныфантазія14", "finalfantasyxiv": "финальнаяфантазія14", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "фінальнаяфантазіяматоя", "lalafell": "лала<PERSON>елл", "dissidia": "дисидыя", "finalfantasy7": "фінальнаефантазія7", "ff7": "ff7", "morbidmotivation": "мяртвыяістоты", "finalfantasyvii": "фінальнаяфантазіяvii", "ff8": "фф8", "otome": "отамэ", "suckerforlove": "забіўпачуццёвзаках", "otomegames": "отамегеймс", "stardew": "стардью", "stardewvalley": "стардўвальлі", "ocarinaoftime": "окарыначасу", "yiikrpg": "йіікрпг", "vampirethemasquerade": "вампірскаямаскарад", "dimension20": "памер20", "gaslands": "газавыязямлі", "pathfinder": "пада<PERSON><PERSON><PERSON><PERSON><PERSON>к", "pathfinder2ndedition": "шляхчыка2еўдванне", "shadowrun": "ценьперадача", "bloodontheclocktower": "кроўнагадзіннікавойвежы", "finalfantasy15": "канчатковаяфантазія15", "finalfantasy11": "фінальнаяфантазія11", "finalfantasy8": "фінальнаефантазія8", "ffxvi": "ffxvi", "lovenikki": "лавнікі", "drakengard": "дракенгарда", "gravityrush": "гравіта<PERSON>ыйныбум", "rpg": "рпг", "dota2": "дота2", "xenoblade": "ксенаблейд", "oneshot": "адзінышот", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "наглядчык", "yourturntodie": "твойчаргадоляць", "persona3": "персона3", "rpghorror": "жахад<PERSON><PERSON><PERSON><PERSON>г", "elderscrollsonline": "старшыяскроліонлайн", "reka": "рэка", "honkai": "хонкай", "marauders": "мараудэры", "shinmegamitensei": "шінмегамітэнсэй", "epicseven": "эпіксімсем", "rpgtext": "rpgтэкст", "genshin": "ген<PERSON>ин", "eso": "есо", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "мораўінд", "starwarskotor": "зорныявайныкотор", "demonsouls": "дэмончыядушы", "mu": "му", "falloutshelter": "пастаноўкаадзіноты", "gurps": "гурпс", "darkestdungeon": "самыцямнічышахта", "eclipsephase": "этапзатемнення", "disgaea": "дисгая", "outerworlds": "знешніясветы", "arpg": "арпг", "crpg": "крпг", "bindingofisaac": "звязваннеісаака", "diabloimmortal": "дзіябалбяссмертны", "dynastywarriors": "дынастыіваіны", "skullgirls": "чэрвякабаброў", "nightcity": "начныгорад", "hogwartslegacy": "гарызонтспадчыны", "madnesscombat": "божаняябарьба", "jaggedalliance2": "зубчастаеадзіноцтва2", "neverwinter": "ніколічыстайна", "road96": "дарога96", "vtmb": "втмб", "chimeraland": "хімераземельле", "homm3": "гомм3", "fe3h": "фе3г", "roguelikes": "роглайкі", "gothamknights": "гота́мскіярыцары", "forgottenrealms": "забытыясветы", "dragonlance": "драконячаяпалачка", "arenaofvalor": "арэнаперамогі", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "тунктаўн", "childoflight": "дзіцясвятла", "aq3d": "aq3d", "mogeko": "mogu<PERSON><PERSON>", "thedivision2": "дывізія2", "lineage2": "lineage2", "digimonworld": "дзігімонсвет", "monsterrancher": "монстар<PERSON>анчер", "ecopunk": "экапанк", "vermintide2": "вермінтайд2", "xeno": "ксенастарабака", "vulcanverse": "вулканаверс", "fracturedthrones": "разбураныятроны", "horizonforbiddenwest": "гарызонтзабароненымзахадзе", "twewy": "т<PERSON><PERSON>", "shadowpunk": "шадоўпанк", "finalfantasyxv": "фінальныфантазія15", "everoasis": "эвер<PERSON><PERSON><PERSON><PERSON>", "hogwartmystery": "гариотаргадка", "deltagreen": "дэлтагрын", "diablo": "дз<PERSON><PERSON><PERSON>ла", "diablo3": "диабло3", "diablo4": "діябла4", "smite": "смажыць", "lastepoch": "апошніяэпохі", "starfinder": "знайдзізоркі", "goldensun": "залатысонца", "divinityoriginalsin": "боскаяарыгінальнагрэха", "bladesinthedark": "лязоўуцянікце", "twilight2000": "твілайт2000", "sandevistan": "сандэвістан", "cyberpunk": "к<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нк", "cyberpunk2077": "кіберпанк2077", "cyberpunkred": "кіберпанкчырвоны", "dragonballxenoverse2": "драгонбалксеноверс2", "fallenorder": "звал<PERSON>л<PERSON>парадак", "finalfantasyxii": "фінальныфантаз<PERSON>йхіі", "evillands": "злыязямлі", "genshinimact": "genshinimact", "aethyr": "эйтэр", "devilsurvivor": "дя<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ццё", "oldschoolrunescape": "старша<PERSON>ласнікібегу", "finalfantasy10": "финальнаяфантазія10", "anime5e": "анімэ5е", "divinity": "боскасць", "pf2": "pf2", "farmrpg": "фармрпг", "oldworldblues": "старыясветблуз", "adventurequest": "падарожжаўпошук", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "ролеваеігры", "roleplayinggames": "рольавыягульні", "finalfantasy9": "финалфантазія9", "sunhaven": "сонечнаеўрадзіва", "talesofsymphonia": "казкісимфоніі", "honkaistarrail": "хонкайста<PERSON><PERSON><PERSON><PERSON>л", "wolong": "валонг", "finalfantasy13": "финальнаяфантазія13", "daggerfall": "даггерфол", "torncity": "разабранаеместа", "myfarog": "май<PERSON>агог", "sacredunderworld": "сакрэтнысусвет", "chainedechoes": "ланцугавыяадглушкі", "darksoul": "цёмнаядуша", "soulslikes": "душышлюзкі", "othercide": "іншыкід", "mountandblade": "горыімечы", "inazumaeleven": "іназумалевен", "acvalhalla": "аквалхала", "chronotrigger": "хранатрынгера", "pillarsofeternity": "святыніадзямлі", "palladiumrpg": "паладзіумrpg", "rifts": "разрывы", "tibia": "тубія", "thedivision": "дзілення", "hellocharlotte": "прывітаннешарлотка", "legendofdragoon": "легендаадракона", "xenobladechronicles2": "ксенаблейдхронікі2", "vampirolamascarada": "вампіроламаскарада", "octopathtraveler": "октапафтападарожнік", "afkarena": "афкарына", "werewolftheapocalypse": "ваўкалакапаслякатастрофы", "aveyond": "авеёнд", "littlewood": "маленькілес", "childrenofmorta": "дзіціморты", "engineheart": "душа<PERSON>а<PERSON>ыны", "fable3": "фэйбл3", "fablethelostchapter": "хвасткicieзгубленайглавы", "hiveswap": "пчаліперамога", "rollenspiel": "ролеплэй", "harpg": "гарпг", "baldursgates": "балдурсгейт", "edeneternal": "еданавечнасць", "finalfantasy16": "фінальныфэнтэзі16", "andyandleyley": "андз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>і", "ff15": "ff15", "starfield": "зорнаеполе", "oldschoolrevival": "стараяшколаадраджэнне", "finalfantasy12": "фінальнаяфантазія12", "ff12": "ff12", "morkborg": "моркборг", "savageworlds": "савяршэнныясветы", "diabloiv": "diabloiv", "pve": "пве", "kingdomheart1": "крындомасярд1", "ff9": "фф9", "kingdomheart2": "каралёўсэрца2", "darknessdungeon": "цямніцытмы", "juegosrpg": "игрыпартнёрскі", "kingdomhearts": "kingdomhearts", "kingdomheart3": "королевствасердец3", "finalfantasy6": "фінальнаяфантазія6", "ffvi": "ффві", "clanmalkavian": "кланмалькавій", "harvestella": "гарвестэла", "gloomhaven": "глумахаўэн", "wildhearts": "дзікіясэрцы", "bastion": "ба<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakarochdemoner": "дракарохдэмонер", "skiesofarcadia": "небісаркадыі", "shadowhearts": "блізкакроўныя", "nierreplicant": "ніррэплікант", "gnosia": "гносія", "pennyblood": "пеніка<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "подыхагніў4", "mother3": "матка3", "cyberpunk2020": "кіберпанк2020", "falloutbos": "фаллаўтбос", "anothereden": "іншаяэдэна", "roleplaygames": "гульн<PERSON><PERSON><PERSON>", "roleplaygame": "хатагульня", "fabulaultima": "фабулаультіма", "witchsheart": "вадзяны_душы", "harrypottergame": "грайпотэра", "pathfinderrpg": "пошукавецrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "вампірскаямаскарад", "dračák": "дра<PERSON><PERSON>к", "spelljammer": "спелджаммер", "dragonageorigins": "драконароджаныя", "chronocross": "кронокрос", "cocttrpg": "кокттрпг", "huntroyale": "паляваннекаралеўства", "albertodyssey": "альбертадэсей", "monsterhunterworld": "монстрпаляўнічыхсвет", "bg3": "bg3", "xenogear": "ксенагеар", "temtem": "тэмтэм", "rpgforum": "rpgфорум", "shadowheartscovenant": "ценявоесякравальнае", "bladesoul": "блейддуша", "baldursgate3": "балдурсгейт3", "kingdomcome": "краінапридзення", "awplanet": "аўпланета", "theworldendswithyou": "святанасцярогаемнавашайруцэ", "dragalialost": "драгаляйалост", "elderscroll": "старшынскіскролл", "dyinglight2": "загасвету2", "finalfantasytactics": "канчатковыяфантазіітактыкі", "grandia": "грандзія", "darkheresy": "цёмнаеўдапатэўство", "shoptitans": "шоптытаны", "forumrpg": "форумрпг", "golarion": "гала<PERSON><PERSON><PERSON>н", "earthmagic": "землянаямагія", "blackbook": "чорнаякніга", "skychildrenoflight": "небазіядзеткаўсвятла", "gryrpg": "грырпг", "sacredgoldedition": "святогазолатавыданне", "castlecrashers": "замакразбойнікі", "gothicgame": "готыкаигра", "scarletnexus": "скарлетнексус", "ghostwiretokyo": "прывідныаштокі", "fallout2d20": "фоллаут2д20", "gamingrpg": "геймерскіrpg", "prophunt": "прахаўт", "starrails": "зораспецы", "cityofmist": "горадтуману", "indierpg": "індыйскі_рпг", "pointandclick": "клікнііскажані", "emilyisawaytoo": "емілінавабраная", "emilyisaway": "эміліадсутнічае", "indivisible": "недзельны", "freeside": "фрі<PERSON><PERSON><PERSON>д", "epic7": "эпік7", "ff7evercrisis": "ff7вечныкрызіс", "xenogears": "ксенагіры", "megamitensei": "мега<PERSON><PERSON>тан<PERSON><PERSON>й", "symbaroum": "сімбарум", "postcyberpunk": "посткіберпанк", "deathroadtocanada": "смерцьнадарогаўканады", "palladium": "палад<PERSON><PERSON>ум", "knightjdr": "рыцарьд<PERSON>р", "monsterhunter": "паляўнічынамонстраў", "fireemblem": "агоньсімвалаў", "genshinimpact": "гэншынаўплыў", "geosupremancy": "геасупрэмасія", "persona5": "персона5", "ghostoftsushima": "привідцуцушыма", "sekiro": "секиро", "monsterhunterrise": "паляванненамонстраў", "nier": "ніер", "dothack": "дотхак", "ys": "яс", "souleater": "ду<PERSON><PERSON><PERSON><PERSON>б", "fatestaynight": "фатэстэйнайт", "etrianodyssey": "этріанодзія", "nonarygames": "ненаўрэнкігульні", "tacticalrpg": "тактычныrpg", "mahoyo": "махойо", "animegames": "анімешныягульні", "damganronpa": "дамганронпа", "granbluefantasy": "гранблюфантазія", "godeater": "гадзятнік", "diluc": "дылук", "venti": "венты", "eternalsonata": "вечнысонат", "princessconnect": "прынцэсаканект", "hexenzirkel": "вядзьмарскікружок", "cristales": "крышталі", "vcs": "вцс", "pes": "пес", "pocketsage": "кашалькавыяахвяры", "valorant": "валара<PERSON>т", "valorante": "валара<PERSON>т", "valorantindian": "валара<PERSON><PERSON><PERSON>н<PERSON>ус", "dota": "дота", "madden": "мадэн", "cdl": "цдл", "efootbal": "эфутбол", "nba2k": "нба2к", "egames": "егры", "fifa23": "фіфа23", "wwe2k": "wwe2k", "esport": "кіберспорт", "mlg": "mlink", "leagueofdreamers": "лігазаснавальнікаў", "fifa14": "fifa14", "midlaner": "мі<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "эфутбол", "dreamhack": "дрымхак", "gaimin": "гай<PERSON><PERSON>н", "overwatchleague": "лі<PERSON><PERSON><PERSON><PERSON>", "cybersport": "кіберспорт", "crazyraccoon": "божаябратка", "test1test": "тэст1тэст", "fc24": "фк24", "riotgames": "riotgames", "eracing": "еразыночкі", "brasilgameshow": "бразілгеймшоў", "valorantcompetitive": "валарантканкурэнтны", "t3arena": "t3арэна", "valorantbr": "валерантбрат", "csgo": "ксго", "tf2": "тф2", "portal2": "портал2", "halflife": "палавінажыцця", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "запор", "portal": "партал", "teamfortress2": "камандакрепасці2", "everlastingsummer": "вечнаеулеташчыкі", "goatsimulator": "козасімулятар", "garrysmod": "гаррісмод", "freedomplanet": "свабодапланета", "transformice": "трансфарміс", "justshapesandbeats": "простафігурыірытмы", "battlefield4": "паляванне4", "nightinthewoods": "ночыўлісах", "halflife2": "палавіннаежыцця2", "hacknslash": "хакі<PERSON>р<PERSON><PERSON><PERSON><PERSON>чы", "deeprockgalactic": "глыбокакашмарыгалактыкі", "riskofrain2": "рызыкадожджу2", "metroidvanias": "метроидвании", "overcooked": "перапёкся", "interplanetary": "міжпланетны", "helltaker": "пакутнік", "inscryption": "інскрыпцыя", "7d2d": "7д2д", "deadcells": "бязжыццёвыяклеткі", "nierautomata": "ніераўтамата", "gmod": "gmod", "dwarffortress": "дворфкрэпасць", "foxhole": "лямбрынт", "stray": "блудны", "battlefield": "бітва", "battlefield1": "полебою1", "swtor": "swtор", "fallout2": "фоллаут2", "uboat": "убоат", "eyeb": "эйб", "blackdesert": "чорныпустэльня", "tabletopsimulator": "табліцысимулятар", "partyhard": "крутаперажыць", "hardspaceshipbreaker": "цяжкікосмічныразбіўнік", "hades": "хадэс", "gunsmith": "збройнік", "okami": "окамі", "trappedwithjester": "заблакаванызжэстэрам", "dinkum": "дзіўна", "predecessor": "папярэднік", "rainworld": "дожджаўсвеце", "cavesofqud": "пячорыкуд", "colonysim": "калоніўсім", "noita": "ноіта", "dawnofwar": "ранаквайны", "minionmasters": "мін<PERSON>ёнмайстры", "grimdawn": "жахлівыранадзіўся", "darkanddarker": "цёмныіпацяюнні", "motox": "мотах", "blackmesa": "чорнаямеса", "soulworker": "душаўпрацоўшчык", "datingsims": "сімулятарызнаёмстваў", "yaga": "яга", "cubeescape": "кабячанаяўцякчка", "hifirush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "свенкуп", "newcity": "новыгорад", "citiesskylines": "гарадскіяпейзажы", "defconheavy": "дэфконцяжкі", "kenopsia": "кенопсія", "virtualkenopsia": "віртуальнаякенопсія", "snowrunner": "снягавыбегун", "libraryofruina": "бібліятэкабурына", "l4d2": "l4d2", "thenonarygames": "нанараваныяігры", "omegastrikers": "омегастрайкеры", "wayfinder": "шляхапошук", "kenabridgeofspirits": "кенабрыдждухаў", "placidplasticduck": "спакойныпластыкавыкачка", "battlebit": "бітв<PERSON><PERSON><PERSON>т", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "декламагород", "smileforme": "усміхнісядляменя", "catnight": "котаночь", "supermeatboy": "супермясныхлопчык", "tinnybunny": "тінічказаяц", "cozygrove": "камфортныбераг", "doom": "дойм", "callofduty": "каллдуты", "callofdutyww2": "свацьбайнбамww2", "rainbow6": "райдужныя6", "apexlegends": "апекслегенды", "cod": "код", "borderlands": "гран<PERSON><PERSON>ы", "pubg": "пабг", "callofdutyzombies": "клічбагатыхзомбі", "apex": "апекс", "r6siege": "r6сцяжэнне", "megamanx": "мегаменх", "touhou": "тэхо", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "farc<PERSON><PERSON>гры", "paladins": "паладзіны", "earthdefenseforce": "абароназямлі", "huntshowdown": "пабоявонне", "ghostrecon": "гострэкон", "grandtheftauto5": "грандкрадзяжаўаўта5", "warz": "вайна", "sierra117": "сьера117", "dayzstandalone": "дзённ<PERSON>кстабільна", "ultrakill": "ультракіл", "joinsquad": "прыходзьукаманду", "echovr": "эхавр", "discoelysium": "дискоэлізіум", "insurgencysandstorm": "бунтмясам<PERSON>урайс", "farcry3": "farcry3", "hotlinemiami": "хатлінамаямі", "maxpayne": "макспэйн", "hitman3": "хітмэн3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "смярцязвязь", "b4b": "b4b", "codwarzone": "кодаваянна", "callofdutywarzone": "заклікбойзалініяй", "codzombies": "кодызомбі", "mirrorsedge": "забяспечанаямясцовасць", "divisions2": "дзелы2", "killzone": "зоназабойцаў", "helghan": "хелган", "coldwarzombies": "халодныявайнызомбі", "metro2033": "метро2033", "metalgear": "мета<PERSON><PERSON><PERSON>р", "acecombat": "асобнаебітва", "crosscode": "кроскода", "goldeneye007": "залатызрок007", "blackops2": "чорныяаперацыі2", "sniperelite": "снайперэліт", "modernwarfare": "сучаснаявайна", "neonabyss": "нэйанавыдубляж", "planetside2": "планетабок2", "mechwarrior": "мехвайнер", "boarderlands": "бардэр<PERSON>андсы", "owerwatch": "оверва<PERSON><PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "выбегзтaркова", "metalslug": "металслуг", "primalcarnage": "прымальнаеўтрапленне", "worldofwarships": "святасубмарын", "back4blood": "back4blood", "warframe": "вайнатэкі", "rainbow6siege": "райбон6сэйдж", "xcom": "ксом", "hitman": "наёмнызабойца", "masseffect": "масавыэфект", "systemshock": "сістэмнышок", "valkyriachronicles": "валкiрчыяхронiкi", "specopstheline": "спецаперацыядлялініі", "killingfloor2": "кіллінгфлор2", "cavestory": "каветнаягандля", "doometernal": "домунавечна", "centuryageofashes": "стагоддзепопелу", "farcry4": "farcry4", "gearsofwar": "шасцярнівайнароў", "mwo": "мво", "division2": "двараздзел", "tythetasmaniantiger": "тытасманійскітыгр", "generationzero": "пакаленненуль", "enterthegungeon": "увойдзівпадвал", "jakanddaxter": "яканддэкстар", "modernwarfare2": "сучаснаявайна2", "blackops1": "чорныяаперацыі1", "sausageman": "касачкамен", "ratchetandclank": "ratchetandclank", "chexquest": "чэксквэст", "thephantompain": "фантомнаяболь", "warface": "ваеннаяасоба", "crossfire": "кросфайр", "atomicheart": "атамнаесэрца", "blackops3": "чорныяаперацыі3", "vampiresurvivors": "вампірскіявыжывальнікі", "callofdutybatleroyale": "калхазванніваенныбасевы", "moorhuhn": "мур<PERSON>ун", "freedoom": "свабода", "battlegrounds": "бітвы", "frag": "фраг", "tinytina": "малечка", "gamepubg": "гульняпубг", "necromunda": "некрамунда", "metalgearsonsoflibert": "металгерсысноўсвабоды", "juegosfps": "гульніфпс", "convertstrike": "канвертстрайк", "warzone2": "вайна2", "shatterline": "шатарскрыжаль", "blackopszombies": "чорныяаперацыізомбі", "bloodymess": "кроўявыбеспарадкаў", "republiccommando": "рэспубліканскікамандос", "elitedangerous": "элитныянебяспекі", "soldat": "салдат", "groundbranch": "граўндбрандч", "squad": "каманда", "destiny1": "доля1", "gamingfps": "геймінгфпс", "redfall": "чырвонызбег", "pubggirl": "pubggirl", "worldoftanksblitz": "светтанкаўбліц", "callofdutyblackops": "клічбоючыхцямчорныаперацыі", "enlisted": "зап<PERSON>саны", "farlight": "фар<PERSON><PERSON><PERSON><PERSON>т", "farcry5": "дальніякрызіс5", "farcry6": "farcry6", "farlight84": "фартлайт84", "splatoon3": "сплатун3", "armoredcore": "бронакрапка", "pavlovvr": "павлаўвір", "xdefiant": "xdefiant", "tinytinaswonderlands": "маленькіяцудоўнасцітынытыны", "halo2": "хало2", "payday2": "деньзарплаты2", "cs16": "cs16", "pubgindonesia": "пубгінданезія", "pubgukraine": "pubgўкраіна", "pubgeu": "пабгеў", "pubgczsk": "pubgczsk", "wotblitz": "вотбліц", "pubgromania": "pubgроманія", "empyrion": "эмп<PERSON>р<PERSON><PERSON>н", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "титанфол2", "soapcod": "сапункод", "ghostcod": "привидкоды", "csplay": "csplay", "unrealtournament": "нерэальнытурнір", "callofdutydmz": "клічдутымдмз", "gamingcodm": "геймінгкодавм", "borderlands2": "кліматысвятлоўмезіборарданд2", "counterstrike": "контрнаступленне", "cs2": "cs2", "pistolwhip": "пісталетаматака", "callofdutymw2": "клічдутымв2", "quakechampions": "землятрусшампіёны", "halo3": "halo3", "halo": "геласвет", "killingfloor": "заб<PERSON>райпаверхню", "destiny2": "доля2", "exoprimal": "экспраймаль", "splintercell": "раздробленаяячэка", "neonwhite": "неанбелы", "remnant": "рэшткі", "azurelane": "азурнышлях", "worldofwar": "святасвяржэння", "gunvolt": "ганвольт", "returnal": "возвратны", "halo4": "halo4", "haloreach": "халосягаць", "shadowman": "сцэнаўшка", "quake2": "квак2", "microvolts": "мікравольты", "reddead": "чырвонымёртвец", "standoff2": "стэндоўф2", "harekat": "герекат", "battlefield3": "бітваполя3", "lostark": "lostark", "guildwars2": "гільдываярскі2", "fallout76": "фаллаута76", "elsword": "эльсворд", "seaofthieves": "моразваротаў", "rust": "іржаўчына", "conqueronline": "канкверо<PERSON><PERSON><PERSON><PERSON>н", "dauntless": "бессмяротны", "warships": "ваенныякараблі", "dayofdragons": "днёмдраконаў", "warthunder": "вартадождж", "flightrising": "адкрывальніксамалётаў", "recroom": "рэкрум", "legendsofruneterra": "легендырунетэры", "pso2": "псо2", "myster": "myster", "phantasystaronline2": "фантастычныстаронлайн2", "maidenless": "безмядзіпара", "ninokuni": "нінокуні", "worldoftanks": "светтанкаў", "crossout": "кросаут", "agario": "агарыа", "secondlife": "другойжыцця", "aion": "айон", "toweroffantasy": "башня<PERSON>ан<PERSON><PERSON><PERSON><PERSON>й", "netplay": "нетплэй", "everquest": "эверквест", "metin2": "metin2", "gtaonline": "gtaон<PERSON><PERSON><PERSON>н", "ninokunicrossworld": "нінакунікросссвет", "reddeadonline": "чырванакровіонлайн", "superanimalroyale": "супержывёласуперматч", "ragnarokonline": "раганароконлайн", "knightonline": "рыцарыонлайн", "gw2": "gw2", "tboi": "тбой", "thebindingofisaac": "звязваннеісаака", "dragonageinquisition": "драконіянадзвычайнасць", "codevein": "кодавоіна", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпінгвін", "lotro": "лотро", "wakfu": "вакафу", "scum": "скот", "newworld": "новысвет", "blackdesertonline": "чорныпусткаонлайн", "multiplayer": "мультіплеер", "pirate101": "пірят101", "honorofkings": "шанакаралёў", "fivem": "пяцьпяць", "starwarsbattlefront": "старыявайныбатлфронт", "karmaland": "кармалады", "ssbu": "ссбу", "starwarsbattlefront2": "зорныявайныбітванафронце2", "phigros": "фігрос", "mmo": "ммо", "pokemmo": "покеммо", "ponytown": "понiта<PERSON>н", "3dchat": "3дчата", "nostale": "ноствэй", "tauriwow": "таурывоу", "wowclassic": "ваукла<PERSON><PERSON>к", "worldofwarcraft": "святамаг<PERSON>йвайну", "warcraft": "вайнусвет", "wotlk": "вотлк", "runescape": "рунескэйп", "neopets": "неапэты", "moba": "моба", "habbo": "habbo", "archeage": "арке<PERSON>дж", "toramonline": "toramonline", "mabinogi": "мабіногі", "ashesofcreation": "попелстварання", "riotmmo": "райотммо", "silkroad": "шаўковышлях", "spiralknights": "спіральныярыцары", "mulegend": "мулегенд", "startrekonline": "стармазаконтакт", "vindictus": "відшчыпнік", "albiononline": "альб<PERSON>ё<PERSON><PERSON>н<PERSON><PERSON><PERSON>н", "bladeandsoul": "клічыедушы", "evony": "евоні", "dragonsprophet": "драконаўпраповеднік", "grymmo": "грымма", "warmane": "ўзняцце", "multijugador": "мультыплэер", "angelsonline": "анёлыо<PERSON><PERSON><PERSON><PERSON>н", "lunia": "луня", "luniaz": "<PERSON>у<PERSON><PERSON><PERSON><PERSON>", "idleon": "ай<PERSON><PERSON><PERSON>н", "dcuniverseonline": "dcuniverseonline", "growtopia": "гратопія", "starwarsoldrepublic": "зорныяваеныстарычнайрэспублікі", "grandfantasia": "грандыфантазія", "blueprotocol": "блакiтныпротакол", "perfectworld": "ідэальнысвет", "riseonline": "узрастацеманлайн", "corepunk": "кортпанк", "adventurequestworlds": "прыгодакаштоўнасцямир", "flyforfun": "лётдлязадавальнення", "animaljam": "жывёлаклуб", "kingdomofloathing": "каралеўстваадчаювання", "cityofheroes": "гарадгерояў", "mortalkombat": "мартакаманта", "streetfighter": "вуліцыбояц", "hollowknight": "пустошнырыцарь", "metalgearsolid": "мет<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "forhonor": "заўзятасць", "tekken": "теккен", "guiltygear": "вінуватыяшкі", "xenoverse2": "ксенавэрс2", "fgc": "фгц", "streetfighter6": "streetfighter6", "multiversus": "мультыверсус", "smashbrosultimate": "смасшбраслтімум", "soulcalibur": "соўлкалiбур", "brawlhalla": "браўхала", "virtuafighter": "віртуальныбаяц", "streetsofrage": "вуліцыпрывядзеньня", "mkdeadlyalliance": "мксмертэльнысоюз", "nomoreheroes": "нямесцыгерояў", "mhr": "мхр", "mortalkombat12": "морталкомбат12", "thekingoffighters": "каралябаявыхмастатак", "likeadragon": "якдракон", "retrofightinggames": "ретрофайтынгігры", "blasphemous": "бласфемны", "rivalsofaether": "супернікіэфіру", "persona4arena": "персона4арэна", "marvelvscapcom": "марвелсвсапкам", "supersmash": "супердзіва", "mugen": "муген", "warofthemonsters": "вайнанамmonstрактоў", "jogosdeluta": "ігрыбою", "cyberbots": "кіберботы", "armoredwarriors": "бронированныеваины", "finalfight": "фінальнэйбітва", "poweredgear": "пауэр<PERSON><PERSON><PERSON>р", "beatemup": "бабіхваты", "blazblue": "блазблу", "mortalkombat9": "морталкомбат9", "fightgames": "баявыяігры", "killerinstinct": "убіцаінстынкт", "kingoffigthers": "карольбаяроў", "ghostrunner": "космадзёргосранер", "chivalry2": "рыцарства2", "demonssouls": "душыдэманаў", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "блазублюэкстагбатл", "blazbluextag": "блазблюэкстаг", "guiltygearstrive": "вінятаргатуйся", "hollowknightsequel": "сіквелхаллоўнайт", "hollowknightsilksong": "халапенькавечнасількпесня", "silksonghornet": "шоўтзабуся", "silksonggame": "гульнясілксонг", "silksongnews": "навінысілксонг", "silksong": "сілксong", "undernight": "днёмпадзямельнем", "typelumina": "тыпалюміна", "evolutiontournament": "эвалюцыйнытурнір", "evomoment": "эвамомент", "lollipopchainsaw": "ліпучкакалядка", "dragonballfighterz": "драконамячфайта<PERSON>з", "talesofberseria": "косціберсэрыі", "bloodborne": "кровапрал<PERSON><PERSON>ёва", "horizon": "гарызонт", "pathofexile": "шляхвыгнання", "slimerancher": "слімер<PERSON><PERSON><PERSON><PERSON>р", "crashbandicoot": "крэшбандзікаут", "bloodbourne": "кроўбурыя", "uncharted": "некартографаванае", "horizonzerodawn": "гарызонтнулявысвет", "ps4": "пс4", "ps5": "пс5", "spyro": "спайро", "playstationplus": "playstationplus", "lastofus": "апошнізнас", "infamous": "знакаміты", "playstationbuddies": "плейстэ<PERSON>шнабадзі", "ps1": "пс1", "oddworld": "незвычайнысвят", "playstation5": "плейстэйшн5", "slycooper": "слайкупер", "psp": "псп", "rabbids": "рабобасы", "splitgate": "сплітгейт", "persona4": "персона4", "hellletloose": "пеклаадкрылісі", "gta4": "gta4", "gta": "gta", "roguecompany": "неканфармісцкаякампанія", "aisomniumfiles": "айсомніумфайлы", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "багвайныбою", "gris": "грызі", "trove": "скарбнічка", "detroitbecomehuman": "дэтройтстаньчалавечым", "beatsaber": "бітабародка", "rimworld": "римсвет", "stellaris": "стал<PERSON><PERSON><PERSON><PERSON>", "ps3": "пс3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "турыстычнытрафей", "lspdfr": "лспдфр", "shadowofthecolossus": "ценіколасаў", "crashteamracing": "качаненароднагагонкі", "fivepd": "пяцьпд", "tekken7": "теккен7", "devilmaycry": "дябалможааплакваць", "devilmaycry3": "бясстрашныбузіна3", "devilmaycry5": "дябалможааплакваць5", "ufc4": "ufc4", "playingstation": "плэйстэйшн", "samuraiwarriors": "самурайскіявайнеры", "psvr2": "psvr2", "thelastguardian": "апошніахоўнік", "soulblade": "душамеча", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "плейстэйшн3", "manhunt": "паляваннезамужыком", "gtavicecity": "гтав<PERSON>ц<PERSON><PERSON><PERSON><PERSON><PERSON>", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2кавета", "pcsx2": "pcsx2", "lastguardian": "апошніахоўнік", "xboxone": "xboxone", "forza": "форца", "cd": "дзея", "gamepass": "геймпас", "armello": "армела", "partyanimal": "жывёланавечарынках", "warharmmer40k": "вайнуйцёк40к", "fightnightchampion": "боюзакаханцэмпіяна", "psychonauts": "псіханаўты", "mhw": "mhw", "princeofpersia": "прынцперасіі", "theelderscrollsskyrim": "старыяскролыскярыма", "pantarhei": "пандар<PERSON><PERSON><PERSON>й", "theelderscrolls": "старшыяскролі", "gxbox": "gxbox", "battlefront": "фронтамбаталы", "dontstarvetogether": "негаладаісцямразам", "ori": "оры", "spelunky": "спеланкі", "xbox1": "xbox1", "xbox360": "хbox360", "starbound": "зорныяфроны", "xboxonex": "xboxonex", "forzahorizon5": "форзагарызонт5", "skate3": "скейт3", "houseflipper": "перекупшчыкнедвижимости", "americanmcgeesalice": "американскійалісбуу", "xboxs": "ксбокс", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6к<PERSON><PERSON>нс", "leagueofkingdoms": "лігакаралеўстваў", "fable2": "фэйбл2", "xboxgamepass": "xboxgamepass", "undertale": "загадкі", "trashtv": "трэштв", "skycotl": "скйкотл", "erica": "эрыка", "ancestory": "радзіма", "cuphead": "капгед", "littlemisfortune": "маленькаенесparticipant", "sallyface": "сэліфэйс", "franbow": "франбоў", "monsterprom": "монстрпрам", "projectzomboid": "праектзомбойд", "ddlc": "ddlc", "motos": "матацыклы", "outerwilds": "вонкавыясвеце", "pbbg": "пббг", "anshi": "аншы", "cultofthelamb": "культзяліна", "duckgame": "качынаягра", "thestanleyparable": "станліпарабала", "towerunite": "вежанасканьня", "occulto": "акультны", "longdrive": "доўгіяпаездкі", "satisfactory": "здавальняюча", "pluviophile": "плювіяфіл", "underearth": "падзя<PERSON><PERSON><PERSON>й", "assettocorsa": "асеттагальфальты", "geometrydash": "геаметрыядаш", "kerbal": "кербал", "kerbalspaceprogram": "кербальпраграмакосмасу", "kenshi": "кэншы", "spiritfarer": "духаведзец", "darkdome": "цёмнагаўлаўка", "pizzatower": "піцавежа", "indiegame": "індыйнаягру", "itchio": "itchio", "golfit": "гольфіт", "truthordare": "праўдацаблізкае", "game": "гуляннё", "rockpaperscissors": "каменьпапераножніцы", "trampoline": "трампліна", "hulahoop": "хупальшчэка", "dare": "смела", "scavengerhunt": "квестнажаданне", "yardgames": "хатніяигры", "pickanumber": "выберылінумар", "trueorfalse": "праўдаіліфальш", "beerpong": "пивныпонт", "dicegoblin": "доскавыгаднік", "cosygames": "камфортныяігры", "datinggames": "святочныягульні", "freegame": "бясплатнаяігра", "drinkinggames": "гульнінапіткі", "sodoku": "содуку", "juegos": "гульні", "mahjong": "мажонг", "jeux": "ігры", "simulationgames": "гульнісимуляцыі", "wordgames": "слоўныяігаркі", "jeuxdemots": "ігравызначэння", "juegosdepalabras": "гульнісловамі", "letsplayagame": "дaвайпанияцьгру", "boredgames": "беспонтовыягульні", "oyun": "ояун", "interactivegames": "интерактивныягульні", "amtgard": "ам<PERSON>г<PERSON><PERSON>д", "staringcontests": "конкурсыпаглядаў", "spiele": "грай", "giochi": "гульні", "geoguessr": "геагуссар", "iphonegames": "айпонавыягры", "boogames": "бугульні", "cranegame": "кран<PERSON><PERSON><PERSON>м", "hideandseek": "схаваццаізнайсці", "hopscotch": "скачкі", "arcadegames": "аркадныягульні", "yakuzagames": "якудзагеймы", "classicgame": "кла<PERSON><PERSON>чнаягульня", "mindgames": "ігрыназаўмеў", "guessthelyric": "адгад<PERSON><PERSON>текст", "galagames": "галагэймы", "romancegame": "гульнядлярамантыкаў", "yanderegames": "яндэрэігры", "tonguetwisters": "язычныяскладаныясловы", "4xgames": "4xигры", "gamefi": "геймфі", "jeuxdarcades": "гуляннеуаркодах", "tabletopgames": "настольныяігры", "metroidvania": "метроидвэнія", "games90": "ігры90", "idareyou": "ясмелаюся", "mozaa": "мозаа", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "гульніпаводзін", "ets2": "ets2", "realvsfake": "сапраўднысупрацьфальшывага", "playgames": "грайцаючытвыігульні", "gameonline": "гульн<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "onlinegames": "ан<PERSON><PERSON><PERSON><PERSON>гульні", "jogosonline": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "writtenroleplay": "напісаныяролеўкі", "playaballgame": "грайуўубалтоўку", "pictionary": "пікшэннеры", "coopgames": "каапгульні", "jenga": "джэнга", "wiigames": "wiigames", "highscore": "высокірэзультат", "jeuxderôles": "ролеваягульня", "burgergames": "бург<PERSON><PERSON>гульні", "kidsgames": "гульнідлядзяцей", "skeeball": "скібол", "nfsmwblackedition": "nfsmwчорнаяверсія", "jeuconcour": "играконкурс", "tcgplayer": "tcgplayer", "juegodepreguntas": "ігравопытаў", "gioco": "гульня", "managementgame": "упраўленскаягульня", "hiddenobjectgame": "схаваныяабектыгра", "roolipelit": "роліпэліт", "formula1game": "гульняформула1", "citybuilder": "гуртакратка", "drdriving": "док<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "аркадныяигры", "memorygames": "гульніпамяці", "vulkan": "вулкан", "actiongames": "экшнігры", "blowgames": "блаўгеймы", "pinballmachines": "пінбольныяпрылады", "oldgames": "старыягульні", "couchcoop": "каштоўнаяпарадаўка", "perguntados": "пытанні", "gameo": "геймка", "lasergame": "лазернаягульня", "imessagegames": "ігрыboo", "idlegames": "бяздзейныяшпілі", "fillintheblank": "запоўніпразорку", "jeuxpc": "ігрывпк", "rétrogaming": "рэстрогейминг", "logicgames": "логічныяигры", "japangame": "япангуча", "rizzupgame": "рэвэрзпрыгода", "subwaysurf": "субвэйсерф", "jeuxdecelebrite": "гульнізнакамітасцяў", "exitgames": "выходныяігры", "5vs5": "5на5", "rolgame": "ролеваягульня", "dashiegames": "да<PERSON><PERSON>йгры", "gameandkill": "грайцiiзабiваць", "traditionalgames": "традыцыйныяігры", "kniffel": "кіфл", "gamefps": "ігравыяфпс", "textbasedgames": "тэкставыяігры", "gryparagrafowe": "грыпараграфавыя", "fantacalcio": "фантакальчыкі", "retrospel": "ретроспэл", "thiefgame": "крадзяжанаягульня", "lawngames": "бюджэтныягульні", "fliperama": "фліперама", "heroclix": "геройклікс", "tablesoccer": "столавыфутбол", "tischfußball": "настольныфутбол", "spieleabende": "вечарыгульняў", "jeuxforum": "жэюксфорум", "casualgames": "кежуальныягульні", "fléchettes": "стрэльбы", "escapegames": "эскейпгульні", "thiefgameseries": "гульняйкразеры", "cranegames": "крана<PERSON>гры", "játék": "гульня", "bordfodbold": "бордфутбол", "jogosorte": "іграсорт", "mage": "магія", "cargames": "гульнізаўжажаўках", "onlineplay": "онлайнгульня", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "ігравыявечары", "pursebingos": "кашальбінга", "randomizer": "хуткасцеразабіральнік", "msx": "мсх", "anagrammi": "анаграмы", "gamespc": "ігрынапк", "socialdeductiongames": "сацыяльныягульнідоказаў", "dominos": "доміно", "domino": "доміно", "isometricgames": "іметрычныяігры", "goodoldgames": "добрыястарыяігры", "truthanddare": "праўдаіадвага", "mahjongriichi": "маёнгрычы", "scavengerhunts": "паляванн<PERSON>зсілічкамі", "jeuxvirtuel": "віртуальныяігры", "romhack": "ромхак", "f2pgamer": "f2pgamer", "free2play": "бясплатнаиграць", "fantasygame": "фантаз<PERSON>йнаягульня", "gryonline": "грыон<PERSON><PERSON><PERSON>н", "driftgame": "дрыфткрутой", "gamesotomes": "гульнідлякрутых", "halotvseriesandgames": "галасерыяіигрыboo", "mushroomoasis": "грыбнаяазіс", "anythingwithanengine": "усёздзішамнаматорэ", "everywheregame": "гульняусюды", "swordandsorcery": "мечыяспеласць", "goodgamegiving": "добраягульнядаваць", "jugamos": "гуляем", "lab8games": "лаб8гульні", "labzerogames": "лабзерагульні", "grykomputerowe": "гульнінакампутары", "virgogami": "віргогамі", "gogame": "граймо", "jeuxderythmes": "гульніритміка", "minaturegames": "мікрамад<PERSON>лі", "ridgeracertype4": "риджэракіпарт4", "selflovegaming": "любоўдасабеиграванне", "gamemodding": "гейммаддз<PERSON>нг", "crimegames": "крымінальныягульні", "dobbelspellen": "дваразовымігірамі", "spelletjes": "ігры", "spacenerf": "космічнынёрф", "charades": "шарады", "singleplayer": "однапользавец", "coopgame": "куперграя", "gamed": "гэймед", "forzahorizon": "forzahorizon", "nexus": "нексус", "geforcenow": "geforcenow", "maingame": "галоўнаягульня", "kingdiscord": "каральдискорд", "scrabble": "скрабл", "schach": "шахматы", "shogi": "шогі", "dandd": "дзендд", "catan": "катан", "ludo": "людо", "backgammon": "назыграйкрасны", "onitama": "онитамы", "pandemiclegacy": "спадчынапандэміі", "camelup": "камелап", "monopolygame": "монопаліягульня", "brettspiele": "настольныягульні", "bordspellen": "бардспэлі", "boardgame": "настольнаягульня", "sällskapspel": "настольныягульні", "planszowe": "планштовыя", "risiko": "рысікo", "permainanpapan": "настольныяігры", "zombicide": "зомбіцыд", "tabletop": "настольныяігры", "baduk": "бадук", "bloodbowl": "кровавыбаўлін", "cluedo": "клюэдо", "xiangqi": "сянцы", "senet": "сенет", "goboardgame": "губордгайм", "connectfour": "звязацьчывясло", "heroquest": "героіквест", "giochidatavolo": "гульнінастале", "farkle": "фаркл", "carrom": "каром", "tablegames": "настольныягульні", "dicegames": "кубічныяигры", "yatzy": "яцці", "parchis": "партчыс", "jogodetabuleiro": "йогаба<PERSON><PERSON><PERSON><PERSON>н", "jocuridesocietate": "гульнінаасобак", "deskgames": "настольныягульні", "alpharius": "альфарыус", "masaoyunları": "масавыярады", "marvelcrisisprotocol": "марвелкрызісныпротакол", "cosmicencounter": "касмічнаесяджанне", "creationludique": "крэатыўнаезабаванне", "tabletoproleplay": "настольныяролявыяигры", "cardboardgames": "картонныяігры", "eldritchhorror": "элдрыджаўжас", "switchboardgames": "бортзабавы", "infinitythegame": "бесканечнаяграўня", "kingdomdeath": "каралёўсмерці", "yahtzee": "яцеце", "chutesandladders": "запавадз<PERSON>шчыілестніцы", "társas": "тэрэса", "juegodemesa": "настольнаягульня", "planszówki": "планшоўкі", "rednecklife": "чырвонашапкажыццё", "boardom": "боскія", "applestoapples": "яблыкадаяблыка", "jeudesociété": "гульнявоесяднанне", "gameboard": "гульнявыборд", "dominó": "доминэ", "kalah": "калаҳ", "crokinole": "крокіноле", "jeuxdesociétés": "гульнінастолях", "twilightimperium": "сутычкіназаходзе", "horseopoly": "коньопалія", "deckbuilding": "дэкбілдынг", "mansionsofmadness": "імшыдляварятаў", "gomoku": "гамоку", "giochidatavola": "гульнінастале", "shadowsofbrimstone": "сцянкібрымы", "kingoftokyo": "карольтакіо", "warcaby": "варкабы", "táblajátékok": "таблеткiгульні", "battleship": "бітвакорабель", "tickettoride": "білетнакатанку", "deskovehry": "столавыягульні", "catán": "катан", "subbuteo": "субутэа", "jeuxdeplateau": "настольныяігры", "stolníhry": "столкіроўкі", "xiángqi": "сянцзі", "jeuxsociete": "настолкі", "gesellschaftsspiele": "гульнідлякампаніі", "starwarslegion": "зорныяваенылегенда", "gochess": "гра<PERSON><PERSON><PERSON><PERSON>матам", "weiqi": "вэйці", "jeuxdesocietes": "настольныягульні", "terraria": "тарэрыя", "dsmp": "дсmp", "warzone": "ваеннаязона", "arksurvivalevolved": "аркsurvivalevolved", "dayz": "<PERSON><PERSON><PERSON>", "identityv": "ідэнтычнасцьv", "theisle": "остравок", "thelastofus": "апошнізнасвеце", "nomanssky": "нямачалавекаўназямлі", "subnautica": "субнаўтыка", "tombraider": "тумбрайдэр", "callofcthulhu": "закліккацялуху", "bendyandtheinkmachine": "бендыічитываўмашыну", "conanexiles": "конанэкзайлс", "eft": "фатабу", "amongus": "сяро<PERSON><PERSON>с", "eco": "эка", "monkeyisland": "мавпачоства", "valheim": "вал<PERSON>ейм", "planetcrafter": "планета<PERSON><PERSON><PERSON><PERSON><PERSON>ер", "daysgone": "дн<PERSON><PERSON>ра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "фобія", "witchit": "ведзьмадзіцця", "pathologic": "партал<PERSON><PERSON><PERSON><PERSON>ны", "zomboid": "зомбойд", "northgard": "нордгард", "7dtd": "7dtd", "thelongdark": "доўгіцёмныць", "ark": "арк", "grounded": "приземлены", "stateofdecay2": "станразлажэння2", "vrising": "вяртанне", "madfather": "божанка", "dontstarve": "негрызці", "eternalreturn": "вечнывяртанне", "pathoftitans": "шляхдацітанаў", "frictionalgames": "фрыкцыянальныяігры", "hexen": "гексен", "theevilwithin": "злоўнутры", "realrac": "рэальнарэц", "thebackrooms": "задніяпакоі", "backrooms": "задніягуртоў", "empiressmp": "эмпірэсмп", "blockstory": "блокгісторыя", "thequarry": "карычня", "tlou": "тлоу", "dyinglight": "святлоўмерцвякоў", "thewalkingdeadgame": "гулядзіжывыхмертвякоў", "wehappyfew": "мышчаслівыянесколькі", "riseofempires": "узрастаюцьімперыі", "stateofsurvivalgame": "гульнястанувыжытаць", "vintagestory": "вінтажнаягісторыя", "arksurvival": "аркавывыжывальны", "barotrauma": "баротраўма", "breathedge": "дыханне", "alisa": "alisa", "westlendsurvival": "вяснажыццябэа", "beastsofbermuda": "бяссіллябермуды", "frostpunk": "фростпанк", "darkwood": "цёмныдуб", "survivalhorror": "выжывальніцкіжах", "residentevil": "жахірэзідэнтэвіла", "residentevil2": "рэзідэнтзла2", "residentevil4": "рэжымжыцця4", "residentevil3": "бія<PERSON><PERSON>цьцё3", "voidtrain": "вадаўзвод", "lifeaftergame": "жыццёпаслягульні", "survivalgames": "гульнінавыжываннёў", "sillenthill": "ціхаягара", "thiswarofmine": "гэтаваяннамая", "scpfoundation": "scpфонд", "greenproject": "зялёныпраект", "kuon": "куон", "cryoffear": "плакніадстраху", "raft": "плот", "rdo": "рдо", "greenhell": "зялёныпекельнік", "residentevil5": "рэзідэнтзла5", "deadpoly": "дэдпалі", "residentevil8": "жывойзлом8", "onironauta": "анір<PERSON><PERSON><PERSON>ы", "granny": "бабуля", "littlenightmares2": "маленькіяжахі2", "signalis": "сигна<PERSON><PERSON><PERSON><PERSON>й", "amandatheadventurer": "амандападарожніца", "sonsoftheforest": "сынылесу", "rustvideogame": "rustгульня", "outlasttrials": "выжыцьвыпрабаванні", "alienisolation": "іншапланетнаеадзіноцтва", "undawn": "недзень", "7day2die": "7дні2памёрці", "sunlesssea": "безсонечнаемора", "sopravvivenza": "выжыванне", "propnight": "прапнавочыць", "deadisland2": "мертваявыспа2", "ikemensengoku": "ікеаменсэнгоку", "ikemenvampire": "айкеменвампір", "deathverse": "смерцьверс", "cataclysmdarkdays": "катаклізмыцёмкіядні", "soma": "сома", "fearandhunger": "страх<PERSON><PERSON><PERSON>утка", "stalkercieńczarnobyla": "сталькерцяньчарнобыль", "lifeafter": "жыццёпасля", "ageofdarkness": "эпохацёмры", "clocktower3": "гадзіннікававежа3", "aloneinthedark": "адзіночнасцьуцемры", "medievaldynasty": "сярэднявечнаядынастыя", "projectnimbusgame": "праектнімбусгейм", "eternights": "вечнаемаразы", "craftopia": "кравтопія", "theoutlasttrials": "выпрабаваннідосвятла", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "светаваядзенне", "rocketleague": "ракэтнаяліга", "tft": "tft", "officioassassinorum": "офіцыёзныхубіццаў", "necron": "некрон", "wfrp": "wfrp", "dwarfslayer": "двар<PERSON>абойца", "warhammer40kcrush": "вайналям40ккрамата", "wh40": "wh40", "warhammer40klove": "вайнаршар40клюб", "warhammer40klore": "вселеннаябурства40к", "warhammer": "ваярмарка", "warhammer30k": "вайнармян30к", "warhammer40k": "вайнушкі40к", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "вынік", "ilovesororitas": "ялюблюсороры", "ilovevindicare": "ялюблювіндзікаце", "iloveassasinorum": "ялюблюасасінаў", "templovenenum": "темпловенэнум", "templocallidus": "темплаколідыз", "templomaerorus": "темпломаерорус", "templovanus": "темплаваўнус", "oficioasesinorum": "офіцыязабойцаў", "tarkov": "таркоў", "40k": "40к", "tetris": "тэтрыс", "lioden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires": "эпохапанавання", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "вайнармеразгарадсігмара", "civilizationv": "цывілізацыяv", "ittakestwo": "ітакімнужнодва", "wingspan": "крыльявыразмах", "terraformingmars": "терраформінгмарса", "heroesofmightandmagic": "героімагутнасцізаідзіцтва", "btd6": "btd6", "supremecommander": "супрымкамандзір", "ageofmythology": "эпохаміфалогіі", "args": "аргс", "rime": "досыць", "planetzoo": "планетазоа", "outpost2": "выйдзі4", "banished": "высланы", "caesar3": "цэзарь3", "redalert": "чырвонысигнал", "civilization6": "цывілізацыя6", "warcraft2": "варкрафт2", "commandandconquer": "каманд<PERSON>йіканкруй", "warcraft3": "warcraft3", "eternalwar": "вечнаявайна", "strategygames": "стратэгічныяигры", "anno2070": "anno2070", "civilizationgame": "гульняццавецтва", "civilization4": "цывілізацыя4", "factorio": "фактарыё", "dungeondraft": "падземнынакід", "spore": "споры", "totalwar": "агульнаявайна", "travian": "травіан", "forts": "фортцы", "goodcompany": "добраякампанія", "civ": "гэтакжэ", "homeworld": "хатнісвет", "heidentum": "хайдэнтум", "aoe4": "аоё4", "hnefatafl": "хнефатавл", "fasterthanlight": "хутчэйзасвятло", "forthekings": "дзядзькам", "realtimestrategy": "рэальнычасавыстратэгія", "starctaft": "старкр<PERSON><PERSON>т", "sidmeierscivilization": "сидмеиерсцивілізацыя", "kingdomtwocrowns": "каралёўдвакароны", "eu4": "еў4", "vainglory": "гардыя", "ww40k": "ww40k", "godhood": "багаслаўленне", "anno": "анно", "battletech": "біцьтэх", "malifaux": "малiфоx", "w40k": "w40k", "hattrick": "хэттрык", "davesfunalgebraclass": "класалгебрыдэйва", "plagueinc": "плачзаразы", "theorycraft": "теорыямастака", "mesbg": "месбг", "civilization3": "цывілізацыя3", "4inarow": "4за376", "crusaderkings3": "крусадэрыкаралі3", "heroes3": "герояў3", "advancewars": "авансныявайны", "ageofempires2": "эпохаімперы2", "disciples2": "дисцыплы2", "plantsvszombies": "раслінаўсупрацьзомбі", "giochidistrategia": "гульністратэгіі", "stratejioyunları": "бупрэтвары", "europauniversalis4": "европауніверсаліс4", "warhammervermintide2": "ваеннымартамбіралкі2", "ageofwonders": "эпохацудоў", "dinosaurking": "д<PERSON><PERSON><PERSON>саваркароль", "worldconquest": "сусветнаеўзброенне", "heartsofiron4": "сердцыжалезаў4", "companyofheroes": "кампаніягерояў", "battleforwesnoth": "бітвазавэсноць", "aoe3": "аое3", "forgeofempires": "кузняімперыяў", "warhammerkillteam": "вархаммеркіўтім", "goosegooseduck": "гусьгусцькачка", "phobies": "фобіі", "phobiesgame": "фобіігры", "gamingclashroyale": "геймінгклашройал", "adeptusmechanicus": "адэптусмеханікус", "outerplane": "зваротныплан", "turnbased": "базавананапавароце", "bomberman": "бамбіст", "ageofempires4": "эпохаімперый4", "civilization5": "цывілізацыя5", "victoria2": "віктарыя2", "crusaderkings": "крыжовыярыцары", "cultris2": "культырус2", "spellcraft": "спелкрафт", "starwarsempireatwar": "вартаарыстараў", "pikmin4": "пікмін4", "anno1800": "anno1800", "estratégia": "стратэгія", "popfulmail": "попфулмэйл", "shiningforce": "сіяючаясіла", "masterduel": "мастэрдуэль", "dysonsphereprogram": "дысонсферапраграма", "transporttycoon": "транспартнытыкон", "unrailed": "незавыранны", "magicarena": "маг<PERSON>чнаяарэна", "wolvesville": "воўкалавільня", "ooblets": "ooblets", "planescapetorment": "планутечацьадпакуты", "uplandkingdoms": "верхавіныкаролёў", "galaxylife": "галактычнаежыццё", "wolvesvilleonline": "воўчыягорадонлайн", "slaythespire": "разабіспіру", "battlecats": "бітвакошкі", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "симс", "simcity": "симсити", "simcity2000": "симсити2000", "sims2": "sims2", "iracing": "ірэй<PERSON>інг", "granturismo": "грантурызмо", "needforspeed": "нужнадрайве", "needforspeedcarbon": "нужнапагонякарбон", "realracing3": "рэальнаярэйсінг3", "trackmania": "тракманія", "grandtourismo": "грандыёзнаетурызмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "сімс2", "thesims3": "тымсімс3", "thesims1": "сімс1", "lossims4": "гублюсімс4", "fnaf": "fnaf", "outlast": "выжыць", "deadbydaylight": "смерцьпаудзеньне", "alicemadnessreturns": "алісавяртаннебясссцяжнасці", "darkhorseanthology": "баяв<PERSON>кбукет", "phasmophobia": "фазмафобія", "fivenightsatfreddys": "пяцьночэйнаўфрэдзі", "saiko": "сайка", "fatalframe": "фатальнаесяння", "littlenightmares": "маленькіякашмары", "deadrising": "ўзрыўмаладосці", "ladydimitrescu": "ладзідымітрэску", "homebound": "хатняездз<PERSON>ш", "deadisland": "мертвыастроў", "litlemissfortune": "маленькаяшчасце", "projectzero": "проектноль", "horory": "ахвярадзія", "jogosterror": "жогас<PERSON><PERSON><PERSON><PERSON>на", "helloneighbor": "прывітаннясуседу", "helloneighbor2": "прывітаннесусед2", "gamingdbd": "геймерабд", "thecatlady": "коткаваямама", "jeuxhorreur": "жа<PERSON><PERSON><PERSON>гры", "horrorgaming": "жахлівыягульні", "magicthegathering": "магiязбору", "mtg": "мтг", "tcg": "тсг", "cardsagainsthumanity": "картызупратівлюдашнасці", "cribbage": "крыбэдж", "minnesotamtg": "міннесотамтк", "edh": "еда", "monte": "монте", "pinochle": "пінокл", "codenames": "кодавыяімёны", "dixit": "дыксіт", "bicyclecards": "вялікіякарткі", "lor": "лор", "euchre": "ю<PERSON>ер", "thegwent": "гвент", "legendofrunetera": "легендаранетэры", "solitaire": "сал<PERSON><PERSON><PERSON><PERSON>", "poker": "покер", "hearthstone": "карт<PERSON><PERSON><PERSON>тура", "uno": "унана", "schafkopf": "шахаўгалоўка", "keyforge": "кіефордж", "cardtricks": "карткіфокусы", "playingcards": "карткіягры", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "гін<PERSON><PERSON><PERSON><PERSON>", "netrunner": "нетранер", "gwent": "гвент", "metazoo": "метазоа", "tradingcards": "трейдынгкарточкі", "pokemoncards": "пакемонакарткі", "fleshandbloodtcg": "плотзьікроўtcg", "sportscards": "спорткарткі", "cardfightvanguard": "картбітвавагнард", "duellinks": "дуэльнызлучэнні", "spades": "піка", "warcry": "ваеннызварот", "digimontcg": "дз<PERSON><PERSON><PERSON><PERSON>онткг", "toukenranbu": "тукен<PERSON><PERSON><PERSON><PERSON>у", "kingofhearts": "каралев<PERSON><PERSON><PERSON>рц<PERSON>й", "truco": "трука", "loteria": "латарэя", "hanafuda": "ханафуда", "theresistance": "супрацьстаянне", "transformerstcg": "трансформерыркг", "doppelkopf": "доппелькроп", "yugiohcards": "югио<PERSON><PERSON><PERSON><PERSON>ы", "yugiohtcg": "югіохтцг", "yugiohduel": "югиёхдуэль", "yugiohocg": "югиохцг", "dueldisk": "дюэльдыск", "yugiohgame": "юг<PERSON>ёгульня", "darkmagician": "цёмнычаклун", "blueeyeswhitedragon": "блакітныявачыбелыдракон", "yugiohgoat": "югиошпачкотка", "briscas": "брыскасы", "juegocartas": "карткавыяігры", "burraco": "буррако", "rummy": "рамі", "grawkarty": "граўкарткі", "dobble": "дока", "mtgcommander": "mtgкаман<PERSON><PERSON><PERSON>р", "cotorro": "каторро", "jeuxdecartes": "гульнікарткі", "mtgjudge": "суддзямtg", "juegosdecartas": "картковыяігры", "duelyst": "дюэліст", "mtgplanschase": "mtgпланавацьбегчы", "mtgpreconcommander": "mtgпрэконкамандзір", "kartenspiel": "гульнякарт", "carteado": "картаэдо", "sueca": "суэка", "beloteonline": "бело<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карціянкі", "battlespirits": "бітваспірітоў", "battlespiritssaga": "бітва_душаў_эпопея", "jogodecartas": "жогадэкарт", "žolíky": "жолікі", "facecard": "тваркартка", "cardfight": "картбітва", "biriba": "бірыба", "deckbuilders": "декбілдэры", "marvelchampions": "марвелчэмпions", "magiccartas": "магічныякарткі", "yugiohmasterduel": "юг<PERSON><PERSON><PERSON><PERSON>айстардуэлі", "shadowverse": "ценявыверс", "skipbo": "скіпбо", "unstableunicorns": "некантрольныяедзіorns", "cyberse": "кіберсэ", "classicarcadegames": "класічныяаркадныягульні", "osu": "осу", "gitadora": "гітадора", "dancegames": "танцавальныягульні", "fridaynightfunkin": "fridaynightfunkin", "fnf": "фнф", "proseka": "празека", "projectmirai": "проектмірай", "projectdiva": "праектдзіва", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "клонагерой", "justdance": "простатан<PERSON>уй", "hatsunemiku": "хатсунэміку", "prosekai": "прасеківай", "rocksmith": "роксміт", "idolish7": "ідоліш7", "rockthedead": "задоймаммертвых", "chunithm": "чунітм", "idolmaster": "ідолмастэр", "dancecentral": "танцавальныцэнтр", "rhythmgamer": "рытмгеймер", "stepmania": "стэпманія", "highscorerythmgames": "хацябольшвысокіхчыслаўнаглядаў", "pkxd": "pkxd", "sidem": "сідэм", "ongeki": "онгэкі", "soundvoltex": "звучныміёксы", "rhythmheaven": "ритмавырай", "hypmic": "гипмик", "adanceoffireandice": "танецагнюіледу", "auditiononline": "аўдыцыяонлайн", "itgmania": "itgманія", "juegosderitmo": "гульніритму", "cryptofthenecrodancer": "крыптафтанэкрадансера", "rhythmdoctor": "ритмдоктар", "cubing": "каб<PERSON><PERSON><PERSON>", "wordle": "слоўдл", "teniz": "тэніз", "puzzlegames": "гульнізгадкамі", "spotit": "зімкай", "rummikub": "румынкаб", "blockdoku": "блкдоку", "logicpuzzles": "галоўаломкі", "sudoku": "судоку", "rubik": "рубікаў", "brainteasers": "галоўаломкі", "rubikscube": "рубікаваскуб", "crossword": "кросвард", "motscroisés": "моўныкросвард", "krzyżówki": "кросворды", "nonogram": "нанаграма", "bookworm": "кніжнычка", "jigsawpuzzles": "пазлы", "indovinello": "загадка", "riddle": "загадка", "riddles": "гадзюкі", "rompecabezas": "пазл", "tekateki": "тэкатэкі", "inside": "унутры", "angrybirds": "злыяптушкі", "escapesimulator": "эскапсімулятар", "minesweeper": "м<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "галаваломкаітраканікі", "crosswordpuzzles": "кросворды", "kurushi": "курышы", "gardenscapesgame": "садовыяшчытапузырьки", "puzzlesport": "пазлспорт", "escaperoomgames": "гульніэскейпрума", "escapegame": "эскейпгейм", "3dpuzzle": "3dголаваломка", "homescapesgame": "гульняхоумскэйпс", "wordsearch": "шукаемсловамі", "enigmistica": "енігмістыка", "kulaworld": "кула<PERSON><PERSON><PERSON><PERSON>р", "myst": "міст", "riddletales": "гадзілкiсказак", "fishdom": "рыбнысвет", "theimpossiblequiz": "незрабімаявіктарына", "candycrush": "сахарныбой", "littlebigplanet": "маленьківялікіпланета", "match3puzzle": "матч3пазл", "huniepop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "katamaridamacy": "катамаридамачы", "kwirky": "квіркі", "rubikcube": "рубікаўкуб", "cuborubik": "кубікрубіка", "yapboz": "япб<PERSON>з", "thetalosprinciple": "традыцыналапрыцягнення", "homescapes": "хатняяжыццё", "puttputt": "путпут", "qbert": "квёрт", "riddleme": "задзіўмяне", "tycoongames": "тымкавыяигры", "cubosderubik": "кубікrubik", "cruciverba": "кросwords", "ciphers": "шыфры", "rätselwörter": "загадкавыясловы", "buscaminas": "шукаемміны", "puzzlesolving": "пазлса<PERSON><PERSON><PERSON>нг", "turnipboy": "бурякхлопец", "adivinanzashot": "адз<PERSON>на<PERSON><PERSON><PERSON><PERSON><PERSON>т", "nobodies": "нікчэмы", "guessing": "д<PERSON>г<PERSON><PERSON><PERSON><PERSON>", "nonograms": "нонаграмы", "kostkirubika": "косткірубіка", "crypticcrosswords": "крыптакрыжыкі", "syberia2": "сыберия2", "puzzlehunt": "гульнязагадка", "puzzlehunts": "пазлымуцьбя", "catcrime": "котнакрыму", "quebracabeça": "зламайгалоўку", "hlavolamy": "галоўныягадасці", "poptropica": "паптропіка", "thelastcampfire": "апошняеагняпальца", "autodefinidos": "аўтавызначаныя", "picopark": "пікапарк", "wandersong": "блукаючыпесня", "carto": "картон", "untitledgoosegame": "нзагалоўнаягусінграмка", "cassetête": "кассетэт", "limbo": "лімба", "rubiks": "рубіка", "maze": "лаб<PERSON><PERSON><PERSON>нт", "tinykin": "ціхонькі", "rubikovakostka": "рубікавакастка", "speedcube": "спідкуб", "pieces": "кусочкі", "portalgame": "портал<PERSON><PERSON><PERSON>м", "bilmece": "білмэчэ", "puzzelen": "пазлы", "picross": "пік<PERSON><PERSON><PERSON>", "rubixcube": "рубікскуб", "indovinelli": "загадкі", "cubomagico": "кубомагіка", "mlbb": "млбб", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "кцдм", "twistedwonderland": "крутаявясёлка", "monopoly": "манаполія", "futurefight": "будучаябітва", "mobilelegends": "мобільныялегенды", "brawlstars": "браўлстары", "brawlstar": "браўлстар", "coc": "какашка", "lonewolf": "адзіночнываўк", "gacha": "гача", "wr": "вяртанне", "fgo": "фго", "bitlife": "жыццяздольнасць", "pikminbloom": "пікмінаблума", "ff": "фф", "ensemblestars": "ансамблеўскіязоркі", "asphalt9": "асфальт9", "mlb": "млб", "cookierunkingdom": "кок<PERSON>рунакінгдам", "alchemystars": "алхіміязорак", "stateofsurvival": "станпавыгляду", "mycity": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>д", "arknights": "аркнайтс", "colorfulstage": "колеравыэтап", "bloonstowerdefense": "блунсторзаабарона", "btd": "бтд", "clashroyale": "клашрояль", "angela": "анджэла", "dokkanbattle": "докканбітва", "fategrandorder": "судьбаўзгадка", "hyperfront": "гиперфронт", "knightrun": "рыцарскіятрост", "fireemblemheroes": "пажарнысімвальгероі", "honkaiimpact": "ханкайімпакт", "soccerbattle": "футбольнаябітва", "a3": "а3", "phonegames": "гульнінафоне", "kingschoice": "карыстальніцківыбар", "guardiantales": "ахвярныказкі", "petrolhead": "бензагал頭", "tacticool": "тактыкул", "cookierun": "кукiруга", "pixeldungeon": "піксельныпадзямелле", "arcaea": "аркаэа", "outoftheloop": "вадзікрупкі", "craftsman": "майстар", "supersus": "суперпадазроны", "slowdrive": "медленнекіраваць", "headsup": "падрыхтуйся", "wordfeud": "словабойка", "bedwars": "бэдварс", "freefire": "бясплатныагонь", "mobilegaming": "мабільныяигры", "lilysgarden": "ліл<PERSON>йнысад", "farmville2": "фармвіл2", "animalcrossing": "анімал<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "bgmi": "бгмі", "teamfighttactics": "камандыбітвы", "clashofclans": "кланавыканфлікт", "pjsekai": "пжсэкашкі", "mysticmessenger": "мясцовыяпаведамлення", "callofdutymobile": "закліквапаслямобіль", "thearcana": "аркана", "8ballpool": "8бадзел", "emergencyhq": "надзвычайнаеhq", "enstars": "енстары", "randonautica": "рандонаўтыка", "maplestory": "кленавыягісторыі", "albion": "альбіён", "hayday": "факусна", "onmyoji": "онміёдзі", "azurlane": "азурагавары", "shakesandfidget": "шэйкыпавароты", "ml": "ml", "bangdream": "бэнгдрым", "clashofclan": "кланавыябойкі", "starstableonline": "зорнаясталаяонлайн", "dragonraja": "драконрая", "timeprincess": "часпрыга<PERSON>уні", "beatstar": "бітстар", "dragonmanialegend": "драконамадэфалистории", "hanabi": "хана<PERSON><PERSON>", "disneymirrorverse": "диснеймірраверс", "pocketlove": "каша<PERSON><PERSON><PERSON><PERSON>клюбові", "androidgames": "андро<PERSON>дгульні", "criminalcase": "крымінальнаясправа", "summonerswar": "сумонерскаясварка", "cookingmadness": "куханнемаднасць", "dokkan": "доккан", "aov": "аав", "triviacrack": "трывіяпрыпынак", "leagueofangels": "лигиа<PERSON><PERSON><PERSON><PERSON>к", "lordsmobile": "лордсмобіль", "tinybirdgarden": "маленькіптушачкасад", "gachalife": "gachalife", "neuralcloud": "нейроннаеаблачка", "mysingingmonsters": "маіпеўцамонстры", "nekoatsume": "некоацумэ", "bluearchive": "блаукапія", "raidshadowlegends": "raidshadowlegends", "warrobots": "ваенныяробаты", "mirrorverse": "люстэркавысусвет", "pou": "поу", "warwings": "ваенныякрылы", "fifamobile": "фіфамаб<PERSON>ль", "mobalegendbangbang": "мобалегендбэнгбэнг", "evertale": "еўратэйл", "futime": "футыме", "antiyoy": "ан<PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "апекслідзікімобіль", "ingress": "інгрэсу", "slugitout": "супрацьстаяць", "mpl": "mpl", "coinmaster": "койнмастар", "punishinggrayraven": "пакутлівышэракарэння", "petpals": "хатняепадобранне", "gameofsultans": "гульнясултанаў", "arenabreakout": "арэнавыбег", "wolfy": "воўчы", "runcitygame": "бяжымградграю", "juegodemovil": "мобільныяігры", "avakinlife": "авакінжыццё", "kogama": "когама", "mimicry": "міметызм", "blackdesertmobile": "чорныпустэльнікамобіль", "rollercoastertycoon": "ролікавыпаркавец", "grandchase": "вялікігонка", "bombmebrasil": "бомбямебразіл", "ldoe": "лдоя", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "атомеігры", "mindustry": "майндустрыя", "callofdragons": "заклікдраконаў", "shiningnikki": "сяючаямікі", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "патхта<PERSON><PERSON><PERSON>удалёк", "sealm": "сілм", "shadowfight3": "shadowfight3", "limbuscompany": "лімбускампанія", "demolitionderby3": "дэмонтажныдельмі3", "wordswithfriends2": "словаміздрузямі2", "soulknight": "душавырыцары", "purrfecttale": "ідэальнаеапавяданне", "showbyrock": "showbyrock", "ladypopular": "жалебелагенсаў", "lolmobile": "лулзмобіль", "harvesttown": "уборкагорода", "perfectworldmobile": "перафектнысветмобільны", "empiresandpuzzles": "імперыітапазлы", "empirespuzzles": "імпірскіпазлы", "dragoncity": "дра<PERSON><PERSON>нагарад", "garticphone": "гартікфон", "battlegroundmobileind": "боювоепаліщемабільнаеин", "fanny": "фані", "littlenightmare": "маленькікашмар", "aethergazer": "aetherгледзяк", "mudrunner": "мудранажэнне", "tearsofthemis": "слёзыгаспадароў", "eversoul": "эвэр<PERSON><PERSON><PERSON>л", "gunbound": "ган<PERSON>унд", "gamingmlbb": "геймінгмлбб", "dbdmobile": "дбдмобіль", "arknight": "арк<PERSON><PERSON>т", "pristontale": "прыстаньtale", "zombiecastaways": "зомбівыдзяленцы", "eveechoes": "эвечкі", "jogocelular": "йогасмартфон", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "зуба", "mobilelegendbangbang": "мобільнылегендабумбум", "gachaclub": "гатчаклуб", "v4": "v4", "cookingmama": "кухнямама", "cabalmobile": "кабалмобіль", "streetfighterduel": "вуліцыбітвыduel", "lesecretdhenri": "лесакрэтдэнры", "gamingbgmi": "геймінгбгмі", "girlsfrontline": "дзіўчатыпапераднiк", "jurassicworldalive": "юрасічнысветжывучы", "soulseeker": "душа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "gettingoverit": "перакладацькропка", "openttd": "openttd", "onepiecebountyrush": "адзіныфрагментбонуснайспешкі", "moonchaistory": "мунчысторы", "carxdriftracingonline": "карксдрифтарынганлайн", "jogosmobile": "мобільныягульні", "legendofneverland": "легендаабнезямі", "pubglite": "pubglite", "gamemobilelegends": "гейммобайллегендс", "timeraiders": "таймрайдэры", "gamingmobile": "гульнявое_мобільнае", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "біт<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "днд", "quest": "квест", "giochidiruolo": "гульняпадкантролем", "dnd5e": "dnd5e", "rpgdemesa": "rpgдэмесы", "worldofdarkness": "светцямроты", "travellerttrpg": "падарожнікіттрпг", "2300ad": "2300гад", "larp": "лар<PERSON>", "romanceclub": "романтычныклуб", "d20": "д20", "pokemongames": "покемонаўгульні", "pokemonmysterydungeon": "покемонамістэрыяпадзямлі", "pokemonlegendsarceus": "покемонылегендысарсеў", "pokemoncrystal": "покеманкрышталь", "pokemonanime": "покемонаанімэ", "pokémongo": "покемонаманга", "pokemonred": "покеманчырвоны", "pokemongo": "покемонамана", "pokemonshowdown": "пакемонаўзмаганне", "pokemonranger": "покеманрэнджэр", "lipeep": "лі<PERSON><PERSON><PERSON>", "porygon": "порыгон", "pokemonunite": "покеманюніт", "entai": "энтай", "hypno": "гипно", "empoleon": "эмпoleon", "arceus": "арце<PERSON>с", "mewtwo": "мьюту", "paldea": "палдзія", "pokemonscarlet": "покемонсарджа", "chatot": "чатот", "pikachu": "піка<PERSON>у", "roxie": "roxie", "pokemonviolet": "покемонавілет", "pokemonpurpura": "покемоныпурпура", "ashketchum": "ашкечум", "gengar": "генгар", "natu": "нату", "teamrocket": "камандаракета", "furret": "фурэт", "magikarp": "магiкарп", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "снорлакс", "pocketmonsters": "кашалькавыманты", "nuzlocke": "нузлок", "pokemonplush": "покемоныплюшы", "teamystic": "камандамістіка", "pokeball": "покебол", "charmander": "чармандыр", "pokemonromhack": "покемонырамхак", "pubgmobile": "pubgmobile", "litten": "гарбачка", "shinypokemon": "бліскучапакемонаў", "mesprit": "меспрыт", "pokémoni": "пакемоны", "ironhands": "жалезнаеўкі", "kabutops": "кабутопс", "psyduck": "псіхопаўк", "umbreon": "умбрэон", "pokevore": "покевор", "ptcg": "птцг", "piplup": "піплап", "pokemonsleep": "покемонасон", "heyyoupikachu": "гэййоупікачу", "pokémonmaster": "покеманмайстар", "pokémonsleep": "покемансон", "kidsandpokemon": "дзіцііпокемоны", "pokemonsnap": "покемонснап", "bulbasaur": "балбасаўр", "lucario": "лукарио", "charizar": "карызара", "shinyhunter": "бліскучыпаляўнічына", "ajedrez": "шахматы", "catur": "катур", "xadrez": "шахматы", "scacchi": "шахматы", "schaken": "шахматы", "skak": "скак", "ajedres": "aje<PERSON>s", "chessgirls": "шахматныядзяўчаты", "magnuscarlsen": "магнускарлсэн", "worldblitz": "святамгновения", "jeudéchecs": "джудэчак", "japanesechess": "японскіяшахі", "chinesechess": "китайскіяшахматы", "chesscanada": "шахматыканада", "fide": "фідэ", "xadrezverbal": "шахматыгаворка", "openings": "адкрыцці", "rook": "рук", "chesscom": "шахматыком", "calabozosydragones": "калабосыядраконы", "dungeonsanddragon": "підзямелліібяздраконаў", "dungeonmaster": "дн<PERSON>нмстр", "tiamat": "ціамат", "donjonsetdragons": "донжонызадраконов", "oxventure": "оксктурн", "darksun": "цямнысонца", "thelegendofvoxmachina": "легендавагасмахіны", "doungenoanddragons": "доўганосцаспраўжняказзяркоў", "darkmoor": "цёмныбярозаў", "minecraftchampionship": "minecraftчуство", "minecrafthive": "май<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftbedrock": "майнкрафтбэдрок", "dreamsmp": "дамадрывкi", "hermitcraft": "гермитк<PERSON><PERSON><PERSON>т", "minecraftjava": "майнкрафтджава", "hypixelskyblock": "hypixelskyblock", "minetest": "минетест", "hypixel": "гипиксель", "karmaland5": "карджылад5", "minecraftmods": "майнкрафтмоды", "mcc": "мцц", "candleflame": "полымясвечкі", "fru": "фру", "addons": "допоўненыя", "mcpeaddons": "mcpeдобавы", "skyblock": "небашына", "minecraftpocket": "майнкрафтпакет", "minecraft360": "minecraft360", "moddedminecraft": "мадфікаванымайнкрафт", "minecraftps4": "minecraftps4", "minecraftpc": "майнкрафтпк", "betweenlands": "міжзямеллі", "minecraftdungeons": "майнкрафтпадзямеллі", "minecraftcity": "майнкрафтгорад", "pcgamer": "пкгеймер", "jeuxvideo": "ігравыявідэа", "gambit": "gambit", "gamers": "геймеры", "levelup": "павышайраўніўм", "gamermobile": "геймермобіль", "gameover": "геймавер", "gg": "гг", "pcgaming": "кампутарныяигры", "gamen": "гулім", "oyunoynamak": "ойнаёмнаваць", "pcgames": "пкгульні", "casualgaming": "кэжуальнаеграванне", "gamingsetup": "ігравыбыток", "pcmasterrace": "пкмайстры", "pcgame": "пкгульня", "gamerboy": "геймерскіхлопец", "vrgaming": "vrгульні", "drdisrespect": "доктарнедапушчальнасць", "4kgaming": "4kgaming", "gamerbr": "геймерб", "gameplays": "гульнявыяпрацэсы", "consoleplayer": "консольныгеймер", "boxi": "бокси", "pro": "прафі", "epicgamers": "эпічныягеймеры", "onlinegaming": "ан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нг", "semigamer": "семігеймер", "gamergirls": "геймеркі", "gamermoms": "геймермамі", "gamerguy": "геймерскіхлопец", "gamewatcher": "геймвочар", "gameur": "геймер", "grypc": "грыпц", "rangugamer": "рангугеймер", "gamerschicas": "геймершчыцы", "otoge": "атоге", "dedsafio": "дэдсапіо", "teamtryhard": "камандападцягніся", "mallugaming": "молл<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нг", "pawgers": "павэргеры", "quests": "квесты", "alax": "алакс", "avgn": "авгн", "oldgamer": "старыгеймер", "cozygaming": "казамарнаеіграванне", "gamelpay": "gamelpay", "juegosdepc": "пкгульні", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "канкурэнтнасцьгульняў", "minecraftnewjersey": "майнкрафтньюджэрсі", "faker": "фэйкер", "pc4gamers": "pc4геймераў", "gamingff": "гульнявыяраскладкі", "yatoro": "яторочка", "heterosexualgaming": "гетэрасэксуальныяигры", "gamepc": "гульнявыпс", "girlsgamer": "девчынкігеймеры", "fnfmods": "фнфмоды", "dailyquest": "штодзённыквест", "gamegirl": "гульняваядзяўка", "chicasgamer": "чоркігеймеры", "gamesetup": "ігравынастаноўка", "overpowered": "пераўладкаваны", "socialgamer": "сацыяльныгеймер", "gamejam": "геймджам", "proplayer": "праплеер", "roleplayer": "рольгравец", "myteam": "маякаманда", "republicofgamers": "рэспублікагеймераў", "aorus": "<PERSON><PERSON><PERSON>", "cougargaming": "кагар<PERSON><PERSON><PERSON><PERSON>инг", "triplelegend": "тройчылегенда", "gamerbuddies": "геймерскіясябры", "butuhcewekgamers": "баўцамкцвеагеймераў", "christiangamer": "хрысціянскігеймер", "gamernerd": "геймернард", "nerdgamer": "нердгеймер", "afk": "афк", "andregamer": "андрег<PERSON>ймер", "casualgamer": "кэжуалгеймер", "89squad": "89каманда", "inicaramainnyagimana": "ініцарамаінягімана", "insec": "незахаваны", "gemers": "гемеры", "oyunizlemek": "гледзетьгульні", "gamertag": "геймерскі_тэг", "lanparty": "лан<PERSON><PERSON><PERSON><PERSON>і", "videogamer": "відэагульняр", "wspólnegranie": "супольнаеигрэнне", "mortdog": "мортдог", "playstationgamer": "плэйстэйшнгеймер", "justinwong": "джастынвонг", "healthygamer": "здаровыигравец", "gtracing": "гтр<PERSON><PERSON><PERSON><PERSON><PERSON>", "notebookgamer": "блокнотгеймер", "protogen": "пратоген", "womangamer": "жанчыныгеймеры", "obviouslyimagamer": "канешнеягеймер", "mario": "mario", "papermario": "папермарыё", "mariogolf": "марыягольф", "samusaran": "самусаран", "forager": "збіральнік", "humanfallflat": "чалавекупадзеравальне", "supernintendo": "супернітэнда", "nintendo64": "nintendo64", "zeroescape": "нулеваяўтеча", "waluigi": "валуіджы", "nintendoswitch": "нітэн<PERSON><PERSON><PERSON><PERSON><PERSON>ч", "nintendosw": "нітэндосв", "nintendomusic": "нінтэндомузыка", "sonicthehedgehog": "сонікзаядзіка", "sonic": "сонік", "fallguys": "осеньскідзвяркі", "switch": "змянісць", "zelda": "зелда", "smashbros": "смэшкабратоў", "legendofzelda": "легендападзеяўзэльды", "splatoon": "сплатун", "metroid": "метроид", "pikmin": "пік<PERSON>iн", "ringfit": "кольц<PERSON><PERSON><PERSON>г", "amiibo": "аміба", "megaman": "мегамен", "majorasmask": "мажарасамаск", "mariokartmaster": "марыёкартмайстар", "wii": "віі", "aceattorney": "асцярожнысуддзя", "ssbm": "ssbm", "skychildrenofthelight": "небапушыяўсвятла", "tomodachilife": "томадачыжыццё", "ahatintime": "асаблівасцяхдругойполові", "tearsofthekingdom": "слёзыкаралеўства", "walkingsimulators": "хаджэннесимулятары", "nintendogames": "нінтэндаигры", "thelegendofzelda": "легендаабзэльда", "dragonquest": "драконяванне", "harvestmoon": "урожайнымесяц", "mariobros": "марыябразькі", "runefactory": "рунафабрыка", "banjokazooie": "банжоказуі", "celeste": "цэлестэ", "breathofthewild": "душадыкайпрыроды", "myfriendpedro": "мойсябарпэдра", "legendsofzelda": "легендызэльды", "donkeykong": "долбарос", "mariokart": "марыёкарт", "kirby": "кірбі", "51games": "51гульняў", "earthbound": "зямлянам", "tales": "казкі", "raymanlegends": "райманлегенды", "luigismansion": "люїгізмэншн", "animalcrosssing": "жывёлаперакрэсьліванне", "taikonotatsujin": "вайнацягнік", "nintendo3ds": "nintendo3ds", "supermariobros": "супермарыёбраткі", "mariomaker2": "mariomaker2", "boktai": "бок<PERSON><PERSON>i", "smashultimate": "смаксультімат", "nintendochile": "nintendoчылі", "tloz": "tloz", "trianglestrategy": "трохкутнікістратэгіі", "supermariomaker": "супермарыёмакер", "xenobladechronicles3": "ксаноблейдхронік3", "supermario64": "супермарыё64", "conkersbadfurday": "клатчыбялюдскіндзінь", "nintendos": "нінтэндос", "new3ds": "новы3дс", "donkeykongcountry2": "долгавалакастны2", "hyrulewarriors": "героігірула", "mariopartysuperstars": "марыяпартынерды", "marioandsonic": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>к", "banjotooie": "бан<PERSON><PERSON><PERSON><PERSON>й", "nintendogs": "ніндзяпсы", "thezelda": "зэльда", "palia": "палія", "marioandluigi": "марыоядужык", "mariorpg": "марыярпг", "zeldabotw": "зельдаботв", "yuumimain": "юумімэйн", "wildrift": "вультавырыў", "riven": "рывен", "ahri": "ахры", "illaoi": "илл<PERSON>oi", "aram": "арам", "cblol": "цблол", "leagueoflegendslas": "лігагерояўлас", "urgot": "ургот", "zyra": "зыра", "redcanids": "чырвоныяканіды", "vanillalol": "ванільнылол", "wildriftph": "wildriftby", "lolph": "лолпф", "leagueoflegend": "лігалегенд", "tốcchiến": "токчына", "gragas": "грагас", "leagueoflegendswild": "лігаёнкаўвагані", "adcarry": "рэкламаёзды", "lolzinho": "ллолзинка", "leagueoflegendsespaña": "лігадылегендаўіспанія", "aatrox": "аатрокс", "euw": "еўк", "leagueoflegendseuw": "лігагерояўеў", "kayle": "кайла", "samira": "саміра", "akali": "акалі", "lunari": "лу<PERSON>ры", "fnatic": "фнатык", "lollcs": "лулзаўкі", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "milio", "shaco": "шака", "ligadaslegendas": "лігадзаскандаламі", "gaminglol": "ігр<PERSON><PERSON><PERSON><PERSON><PERSON>", "nasus": "насус", "teemo": "тэма", "zedmain": "зедмейн", "hexgates": "гексвароты", "hextech": "гексатэх", "fortnitegame": "фарт<PERSON>йтгейм", "gamingfortnite": "геймінгдля<PERSON><PERSON><PERSON><PERSON><PERSON>йт", "fortnitebr": "фартнайтбразілія", "retrovideogames": "ретраигры", "scaryvideogames": "страшныявідэагульні", "videogamemaker": "відэагемейкер", "megamanzero": "мегаманнуль", "videogame": "відэагульня", "videosgame": "відэагульні", "professorlayton": "прафес<PERSON><PERSON><PERSON><PERSON><PERSON>тан", "overwatch": "агляд", "ow2": "аў2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblockтеатр", "arcades": "аркеды", "acnh": "acnh", "puffpals": "пухлыясябры", "farmingsimulator": "симулятарфермерства", "robloxchile": "роблексчылі", "roblox": "роблокс", "robloxdeutschland": "robloxбеларусь", "robloxdeutsch": "robloxбеларусь", "erlc": "erlc", "sanboxgames": "пясочніцкіхгульняў", "videogamelore": "гульнявыярайоны", "rollerdrome": "ролердром", "parasiteeve": "паразіткавывечар", "gamecube": "геймкуб", "starcraft2": "старкрафт2", "duskwood": "дuskwood", "dreamscape": "снягасвіт", "starcitizen": "зорныграмадзянін", "yanderesimulator": "яндерэсімулятар", "grandtheftauto": "грандкраўдзікаўтэа", "deadspace": "мертваепрастора", "amordoce": "салодкаекаханне", "videogiochi": "відэагульні", "theoldrepublic": "старырэспубліка", "videospiele": "відэагульні", "touhouproject": "тохапраект", "dreamcast": "дрымкаст", "adventuregames": "прыгодніцкіягульні", "wolfenstein": "вульфен<PERSON>тайн", "actionadventure": "экшнпрыгода", "storyofseasons": "історыясезонаў", "retrogames": "ретрагульні", "retroarcade": "ретроаркейт", "vintagecomputing": "вінтажныякампютары", "retrogaming": "рэла<PERSON><PERSON><PERSON><PERSON><PERSON>нг", "vintagegaming": "вінтажныяігры", "playdate": "гульнявыдня", "commanderkeen": "камандзіркіна", "bugsnax": "багзнэкс", "injustice2": "несправядлівасць2", "shadowthehedgehog": "shadowтаязяжака", "rayman": "райман", "skygame": "небагульня", "zenlife": "зенжыццё", "beatmaniaiidx": "бітманіяiidx", "steep": "крутэзна", "mystgames": "гульні<PERSON>", "blockchaingaming": "блака<PERSON><PERSON>йнгейминг", "medievil": "сярэднявечча", "consolegaming": "кансольныягульні", "konsolen": "кансолен", "outrun": "бяжынаперад", "bloomingpanic": "разквітнелыпаніка", "tobyfox": "tobyfox", "hoyoverse": "хойовэрс", "senrankagura": "сенранкагура", "gaminghorror": "гульнявоеўжасаў", "monstergirlquest": "манстр<PERSON><PERSON><PERSON>чкіквест", "supergiant": "суперг<PERSON><PERSON>ант", "disneydreamlightvalle": "диснеісонечныдзённік", "farmingsims": "фермярскісімулятор", "juegosviejos": "старыягульні", "bethesda": "бэтэсда", "jackboxgames": "jackboxgames", "interactivefiction": "інтэрактыўнаяфантастыка", "pso2ngs": "псо2нпгс", "grimfandango": "грымфанданга", "thelastofus2": "апошнізнас2", "amantesamentes": "амантаз<PERSON>бараць", "visualnovel": "візуальнаянавэла", "visualnovels": "візуальныяраманы", "rgg": "ргг", "shadowolf": "шадоўольф", "tcrghost": "tcrghost", "payday": "платежныдень", "chatherine": "катарына", "twilightprincess": "святлавечнапрынцэса", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "пясочніца", "aestheticgames": "эстэтычныягульні", "novelavisual": "раманавізуал", "thecrew2": "каманда2", "alexkidd": "алекскід", "retrogame": "ретрагейм", "tonyhawkproskater": "тоніхокпрошкейтэр", "smbz": "smbz", "lamento": "ламэнт", "godhand": "багатаярука", "leafblowerrevolution": "рэвалюцыявецадзьмухтоўкі", "wiiu": "вііу", "leveldesign": "узровеньдизайн", "starrail": "стар<PERSON><PERSON><PERSON>л", "keyblade": "ключавымеч", "aplaguetale": "аплака<PERSON>гейл", "fnafsometimes": "фнафкалінекалі", "novelasvisuales": "візуальныянавіны", "robloxbrasil": "роблаксбразіл", "pacman": "пакман", "gameretro": "геймерскіностальгія", "videojuejos": "відэагульні", "videogamedates": "відэагульнявыядаты", "mycandylove": "майяцукроваялюбоў", "megaten": "мегатан", "mortalkombat11": "морталкомбат11", "everskies": "эвэрскайз", "justcause3": "простатак3", "hulkgames": "халкгульні", "batmangames": "батман<PERSON>гры", "returnofreckoning": "вяртаннеразплаты", "gamstergaming": "гамстэрг<PERSON><PERSON>минг", "dayofthetantacle": "деньта<PERSON>нтала", "maniacmansion": "бязумныдома", "crashracing": "крэшрэ<PERSON><PERSON><PERSON>нг", "3dplatformers": "3дплатформеры", "nfsmw": "нфсмв", "kimigashine": "кімінгашыне", "oldschoolgaming": "старашкольнапрыгоды", "hellblade": "пекельныклінок", "storygames": "гульнідлягісторый", "bioware": "біяварэ", "residentevil6": "рэжысёрвяль6", "soundodger": "звукадзіра", "beyondtwosouls": "замежабольшыхдушаў", "gameuse": "гульнявыкарыстальнік", "offmortisghost": "адкосмярцянскагадуха", "tinybunny": "маленькізаяц", "retroarch": "ретроарх", "powerup": "паветраныбуст", "katanazero": "катаназеро", "famicom": "фамікон", "aventurasgraficas": "графічныяпрыгоды", "quickflash": "хуткіспалах", "fzero": "фзэро", "gachagaming": "гатчыгейминг", "retroarcades": "ретроаркады", "f123": "ф123", "wasteland": "пустка", "powerwashsim": "симулятармыйкі", "coralisland": "каралевыастраў", "syberia3": "сыберыя3", "grymmorpg": "грыммартфпг", "bloxfruit": "блоксфрут", "anotherworld": "іншасвет", "metaquest": "метаквест", "animewarrios2": "анімэвайндзі2", "footballfusion": "футбольныфюжн", "edithdlc": "эдзітдлц", "abzu": "абзу", "astroneer": "астронір", "legomarvel": "легочароўна", "wranduin": "вранд<PERSON><PERSON>н", "twistedmetal": "закручанаяметалка", "beamngdrive": "beamngdrive", "twdg": "твдг", "pileofshame": "кучасарому", "simulator": "сімулятар", "symulatory": "симулятары", "speedrunner": "спідра<PERSON><PERSON>р", "epicx": "эпікx", "superrobottaisen": "суперроба<PERSON><PERSON><PERSON><PERSON>н", "dcuo": "dcuo", "samandmax": "самідан<PERSON>а<PERSON>с", "grywideo": "грывідэа", "gaiaonline": "гaя<PERSON><PERSON><PERSON><PERSON><PERSON>н", "korkuoyunu": "кorkуойну", "wonderlandonline": "wonderlandonline", "skylander": "скандар", "boyfrienddungeon": "хлопецдэмон", "toontownrewritten": "toontownперанапісаны", "simracing": "симрэ<PERSON><PERSON><PERSON>нг", "simrace": "симр<PERSON>йс", "pvp": "пвп", "urbanchaos": "урадлівыхаос", "heavenlybodies": "небесныяцелы", "seum": "сім", "partyvideogames": "партыйныявідэагульні", "graveyardkeeper": "клад<PERSON><PERSON><PERSON>чашчык", "spaceflightsimulator": "космічнысімулятар", "legacyofkain": "спадчынакайна", "hackandslash": "хак<PERSON>юшкi", "foodandvideogames": "ежаі<PERSON>гры", "oyunvideoları": "бужанараўнікавідэа", "thewolfamongus": "воўкпаміжнас", "truckingsimulator": "грузавысямулятар", "horizonworlds": "горызонтсветаў", "handygame": "досуггра", "leyendasyvideojuegos": "легендыгульняў", "oldschoolvideogames": "старыягульні", "racingsimulator": "симулятаргонкi", "beemov": "bee<PERSON>v", "agentsofmayhem": "агентыхаоса", "songpop": "песнапоп", "famitsu": "фаміц<PERSON>у", "gatesofolympus": "парталыалімпу", "monsterhunternow": "монстрпаляўнічыцяперашняга", "rebelstar": "бунтарскаязорка", "indievideogaming": "індзігульні", "indiegaming": "індз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нг", "indievideogames": "індзігульні", "indievideogame": "індыегульня", "chellfreeman": "чэлфрымэн", "spidermaninsomniac": "чалавекпавукінсомніяк", "bufffortress": "бункернаяпадпорка", "unbeatable": "незламны", "projectl": "праектл", "futureclubgames": "клуббудучыніигры", "mugman": "чалавекзкубка", "insomniacgames": "інсомнікамгульні", "supergiantgames": "суперграндыяйгры", "henrystickman": "хенрыстикмэн", "henrystickmin": "генрыстыкмін", "celestegame": "цэстэргейм", "aperturescience": "апертурнаянаука", "backlog": "заданні", "gamebacklog": "гульнявызадзел", "gamingbacklog": "геймингбэклаг", "personnagejeuxvidéos": "персанажыгульняў", "achievementhunter": "паляўнічасцяжкасцяў", "cityskylines": "гарадскіясілкіні", "supermonkeyball": "супермонкібал", "deponia": "дэпунія", "naughtydog": "непрыстойнысабака", "beastlord": "бяссмертнызвер", "juegosretro": "ретроігры", "kentuckyroutezero": "кентукіруткzero", "oriandtheblindforest": "оріаніслепымлесце", "alanwake": "аланв<PERSON><PERSON>к", "stanleyparable": "станліпараблі", "reservatoriodedopamin": "рэзервуардопаміна", "staxel": "стаксэль", "videogameost": "гульнявысаундтрэкі", "dragonsync": "дра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>к", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ялюблюкфхв", "arcanum": "арканум", "neoy2k": "нео2000", "pcracing": "пцгоначныя", "berserk": "бязумства", "baki": "билі", "sailormoon": "сейлормун", "saintseiya": "святыясейя", "inuyasha": "інай<PERSON><PERSON>a", "yuyuhakusho": "юю<PERSON><PERSON><PERSON><PERSON><PERSON>с", "initiald": "ініціялd", "elhazard": "эльхазард", "dragonballz": "драго<PERSON>бо<PERSON>з", "sadanime": "сумнаяанімэ", "darkerthanblack": "цёплешыцямнога", "animescaling": "анімескейліng", "animewithplot": "анімэзіспавядамі", "pesci": "песці", "retroanime": "ретроаніме", "animes": "анімэ", "supersentai": "суперсэнтай", "samuraichamploo": "самура<PERSON>шамплу", "madoka": "мадока", "higurashi": "х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>й", "80sanime": "80санімэ", "90sanime": "90сярэдзінаваанімэ", "darklord": "чорныгаспадар", "popeetheperformer": "попеэтыпадзея", "masterpogi": "мастэрпогі", "samuraix": "самурайксы", "dbgt": "dbgt", "veranime": "веравням", "2000sanime": "анімэ2000х", "lupiniii": "лупінііі", "drstoneseason1": "доктаркаменьсезон1", "rapanime": "рэпаніме", "chargemanken": "зара<PERSON><PERSON><PERSON>льнікі", "animecover": "анімэкавера", "thevisionofescaflowne": "бачаннеэскафлоўна", "slayers": "слэйеры", "tokyomajin": "тоўкімадзіна", "anime90s": "анімэ90х", "animcharlotte": "ані<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ка", "gantz": "gantz", "shoujo": "шодзё", "bananafish": "бананавырыба", "jujutsukaisen": "джуд<PERSON><PERSON>цукаісен", "jjk": "дж<PERSON><PERSON><PERSON>", "haikyu": "ха<PERSON><PERSON>ю", "toiletboundhanakokun": "тоалетнабяздольныханакокун", "bnha": "бнха", "hellsing": "хэл<PERSON><PERSON>нг", "skipbeatmanga": "скіпбітымanga", "vanitas": "ванітаз", "fireforce": "агненнаясіла", "moriartythepatriot": "морыяртыпацан", "futurediary": "будучынядзённік", "fairytail": "феерічнаяняня", "dorohedoro": "дарохедороу", "vinlandsaga": "вінландсага", "madeinabyss": "зробленаўабісы", "parasyte": "паразіт", "punpun": "пунпун", "shingekinokyojin": "шынгекінокйодзін", "mushishi": "мушышы", "beastars": "бісцары", "vanitasnocarte": "ванітаснакарце", "mermaidmelody": "мелодыярусалкі", "kamisamakiss": "камісамацыраўка", "blmanga": "блмanga", "horrormanga": "жах<PERSON><PERSON>anga", "romancemangas": "рамантычныямангі", "karneval": "карнаваль", "dragonmaid": "драконіца", "blacklagoon": "чорнаялагуна", "kentaromiura": "кентароміура", "mobpsycho100": "mobпсіх100", "terraformars": "тэраформары", "geniusinc": "ген<PERSON><PERSON><PERSON><PERSON>к", "shamanking": "ша<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "куроканобаскет", "jugo": "жугo", "bungostraydogs": "бунгасцягдоўгi", "jujustukaisen": "жужастукайсэна", "jujutsu": "джүйцюцу", "yurionice": "юрыанадворы", "acertainmagicalindex": "некімагічныіндэкс", "sao": "сао", "blackclover": "чорныкончык", "tokyoghoul": "tokyoghoul", "onepunchman": "адзіныпартунаўзлом", "hetalia": "гетылянія", "kagerouproject": "кэйгераўпраект", "haikyuu": "гайку", "toaru": "тоару", "crunchyroll": "кранчыролл", "aot": "аот", "sk8theinfinity": "скейтнабясконцасць", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "шпіянасямя", "rezero": "рэзера", "swordartonline": "swordartonline", "dororo": "дорыро", "wondereggpriority": "вандэрядзяпрыярытэт", "angelsofdeath": "анёлысмерці", "kakeguri": "какегури", "dragonballsuper": "драконіяшарысупер", "hypnosismic": "гіпнасімерык", "goldenkamuy": "золатаякамую", "monstermusume": "монстрмусумэ", "konosuba": "каносуба", "aikatsu": "айкатсу", "sportsanime": "спортыўнаеанімэ", "sukasuka": "сукесінка", "arwinsgame": "арв<PERSON>нграм", "angelbeats": "ан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>с", "isekaianime": "ізекайанімэ", "sagaoftanyatheevil": "сагадатанятворчасцю", "shounenanime": "шунэнанімэ", "bandori": "бандоры", "tanya": "таня", "durarara": "дюрарара", "prettycure": "прыгожаяпамачь", "theboyandthebeast": "хлопеціливечча", "fistofthenorthstar": "кулакпаўночнайзоркі", "mazinger": "маз<PERSON><PERSON><PERSON><PERSON>р", "blackbuttler": "чорныбатлер", "towerofgod": "вежапана", "elfenlied": "эльфенлід", "akunohana": "аку<PERSON><PERSON>хна", "chibi": "чыбі", "servamp": "сервамп", "howtokeepamummy": "якпрадавацьмаму", "fullmoonwosagashite": "фуллмунвосагашытэ", "shugochara": "шугачара", "tokyomewmew": "токіёмямям", "gugurekokkurisan": "гугурэкакурысана", "cuteandcreepy": "мілаятаксампаранажны", "martialpeak": "мартальнаеўзгоре", "bakihanma": "бакаханма", "hiscoregirl": "высокіскоргорла", "orochimaru": "орочимару", "mierukochan": "міерука<PERSON>ан", "dabi": "<PERSON><PERSON><PERSON><PERSON>", "johnconstantine": "д<PERSON><PERSON>нканстан<PERSON><PERSON>н", "astolfo": "астолфа", "revanantfae": "рэванантфея", "shinji": "шындзі", "zerotwo": "нульдва", "inosuke": "іносуке", "nezuko": "незуко", "monstergirl": "ман<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "канае", "yone": "<PERSON>он", "mitsuki": "міцу<PERSON>і", "kakashi": "какашы", "lenore": "ленор", "benimaru": "бэнімару", "saitama": "сайтама", "sanji": "санзі", "bakugo": "бакуго", "griffith": "грыфіт", "ririn": "рырын", "korra": "корава", "vanny": "ванны", "vegeta": "вегета", "goromi": "гаромі", "luci": "ла<PERSON><PERSON><PERSON>", "reigen": "рэйген", "scaramouche": "скарамуш", "amiti": "амiц<PERSON>", "sailorsaturn": "sailorsaturn", "dio": "дзіва", "sailorpluto": "сейларплутон", "aloy": "алой", "runa": "руна", "oldanime": "старыяанімэ", "chainsawman": "чылевалабут", "bungoustraydogs": "бунгасцягдогаў", "jogo": "джа<PERSON>у", "franziska": "франзіска", "nekomimi": "некйомімі", "inumimi": "інумімі", "isekai": "ісэкай", "tokyorevengers": "токіярэвенджарс", "blackbutler": "чорныратмант", "ergoproxy": "эргапраксі", "claymore": "клеймор", "loli": "лолі", "horroranime": "жаханімэ", "fruitsbasket": "фруткарзина", "devilmancrybaby": "дяба<PERSON><PERSON>укплакаты", "noragami": "норагамі", "mangalivre": "мангал<PERSON>бера", "kuroshitsuji": "кюрошицудзі", "seinen": "сэйнин", "lovelive": "жывімк爱", "sakuracardcaptor": "сакуракаардынатар", "umibenoetranger": "умібенатрэнджар", "owarinoseraph": "аварынасераф", "thepromisedneverland": "абяцанаяземля", "monstermanga": "монстрманага", "yourlieinapril": "тваябрыдкатыпаправе", "buggytheclown": "багісклоун", "bokunohero": "бокунохіро", "seraphoftheend": "серафімканца", "trigun": "тригун", "cyborg009": "кіборг009", "magi": "магі", "deepseaprisoner": "глыбокаводнызняволены", "jojolion": "jojo<PERSON>", "deadmanwonderland": "сьветмёртвыхчалвекаў", "bannafish": "ба<PERSON><PERSON><PERSON><PERSON>", "sukuna": "сукана", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "хусбу", "sugurugeto": "сугуругета", "leviackerman": "левіакермана", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "пандорасэрцаў", "yoimiya": "йоіміям", "foodwars": "бітва<PERSON><PERSON>ы", "cardcaptorsakura": "картазахапіццясакура", "stolas": "столас", "devilsline": "дзіявалінейка", "toyoureternity": "длацябеўвечнасць", "infpanime": "інфпаніме", "eleceed": "элесід", "akamegakill": "акам<PERSON>га<PERSON><PERSON>л", "blueperiod": "блакітныперыяд", "griffithberserk": "грыфітберсэрк", "shinigami": "шы<PERSON>мiгі", "secretalliance": "сакрэтнаесаюзніцтва", "mirainikki": "мір<PERSON><PERSON>н<PERSON><PERSON>і", "mahoutsukainoyome": "махоцукайнояме", "yuki": "юкі", "erased": "ануляваны", "bluelock": "блаукак", "goblinslayer": "гоблінакалупка", "detectiveconan": "детективконан", "shiki": "шікі", "deku": "дэкю", "akitoshinonome": "акіт<PERSON><PERSON>ынанавам", "riasgremory": "ріасгрэморы", "shojobeat": "шоябіты", "vampireknight": "вампірскірыцар", "mugi": "мугі", "blueexorcist": "блакітныэкзорцыст", "slamdunk": "слэмданк", "zatchbell": "зачпель", "mashle": "мяш<PERSON>ё<PERSON><PERSON><PERSON>", "scryed": "скрыд", "spyfamily": "шпіянасямейка", "airgear": "хавацьвецер", "magicalgirl": "магічнаядзяўчынка", "thesevendeadlysins": "сімсмертныхгрэхаў", "prisonschool": "школазняволення", "thegodofhighschool": "богшколы", "kissxsis": "цалуся<PERSON><PERSON><PERSON><PERSON>й", "grandblue": "грандблю", "mydressupdarling": "маядзяўкадорога", "dgrayman": "дг<PERSON><PERSON><PERSON><PERSON><PERSON>н", "rozenmaiden": "розенмэідзен", "animeuniverse": "анімэсусвет", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "саабрыджаная", "hoshizora": "хошызыора", "dragonballgt": "драк<PERSON>набалгт", "bocchitherock": "бочкацiрок", "kakegurui": "какеґуруі", "mobpyscho100": "мобпсіх100", "hajimenoippo": "хадзіменаіппо", "undeadunluck": "недзеядушнасць", "romancemanga": "раманчыкамanga", "blmanhwa": "блманхва", "kimetsunoyaba": "кімэцуноябэ", "kohai": "каха<PERSON>", "animeromance": "анімэрамансе", "senpai": "сэмпай", "blmanhwas": "блманхвы", "animeargentina": "анімэаргенціна", "lolicon": "лолікон", "demonslayertothesword": "дэмонслаердамячоразуму", "bloodlad": "кроўбосаў", "goodbyeeri": "пакаейэры", "firepunch": "падпальвае", "adioseri": "адзіосери", "tatsukifujimoto": "тацукiфудзiмота", "kinnikuman": "кинни<PERSON>у<PERSON>ан", "mushokutensei": "мукокутэнсэй", "shoujoai": "шадзёншлюб", "starsalign": "зорачкінаместе", "romanceanime": "романтычнаеанімэ", "tsundere": "цундэрэ", "yandere": "яндерэ", "mahoushoujomadoka": "махоўшоўджамадока", "kenganashura": "кэнга<PERSON>шура", "saointegralfactor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cherrymagic": "сакурачароўня", "housekinokuni": "хатасінемакаўні", "recordragnarok": "запісвацьрагарок", "oyasumipunpun": "оясуміпунпун", "meliodas": "меліодас", "fudanshi": "фуданшы", "retromanga": "ретроманга", "highschoolofthedead": "вучобаўзмёртвых", "germantechno": "германскятэхна", "oshinoko": "ошынако", "ansatsukyoushitsu": "ансад<PERSON><PERSON><PERSON>іёсцюцу", "vindlandsaga": "віндландсага", "mangaka": "мангака", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "прынцтэнісу", "tonikawa": "тонікава", "esdeath": "эсдэс", "dokurachan": "докурачан", "bjalex": "бялэкс", "assassinclassroom": "класзасасінаў", "animemanga": "анімеманга", "bakuman": "бакуман", "deathparade": "досвітчыцатрупаў", "shokugekinosouma": "шокугэкіносоумо", "japaneseanime": "японскаеанімэ", "animespace": "анімэпростор", "girlsundpanzer": "дзяўчатыўтанкулюдзі", "akb0048": "акб0048", "hopeanuoli": "кахайзнаўлі", "animedub": "анімедаб", "animanga": "аніманга", "tsurune": "цюрнэ", "uqholder": "uqholder", "indieanime": "індзіанімэ", "bungoustray": "бунгўстрай", "dagashikashi": "дага<PERSON>ыкашы", "gundam0": "гундом0", "animescifi": "анімэнавука", "ratman": "ратмэн", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "некобой", "gashbell": "gashbell", "peachgirl": "перскаваядзявочына", "cavalieridellozodiaco": "кавалерызадзіяка", "mechamusume": "мехамусумэ", "nijigasaki": "ніджігасакі", "yarichinbitchclub": "ярчынскiбiчклуб", "dragonquestdai": "драгонквестдай", "heartofmanga": "сэрцаб<PERSON><PERSON><PERSON>anga", "deliciousindungeon": "вкусняшкіўпадзямеллі", "manhviyaoi": "манхв<PERSON>я<PERSON>й", "recordofragnarok": "запісраганарога", "funamusea": "фунатэатры", "hiranotokagiura": "гіра<PERSON>т<PERSON><PERSON><PERSON>г<PERSON>ура", "mangaanime": "мангааніме", "bochitherock": "бочытакова", "kamisamahajimemashita": "камісамахайдзімэшта", "skiptoloafer": "скачышнавафери", "shuumatsunovalkyrie": "шума<PERSON><PERSON><PERSON>навалкіріі", "tutorialistoohard": "туторызанадтаскладныя", "overgeared": "празмернаабсталяваны", "toriko": "торыко", "ravemaster": "равемастэр", "kkondae": "ккондае", "chobits": "чобіцы", "witchhatatelier": "ведзьмінакаптураatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангат<PERSON>унолён", "kamen": "камень", "mangaislife": "манг<PERSON><PERSON><PERSON><PERSON>ьжыццё", "dropsofgod": "краплібога", "loscaballerosdelzodia": "рыцарызадзіака", "animeshojo": "анімешодзі", "reverseharem": "адваротныгараем", "saintsaeya": "святыясейя", "greatteacheronizuka": "выдатнынастаўніконідзука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "салдат", "mybossdaddy": "мойшэфдзіця", "gear5": "gear5", "grandbluedreaming": "грандавоемарыццаванне", "bloodplus": "кроўплюс", "bloodplusanime": "кроўплюсанімэ", "bloodcanime": "кроўаніme", "bloodc": "кровьc", "talesofdemonsandgods": "казкіпраманоўібагоў", "goreanime": "гурэанімэ", "animegirls": "анімэгаблінкі", "sharingan": "шар<PERSON>нган", "crowsxworst": "вароныхудшыя", "splatteranime": "splatteranime", "splatter": "сплэтэр", "risingoftheshieldhero": "падёмшчытагероя", "somalianime": "самалійскаеанімэ", "riodejaneiroanime": "рыаджанейраанімэ", "slimedattaken": "слезьзабраная", "animeyuri": "анімеюры", "animeespaña": "анімэіспанія", "animeciudadreal": "анімэсіудадрэал", "murim": "мурым", "netjuunosusume": "нетюносусумэ", "childrenofthewhales": "дзіцятывалакі", "liarliar": "брацьнябрат", "supercampeones": "суперкампёны", "animeidols": "анімешныяідолы", "isekaiwasmartphone": "ісэкайбылсмартфонам", "midorinohibi": "мідор<PERSON>нах<PERSON>бы", "magicalgirls": "магічныядзяўчаты", "callofthenight": "звонкахалявы", "bakuganbrawler": "бакуганбраўлер", "bakuganbrawlers": "бакуганбраўлеры", "natsuki": "нацукі", "mahoushoujo": "махошоўджаў", "shadowgarden": "таннысад", "tsubasachronicle": "цубасачр<PERSON>нік", "findermanga": "знайдзімангу", "princessjellyfish": "прынцэсжэлеўка", "kuragehime": "курэжымэ", "paradisekiss": "парадызныцалау", "kurochan": "кюра<PERSON>ан", "revuestarlight": "ревюэстарасвіт", "animeverse": "анімэсвет", "persocoms": "пэрсакомы", "omniscientreadersview": "усезнаўцаўчытальскаявідзежка", "animecat": "анімэкат", "animerecommendations": "парадыпаанімэ", "openinganime": "адкрыццёанімэ", "shinichirowatanabe": "сінічыроўатанабэ", "uzumaki": "узумаки", "myteenromanticcomedy": "мойпадлеткавырамантычныкамедыя", "evangelion": "эвангелион", "gundam": "gundam", "macross": "ма<PERSON><PERSON><PERSON><PERSON>", "gundams": "гундмы", "voltesv": "вальтэсв", "giantrobots": "гіганцкіяробаты", "neongenesisevangelion": "неонгенаэсісэвангеліён", "codegeass": "кодгяс", "mobilefighterggundam": "мобільныбатлерggundam", "neonevangelion": "неаневангеліон", "mobilesuitgundam": "мобільныкамплектгандзім", "mech": "меч", "eurekaseven": "еўракасемь", "eureka7": "еўкера7", "thebigoanime": "вялікіяанімэ", "bleach": "белы", "deathnote": "смерцьзапіска", "cowboybebop": "кабоябип", "jjba": "джаджабра", "jojosbizarreadventure": "жыццязабаўляюццаджоджо", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "лед", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "камуйято", "militaryanime": "ваенныанімэ", "greenranger": "зеленыйратнік", "jimmykudo": "джымікудо", "tokyorev": "токіорэва", "zorro": "зорра", "leonscottkennedy": "леонскоткэнедзі", "korosensei": "коросэнсэй", "starfox": "старфокс", "ultraman": "ультраман", "salondelmanga": "салондеманга", "lupinthe3rd": "лепіны3й", "animecity": "анімэмузэй", "animetamil": "аніметаміле", "jojoanime": "дзядзькадзядзька", "naruto": "наруто", "narutoshippuden": "нарутошыппудэн", "onepiece": "адзіныфрагмент", "animeonepiece": "анімэадночастка", "dbz": "дб<PERSON>", "dragonball": "драко<PERSON>ball", "yugioh": "югіёҳ", "digimon": "дзіжымон", "digimonadventure": "дзігімапрыгода", "hxh": "hxх", "highschooldxd": "высшаяшколадзд", "goku": "goku", "broly": "броуці", "shonenanime": "шонаэнімэ", "bokunoheroacademia": "бокуногероакадэмія", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "дракосці", "kimetsunoyaiba": "кімэцунояйба", "shonenjump": "шан<PERSON>н<PERSON><PERSON><PERSON><PERSON>п", "otaka": "отака", "hunterxhunter": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "mha": "мха", "demonslayer": "дэманабіваў", "hinokamikagurademonsl": "хінакалёкдэмана", "attackontitan": "ата<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на", "erenyeager": "ерэнья<PERSON>ар", "myheroacademia": "маіхераўакадэмія", "boruto": "боруто", "rwby": "rwby", "dandadan": "дан<PERSON>а<PERSON><PERSON><PERSON>", "tomodachigame": "тамада<PERSON><PERSON><PERSON><PERSON><PERSON>м", "akatsuki": "акацукі", "surveycorps": "атраддаследавання", "onepieceanime": "анімэадзіночка", "attaquedestitans": "атаканастытанаў", "theonepieceisreal": "адзінышматрэальнащы", "revengers": "мяснікі", "mobpsycho": "мобпсіхазор", "aonoexorcist": "akhonaэксарцыст", "joyboyeffect": "эффектшчаслівца", "digimonstory": "дзігімонпавяданне", "digimontamers": "дзігімонтамеры", "superjail": "суперзняволеньне", "metalocalypse": "металакалапс", "shinchan": "шынчан", "watamote": "ватамотэ", "uramichioniisan": "урамічоніінасан", "uruseiyatsura": "уруйсейяцурам", "gintama": "gintama", "ranma": "ранма", "doraemon": "дораэмон", "gto": "gto", "ouranhostclub": "нашыанхостклаб", "flawlesswebtoon": "бездакорнывебтун", "kemonofriends": "кеманофрэўндз", "utanoprincesama": "утанапрынцэсама", "animecom": "анім<PERSON>кам", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юк<PERSON>юна", "nichijou": "нічыё", "yurucamp": "юрукемп", "nonnonbiyori": "ннннбйоры", "flyingwitch": "летаючыяведзьмы", "wotakoi": "вотакаі", "konanime": "кананіме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "простатак", "horimiya": "горымія", "allsaintsstreet": "усіхсвятыхвуліца", "recuentosdelavida": "перажыванніжыцця"}