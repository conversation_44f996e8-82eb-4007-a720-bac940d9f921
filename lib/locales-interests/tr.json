{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astroloji", "cognitivefunctions": "bilişselfonksiyonlar", "psychology": "psikoloji", "philosophy": "felsefe", "history": "tarih", "physics": "fizik", "science": "bilim", "culture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "languages": "diller", "technology": "teknoloji", "memes": "<PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "mbtimizahları", "astrologymemes": "astrolojimizahları", "enneagrammemes": "enneagrammizahları", "showerthoughts": "duşdüşünceleri", "funny": "komik", "videos": "videolar", "gadgets": "araçlar", "politics": "siyaset", "relationshipadvice": "ilişkitavsiyesi", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "kripto", "news": "haberler", "worldnews": "d<PERSON><PERSON>hab<PERSON>leri", "archaeology": "a<PERSON><PERSON><PERSON><PERSON>", "learning": "öğrenme", "debates": "tartışmalar", "conspiracytheories": "komploteorileri", "universe": "evren", "meditation": "meditasyon", "mythology": "<PERSON><PERSON><PERSON>", "art": "sanat", "crafts": "elsanatları", "dance": "dans", "design": "tasarım", "makeup": "makyaj", "beauty": "güzellik", "fashion": "moda", "singing": "şarkısöyleme", "writing": "<PERSON><PERSON>ma", "photography": "fotoğrafçılık", "cosplay": "cosplay", "painting": "boyama", "drawing": "<PERSON><PERSON><PERSON>", "books": "kitaplar", "movies": "filmler", "poetry": "ş<PERSON>r", "television": "televizyon", "filmmaking": "filmyapımı", "animation": "animasyon", "anime": "anime", "scifi": "bilimkurgu", "fantasy": "<PERSON><PERSON><PERSON>", "documentaries": "belgeseller", "mystery": "gizem", "comedy": "komedi", "crime": "polisiye", "drama": "dram", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "korku", "romance": "romantik", "realitytv": "realityshow", "action": "aksiyon", "music": "m<PERSON><PERSON><PERSON>", "blues": "blues", "classical": "klasik", "country": "countrymüzik", "desi": "<PERSON><PERSON><PERSON>", "edm": "edm", "electronic": "elektronik", "folk": "folklör", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tekno", "travel": "seyahat", "concerts": "kons<PERSON><PERSON>", "festivals": "festivaller", "museums": "<PERSON><PERSON><PERSON><PERSON>", "standup": "komedi", "theater": "tiyatro", "outdoors": "d<PERSON>şmekanlar", "gardening": "bahçıvanlık", "partying": "partilemek", "gaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgames": "kutuoyunları", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "yemek", "baking": "p<PERSON><PERSON><PERSON><PERSON>", "cooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetarian": "vejet<PERSON><PERSON>", "vegan": "vegan", "birds": "k<PERSON><PERSON>lar", "cats": "<PERSON><PERSON>", "dogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fish": "balık", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "blacklivesmatter", "environmentalism": "çevrecilik", "feminism": "feminizm", "humanrights": "insanhakları", "lgbtqally": "lgbtqally", "stopasianhate": "asyalınefretidurdur", "transally": "transally", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "sporlar", "badminton": "badminton", "baseball": "beyzbol", "basketball": "basketbol", "boxing": "boks", "cricket": "kriket", "cycling": "bisikletebinme", "fitness": "fitness", "football": "futbol", "golf": "golf", "gym": "<PERSON><PERSON><PERSON><PERSON>", "gymnastics": "<PERSON><PERSON><PERSON><PERSON>", "hockey": "hokey", "martialarts": "dövüşsanatları", "netball": "netbol", "pilates": "pilates", "pingpong": "ma<PERSON><PERSON><PERSON>", "running": "koşu", "skateboarding": "kaykay", "skiing": "kayak", "snowboarding": "snowboard<PERSON><PERSON><PERSON>", "surfing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swimming": "<PERSON><PERSON><PERSON><PERSON>", "tennis": "tenis", "volleyball": "voleybol", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "tüplüdalış", "hiking": "doğayürüyüşü", "capricorn": "<PERSON><PERSON><PERSON>", "aquarius": "kova", "pisces": "balık", "aries": "koç", "taurus": "b<PERSON><PERSON><PERSON>", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "yenge<PERSON>", "leo": "aslan", "virgo": "ba<PERSON><PERSON>", "libra": "<PERSON><PERSON>i", "scorpio": "akrep", "sagittarius": "yay", "shortterm": "kısasüreliilişki", "casual": "rastgeleilişki", "longtermrelationship": "uzunvadeliilişki", "single": "bekar", "polyamory": "çokeslilik", "enm": "tekpartnerdeğil", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "<PERSON><PERSON><PERSON><PERSON>", "lesbian": "lezbiyen", "bisexual": "biseksüel", "pansexual": "panseksüel", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "kızılölümödülleri2", "dragonage": "dragonage", "assassinscreed": "suikastçılarcreed", "saintsrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "kırmız<PERSON><PERSON>hlar", "kingsquest": "kralınseferi", "soulreaver": "r<PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "spyrol<PERSON><PERSON><PERSON>i", "rouguelikes": "roguelikelar", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "ejderhansözü", "sunsetoverdrive": "günbatımıhızlandırması", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemdestanı", "yokaiwatch": "yokaizamanı", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildsavaşları", "openworld": "açıkdünya", "heroesofthestorm": "fırtınanınkahramanları", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON>i", "planescape": "planedünyası", "lordsoftherealm2": "realmınlordları2", "baldursgate": "baldurskapısı", "colorvore": "renklerdünyası", "medabots": "medabotlar", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersifsimler", "okage": "okage", "juegoderol": "rolo<PERSON>", "witcher": "witcher", "dishonored": "onurkırıcı", "eldenring": "elden<PERSON>", "darksouls": "karanlı<PERSON><PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "çöküş", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modlama", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "dald<PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyeskiokul", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON>or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "aşkaşık", "otomegames": "otomeoyunları", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "zamanınocarinası", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiremasquerade", "dimension20": "boyut20", "gaslands": "gazülkeleri", "pathfinder": "yolbulucu", "pathfinder2ndedition": "pathfinder2sezon", "shadowrun": "g<PERSON>lgekoşusu", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "yerçekimheyecanı", "rpg": "r<PERSON><PERSON><PERSON>", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "efendi", "yourturntodie": "sırasendekiölüm", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "maraude<PERSON>ar", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgmetin", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON>", "mu": "ben", "falloutshelter": "sığınaktakalıntılar", "gurps": "gurps", "darkestdungeon": "enkaranlıkzindanı", "eclipsephase": "güneştutulmasıdönemi", "disgaea": "disgaea", "outerworlds": "dışdünya", "arpg": "arpg", "crpg": "krpg", "bindingofisaac": "isaacinbağlanması", "diabloimmortal": "diabloimmortal", "dynastywarriors": "hanedansavaşçıları", "skullgirls": "i̇skeletkızlar", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartsmirası", "madnesscombat": "deliligeşidiş", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "yol96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikelar", "gothamknights": "gothamdüştüleri", "forgottenrealms": "unutulmuşkrallıklar", "dragonlance": "ejderhamızrağı", "arenaofvalor": "arenadevleri", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "şehirtiyatro", "childoflight": "ışıkçocuk", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "canavarçiftliği", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanverse", "fracturedthrones": "kır<PERSON>ktahtlar", "horizonforbiddenwest": "horizonyasakbatı", "twewy": "twewy", "shadowpunk": "gölgepunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "yoket", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "yıldızbulucu", "goldensun": "altıngüneş", "divinityoriginalsin": "ilahiorijinalgünah", "bladesinthedark": "karanlıktakikenarlar", "twilight2000": "alacakaranlık2000", "sandevistan": "sandevistan", "cyberpunk": "siberpunk", "cyberpunk2077": "siberpunk2077", "cyberpunkred": "siberpunkkırmızı", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "düşmüşsipariş", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "şeytanyolları", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON>hayatta", "oldschoolrunescape": "eskiokulrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON>", "pf2": "pf2", "farmrpg": "çiftlikrpg", "oldworldblues": "eskininmel<PERSON><PERSON><PERSON><PERSON>", "adventurequest": "maceraavı", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "roloyunları", "roleplayinggames": "roloyunları", "finalfantasy9": "finalfantasy9", "sunhaven": "güneşcennet", "talesofsymphonia": "symph<PERSON><PERSON><PERSON><PERSON>", "honkaistarrail": "honka<PERSON>rayol<PERSON>", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON>", "myfarog": "benimfarogum", "sacredunderworld": "sacrédunderland", "chainedechoes": "zincirdekankalar", "darksoul": "karanlıkruha", "soulslikes": "ruhbenzer<PERSON>i", "othercide": "diğerkıyamet", "mountandblade": "dağvekılıç", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "zamanlayıcı", "pillarsofeternity": "sonsuzluğunpilları", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "sekizyolseyyahı", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "küçükorman", "childrenofmorta": "mortaçocukları", "engineheart": "motorkalp", "fable3": "masal3", "fablethelostchapter": "masalkayıpbölüm", "hiveswap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "r<PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurskapıları", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "yıldızlanmış", "oldschoolrevival": "eskiokuluyandırma", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "vahşidünya", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "krallıkgقلب1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "karanlıkhücre", "juegosrpg": "juegosrpg", "kingdomhearts": "krallıkkalpleri", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "çılgınkalpler", "bastion": "kale", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "arcadianıngökyüzü", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplika", "gnosia": "gnosia", "pennyblood": "pennykanı", "breathoffire4": "ateşsoluması4", "mother3": "anne3", "cyberpunk2020": "siberpunk2020", "falloutbos": "çökmüşbos", "anothereden": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "roloyunları", "roleplaygame": "rolo<PERSON>", "fabulaultima": "ha<PERSON>sonuç", "witchsheart": "cadınınkalbi", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "yolbulurrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "şeytanlık", "spelljammer": "<PERSON><PERSON><PERSON>ü<PERSON><PERSON><PERSON>", "dragonageorigins": "dragonageköken<PERSON>i", "chronocross": "zamançarpışması", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "canavaravcıdünyası", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforumtr", "shadowheartscovenant": "gölgekalplercovenant", "bladesoul": "bı<PERSON>ks<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "krallıkgeliyor", "awplanet": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "ka<PERSON>bettimdragalia", "elderscroll": "geçmişsayfa", "dyinglight2": "ölümsüzışık2", "finalfantasytactics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "dükkanşövalyeleri", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "dünyaşansı", "blackbook": "siyah<PERSON><PERSON>", "skychildrenoflight": "gökyüzüışıkçocukları", "gryrpg": "gryrpg", "sacredgoldedition": "kutsalgoldversiyonu", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "<PERSON><PERSON><PERSON>", "scarletnexus": "kızılbağlantı", "ghostwiretokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "oyunrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "bağımsızrpg", "pointandclick": "noktayıveşıkla", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "emilydefansi̇z", "indivisible": "b<PERSON><PERSON>ün<PERSON>z", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7sonsuzkriz", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "ölümtorontoyanada", "palladium": "palyadyum", "knightjdr": "şövalyejdr", "monsterhunter": "canavardavcısı", "fireemblem": "ateşsimgesi", "genshinimpact": "genshinimpact", "geosupremancy": "yerüstünlüğü", "persona5": "persona5", "ghostoftsushima": "tsushimadakihayalet", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "canavaryýü<PERSON>elişi", "nier": "nier", "dothack": "hackle", "ys": "ys", "souleater": "ruhsavaşçısı", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktikselrpg", "mahoyo": "<PERSON><PERSON><PERSON><PERSON>", "animegames": "animeoyunları", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "yemekavcısı", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON>ata", "princessconnect": "prensesbağlantısı", "hexenzirkel": "b<PERSON><PERSON><PERSON><PERSON><PERSON>lerçevresi", "cristales": "kristaller", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valoranthinturk", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootball", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "espor", "mlg": "mlg", "leagueofdreamers": "hayalperestlerligi", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "efootballtr", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "overwatchligi", "cybersport": "siberolarak", "crazyraccoon": "çılgınrakun", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "yarışçı", "brasilgameshow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorantcompetitive": "valorantrekabet", "t3arena": "t3arena", "valorantbr": "valoranttr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "yarımhayat", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valf", "portal": "portal", "teamfortress2": "takımkalesi2", "everlastingsummer": "sonsuzyaz", "goatsimulator": "keçisimülatörü", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "özgürlükgezegeni", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "savaşalanı4", "nightinthewoods": "ormangecesi", "halflife2": "yarımhayat2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvaniyalar", "overcooked": "yanmış", "interplanetary": "gezegenlerarası", "helltaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7gün2gün", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "foxhole", "stray": "sokak", "battlefield": "savaşalanı", "battlefield1": "savaşalanı1", "swtor": "swtor", "fallout2": "yıkım2", "uboat": "uboat", "eyeb": "gözb", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nc<PERSON>", "partyhard": "partiylee", "hardspaceshipbreaker": "zoruzaygemisiarkadaşım", "hades": "hades", "gunsmith": "silahçı", "okami": "<PERSON>ami", "trappedwithjester": "şakacıylakapalısın", "dinkum": "dinkum", "predecessor": "öncekiler", "rainworld": "yağmurdünyası", "cavesofqud": "qudmağaraları", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "savaşınşafağı", "minionmasters": "minyonusta", "grimdawn": "karanlıkg<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "karanlıvedahakaranlık", "motox": "motoxtr", "blackmesa": "blackmesa", "soulworker": "ruhçalışanı", "datingsims": "flörtoyunları", "yaga": "yaga", "cubeescape": "küpkaçış", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON>ş<PERSON><PERSON>", "citiesskylines": "şehirlerinufukları", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "sanalkenopisi", "snowrunner": "karkaçakçı", "libraryofruina": "kütüphanedekiölüm", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "omegastrikers", "wayfinder": "yolbulucu", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "sakinplastikördek", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON>", "supermeatboy": "süperetadam", "tinnybunny": "tinnybunny", "cozygrove": "rah<PERSON><PERSON>", "doom": "kıyamet", "callofduty": "callofduty", "callofdutyww2": "callofdutyiiidünyaयुद्ध", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kod", "borderlands": "sınırtoprakları", "pubg": "pubg", "callofdutyzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6kuşatma", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "uzakcry", "farcrygames": "farcryoyunları", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "yeryüzükorumaordusu", "huntshowdown": "avaşov<PERSON>", "ghostrecon": "hayaletoperasyonu", "grandtheftauto5": "grandtheftauto5", "warz": "savaşz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "takımakatıl", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "ayaklanmafırtınası", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "kiralikkatil3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ölümkıyameti", "b4b": "b4b", "codwarzone": "codsavaşzona", "callofdutywarzone": "callofdutywarzone", "codzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisions2", "killzone": "ölümzonu", "helghan": "hel<PERSON>", "coldwarzombies": "soğukzombiyavaşları", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "uçuşsavaşları", "crosscode": "çaprazkod", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "keskinnişancıelite", "modernwarfare": "modernsavaş", "neonabyss": "neonçukur", "planetside2": "gezegenlerüstü2", "mechwarrior": "mekanikkavga", "boarderlands": "sınırdışı", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "tarkovdankaçış", "metalslug": "metalçizgi", "primalcarnage": "ilkelsoykırım", "worldofwarships": "dünyadangiçatışmalar", "back4blood": "back4blood", "warframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6siege": "rainbow6kuşatması", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "kütleseletki", "systemshock": "sistemşoku", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "ma<PERSON><PERSON>inceleme", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eri", "farcry4": "farcry4", "gearsofwar": "savaşçılarveekipmanlar", "mwo": "mwo", "division2": "bölüm2", "tythetasmaniantiger": "tytaygermelbo", "generationzero": "sıfırnesil", "enterthegungeon": "silahlaradakil", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "siyahgizli1", "sausageman": "sosisadam", "ratchetandclank": "ratchetveclank", "chexquest": "chexquest", "thephantompain": "hayaletacı", "warface": "savaşyüzü", "crossfire": "kavgaçekişme", "atomicheart": "atomikkalp", "blackops3": "siyahoperasyon3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "özgürlük", "battlegrounds": "savaşalanları", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "oyunpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearözgürlükçocukları", "juegosfps": "fpsoyunları", "convertstrike": "dönüştürsaldırısı", "warzone2": "warzone2", "shatterline": "şatterline", "blackopszombies": "siyah<PERSON><PERSON><PERSON><PERSON>", "bloodymess": "kanlıkaos", "republiccommando": "cumhuriyetkomandosu", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "asker", "groundbranch": "groundbranch", "squad": "ekip", "destiny1": "destiny1tr", "gamingfps": "oyunfps", "redfall": "kızılbaş", "pubggirl": "pubggirl", "worldoftanksblitz": "tankdünyasıblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "askerealındım", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "zırhlıçekirdek", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinasonwonderlandları", "halo2": "halo2", "payday2": "ödemegünü2", "cs16": "cs16", "pubgindonesia": "pubgindonez<PERSON>", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromanya", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sabu<PERSON>d", "ghostcod": "hayaletkod", "csplay": "csplay", "unrealtournament": "gerçeküstüturnuvası", "callofdutydmz": "callofdutydmz", "gamingcodm": "oyuncodm", "borderlands2": "sınırlands2", "counterstrike": "karşısaldırı", "cs2": "cs2", "pistolwhip": "tabancayısallamak", "callofdutymw2": "callofdutymw2", "quakechampions": "depremşampiyonları", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "parçalanmışhücre", "neonwhite": "neonbeyaz", "remnant": "<PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "savaşdünyası", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "gölgeadam", "quake2": "deprem2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "kırmızıölü", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "savaşalanı3", "lostark": "kaybolanşark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "pas", "conqueronline": "feth<PERSON><PERSON><PERSON>", "dauntless": "cesur", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "ej<PERSON><PERSON><PERSON>_gün<PERSON>", "warthunder": "savaşgökkuşağı", "flightrising": "uçuşyükselişi", "recroom": "recroom", "legendsofruneterra": "runeterraefsaneleri", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantastikyıldızçevrimiçi2", "maidenless": "maidenless", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "çizikçizik", "agario": "agario", "secondlife": "ikincihayat", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtayonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "kızılölümçevrimiçi", "superanimalroyale": "süperhayvankralığı", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "şövalyeonline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "isaa<PERSON>ınbağlanması", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>ük", "newworld": "yenidünya", "blackdesertonline": "siyahçölçevrimiçi", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "korsan101", "honorofkings": "onurunkralları", "fivem": "fivem", "starwarsbattlefront": "starwarsbattleground", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3ds<PERSON><PERSON>", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasik", "worldofwarcraft": "d<PERSON><PERSON><PERSON>ıwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "ya<PERSON><PERSON>mınkoru", "riotmmo": "riyotmmo", "silkroad": "ipekyolı", "spiralknights": "spiralknightlar", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "intikam", "albiononline": "albiononline", "bladeandsoul": "bıçakvesoul", "evony": "evony", "dragonsprophet": "dragonlarınpeygamberi", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "çokoyunculu", "angelsonline": "<PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcüniversonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarseskiyoldaşlık", "grandfantasia": "büyükfantazi", "blueprotocol": "maviprotocol", "perfectworld": "mükemmelbirdünya", "riseonline": "onlineyükseliş", "corepunk": "çekirdekpunk", "adventurequestworlds": "maceraoyunları", "flyforfun": "uçmakiçinkeyif", "animaljam": "hayvanjam", "kingdomofloathing": "tutmacaülk<PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalcombat", "streetfighter": "sokakdövişçisi", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "şerefi̇çin", "tekken": "tekken", "guiltygear": "suçluçizgi", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "sokakdövüşçüsü6", "multiversus": "çokdünya", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "ruhayarışı", "brawlhalla": "brawlhalla", "virtuafighter": "virtuasavaşçı", "streetsofrage": "sokaklarındansözler", "mkdeadlyalliance": "mkölümcüli̇ttifak", "nomoreheroes": "artıkkahramanyok", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "dövüşçülerinşahı", "likeadragon": "birejder<PERSON><PERSON><PERSON>", "retrofightinggames": "retrodövüşoyunları", "blasphemous": "küfretebilir", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvcapcom", "supersmash": "süpersmash", "mugen": "mugen", "warofthemonsters": "canavarsavaşları", "jogosdeluta": "mücadeleoyunları", "cyberbots": "siberbotlar", "armoredwarriors": "zırhlısavaşçılar", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "güçekipmanları", "beatemup": "dayakçı", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "savaşoyunları", "killerinstinct": "<PERSON><PERSON><PERSON>çg<PERSON>d<PERSON>ü", "kingoffigthers": "dövüşlerkralı", "ghostrunner": "hayaletkoşucu", "chivalry2": "şövalyelik2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "ipekimsişi<PERSON><PERSON>ül", "silksonggame": "silksongoyunu", "silksongnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksong": "i̇pekşarkısı", "undernight": "gecealtı", "typelumina": "typelum<PERSON>", "evolutiontournament": "e<PERSON><PERSON>", "evomoment": "evomoment", "lollipopchainsaw": "şekerlügüçkılıcı", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "bloodborne", "horizon": "ufuk", "pathofexile": "s<PERSON>rg<PERSON>npath", "slimerancher": "slimerancher", "crashbandicoot": "çökmüşbandicoot", "bloodbourne": "kanladoğmuş", "uncharted": "keşfedilmemiş", "horizonzerodawn": "horizonzeroşafak", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "kötüşöhretli", "playstationbuddies": "playstationarkadaşları", "ps1": "ps1", "oddworld": "garipdünya", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "haydikopalım", "gta4": "gta4", "gta": "gta", "roguecompany": "asişirket", "aisomniumfiles": "aisomniumdosyaları", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "tanrıkahramanı", "gris": "gri", "trove": "hazine", "detroitbecomehuman": "detroitins<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "<PERSON><PERSON>", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "geceyarısınakadar", "touristtrophy": "turistkupası", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "çarpışmatakımıyarışı", "fivepd": "beşpd", "tekken7": "tekken7", "devilmaycry": "şeytanlarağlamaz", "devilmaycry3": "şeytanlarağlamaz3", "devilmaycry5": "şeytanımsızağla5", "ufc4": "ufc4", "playingstation": "oyunistasyonu", "samuraiwarriors": "samuraisavaşçıları", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "ruhkılıcı", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "adamavcısı", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "gölgekalpleri2antlaşma", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "oyungeçişi", "armello": "armello", "partyanimal": "partihayvanı", "warharmmer40k": "savaşzırhı40k", "fightnightchampion": "dövüşgeceşampiyonu", "psychonauts": "psikonotlar", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "yaşlıdalarşeytanıskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "birlikteaçkalma", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "yı<PERSON><PERSON><PERSON><PERSON>ınulaşımı", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "amerikanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriex", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "krallıklarligı", "fable2": "masal2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "çöptv", "skycotl": "gökyüzücotl", "erica": "erica", "ancestory": "ataöyküsü", "cuphead": "kupahead", "littlemisfortune": "küçükşanssızlık", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "canavarkontesliği", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motosiklet", "outerwilds": "dışyıldızlar", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "koyuncukültü", "duckgame": "ördekoyunu", "thestanleyparable": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "<PERSON><PERSON><PERSON>", "longdrive": "uzunsürüş", "satisfactory": "tatminedici", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "yeraltında", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbaluzayprogramı", "kenshi": "kenshi", "spiritfarer": "ruhgemisi", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "golfit", "truthordare": "gerçekteventahmin", "game": "oyun", "rockpaperscissors": "taşkağıtmakas", "trampoline": "trambolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "avbulmaca", "yardgames": "bahçesoyunları", "pickanumber": "birnumarayılpick", "trueorfalse": "doğruyumuykülbeyaz", "beerpong": "bisoftası", "dicegoblin": "zarcihaylaz", "cosygames": "cosygames", "datinggames": "flörtoyunları", "freegame": "ücretsizoyun", "drinkinggames": "içkilioyunlar", "sodoku": "sudoku", "juegos": "oyunlar", "mahjong": "majong", "jeux": "oyunlar", "simulationgames": "simülasyonoyunları", "wordgames": "kelimeoyunları", "jeuxdemots": "kelimeoyunları", "juegosdepalabras": "kelimeoyunları", "letsplayagame": "haydioynayalım", "boredgames": "sıkıntıoyunları", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "bakışyarışmaları", "spiele": "oyunlar", "giochi": "oyunlar", "geoguessr": "geoguessr", "iphonegames": "iphoneoyunları", "boogames": "booyarışmaları", "cranegame": "kranoyunu", "hideandseek": "sa<PERSON><PERSON><PERSON>", "hopscotch": "<PERSON><PERSON><PERSON>", "arcadegames": "oyunmerkezi", "yakuzagames": "yakuzagames", "classicgame": "klas<PERSON><PERSON>", "mindgames": "zihinoyunları", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "romantiksavasi", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "dilbaşdanbacakları", "4xgames": "4xoyunları", "gamefi": "oyunfi", "jeuxdarcades": "oyunalanları", "tabletopgames": "masaüstüoyunları", "metroidvania": "metroidvania", "games90": "oyunlar90", "idareyou": "id<PERSON><PERSON>ms<PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "yarışoyunları", "ets2": "ets2", "realvsfake": "gerçekvsait", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "oyunçevrimiçi", "onlinegames": "çevrimtioyunlar", "jogosonline": "çevrimtiyemoyunlar", "writtenroleplay": "yazılırolplay", "playaballgame": "toplayarakoynamak", "pictionary": "pictionary", "coopgames": "koopoyunları", "jenga": "jenga", "wiigames": "wiigamestr", "highscore": "yüksekskor", "jeuxderôles": "roloyunları", "burgergames": "burgeroyunları", "kidsgames": "çocukoyunları", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwkarakutu", "jeuconcour": "oyunkazanç", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "oyun", "managementgame": "yönetimoyunu", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1oyunu", "citybuilder": "şehirsanalıç", "drdriving": "drdriving", "juegosarcade": "arcadesoyunları", "memorygames": "hafızaoyunları", "vulkan": "vulkan", "actiongames": "aksiyonoyunları", "blowgames": "şutoyunları", "pinballmachines": "pinball<PERSON><PERSON><PERSON><PERSON>", "oldgames": "eskiomalardevamkeşfet", "couchcoop": "kane<PERSON><PERSON><PERSON>", "perguntados": "sorularoosters", "gameo": "gameo", "lasergame": "laserkıralar", "imessagegames": "imessageoyunları", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "boşluklarıdoldur", "jeuxpc": "pcoyunlar", "rétrogaming": "retrooyun<PERSON>", "logicgames": "mantıkoyunları", "japangame": "japanga<PERSON>", "rizzupgame": "riz<PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "metrobüstahişi", "jeuxdecelebrite": "ünlüoyunlar", "exitgames": "çıkışoyunları", "5vs5": "5.00E+05", "rolgame": "rolo<PERSON>", "dashiegames": "dashiegames", "gameandkill": "oyunvekapat", "traditionalgames": "gelenekseloyunlar", "kniffel": "zaro<PERSON>", "gamefps": "oyunfps", "textbasedgames": "metinbazlıoyunlar", "gryparagrafowe": "gryparagrafı", "fantacalcio": "fantasfutbolu", "retrospel": "retrospil", "thiefgame": "<PERSON>ı<PERSON><PERSON><PERSON>yunu", "lawngames": "çimoyunları", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "masafutbolu", "tischfußball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spieleabende": "<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "oyunforum", "casualgames": "rahatoyunlar", "fléchettes": "okçuluk", "escapegames": "kaçışoyunları", "thiefgameseries": "hırsızoyunserisi", "cranegames": "kranoyunları", "játék": "boooyun", "bordfodbold": "bordfodbold", "jogosorte": "oyunçekilişi", "mage": "mage", "cargames": "arabcacaklar", "onlineplay": "çevrimiçioyun", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "çantaoyunları", "randomizer": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "oyunpc", "socialdeductiongames": "sosyalçıkmaoyunları", "dominos": "dominoş", "domino": "domino", "isometricgames": "izometrikoyunlar", "goodoldgames": "eskigüzeloyunlar", "truthanddare": "gerçevevecesarete", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "avcılaroyunları", "jeuxvirtuel": "sanaloyun<PERSON>", "romhack": "romhack", "f2pgamer": "f2<PERSON><PERSON><PERSON>", "free2play": "ücretsizoyna", "fantasygame": "fantas<PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvserileriveyapımlar", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "motorluherşey", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "kılıçvesihir", "goodgamegiving": "iyiyapıverme", "jugamos": "oynuyoruz", "lab8games": "lab8oyunlar", "labzerogames": "labzerogames", "grykomputerowe": "oyunbilgisayarları", "virgogami": "virgogami", "gogame": "oynayalım", "jeuxderythmes": "ritmoyunları", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "oyunmodlama", "crimegames": "suçoyunları", "dobbelspellen": "dobbelspellen", "spelletjes": "oyunlar", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "harfiyat", "singleplayer": "tekoyuncu", "coopgame": "koop<PERSON><PERSON>u", "gamed": "<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "şimdiçekiştir", "maingame": "maingame", "kingdiscord": "krallardiscord", "scrabble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schach": "<PERSON><PERSON><PERSON>", "shogi": "<PERSON><PERSON><PERSON>", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "tırnac", "onitama": "onitama", "pandemiclegacy": "pandemimirası", "camelup": "deveyarışı", "monopolygame": "monopolygamesesimi", "brettspiele": "masaoyunları", "bordspellen": "masaoyunları", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON>ş<PERSON><PERSON><PERSON><PERSON>", "planszowe": "oyunkeyfi", "risiko": "risiko", "permainanpapan": "tahtakalem", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "kötüduka", "bloodbowl": "kanlarbowl", "cluedo": "kluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "bağlantıbiriçin", "heroquest": "kahramançıktı", "giochidatavolo": "ma<PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karambol", "tablegames": "masaoyunları", "dicegames": "zaroyunları", "yatzy": "yatzy", "parchis": "par<PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "toplumsaloyunlar", "deskgames": "masaoyunları", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelkrizprotokolü", "cosmicencounter": "kozmikka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "oyunculuk", "tabletoproleplay": "masatabloyapımı", "cardboardgames": "kartontoyunlar", "eldritchhorror": "dışavurumkorku", "switchboardgames": "anahtartab<PERSON>lar", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "krallıkölümü", "yahtzee": "yahtzee", "chutesandladders": "kaydıraklarvémerdiven<PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "kırmızıboyunhayatı", "boardom": "sıkıntıdanboğulmak", "applestoapples": "elmadandolmalara", "jeudesociété": "sosyalsözleşme", "gameboard": "oyunpani", "dominó": "domino", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "masaoyunları", "twilightimperium": "twilightimperium", "horseopoly": "atyarışları", "deckbuilding": "destekoluşturma", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "gölgelernoktasında", "kingoftokyo": "tokyoununhatırı", "warcaby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "oyunkartları", "battleship": "battleship", "tickettoride": "biletial", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "katana", "subbuteo": "subbuteo", "jeuxdeplateau": "masaoyunları", "stolníhry": "masaoyunları", "xiángqi": "<PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "sosyaloyunlar", "starwarslegion": "starwarslegion", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "topluyastakataşımacan", "terraria": "terarye", "dsmp": "dsmp", "warzone": "savaşzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "kimlikv", "theisle": "<PERSON><PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "erkeklerinyardışı", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "cthulhuçığlığı", "bendyandtheinkmachine": "bendyveinkma<PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "may<PERSON><PERSON>das<PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafted", "daysgone": "günlergeç<PERSON>", "fobia": "fobi", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patolojik", "zomboid": "zomboid", "northgard": "kuzeybahçesi", "7dtd": "7dtd", "thelongdark": "uzunkaranlık", "ark": "arkadaş", "grounded": "dirtte", "stateofdecay2": "çürümedurum2", "vrising": "vrising", "madfather": "delik<PERSON>z", "dontstarve": "açkalma", "eternalreturn": "sonsuzdönüş", "pathoftitans": "titanlarınyolu", "frictionalgames": "frictionalgames", "hexen": "cadı", "theevilwithin": "içimdekişey<PERSON>", "realrac": "gerçekyarış", "thebackrooms": "arkakodlar", "backrooms": "arkaodalar", "empiressmp": "imparatorluksmp", "blockstory": "b<PERSON><PERSON><PERSON><PERSON>", "thequarry": "taşocak", "tlou": "tlou", "dyinglight": "ölüışıklar", "thewalkingdeadgame": "<PERSON>ür<PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>meleri", "wehappyfew": "bizmutluyuz", "riseofempires": "imparatorluklarınyükselişi", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "<PERSON><PERSON><PERSON><PERSON>", "arksurvival": "<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "bar<PERSON><PERSON><PERSON><PERSON>", "breathedge": "breatheçizgi", "alisa": "alisa", "westlendsurvival": "batıdayaşamak", "beastsofbermuda": "bermudakabusları", "frostpunk": "buz<PERSON><PERSON>", "darkwood": "koyulukorusu", "survivalhorror": "<PERSON>attakalmakorkusu", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "oyundansonrakihayat", "survivalgames": "hayattakalmaoyunları", "sillenthill": "sillesondak", "thiswarofmine": "buşavaşbizim", "scpfoundation": "scpfoundation", "greenproject": "<PERSON>şil<PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "korkudanagaçma", "raft": "salıncak", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>ne<PERSON>", "residentevil5": "residentevil5", "deadpoly": "ölüpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nine", "littlenightmares2": "küçükkâbuslar2", "signalis": "signalis", "amandatheadventurer": "amandabirkaşif", "sonsoftheforest": "ormanlarınsonları", "rustvideogame": "rust<PERSON><PERSON><PERSON>", "outlasttrials": "dayanıkentavsiye", "alienisolation": "uzaylıyalnızlığı", "undawn": "günyansıra", "7day2die": "7gündeöl", "sunlesssea": "güneşsizdeniz", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON>", "propnight": "propgece", "deadisland2": "ölüadalar2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "ölümverse", "cataclysmdarkdays": "felaketkoyugü<PERSON><PERSON>", "soma": "soma", "fearandhunger": "korkuvehungry", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "hayattansonra", "ageofdarkness": "karanlıkçağı", "clocktower3": "clocktower3", "aloneinthedark": "karanlıktayalnız", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "projeni̇mbusoyunu", "eternights": "eternights", "craftopia": "elcraftopia", "theoutlasttrials": "theoutlastdenemeleri", "bunker": "sığınak", "worlddomination": "dünyayıelegeçirmek", "rocketleague": "r<PERSON><PERSON><PERSON>", "tft": "tft", "officioassassinorum": "ofiskatilleri", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "cüceavcısı", "warhammer40kcrush": "warhammer40kbağımlılığı", "wh40": "wh40", "warhammer40klove": "warhammer40ksev<PERSON>i", "warhammer40klore": "warhammer40kmit<PERSON>jisi", "warhammer": "savaşçarkı", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "intikam", "ilovesororitas": "sororitalarımıseviyorum", "ilovevindicare": "vindicareyiçokseviyorum", "iloveassasinorum": "assasinorumhaticim", "templovenenum": "templovenenum", "templocallidus": "temlocallidus", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40bin", "tetris": "tetris", "lioden": "lioden", "ageofempires": "imparatorlukzamanı", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerözgünyüzyıl", "civilizationv": "medeniyetv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "kanatuzunluğu", "terraformingmars": "marsıdönüştürme", "heroesofmightandmagic": "gücünvebüyününkahramanları", "btd6": "btd6", "supremecommander": "üstkomutan", "ageofmythology": "mitolojiyaşı", "args": "<PERSON><PERSON><PERSON><PERSON>", "rime": "şarkısohbeti", "planetzoo": "gezegenzoo", "outpost2": "öncülük2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "sezar3", "redalert": "kırmızıuyarı", "civilization6": "uygarlık6", "warcraft2": "warcraft2", "commandandconquer": "komutvefethet", "warcraft3": "warcraft3", "eternalwar": "sonsuzsavaş", "strategygames": "strate<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "medeniyetoyunu", "civilization4": "medeni̇yet4", "factorio": "factorio", "dungeondraft": "zindangeliş<PERSON>r", "spore": "spor", "totalwar": "toplamsavaş", "travian": "travian", "forts": "kale", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "evdünya", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "ışıktenhızlı", "forthekings": "k<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "g<PERSON><PERSON><PERSON><PERSON>andastrateji", "starctaft": "starctaft", "sidmeierscivilization": "sidmeiersuygarlığı", "kingdomtwocrowns": "kralyettirik<PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "kibir", "ww40k": "ww40k", "godhood": "tanrısallık", "anno": "anno", "battletech": "savaşteknolojisi", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveseglencelicebrikursu", "plagueinc": "vurgunoyunu", "theorycraft": "teoriyaratımı", "mesbg": "mesbg", "civilization3": "medeniyet3", "4inarow": "4tanesırayla", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "i̇lerisavaşlar", "ageofempires2": "ageofempires2", "disciples2": "müritler2", "plantsvszombies": "bitkilervezekalar", "giochidistrategia": "stratejikoyunlar", "stratejioyunları": "stratagemoyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "harikalarçağı", "dinosaurking": "dinozorkralı", "worldconquest": "dünyakontrolü", "heartsofiron4": "demirkalpliler4", "companyofheroes": "<PERSON><PERSON>manlar<PERSON>ği", "battleforwesnoth": "<PERSON><PERSON>not<PERSON><PERSON>insava<PERSON>", "aoe3": "aoe3", "forgeofempires": "imparatorluklarınızınşekillenmesi", "warhammerkillteam": "warhammeröldürebilmeekibi", "goosegooseduck": "ördekötegazoz", "phobies": "fobiler", "phobiesgame": "fobi̇yalaroyunu", "gamingclashroyale": "oyunclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "dışuçak", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bombacıadam", "ageofempires4": "imparatorlukçağı4", "civilization5": "medeni̇yet5", "victoria2": "victoria2", "crusaderkings": "hakim<PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "starwarsempireatalanıyor", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strateji", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "ustaçatışma", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "taşımacılıkefendisi", "unrailed": "yoldakalamadık", "magicarena": "magiarenası", "wolvesville": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "uçaklıkaçışşeytanlıktan", "uplandkingdoms": "yükseltilerkrallıkları", "galaxylife": "galaksihayatı", "wolvesvilleonline": "kurtlarşehiriçinonline", "slaythespire": "zirvayiçinçarparakgeç", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "gerçekyarış3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "kaybetsims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON>id<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "karartayazmaları", "phasmophobia": "hayaletkorkusu", "fivenightsatfreddys": "beşgecedefreddyler", "saiko": "saiko", "fatalframe": "ö<PERSON>ümkare", "littlenightmares": "küçükkabuslar", "deadrising": "ölümcanlanması", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "ev<PERSON><PERSON>", "deadisland": "ölüada", "litlemissfortune": "küçükşanssızbayan", "projectzero": "projezero", "horory": "korkuu", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "merhabakomşu2", "gamingdbd": "oyunmdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "korkuoyunları", "horrorgaming": "korkuoyunları", "magicthegathering": "büyüleriktiplere", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "insanlığakarşıkartlar", "cribbage": "cribbage", "minnesotamtg": "minnesotamtk", "edh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monte": "monte", "pinochle": "pinochle", "codenames": "kod<PERSON>mleri", "dixit": "dixit", "bicyclecards": "bisikletkartları", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "runeterafırtınası", "solitaire": "tekbaşına", "poker": "poker", "hearthstone": "kalptaşları", "uno": "uno", "schafkopf": "kuzuoyunu", "keyforge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardtricks": "kart<PERSON><PERSON><PERSON>", "playingcards": "kartoyunları", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netkoşucu", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "ticaretkartları", "pokemoncards": "pokemonkartları", "fleshandbloodtcg": "etvekantcg", "sportscards": "sporkartları", "cardfightvanguard": "kartmücadelesivanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON>", "warcry": "savaşşarkısı", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kalpkralları", "truco": "truko", "loteria": "loterya", "hanafuda": "hana<PERSON>da", "theresistance": "direniş", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkartları", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohoyunu", "darkmagician": "karanlıksihirbaz", "blueeyeswhitedragon": "mavilişgözlüyücedragon", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kartoy<PERSON>u", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "do<PERSON><PERSON>", "mtgcommander": "mtgkomutan", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kartoyunları", "mtgjudge": "mtgjüri", "juegosdecartas": "kartoyunları", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanlarınıyakala", "mtgpreconcommander": "mtgpreconkomutan", "kartenspiel": "kartoy<PERSON>u", "carteado": "kart<PERSON><PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "savaşruhlarıdestanı", "jogodecartas": "kartoy<PERSON>u", "žolíky": "şansıver", "facecard": "yüzkartı", "cardfight": "kartdövüşü", "biriba": "biriba", "deckbuilders": "deck<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelşampiyonları", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "skipo", "unstableunicorns": "dengesiztekboynuzlar", "cyberse": "si<PERSON>e", "classicarcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansoyunları", "fridaynightfunkin": "cumagecesiçekici", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projemirai", "projectdiva": "proje<PERSON><PERSON>", "djmax": "djmax", "guitarhero": "gitarkahramanı", "clonehero": "klonkahramanı", "justdance": "sadecedanset", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "ölümsüzlükdansı", "chunithm": "çunithm", "idolmaster": "idolmaster<PERSON>", "dancecentral": "dansmerkezi", "rhythmgamer": "ritmikgamer", "stepmania": "stepmania", "highscorerythmgames": "yüksekpuanritimoyunları", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritimcehennemi", "hypmic": "<PERSON><PERSON><PERSON>", "adanceoffireandice": "ateşvebuzdansı", "auditiononline": "çevrimdışıseçmeler", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ritimoyunları", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ritimdoktoru", "cubing": "küplük", "wordle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teniz": "teniz", "puzzlegames": "bulmacalar", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "mantıksoruları", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "beyinoyunları", "rubikscube": "rubiksküp", "crossword": "bulmacalar", "motscroisés": "kelimeavcıları", "krzyżówki": "bulmacalar", "nonogram": "nonogram", "bookworm": "kitapkurdu", "jigsawpuzzles": "bulmacalar", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "bulmaca", "riddles": "bulmacalar", "rompecabezas": "puzzle", "tekateki": "tekateki", "inside": "içeride", "angrybirds": "öfkeqartalları", "escapesimulator": "kaçışsimülatörü", "minesweeper": "mayıntarlası", "puzzleanddragons": "bulmacaveejderhaları", "crosswordpuzzles": "bulmacalar", "kurushi": "k<PERSON>hi", "gardenscapesgame": "bah<PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "bulmacaspor", "escaperoomgames": "kaçışoyunları", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dbulmaca", "homescapesgame": "homescapesoyunu", "wordsearch": "kelimebulmaca", "enigmistica": "enigmistika", "kulaworld": "kuladünya", "myst": "myst", "riddletales": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "balıkdünyası", "theimpossiblequiz": "imkansızquiz", "candycrush": "şekerpatlat", "littlebigplanet": "küçükbüyükgezegen", "match3puzzle": "match3bulmacası", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamadım", "kwirky": "<PERSON><PERSON><PERSON><PERSON>", "rubikcube": "rubik<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "küprubik", "yapboz": "puzzle", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoonoyunları", "cubosderubik": "rubikküpü", "cruciverba": "kelimebulmaca", "ciphers": "<PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "kelimeleryarışması", "buscaminas": "<PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "bulmacacılık", "turnipboy": "turpçocuk", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogramlar", "kostkirubika": "kostkirubika", "crypticcrosswords": "<PERSON><PERSON><PERSON><PERSON>_kelimeler", "syberia2": "syberia2", "puzzlehunt": "bulmacavadi", "puzzlehunts": "bulmacaavıları", "catcrime": "kedisuçları", "quebracabeça": "beyinqarıştırıcı", "hlavolamy": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "sonkamp<PERSON>ş<PERSON>", "autodefinidos": "kenditanımlayanlar", "picopark": "picopark", "wandersong": "gezginşarkı", "carto": "karto", "untitledgoosegame": "başlıksızkazoyunu", "cassetête": "kasetbükme", "limbo": "limbo", "rubiks": "rubik", "maze": "labirent", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "hızlıküp", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "portaldovez", "bilmece": "boobilmece", "puzzelen": "<PERSON><PERSON>", "picross": "picross", "rubixcube": "rubiksküp", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobil", "codm": "codm", "twistedwonderland": "kıvrıkkasırga", "monopoly": "monopoly", "futurefight": "gelecekleşime", "mobilelegends": "mobillegendler", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "yalnızkurdu", "gacha": "gacha", "wr": "wrd", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "takımyıldızları", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "hayatınsavaşı", "mycity": "şehrim", "arknights": "arknights", "colorfulstage": "renkliabet", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "şansbüyükkommando", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "şövalyerkoşusu", "fireemblemheroes": "fireemblemkahramanları", "honkaiimpact": "<PERSON><PERSON>iet<PERSON>i", "soccerbattle": "futbolsavaşı", "a3": "a3", "phonegames": "telefonoyunları", "kingschoice": "kralınseçimi", "guardiantales": "k<PERSON><PERSON><PERSON><PERSON><PERSON>i", "petrolhead": "petrolkafası", "tacticool": "taktikool", "cookierun": "cookieruntr", "pixeldungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "dışarıdakalmış", "craftsman": "usta", "supersus": "supersus", "slowdrive": "yavaşsürüş", "headsup": "<PERSON><PERSON><PERSON>", "wordfeud": "kelimelavaşımı", "bedwars": "ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı", "freefire": "freefire", "mobilegaming": "mobiloyunlar", "lilysgarden": "lilysbahçesi", "farmville2": "farmville2", "animalcrossing": "hayvangeçişi", "bgmi": "bgmi", "teamfighttactics": "takımdahtateknikleri", "clashofclans": "klanlararasiçatışma", "pjsekai": "pjsekai", "mysticmessenger": "mistikmesajcı", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8tophavuz", "emergencyhq": "acilmerkez", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdreamtr", "clashofclan": "clanlarınçarpışması", "starstableonline": "starstableonline", "dragonraja": "ejderhatajı", "timeprincess": "zamanprensesi", "beatstar": "beatstar", "dragonmanialegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "cepaşkı", "androidgames": "androidoyunları", "criminalcase": "suç<PERSON>va", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "meleklerligı", "lordsmobile": "lordsmobile", "tinybirdgarden": "küçükkuşbahçesi", "gachalife": "gachalife", "neuralcloud": "nöralbulut", "mysingingmonsters": "şarkıcanavalarım", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "ma<PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "savaşrobotları", "mirrorverse": "ayna<PERSON><PERSON>", "pou": "pou", "warwings": "savaşkanatları", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobil", "ingress": "<PERSON><PERSON><PERSON>", "slugitout": "savaşıbittik", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "cezalandırmagrayraven", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "kurt<PERSON>", "runcitygame": "ş<PERSON>rik<PERSON>şuo<PERSON>u", "juegodemovil": "mobiloyun", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "taklit", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "b<PERSON><PERSON><PERSON><PERSON>kovalamaca", "bombmebrasil": "bombmebrazilya", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "o<PERSON><PERSON><PERSON><PERSON>", "mindustry": "zihinendüstri", "callofdragons": "dragonlarınçağı", "shiningnikki": "parlayan_nikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "pathtonowhere", "sealm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowfight3": "gölgekapışması3", "limbuscompany": "limbuscompany", "demolitionderby3": "yıkımderbisi3", "wordswithfriends2": "kelimelerlearkadaşlar2", "soulknight": "ruhşövalyesi", "purrfecttale": "purr<PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "kızlarınpopüleri", "lolmobile": "lolmobil", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "mükemmelbirdünyamobil", "empiresandpuzzles": "imparatorluklarlavebulmacalarla", "empirespuzzles": "empirepuzzleları", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "kıcıkkâbus", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "misafirg<PERSON><PERSON>a<PERSON>ı", "eversoul": "ebersoul", "gunbound": "gunbound", "gamingmlbb": "oyunmlbb", "dbdmobile": "dbdmobile", "arknight": "arknights", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombicennetgöçmenleri", "eveechoes": "eveyankıları", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobillegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "kabalmobil", "streetfighterduel": "sokakdövüşçüsüduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "oyunbgmi", "girlsfrontline": "kızlaruyuzhatı", "jurassicworldalive": "jurassicdünyasalive", "soulseeker": "ruhavcısı", "gettingoverit": "bunuatlattım", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobiloyunlar", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "oyunmobill<PERSON><PERSON><PERSON>", "timeraiders": "zamanavadıları", "gamingmobile": "mobiloyun", "marvelstrikeforce": "marvelçarpışması", "thebattlecats": "mücadelekedileri", "dnd": "yokum", "quest": "macera", "giochidiruolo": "ruletoyunları", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "karanlıkdünya", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "rolo<PERSON>", "romanceclub": "romantikklub", "d20": "d20", "pokemongames": "pokemonoyunları", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonkırmızı", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "<PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemon<PERSON>", "ashketchum": "aşkeçakum", "gengar": "gengar", "natu": "<PERSON><PERSON><PERSON>", "teamrocket": "teamrocket", "furret": "furette", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "cep<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonpeluş", "teamystic": "ekibüyüsü", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "demireller", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokemonustası", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "çocuklarvepokémon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "parlakavcı", "ajedrez": "<PERSON><PERSON><PERSON>", "catur": "çatur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON><PERSON>", "schaken": "ş<PERSON>n", "skak": "skak", "ajedres": "<PERSON><PERSON><PERSON>", "chessgirls": "satrançkızları", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "dünyahemenhemen", "jeudéchecs": "<PERSON><PERSON><PERSON>", "japanesechess": "japonsatranç", "chinesechess": "çinşahmatı", "chesscanada": "satranctürkiye", "fide": "tohum", "xadrezverbal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openings": "açılışlar", "rook": "kanka", "chesscom": "satrancom", "calabozosydragones": "zindanveejderhalar", "dungeonsanddragon": "zindanlarveejderhalar", "dungeonmaster": "zindanustası", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxmacera", "darksun": "karanlıkgüneş", "thelegendofvoxmachina": "voxmachinanınefs<PERSON>si", "doungenoanddragons": "dndoyna", "darkmoor": "karanlıkmoor", "minecraftchampionship": "minecraftşampiyonası", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "<PERSON><PERSON><PERSON>", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodları", "mcc": "mcc", "candleflame": "mumalevrası", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftcepleri", "minecraft360": "minecraft360tr", "moddedminecraft": "moddedmc", "minecraftps4": "minecraftps4tr", "minecraftpc": "minecraftbilgisayar", "betweenlands": "aralarda", "minecraftdungeons": "minecraftzindanları", "minecraftcity": "minecraftşehri", "pcgamer": "pcgamer", "jeuxvideo": "oyunlar", "gambit": "gambit", "gamers": "oyuncular", "levelup": "sevi<PERSON><PERSON><PERSON>", "gamermobile": "gamermobil", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "pcoyunlari", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcoyunları", "casualgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcustalıları", "pcgame": "pcoyun", "gamerboy": "gamerçocuk", "vrgaming": "vroyunları", "drdisrespect": "drdisrespect", "4kgaming": "4kgamingtr", "gamerbr": "oyuncudost", "gameplays": "oynarakgeçirilenzamanlar", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "profesyonel", "epicgamers": "epikoyuncular", "onlinegaming": "çevrimiçioyun", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "oyuncukızlar", "gamermoms": "oyun<PERSON><PERSON><PERSON>", "gamerguy": "oyuncuyuz", "gamewatcher": "oyunizleyici", "gameur": "oyundelisi", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "oyunkızları", "otoge": "<PERSON><PERSON><PERSON><PERSON>", "dedsafio": "dedsafio", "teamtryhard": "takımçalışıyor", "mallugaming": "mallugaming", "pawgers": "pat<PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "ortal<PERSON>", "oldgamer": "eskiyarışçı", "cozygaming": "rahatoyunlaştırma", "gamelpay": "gamelpay", "juegosdepc": "pcoyunları", "dsswitch": "dsswitch", "competitivegaming": "rekabetçigaming", "minecraftnewjersey": "minecraftyenijersey", "faker": "faker", "pc4gamers": "pc4gamers", "gamingff": "<PERSON>yun<PERSON>olla", "yatoro": "yatörö", "heterosexualgaming": "heteroseksüeloyun", "gamepc": "oyunpc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmodları", "dailyquest": "günlükgörev", "gamegirl": "oyuncukız", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "aşırıgüçlenmiş", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "oyunjam", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "takımım", "republicofgamers": "oyuncularcumhur<PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "üçlegend", "gamerbuddies": "oyunarkadaşları", "butuhcewekgamers": "bayanlaroyunuycusunuz", "christiangamer": "hrist<PERSON><PERSON><PERSON><PERSON>", "gamernerd": "oyuncusalıldı", "nerdgamer": "nerdoyunucu", "afk": "afk", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89takım", "inicaramainnyagimana": "<PERSON>i<PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "insec", "gemers": "gamerlar", "oyunizlemek": "oyunizlemek", "gamertag": "oyuncuadı", "lanparty": "lan<PERSON><PERSON><PERSON>", "videogamer": "oyun<PERSON>", "wspólnegranie": "birlik<PERSON><PERSON><PERSON>", "mortdog": "mortdogtr", "playstationgamer": "playstationoyuncusu", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "<PERSON>ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "tabi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "keşifçi", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendo64": "nintendo64", "zeroescape": "sıfırakaçış", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomüzik", "sonicthehedgehog": "sonikteped<PERSON>z", "sonic": "sonik", "fallguys": "fallguys", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majörmask", "mariokartmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "büyükavukat", "ssbm": "ssbm", "skychildrenofthelight": "gökyüzüçocuklarıışığında", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "ahatinzamandız", "tearsofthekingdom": "krallığıngözyaşları", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>şsi<PERSON>ü<PERSON><PERSON><PERSON>", "nintendogames": "nintendoygames", "thelegendofzelda": "zeldanınefsaneleri", "dragonquest": "ejderharyağı", "harvestmoon": "hasatayı", "mariobros": "mario<PERSON>s", "runefactory": "rüntarımları", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "vahşetinnefesi", "myfriendpedro": "arkadaşımpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "eşekfong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON>", "earthbound": "yeryüzübağımlısı", "tales": "masallar", "raymanlegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariomaker2": "mariomaker2", "boktai": "boktay", "smashultimate": "smashultimate", "nintendochile": "nintendotürkiye", "tloz": "tloz", "trianglestrategy": "üçgenstrateji", "supermariomaker": "supermarioyapımcısı", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "süpermario64", "conkersbadfurday": "bucukcukkötügün", "nintendos": "nintendos", "new3ds": "yeni3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartytutkusüperstarları", "marioandsonic": "mariovesonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendoglar", "thezelda": "zelda", "palia": "palia", "marioandluigi": "mariovealugi", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "kırmızıcanavarlar", "vanillalol": "vanillalol", "wildriftph": "wildrifttr", "lolph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegend": "leagueoflegendtr", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswildtr", "adcarry": "adkapat", "lolzinho": "lol<PERSON>", "leagueoflegendsespaña": "leagueoflegendsispanya", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milyon", "shaco": "şako", "ligadaslegendas": "efsanelerlebağlantıda", "gaminglol": "oyunşakası", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexkapıları", "hextech": "hextech", "fortnitegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingfortnite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fortnitebr": "fortnitebr", "retrovideogames": "retrooyun<PERSON>", "scaryvideogames": "korkunçoyunlar", "videogamemaker": "videooyunuyaratıcı", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "gözetim", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "büyücü101", "battleblocktheater": "battle<PERSON><PERSON>ya<PERSON>", "arcades": "<PERSON>yun<PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffdostları", "farmingsimulator": "tarım_simülatörü", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxalmanyası", "robloxdeutsch": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sandboxoyunları", "videogamelore": "videoyüklüğü", "rollerdrome": "rollerdrom", "parasiteeve": "parazitbahçe", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "hayaldiyarı", "starcitizen": "yıldızvatandaş", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "gta", "deadspace": "ölüalan", "amordoce": "aşklıboşluk", "videogiochi": "videoyunlar", "theoldrepublic": "eskicumhuriyet", "videospiele": "videoyunlar", "touhouproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamcast": "<PERSON><PERSON><PERSON><PERSON>", "adventuregames": "maceraoyunları", "wolfenstein": "wolfenstein", "actionadventure": "aks<PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retrooyun<PERSON>", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "retrooyun<PERSON>", "vintagegaming": "vintagegaming", "playdate": "oyunbuluşması", "commanderkeen": "komandokeen", "bugsnax": "bugsnax", "injustice2": "adaletsizlik2", "shadowthehedgehog": "gölgegeyiği", "rayman": "rayman", "skygame": "gökyüzüoyunu", "zenlife": "zenhayatı", "beatmaniaiidx": "beatmaniaiidx", "steep": "daldım", "mystgames": "mystoyunları", "blockchaingaming": "blokzinciroyunları", "medievil": "ortaçağ", "consolegaming": "oyuncukonsolu", "konsolen": "konsolparty", "outrun": "yatoyuzü", "bloomingpanic": "çiçeklenenkorku", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "oyunkorkusu", "monstergirlquest": "monsterkızmacerası", "supergiant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "tarımsımüzipatı", "juegosviejos": "eskioyunlar", "bethesda": "bethesda", "jackboxgames": "jackboxoyunları", "interactivefiction": "etkileşimlikkurgu", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "görselroman", "visualnovels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "gölgekurdu", "tcrghost": "tcrhayalet", "payday": "maaşgü<PERSON><PERSON>", "chatherine": "katherine", "twilightprincess": "alacakaranlıkprensesi", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "estetikoyunlar", "novelavisual": "yeniavizual", "thecrew2": "ekip2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retro<PERSON>un", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "bunalım", "godhand": "tanrınıneli", "leafblowerrevolution": "yapraküflemeihyası", "wiiu": "wiiu", "leveldesign": "seviyeedizimi", "starrail": "yıld<PERSON>zrayı", "keyblade": "anahtarkılıcı", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsırada", "novelasvisuales": "görselromanlar", "robloxbrasil": "robloxbrezilya", "pacman": "pacman", "gameretro": "eskiygamers", "videojuejos": "videooyun<PERSON>", "videogamedates": "videoyedatları", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "hesabıngeriçıkışı", "gamstergaming": "gamstergaming", "dayofthetantacle": "tentakliseninikirgünü", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON>", "crashracing": "çarpışyarakyarış", "3dplatformers": "3dplatformerlar", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "eskiokuloyunları", "hellblade": "cehennemkılıcı", "storygames": "hikayeoyunları", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "seskaçırıcı", "beyondtwosouls": "ötesindeikiyüz", "gameuse": "oyun<PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "küçüktavşan", "retroarch": "retroarch", "powerup": "güçlen", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "gra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "hızlıparıltı", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retrooyunalanları", "f123": "f123", "wasteland": "çorakalan", "powerwashsim": "güçtemizleme_sim", "coralisland": "mercanadası", "syberia3": "syberia3", "grymmorpg": "çılgınmmorpg", "bloxfruit": "bloxfruit", "anotherworld": "başkadünya", "metaquest": "metaquest", "animewarrios2": "animesavaşçıları2", "footballfusion": "futbolfüzyonu", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomucize", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "çarpıkmetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "utançyığını", "simulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symulatory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedrunner": "hızkoşucusu", "epicx": "epicx", "superrobottaisen": "süperrobotkapışması", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "hayalülkesiçevrimiçi", "skylander": "gökyüzü", "boyfrienddungeon": "erkekarkadaşzindanı", "toontownrewritten": "toontownyeníyazım", "simracing": "simracing", "simrace": "simyarışı", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "partioyunları", "graveyardkeeper": "mezarbekçisi", "spaceflightsimulator": "uzayuçuşsimülatörü", "legacyofkain": "kaininmirası", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "yemekvetvoyunları", "oyunvideoları": "oyunvideoları", "thewolfamongus": "aramızdakikurt", "truckingsimulator": "ka<PERSON>onsimülatör<PERSON>", "horizonworlds": "horizon_dünyaları", "handygame": "handygame", "leyendasyvideojuegos": "oyunlegendası", "oldschoolvideogames": "eskiokulvideoyunları", "racingsimulator": "yarışsimülatörü", "beemov": "beemovtr", "agentsofmayhem": "kıyametinajanları", "songpop": "şarkıpatı", "famitsu": "famitsu", "gatesofolympus": "olimpusunkapıları", "monsterhunternow": "canavarda<PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "asiizyıld<PERSON>z", "indievideogaming": "bağımsızvideoyuezgücü", "indiegaming": "bağımsızoyunlar", "indievideogames": "indietablooyunları", "indievideogame": "bağımsızvideoyapımı", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanuykusuz", "bufffortress": "buff<PERSON>esi", "unbeatable": "<PERSON><PERSON><PERSON><PERSON>", "projectl": "proje<PERSON><PERSON>", "futureclubgames": "geleceklubuhoyunlar", "mugman": "mugadam", "insomniacgames": "uykusuzoyunlar", "supergiantgames": "superdevoyunlar", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturebilim", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "oyunkuyruğu", "gamingbacklog": "oyunlistem", "personnagejeuxvidéos": "karakteroyunlar", "achievementhunter": "başarıavcısı", "cityskylines": "şehirsilüetleri", "supermonkeyball": "süpermaymuntop<PERSON>", "deponia": "deponia", "naughtydog": "yaramazköpek", "beastlord": "canavarüstası", "juegosretro": "retrooyun<PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dopaminrezervuarı", "staxel": "staxel", "videogameost": "videoyemusik", "dragonsync": "dragonşink", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "çıldırmış", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "ilkdaşlama", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "üzülanime", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animeölçekleme", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "pesci", "retroanime": "retroanime", "animes": "animeler", "supersentai": "süpersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "karanlıkefendi", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON>iii", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "şarjadamlar", "animecover": "animekapak", "thevisionofescaflowne": "escaflowneninvizyonu", "slayers": "<PERSON><PERSON>şahları", "tokyomajin": "tokyomajin", "anime90s": "anime90lar", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "muzbalığı", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "tuvaletbağlıhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "yangınkuvveti", "moriartythepatriot": "moria<PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "perimasalı", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "denizkızımelodisi", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "korkumanga", "romancemangas": "romantikkonular", "karneval": "karnaval", "dragonmaid": "dragonmaid", "blacklagoon": "<PERSON><PERSON><PERSON>ü<PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON>", "shamanking": "şamanlık", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "belirlibüyülemlik", "sao": "sao", "blackclover": "siyahşalgam", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON>umrukadam", "hetalia": "hetalia", "kagerouproject": "ka<PERSON>oup<PERSON><PERSON><PERSON>", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8sonsuzluk", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "kılıçsanatlarıçevrimiçi", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "angelsofdeath": "ölümmeleği", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosimik", "goldenkamuy": "altınkamuy", "monstermusume": "canavarkızlar", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "spor<PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "melekvuruşları", "isekaianime": "isekaianime", "sagaoftanyatheevil": "kötüoftaboo", "shounenanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "çocuklevahşi", "fistofthenorthstar": "kuzeyinyıldızıyumruğu", "mazinger": "mazinger", "blackbuttler": "siyahşövalye", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "çizgişekil", "servamp": "servamp", "howtokeepamummy": "anneminasıltutabilirim", "fullmoonwosagashite": "fullmoonwoşagashite", "shugochara": "şugochara", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "sevimliğereceli", "martialpeak": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "yüksekpuankız", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "canavarkız", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amitim", "sailorsaturn": "denizcilerdayürük", "dio": "dio", "sailorpluto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "eskanime", "chainsawman": "testereadam", "bungoustraydogs": "bungoustraydogs", "jogo": "oyun", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "siyahustalık", "ergoproxy": "ergoproxy", "claymore": "kilitkaya", "loli": "loli", "horroranime": "kor<PERSON><PERSON><PERSON>", "fruitsbasket": "meyve_sepeti", "devilmancrybaby": "şeytanadamakıntı", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "aşkyaşa", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "sevgilişeytanım", "thepromisedneverland": "vaaded<PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "ap<PERSON><PERSON><PERSON>_yalan<PERSON>ın", "buggytheclown": "buzgi<PERSON><PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "sonunserapı", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "ölümadamıharikalardiyarı", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "yemek<PERSON>ü<PERSON><PERSON><PERSON>", "cardcaptorsakura": "kartyakalıcısakura", "stolas": "stolas", "devilsline": "şeytanlarhattı", "toyoureternity": "sanaefinitin", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "maviperiyot", "griffithberserk": "griffithberserk", "shinigami": "ş<PERSON>gami", "secretalliance": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "silinmiş", "bluelock": "ma<PERSON><PERSON><PERSON>", "goblinslayer": "goblinavcısı", "detectiveconan": "dedektifconan", "shiki": "şiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirşö<PERSON>ye", "mugi": "mugi", "blueexorcist": "mavişeytançı", "slamdunk": "slamvu<PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "baktı", "spyfamily": "casus<PERSON><PERSON><PERSON>", "airgear": "havaekipmanları", "magicalgirl": "büyülükız", "thesevendeadlysins": "bu7<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>nah", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "yüksekokuluntanrısı", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "büyükkoyu", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeevreni", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "ilkadım", "undeadunluck": "ölümdenşanssız", "romancemanga": "romansmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeaşk", "senpai": "senpai", "blmanhwas": "blman<PERSON>", "animeargentina": "<PERSON><PERSON><PERSON><PERSON>", "lolicon": "lolikon", "demonslayertothesword": "şeytanavcısındankılıca", "bloodlad": "kanlıçocuk", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "ateşyumruğu", "adioseri": "hoşçakalserin", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>umsa", "romanceanime": "romantikanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "ka<PERSON>ıtragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retroçizgiroman", "highschoolofthedead": "ölümokulu", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "d<PERSON><PERSON><PERSON>", "princeoftennis": "tenisinp<PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "suikastçilerokulu", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "ölümgecesi", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japonanime", "animespace": "animespace", "girlsundpanzer": "kızlarundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedublaj", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqyagı", "indieanime": "ba<PERSON><PERSON><PERSON><PERSON><PERSON>ime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "fareadam", "haremanime": "<PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "nekoğlan", "gashbell": "gashbell", "peachgirl": "şeftalkız", "cavalieridellozodiaco": "zodiacşövalyeleri", "mechamusume": "mekanbabası", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchkulübü", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokkaydı", "funamusea": "eğlencemüzesi", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "şuumatsunovalkyrie", "tutorialistoohard": "öğreticiçookzor", "overgeared": "i̇htiyaçsahibi", "toriko": "<PERSON><PERSON>o", "ravemaster": "raveustası", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "cadışapkasıatölyesi", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangahayatım", "dropsofgod": "tanrıdamatıklar", "loscaballerosdelzodia": "zodyakşövalyeleri", "animeshojo": "animeshojo", "reverseharem": "tersharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON>rik<PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "asker", "mybossdaddy": "patronbabam", "gear5": "dişlide5", "grandbluedreaming": "büyüklaciverthayaller", "bloodplus": "kanartı", "bloodplusanime": "kanar<PERSON><PERSON><PERSON>me", "bloodcanime": "kananime", "bloodc": "kan<PERSON><PERSON>c", "talesofdemonsandgods": "şeytanvegözcülerhikayeleri", "goreanime": "goreanime", "animegirls": "animekızlar", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kargalarxabuklar", "splatteranime": "splatlayananime", "splatter": "splat<PERSON>", "risingoftheshieldhero": "kalkanıkaldırankahraman", "somalianime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeispanya", "animeciudadreal": "animedunyasi", "murim": "murin", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "balinalarınçocukları", "liarliar": "yalancıyalancı", "supercampeones": "süperşampiyonlar", "animeidols": "animeidolü", "isekaiwasmartphone": "isekaiüzerinesmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "büyülükızlar", "callofthenight": "geceyinçağrısı", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "gölgebahçe", "tsubasachronicle": "tsubasachronicle", "findermanga": "bulmanga", "princessjellyfish": "prensesahtapot", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "cennetöpücü<PERSON>ü", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animealemi", "persocoms": "persokomlar", "omniscientreadersview": "herşeyibilenokurbakışı", "animecat": "animekedisi", "animerecommendations": "animeönerileri", "openinganime": "animeaçılışı", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "benimteengönülkomedim", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamlar", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobildövüşçüggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilsuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "bigoanime", "bleach": "soda", "deathnote": "ölümdefteri", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojolarınacayipmacerası", "fullmetalalchemist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "buz", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "askeranime", "greenranger": "yeşilşövalye", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonmacerası", "hxh": "hxh", "highschooldxd": "liseydxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonavcısı", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "titanlaraşırıolfans", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "<PERSON><PERSON><PERSON>ka<PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "anket_takımı", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "birbirparçaşgerçek", "revengers": "intikamcılar", "mobpsycho": "<PERSON><PERSON><PERSON>", "aonoexorcist": "aonoexorcist", "joyboyeffect": "neşelikidiri", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalokalips", "shinchan": "<PERSON><PERSON><PERSON><PERSON>", "watamote": "watamote", "uramichioniisan": "<PERSON>ram<PERSON><PERSON><PERSON><PERSON>", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "kusursuzwebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "utançprensesi", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "gündelikhayat", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "uçancadırrasputin", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "tümazizlersokağı", "recuentosdelavida": "hayatkayıtları"}