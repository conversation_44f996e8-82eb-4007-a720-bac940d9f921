{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologie", "cognitivefunctions": "kognitiewefunksie", "psychology": "sielkunde", "philosophy": "filosofie", "history": "geskiedenis", "physics": "<PERSON><PERSON>a", "science": "wetenskap", "culture": "kulture", "languages": "tale", "technology": "tegnologie", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiememe", "enneagrammemes": "enneagrammemes", "showerthoughts": "stor<PERSON><PERSON><PERSON><PERSON>", "funny": "snaaks", "videos": "videos", "gadgets": "gadgets", "politics": "politieks", "relationshipadvice": "verhouding<PERSON><PERSON>", "lifeadvice": "lewen<PERSON><PERSON>", "crypto": "kripto", "news": "nuus", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "argeologie", "learning": "leer", "debates": "debatte", "conspiracytheories": "samesweringsteorieë", "universe": "heelal", "meditation": "meditasie", "mythology": "mitologie", "art": "kuns", "crafts": "handwerk", "dance": "dans", "design": "ontwerp", "makeup": "grimering", "beauty": "skoon<PERSON><PERSON>", "fashion": "mode", "singing": "sing", "writing": "skryf", "photography": "fotografie", "cosplay": "cosplay", "painting": "skildery", "drawing": "skets", "books": "boeke", "movies": "flieks", "poetry": "po<PERSON><PERSON>", "television": "televisie", "filmmaking": "filmvervaardiging", "animation": "animasie", "anime": "anime", "scifi": "scifi", "fantasy": "fantasie", "documentaries": "dokumentêreprogramme", "mystery": "misterie", "comedy": "komedie", "crime": "misdaad", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "gru<PERSON>l", "romance": "romanse", "realitytv": "werklikheidstv", "action": "a<PERSON><PERSON>", "music": "musiek", "blues": "blues", "classical": "klassieke", "country": "boere", "desi": "desi", "edm": "edm", "electronic": "elektroniese", "folk": "volks", "funk": "funk", "hiphop": "hiphop", "house": "huis", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "<PERSON><PERSON><PERSON>", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tegno", "travel": "reis", "concerts": "konserte", "festivals": "feeste", "museums": "museums", "standup": "opstaankomedie", "theater": "teater", "outdoors": "buit<PERSON><PERSON>", "gardening": "<PERSON><PERSON><PERSON><PERSON>", "partying": "partytjiehou", "gaming": "gaming", "boardgames": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "skaak", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "kos", "baking": "bak", "cooking": "kosmaak", "vegetarian": "vegetariese", "vegan": "vegan", "birds": "vo<PERSON><PERSON>", "cats": "katte", "dogs": "honde", "fish": "vis", "animals": "diere", "blacklivesmatter": "blacklivesmatter", "environmentalism": "omgewingsbewustheid", "feminism": "feminisme", "humanrights": "menseregte", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON>an<PERSON><PERSON>", "transally": "transbondgenoot", "volunteering": "vrywilligerswerk", "sports": "sport", "badminton": "badminton", "baseball": "bofbal", "basketball": "basketbal", "boxing": "boks", "cricket": "krieket", "cycling": "fietsry", "fitness": "<PERSON><PERSON><PERSON><PERSON>", "football": "sokker", "golf": "g<PERSON>f", "gym": "gimnasium", "gymnastics": "gimnastiek", "hockey": "hokkie", "martialarts": "gevegskuns", "netball": "netbal", "pilates": "pilates", "pingpong": "<PERSON><PERSON><PERSON><PERSON>", "running": "hardloop", "skateboarding": "skaatsplankry", "skiing": "ski", "snowboarding": "sneeup<PERSON>ry", "surfing": "branderplankry", "swimming": "swem", "tennis": "tennis", "volleyball": "vlugbal", "weightlifting": "g<PERSON><PERSON><PERSON><PERSON>", "yoga": "joga", "scubadiving": "skubaduik", "hiking": "stap", "capricorn": "steenbok", "aquarius": "water<PERSON>er", "pisces": "vis", "aries": "ram", "taurus": "bul", "gemini": "tweeling", "cancer": "kreef", "leo": "leeu", "virgo": "maagd", "libra": "weeg<PERSON><PERSON>", "scorpio": "skerpioen", "sagittarius": "boogskutter", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "losknyp", "longtermrelationship": "langtermynverhouding", "single": "enkellopend", "polyamory": "poligamie", "enm": "ni<PERSON><PERSON><PERSON>", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "bi<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "rooidoodvergifnis2", "dragonage": "drakenslag", "assassinscreed": "assassinscreed", "saintsrow": "heiligestrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "waak<PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subversief", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "r<PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "drak<PERSON><PERSON><PERSON>", "sunsetoverdrive": "sonsonderbreking", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "vuurlandskies", "yokaiwatch": "yokaiwatch", "rocksteady": "rockensteddy", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "gildoorloë", "openworld": "oopw<PERSON><PERSON>d", "heroesofthestorm": "heldevanherstorm", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dungeonverkenning", "jetsetradio": "jetsetradio", "tribesofmidgard": "stam<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "heregestepevandieryk2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kleurvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersievesims", "okage": "okage", "juegoderol": "rollespeelgoed", "witcher": "<PERSON><PERSON><PERSON>", "dishonored": "oneer<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "ourollewers", "modding": "modding", "charactercreation": "karaktercreation", "immersive": "omvattend", "falloutnewvegas": "valstrydnuwevegase", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoudskool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivering", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "sukkelvirliefde", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampierdemaakspeel", "dimension20": "dimensie20", "gaslands": "gaslande", "pathfinder": "<PERSON><PERSON><PERSON>", "pathfinder2ndedition": "padvinder2deuitgawe", "shadowrun": "skaduwegrace", "bloodontheclocktower": "bloedopdiehorlosie", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitatsiehaast", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "eenkeerafstand", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "oorheerser", "yourturntodie": "jouturnomtekan", "persona3": "persona3", "rpghorror": "rpghorroor", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgteks", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "ster<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsiele", "mu": "mu", "falloutshelter": "valskuiling", "gurps": "gurps", "darkestdungeon": "donkerste2el", "eclipsephase": "eclipsfase", "disgaea": "disgaea", "outerworlds": "buitelands", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastiestrijders", "skullgirls": "skullgirls", "nightcity": "nagstad", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madnessgeveg", "jaggedalliance2": "geskeurdealliansie2", "neverwinter": "nooitwinter", "road96": "pad96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamridders", "forgottenrealms": "vergeetreiklande", "dragonlance": "drakestroon", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON>li<PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "dedivisie2", "lineage2": "stamboom2", "digimonworld": "digimonwêreld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanvers", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonverbodeweste", "twewy": "twewy", "shadowpunk": "skadupunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmysterie", "deltagreen": "<PERSON><PERSON><PERSON><PERSON>", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "ve<PERSON><PERSON><PERSON>", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "sterfinders", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "goddelikeoorspronklikewee", "bladesinthedark": "swaardeinddonker", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "kuberrukkers", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrooi", "dragonballxenoverse2": "drakonballexenoverse2", "fallenorder": "valleorde", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "diablesurvivor", "oldschoolrunescape": "oueskoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "boerrpg", "oldworldblues": "oudewereldblues", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rolspel<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "son<PERSON>", "talesofsymphonia": "verhaaltjiesvansimfonie", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "<PERSON><PERSON><PERSON>", "torncity": "geskeurdestad", "myfarog": "myfarog", "sacredunderworld": "sacredeonderwêreld", "chainedechoes": "geketteldeklank", "darksoul": "<PERSON><PERSON><PERSON>", "soulslikes": "sielsoorte", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "bergenswaard", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "skeure", "tibia": "tibia", "thedivision": "dieafdeling", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "weerwolfdieapokalips", "aveyond": "aveyond", "littlewood": "kleinbos", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fabledieverloorehoofstuk", "hiveswap": "hekswisseling", "rollenspiel": "rolspel", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "<PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "sterreveld", "oldschoolrevival": "oues<PERSON><PERSON><PERSON><PERSON>ing", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savagewêrelds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "koninkrykhart1", "ff9": "ff9", "kingdomheart2": "konyngshart2", "darknessdungeon": "don<PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomhearts": "koninkrykskepe", "kingdomheart3": "koninkrykhart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "hemelvanarcadia", "shadowhearts": "ska<PERSON><PERSON>s", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennybloed", "breathoffire4": "asemvandvuurgap", "mother3": "ma3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "andereden", "roleplaygames": "rolspel<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "rolspeletjie", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON>", "pathfinderrpg": "padvinderrpg", "pathfinder2e": "padvinder2e", "vampirilamasquerade": "vampierlamaskerade", "dračák": "d<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "<PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "jag<PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstersjagterwêreld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "schaduwhartskoninkryk", "bladesoul": "blaaiblend", "baldursgate3": "baldursgate3", "kingdomcome": "koninkrykkom", "awplanet": "awplaneet", "theworldendswithyou": "diewereldeindigmetjou", "dragalialost": "dragalialost", "elderscroll": "ouds<PERSON><PERSON><PERSON>", "dyinglight2": "sterflig2", "finalfantasytactics": "finalfantasytaktieke", "grandia": "grandia", "darkheresy": "donkererfgedeelte", "shoptitans": "winkeltitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "aardmagie", "blackbook": "swartboek", "skychildrenoflight": "lugkindervanlig", "gryrpg": "gryrpg", "sacredgoldedition": "heiligegouduitgawe", "castlecrashers": "kasteelkrakers", "gothicgame": "gothiesespel", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "spook<PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "speelrpg", "prophunt": "prophunt", "starrails": "sterrerails", "cityofmist": "stadvannebel", "indierpg": "indierpg", "pointandclick": "puntenkliek", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "ongedeeld", "freeside": "<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7vooraltyd", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "doodpadnaartoecanada", "palladium": "palladium", "knightjdr": "riddereksidz", "monsterhunter": "monstermaker", "fireemblem": "vuurwapen", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "geestvantsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterjagopstyg", "nier": "niere", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktieserpg", "mahoyo": "mahoyo", "animegames": "animespeletjies", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON>t", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON>ata", "princessconnect": "prinsesverbinding", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON>", "cristales": "kristalle", "vcs": "vcs", "pes": "pes", "pocketsage": "sakken<PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindiese", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "evoetbal", "nba2k": "nba2k", "egames": "espeletjies", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligavandroomers", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "evoetbal", "dreamhack": "droomhaks", "gaimin": "gaimin", "overwatchleague": "oorwachtliga", "cybersport": "cybersport", "crazyraccoon": "<PERSON><PERSON><PERSON>", "test1test": "toets1toets", "fc24": "fc24", "riotgames": "opstandspelletjes", "eracing": "e<PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portaal2", "halflife": "halflewe", "left4dead": "linksvirdoode", "left4dead2": "left4dead2", "valve": "klep", "portal": "poort", "teamfortress2": "spanforte2", "everlastingsummer": "e<PERSON><PERSON><PERSON>", "goatsimulator": "boksimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "vryheidplaneet", "transformice": "transformice", "justshapesandbeats": "netvormsenritmes", "battlefield4": "slagveld4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "haknslay", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risikoftoenval2", "metroidvanias": "metroidvanias", "overcooked": "oorgekook", "interplanetary": "interplanetaire", "helltaker": "helldiener", "inscryption": "inskripsie", "7d2d": "7d2d", "deadcells": "doodsellies", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwergvesting", "foxhole": "vossel", "stray": "verd<PERSON><PERSON>", "battlefield": "slagveld", "battlefield1": "slagveld1", "swtor": "s<PERSON><PERSON><PERSON><PERSON>", "fallout2": "fallout2", "uboat": "uboot", "eyeb": "kyk", "blackdesert": "s<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "tafelspelsimulator", "partyhard": "partytotdie", "hardspaceshipbreaker": "hardruimteskipbreker", "hades": "hades", "gunsmith": "wapenmaker", "okami": "<PERSON>ami", "trappedwithjester": "vasgevangmetjester", "dinkum": "dinkum", "predecessor": "voorloper", "rainworld": "reënwêreld", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "oorlogsa<PERSON><PERSON><PERSON>", "minionmasters": "minionmeesters", "grimdawn": "grimdawn", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "swart<PERSON>a", "soulworker": "sielwerker", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kubervlugtog", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nuestad", "citiesskylines": "stadsiluette", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtuelekenopsie", "snowrunner": "sneeu<PERSON>er", "libraryofruina": "bibliotheekvanruïne", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON>ary<PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "kalmeplastiekentjie", "battlebit": "oorlogsbietjie", "ultimatechickenhorse": "ultimatehennakip", "dialtown": "belstad", "smileforme": "glim<PERSON><PERSON><PERSON>", "catnight": "kattnacht", "supermeatboy": "superv<PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "b<PERSON><PERSON><PERSON>", "cozygrove": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "doom", "callofduty": "oproepvanpligtigheid", "callofdutyww2": "callofdutyww2", "rainbow6": "reënboog6", "apexlegends": "apexlegends", "cod": "kod", "borderlands": "grenslande", "pubg": "pubg", "callofdutyzombies": "beloftevanoorlogszombies", "apex": "apex", "r6siege": "r6seige", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "verrecryspel", "paladins": "paladins", "earthdefenseforce": "aardeverdedigingsmag", "huntshowdown": "jag<PERSON><PERSON><PERSON>ing", "ghostrecon": "geesherkenning", "grandtheftauto5": "grootdiefstelauto5", "warz": "oorlogz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "sluitbydiegangaan", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "opstandsandstorm", "farcry3": "verregek3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hittman3", "r6s": "r6s", "rainbowsixsiege": "reënboog6layers", "deathstranding": "doodverbinding", "b4b": "b4b", "codwarzone": "codoorlogzone", "callofdutywarzone": "oorlogstoepassingboo", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "afdelings2", "killzone": "doods<PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "koudeoorlogzombies", "metro2033": "metro2033", "metalgear": "metaalgear", "acecombat": "ascombat", "crosscode": "kruiskode", "goldeneye007": "goudoog007", "blackops2": "swartoperasie2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "moderneoorlogvoering", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "grenslande", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslag", "primalcarnage": "primalcarnage", "worldofwarships": "wêreldvanoorlogskepe", "back4blood": "back4blood", "warframe": "oorlogskaf", "rainbow6siege": "reënboog6beleëring", "xcom": "xcom", "hitman": "huurmoordenaar", "masseffect": "massaeffect", "systemshock": "stelselskok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "grotstories", "doometernal": "doometernal", "centuryageofashes": "eeuagevanasies", "farcry4": "verwydering4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "afdeling2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generasiezero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "moderneoorlog2", "blackops1": "swartoperasies1", "sausageman": "worsman", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "diegeest<PERSON>n", "warface": "oorlogsgesig", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomaalklip", "blackops3": "swartoperasies3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "slagvelde", "frag": "frag", "tinytina": "k<PERSON><PERSON>", "gamepubg": "speelpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearseunsvanvryheid", "juegosfps": "fpssp<PERSON>t<PERSON><PERSON>", "convertstrike": "convertnetwerk", "warzone2": "oorlogsone2", "shatterline": "sketterlyn", "blackopszombies": "swartoperasieszombies", "bloodymess": "bloody<PERSON><PERSON>s", "republiccommando": "republiekcommando", "elitedangerous": "elitedangerous", "soldat": "sold<PERSON>t", "groundbranch": "grondtak", "squad": "spandit", "destiny1": "bestemming1", "gamingfps": "spel<PERSON><PERSON><PERSON><PERSON>", "redfall": "rooival", "pubggirl": "pubggirl", "worldoftanksblitz": "wereldvantoergeregte", "callofdutyblackops": "roepvandeplichtswartoperas", "enlisted": "ingevolg", "farlight": "verafli<PERSON>ing", "farcry5": "verweggegronde5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "gepantserkern", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "paydag2", "cs16": "cs16", "pubgindonesia": "pubgindonesië", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgck", "titanfall2": "titanfall2", "soapcod": "seepkode", "ghostcod": "spookcod", "csplay": "csplay", "unrealtournament": "onwerkliketoernooi", "callofdutydmz": "callofdutydmz", "gamingcodm": "speletjiescodm", "borderlands2": "grenslande2", "counterstrike": "teenstryd", "cs2": "cs2", "pistolwhip": "pistoolklap", "callofdutymw2": "callofdutymw2", "quakechampions": "aardskuddingkampioene", "halo3": "halo3", "halo": "halo", "killingfloor": "dodingsv<PERSON>er", "destiny2": "bestemming2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON>t", "remnant": "oor<PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "wereldvanoorlog", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haalbereik", "shadowman": "ska<PERSON>man", "quake2": "skok2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "rooidood", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "slagveld3", "lostark": "verloorark", "guildwars2": "gildeoorloë2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rust", "conqueronline": "ve<PERSON><PERSON><PERSON><PERSON>", "dauntless": "onverskrokke", "warships": "oorlogskepe", "dayofdragons": "dagvandragons", "warthunder": "oorlogdonker", "flightrising": "vlugopstig", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantasystaronline2", "maidenless": "sondermeisie", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "kru<PERSON>uit", "agario": "agario", "secondlife": "tweedelewe", "aion": "aion", "toweroffantasy": "torenfant<PERSON><PERSON>", "netplay": "netspel", "everquest": "ewige<PERSON>", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "superanimalroyale": "superdiereoorlog", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ridder<PERSON>lyn", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "drakenouderinquisisie", "codevein": "codevain", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpinguïn", "lotro": "lotro", "wakfu": "wakfu", "scum": "skum", "newworld": "nuwewêreld", "blackdesertonline": "swart<PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer": "multiplayer", "pirate101": "pirate101", "honorofkings": "eervandekonings", "fivem": "fivem", "starwarsbattlefront": "sterreoorlogstrydfront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "sterrewarsgevegfront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponystad", "3dchat": "3dgeselskap", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassiek", "worldofwarcraft": "wereldvanoorlogsvakansie", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "asiesvancreati<PERSON>", "riotmmo": "riotspeletjie", "silkroad": "silkroad", "spiralknights": "spiraalknightse", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "wraak", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "drakensprofet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "<PERSON><PERSON><PERSON><PERSON>", "angelsonline": "engelsonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuni<PERSON><PERSON><PERSON>", "growtopia": "groeitemaande", "starwarsoldrepublic": "sterrewarsoudrepubliek", "grandfantasia": "grootfantasie", "blueprotocol": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "perfektewêreld", "riseonline": "sty<PERSON><PERSON><PERSON>", "corepunk": "kern<PERSON>nks", "adventurequestworlds": "avontuurlustewêrelde", "flyforfun": "vliegvirpret", "animaljam": "<PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "koninkrykvanafsku", "cityofheroes": "stadvanhelde", "mortalkombat": "mortalcombat", "streetfighter": "straatvegter", "hollowknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsoldat", "forhonor": "vireer", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "straatvegter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "sielkaliber", "brawlhalla": "brawlhalla", "virtuafighter": "virtua<PERSON><PERSON>er", "streetsofrage": "stratevanrage", "mkdeadlyalliance": "mkdodelikealliansie", "nomoreheroes": "g<PERSON><PERSON><PERSON>held<PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "diekingvanvegters", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrovechtspiele", "blasphemous": "godslasterlik", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelteenkapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "oorlogvanmonsters", "jogosdeluta": "s<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "gepantserdeoorlogvoerders", "finalfight": "laastegeveg", "poweredgear": "krag<PERSON><PERSON><PERSON>", "beatemup": "klophomptoe", "blazblue": "blazblue", "mortalkombat9": "mortalcombat9", "fightgames": "strydspel<PERSON><PERSON><PERSON>", "killerinstinct": "dodelikeinstink", "kingoffigthers": "kingoffighters", "ghostrunner": "spooklooper", "chivalry2": "ridderlikheid2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "holleridderssilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongspel", "silksongnews": "silksongnuus", "silksong": "silksong", "undernight": "ondernag", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolusietoernooi", "evomoment": "evomoment", "lollipopchainsaw": "lollipop<PERSON>saw", "dragonballfighterz": "drakbalvegtersz", "talesofberseria": "verhalevanberseria", "bloodborne": "bloedgebore", "horizon": "horison", "pathofexile": "padvanuitdieverbanning", "slimerancher": "slimerancher", "crashbandicoot": "krashbandicoot", "bloodbourne": "bloedgebore", "uncharted": "onontdekte", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "laatstesvanons", "infamous": "berug", "playstationbuddies": "playstationmaats", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persoon4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomnium<PERSON><PERSON><PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "godof<PERSON>", "gris": "grys", "trove": "skattewerf", "detroitbecomehuman": "detroitwordmenslik", "beatsaber": "beats<PERSON>r", "rimworld": "rimwêrel<PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "totsondag", "touristtrophy": "toeristtrofee", "lspdfr": "lspdfr", "shadowofthecolossus": "skaduvandiekolosse", "crashteamracing": "botskapingsrasspanne", "fivepd": "vijfpd", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "duiwelkanroep3", "devilmaycry5": "diabokanhuil5", "ufc4": "ufc4", "playingstation": "speelstasie", "samuraiwarriors": "samuraiwarriors", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "si<PERSON>kedel", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "mansjag", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "skaduherte2verbond", "pcsx2": "pcsx2", "lastguardian": "laastewagte", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "spel<PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "partydier", "warharmmer40k": "oorlogshamer40k", "fightnightchampion": "gevegsaandkampioen", "psychonauts": "psigonauts", "mhw": "mhw", "princeofpersia": "prins<PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "dieoueskrolleskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gxbox": "gxboks", "battlefront": "gevegfront", "dontstarvetogether": "moenie<PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "sterrekant", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "huisflipper", "americanmcgeesalice": "amerikanersmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "koninkrykeliga", "fable2": "fabel2", "xboxgamepass": "xboxgamepass", "undertale": "onderverhaal", "trashtv": "vull<PERSON>v", "skycotl": "hemelcotl", "erica": "erica", "ancestory": "voor<PERSON><PERSON>", "cuphead": "koppiekop", "littlemisfortune": "kleinmisforuin", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterbal", "projectzomboid": "projekzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultvandelam", "duckgame": "entduckspel", "thestanleyparable": "thestanleyeuendom", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "occulto", "longdrive": "langrit", "satisfactory": "bevredigend", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "onderdieaarde", "assettocorsa": "assettocorsa", "geometrydash": "geometrieklop", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalruimteprogram", "kenshi": "kenshi", "spiritfarer": "g<PERSON><PERSON>ger", "darkdome": "don<PERSON><PERSON><PERSON>", "pizzatower": "pizzatoring", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfdit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "spel", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolien", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "durf", "scavengerhunt": "speurdersjag", "yardgames": "grashandwerke", "pickanumber": "kiesngetal", "trueorfalse": "werkomwaarheid", "beerpong": "bierpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "geselligespele", "datinggames": "datinggames", "freegame": "gratisnspel", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "spelet<PERSON><PERSON>", "mahjong": "mahjong", "jeux": "spelet<PERSON><PERSON>", "simulationgames": "simulas<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "woordspeletjies", "jeuxdemots": "speletjiesvanwoorde", "juegosdepalabras": "speletjiesmetwoorde", "letsplayagame": "komspeletjie", "boredgames": "boregames", "oyun": "speletjie", "interactivegames": "interaktiewespeletjies", "amtgard": "amtgard", "staringcontests": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "speel", "giochi": "spelet<PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphonespeletjies", "boogames": "boos<PERSON><PERSON><PERSON><PERSON>", "cranegame": "kranespeletjie", "hideandseek": "wegkruipenzoek", "hopscotch": "klipgooi", "arcadegames": "arkadespeletjies", "yakuzagames": "yakuzaspelletjes", "classicgame": "klassiekespeletjie", "mindgames": "kopspelletjes", "guessthelyric": "raadd<PERSON>woorde", "galagames": "galagames", "romancegame": "romansspel", "yanderegames": "ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "taaltwi<PERSON>", "4xgames": "4x<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamefi": "spel<PERSON><PERSON><PERSON>i", "jeuxdarcades": "speletjiesarcades", "tabletopgames": "tafelspelletjies", "metroidvania": "metroidvania", "games90": "speletjies90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "racingspeletjies", "ets2": "ets2", "realvsfake": "e<PERSON><PERSON><PERSON><PERSON>", "playgames": "speelspelletjes", "gameonline": "spe<PERSON><PERSON><PERSON>", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "spelet<PERSON><PERSON><PERSON>lyn", "writtenroleplay": "geskreverolspel", "playaballgame": "spelet<PERSON><PERSON><PERSON><PERSON>bal", "pictionary": "te<PERSON>e", "coopgames": "sameerkind<PERSON>pel<PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiigames", "highscore": "hoëpu<PERSON>le", "jeuxderôles": "rollespel", "burgergames": "burgerspelletjies", "kidsgames": "<PERSON>ers<PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeebal", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "speelkompetisie", "tcgplayer": "tcgspeler", "juegodepreguntas": "speletjievraagte", "gioco": "speletjie", "managementgame": "bestuurspeletji<PERSON>", "hiddenobjectgame": "verborgevoorwerpspel", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formule1spel", "citybuilder": "<PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "speletjiesarcade", "memorygames": "geheuespele", "vulkan": "vulkan", "actiongames": "aksiespeletjies", "blowgames": "blowgames", "pinballmachines": "pinball<PERSON><PERSON><PERSON>", "oldgames": "oudspelet<PERSON><PERSON>", "couchcoop": "banksamewerking", "perguntados": "g<PERSON><PERSON><PERSON><PERSON>", "gameo": "speelo<PERSON>", "lasergame": "laserspel", "imessagegames": "imess<PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "vulindieleëruimte", "jeuxpc": "speletjiespc", "rétrogaming": "rétrogaming", "logicgames": "logikaspeletjies", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "speelmetsters", "exitgames": "uitgangspeletjies", "5vs5": "5teen5", "rolgame": "rolspeletjie", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tradisionelespeletjies", "kniffel": "kniffel", "gamefps": "spel<PERSON><PERSON><PERSON><PERSON>", "textbasedgames": "tekstgebaseerdespeletjies", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON>", "retrospel": "retrospel", "thiefgame": "die<PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "tabelvoetbal", "tischfußball": "tafelvoetbal", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "speletjiesforum", "casualgames": "ontspanningspeletjies", "fléchettes": "pylettes", "escapegames": "ontsnappingspeletjies", "thiefgameseries": "diefspelreeks", "cranegames": "kranespeletjies", "játék": "speel", "bordfodbold": "bordfodboll", "jogosorte": "speletjiesorte", "mage": "mage", "cargames": "motorspeletjies", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "spel<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "handsakbingo", "randomizer": "ewekansmaker", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "speletjiespc", "socialdeductiongames": "socialdeduksiespeletjies", "dominos": "dominos", "domino": "domino", "isometricgames": "isometriesespele", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "skat<PERSON><PERSON><PERSON>", "jeuxvirtuel": "spel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pspeler", "free2play": "vry2speel", "fantasygame": "fantasie<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "driftspel", "gamesotomes": "spel<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesengames", "mushroomoasis": "mushroomoase", "anythingwithanengine": "en<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "ooralspel", "swordandsorcery": "swaardenbeswering", "goodgamegiving": "goedspellyngeven", "jugamos": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8speletjies", "labzerogames": "laboratorynulspelletjes", "grykomputerowe": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "maagdskap", "gogame": "gogame", "jeuxderythmes": "ritmes<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniatuurspelletjies", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "jouselweesgaming", "gamemodding": "spelet<PERSON><PERSON><PERSON>", "crimegames": "misdaadspel<PERSON><PERSON><PERSON>", "dobbelspellen": "do<PERSON><PERSON>pel<PERSON><PERSON><PERSON>", "spelletjes": "spelet<PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "en<PERSON><PERSON><PERSON>", "coopgame": "samegame", "gamed": "speel", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "hoofdspel", "kingdiscord": "koningdiscord", "scrabble": "skrabbelspeletjie", "schach": "sjaak", "shogi": "shogi", "dandd": "dandd", "catan": "katan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pande<PERSON><PERSON><PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON>", "monopolygame": "monopoliespeletjie", "brettspiele": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "bordspelle", "boardgame": "bordspel", "sällskapspel": "speletjie", "planszowe": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "speletjiebord", "zombicide": "zombicide", "tabletop": "tafelspel", "baduk": "baduk", "bloodbowl": "bloedbak", "cluedo": "kloedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardspel", "connectfour": "verbindvier", "heroquest": "heldequest", "giochidatavolo": "tafelspelletjes", "farkle": "farkle", "carrom": "karrom", "tablegames": "tafelspelletjes", "dicegames": "do<PERSON><PERSON>pel<PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "tafelspelletjes", "alpharius": "alpharius", "masaoyunları": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON>p<PERSON><PERSON><PERSON>", "cosmicencounter": "kosmieseontmoeting", "creationludique": "skepping<PERSON><PERSON>", "tabletoproleplay": "tafelrolspel", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "el<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "skakelbordspeletjies", "infinitythegame": "oneindigheiddiegame", "kingdomdeath": "kingdomdood", "yahtzee": "jaatzie", "chutesandladders": "glybaneentrap", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "speletjiesopdietafel", "planszówki": "spelet<PERSON><PERSON>", "rednecklife": "roo<PERSON><PERSON><PERSON>", "boardom": "verve<PERSON><PERSON><PERSON>", "applestoapples": "appelsopappels", "jeudesociété": "jeudesociété", "gameboard": "spelbord", "dominó": "domino", "kalah": "kalah", "crokinole": "krokinole", "jeuxdesociétés": "speletjiesvanmaats", "twilightimperium": "skemerimperium", "horseopoly": "perdeopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "speletjiesaantafel", "shadowsofbrimstone": "skaduweesvanbrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "spelet<PERSON><PERSON>", "battleship": "gevegskepe", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "boos<PERSON><PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "spelet<PERSON><PERSON>", "xiángqi": "skaak", "jeuxsociete": "speletjiesgenootskap", "gesellschaftsspiele": "maatskapspeletjies", "starwarslegion": "sterrewarslegioen", "gochess": "speel<PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "speletjiesvanmaats", "terraria": "terrafaam", "dsmp": "dsmp", "warzone": "oorlogsone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dagte", "identityv": "identiteitv", "theisle": "<PERSON><PERSON><PERSON>", "thelastofus": "dielaatstevanons", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyendieink<PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "onderons", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planeetbouwer", "daysgone": "da<PERSON>by", "fobia": "fobie", "witchit": "he<PERSON><PERSON>", "pathologic": "patologies", "zomboid": "zomboid", "northgard": "noordgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "gegrond", "stateofdecay2": "staatvanverval2", "vrising": "vrising", "madfather": "flippe<PERSON><PERSON>", "dontstarve": "moeniesterwe", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "pad<PERSON><PERSON><PERSON>", "frictionalgames": "wrygames", "hexen": "he<PERSON><PERSON>", "theevilwithin": "die<PERSON><PERSON><PERSON><PERSON>", "realrac": "<PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "dieagterkamers", "backrooms": "agterkamers", "empiressmp": "empiresmp", "blockstory": "blokverha<PERSON>", "thequarry": "die<PERSON><PERSON>", "tlou": "tlou", "dyinglight": "sterwendelicht", "thewalkingdeadgame": "diewalkendestoringspel", "wehappyfew": "onsgelukkigepaar", "riseofempires": "stygvanimperiums", "stateofsurvivalgame": "staatvanoorlewinggame", "vintagestory": "vintage<PERSON><PERSON><PERSON>", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "beestevanbermu<PERSON>", "frostpunk": "vriespunch", "darkwood": "donkerhout", "survivalhorror": "oorlewing<PERSON>es", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "nutteloosvervoer", "lifeaftergame": "leweaftergame", "survivalgames": "oorleefspeletjies", "sillenthill": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "groenproject", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "vlot", "rdo": "rdo", "greenhell": "gro<PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "<PERSON><PERSON>", "littlenightmares2": "kleinnagmare2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "oorleefproewe", "alienisolation": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "onontwaak", "7day2die": "7dae2sterf", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "oorlewing", "propnight": "propnag", "deadisland2": "doedeyland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampier", "deathverse": "doodvers", "cataclysmdarkdays": "kataklismosdonkerdae", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "leweafter", "ageofdarkness": "tydvansterre", "clocktower3": "kloktoren3", "aloneinthedark": "alleenindedikdonker", "medievaldynasty": "middel<PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "projeknimbusspel", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "handwerkland", "theoutlasttrials": "dieuitdagingstoetse", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "raketliga", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "dwarfslayer", "warhammer40kcrush": "warhammer40kverlief", "wh40": "wh40", "warhammer40klove": "warhammer40kliefde", "warhammer40klore": "warhammer40kstorielore", "warhammer": "oorloghamer", "warhammer30k": "oorloghamer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totaleoorloghamer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "ekhaalliefdeuitvindicare", "iloveassasinorum": "ekh<PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "burovanslagters", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "tydvanimperiums", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "oorloghamerleeftydsiegmar", "civilizationv": "beskawingv", "ittakestwo": "di<PERSON><PERSON>mtwee", "wingspan": "vlerkeafstand", "terraformingmars": "terreformingmars", "heroesofmightandmagic": "heldevanmagteenmagie", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "tydperkvanmitologie", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "voorpos2", "banished": "verbanne", "caesar3": "caesar3", "redalert": "roo<PERSON><PERSON><PERSON>", "civilization6": "civilisasie6", "warcraft2": "warcraft2", "commandandconquer": "beve<PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "ewigeoorlog", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "beskaafdingspel", "civilization4": "beskawing4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totaleoorlog", "travian": "travian", "forts": "forts", "goodcompany": "goeiemaat", "civ": "civ", "homeworld": "tuinw<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "vinigeraslig", "forthekings": "virdie<PERSON>ings", "realtimestrategy": "werkliketaktiek", "starctaft": "sterkuns", "sidmeierscivilization": "sidmeiersbescivilization", "kingdomtwocrowns": "koninkrijktwokronies", "eu4": "eu4", "vainglory": "selfverheerliking", "ww40k": "ww40k", "godhood": "godskap", "anno": "anno", "battletech": "slagtegnologie", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveslekkeralgebraklas", "plagueinc": "plagueinc", "theorycraft": "teoriecraft", "mesbg": "mesbg", "civilization3": "beskaafd3", "4inarow": "4<PERSON>ry", "crusaderkings3": "kruisvaarderskonings3", "heroes3": "helde3", "advancewars": "gevegsoorlog", "ageofempires2": "tijdsperkvanoorloë2", "disciples2": "dissipels2", "plantsvszombies": "planteteenzombies", "giochidistrategia": "strateggames", "stratejioyunları": "strate<PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "ouderdomvanwonders", "dinosaurking": "dinosour<PERSON>ing", "worldconquest": "wereldoorwinning", "heartsofiron4": "hartsevanyster4", "companyofheroes": "maatskappyvanhelde", "battleforwesnoth": "stryd<PERSON><PERSON>not<PERSON>", "aoe3": "aoe3", "forgeofempires": "smederijvanimperiums", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansgegooseenduck", "phobies": "fobies", "phobiesgame": "phobiesgame", "gamingclashroyale": "speletjieskloofroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "buit<PERSON><PERSON>", "turnbased": "turngebaseerd", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "beskawing5", "victoria2": "victoria2", "crusaderkings": "kruisvaarderskonings", "cultris2": "cultris2", "spellcraft": "tovenaarskuns", "starwarsempireatwar": "sterreoorlogsimperiumteoorlog", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategie", "popfulmail": "popfulmail", "shiningforce": "skynkrag", "masterduel": "meesterduel", "dysonsphereprogram": "dysonsfeerprogram", "transporttycoon": "ver<PERSON><PERSON><PERSON><PERSON>r", "unrailed": "ongebaan", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolweplek", "ooblets": "ooblets", "planescapetorment": "vliegtuigslipteistering", "uplandkingdoms": "<PERSON><PERSON>andkonink<PERSON><PERSON>", "galaxylife": "<PERSON>lew<PERSON>", "wolvesvilleonline": "wolvesvilleaanlyn", "slaythespire": "slaydie<PERSON>", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON>od<PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "nodanweescarbon", "realracing3": "regtewissels3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "verloorsims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "dooddeurligdag", "alicemadnessreturns": "alicemadnesskomterug", "darkhorseanthology": "donkerpaardbundel", "phasmophobia": "spookvrees", "fivenightsatfreddys": "vivenaans<PERSON>j<PERSON><PERSON>", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "kleinnagmerries", "deadrising": "doodopstanding", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "huisgebonde", "deadisland": "doodeilande", "litlemissfortune": "kleinmeisiejansnik", "projectzero": "projekniks", "horory": "horo<PERSON><PERSON><PERSON>", "jogosterror": "jogosterror", "helloneighbor": "hellobuur", "helloneighbor2": "hellobuur2", "gamingdbd": "gamingdbd", "thecatlady": "kat<PERSON><PERSON>", "jeuxhorreur": "speletjieshorror", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kaartteenmenswaardigheid", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodesname", "dixit": "dixit", "bicyclecards": "fietskaarte", "lor": "lol", "euchre": "euchre", "thegwent": "dieg<PERSON>t", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "<PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "sleutel<PERSON>ur", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "speel<PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "jin<PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "handelskaarte", "pokemoncards": "pokemonkaarte", "fleshandbloodtcg": "vleesenenbloedtcg", "sportscards": "sportkaarte", "cardfightvanguard": "kaartgevegvanguard", "duellinks": "duellinks", "spades": "borde", "warcry": "oorlogskreet", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "koningvanhartse", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "dieresistance", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopfsessies", "yugiohcards": "yug<PERSON><PERSON>ka<PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspel", "darkmagician": "donkertowenaar", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkommandeur", "cotorro": "kotorro", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "karteer", "sueca": "sueca", "beloteonline": "be<PERSON><PERSON><PERSON><PERSON>", "karcianki": "karciankies", "battlespirits": "g<PERSON>gg<PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON>", "žolíky": "boo", "facecard": "gezichtskaart", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelkampioene", "magiccartas": "magickaarte", "yugiohmasterduel": "yug<PERSON>hm<PERSON><PERSON><PERSON><PERSON>", "shadowverse": "skadu<PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "onbetroubar<PERSON><PERSON><PERSON><PERSON>s", "cyberse": "cyberse", "classicarcadegames": "klassiekearkadespeletjies", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansspeletjies", "fridaynightfunkin": "vrydagnagfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projekmirai", "projectdiva": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "k<PERSON><PERSON><PERSON>", "justdance": "netdans", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "skopdead", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON><PERSON><PERSON>", "dancecentral": "danssentraal", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stapmania", "highscorerythmgames": "hoëpuntritmespelet<PERSON><PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "dansvanvuurensneeuw", "auditiononline": "audis<PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ritmes<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptootdancer", "rhythmdoctor": "ritmedokter", "cubing": "kubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "legpuzzles", "spotit": "spotdit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "logika<PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rub<PERSON>", "brainteasers": "breinbrekers", "rubikscube": "rubikskuikentje", "crossword": "kodek<PERSON><PERSON>", "motscroisés": "kruiswoordpuzzels", "krzyżówki": "kruiswoorde", "nonogram": "nonogram", "bookworm": "boekduikert", "jigsawpuzzles": "legkaarte", "indovinello": "raaispel", "riddle": "raads<PERSON>", "riddles": "raaisels", "rompecabezas": "legpuzzle", "tekateki": "tekatikie", "inside": "binne", "angrybirds": "woedendevoël<PERSON>", "escapesimulator": "ontsnappingsimulator", "minesweeper": "mynveger", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "kruiswoorde", "kurushi": "k<PERSON>hi", "gardenscapesgame": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "legkaarte", "escaperoomgames": "ontsnappingspeletjies", "escapegame": "ontsnappingspel", "3dpuzzle": "3draadpuzzel", "homescapesgame": "huislandspeletjie", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "ensiklopediespeel", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "raaisstofstories", "fishdom": "visland", "theimpossiblequiz": "dieonmoontlikequiz", "candycrush": "soetverpletter", "littlebigplanet": "kleinbigplanete", "match3puzzle": "wedstryd3legpuzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kuiers", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "raaiemy", "tycoongames": "tycoon<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "kruiswoordpuzzle", "ciphers": "koderinge", "rätselwörter": "raaiswoorde", "buscaminas": "<PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "puzzeloplossing", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adiv<PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON>", "guessing": "raai", "nonograms": "nonogramme", "kostkirubika": "kostkirubika", "crypticcrosswords": "kruiswoordraaisels", "syberia2": "syberia2", "puzzlehunt": "puzzeljag", "puzzlehunts": "puzzeljaag", "catcrime": "kat<PERSON><PERSON><PERSON>", "quebracabeça": "breinbreker", "hlavolamy": "puzzels", "poptropica": "poptropica", "thelastcampfire": "dieultiemekampvuur", "autodefinidos": "outodefinies", "picopark": "picopark", "wandersong": "wanderlied", "carto": "kartrit", "untitledgoosegame": "sondertitelgansspeld", "cassetête": "kassette", "limbo": "limbo", "rubiks": "rubiks", "maze": "doolhof", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "spoedkubus", "pieces": "stukkies", "portalgame": "portaalspel", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "<PERSON><PERSON><PERSON><PERSON>", "rubixcube": "rubixkubus", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobiel", "codm": "codm", "twistedwonderland": "verwrongenwonderland", "monopoly": "monopolie", "futurefight": "toekomsgeveg", "mobilelegends": "mobielelegendes", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "kak", "lonewolf": "een<PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "koekiehardloopkoninkryk", "alchemystars": "alkemistestertjies", "stateofsurvival": "toestandvanoorlewing", "mycity": "mystad", "arknights": "arknights", "colorfulstage": "kleurvollefase", "bloonstowerdefense": "bloonstowerdefensie", "btd": "btd", "clashroyale": "klasiekroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "lotgra<PERSON>rde", "hyperfront": "hyperfront", "knightrun": "riddershard<PERSON>", "fireemblemheroes": "vuurwapenhelde", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "foons<PERSON><PERSON><PERSON><PERSON>", "kingschoice": "koningskeuse", "guardiantales": "guardianstories", "petrolhead": "petrolkop", "tacticool": "taktiescool", "cookierun": "koekiebaan", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "handwerker", "supersus": "supersus", "slowdrive": "stadigrits", "headsup": "kopop", "wordfeud": "woordgeveg", "bedwars": "bedoorlog", "freefire": "<PERSON><PERSON><PERSON><PERSON>", "mobilegaming": "mobielespeletjies", "lilysgarden": "lilystuin", "farmville2": "boerderijville2", "animalcrossing": "diereoorsteek", "bgmi": "bgmi", "teamfighttactics": "spannevegtingstaktieke", "clashofclans": "gevegvanstamme", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "diear<PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "noodhk", "enstars": "ensterre", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "sku<PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdroom", "clashofclan": "klapvanstamme", "starstableonline": "sterrestallelynlyn", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "tydprinses", "beatstar": "beatster", "dragonmanialegend": "drak<PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON>", "androidgames": "android<PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "misdaadjo<PERSON>", "summonerswar": "summonerswar", "cookingmadness": "kook<PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaengel", "lordsmobile": "lordsmobile", "tinybirdgarden": "kin<PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "b<PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "oorlogsrobote", "mirrorverse": "s<PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "oorlogvleuels", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiboo", "apexlegendmobile": "apexlegendemobiel", "ingress": "<PERSON>gang", "slugitout": "slugitout", "mpl": "mpl", "coinmaster": "muntmeester", "punishinggrayraven": "strafgrijngraaf", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "speletjievansultane", "arenabreakout": "arenabreekuit", "wolfy": "wolfy", "runcitygame": "hardloopminestad", "juegodemovil": "mobielspel", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "swartwoestynmobiel", "rollercoastertycoon": "rollercoastertekonслот", "grandchase": "grootjag", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "geesindustrie", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "skitterendenik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "padnaarniks", "sealm": "sealm", "shadowfight3": "skadugeveg3", "limbuscompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "sloperspeletjie3", "wordswithfriends2": "woordemetvriende2", "soulknight": "siel<PERSON><PERSON>", "purrfecttale": "purr<PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "damelike", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "oesdorp", "perfectworldmobile": "perfektewêreldmobiel", "empiresandpuzzles": "rykdespelleenpuzzles", "empirespuzzles": "empirepuzzles", "dragoncity": "drakestad", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "kleinnagmerrie", "aethergazer": "aether<PERSON><PERSON>", "mudrunner": "modderhardloper", "tearsofthemis": "t<PERSON><PERSON><PERSON><PERSON>", "eversoul": "ewer<PERSON>l", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombievlugtelinge", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "ma<PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobielelegendeplofplof", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "kook<PERSON><PERSON>", "cabalmobile": "cabalmobiel", "streetfighterduel": "straatvechterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "meisies<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldleef", "soulseeker": "<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "oorwinning", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ma<PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosmobile": "mobielespeletjies", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "speletjemobilelegends", "timeraiders": "tydrovers", "gamingmobile": "gamingmobiel", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "diegevegskatte", "dnd": "dnd", "quest": "soektog", "giochidiruolo": "speeljourol", "dnd5e": "dnd5e", "rpgdemesa": "rpgtafel", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "reisgangertrpg", "2300ad": "2300nc", "larp": "larp", "romanceclub": "romanseclub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokémonmysteriekerker", "pokemonlegendsarceus": "poké<PERSON>legends<PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkristal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON>d", "pokemonranger": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokémonverenig", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokémonpers", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "varkie", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "sakmonsters", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON><PERSON>es", "teamystic": "teamystic", "pokeball": "pokebal", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "glinskpokemon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "y<PERSON><PERSON>ed", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "ombreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonslaap", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmeester", "pokémonsleep": "pokémonslaap", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasour", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "skitterjagter", "ajedrez": "skaak", "catur": "<PERSON>ur", "xadrez": "skak", "scacchi": "skaak", "schaken": "skaak", "skak": "skak", "ajedres": "skaak", "chessgirls": "skaakmeisies", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "japanneseskaak", "chinesechess": "chinesejaag", "chesscanada": "skaakcanada", "fide": "fide", "xadrezverbal": "xadrez<PERSON><PERSON><PERSON>", "openings": "oopings", "rook": "rokie", "chesscom": "chesscom", "calabozosydragones": "keldersendrake", "dungeonsanddragon": "keldersenwoel", "dungeonmaster": "kerkermeester", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "o<PERSON><PERSON><PERSON><PERSON>", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "dielegendofthevoxmachina", "doungenoanddragons": "dungeonsanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftkampioenskap", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dromesmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodifikasies", "mcc": "mcc", "candleflame": "k<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "byvoegsels", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "hemelblokkie", "minecraftpocket": "minecraftsak", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftrekenaar", "betweenlands": "tussenlande", "minecraftdungeons": "minecraftkerker", "minecraftcity": "minecrafthandstad", "pcgamer": "pcs<PERSON>er", "jeuxvideo": "speelvideo", "gambit": "gambiet", "gamers": "spelers", "levelup": "<PERSON><PERSON><PERSON>", "gamermobile": "gamermobiel", "gameover": "speldood", "gg": "gg", "pcgaming": "rekenaarspeletjies", "gamen": "gamen", "oyunoynamak": "speelspel", "pcgames": "pcspellen", "casualgaming": "ontspannespeletjies", "gamingsetup": "spelet<PERSON><PERSON>ops<PERSON>ling", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "pcspel", "gamerboy": "gamerseun", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "speelstylen", "consoleplayer": "konsole<PERSON>eler", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "semiwëreldspeler", "gamergirls": "gamergirls", "gamermoms": "gamermas", "gamerguy": "speelma<PERSON>", "gamewatcher": "speletjiekyker", "gameur": "spelet<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "speelmeisies", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "spannep<PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "avontuurlustig", "alax": "alax", "avgn": "gemid<PERSON><PERSON>", "oldgamer": "ougamers", "cozygaming": "gerieflikspel", "gamelpay": "gamelbetal", "juegosdepc": "rekenaarspeletjies", "dsswitch": "dsswitch", "competitivegaming": "mededingendespeletjies", "minecraftnewjersey": "minecrafteenjersie", "faker": "fakester", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksuelegaming", "gamepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "meisiegamer", "fnfmods": "fnfmods", "dailyquest": "daaglikseuitdaging", "gamegirl": "spe<PERSON><PERSON><PERSON>", "chicasgamer": "meisiegamer", "gamesetup": "spelet<PERSON><PERSON>ops<PERSON>ling", "overpowered": "oorweldigend", "socialgamer": "sosialespeletjie", "gamejam": "spelet<PERSON><PERSON><PERSON>", "proplayer": "prospeler", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "myspan", "republicofgamers": "republiekvanspelers", "aorus": "aorus", "cougargaming": "koubargaming", "triplelegend": "drievoud<PERSON><PERSON>", "gamerbuddies": "gamermaats", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "christ<PERSON>kes<PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdspelers", "afk": "afk", "andregamer": "andregamer", "casualgamer": "ontspannegameerder", "89squad": "89span", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insek", "gemers": "gemers", "oyunizlemek": "speletjiekyk", "gamertag": "spelernaam", "lanparty": "lanpartytjie", "videogamer": "videospeletjie", "wspólnegranie": "gesamentlikespeel", "mortdog": "mortdog", "playstationgamer": "playstationspelers", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gesondegamers", "gtracing": "gtracing", "notebookgamer": "notebookspeler", "protogen": "protogen", "womangamer": "vrouespelers", "obviouslyimagamer": "duidelikekiesgamertjie", "mario": "mario", "papermario": "pap<PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "versameler", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nulekraak", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusiek", "sonicthehedgehog": "sonic<PERSON><PERSON><PERSON><PERSON>", "sonic": "sonies", "fallguys": "val<PERSON>ys", "switch": "skakel", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "legendoft<PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "asseverdediger", "ssbm": "ssbm", "skychildrenofthelight": "hemelkindersvanlig", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tranevanmekoning", "walkingsimulators": "staproksimulators", "nintendogames": "nintendospelletjes", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "draken<PERSON><PERSON>", "harvestmoon": "oesmaanskyn", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "my<PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51speelgoed", "earthbound": "aardegebound", "tales": "verhale", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "dieretjiesoversteek", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobroers", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "driekhoekstrategie", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "konkerssleghaardag", "nintendos": "nintendos", "new3ds": "nuwe3ds", "donkeykongcountry2": "donkeykongland2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysupersterre", "marioandsonic": "marioensonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "geb<PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zyra": "zyra", "redcanids": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lol<PERSON>", "leagueoflegend": "ligavandlegendes", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "ligavanlegendesspanje", "aatrox": "aaatrox", "euw": "eww", "leagueoflegendseuw": "ligavanlegendeseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON><PERSON>", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "zedhoof", "hexgates": "hex<PERSON><PERSON><PERSON>", "hextech": "hektegniek", "fortnitegame": "fortnitegame", "gamingfortnite": "speletjiesfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrospelletjies", "scaryvideogames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "videogameskepper", "megamanzero": "megamanzero", "videogame": "videospeletjie", "videosgame": "speletjiesvideo", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "oorsig", "ow2": "ow2", "overwatch2": "oorwag2", "wizard101": "tovernaar101", "battleblocktheater": "gevegb<PERSON>kt<PERSON><PERSON>", "arcades": "speletjieshallen", "acnh": "acnh", "puffpals": "puffmaats", "farmingsimulator": "boerde<PERSON>jsimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxafrikaans", "erlc": "erlc", "sanboxgames": "sandbokspelletjes", "videogamelore": "speletjiesverhale", "rollerdrome": "rollerdrome", "parasiteeve": "parasietnaand", "gamecube": "gamecube", "starcraft2": "sterrewêreld2", "duskwood": "skemerwoud", "dreamscape": "droomland", "starcitizen": "sterrekunstenaar", "yanderesimulator": "yanderesimulator", "grandtheftauto": "g<PERSON><PERSON><PERSON><PERSON>", "deadspace": "dooies<PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "spelet<PERSON><PERSON>", "theoldrepublic": "dieourepubliek", "videospiele": "spelet<PERSON><PERSON>", "touhouproject": "touhouprojek", "dreamcast": "droomgroep", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retrospelletjies", "retroarcade": "retroarcade", "vintagecomputing": "oudeskuif", "retrogaming": "retrogaming", "vintagegaming": "oudskoolspeel", "playdate": "speeldate", "commanderkeen": "kommandantkeen", "bugsnax": "buxsnax", "injustice2": "onreg2", "shadowthehedgehog": "s<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "lugspel", "zenlife": "zenlewe", "beatmaniaiidx": "beatmaniaiidx", "steep": "steil", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "middeleeuws", "consolegaming": "konsoelspeletjies", "konsolen": "konsolen", "outrun": "outrun", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "speletjiesgruwel", "monstergirlquest": "monstergirlquest", "supergiant": "superreus", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "ougames", "bethesda": "bethesda", "jackboxgames": "jackboxspeletjies", "interactivefiction": "interaktieweverslaggewing", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "dielaastevanons2", "amantesamentes": "liefdeskoppies", "visualnovel": "vis<PERSON><PERSON><PERSON>", "visualnovels": "visueleposte", "rgg": "rgg", "shadowolf": "s<PERSON><PERSON><PERSON>", "tcrghost": "tcrspook", "payday": "salarisdag", "chatherine": "katherine", "twilightprincess": "twilightprinses", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sanddoos", "aestheticgames": "estetiesespeletjies", "novelavisual": "nuweafbeelding", "thecrew2": "diecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "jammer", "godhand": "godhand", "leafblowerrevolution": "blaasbladrevolusie", "wiiu": "wiiu", "leveldesign": "vlakontwerp", "starrail": "sterraail", "keyblade": "sleutelblad", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsomtyds", "novelasvisuales": "vis<PERSON><PERSON><PERSON>", "robloxbrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "speletjiesretro", "videojuejos": "videos<PERSON><PERSON><PERSON><PERSON>", "videogamedates": "videospeletjiesdatings", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "netomdat3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "terugvanrekening", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "maniakmansion", "crashracing": "botsracing", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oudskoolspeletjies", "hellblade": "hellblade", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "beyondtwosouls", "gameuse": "speletjiegebruikers", "offmortisghost": "afwesgees", "tinybunny": "tienerbunny", "retroarch": "retroarch", "powerup": "kragop", "katanazero": "katanazero", "famicom": "famikom", "aventurasgraficas": "avontuurlikeprente", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkades", "f123": "f123", "wasteland": "woestyn", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON>", "coralisland": "koraaleiland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "anotherwereld", "metaquest": "metaquest", "animewarrios2": "animeoorloë2", "footballfusion": "voetbalversmelting", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legoma<PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "gebrokemetaal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "stapelvanalk<PERSON>ol", "simulator": "simulator", "symulatory": "simulatorie", "speedrunner": "spoedrenner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandaanlyn", "skylander": "skylander", "boyfrienddungeon": "seunvriendkerker", "toontownrewritten": "toontownhersk<PERSON><PERSON>", "simracing": "si<PERSON><PERSON><PERSON>", "simrace": "si<PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "stedelikechaos", "heavenlybodies": "<PERSON><PERSON><PERSON>gg<PERSON>", "seum": "seum", "partyvideogames": "partyspeletjies", "graveyardkeeper": "begraafplaasbewaker", "spaceflightsimulator": "ruimtevlugsimulator", "legacyofkain": "nalatenskapvankain", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "kosenvideospeletjies", "oyunvideoları": "speelvideos", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "vragmotorsimulator", "horizonworlds": "horisonwerde", "handygame": "handigspeletjie", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON>ospellet<PERSON><PERSON>", "oldschoolvideogames": "ouklasvideospeletjies", "racingsimulator": "rennsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "liedpop", "famitsu": "famitsu", "gatesofolympus": "poortevanolimpus", "monsterhunternow": "monsterjagnou", "rebelstar": "rebellester", "indievideogaming": "indievideospel", "indiegaming": "indiegaming", "indievideogames": "indievideospeletjies", "indievideogame": "indiekomputerspel", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "bufffortres", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projekl", "futureclubgames": "toekomsclubspelletjes", "mugman": "mugbaba", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "agterstand", "gamebacklog": "speletjiesagterstand", "gamingbacklog": "speelterugwerk", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON>pel<PERSON><PERSON><PERSON>", "achievementhunter": "prestasiejagter", "cityskylines": "stadsskyne", "supermonkeyball": "superaapbal", "deponia": "deponia", "naughtydog": "stouth<PERSON>", "beastlord": "beestheer", "juegosretro": "retrospelletjes", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriendieblinde<PERSON>ud", "alanwake": "alanwake", "stanleyparable": "stanleyste<PERSON>", "reservatoriodedopamin": "reservatoriodopamin", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "drakensink", "vivapiñata": "vivapieñata", "ilovekofxv": "ekliefkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "seelepienk", "saintseiya": "heiligeseiya", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "beginnerd", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "harts<PERSON><PERSON><PERSON>", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON>", "animescaling": "animeskaal", "animewithplot": "animemetnarratief", "pesci": "visse", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "<PERSON><PERSON><PERSON><PERSON>", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseiso1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON>", "animecover": "animecover", "thevisionofescaflowne": "dievisievanescaflowne", "slayers": "vers<PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletgebondhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "vuurmag", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "toekomsdagboek", "fairytail": "sprokiesstaal", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "gemaakinaafgrond", "parasyte": "parasiet", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "meerminstemmelodie", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "karnaval", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "swartlagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformers", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraw<PERSON><PERSON>", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "nsekerehowsmagieseindeks", "sao": "saao", "blackclover": "<PERSON>wart<PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "<PERSON><PERSON><PERSON><PERSON>", "aot": "aot", "sk8theinfinity": "sk8dieoneindigheid", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamilie", "rezero": "rezero", "swordartonline": "swaardkunstonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggprioriteit", "angelsofdeath": "engeldood", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "drakbal<PERSON>per", "hypnosismic": "hipnosiesies", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "<PERSON><PERSON><PERSON><PERSON>", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "diejontenieenbeest", "fistofthenorthstar": "vuisvandelnoordster", "mazinger": "mazinger", "blackbuttler": "swartbutler", "towerofgod": "torenvangod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "ho<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON>sagas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "koddigenkruipend", "martialpeak": "<PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "syhoogpuntmeisie", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "wederkomsfee", "shinji": "shinji", "zerotwo": "zerotwee", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "g<PERSON><PERSON><PERSON>", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "a<PERSON><PERSON>", "sailorsaturn": "matrosesaturn", "dio": "dio", "sailorpluto": "seerpluto", "aloy": "aloy", "runa": "runa", "oldanime": "oudan<PERSON>", "chainsawman": "kettingsaagmanga", "bungoustraydogs": "bungoustraydogs", "jogo": "speel", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "swartbutler", "ergoproxy": "ergoproxy", "claymore": "<PERSON><PERSON><PERSON><PERSON>", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "vrugtemandjie", "devilmancrybaby": "<PERSON><PERSON><PERSON>lmanhuilbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "diestofwatneverlandbelowe", "monstermanga": "monstermanga", "yourlieinapril": "jou<PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggydieclown", "bokunohero": "bokunoheld", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "diepseegevangene", "jojolion": "jojo<PERSON>", "deadmanwonderland": "doodmanwonderland", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "kosoorloë", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blouperiode", "griffithberserk": "griff<PERSON><PERSON><PERSON>nd", "shinigami": "shinigami", "secretalliance": "geheimealliansie", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinverslaggever", "detectiveconan": "speurderconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spiofamilie", "airgear": "lugto<PERSON><PERSON>", "magicalgirl": "ma<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "gevangenisschool", "thegodofhighschool": "diegodvanhoërskool", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "my<PERSON><PERSON><PERSON>ling", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozenmeisie", "animeuniverse": "<PERSON><PERSON><PERSON>", "swordartonlineabridge": "swaardkunstonlinebrug", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ongelukkigondood", "romancemanga": "romantiesemanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinië", "lolicon": "lolikon", "demonslayertothesword": "demonslaggernaarswaard", "bloodlad": "blo<PERSON><PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "v<PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romantieseanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "kersiekrag", "housekinokuni": "<PERSON>ui<PERSON><PERSON><PERSON>", "recordragnarok": "stoorragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prins<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "moordklas", "animemanga": "animemanga", "bakuman": "bak<PERSON><PERSON>", "deathparade": "dood<PERSON>ade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanneseanime", "animespace": "animespace", "girlsundpanzer": "meisiesonderpanser", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungod<PERSON>s", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratmannet", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbel", "peachgirl": "persemeisie", "cavalieridellozodiaco": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklub", "dragonquestdai": "drakenspoorkwaad", "heartofmanga": "hartvanmanga", "deliciousindungeon": "heerlijkeindungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "rekordvanragnarok", "funamusea": "pretamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "slaagskoolboot", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "oorgegear", "toriko": "<PERSON><PERSON>o", "ravemaster": "rave<PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "druppelsvangod", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "omgekeerdeharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "fantastieseonderwyseronizuka", "gridman": "roosterman", "kokorone": "koko<PERSON>", "soldato": "sold<PERSON>t", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "toerusting5", "grandbluedreaming": "grootbloudroom", "bloodplus": "bloedplus", "bloodplusanime": "bloedgeplusanime", "bloodcanime": "blo<PERSON>ni<PERSON>", "bloodc": "bloedc", "talesofdemonsandgods": "storiesvanduiwelsengode", "goreanime": "bloedigeanime", "animegirls": "animemeisies", "sharingan": "<PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "kra<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "spetteranime", "splatter": "spat", "risingoftheshieldhero": "opstandingvandieskildheld", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjusoosies", "childrenofthewhales": "kinders<PERSON><PERSON>es", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superkampioene", "animeidols": "animeidole", "isekaiwasmartphone": "isekaiwasnsmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magiesemeisies", "callofthenight": "roepvandedoodnacht", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "s<PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachroniek", "findermanga": "vindermanga", "princessjellyfish": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekus", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "herinnersterrigting", "animeverse": "animewereld", "persocoms": "persocoms", "omniscientreadersview": "omniesiendeleesperspektief", "animecat": "animekat", "animerecommendations": "animaanbevelings", "openinganime": "oopanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "myeensromantiesekomedi", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobielevegterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobielepakgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleik", "deathnote": "<PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "volmetaalalchimist", "ghiaccio": "ys", "jojobizarreadventures": "jojobizarredaventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militêreanime", "greenranger": "gro<PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "sterrevolk", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animestad", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "eenstuk", "animeonepiece": "animeeenstuk", "dbz": "dbz", "dragonball": "<PERSON><PERSON><PERSON><PERSON>", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonavontuur", "hxh": "hxh", "highschooldxd": "hoërskooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "aanvaloptitan", "erenyeager": "erenyeager", "myheroacademia": "myheldakademie", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "opnamegroep", "onepieceanime": "eenstukanime", "attaquedestitans": "aanvalvansteke", "theonepieceisreal": "dieeenstukisregteggemaakt", "revengers": "w<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobps<PERSON>es", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "digimonverhaal", "digimontamers": "digimontamers", "superjail": "supergevangevangenis", "metalocalypse": "metaalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "onsanhostklub", "flawlesswebtoon": "flawlesswebtoon", "kemonofriends": "kemonovriende", "utanoprincesama": "staanopvirprinsesamma", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "vliegendeheks", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "netomdat", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "alheiligestraat", "recuentosdelavida": "leweverhale"}