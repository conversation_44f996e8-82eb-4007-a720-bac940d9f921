{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramm", "astrology": "astrologie", "cognitivefunctions": "kognitivefunktionen", "psychology": "psychologie", "philosophy": "philosophie", "history": "geschichte", "physics": "physik", "science": "wissenschaft", "culture": "kultur", "languages": "sprachen", "technology": "technologie", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiememes", "enneagrammemes": "enneagrammmemes", "showerthoughts": "duschgedanken", "funny": "witzig", "videos": "videos", "gadgets": "gadgets", "politics": "politik", "relationshipadvice": "beziehungsratschläge", "lifeadvice": "lebensratschläge", "crypto": "krypto", "news": "nachrichten", "worldnews": "worldnews", "archaeology": "archäologie", "learning": "lernen", "debates": "debatten", "conspiracytheories": "verschwörungstheorien", "universe": "universum", "meditation": "meditation", "mythology": "mythologie", "art": "kunst", "crafts": "handwerk", "dance": "tanzen", "design": "design", "makeup": "makeup", "beauty": "beauty", "fashion": "mode", "singing": "singen", "writing": "schre<PERSON>n", "photography": "fotografie", "cosplay": "cosplay", "painting": "malen", "drawing": "<PERSON><PERSON><PERSON><PERSON>", "books": "<PERSON><PERSON><PERSON>", "movies": "filme", "poetry": "poesie", "television": "fern<PERSON><PERSON>", "filmmaking": "filmproduktion", "animation": "animation", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "dokumentationen", "mystery": "mystery", "comedy": "comedy", "crime": "krimi", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romantik", "realitytv": "realitytv", "action": "action", "music": "musik", "blues": "blues", "classical": "klassisch", "country": "country", "desi": "desi", "edm": "edm", "electronic": "elektronisch", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "lateinamerikanisch", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "reisen", "concerts": "konzerte", "festivals": "festivals", "museums": "museen", "standup": "standup", "theater": "theater", "outdoors": "outdoor", "gardening": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partying": "party", "gaming": "gaming", "boardgames": "brettspiele", "dungeonsanddragons": "dungeonsanddragons", "chess": "schach", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "essen", "baking": "backen", "cooking": "kochen", "vegetarian": "vegetarisch", "vegan": "vegan", "birds": "vögel", "cats": "katzen", "dogs": "hunde", "fish": "fische", "animals": "tiere", "blacklivesmatter": "blacklivesmatter", "environmentalism": "umweltschutz", "feminism": "feminismus", "humanrights": "menschenrechte", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "freiwilligenarbeit", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "basketball", "boxing": "boxen", "cricket": "cricket", "cycling": "rad<PERSON><PERSON>", "fitness": "fitness", "football": "fußball", "golf": "golf", "gym": "fitnessstudio", "gymnastics": "turnen", "hockey": "hockey", "martialarts": "kampfsport", "netball": "netball", "pilates": "pilates", "pingpong": "tischtennis", "running": "laufen", "skateboarding": "skateboarden", "skiing": "schi<PERSON><PERSON>", "snowboarding": "snowboarden", "surfing": "surfen", "swimming": "schwi<PERSON>n", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "gewichtheben", "yoga": "yoga", "scubadiving": "tauchen", "hiking": "wandern", "capricorn": "steinbock", "aquarius": "<PERSON><PERSON><PERSON>", "pisces": "fische", "aries": "widder", "taurus": "stier", "gemini": "zwillinge", "cancer": "krebs", "leo": "löwe", "virgo": "j<PERSON><PERSON><PERSON>", "libra": "waage", "scorpio": "skorpion", "sagittarius": "s<PERSON><PERSON><PERSON><PERSON>", "shortterm": "kurzfristig", "casual": "<PERSON><PERSON><PERSON>", "longtermrelationship": "langzeitbeziehung", "single": "single", "polyamory": "polyamorie", "enm": "beziehungohneausschließlichkeit", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "schwul", "lesbian": "lesbe", "bisexual": "bisexuell", "pansexual": "pansexuell", "asexual": "asexuell", "reddeadredemption2": "reddeadredemption2", "dragonage": "drac<PERSON><PERSON><PERSON>", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "wachhunde", "dislyte": "dislyte", "rougelikes": "roguelikes", "kingsquest": "königsquest", "soulreaver": "seelenräuber", "suikoden": "su<PERSON><PERSON>", "subverse": "subversiv", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "sonnenuntergangsüberdosis", "arkham": "arkham", "deusex": "<PERSON>uss<PERSON>", "fireemblemfates": "fireemblemschicksale", "yokaiwatch": "yo<PERSON><PERSON><PERSON>", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guildwars": "gildenkriege", "openworld": "<PERSON><PERSON>welt", "heroesofthestorm": "heldendersturm", "cytus": "cytus", "soulslike": "seelenlike", "dungeoncrawling": "dungeoncrawlen", "jetsetradio": "jetsetradio", "tribesofmidgard": "stämmevonmidgard", "planescape": "planescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "farbenfreak", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "patfofexil", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "rollenspiel", "witcher": "hexer", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modden", "charactercreation": "charaktererstellung", "immersive": "eintauchen", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidemotivation", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "liebesverr<PERSON><PERSON>", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirediemaskerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "wegfinder", "pathfinder2ndedition": "pathfinder2teedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "blutaufdemuhrenturm", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "schwerkraftrausch", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "einmaligesache", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "überlord", "yourturntodie": "deinerundezusterben", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "zufluchtsort", "gurps": "gurps", "darkestdungeon": "dunkelsterkerker", "eclipsephase": "eclipsephase", "disgaea": "disgaea", "outerworlds": "äußerewelten", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "schädelmädchen", "nightcity": "nachtstadt", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "wahnsinnsgefechte", "jaggedalliance2": "gezackteallianz2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamritter", "forgottenrealms": "vergessenewelten", "dragonlance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenaofvalor": "arenavonwert", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "kinddeslichts", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "diedivision2", "lineage2": "linien2", "digimonworld": "digimonwelt", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulkanversum", "fracturedthrones": "gebrochenethronsitze", "horizonforbiddenwest": "horizontverboteneswesten", "twewy": "twewy", "shadowpunk": "schattenpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartsgeheimnis", "deltagreen": "deltagrün", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "lastepoche", "starfinder": "sternenfinder", "goldensun": "goldensun", "divinityoriginalsin": "göttlichesursünde", "bladesinthedark": "klingeninderdunkelheit", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrot", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordnenfallen", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "teufelsüberlebender", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "göttlichkeit", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "alteweltblues", "adventurequest": "abenteuerquest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rollenspielspiele", "roleplayinggames": "rollenspiele", "finalfantasy9": "finalfantasy9", "sunhaven": "sonnenhaven", "talesofsymphonia": "geschichtenvonsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "zerrissenestadt", "myfarog": "<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "heiligesunterwelt", "chainedechoes": "kettenechos", "darksoul": "darksoul", "soulslikes": "soulslikes", "othercide": "anderstod", "mountandblade": "booundbogen", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "säulenderewigkeit", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "diedivision", "hellocharlotte": "hallocharlotte", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werwolfapokalypse", "aveyond": "aveyond", "littlewood": "kleineholzstadt", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorherz", "fable3": "fabel3", "fablethelostchapter": "fabeldasverlorenekapitel", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON>", "oldschoolrevival": "oldschoolrevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "savageworlds", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomherz1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "dunklesverlies", "juegosrpg": "rollenspielspiele", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomherz3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "erntefall", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "drakarochdämonen", "skiesofarcadia": "himmelsvonarcadia", "shadowhearts": "s<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblut", "breathoffire4": "atemderflamme4", "mother3": "mutter3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "rollenspielspiele", "roleplaygame": "rollenspielspiel", "fabulaultima": "fabulault<PERSON>", "witchsheart": "herzderhexe", "harrypottergame": "harry<PERSON><PERSON><PERSON>l", "pathfinderrpg": "pfadfinderrpg", "pathfinder2e": "pfadfinder2e", "vampirilamasquerade": "vampirlamaskerade", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "zaubermarionette", "dragonageorigins": "dragonageursprünge", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "jag<PERSON><PERSON>", "albertodyssey": "albertodyssee", "monsterhunterworld": "monsterjägerwelt", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "schattenherzbund", "bladesoul": "kling<PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "reichkomm", "awplanet": "awplanet", "theworldendswithyou": "dieweltendetmitdir", "dragalialost": "dragalialost", "elderscroll": "ältereinstellung", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytaktik", "grandia": "grandia", "darkheresy": "dunklehäresie", "shoptitans": "shoptitanen", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "schwarzesbuch", "skychildrenoflight": "himmel<PERSON>der<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "heiligesgoldedition", "castlecrashers": "s<PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gothicspiel", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "propunk", "starrails": "starrails", "cityofmist": "stadtdesnebel", "indierpg": "indierpg", "pointandclick": "pointandclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "todesstraßenachkanada", "palladium": "palladium", "knightjdr": "ritter<PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "feuerzeichen", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "gespenstvontsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterjägererwacht", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktischesrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "ewigesonate", "princessconnect": "princessconnect", "hexenzirkel": "hexenkreis", "cristales": "kristalente", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketsage", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efußball", "nba2k": "nba2k", "egames": "espiele", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaderträumer", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "traumhack", "gaimin": "gaimin", "overwatchleague": "overwatchliga", "cybersport": "cybersport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitive", "t3arena": "t3arena", "valorantbr": "valorantde", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "halbzeit", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "ziegensimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "freiheitsplanet", "transformice": "transformice", "justshapesandbeats": "einfachformenundbeats", "battlefield4": "battlefield4", "nightinthewoods": "nachtimwald", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risikoregens2", "metroidvanias": "metroidvanias", "overcooked": "überkocht", "interplanetary": "interplanetarisch", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "totenzellen", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "g<PERSON><PERSON>e", "dwarffortress": "zwergenfestung", "foxhole": "fuchsbau", "stray": "streunend", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "schlachtfeld1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboot", "eyeb": "auge", "blackdesert": "schwarzewüste", "tabletopsimulator": "tabletopsimulator", "partyhard": "partyhart", "hardspaceshipbreaker": "harterraumschiffzerstörer", "hades": "hades", "gunsmith": "waffensch<PERSON>d", "okami": "<PERSON>ami", "trappedwithjester": "eingeschlossenmitjester", "dinkum": "dinkum", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "regenwelt", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "koloniesimulation", "noita": "noita", "dawnofwar": "morgenderkrieg", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "dunkelunddunkler", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "datingsims", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON>elentkommen", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "neustad<PERSON>", "citiesskylines": "stadtansichten", "defconheavy": "defconheavy", "kenopsia": "kenopsie", "virtualkenopsia": "virtuellekenopsie", "snowrunner": "sch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bibliothekderruine", "l4d2": "l4d2", "thenonarygames": "dienonarygames", "omegastrikers": "omegastrikers", "wayfinder": "wegfinder", "kenabridgeofspirits": "kenabrückedergeister", "placidplasticduck": "ruhigerplastikschwan", "battlebit": "battlebit", "ultimatechickenhorse": "ultimat<PERSON><PERSON><PERSON><PERSON>pf<PERSON>", "dialtown": "dialstadt", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "tinny<PERSON>e", "cozygrove": "gemütlicheshain", "doom": "untergang", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "grenzgebiete", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcryspiele", "paladins": "paladine", "earthdefenseforce": "erdeverteidigen", "huntshowdown": "huntshowdown", "ghostrecon": "gespensterrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "machmitimteam", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "aufstandsandsturm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "spiegelkanten", "divisions2": "divisionen2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "coldwarzombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "kreuzcode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "scharfschützenelite", "modernwarfare": "modernekriegsführung", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "borderlands", "owerwatch": "ower<PERSON>", "rtype": "rtyp", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "weltderschlachtschiffe", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "auftrag<PERSON>ller", "masseffect": "masseneffekt", "systemshock": "systemschock", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopsdielinie", "killingfloor2": "killingfloor2", "cavestory": "höhlenstory", "doometernal": "doometernal", "centuryageofashes": "jahrhundertzeitaltderasche", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tydertasmanischetiger", "generationzero": "generationnull", "enterthegungeon": "betreted<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernewarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetundclank", "chexquest": "chexquest", "thephantompain": "derphantomschmerz", "warface": "kriegsgesicht", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomischesherz", "blackops3": "blackops3", "vampiresurvivors": "vampirüberlebende", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "freedoom", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "fragboo", "tinytina": "<PERSON><PERSON>", "gamepubg": "spielepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsohnederfreiheit", "juegosfps": "fpsspiele", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "shatterline", "blackopszombies": "blackopszombies", "bloodymess": "blutigesch<PERSON>s", "republiccommando": "republikkommandos", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldat", "groundbranch": "bodenablage", "squad": "crew", "destiny1": "destiny1", "gamingfps": "gamingfps", "redfall": "rotfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "eingeschrieben", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "gepanzerteskern", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswunderwelten", "halo2": "halo2", "payday2": "zahltag2", "cs16": "cs16", "pubgindonesia": "pubgindonesien", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgdeutsch", "titanfall2": "titanfall2", "soapcod": "seifenkodex", "ghostcod": "geistcod", "csplay": "c<PERSON><PERSON><PERSON>", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "erdbebenmeister", "halo3": "halo3", "halo": "hallo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splinter<PERSON>le", "neonwhite": "<PERSON><PERSON><PERSON>", "remnant": "überbleibsel", "azurelane": "azurlane", "worldofwar": "weltderkriege", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "<PERSON><PERSON><PERSON>", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON>", "reddead": "rototot", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "meerderd<PERSON>be", "rust": "rost", "conqueronline": "erobereonline", "dauntless": "<PERSON><PERSON><PERSON>", "warships": "kriegsschiffe", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "flugsteigend", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "weltderpanzer", "crossout": "durchstreichen", "agario": "agario", "secondlife": "zweitesleben", "aion": "aion", "toweroffantasy": "towerderfantasie", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "ritteronline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "dasbandvonisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "abschaum", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "schwarzewüsteonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirat101", "honorofkings": "ehrederkönige", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmorpg", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassik", "worldofwarcraft": "wow", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "aschederschöpfung", "riotmmo": "riotmmo", "silkroad": "seidenstraße", "spiralknights": "spiralritter", "mulegend": "mulegend", "startrekonline": "startekonline", "vindictus": "rache", "albiononline": "albiononline", "bladeandsoul": "klingeundseele", "evony": "evony", "dragonsprophet": "dragonsprophet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multiplayer", "angelsonline": "engelonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversumonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsalterepublik", "grandfantasia": "großefantasie", "blueprotocol": "blueprotocol", "perfectworld": "perfektewelt", "riseonline": "onlineaufsteigen", "corepunk": "kernpunk", "adventurequestworlds": "abenteuerquestwelten", "flyforfun": "fliegenzumspaß", "animaljam": "animaljam", "kingdomofloathing": "königreichdesverdrusses", "cityofheroes": "stadtderhelden", "mortalkombat": "mortalkombat", "streetfighter": "straßenkämpfer", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON>eh<PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosevolution", "soulcalibur": "seelecalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "strassenvonrage", "mkdeadlyalliance": "mkfurchtbareallianz", "nomoreheroes": "keineheldenmehr", "mhr": "mhr", "mortalkombat12": "mortalcombat12", "thekingoffighters": "derkönigderkämpfer", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightinggames", "blasphemous": "blasphemisch", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "kriegdermonstren", "jogosdeluta": "kämpferspiele", "cyberbots": "cyberbots", "armoredwarriors": "rüstungskrieger", "finalfight": "finalkampf", "poweredgear": "powergear", "beatemup": "prügelspiel", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "kampfspiele", "killerinstinct": "killerinstinkt", "kingoffigthers": "königderkämpfer", "ghostrunner": "gespenstläufer", "chivalry2": "ritterlichkeit2", "demonssouls": "demonseelen", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightnachfolger", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongspiel", "silksongnews": "silksongnews", "silksong": "seidensong", "undernight": "unternacht", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolutionturnier", "evomoment": "evomoment", "lollipopchainsaw": "lollipopkettensäge", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "geschichtenvonberseria", "bloodborne": "blutgeboren", "horizon": "horizont", "pathofexile": "pfaddesexils", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "blutgeboren", "uncharted": "unerforscht", "horizonzerodawn": "horizontnullmorgen", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationkumpels", "ps1": "ps1", "oddworld": "seltsamewelt", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "höllenloslassen", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "gottkrieg", "gris": "g<PERSON><PERSON><PERSON>", "trove": "schatzkammer", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "randwelt", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "biszummorgen", "touristtrophy": "touristenpokal", "lspdfr": "lspdfr", "shadowofthecolossus": "schatten_des_kolosseums", "crashteamracing": "crashteamracing", "fivepd": "fünfpd", "tekken7": "tekken7", "devilmaycry": "teufelmagweinen", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "samuraikriegskämpfer", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON>len<PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "männerjagd", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "partybär", "warharmmer40k": "warhammer40k", "fightnightchampion": "kampfnachtchampion", "psychonauts": "psychonauten", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "dieelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "dieälterenrollen", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "nichtsterben<PERSON><PERSON>nder", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "sternentdeck<PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "hausflipper", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "reichderkönige", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "trashfernsehen", "skycotl": "himmelcotl", "erica": "erica", "ancestory": "ahnenforschung", "cuphead": "kaffeetasse", "littlemisfortune": "kleineunglücksfee", "sallyface": "sallygesicht", "franbow": "franbow", "monsterprom": "monsterball", "projectzomboid": "projektzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "äußerewildnis", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultdeslamms", "duckgame": "entenspiel", "thestanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "towerunite": "turmvereinen", "occulto": "<PERSON>ku<PERSON>o", "longdrive": "langstreckenfahrt", "satisfactory": "zufriedenstellend", "pluviophile": "regentagelieber", "underearth": "unterdererde", "assettocorsa": "assettocorsa", "geometrydash": "geometriesprung", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "seelenüberbringer", "darkdome": "dunkeldom", "pizzatower": "pizzaturm", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "wahrheitoderpflicht", "game": "spiel", "rockpaperscissors": "steinscherepapier", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "schnitzeljagd", "yardgames": "ho<PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "wahroderf<PERSON>ch", "beerpong": "beerpong", "dicegoblin": "würfelgoblin", "cosygames": "gemütlichespiele", "datinggames": "datinggames", "freegame": "kostenlosespiel", "drinkinggames": "trinkspiele", "sodoku": "sodoku", "juegos": "spiele", "mahjong": "mahjong", "jeux": "spiele", "simulationgames": "simulationspiele", "wordgames": "wortspiele", "jeuxdemots": "wortspiele", "juegosdepalabras": "wortspiele", "letsplayagame": "lassunszockenspielen", "boredgames": "langeweilespiele", "oyun": "spiel", "interactivegames": "interaktivespiele", "amtgard": "amtgard", "staringcontests": "blick<PERSON>lle", "spiele": "spieltspiele", "giochi": "spiele", "geoguessr": "geoguessr", "iphonegames": "iphonespiele", "boogames": "boogames", "cranegame": "kranespiel", "hideandseek": "versteckenundsuchen", "hopscotch": "hüpfspiel", "arcadegames": "arcadespiele", "yakuzagames": "yakuzagames", "classicgame": "klassikerspiel", "mindgames": "kopfkino", "guessthelyric": "schätzdenliedtext", "galagames": "galagames", "romancegame": "liebesspiel", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "zungenbrecher", "4xgames": "4xspiele", "gamefi": "gamefi", "jeuxdarcades": "arcadegames", "tabletopgames": "brettspiele", "metroidvania": "metroidvania", "games90": "spiele90", "idareyou": "ichforderedichheraus", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "rennspiele", "ets2": "ets2", "realvsfake": "echtvsfalsch", "playgames": "spielspiele", "gameonline": "spielonline", "onlinegames": "onlinespiele", "jogosonline": "onlinespiele", "writtenroleplay": "geschriebenesrollenspiel", "playaballgame": "spieleinballspiel", "pictionary": "pictionary", "coopgames": "coopspiele", "jenga": "jenga", "wiigames": "wiigames", "highscore": "highscore", "jeuxderôles": "rollenspiele", "burgergames": "burgergames", "kidsgames": "kinders<PERSON>le", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwschwarzeausgabe", "jeuconcour": "spielwettbewerb", "tcgplayer": "tcgspieler", "juegodepreguntas": "fragenspiel", "gioco": "spiel", "managementgame": "managementspiel", "hiddenobjectgame": "verstecktesobjektspiel", "roolipelit": "röllienspiele", "formula1game": "formel1spiel", "citybuilder": "<PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "spielea<PERSON>de", "memorygames": "gedächtnisspiele", "vulkan": "vulkan", "actiongames": "actionspiele", "blowgames": "blowgames", "pinballmachines": "flippermaschinen", "oldgames": "altespiele", "couchcoop": "couchcoop", "perguntados": "fragenstellung", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imessagegames", "idlegames": "idlegames", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "pcspiele", "rétrogaming": "retrogaming", "logicgames": "logikspiele", "japangame": "japanga<PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "ubahnsurf", "jeuxdecelebrite": "berühmtespiel", "exitgames": "exitgames", "5vs5": "5vs5", "rolgame": "rollenspiel", "dashiegames": "dashiegames", "gameandkill": "spie<PERSON><PERSON>", "traditionalgames": "traditionellespiele", "kniffel": "kniffel", "gamefps": "spielfps", "textbasedgames": "textbasiertespiele", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospiele", "thiefgame": "diebspiel", "lawngames": "rase<PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "tischfußball", "tischfußball": "kicker", "spieleabende": "spie<PERSON><PERSON>de", "jeuxforum": "spieleforum", "casualgames": "casualgames", "fléchettes": "pfeile", "escapegames": "escapegames", "thiefgameseries": "dieboodiebspielreihe", "cranegames": "kranspiele", "játék": "boobock", "bordfodbold": "bordfußball", "jogosorte": "s<PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "autospiele", "onlineplay": "onlinezocken", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "zocknächte", "pursebingos": "taschenbingo", "randomizer": "zufallsentscheidungen", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "spielepc", "socialdeductiongames": "sozialededuktionsspiele", "dominos": "dominos", "domino": "domino", "isometricgames": "isometrischespiele", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "wahrheitundpflicht", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "schnitzeljagd", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "free2play", "fantasygame": "fantasyspiel", "gryonline": "gryonline", "driftgame": "driftspiel", "gamesotomes": "gamesotomes", "halotvseriesandgames": "halotvserienundspiele", "mushroomoasis": "pilzoase", "anythingwithanengine": "allesmitmotor", "everywheregame": "überallspiel", "swordandsorcery": "schwertundzauberei", "goodgamegiving": "gutesspielgeben", "jugamos": "zocken", "lab8games": "lab8spiele", "labzerogames": "labzerogames", "grykomputerowe": "gryycomputer", "virgogami": "virgogami", "gogame": "spiellos", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "minispiele", "ridgeracertype4": "ridgeracertyp4", "selflovegaming": "selbstliebegaming", "gamemodding": "gamemodding", "crimegames": "verbrechensspiele", "dobbelspellen": "dobbelspellen", "spelletjes": "spiele", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "charaden", "singleplayer": "<PERSON><PERSON>zelspieler", "coopgame": "coopspiel", "gamed": "gezockt", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "hauptspiel", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "schach<PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemievererbung", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolyspiel", "brettspiele": "brettspiele", "bordspellen": "brettspiele", "boardgame": "brettspiel", "sällskapspel": "gesellschaftsspiel", "planszowe": "brettspielen", "risiko": "risiko", "permainanpapan": "brettspiele", "zombicide": "zombicide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "b<PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "vierge<PERSON><PERSON>", "heroquest": "heldenquest", "giochidatavolo": "brettspieleschätzen", "farkle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carrom": "carrom", "tablegames": "brettspiele", "dicegames": "<PERSON><PERSON><PERSON><PERSON>piele", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "brettspiel<PERSON>", "jocuridesocietate": "jocuridesocietate", "deskgames": "schreibtischspiele", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmischebegegnung", "creationludique": "spielerischekreation", "tabletoproleplay": "brettspielrollenspiel", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchboardspiele", "infinitythegame": "infinitydasspiel", "kingdomdeath": "königreichtod", "yahtzee": "yahtzee", "chutesandladders": "rutschenundleitern", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "brettspiel", "planszówki": "brettspiele", "rednecklife": "red<PERSON><PERSON><PERSON>", "boardom": "langweiligeeinebrettspiele", "applestoapples": "äpfelzuäpfeln", "jeudesociété": "gesellschaftsspiel", "gameboard": "spiel<PERSON><PERSON>", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "brett<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "pferdeopoly", "deckbuilding": "deckbau", "mansionsofmadness": "häuserdeswahnsinns", "gomoku": "gomoku", "giochidatavola": "brettspielzeit", "shadowsofbrimstone": "schattenvonbrimstone", "kingoftokyo": "k<PERSON>nigvon<PERSON><PERSON>", "warcaby": "dame", "táblajátékok": "brettspiele", "battleship": "schlachtschiff", "tickettoride": "fahrtsticket", "deskovehry": "schreibtischspiele", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "brett<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "brettspiele", "xiángqi": "<PERSON>ian<PERSON><PERSON>", "jeuxsociete": "brettspiele", "gesellschaftsspiele": "brettspiele", "starwarslegion": "starwarslegion", "gochess": "gehschachspielen", "weiqi": "weiqi", "jeuxdesocietes": "gesellschaftsspiele", "terraria": "terraria", "dsmp": "dsmp", "warzone": "kriegszone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "tagz", "identityv": "identitätv", "theisle": "dieinsel", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "keinemannshimmel", "subnautica": "subnautica", "tombraider": "grabraider", "callofcthulhu": "c<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyunddietintenmaschine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "unteruns", "eco": "umwelt", "monkeyisland": "affe<PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafters", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "witchit", "pathologic": "pathologisch", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "derlangedunkelheit", "ark": "ark", "grounded": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "verrücktervater", "dontstarve": "ver<PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "wegdergiganten", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "diehinterzimmer", "backrooms": "hinterzimmer", "empiressmp": "empiressmp", "blockstory": "blockgeschichte", "thequarry": "dersteinbruch", "tlou": "tlou", "dyinglight": "sterbendeslicht", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "wirwenigenglück", "riseofempires": "aufstiegderreiche", "stateofsurvivalgame": "zustanddesüberlebensspiels", "vintagestory": "vintagestory", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "atmenedge", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "bestienausbermuda", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "überlebensangst", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "leerebahn", "lifeaftergame": "lebennachdemspiel", "survivalgames": "überlebensspiele", "sillenthill": "stilleshügbelt", "thiswarofmine": "dieserkriegistmeins", "scpfoundation": "scpstiftung", "greenproject": "grünprojekt", "kuon": "kuon", "cryoffear": "schreivorangst", "raft": "floß", "rdo": "rdo", "greenhell": "gr<PERSON>neh<PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "totpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "omi", "littlenightmares2": "kleinealbträume2", "signalis": "signalis", "amandatheadventurer": "amandadiekopfabenteurerin", "sonsoftheforest": "söhnedeswaldes", "rustvideogame": "rustspiel", "outlasttrials": "überstehdietrials", "alienisolation": "alienisolation", "undawn": "undawn", "7day2die": "7tage2sterben", "sunlesssea": "sonnenlose<PERSON>eer", "sopravvivenza": "überleben", "propnight": "propnacht", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "todesvers", "cataclysmdarkdays": "katalysmadunkletage", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "lebendanach", "ageofdarkness": "zeitalterderdunkelheit", "clocktower3": "uhrturm3", "aloneinthedark": "alleineinderdunkelheit", "medievaldynasty": "mittelalterlichedynastie", "projectnimbusgame": "projektnimbusspiel", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "weltbeherrschung", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "büromörder", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "zwerghäufer", "warhammer40kcrush": "warhammer40kverliebt", "wh40": "wh40", "warhammer40klove": "warhammer40kliebe", "warhammer40klore": "warhammer40kfolklore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ilovesororitas", "ilovevindicare": "ichliebevindicare", "iloveassasinorum": "iloveassasinorum", "templovenenum": "templovenenum", "templocallidus": "templa<PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "mörderhandwerk", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "zeitalterderimperien", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerzeitaltervonsigmar", "civilizationv": "zivilisationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "flügelspannweite", "terraformingmars": "marsterraforming", "heroesofmightandmagic": "heldenvonmachtundleidenschaft", "btd6": "btd6", "supremecommander": "supremekommandeur", "ageofmythology": "zeitalterdermythologie", "args": "args", "rime": "reim", "planetzoo": "planetzoo", "outpost2": "außenpost2", "banished": "verbannt", "caesar3": "caesar3", "redalert": "rotealarm", "civilization6": "zivilisation6", "warcraft2": "warcraft2", "commandandconquer": "befehlenunderobern", "warcraft3": "warcraft3", "eternalwar": "ewigerkrieg", "strategygames": "strategiegames", "anno2070": "anno2070", "civilizationgame": "zivilisationsspiel", "civilization4": "zivilisation4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "totalwar", "travian": "travian", "forts": "forts", "goodcompany": "gutegesellschaft", "civ": "civ", "homeworld": "heimatwelt", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "fürdiekönige", "realtimestrategy": "echtezeitstrategie", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierszivilisation", "kingdomtwocrowns": "königreichzweihronzen", "eu4": "eu4", "vainglory": "eitelkeit", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daveslustigealgebraklasse", "plagueinc": "pestinc", "theorycraft": "theoriebasteln", "mesbg": "mesbg", "civilization3": "zivilisation3", "4inarow": "4in<PERSON>ihe", "crusaderkings3": "kreuzzügler3", "heroes3": "helden3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "pflanzenvszombies", "giochidistrategia": "strategiespiele", "stratejioyunları": "strategiespiele", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "zeitalterderwunder", "dinosaurking": "dinosaurenk<PERSON><PERSON>g", "worldconquest": "weltoberhohlung", "heartsofiron4": "herzenauseisen4", "companyofheroes": "heldenderfirma", "battleforwesnoth": "sch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "schmiedederre<PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansgansente", "phobies": "phobien", "phobiesgame": "phobiesgame", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "außenebene", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "<PERSON><PERSON>", "ageofempires4": "ageofempires4", "civilization5": "zivilisation5", "victoria2": "victoria2", "crusaderkings": "kreuzritterkönige", "cultris2": "cultris2", "spellcraft": "zauberkunst", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategie", "popfulmail": "popfulmail", "shiningforce": "glanzkraft", "masterduel": "meisterduell", "dysonsphereprogram": "dysonsphereprogramm", "transporttycoon": "transporttycoon", "unrailed": "unrailed", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "flugangstüberwinden", "uplandkingdoms": "uplandkönigreiche", "galaxylife": "galaxyleben", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slay<PERSON>sp<PERSON>", "battlecats": "kampfkatzen", "sims3": "sims3", "sims4": "sims4", "thesims4": "diesims4", "thesims": "diesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "brauchtschnelligkeit", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realesracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreispiel", "ts4": "ts4", "thesims2": "diesims2", "thesims3": "diesims3", "thesims1": "diesims1", "lossims4": "lassunsims4", "fnaf": "fnaf", "outlast": "überdauern", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "aliceswahnsinnkehrtzurück", "darkhorseanthology": "dunklespferdeanthologie", "phasmophobia": "phasmofieber", "fivenightsatfreddys": "fünfnächtebeifreddy", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "kleinenachtalbträume", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "hausgeblieben", "deadisland": "to<PERSON><PERSON>", "litlemissfortune": "kleineschicksal", "projectzero": "proje<PERSON><PERSON><PERSON>", "horory": "g<PERSON><PERSON><PERSON>", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hallonachbar2", "gamingdbd": "gamingdbd", "thecatlady": "diekatzenfrau", "jeuxhorreur": "horrorspiele", "horrorgaming": "horrorgaming", "magicthegathering": "magicdiegeneration", "mtg": "mtg", "tcg": "kcg", "cardsagainsthumanity": "kartengegendiehumanität", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenames", "dixit": "dixit", "bicyclecards": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendevonruneterra", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "schlüsselschmiede", "cardtricks": "<PERSON><PERSON><PERSON><PERSON>", "playingcards": "spielkarten", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON>", "pokemoncards": "poke<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleischundbluttcg", "sportscards": "sportkarten", "cardfightvanguard": "kartenkampfvanguard", "duellinks": "duellinks", "spades": "pik", "warcry": "kriegsruf", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "königderherzen", "truco": "truco", "loteria": "lotto", "hanafuda": "hana<PERSON>da", "theresistance": "der<PERSON>rstand", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yug<PERSON><PERSON><PERSON>ll", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspiel", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "blauäugigweißerdrache", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kartenspiel", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "kartenspiele", "mtgjudge": "mtgjudge", "juegosdecartas": "kartenspiele", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "karateo", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "kartenspiele", "battlespirits": "kampfgeister", "battlespiritssaga": "kampfgeistsaga", "jogodecartas": "kartenjammern", "žolíky": "žolíky", "facecard": "gesichtskarte", "cardfight": "kartenkampf", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "magis<PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "schattenuniversum", "skipbo": "skip<PERSON>", "unstableunicorns": "unstabileeinhörner", "cyberse": "cyberse", "classicarcadegames": "klassischearcadegames", "osu": "osu", "gitadora": "gitadora", "dancegames": "tanzspiele", "fridaynightfunkin": "freitagabendfunken", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projektdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "einfachtanzen", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rocktdead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tanzzentrale", "rhythmgamer": "rhythmgamer", "stepmania": "stepmania", "highscorerythmgames": "highscorerhythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "tanzvonfeuerundeis", "auditiononline": "onlinecasting", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kryptodertotentänzer", "rhythmdoctor": "rhythmdoktor", "cubing": "ruben", "wordle": "wortle", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON>le", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logikrätsel", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "kreuzworträtsel", "motscroisés": "wortkreuz", "krzyżówki": "kreuzworträtsel", "nonogram": "nonogramm", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "puzzlespass", "indovinello": "schnitzeljagd", "riddle": "r<PERSON><PERSON>", "riddles": "r<PERSON><PERSON>", "rompecabezas": "puzzlebock", "tekateki": "tekateki", "inside": "drin", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "fluchtsimulator", "minesweeper": "minenfeld", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "kreuzworträtsel", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "puzzlesport", "escaperoomgames": "escaperoomspiele", "escapegame": "escapegame", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesgame", "wordsearch": "wortsuche", "enigmistica": "rätselfieber", "kulaworld": "kula<PERSON><PERSON>", "myst": "myst", "riddletales": "rätselmärchen", "fishdom": "fishdom", "theimpossiblequiz": "dasunmöglichequiz", "candycrush": "candycrush", "littlebigplanet": "kleinesgroßesland", "match3puzzle": "match3rätsel", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "eigenartig", "rubikcube": "rubikcube", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "dastalosprinzip", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "kreuzworträtsel", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "rätselwörter", "buscaminas": "minensucher", "puzzlesolving": "rätselknacken", "turnipboy": "rübenjunge", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "nobodys", "guessing": "raten", "nonograms": "nonogramme", "kostkirubika": "kostkurubika", "crypticcrosswords": "kryptischekreuzworträtsel", "syberia2": "syberia2", "puzzlehunt": "rätseljagd", "puzzlehunts": "rätseljagden", "catcrime": "katzenverbrechen", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "hauptknobeleien", "poptropica": "poptropica", "thelastcampfire": "<PERSON>letztelagerfeuer", "autodefinidos": "selbstaufgestellt", "picopark": "picopark", "wandersong": "wandersong", "carto": "carto", "untitledgoosegame": "untitledgansspiel", "cassetête": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrinth", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "speedcube", "pieces": "st<PERSON><PERSON>", "portalgame": "portalspiel", "bilmece": "r<PERSON><PERSON>", "puzzelen": "puzzeln", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "verdrehteswunderland", "monopoly": "monopoly", "futurefight": "zukunftsfight", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "einsamerwolf", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pikminblühende", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "alchemystars", "stateofsurvival": "zustanddesüberlebens", "mycity": "meinestadt", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btw", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "schicksalsgrandauftrag", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "feuerzeichenhelden", "honkaiimpact": "honkaiimpact", "soccerbattle": "soccerbattle", "a3": "a3", "phonegames": "handyspiele", "kingschoice": "<PERSON>wa<PERSON>", "guardiantales": "guardian<PERSON>ärchen", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktischcool", "cookierun": "kekslauf", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "nichtaufdemlaufenden", "craftsman": "handwerker", "supersus": "supersus", "slowdrive": "langsamefahrt", "headsup": "achtung", "wordfeud": "wortgefecht", "bedwars": "bettkriege", "freefire": "freefire", "mobilegaming": "mobilegaming", "lilysgarden": "lilysgarten", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "<PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "notfallzentrale", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "heimspiel", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON>and<PERSON>dget", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "sternstableonline", "dragonraja": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeprincess": "zeitprinzessin", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegende", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "taschenliebe", "androidgames": "androidspiele", "criminalcase": "kriminalfall", "summonerswar": "beschwörerkrieg", "cookingmadness": "kochwahnsinn", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligaderengel", "lordsmobile": "lordsmobile", "tinybirdgarden": "kleinervogelsgarten", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "meinesingendenmonster", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "kriegroboter", "mirrorverse": "spiegelverse", "pou": "pou", "warwings": "warflügel", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendenmobil", "ingress": "<PERSON><PERSON><PERSON>", "slugitout": "schlage<PERSON><PERSON>", "mpl": "mpl", "coinmaster": "münzenmeister", "punishinggrayraven": "bestrafenbleirabe", "petpals": "haustierfreunde", "gameofsultans": "spieldersultane", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "rundspielstadt", "juegodemovil": "handyspiel", "avakinlife": "avakinleben", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "achterbahndynamo", "grandchase": "großeverfolgung", "bombmebrasil": "bombmichbrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "ruf<PERSON><PERSON><PERSON>", "shiningnikki": "leuchtendenikki", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "weginsnirgendwo", "sealm": "sealm", "shadowfight3": "schattenkampf3", "limbuscompany": "limbuscompany", "demolitionderby3": "abgerissenerderby3", "wordswithfriends2": "wörtermitfreunden2", "soulknight": "seelenritter", "purrfecttale": "purrfektertale", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobil", "harvesttown": "erntestadt", "perfectworldmobile": "perfekteweltmobil", "empiresandpuzzles": "imperienundrätsel", "empirespuzzles": "empirespuzzles", "dragoncity": "drachenstadt", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilede", "fanny": "fanny", "littlenightmare": "kleinesnachtmahl", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "trä<PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "eveechos", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "k<PERSON>mama", "cabalmobile": "cabalmobil", "streetfighterduel": "straßenkämpferduell", "lesecretdhenri": "lesegeheimnisdenhenri", "gamingbgmi": "gamingbgmi", "girlsfrontline": "mädelsfrontlinie", "jurassicworldalive": "jurassicworldalive", "soulseeker": "seelenfinder", "gettingoverit": "darüberwegkommen", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "mondchaigeschichte", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilespielen", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "spielemobilelegends", "timeraiders": "zeitraubers", "gamingmobile": "gamingmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "diekatzenimkampf", "dnd": "dnd", "quest": "quest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "weltderdunkelheit", "travellerttrpg": "reiseabenteurerttrpg", "2300ad": "2300nchr", "larp": "larp", "romanceclub": "romanceclub", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "pokemonmysterödungeon", "pokemonlegendsarceus": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatot": "plau<PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "karp<PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "schlummernase", "pocketmonsters": "taschenmonster", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "teammystic", "pokeball": "pokéball", "charmander": "glumanda", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "glänzendepokémon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "eisenhände", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbrasion", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "kidsundpokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bisasam", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON>", "ajedrez": "schach", "catur": "katzenrück<PERSON>", "xadrez": "schach", "scacchi": "schach", "schaken": "schaken", "skak": "skak", "ajedres": "schachspass", "chessgirls": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "weltblitz", "jeudéchecs": "schachliebe", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "chin<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "s<PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "schach<PERSON><PERSON>lapper", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "rook": "boorook", "chesscom": "schachkom", "calabozosydragones": "ka<PERSON><PERSON><PERSON>und<PERSON><PERSON>", "dungeonsanddragon": "dungeonsunddrachen", "dungeonmaster": "dungeonmeister", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "dunklesonnen", "thelegendofvoxmachina": "dielegendevonvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "dunkelmoor", "minecraftchampionship": "minecraftmeisterschaft", "minecrafthive": "minecraftschwarm", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "kerzenflamme", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "himmels<PERSON>", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "zwischenwelten", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftstadt", "pcgamer": "pcgamer", "jeuxvideo": "spie<PERSON><PERSON><PERSON>n", "gambit": "gambit", "gamers": "gamer", "levelup": "levelup", "gamermobile": "gamermobil", "gameover": "spielende", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "spie<PERSON><PERSON><PERSON>n", "pcgames": "pcspiele", "casualgaming": "casualgaming", "gamingsetup": "gamingsetup", "pcmasterrace": "pc<PERSON><PERSON><PERSON>", "pcgame": "pcspiel", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplay", "consoleplayer": "konsolenzocker", "boxi": "boxi", "pro": "pro", "epicgamers": "epischegamer", "onlinegaming": "onlinegaming", "semigamer": "semigamer", "gamergirls": "gamermädchen", "gamermoms": "gamermoms", "gamerguy": "gamerguy", "gamewatcher": "spielbeobachter", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamzusammenreißer", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "quests", "alax": "alax", "avgn": "avgn", "oldgamer": "altergamer", "cozygaming": "gemütlicheszocken", "gamelpay": "gamelpay", "juegosdepc": "pcspiele", "dsswitch": "dsswitch", "competitivegaming": "wettbewerbsspiel", "minecraftnewjersey": "minecraftneujersey", "faker": "faker", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosex<PERSON><PERSON><PERSON><PERSON>n", "gamepc": "gamingpc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "täglichequest", "gamegirl": "gamegirl", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "übermächtig", "socialgamer": "sozialgamer", "gamejam": "gamejam", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "meinecrew", "republicofgamers": "republikdergamer", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "dreifachlegende", "gamerbuddies": "gamerfreunde", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "christ<PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gelegenheitszocker", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "zocker", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "videospieler", "wspólnegranie": "gemeinsamspielen", "mortdog": "mortdog", "playstationgamer": "playstationzocker", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gesundespieler", "gtracing": "gtracing", "notebookgamer": "notizbuchgamer", "protogen": "protogen", "womangamer": "fra<PERSON><PERSON><PERSON>", "obviouslyimagamer": "ichbinlogischerweiseeinspieler", "mario": "mario", "papermario": "pap<PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "menschenfallenflach", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "null<PERSON>lucht", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonic<PERSON><PERSON>l", "sonic": "sonic", "fallguys": "fallguys", "switch": "wechseln", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "legend<PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "himmelskinderdeslichts", "tomodachilife": "tomodachileben", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tränenedeskönigreichs", "walkingsimulators": "walkingsimulators", "nintendogames": "nintendospiele", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "drachenquest", "harvestmoon": "<PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefabrik", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "meinfreundpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spiele", "earthbound": "erdverbunden", "tales": "geschichten", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "tierüberqueren", "taikonotatsujin": "taikomeister", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "dreiecksstrategie", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "kastanienschlechtefelltag", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulekrieger", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioundsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftde", "lolph": "lolph", "leagueoflegend": "leagueoflegenden", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsdeutschland", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexportale", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideospiele", "scaryvideogames": "gruseligevideospiele", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videospiel", "videosgame": "videospiele", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "überwachung", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "zauberer101", "battleblocktheater": "battleblocktheater", "arcades": "spielhallen", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "landwirtschaftssimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sanboxspiele", "videogamelore": "videospielwissen", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "traumland", "starcitizen": "<PERSON><PERSON><PERSON><PERSON>", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "deadspace", "amordoce": "amordoce", "videogiochi": "videospiele", "theoldrepublic": "diealterepublik", "videospiele": "videospiele", "touhouproject": "touhouprojekt", "dreamcast": "traumcast", "adventuregames": "abenteuerspiele", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "geschichtenvonsaisons", "retrogames": "retrogames", "retroarcade": "retrospielhalle", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "spielzeit", "commanderkeen": "kommanderkeen", "bugsnax": "bugsnax", "injustice2": "ungerechtigkeit2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "himmelsspiel", "zenlife": "zenleben", "beatmaniaiidx": "beatmaniaiidx", "steep": "steil", "mystgames": "my<PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "medieval", "consolegaming": "konsolenspiele", "konsolen": "konsolen", "outrun": "ausweichen", "bloomingpanic": "b<PERSON>ü<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaminghorror", "monstergirlquest": "monstermädchenquest", "supergiant": "superriese", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "farmingsims", "juegosviejos": "altespiele", "bethesda": "bethesda", "jackboxgames": "jackboxspiele", "interactivefiction": "interaktivefiktion", "pso2ngs": "pso2ngs", "grimfandango": "grimmfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON>bergeister", "visualnovel": "visualnovel", "visualnovels": "visualnovels", "rgg": "rgg", "shadowolf": "s<PERSON><PERSON><PERSON>", "tcrghost": "tcrgeist", "payday": "zahltag", "chatherine": "katherine", "twilightprincess": "dämmerungsprinzessin", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandkasten", "aestheticgames": "aestheticspiele", "novelavisual": "novelavisual", "thecrew2": "diecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrospiel", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamentieren", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "laubbläserrevolution", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyblade": "schlüsselblade", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "visuellenovels", "robloxbrasil": "robloxdeutschland", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospiele", "videogamedates": "<PERSON><PERSON><PERSON><PERSON>", "mycandylove": "mycandylove", "megaten": "megaten", "mortalkombat11": "mortalcombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkgames", "batmangames": "batmangames", "returnofreckoning": "rückkehrderrechnung", "gamstergaming": "gamstergaming", "dayofthetantacle": "tagdertentakel", "maniacmansion": "maniacmansion", "crashracing": "crashracing", "3dplatformers": "3dplattformspiele", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "geschichtegames", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "jenseitsderzweiseelen", "gameuse": "spieln<PERSON>er", "offmortisghost": "offmortisghost", "tinybunny": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "energiebooster", "katanazero": "katanazero", "famicom": "famicon", "aventurasgraficas": "grafi<PERSON><PERSON><PERSON><PERSON>", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "ödland", "powerwashsim": "powerwashsim", "coralisland": "koralleninsel", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfrucht", "anotherworld": "eineanderewelt", "metaquest": "metaquest", "animewarrios2": "animekrieger2", "footballfusion": "fußballfusion", "edithdlc": "edithdlc", "abzu": "ab<PERSON><PERSON><PERSON><PERSON>", "astroneer": "astroneer", "legomarvel": "leg<PERSON>ä<PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "verdrehtemetall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "haufenderschande", "simulator": "simulator", "symulatory": "symulatorisch", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wunderlandonline", "skylander": "skylander", "boyfrienddungeon": "freundinnenkerker", "toontownrewritten": "toontownneugeschrieben", "simracing": "simracing", "simrace": "simracing", "pvp": "pvp", "urbanchaos": "stadtsalat", "heavenlybodies": "himmlischekörper", "seum": "seum", "partyvideogames": "partyvideospiele", "graveyardkeeper": "fried<PERSON>sw<PERSON>chter", "spaceflightsimulator": "weltraumflugsimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackenschnetzeln", "foodandvideogames": "essenundvideospiele", "oyunvideoları": "spielevideos", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "lkwsimulator", "horizonworlds": "horizonwelten", "handygame": "handyspiel", "leyendasyvideojuegos": "legendenundvideospiele", "oldschoolvideogames": "oldschoolvideospiele", "racingsimulator": "rennsimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentend<PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gatesofolympus", "monsterhunternow": "monsterjägerjetzt", "rebelstar": "rebelstar", "indievideogaming": "indievideospiele", "indiegaming": "indiegaming", "indievideogames": "indievideospiele", "indievideogame": "indievideospiel", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffestung", "unbeatable": "unschlagbar", "projectl": "projektl", "futureclubgames": "zukunftsclubspiele", "mugman": "becherjunge", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestespiel", "aperturescience": "aperturwissenschaft", "backlog": "rückstand", "gamebacklog": "spielrückstand", "gamingbacklog": "gamingrückstand", "personnagejeuxvidéos": "charakterevideospiele", "achievementhunter": "er<PERSON><PERSON><PERSON>jä<PERSON>", "cityskylines": "stadtansichten", "supermonkeyball": "superaffeball", "deponia": "deponia", "naughtydog": "naughtyhund", "beastlord": "bestien<PERSON>", "juegosretro": "retrogames", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriundderblindenwald", "alanwake": "alanwake", "stanleyparable": "stan<PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "dopaminreservoir", "staxel": "staxel", "videogameost": "videospielost", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ichliebekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "ausras<PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "sadanime", "darkerthanblack": "dunkleralsschwarz", "animescaling": "animeskalierung", "animewithplot": "animemitstory", "pesci": "fische", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "dunkler<PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "me<PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonestaffel1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON>", "animecover": "animecover", "thevisionofescaflowne": "dievisionvonescaflowne", "slayers": "schlitzer", "tokyomajin": "tokyomajin", "anime90s": "anime90er", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toilettenboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "feuerkraft", "moriartythepatriot": "moria<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "zukunftstagebuch", "fairytail": "märchenhafte", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "meerjungfrauenmelodie", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romanceman<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON>mäd<PERSON>", "blacklagoon": "schwarzel<PERSON>ne", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "einbestimmtermagischerindex", "sao": "sao", "blackclover": "schwarzeskleeblatt", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouprojekt", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8dieunendlichkeit", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamilie", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriorität", "angelsofdeath": "engeldestodes", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "ar<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyadieböse", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "derjungeunddastier", "fistofthenorthstar": "fistdernordstern", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "wie<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "vollmondboo", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "süßundgruselig", "martialpeak": "kampfspitze", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanantfee", "shinji": "shinji", "zerotwo": "nullzwei", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstermädchen", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegetapower", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "freundschaft", "sailorsaturn": "seemänneraufsaturn", "dio": "dio", "sailorpluto": "seefahrerpluto", "aloy": "aloy", "runa": "runa", "oldanime": "alteanime", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON>mann", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "sch<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "früchtestapel", "devilmancrybaby": "devilmanweepennbaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "liebelive", "sakuracardcaptor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "dasversprocheneniemandsland", "monstermanga": "monstermanga", "yourlieinapril": "deinelügenimapril", "buggytheclown": "buggydasclown", "bokunohero": "meinheld", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "<PERSON><PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorasherzen", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "essenkrieg", "cardcaptorsakura": "kartenfängersakura", "stolas": "stolas", "devilsline": "devillslinie", "toyoureternity": "zudeinerewigkeit", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blaueszeitalter", "griffithberserk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinigami": "shinigami", "secretalliance": "geheimesbündnis", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON>s<PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detektivconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirritter", "mugi": "mugi", "blueexorcist": "blauerexorzist", "slamdunk": "slamdundee", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spionfamilie", "airgear": "luftgear", "magicalgirl": "magicalgirl", "thesevendeadlysins": "diese7todsünden", "prisonschool": "gefängnisschule", "thegodofhighschool": "dergottderoberschule", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "meinedressupliebe", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniversum", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabgespeckt", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentinien", "lolicon": "lo<PERSON>on", "demonslayertothesword": "dämonenärse<PERSON><PERSON>nkel", "bloodlad": "blutbub", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "feuerschlag", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romantische<PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfaktor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "germanetechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prinzenvomtennis", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinenklasse", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "todenstanz", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanis<PERSON><PERSON>me", "animespace": "animewelt", "girlsundpanzer": "mädelsundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashglocke", "peachgirl": "pfirsichmädchen", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamädchen", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "herzdesmanga", "deliciousindungeon": "leckerschmausinderkerker", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "aufzeichnungderragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "manganime", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "überlevelt", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemeister", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "hexenhutatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolöwe", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "tropfengottes", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeschatz", "reverseharem": "reverseharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "blutplus", "bloodplusanime": "blutundanime", "bloodcanime": "blutcanime", "bloodc": "blutc", "talesofdemonsandgods": "geschichtenvondevilundgötter", "goreanime": "goreanime", "animegirls": "animegirls", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowxschlimm", "splatteranime": "spritzanime", "splatter": "spritzen", "risingoftheshieldhero": "deraufstiegdesschildhelden", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "kinderder<PERSON>e", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "animeidole", "isekaiwasmartphone": "isekaiwarhand<PERSON>", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magische<PERSON><PERSON><PERSON><PERSON>", "callofthenight": "rufdernacht", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "magis<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "schattengarten", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekuss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeuniversum", "persocoms": "persocoms", "omniscientreadersview": "omniscientreaderansicht", "animecat": "animekatze", "animerecommendations": "animeempfehlungen", "openinganime": "animeeröffnung", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "meinteenromantikkomödie", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarredaventur", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "eis", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "milit<PERSON><PERSON><PERSON>", "greenranger": "gr<PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "<PERSON><PERSON><PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lup<PERSON><PERSON><PERSON>", "animecity": "animestadt", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "einstück", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "dig<PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "meinheldakademie", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>jä<PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "angriffauftitan", "erenyeager": "boohun<PERSON><PERSON>", "myheroacademia": "meinheldenakademie", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachispiel", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "umfragekorps", "onepieceanime": "onepieceanime", "attaquedestitans": "angreiftdietitanen", "theonepieceisreal": "dereinest<PERSON>ist<PERSON><PERSON>", "revengers": "<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffekt", "digimonstory": "digimonstory", "digimontamers": "digimontamers", "superjail": "superjail", "metalocalypse": "metallkatastrophe", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "fehlerloswebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "uchbinprincessama", "animecom": "animecommunity", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "fliegendehexe", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "e<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstraße", "recuentosdelavida": "lebensgeschichten"}