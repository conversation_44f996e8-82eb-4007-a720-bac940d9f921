{"2048": "2048", "mbti": "mbti", "enneagram": "eneagrama", "astrology": "astrologia", "cognitivefunctions": "funçõescognitivas", "psychology": "psicologia", "philosophy": "filosofia", "history": "história", "physics": "física", "science": "ciência", "culture": "cultura", "languages": "idiomas", "technology": "tecnologia", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiam<PERSON><PERSON>", "enneagrammemes": "eneagramamemes", "showerthoughts": "pensamentosdechuveiro", "funny": "diversão", "videos": "vídeos", "gadgets": "gadgets", "politics": "política", "relationshipadvice": "consel<PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "conselhodevida", "crypto": "crypto", "news": "not<PERSON><PERSON><PERSON>", "worldnews": "not<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "arqueologia", "learning": "aprender", "debates": "debates", "conspiracytheories": "teoriasdaconspiração", "universe": "universo", "meditation": "meditação", "mythology": "mitologia", "art": "arte", "crafts": "artesanato", "dance": "<PERSON><PERSON><PERSON>", "design": "design", "makeup": "maquiagem", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "moda", "singing": "cantar", "writing": "escrever", "photography": "fotografia", "cosplay": "cosplay", "painting": "pintar", "drawing": "desenhar", "books": "livros", "movies": "filmes", "poetry": "poesia", "television": "televisão", "filmmaking": "filmagem", "animation": "animação", "anime": "anime", "scifi": "scifi", "fantasy": "fantasia", "documentaries": "documentários", "mystery": "<PERSON><PERSON><PERSON>", "comedy": "comédia", "crime": "crime", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "terror", "romance": "romance", "realitytv": "realitytv", "action": "ação", "music": "música", "blues": "blues", "classical": "clássica", "country": "country", "desi": "desi", "edm": "edm", "electronic": "eletrônica", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latina", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "reb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "viagem", "concerts": "concertos", "festivals": "festivais", "museums": "museus", "standup": "standup", "theater": "teatro", "outdoors": "aoarlivre", "gardening": "jardinagem", "partying": "festejar", "gaming": "gaming", "boardgames": "boardgames", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "comida", "baking": "confeitaria", "cooking": "<PERSON><PERSON><PERSON>", "vegetarian": "vegetarianismo", "vegan": "veganismo", "birds": "p<PERSON><PERSON><PERSON>", "cats": "gatos", "dogs": "c<PERSON><PERSON>", "fish": "peixes", "animals": "animais", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ambientalismo", "feminism": "feminismo", "humanrights": "direitoshumanos", "lgbtqally": "lgbtqally", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "voluntariado", "sports": "esportes", "badminton": "badminton", "baseball": "baseball", "basketball": "basquete", "boxing": "boxe", "cricket": "cr<PERSON><PERSON><PERSON>", "cycling": "ciclismo", "fitness": "fitness", "football": "futebol", "golf": "golfe", "gym": "academia", "gymnastics": "ginástica", "hockey": "<PERSON><PERSON><PERSON><PERSON>", "martialarts": "artesmarcia<PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "corrida", "skateboarding": "skateboarding", "skiing": "esquiar", "snowboarding": "snowboard", "surfing": "surfe", "swimming": "natação", "tennis": "t<PERSON><PERSON>", "volleyball": "vôlei", "weightlifting": "levantamentodepeso", "yoga": "ioga", "scubadiving": "merg<PERSON>ho", "hiking": "camin<PERSON>a", "capricorn": "capricórnio", "aquarius": "a<PERSON><PERSON><PERSON><PERSON>", "pisces": "peixes", "aries": "<PERSON>ries", "taurus": "touro", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON>", "leo": "leão", "virgo": "virgem", "libra": "libra", "scorpio": "escorpião", "sagittarius": "sagit<PERSON><PERSON>", "shortterm": "curtoprazo", "casual": "casual", "longtermrelationship": "relacionamentosério", "single": "solt<PERSON>", "polyamory": "poliamor", "enm": "nãomonogâmico", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lésbica", "bisexual": "bissexual", "pansexual": "pansexual", "asexual": "assexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "ageofdrag<PERSON><PERSON>", "assassinscreed": "assassinscreed", "saintsrow": "santos<PERSON>", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "cachorrosdeguarda", "dislyte": "dislyte", "rougelikes": "roguelikes", "kingsquest": "buscadoreis", "soulreaver": "recol<PERSON><PERSON><PERSON>l", "suikoden": "su<PERSON><PERSON>", "subverse": "subverso", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "dragãoespiao", "dragonsdogma": "<PERSON><PERSON><PERSON>dog<PERSON>", "sunsetoverdrive": "pôrdosolacelerado", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloin<PERSON>ito", "guildwars": "guildwars", "openworld": "mund<PERSON><PERSON>", "heroesofthestorm": "heróisdatempestade", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "exploraçãoemmasmorras", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribosdemidgard", "planescape": "planoescape", "lordsoftherealm2": "lordesdoreino2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simsimersivos", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "desonrado", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "criaçãodepersonagens", "immersive": "imersivo", "falloutnewvegas": "falloutnovagas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfant<PERSON>yan<PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivacaomorbida", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "apaixonadosporamor", "otomegames": "otomeg<PERSON>s", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiroamascarada", "dimension20": "dimensao20", "gaslands": "gaslands", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ed", "shadowrun": "shadowrun", "bloodontheclocktower": "sanguenorelógiodacasa", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxiv", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravidadeapressada", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "umgolpe", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "sobrelord", "yourturntodie": "suavizinhaquevocêmorre", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtexto", "genshin": "genshin", "eso": "isso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "abrigoapocaliptico", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "faseeclipse", "disgaea": "disgaea", "outerworlds": "mundosex<PERSON>os", "arpg": "arpg", "crpg": "rpgdecrônicas", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "guerreirosdadinastia", "skullgirls": "skullgirls", "nightcity": "cidadeumarada", "hogwartslegacy": "legadodehogwarts", "madnesscombat": "loucurascombate", "jaggedalliance2": "jaggedalliance2", "neverwinter": "nuncanieveinverno", "road96": "estrada96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "reinosesquecidos", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "fil<PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonmund<PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "tronosquebradosemfragmentsos", "horizonforbiddenwest": "horizonteoesteproibido", "twewy": "twewy", "shadowpunk": "sombropunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "misteriodahog<PERSON>s", "deltagreen": "deltaverde", "diablo": "diabo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "mat<PERSON><PERSON><PERSON><PERSON>", "lastepoch": "últimapróxima", "starfinder": "buscadorastros", "goldensun": "<PERSON><PERSON><PERSON>", "divinityoriginalsin": "pecadodadivindade", "bladesinthedark": "lâminasnandotempo", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkvermelho", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordemcaída", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "terrasdomal", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "sobreviventadoinferninho", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divindade", "pf2": "pf2", "farmrpg": "fazendargp", "oldworldblues": "bluesdoantigo", "adventurequest": "aventuraembusca", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "jogosderoleplaying", "roleplayinggames": "jogosderpg", "finalfantasy9": "finalfantasy9", "sunhaven": "solrefúgio", "talesofsymphonia": "contosdesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "cidadepartida", "myfarog": "meufarog", "sacredunderworld": "sacredunderworld", "chainedechoes": "ecosencadeados", "darksoul": "<PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "outrocídio", "mountandblade": "montanegrito", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "pilaresdaeternidade", "palladiumrpg": "palladiumrpg", "rifts": "fendas", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "viajantesdooctopath", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "lobisomemdoapocalipse", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "filhosdamorta", "engineheart": "coraçãomotor", "fable3": "fable3", "fablethelostchapter": "fableocapítuloperdido", "hiveswap": "hiveswap", "rollenspiel": "jogodepapel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneterno", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "renovacaovintage", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mundosselvagens", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "coraçãodereino1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "jogosrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "coraçõeslivres", "bastion": "bastião", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "céusdearcadia", "shadowhearts": "coraçãodesombra", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "sangueemcentavos", "breathoffire4": "respiraofogo4", "mother3": "mãe3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "jogosderoleplay", "roleplaygame": "jogodeinterpretação", "fabulaultima": "fabulault<PERSON>", "witchsheart": "coraçãodefeiticeira", "harrypottergame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "d<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "caçadorroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "caçadordemonstrosmundo", "bg3": "bg3", "xenogear": "xenogears", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "pactodasombra", "bladesoul": "almaspoderosas", "baldursgate3": "baldursgate3", "kingdomcome": "reinoquevem", "awplanet": "awplanet", "theworldendswithyou": "omundocomaevo<PERSON>ê", "dragalialost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elderscroll": "elderscrolls", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "heresiadark", "shoptitans": "titãsdoshopping", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magiaterrestre", "blackbook": "livropreto", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "ediçãosagradourara", "castlecrashers": "casteloinvasores", "gothicgame": "jogog<PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "fantasmasnapolitica", "fallout2d20": "fallout2d20", "gamingrpg": "jogosrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "cidadedebruma", "indierpg": "indierpg", "pointandclick": "apontaeclique", "emilyisawaytoo": "emilyestáforanovamente", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisível", "freeside": "freeside", "epic7": "epic7", "ff7evercrisis": "ff7emprecrise", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "póscyberpunk", "deathroadtocanada": "estradadafalteacanadá", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "caçadordemonstro", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacia", "persona5": "persona5", "ghostoftsushima": "fantas<PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "caçadordemonstrosascensão", "nier": "<PERSON><PERSON><PERSON>", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "jogosnon<PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtatico", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeternal", "princessconnect": "princessconnect", "hexenzirkel": "circulodasbruxas", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "pocketsage", "valorant": "valorantrfã", "valorante": "valorante", "valorantindian": "valorantindiabr", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esportes", "mlg": "mlg", "leagueofdreamers": "leagueofdreamers", "fifa14": "fifa14", "midlaner": "meiodoado", "efootball": "futeboleletrônico", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "ciberesporte", "crazyraccoon": "raconlouco", "test1test": "teste1teste", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitivo", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "meiantidade", "left4dead": "deixado<PERSON><PERSON><PERSON>", "left4dead2": "left4dead2", "valve": "valve", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simuladordecabras", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetaliberdade", "transformice": "transformice", "justshapesandbeats": "apenasformase<PERSON><PERSON><PERSON>", "battlefield4": "campodebatalha4", "nightinthewoods": "noited<PERSON><PERSON><PERSON>", "halflife2": "metadode2", "hacknslash": "matacutucando", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riscodechuva2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "interplanetário", "helltaker": "desafiodoinferno", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "célulasmortas", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortale<PERSON>ãozin<PERSON>", "foxhole": "buracofoxhole", "stray": "errante", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "campodebatalha1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "olho", "blackdesert": "desertonegro", "tabletopsimulator": "simuladordetabuleiros", "partyhard": "festaduramente", "hardspaceshipbreaker": "desmanteladoradeespaço", "hades": "hades", "gunsmith": "armadeiro", "okami": "<PERSON>ami", "trappedwithjester": "presaocomoeste", "dinkum": "dinkum", "predecessor": "antecessor", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "simuladadecolônia", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "mestresdaminhão", "grimdawn": "dawnsombria", "darkanddarker": "escuroemaisescuro", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "trabal<PERSON><PERSON>al<PERSON>", "datingsims": "simuladordedating", "yaga": "yaga", "cubeescape": "escapedocubo", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novacidade", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconpesado", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiavirtual", "snowrunner": "nevasca", "libraryofruina": "bibliotecadaruina", "l4d2": "l4d2", "thenonarygames": "osjogosnonários", "omegastrikers": "omegastrikers", "wayfinder": "buscador", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialcidade", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "coelhinodeminifeliz", "cozygrove": "cozygrove", "doom": "fimdostempos", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "codin", "borderlands": "terrasfronteiriças", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "fugida", "farcrygames": "jogosfarcry", "paladins": "paladins", "earthdefenseforce": "forçadefesaedaterra", "huntshowdown": "caçanegada", "ghostrecon": "fantasmasrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "entranaturma", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "tempestadasinsurgência", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzumbis", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisões2", "killzone": "zonaexterminio", "helghan": "hel<PERSON>", "coldwarzombies": "frio<PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "guerraformoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "territóriosdeaventura", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>cap<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnagemprimal", "worldofwarships": "mundodosnaviosdeguerra<PERSON>", "back4blood": "devoltaporgore", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "matador", "masseffect": "masseffect", "systemshock": "choquesistema", "valkyriachronicles": "cronicasdavalquiria", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cavestory", "doometernal": "doometernal", "centuryageofashes": "séculoidadecinzas", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "divisão2", "tythetasmaniantiger": "tyotigretasmânico", "generationzero": "geracazero", "enterthegungeon": "entreeoganjo", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "homensalsichas", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "dorphantasmagórica", "warface": "warface", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "coraçãobrilhante", "blackops3": "blackops3", "vampiresurvivors": "vampirossobreviventes", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "liberdade", "battlegrounds": "camposdebatalha", "frag": "fraga", "tinytina": "<PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "filhosdaliberdademetalgear", "juegosfps": "jogosfps", "convertstrike": "convertebatalha", "warzone2": "zonadeguerra2", "shatterline": "shatterline", "blackopszombies": "zumbisblackops", "bloodymess": "baguncabizarra", "republiccommando": "republiccommando", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldado", "groundbranch": "ramobranch", "squad": "squad", "destiny1": "destino1", "gamingfps": "jogosfps", "redfall": "redfall", "pubggirl": "garotapubg", "worldoftanksblitz": "mundod<PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "alistado", "farlight": "luzdistante", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "diadepagamento2", "cs16": "cs16", "pubgindonesia": "pubgindonésia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sabonetecod", "ghostcod": "fantasmacod", "csplay": "csplay", "unrealtournament": "tournamentincrível", "callofdutydmz": "callofdutydmz", "gamingcodm": "jogandocodm", "borderlands2": "borderlands2", "counterstrike": "contraataque", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "campeõ<PERSON>dos<PERSON><PERSON>", "halo3": "halo3", "halo": "al<PERSON>", "killingfloor": "chãoquebrado", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "celuladepenetra", "neonwhite": "neonwhite", "remnant": "remanescente", "azurelane": "azurlane", "worldofwar": "mundodaguerra", "gunvolt": "gunvolt", "returnal": "retornal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "sombrão", "quake2": "quake2", "microvolts": "microvolts", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "campodebatalha3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "océandosladr<PERSON><PERSON>", "rust": "ferrugem", "conqueronline": "conqueronline", "dauntless": "destemido", "warships": "naviosdeguerras", "dayofdragons": "diadosdrag<PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "ascensãodevoos", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "semnamorada", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "mundodtanques", "crossout": "riscado", "agario": "agario", "secondlife": "segundavid<PERSON>", "aion": "aion", "toweroffantasy": "towerofantasy", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "cavaleironline", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "lixo", "newworld": "novomundo", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirata101", "honorofkings": "honradereis", "fivem": "fivem", "starwarsbattlefront": "battlefrontstarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "mundodaguerra", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cinzasdecriação", "riotmmo": "riotmmo", "silkroad": "caminhodaseda", "spiralknights": "cavaleirosdaspirais", "mulegend": "mulenda", "startrekonline": "startrekonline", "vindictus": "vindicativo", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "profetadosdragões", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijogador", "angelsonline": "anjosonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "universodconline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsvelhorepublica", "grandfantasia": "granfantasia", "blueprotocol": "protocoloazul", "perfectworld": "mundoperfeito", "riseonline": "subaonline", "corepunk": "corepunk", "adventurequestworlds": "aventuraquest<PERSON><PERSON><PERSON>", "flyforfun": "voepelobom", "animaljam": "animaljam", "kingdomofloathing": "reinodafossa", "cityofheroes": "cidadedosh<PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "luchadorderua", "hollowknight": "cavernavazia", "metalgearsolid": "metalgearsolid", "forhonor": "porhonra", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "ruasderage", "mkdeadlyalliance": "mkaliançamortal", "nomoreheroes": "cheg<PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "okingdoslutadores", "likeadragon": "comoumdragão", "retrofightinggames": "jogosdeforça<PERSON>ro", "blasphemous": "blasfemo", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superbrawl", "mugen": "mugen", "warofthemonsters": "guerradosmonstros", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberbots", "armoredwarriors": "guerreirosblindados", "finalfight": "lutafinal", "poweredgear": "fer<PERSON>ent<PERSON><PERSON><PERSON>der", "beatemup": "socoeporra", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "j<PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "reidoslutadores", "ghostrunner": "fantasmagórico", "chivalry2": "cavalheirismo2", "demonssouls": "almasdemoníacas", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sequelhollowknight", "hollowknightsilksong": "hollowknightcançõesilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "novid<PERSON>dosilks<PERSON>", "silksong": "silksong", "undernight": "undernight", "typelumina": "tipolumina", "evolutiontournament": "torneiodaevolução", "evomoment": "evomomento", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "contosdeberseria", "bloodborne": "bloodborne", "horizon": "horizonte", "pathofexile": "caminhodexílio", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "infame", "playstationbuddies": "amigosdoplaystation", "ps1": "ps1", "oddworld": "mundoi<PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "deixacair", "gta4": "gta4", "gta": "gta", "roguecompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "trove", "detroitbecomehuman": "detroitvirouhumano", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "atéamanhã", "touristtrophy": "trof<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "sombradoscolossos", "crashteamracing": "crashteamracing", "fivepd": "fivepd", "tekken7": "tekken7", "devilmaycry": "diabonãochora", "devilmaycry3": "diaboapenaschora3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "olastguardian", "soulblade": "almaespada", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "caçamanos", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "coraçõessombrios2aliança", "pcsx2": "pcsx2", "lastguardian": "últimoguarda", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "animaldefestas", "warharmmer40k": "warharmmer40k", "fightnightchampion": "noitedacompetição", "psychonauts": "psiconautas", "mhw": "mhw", "princeofpersia": "príncipedapérsia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "naoencontronojuntos", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "starbound", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "fliperdecasa", "americanmcgeesalice": "aliceamericanosmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligadosreinos", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "boletvlixo", "skycotl": "céunacotl", "erica": "erica", "ancestory": "ancestralidade", "cuphead": "cuphead", "littlemisfortune": "pequenaazarada", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstromatrícula", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultodalamb", "duckgame": "jogodapa<PERSON>", "thestanleyparable": "ostanleyparável", "towerunite": "torreunida", "occulto": "oculto", "longdrive": "longdrive", "satisfactory": "satis<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "pluviômano", "underearth": "debaixodaterra", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "espíritoviajante", "darkdome": "capadomblack", "pizzatower": "torredapizza", "indiegame": "jogosindies", "itchio": "itchio", "golfit": "golfit", "truthordare": "verda<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "jogo", "rockpaperscissors": "ped<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolim", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON>afio", "scavengerhunt": "caçadoradetrecos", "yardgames": "jogosdejardim", "pickanumber": "escolhaoalgarismo", "trueorfalse": "<PERSON>rda<PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "goblindadosdados", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "jogosdeencontro", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "jogosdebeber", "sodoku": "sudoku", "juegos": "jogos", "mahjong": "mahjong", "jeux": "jogos", "simulationgames": "jogosdesimulação", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "vamosjogarumjogo", "boredgames": "jogosdechato", "oyun": "booyun", "interactivegames": "jogosinterativos", "amtgard": "amtgard", "staringcontests": "desafiodeencaramento", "spiele": "joga", "giochi": "jogos", "geoguessr": "geoguessr", "iphonegames": "jogosdoiphone", "boogames": "boojogos", "cranegame": "jogodacranes", "hideandseek": "escondeesconde", "hopscotch": "pular<PERSON><PERSON><PERSON>", "arcadegames": "jogosdearcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "jogodeclassico", "mindgames": "jogos<PERSON><PERSON>", "guessthelyric": "chutaletra", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "travadin<PERSON>", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "jogosdearcade", "tabletopgames": "jogos<PERSON>ab<PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "games90br", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON>der<PERSON><PERSON>", "ets2": "ets2", "realvsfake": "realvsfake", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "jogoonline", "onlinegames": "jogosonline", "jogosonline": "jogosonline", "writtenroleplay": "escritorparodia", "playaballgame": "jogarumjogodabola", "pictionary": "pictionary", "coopgames": "jogoscoop", "jenga": "jenga", "wiigames": "wiigames", "highscore": "pontuaçãoalta", "jeuxderôles": "jogosder<PERSON>", "burgergames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>uer", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwediçãopreta", "jeuconcour": "jogosdeconcurso", "tcgplayer": "tcgplayer", "juegodepreguntas": "jogodaspreguntas", "gioco": "gioco", "managementgame": "jogodegestão", "hiddenobjectgame": "jogodeobjetosocultos", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "jogodeformula1", "citybuilder": "construindacidade", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "jogosarcade", "memorygames": "jogosdememória", "vulkan": "vulkan", "actiongames": "jogosdeação", "blowgames": "blowgames", "pinballmachines": "maquinadepinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "cooperativanosofá", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "jogodagas", "imessagegames": "jogosimessage", "idlegames": "jogosparapassarotempo", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "jogosdepc", "rétrogaming": "retrôgame", "logicgames": "jogosdelógica", "japangame": "japanga<PERSON>", "rizzupgame": "jogodarizz", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jogosdecelebridade", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5pra5", "rolgame": "rola<PERSON>", "dashiegames": "dashiegames", "gameandkill": "jogeeacabeçasejóia", "traditionalgames": "jogostradicionais", "kniffel": "kniffel", "gamefps": "jogofps", "textbasedgames": "jogosbaseadosemtextos", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "jogo<PERSON><PERSON><PERSON><PERSON>", "lawngames": "jogosdegramados", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "futeboldetableiro", "tischfußball": "tischfußball", "spieleabende": "no<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jogosforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "dardos", "escapegames": "jogosdeescape", "thiefgameseries": "jogosdatrapaça", "cranegames": "jogosdeguindaste", "játék": "joga<PERSON>", "bordfodbold": "bordaf<PERSON><PERSON><PERSON><PERSON>", "jogosorte": "jogosorte", "mage": "magia", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "jogosonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "noitesdegames", "pursebingos": "pursebingos", "randomizer": "randomizador", "msx": "msx", "anagrammi": "anagramas", "gamespc": "gamespc", "socialdeductiongames": "jogosdeducaçãosocial", "dominos": "dominos", "domino": "domino", "isometricgames": "jogosisométric<PERSON>", "goodoldgames": "bonsvelhossgames", "truthanddare": "verdadeouconsequência", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "caçadoresdepeças", "jeuxvirtuel": "jogosvirtuais", "romhack": "romhack", "f2pgamer": "gamerf2p", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>paraj<PERSON><PERSON>", "fantasygame": "jogofantasia", "gryonline": "gryonline", "driftgame": "jogodadroga", "gamesotomes": "gamesotomes", "halotvseriesandgames": "halotvseriesjogos", "mushroomoasis": "oásismushroom", "anythingwithanengine": "qualquercoisaquinmotor", "everywheregame": "jogo<PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "espadaemagia", "goodgamegiving": "jogobomédoar", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8jogos", "labzerogames": "jogoslabzero", "grykomputerowe": "jogosdecomputador", "virgogami": "virgogami", "gogame": "vai<PERSON><PERSON>", "jeuxderythmes": "jogosderitmo", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "amorpelojogo", "gamemodding": "modificand<PERSON><PERSON><PERSON>", "crimegames": "jogosdecrime", "dobbelspellen": "dobbelspellen", "spelletjes": "joguin<PERSON>", "spacenerf": "nerfdoespaco", "charades": "mímica", "singleplayer": "jogadorsolo", "coopgame": "jogocoop", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "jogocentral", "kingdiscord": "reid<PERSON><PERSON>", "scrabble": "palavras<PERSON><PERSON><PERSON><PERSON>", "schach": "<PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "gamão", "onitama": "onitama", "pandemiclegacy": "legadodapandemia", "camelup": "camelup", "monopolygame": "jogomonopolio", "brettspiele": "jogos<PERSON>ab<PERSON><PERSON>", "bordspellen": "jogosdeborda", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "jogosde<PERSON><PERSON>", "planszowe": "jogosdeboard", "risiko": "risiko", "permainanpapan": "jogosdemesa", "zombicide": "zombicide", "tabletop": "mesa", "baduk": "baduk", "bloodbowl": "bowldeamor", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "conect<PERSON>ois<PERSON><PERSON>", "heroquest": "heroquest", "giochidatavolo": "jogos<PERSON>ab<PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "jogos<PERSON>ab<PERSON><PERSON>", "dicegames": "jogo<PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jogosdesociedade", "deskgames": "jogosdelocal", "alpharius": "alfarius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "protocolodacrisemarvel", "cosmicencounter": "encontrocosmico", "creationludique": "criaçãolúdica", "tabletoproleplay": "jogosdetabletop", "cardboardgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>ão", "eldritchhorror": "horrorporcânico", "switchboardgames": "jogosdeswitchboard", "infinitythegame": "infinitojogos", "kingdomdeath": "mort<PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "juegodemesa", "planszówki": "jogos<PERSON>ab<PERSON><PERSON>", "rednecklife": "vidal<PERSON>", "boardom": "tédio", "applestoapples": "maçãcommaçã", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "tabuleirodejogo", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "croquinole", "jeuxdesociétés": "jogosdesociedade", "twilightimperium": "imperiostwilight", "horseopoly": "cavalopoly", "deckbuilding": "construçãodebaralhos", "mansionsofmadness": "mansõesdamalucura", "gomoku": "gomoku", "giochidatavola": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "sombrasdobrimstone", "kingoftokyo": "reinadoemtokio", "warcaby": "warcaby", "táblajátékok": "táblajátékok", "battleship": "<PERSON><PERSON>", "tickettoride": "bilheteparacavaleiro", "deskovehry": "escritorioaventureiro", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "jogosdecomplemento", "stolníhry": "jogosdemesa", "xiángqi": "<PERSON>ian<PERSON><PERSON>", "jeuxsociete": "jogosdesociedade", "gesellschaftsspiele": "bretasdegesellschaft", "starwarslegion": "legiaostardas", "gochess": "j<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "jogosdesociedade", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonadeguerra", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identidadev", "theisle": "aile", "thelastofus": "osúltimosdoplaneta", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "chama<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyeamáquinadetin<PERSON>", "conanexiles": "conanexiles", "eft": "eif", "amongus": "entrenós", "eco": "eco", "monkeyisland": "ilhadosmacacos", "valheim": "valheim", "planetcrafter": "planetcrafters", "daysgone": "diaspassados", "fobia": "fobia", "witchit": "feitiçait", "pathologic": "patológico", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "thelongdark", "ark": "arco", "grounded": "conectado", "stateofdecay2": "estadoemdecadência2", "vrising": "vrising", "madfather": "paidoido", "dontstarve": "nãodeixeofrango", "eternalreturn": "retornoeterno", "pathoftitans": "caminhodostitanes", "frictionalgames": "jogosfrictionais", "hexen": "hexen", "theevilwithin": "oevilde<PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "osquartosdeatrás", "backrooms": "quartosdosbastidores", "empiressmp": "empiresemp", "blockstory": "bloquearhistórias", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "luz<PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "nósfelizespoucos", "riseofempires": "ascensãodosimpérios", "stateofsurvivalgame": "estadodesobrevivencia", "vintagestory": "históriavintage", "arksurvival": "arksobreviver", "barotrauma": "barotrauma", "breathedge": "breathegah", "alisa": "alisa", "westlendsurvival": "sobrevivêndonawestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "sobrevivênciaemterror", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tremva<PERSON>", "lifeaftergame": "vidasdepoisdogame", "survivalgames": "jogosdesobrevivência", "sillenthill": "silenthill", "thiswarofmine": "estaguerradeles", "scpfoundation": "fundacaoscp", "greenproject": "projetoverde", "kuon": "kuon", "cryoffear": "chorandodemedo", "raft": "batebate", "rdo": "boraboo", "greenhell": "infernogreen", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "vovó", "littlenightmares2": "pesadelinhos2", "signalis": "signalis", "amandatheadventurer": "amandanaaventura", "sonsoftheforest": "filhosdoresto", "rustvideogame": "rustjogovideo", "outlasttrials": "sobrevivanotests", "alienisolation": "isolamentoalienígena", "undawn": "semalvorada", "7day2die": "7diasparamorrer", "sunlesssea": "marsemsol", "sopravvivenza": "sobrevivência", "propnight": "noiteprop", "deadisland2": "ilhasmortas2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampiro", "deathverse": "morteverso", "cataclysmdarkdays": "diasescuroscataclísmicos", "soma": "soma", "fearandhunger": "medoeapetite", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "vidaapós", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "clocktower3": "relógio3", "aloneinthedark": "sozinonobrasileiro", "medievaldynasty": "dinastiam<PERSON><PERSON><PERSON>", "projectnimbusgame": "projectnimbusjogo", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "osdesafiostheoutlast", "bunker": "bunker", "worlddomination": "dominaomundo", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officiodeassassinos", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "matadordeanões", "warhammer40kcrush": "paixonetim40k", "wh40": "wh40", "warhammer40klove": "amorwarhammer40k", "warhammer40klore": "lorewarhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kmaréescura", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "virandojogo", "ilovesororitas": "euamossororitas", "ilovevindicare": "euamovindicare", "iloveassasinorum": "euamoassassinorum", "templovenenum": "temploveneno", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "ofícioasseinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "eradosimp<PERSON><PERSON>s", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammeridadeofsigmar", "civilizationv": "civilizaçãov", "ittakestwo": "ittakestwo", "wingspan": "envergadura", "terraformingmars": "transformandomarspravida", "heroesofmightandmagic": "heróisdeforçasemagia", "btd6": "btd6", "supremecommander": "comandantesupremo", "ageofmythology": "eraofmtologia", "args": "args", "rime": "rime", "planetzoo": "planetazoo", "outpost2": "outpost2", "banished": "banido", "caesar3": "caesar3", "redalert": "alarmered", "civilization6": "civilização6", "warcraft2": "warcraft2", "commandandconquer": "comandoseconquiste", "warcraft3": "warcraft3", "eternalwar": "guerrainfinita", "strategygames": "jogosdestrategia", "anno2070": "ano2070", "civilizationgame": "jogodacivilização", "civilization4": "civilização4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "esporo", "totalwar": "totalguerra", "travian": "travian", "forts": "forts", "goodcompany": "boacompanhia", "civ": "civ", "homeworld": "mund<PERSON><PERSON>a", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "pelo<PERSON><PERSON>", "realtimestrategy": "estrategiaemtemporeal", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "reinodeduascoroas", "eu4": "eu4", "vainglory": "vainglory", "ww40k": "ww40k", "godhood": "divindade", "anno": "<PERSON><PERSON><PERSON><PERSON>", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "auladeálgebradodavedivertida", "plagueinc": "plagueinc", "theorycraft": "teoriadocrack", "mesbg": "mesbg", "civilization3": "civilização3", "4inarow": "4emlinha", "crusaderkings3": "crusaderkings3", "heroes3": "heróis3", "advancewars": "advancewars", "ageofempires2": "idadedosimpérios2", "disciples2": "discípulos2", "plantsvszombies": "plantasvszumbis", "giochidistrategia": "jogosdeestratégia", "stratejioyunları": "jogosestratégicos", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "conquistandoomundo", "heartsofiron4": "coraçãodeferro4", "companyofheroes": "compan<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "batalha<PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "for<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansotrapacega", "phobies": "fobias", "phobiesgame": "jogodafobia", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "expressãoexterna", "turnbased": "turnos", "bomberman": "bomberman", "ageofempires4": "idadedosimpérios4", "civilization5": "civilização5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "feitiçaria", "starwarsempireatwar": "starwar<PERSON>mperioemguerra", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrategia", "popfulmail": "popfulmail", "shiningforce": "forçaaluz", "masterduel": "duelomestre", "dysonsphereprogram": "programadysonsphere", "transporttycoon": "magnatedotransporte", "unrailed": "destrambelhado", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planejandocapetormento", "uplandkingdoms": "reinosupland", "galaxylife": "vidagaláctica", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "precisodevelocidade", "needforspeedcarbon": "precisodevelocidadecarbono", "realracing3": "corridareal3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "perdims4", "fnaf": "fnaf", "outlast": "sobreviva", "deadbydaylight": "mortoporporvida", "alicemadnessreturns": "retornoalicemadness", "darkhorseanthology": "antologiadocavaloescuro", "phasmophobia": "fasmofobia", "fivenightsatfreddys": "cinquenaosnofreddys", "saiko": "saiko", "fatalframe": "quadrofatal", "littlenightmares": "pequenospesadelos", "deadrising": "zumb<PERSON>nascendo", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "emcasa", "deadisland": "ilhadomorto", "litlemissfortune": "pequenaazarada", "projectzero": "projetozero", "horory": "hororinho", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "olaprof2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON>", "jeuxhorreur": "jogosdehorror", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardscontraumanidade", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "nomesdecódigo", "dixit": "dixit", "bicyclecards": "cartasdebike", "lor": "lora", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legend<PERSON><PERSON><PERSON>", "solitaire": "solitário", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "truquesdecartas", "playingcards": "cartasdeflame", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartascolecionáveis", "pokemoncards": "cartasdepokemon", "fleshandbloodtcg": "carneebludotcg", "sportscards": "cartasdesportivas", "cardfightvanguard": "lutadedecartasvanguard", "duellinks": "duellinks", "spades": "espadas", "warcry": "gritodeguerra", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "reidoscorações", "truco": "truco", "loteria": "sorteio", "hanafuda": "hana<PERSON>da", "theresistance": "aresistência", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "cartasdeyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "magiadastrela", "blueeyeswhitedragon": "dragãobrancoolhoazul", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcomandante", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "jogosdecartas", "mtgjudge": "juizmtg", "juegosdecartas": "jogosdecartas", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "j<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "espiritosdebatalha", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "f<PERSON><PERSON><PERSON>", "facecard": "facecard", "cardfight": "lutaocartas", "biriba": "biriba", "deckbuilders": "construtoresdeck", "marvelchampions": "marvel<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "cartasmágicas", "yugiohmasterduel": "yugioumasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicórniosinstáveis", "cyberse": "ciberse", "classicarcadegames": "jogosdearcadeclássicos", "osu": "osu", "gitadora": "gitadora", "dancegames": "jogosdedança", "fridaynightfunkin": "feriadonightfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectomirai", "projectdiva": "projetodiva", "djmax": "djmax", "guitarhero": "heróidaguitarra", "clonehero": "clonehero", "justdance": "sódançar", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "<PERSON><PERSON><PERSON>", "dancecentral": "dancecentral", "rhythmgamer": "gamerdoerhythm", "stepmania": "stepmania", "highscorerythmgames": "jogosderitmodaltoscorers", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "dan<PERSON>ndoemfogoegelo", "auditiononline": "audiçãonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "jogosderitmo", "cryptofthenecrodancer": "cryptodondanecromante", "rhythmdoctor": "medicodoritmo", "cubing": "cubismo", "wordle": "palavras", "teniz": "teniz", "puzzlegames": "jogosdepuzzle", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "puzzlesdelógicos", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "desafiosmentais", "rubikscube": "cubo<PERSON><PERSON><PERSON>", "crossword": "palavras<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "palavraschave", "nonogram": "nonograma", "bookworm": "b<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "quebracabeças", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "enigma", "riddles": "charadas", "rompecabezas": "quebracabeça", "tekateki": "tekateki", "inside": "dentro", "angrybirds": "pássaros<PERSON><PERSON><PERSON>", "escapesimulator": "simulad<PERSON><PERSON>", "minesweeper": "bandeiras", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "palavraschave", "kurushi": "k<PERSON>hi", "gardenscapesgame": "jogogardenscapes", "puzzlesport": "puzzleesporte", "escaperoomgames": "jogosdeescaperoom", "escapegame": "jogo<PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "quebracabeça3d", "homescapesgame": "jogo<PERSON><PERSON><PERSON>", "wordsearch": "buscadaspalavras", "enigmistica": "enigmistica", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "contoseenigma", "fishdom": "fishdom", "theimpossiblequiz": "oquizimpossível", "candycrush": "candycrush", "littlebigplanet": "pequenoplanetagigante", "match3puzzle": "quebracabeça3match", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "cubomágico", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "oprincipiodastalos", "homescapes": "homescapes", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "desafieomeucérebro", "tycoongames": "jogosdetycoon", "cubosderubik": "cubosderubik", "cruciverba": "palavras<PERSON><PERSON><PERSON><PERSON>", "ciphers": "cifras", "rätselwörter": "palavrasecretas", "buscaminas": "minado<PERSON>", "puzzlesolving": "resolvendopuzzles", "turnipboy": "turnipboy", "adivinanzashot": "adivinan<PERSON>", "nobodies": "ning<PERSON><PERSON>", "guessing": "chutando", "nonograms": "nonogramas", "kostkirubika": "kostkirubika", "crypticcrosswords": "cruzadacriptica", "syberia2": "syberia2", "puzzlehunt": "caçapuzzlês", "puzzlehunts": "caçapuzzas", "catcrime": "<PERSON><PERSON><PERSON>", "quebracabeça": "quebracabeça", "hlavolamy": "quebracabeça", "poptropica": "poptropica", "thelastcampfire": "oúltimofogueiralocal", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "cançõesdevagantes", "carto": "carto", "untitledgoosegame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "cassetete", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirinto", "tinykin": "tinykin", "rubikovakostka": "cuboderubik", "speedcube": "speedcube", "pieces": "peças", "portalgame": "portalgame", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "cuberubik", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomágico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "ma<PERSON><PERSON><PERSON>ret<PERSON><PERSON><PERSON>", "monopoly": "monopólio", "futurefight": "futuro<PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coque", "lonewolf": "lobosolitário", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "estrelasensemble", "asphalt9": "asfalto9", "mlb": "mlb", "cookierunkingdom": "reinodascookies", "alchemystars": "astrosalquímicos", "stateofsurvival": "estadodesobrevivência", "mycity": "minhacidade", "arknights": "arknights", "colorfulstage": "palcoscoloridos", "bloonstowerdefense": "defesadatorresbloon", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "corridadecavaleiros", "fireemblemheroes": "heróisdefogo", "honkaiimpact": "honkaiimpact", "soccerbattle": "batalhadfutebol", "a3": "a3", "phonegames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "escolhadokings", "guardiantales": "contosdaguardiã", "petrolhead": "apaixonadoporpetróleo", "tacticool": "tacticool", "cookierun": "corridadoce", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "for<PERSON><PERSON><PERSON>", "craftsman": "artesão", "supersus": "supersus", "slowdrive": "dirigi<PERSON><PERSON><PERSON>", "headsup": "ficaadica", "wordfeud": "wordfeud", "bedwars": "guerrasdoleito", "freefire": "freefire", "mobilegaming": "jogosdemóvel", "lilysgarden": "jardimdasilly", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "taticadecombate", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mensageiromístico", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "bilhar8ball", "emergencyhq": "emergênciahq", "enstars": "estrelas", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "festanostalgia", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON>and<PERSON>dget", "ml": "ml", "bangdream": "bangdreambr", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "temporeina", "beatstar": "beatstar", "dragonmanialegend": "dragãohomemlenda", "hanabi": "hanabi", "disneymirrorverse": "espelhodadisneylândia", "pocketlove": "amordebolso", "androidgames": "jogosandroid", "criminalcase": "casodecrime", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leagueofangels", "lordsmobile": "lordsmobile", "tinybirdgarden": "jardimpequenobird", "gachalife": "gachalife", "neuralcloud": "nuvemneural", "mysingingmonsters": "meusmonstroscantores", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "espelhoverso", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ingresso", "slugitout": "esbofeteia", "mpl": "mpl", "coinmaster": "monet<PERSON><PERSON>", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "amigosdepets", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenabreakout", "wolfy": "lo<PERSON><PERSON>", "runcitygame": "<PERSON><PERSON>dacor<PERSON>", "juegodemovil": "j<PERSON><PERSON><PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimese", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandeper<PERSON><PERSON><PERSON><PERSON>", "bombmebrasil": "bombemeibrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "otomeg<PERSON>", "mindustry": "mindústria", "callofdragons": "chamadadosdragões", "shiningnikki": "brilhandonik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "caminoquase", "sealm": "selam", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "palavrascomamigos2", "soulknight": "cavaleiradasalmas", "purrfecttale": "históriaperfeita", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "lolmobile", "harvesttown": "colhetacidade", "perfectworldmobile": "mundoperfeitomóvel", "empiresandpuzzles": "impériosepuzzles", "empirespuzzles": "puzzlesdoboo", "dragoncity": "dragãocidade", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilebr", "fanny": "bumbum", "littlenightmare": "pequenospesadelos", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "lágrimasdosmitos", "eversoul": "eversoul", "gunbound": "armadaboa", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombisaboados", "eveechoes": "<PERSON><PERSON><PERSON>", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendstácomtudo", "gachaclub": "gachaclube", "v4": "v4", "cookingmama": "co<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "desafiostreetfighter", "lesecretdhenri": "o<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "frentedas<PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldvivo", "soulseeker": "caçadorsoul", "gettingoverit": "superando", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "legendadenuncaterra", "pubglite": "pubglite", "gamemobilelegends": "jogomobilelegends", "timeraiders": "caçadoresdetemporadas", "gamingmobile": "jogosmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "os<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "<PERSON>afio", "giochidiruolo": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "mundodasobras", "travellerttrpg": "viajantesttrpg", "2300ad": "2300dc", "larp": "larp", "romanceclub": "clubederomance", "d20": "d20", "pokemongames": "pokemongames", "pokemonmysterydungeon": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonvermelho", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON>leta", "pokemonpurpura": "pokemonroxo", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "equipefoguete", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monstrosdetag", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "equipemística", "pokeball": "pokebola", "charmander": "charmander", "pokemonromhack": "rom<PERSON><PERSON><PERSON><PERSON>", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokémonbrilhante", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "mãosdeferro", "kabutops": "kabutops", "psyduck": "psydduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokémondormir", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "mest<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "caçadorbrilhante", "ajedrez": "<PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON><PERSON><PERSON>", "schaken": "chacoalhando", "skak": "skak", "ajedres": "<PERSON><PERSON><PERSON>", "chessgirls": "garotalogica", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "mundoblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "xadrescanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "aberturas", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabozosedragões", "dungeonsanddragon": "cavernasedragões", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdrag<PERSON><PERSON>", "oxventure": "oxventura", "darksun": "solsombrio", "thelegendofvoxmachina": "alegendadevoxmachina", "doungenoanddragons": "<PERSON><PERSON>ndoedrag<PERSON><PERSON>", "darkmoor": "darkmoor", "minecraftchampionship": "campeonatodminecraft", "minecrafthive": "colmeiadominecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsdominecraft", "mcc": "mcc", "candleflame": "ch<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "adicionais", "mcpeaddons": "mcpeadicionais", "skyblock": "skyblock", "minecraftpocket": "minecraftbolso", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodificado", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "mundosintermediários", "minecraftdungeons": "dungeonsdominecraft", "minecraftcity": "cidademinecraft", "pcgamer": "pcgamer", "jeuxvideo": "jogosdevideo", "gambit": "gambito", "gamers": "gamers", "levelup": "evoluir", "gamermobile": "gamermobile", "gameover": "gameover", "gg": "gg", "pcgaming": "jogosdepc", "gamen": "j<PERSON><PERSON>", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "jogosdepc", "casualgaming": "jogosdescontraídos", "gamingsetup": "configuraçãodejo<PERSON>", "pcmasterrace": "masterracepc", "pcgame": "jogodepc", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "jogosvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "jogadorconsole", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "jogosonline", "semigamer": "semigamer", "gamergirls": "garotasgamer", "gamermoms": "maesgamer", "gamerguy": "garotodojogo", "gamewatcher": "observadordejogo", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerchicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "equipefocada", "mallugaming": "mallugaming", "pawgers": "patudebom", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerantigo", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "jogosdepc", "dsswitch": "dsswitch", "competitivegaming": "jogosemprestativa", "minecraftnewjersey": "minecraftnovajersey", "faker": "f<PERSON><PERSON><PERSON>", "pc4gamers": "pc4gamers", "gamingff": "jogan<PERSON><PERSON>o", "yatoro": "yatoro", "heterosexualgaming": "gamingheteros", "gamepc": "gamepc", "girlsgamer": "meninasgamer", "fnfmods": "modsfdoboo", "dailyquest": "buscadefinadiária", "gamegirl": "garotadogame", "chicasgamer": "chicasgamer", "gamesetup": "configu<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "superpoderoso", "socialgamer": "game<PERSON><PERSON><PERSON><PERSON>", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "jogador", "myteam": "meuteam", "republicofgamers": "republicadosgamers", "aorus": "aorus", "cougargaming": "jogandocougar", "triplelegend": "triplegênia", "gamerbuddies": "amigosgamer", "butuhcewekgamers": "precisandodegamerfeminina", "christiangamer": "gamercristão", "gamernerd": "nerddegame", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamerdescontraído", "89squad": "89squad", "inicaramainnyagimana": "iniciaramainnyagimana", "insec": "insec", "gemers": "gemers", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "nomedogamer", "lanparty": "festadejogos", "videogamer": "videogamer", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdoge", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamerfitness", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "mulhergamer", "obviouslyimagamer": "obviamenteeujugador", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forageiro", "humanfallflat": "humanoquebrado", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zerofuga", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musicanintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "troca", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "máscarademajora", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "advocatusaceito", "ssbm": "ssbm", "skychildrenofthelight": "criançasdo<PERSON><PERSON>", "tomodachilife": "vidadotomo", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simuladoresdeandar", "nintendogames": "jogosdenintendo", "thelegendofzelda": "thelegendofzelda", "dragonquest": "<PERSON><PERSON><PERSON><PERSON>", "harvestmoon": "luaes<PERSON>a", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "soprodaselvagem", "myfriendpedro": "meuamigopedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON><PERSON>", "earthbound": "chamadaterra", "tales": "contos", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "estratégiadet<PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintend<PERSON>s", "new3ds": "novo3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerreirosdehyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioeesonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendsbr", "urgot": "vamoquevamo", "zyra": "zyra", "redcanids": "canidosvermelhos", "vanillalol": "vanillalol", "wildriftph": "wildriftbr", "lolph": "lolph", "leagueoflegend": "leagueoflegendas", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsbrasil", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "rind<PERSON><PERSON><PERSON><PERSON>", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadasnaslegendas", "gaminglol": "gamingrsrs", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "portaishex", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamefortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrogames", "scaryvideogames": "jogosdevideoguedearrepiar", "videogamemaker": "criadorvideogame", "megamanzero": "megamanzero", "videogame": "videogame", "videosgame": "videogame", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arcades", "acnh": "acnh", "puffpals": "amigosdebufo", "farmingsimulator": "simuladordeagricultura", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxbrasil", "robloxdeutsch": "robloxbrasil", "erlc": "erlc", "sanboxgames": "sandboxgames", "videogamelore": "loredevideogame", "rollerdrome": "rollerdrome", "parasiteeve": "parasitaeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "pais<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "espaçomorto", "amordoce": "amordoce", "videogiochi": "videogames", "theoldrepublic": "aantigarepública", "videospiele": "videogames", "touhouproject": "touhouproject", "dreamcast": "sonhoemcast", "adventuregames": "jogosdeaventura", "wolfenstein": "wolfenstein", "actionadventure": "açãoeaventura", "storyofseasons": "históriadasestações", "retrogames": "retrogames", "retroarcade": "<PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "computaçãoantiga", "retrogaming": "retrogaming", "vintagegaming": "jogosvintage", "playdate": "encontrodebrincar", "commanderkeen": "comandantekeen", "bugsnax": "bugsnax", "injustice2": "injustiça2", "shadowthehedgehog": "sombra<PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "jogosdo<PERSON>u", "zenlife": "vidazen", "beatmaniaiidx": "beatmaniaidx", "steep": "íngreme", "mystgames": "<PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "jogosblockchain", "medievil": "medieval", "consolegaming": "jogosnaconsola", "konsolen": "konsolen", "outrun": "superar", "bloomingpanic": "panicab<PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horroreg<PERSON>s", "monstergirlquest": "monstergirlquest", "supergiant": "supersônico", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simuladordeagricultura", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ficçãointerativa", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesmentes", "visualnovel": "novelavisual", "visualnovels": "novelsvisuais", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "fantasmatcr", "payday": "diadepa<PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "princesadanoite", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "caixadeareia", "aestheticgames": "jogosestétic<PERSON>", "novelavisual": "novelavisual", "thecrew2": "aequipa2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "boraboo", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revoluçãodosventiladoresdefolhas", "wiiu": "wiiu", "leveldesign": "designnivel", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "mecontaaperigo", "fnafsometimes": "asvezesfnaf", "novelasvisuales": "novelasvisuais", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videogames", "videogamedates": "encontrosdevideogame", "mycandylove": "meumelocatodo", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "sóporcausa3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "batmangames", "returnofreckoning": "retornodaconta", "gamstergaming": "gamstergaming", "dayofthetantacle": "diadotentá<PERSON>lo", "maniacmansion": "mansãomaníaca", "crashracing": "corridaacidente", "3dplatformers": "plataformas3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "jogosdethistó<PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "soundodger", "beyondtwosouls": "alémdestressouls", "gameuse": "jogacomigo", "offmortisghost": "booforadocaixão", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "potencialize", "katanazero": "katanazero", "famicom": "famicon", "aventurasgraficas": "aventurasgráficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "arcadesretrô", "f123": "f123", "wasteland": "desolação", "powerwashsim": "lavagempoderosa", "coralisland": "ilhacoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "blox<PERSON><PERSON><PERSON>", "anotherworld": "outromundo", "metaquest": "metaquest", "animewarrios2": "guerreirosanime2", "footballfusion": "fusãofutebol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalretorcido", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "montanhadacontratação", "simulator": "simulador", "symulatory": "simulat<PERSON><PERSON>", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "j<PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "paradisevirtual", "skylander": "skylander", "boyfrienddungeon": "namorado<PERSON><PERSON><PERSON>", "toontownrewritten": "toontownreescrito", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "caosurbano", "heavenlybodies": "corposcelest<PERSON><PERSON>", "seum": "seum", "partyvideogames": "festavideogames", "graveyardkeeper": "zeladordecemitério", "spaceflightsimulator": "simuladordeflientedoespaco", "legacyofkain": "leg<PERSON><PERSON><PERSON>", "hackandslash": "<PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "comidasevideogames", "oyunvideoları": "jogosdevideo", "thewolfamongus": "owolfentrenós", "truckingsimulator": "simuladordecaminhão", "horizonworlds": "mundoshorizonte", "handygame": "jogo<PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "lendasevideojogos", "oldschoolvideogames": "videogamesoldschool", "racingsimulator": "simuladordecorrida", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentesdamáfia", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "portõesdoolímpo", "monsterhunternow": "caçadmonstrosagora", "rebelstar": "estrelarebelde", "indievideogaming": "indievideogame", "indiegaming": "jogo<PERSON><PERSON>", "indievideogames": "videogamesindie", "indievideogame": "videogabeindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "homemdeferroinsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "imbatível", "projectl": "projetol", "futureclubgames": "futuroclubedosjogos", "mugman": "mugman", "insomniacgames": "insomniacgames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "ciênciadeaperturas", "backlog": "pendências", "gamebacklog": "atrasadosnogame", "gamingbacklog": "<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "personage<PERSON><PERSON><PERSON>", "achievementhunter": "caçadoradeconquistas", "cityskylines": "paisagensurbanas", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "cachorrin<PERSON><PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "jogosretro", "kentuckyroutezero": "roteirokentuckyzero", "oriandtheblindforest": "oriemflorestasurda", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatóriodedopamina", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "euamokofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "corridapc", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "cavaleirosdodia", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animechato", "darkerthanblack": "maisescuroquepreto", "animescaling": "animescaling", "animewithplot": "animecomhistória", "pesci": "pesquisas", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "animeanos80", "90sanime": "animeanos90", "darklord": "senhornegro", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "animeanos2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "homedacharge", "animecover": "coberturanime", "thevisionofescaflowne": "avisãodeescaflowne", "slayers": "matadores", "tokyomajin": "tokyomajin", "anime90s": "animeanos90", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "pei<PERSON>aba<PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "forçafogo", "moriartythepatriot": "moriartyopatriota", "futurediary": "diariofuturo", "fairytail": "contofadas", "dorohedoro": "dorohedoro", "vinlandsaga": "sagavinland", "madeinabyss": "feitonobrinco", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "se<PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON>", "romancemangas": "romancesmangas", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragamaid", "blacklagoon": "lagoonpreta", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformares", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasquete", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "umcertainmagicalindex", "sao": "sao", "blackclover": "trevopreto", "tokyoghoul": "tokyoghoul", "onepunchman": "umgolpemano", "hetalia": "hetalia", "kagerouproject": "projet<PERSON><PERSON><PERSON>", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8oinafinidade", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioridadedeovosmaravilhosos", "angelsofdeath": "anjosdamorte", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosímico", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeesporte", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "j<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagadatanyaaevil", "shounenanime": "animejovem", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "linda<PERSON>ra", "theboyandthebeast": "oboyoefera", "fistofthenorthstar": "punh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "to<PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "comoacompanharumamae", "fullmoonwosagashite": "lua<PERSON><PERSON>wosagas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "fofoeestranho", "martialpeak": "picoamarcial", "bakihanma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "fadarevenante", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "garotamonstro", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animevelho", "chainsawman": "homemserra", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "frutasebanco", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "amomorando", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "oprometidoque<PERSON><PERSON><PERSON><PERSON><PERSON>u", "monstermanga": "monstermanga", "yourlieinapril": "suamentiraemabril", "buggytheclown": "buggyopalhaço", "bokunohero": "<PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "seraphoftheend", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "prisioneirodasprofundezas", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mortalparadise", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "coraçõesdepandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerrafood", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "linhadosmalfeitores", "toyoureternity": "paratueternidade", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "periododeazul", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "aliançasecreta", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "matadordegoblins", "detectiveconan": "detetiveconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "cavaleirovampiro", "mugi": "mugi", "blueexorcist": "exorcistazul", "slamdunk": "afundaço", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "familiaespiã", "airgear": "airgear", "magicalgirl": "garotamagica", "thesevendeadlysins": "essessetepecadoscapitais", "prisonschool": "escoladeprisão", "thegodofhighschool": "ogodohighschool", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandeazul", "mydressupdarling": "meufantasiadearte", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universodeanime", "swordartonlineabridge": "swordartonlineabraçada", "saoabridged": "s<PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "romancemangá", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayertopasso", "bloodlad": "sanguel<PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "punchfogo", "adioseri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "estrelasseconectam", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "fatorintegralsao", "cherrymagic": "magiacherry", "housekinokuni": "ca<PERSON><PERSON>no", "recordragnarok": "<PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "escolademortos", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "sagadevindland", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "saladeassassinos", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "desfiledamorte", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "<PERSON>ja<PERSON><PERSON><PERSON><PERSON>", "animespace": "espaçoanime", "girlsundpanzer": "garotasundpanzer", "akb0048": "akb0048", "hopeanuoli": "esper<PERSON><PERSON><PERSON>", "animedub": "dublagemanime", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "homemrato", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "garotapêssego", "cavalieridellozodiaco": "cavaleirosdodiazodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "coraçãodamanga", "deliciousindungeon": "deliciosoesnocaverna", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "registroderagnarok", "funamusea": "diversãodamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "pu<PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "mestredorama", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ateliêdochapéudebruxa", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangats<PERSON>leão", "kamen": "kamen", "mangaislife": "mangáisvid<PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "oscavaleirosdozodíaco", "animeshojo": "animeshojo", "reverseharem": "harem<PERSON><PERSON>o", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "grandeprofessoronizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldado", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "sonhodagrandeblue", "bloodplus": "<PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "sangueeanime", "bloodcanime": "sanguecanime", "bloodc": "<PERSON>ue<PERSON>", "talesofdemonsandgods": "contosdedemôniosiegods", "goreanime": "goreanime", "animegirls": "garo<PERSON>anime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "cãesxpiores", "splatteranime": "animepicado", "splatter": "respingo", "risingoftheshieldhero": "aerguidaodoheróiscoloridos", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedaatacada", "animeyuri": "animeyuri", "animeespaña": "animeespanha", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "criançasdasbaleias", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeões", "animeidols": "idols<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiestavainteligente", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "garotasmagicas", "callofthenight": "chamadosolanoche", "bakuganbrawler": "brawlege<PERSON><PERSON>u", "bakuganbrawlers": "brawlersdobakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "achomanga", "princessjellyfish": "princesaaguamonstra", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "beijodoparaíso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revueestrelada", "animeverse": "universodeanime", "persocoms": "persocoms", "omniscientreadersview": "visãodoleitoromnisciente", "animecat": "animegato", "animerecommendations": "recomendaçõesanime", "openinganime": "abrindo<PERSON>me", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "minharomancejuvenilcomédia", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "rob<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesevangelion", "codegeass": "codegeass", "mobilefighterggundam": "gundamdecombatemóvel", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "obigobooanime", "bleach": "descolorante", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "aventurabizarradojojo", "fullmetalalchemist": "alfabetocompleto", "ghiaccio": "gelado", "jojobizarreadventures": "aventurasbizarras<PERSON>jojo", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitar", "greenranger": "hashtagverdemorango", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondemangá", "lupinthe3rd": "lupinthe3rd", "animecity": "cidadeanime", "animetamil": "animesantais", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "aventuradigimon", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "animejovem", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "matadoresdedemô<PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ataquedostitãs", "erenyeager": "erene<PERSON>", "myheroacademia": "meuheróiacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "jogo<PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corposdepesquisa", "onepieceanime": "animeonepiece", "attaquedestitans": "atacaosgigantes", "theonepieceisreal": "oonepieceéverdadeiro", "revengers": "vingadores", "mobpsycho": "mobpsycho", "aonoexorcist": "anoexorcista", "joyboyeffect": "efeitojoyboy", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "supercadeia", "metalocalypse": "metalocause", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonsemdefeitos", "kemonofriends": "kemonosa<PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "bruxaflu<PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "sóporque", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "recontosdavida"}