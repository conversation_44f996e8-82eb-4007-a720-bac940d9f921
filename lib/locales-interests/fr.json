{"2048": "2048", "mbti": "mbti", "enneagram": "énnéagramme", "astrology": "astrologie", "cognitivefunctions": "fonctionscognitives", "psychology": "psychologie", "philosophy": "philosophie", "history": "histoire", "physics": "physique", "science": "science", "culture": "culture", "languages": "langues", "technology": "technologie", "memes": "mèmes", "mbtimemes": "mèmesmbti", "astrologymemes": "mèmesastrologie", "enneagrammemes": "mèmesénnéagramme", "showerthoughts": "ré<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "d<PERSON><PERSON><PERSON>", "videos": "vidéos", "gadgets": "gadgets", "politics": "politique", "relationshipadvice": "conseilsrelationnels", "lifeadvice": "conseilsdevie", "crypto": "cryptomonnaie", "news": "actualités", "worldnews": "actualitésmondiales", "archaeology": "archéologie", "learning": "apprentissage", "debates": "débats", "conspiracytheories": "théoriesducomplot", "universe": "univers", "meditation": "méditation", "mythology": "mythologie", "art": "art", "crafts": "artisanat", "dance": "danse", "design": "design", "makeup": "maquillage", "beauty": "beauté", "fashion": "mode", "singing": "chant", "writing": "écriture", "photography": "photographie", "cosplay": "cosplay", "painting": "peinture", "drawing": "dessin", "books": "livres", "movies": "films", "poetry": "poésie", "television": "télévision", "filmmaking": "cinématographie", "animation": "animation", "anime": "anime", "scifi": "sciencefiction", "fantasy": "fantasy", "documentaries": "documentaires", "mystery": "myst<PERSON>", "comedy": "comédie", "crime": "policier", "drama": "drame", "bollywood": "bollywood", "kdrama": "dramacoréen", "horror": "horreur", "romance": "romance", "realitytv": "téléréalité", "action": "action", "music": "musique", "blues": "blues", "classical": "classique", "country": "country", "desi": "desi", "edm": "edm", "electronic": "électronique", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "house", "indie": "indé", "jazz": "jazz", "kpop": "kpop", "latin": "latine", "metal": "m<PERSON><PERSON>", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "voyage", "concerts": "concerts", "festivals": "festivals", "museums": "musées", "standup": "standup", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "extérieur", "gardening": "jardinage", "partying": "fête", "gaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgames": "jeux<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "donjonsetdragons", "chess": "échecs", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pok<PERSON><PERSON>", "food": "alimentation", "baking": "pâtisserie", "cooking": "cuisine", "vegetarian": "végétarien", "vegan": "végan", "birds": "oiseaux", "cats": "chats", "dogs": "chiens", "fish": "poissons", "animals": "animaux", "blacklivesmatter": "blacklivesmatter", "environmentalism": "écologie", "feminism": "féminisme", "humanrights": "droitsdelhomme", "lgbtqally": "alliélgbtq", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON>", "transally": "alliétrans", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "basket", "boxing": "boxe", "cricket": "cricket", "cycling": "cyclisme", "fitness": "fitness", "football": "football", "golf": "golf", "gym": "salledesport", "gymnastics": "gymnastique", "hockey": "hockey", "martialarts": "<PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "courseàpied", "skateboarding": "skateboard", "skiing": "ski", "snowboarding": "snowboard", "surfing": "surf", "swimming": "natation", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "haltérophilie", "yoga": "yoga", "scubadiving": "plongéesousmarine", "hiking": "randonn<PERSON>", "capricorn": "<PERSON>ric<PERSON><PERSON>", "aquarius": "verseau", "pisces": "poisson", "aries": "b<PERSON><PERSON>", "taurus": "taure<PERSON>", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "cancer", "leo": "lion", "virgo": "vierge", "libra": "balance", "scorpio": "scorpion", "sagittarius": "sagittaire", "shortterm": "courtter<PERSON>e", "casual": "decontracte", "longtermrelationship": "relationserieuse", "single": "célibataire", "polyamory": "polyamour", "enm": "nonmonogame", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisexuel", "pansexual": "pansexuel", "asexual": "asexuel", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "ch<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "qu<PERSON><PERSON><PERSON><PERSON>", "soulreaver": "récupérateursdâme", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "coucherdesoleiloverdrive", "arkham": "arkham", "deusex": "deuxex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "halo<PERSON><PERSON>i", "guildwars": "guerresdeguildes", "openworld": "<PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "âmessemblables", "dungeoncrawling": "explorationdedungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribusdemidgard", "planescape": "planquespirituelles", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "patfofexil", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "sorcelier", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "chute", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "créationdepersonnages", "immersive": "immersif", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivationmorbid", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "craquepourlamour", "otomegames": "jeuxotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampireslamasquerade", "dimension20": "dimension20", "gaslands": "terresgazeuses", "pathfinder": "cher<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ed", "shadowrun": "shadowrun", "bloodontheclocktower": "dusangsurlhorloge", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "vaguegravitationnelle", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "souverain", "yourturntodie": "votretournedecrever", "persona3": "persona3", "rpghorror": "horreurrpg", "elderscrollsonline": "lesancienscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "maraudeurs", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "épique7", "rpgtext": "rpgtexte", "genshin": "genshin", "eso": "peuch<PERSON>", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "abriantiatomique", "gurps": "gourps", "darkestdungeon": "donjonleplusnoir", "eclipsephase": "phaseeclipse", "disgaea": "disgaea", "outerworlds": "mondesextérieurs", "arpg": "rpgà<PERSON>laise", "crpg": "jdr", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "guerriersdudynastie", "skullgirls": "skullgirls", "nightcity": "villedenuit", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "combatdelamaison", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "route96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "chevaliersdegotham", "forgottenrealms": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "lancedurd<PERSON><PERSON>", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "enfantdelumière", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "mon<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "trônesfêlés", "horizonforbiddenwest": "horizonlamerinterdite", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "mysteresdhogwarts", "deltagreen": "deltavert", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "frapper", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "cher<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinitéoriginalesin", "bladesinthedark": "lamesdanslombre", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrouge", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "terresmaléfiques", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "diablesurvivant", "oldschoolrunescape": "runescapeoldschool", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinité", "pf2": "pf2", "farmrpg": "fermerpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "quêteaventure", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "j<PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "histoiresdesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "villedévas<PERSON>e", "myfarog": "<PERSON><PERSON><PERSON>", "sacredunderworld": "sacreduniverssouterrain", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "darksoul", "soulslikes": "âmessemblables", "othercide": "autrescides", "mountandblade": "montetépée", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chron<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "pilliersdéternité", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "ladivision", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendededrago<PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octopathvoyageur", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "loupgarouapocalypse", "aveyond": "aveyond", "littlewood": "petit<PERSON><PERSON>", "childrenofmorta": "enfants<PERSON>mor<PERSON>", "engineheart": "cœ<PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fablelechapitreperdu", "hiveswap": "hiveswap", "rollenspiel": "jeudemot", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "étoilefield", "oldschoolrevival": "revivaloldschool", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mondessauvage", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "jeuxrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmalkavien", "harvestella": "<PERSON><PERSON><PERSON>lt<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "cœurssauvages", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "cielsdarcadia", "shadowhearts": "coeur<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennysang", "breathoffire4": "souffledefeu4", "mother3": "maman3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "j<PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "jeuderôle", "fabulaultima": "fabulault<PERSON>", "witchsheart": "co<PERSON>dewitch", "harrypottergame": "harrypottergame", "pathfinderrpg": "cheminotrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamascarade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "chasseroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "chasseur<PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "lalli<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "<PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "royaumedelavènement", "awplanet": "awplanète", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "vieuxscroll", "dyinglight2": "lumièreremorte2", "finalfantasytactics": "finalfantasytactiques", "grandia": "grandia", "darkheresy": "h<PERSON><PERSON>sieobscure", "shoptitans": "bootitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magie<PERSON><PERSON><PERSON>", "blackbook": "carnetnoir", "skychildrenoflight": "en<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "éditiondorées<PERSON><PERSON><PERSON>", "castlecrashers": "castlecrasher", "gothicgame": "jeugothique", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "jeuxrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "villebrumeuse", "indierpg": "indierpg", "pointandclick": "pointetclic", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisible", "freeside": "libreside", "epic7": "epic7", "ff7evercrisis": "ff7pourlavie", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "routeenmourantverslecanada", "palladium": "palladium", "knightjdr": "cheval<PERSON><PERSON><PERSON>", "monsterhunter": "chasseur<PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "géosupremacie", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "chasseur<PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "mangeurdes<PERSON>ls", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "jeuxnonaires", "tacticalrpg": "rpgstratégiques", "mahoyo": "mahoyo", "animegames": "j<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "mangeurdegode", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonate<PERSON><PERSON><PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "cercledemamies", "cristales": "crist<PERSON>", "vcs": "vcs", "pes": "peuf", "pocketsage": "pochesage", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON>ien", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "jeuxvideo", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "liguedesrêveurs", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gaimin", "overwatchleague": "liguedesurveillance", "cybersport": "cybersport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eracing": "éraciser", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompétitif", "t3arena": "t3arena", "valorantbr": "valorantfr", "csgo": "csgo", "tf2": "tf2", "portal2": "portail2", "halflife": "demivie", "left4dead": "left4mort", "left4dead2": "left4dead2", "valve": "valve", "portal": "portail", "teamfortress2": "teamfortress2", "everlastingsummer": "é<PERSON>é<PERSON>el", "goatsimulator": "simulateurdechèvres", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planètedelafreedom", "transformice": "transformice", "justshapesandbeats": "justeshapesetbeats", "battlefield4": "champdebataille4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "trancherviolent", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "risquedepluie2", "metroidvanias": "metroidvanias", "overcooked": "surcuite", "interplanetary": "interplanétaire", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fortressdesnains", "foxhole": "troudepoule", "stray": "errant", "battlefield": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "bataille1", "swtor": "swtorr", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "yeux", "blackdesert": "desertnoir", "tabletopsimulator": "simulateurdeplateaux", "partyhard": "fêteàfond", "hardspaceshipbreaker": "casseursdespacesdurs", "hades": "hades", "gunsmith": "armurier", "okami": "<PERSON>ami", "trappedwithjester": "coincésavecjester", "dinkum": "dinkum", "predecessor": "pré<PERSON><PERSON><PERSON>eur", "rainworld": "mondedepluie", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "simdelacolonie", "noita": "noita", "dawnofwar": "leviedelaguerre", "minionmasters": "maîtresminions", "grimdawn": "grimdawn", "darkanddarker": "sombreetplusobscur", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "travailleurdesâme", "datingsims": "simspourleflirt", "yaga": "yaga", "cubeescape": "cubeescape", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nouvelleville", "citiesskylines": "cielsdecités", "defconheavy": "defconlourd", "kenopsia": "kenopsie", "virtualkenopsia": "kenopsievirtuelle", "snowrunner": "snowrunner", "libraryofruina": "bibliothequedruina", "l4d2": "l4d2", "thenonarygames": "lesjeuxdenonary", "omegastrikers": "omegastrikers", "wayfinder": "explorateur", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "canardplastiquecalme", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialville", "smileforme": "sour<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "nuitducat", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "fatalité", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "terresfrontalières", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siège", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "j<PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "forcedéfenseterre", "huntshowdown": "chasselacompétition", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "rejoinsquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "tempêteinsurrectionnelle", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "cityhunter3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "mirrorsedge", "divisions2": "divisions2", "killzone": "zonedemort", "helghan": "hel<PERSON>", "coldwarzombies": "guerrefroidezombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "combatace", "crosscode": "codecroisé", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "guerremoderne", "neonabyss": "néonabîme", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "territoiresbizarres", "owerwatch": "overwatch", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnageprimal", "worldofwarships": "monded<PERSON>bateauxdeguerre", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "tueuràgages", "masseffect": "effe<PERSON><PERSON><PERSON>", "systemshock": "chocdusystème", "valkyriachronicles": "chroniquesdevalkyria", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cavestory", "doometernal": "doometernal", "centuryageofashes": "centurieagedescendres", "farcry4": "farcry4", "gearsofwar": "engrenagesdelaguerre", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tylety<PERSON>en", "generationzero": "générationzéro", "enterthegungeon": "entreledungeon", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "guerremoderne2", "blackops1": "blackops1", "sausageman": "homme<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetetchoc", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "visa<PERSON><PERSON><PERSON><PERSON>", "crossfire": "bataillecroisée", "atomicheart": "coeuratomique", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivants", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "libération", "battlegrounds": "zonesdebataille", "frag": "fragile", "tinytina": "<PERSON><PERSON>", "gamepubg": "jeupubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsoffreedom", "juegosfps": "jeuxfps", "convertstrike": "convertirfrappée", "warzone2": "zonedeguerre2", "shatterline": "shatterline", "blackopszombies": "zombiesnoirops", "bloodymess": "bord<PERSON><PERSON><PERSON>", "republiccommando": "républiquecommando", "elitedangerous": "élitedangereux", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "équipe", "destiny1": "destin1", "gamingfps": "jeuxfps", "redfall": "chuterouge", "pubggirl": "fillepubg", "worldoftanksblitz": "mondedetanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "inscrit", "farlight": "lumi<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "lesmerveillesdetinytina", "halo2": "halo2", "payday2": "jourdepaie2", "cs16": "cs16", "pubgindonesia": "pubgindonésie", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgroumanie", "empyrion": "empyrion", "pubgczech": "pubgcz", "titanfall2": "titanfall2", "soapcod": "savoncod", "ghostcod": "fantôcod", "csplay": "csjoue", "unrealtournament": "tournoireal", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "batteau", "callofdutymw2": "callofdutymw2", "quakechampions": "championsduquake", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "cellulardesplinters", "neonwhite": "neonblanc", "remnant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "retoural", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "microvolts", "reddead": "rougemort", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "champdebataille3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "merdetrésors", "rust": "rouille", "conqueronline": "conq<PERSON><PERSON><PERSON><PERSON>", "dauntless": "intré<PERSON>e", "warships": "navir<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "jourdesdragons", "warthunder": "guerrevolante", "flightrising": "éleversonvol", "recroom": "recroom", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myst<PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "sansfiancée", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "mondedecarros", "crossout": "barré", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "tourdefantaisie", "netplay": "jeuàdistance", "everquest": "quêteséternelles", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "rougedeadenligne", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "chevalierenligne", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "lebin<PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "vulgaire", "newworld": "nouveaumonde", "blackdesertonline": "blackdesertenligne", "multiplayer": "multijoueur", "pirate101": "pirate101", "honorofkings": "honneurdesrois", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmorpg", "pokemmo": "pokemmo", "ponytown": "ponyville", "3dchat": "chat3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cendresdecréation", "riotmmo": "riotenligne", "silkroad": "cheminsoie", "spiralknights": "chevaliersenspirale", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vengeful", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "dragonsprophete", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijoueur", "angelsonline": "angesenligne", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniversenligne", "growtopia": "growtopia", "starwarsoldrepublic": "starwarslarepubliquetemporelle", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "mondeparfait", "riseonline": "monteenligne", "corepunk": "corepunk", "adventurequestworlds": "quêtesaventure", "flyforfun": "volpourlefun", "animaljam": "animaljam", "kingdomofloathing": "royaumedelalâcherie", "cityofheroes": "villedes<PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "combattant<PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultime", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "ruesdeboo", "mkdeadlyalliance": "mkalliancefatale", "nomoreheroes": "<PERSON><PERSON>hé<PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "leroidescombats", "likeadragon": "commeundragon", "retrofightinggames": "jeuxdefightingrétro", "blasphemous": "blasphé<PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superfracas", "mugen": "mugen", "warofthemonsters": "guerredesmonstres", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberbots", "armoredwarriors": "guerriersblindés", "finalfight": "derniercombat", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "jeuxdecombat", "killerinstinct": "<PERSON><PERSON><PERSON>", "kingoffigthers": "roidescombattants", "ghostrunner": "fantôme_runner", "chivalry2": "chevalerie2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "suitehollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongjeu", "silksongnews": "nouvellesilksong", "silksong": "silksong", "undernight": "souslanuit", "typelumina": "typelum<PERSON>", "evolutiontournament": "tournoievolution", "evomoment": "évomoment", "lollipopchainsaw": "bâtondebourrelettes", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "histoiredelaberseria", "bloodborne": "sang<PERSON>urp<PERSON>", "horizon": "horizon", "pathofexile": "chemindelexil", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "inexploré", "horizonzerodawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "infâme", "playstationbuddies": "potesplaystation", "ps1": "ps1", "oddworld": "mondebizarre", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "entrepriseindisciplinée", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "grise", "trove": "trouvaille", "detroitbecomehuman": "detroitdevienthumain", "beatsaber": "beatssaber", "rimworld": "<PERSON><PERSON><PERSON>", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "jus<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "trophéedutouriste", "lspdfr": "lspdfr", "shadowofthecolossus": "ombreducolosse", "crashteamracing": "courseéquipesduchoc", "fivepd": "cinqpd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation", "samuraiwarriors": "guerrier<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "lederniergard<PERSON>", "soulblade": "âmesabre", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "chasse<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "derniergardien", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "animauxdefête", "warharmmer40k": "warhammer40k", "fightnightchampion": "combatdechampion", "psychonauts": "psychonautes", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "lescrollsanciensskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "lescrollsanciens", "gxbox": "gxbox", "battlefront": "frontdecombat", "dontstarvetogether": "necrivezpasensemble", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "étoileperdue", "xboxonex": "xboxonedanslaville", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "flipper<PERSON>ison", "americanmcgeesalice": "aliceaupaysdesmerveillesdeamericanmcgee", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxséries", "r6xbox": "r6xbox", "leagueofkingdoms": "lig<PERSON><PERSON>royaumes", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "télépoubelle", "skycotl": "skycotl", "erica": "erica", "ancestory": "anc<PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "petitemalchance", "sallyface": "bouchedesally", "franbow": "franbow", "monsterprom": "promm<PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "wild<PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultedellam", "duckgame": "canard<PERSON><PERSON>", "thestanleyparable": "les<PERSON><PERSON><PERSON><PERSON>", "towerunite": "tourunite", "occulto": "occulto", "longdrive": "longdrive", "satisfactory": "satisfaisant", "pluviophile": "pluviophile", "underearth": "souslaplanète", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "espritferry", "darkdome": "domeobs<PERSON>r", "pizzatower": "tourdepizza", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfons", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "jeu", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "ose", "scavengerhunt": "chasseauxtrésors", "yardgames": "j<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "choisistonombre", "trueorfalse": "<PERSON><PERSON><PERSON>aux", "beerpong": "beerpong", "dicegoblin": "goblinedesdés", "cosygames": "<PERSON><PERSON><PERSON><PERSON>", "datinggames": "jeuxdedating", "freegame": "jeugratuit", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "jeux", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "jeuxdesimulation", "wordgames": "jeuxdemots", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "jeuxdemots", "letsplayagame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "jeux<PERSON><PERSON><PERSON>", "oyun": "jeu", "interactivegames": "jeuxinteractifs", "amtgard": "amtgard", "staringcontests": "concoursderegard", "spiele": "joue", "giochi": "jeux", "geoguessr": "geoguessr", "iphonegames": "j<PERSON><PERSON><PERSON>", "boogames": "boojoueurs", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "cachercacher", "hopscotch": "<PERSON><PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "jeuclassique", "mindgames": "jeuxdemind", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "jeuromantique", "yanderegames": "<PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "virelangues", "4xgames": "4x<PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "jeuxdetable", "metroidvania": "metroidvania", "games90": "jeux90", "idareyou": "jevousteste", "mozaa": "mozaa", "fumitouedagames": "fumitoudagames", "racinggames": "jeuxdecourse", "ets2": "ets2", "realvsfake": "vraivsvfaux", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "jeuennettement", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "rôleécrit", "playaballgame": "jou<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "jeuxencoop", "jenga": "jenga", "wiigames": "wiigames", "highscore": "meilleurscore", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "j<PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcours", "tcgplayer": "tcgplayer", "juegodepreguntas": "jeudequestions", "gioco": "jeu", "managementgame": "jeudegestion", "hiddenobjectgame": "jeuxdobjetscachés", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "jeuf1", "citybuilder": "constructeurdecities", "drdriving": "drdriving", "juegosarcade": "jeuxarcade", "memorygames": "jeuxdemémoire", "vulkan": "vulkan", "actiongames": "jeuxdaction", "blowgames": "blowgames", "pinballmachines": "machineàboules", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "canapcoop", "perguntados": "questionnés", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "jeuximessage", "idlegames": "jeuxin<PERSON>s", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "jeuxlogiques", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jeuxdecelebrités", "exitgames": "jeuxdecompression", "5vs5": "5vs5", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "jeuetcarnage", "traditionalgames": "jeuxtraditionnels", "kniffel": "yams", "gamefps": "jeuf<PERSON>", "textbasedgames": "jeux<PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retro<PERSON><PERSON>", "thiefgame": "jeuxde<PERSON><PERSON><PERSON>", "lawngames": "j<PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "babyfoot", "tischfußball": "babyfoot", "spieleabende": "soiréeb104", "jeuxforum": "forumdesjeux", "casualgames": "jeuxdécontractés", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "jeuxdescape", "thiefgameseries": "jeuxde<PERSON><PERSON><PERSON>", "cranegames": "j<PERSON><PERSON><PERSON><PERSON>", "játék": "jeux", "bordfodbold": "bordfodbold", "jogosorte": "jeuxdelapotagerie", "mage": "mage", "cargames": "jeuxdecars", "onlineplay": "jeuenligne", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "randomiseur", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "jeuxpc", "socialdeductiongames": "jeuxdedéductionsociale", "dominos": "dominos", "domino": "domino", "isometricgames": "jeuxisométriques", "goodoldgames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "véracitéetchallenge", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "chassesauxtrésors", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "joueurf2p", "free2play": "gratuit<PERSON>ur<PERSON><PERSON>", "fantasygame": "jeufantastique", "gryonline": "gryonline", "driftgame": "driftgame", "gamesotomes": "j<PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotv<PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "oasisdeschampignons", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "épéeetchansons", "goodgamegiving": "bonjeug<PERSON>", "jugamos": "onjoue", "lab8games": "lab<PERSON><PERSON><PERSON>", "labzerogames": "labzerogames", "grykomputerowe": "j<PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "jouons", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "jeuxminiatures", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "mod<PERSON><PERSON><PERSON>", "crimegames": "jeuxdécrime", "dobbelspellen": "dobbelspellen", "spelletjes": "jeux", "spacenerf": "spacenerf", "charades": "charades", "singleplayer": "jou<PERSON><PERSON><PERSON>", "coopgame": "jeucoop", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "échecs", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "jeudesdame", "onitama": "onitama", "pandemiclegacy": "héritagedelapandémie", "camelup": "dromadaireàfond", "monopolygame": "monopolygame", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "jeuxdebord", "boardgame": "jeudesociété", "sällskapspel": "jeux<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "jeuxdestable", "risiko": "risque", "permainanpapan": "jeuxdesociete", "zombicide": "zombicide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "bowldesang", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardgame", "connectfour": "quatreconnectés", "heroquest": "heroquest", "giochidatavolo": "j<PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "jeuxdetable", "dicegames": "jeux<PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "j<PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "protocolecrisemarvel", "cosmicencounter": "rencontrecosmique", "creationludique": "créationludique", "tabletoproleplay": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "jeu<PERSON><PERSON><PERSON>", "infinitythegame": "infinitylejeu", "kingdomdeath": "r<PERSON><PERSON>delamort", "yahtzee": "yahtzee", "chutesandladders": "chutesetladders", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "jeudetable", "planszówki": "j<PERSON><PERSON><PERSON><PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "ennuie", "applestoapples": "pom<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudesociété", "gameboard": "plateaudejeu", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jeuxdesociétés", "twilightimperium": "twilightimperium", "horseopoly": "chevalopoly", "deckbuilding": "constructiondeck", "mansionsofmadness": "manoirsdelabièvre", "gomoku": "gomoku", "giochidatavola": "jeuxdetable", "shadowsofbrimstone": "ombresdebrimstone", "kingoftokyo": "kingdumontreal", "warcaby": "dames", "táblajátékok": "jeuxdebo<PERSON>s", "battleship": "battleship", "tickettoride": "ticketpourmontrer", "deskovehry": "boobureau", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "jeux<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxdesociete", "gesellschaftsspiele": "jeux<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "legiondesétoiles", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "j<PERSON><PERSON><PERSON><PERSON>", "terraria": "terarria", "dsmp": "dsmp", "warzone": "zonedeguerre", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "identitév", "theisle": "lile", "thelastofus": "ledernierdesnotres", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "chassedeprimordial", "callofcthulhu": "appelducthulhu", "bendyandtheinkmachine": "bendyetlamachineàencre", "conanexiles": "conanexiles", "eft": "fint", "amongus": "parminous", "eco": "éco", "monkeyisland": "îledesinges", "valheim": "valheim", "planetcrafter": "planèteboo", "daysgone": "jourspassés", "fobia": "fobia", "witchit": "sorcellerie", "pathologic": "pathologique", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7jtd", "thelongdark": "lelongdark", "ark": "arche", "grounded": "anc<PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "pèredemalade", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "retoureternel", "pathoftitans": "chemindestitans", "frictionalgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "vrairealc", "thebackrooms": "les<PERSON><PERSON>", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "blockstory", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "lueurdecader", "thewalkingdeadgame": "lejeudesmortsvivants", "wehappyfew": "onestbeaucoupmoins", "riseofempires": "lascensiondesempires", "stateofsurvivalgame": "étatdesurvivalgame", "vintagestory": "histoiredemode", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "survivewestlend", "beastsofbermuda": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "boisnoir", "survivalhorror": "horreur<PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "trainvide", "lifeaftergame": "laviedap<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "j<PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "cettegue<PERSON><PERSON>s", "scpfoundation": "fondationscp", "greenproject": "projetvert", "kuon": "kuon", "cryoffear": "pleure<PERSON><PERSON><PERSON><PERSON>", "raft": "r<PERSON><PERSON>", "rdo": "rdo", "greenhell": "enfervert", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironaute", "granny": "mamie", "littlenightmares2": "petitscauchemar2", "signalis": "signalis", "amandatheadventurer": "amandalevadrouilleuse", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rust<PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "survivreauxessais", "alienisolation": "isolementalien", "undawn": "undawn", "7day2die": "7jours2mourir", "sunlesssea": "mersanssoleil", "sopravvivenza": "survie", "propnight": "propnuit", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "mortverse", "cataclysmdarkdays": "catalysmejoursobscurs", "soma": "soma", "fearandhunger": "peuretchance", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "époquedelobscurité", "clocktower3": "cloître3", "aloneinthedark": "seuldansladark", "medievaldynasty": "dynastiemédiévale", "projectnimbusgame": "projetnimbusgame", "eternights": "éternights", "craftopia": "craftopia", "theoutlasttrials": "lesessaisdoutlast", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "tuerdesnains", "warhammer40kcrush": "coupdecoeurwarhammer40k", "wh40": "wh40", "warhammer40klove": "amourwarhammer40k", "warhammer40klore": "lorewarhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindik<PERSON>", "ilovesororitas": "jadorelessororitas", "ilovevindicare": "jadorev<PERSON><PERSON><PERSON>", "iloveassasinorum": "jador<PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templa<PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "âgedesempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerlagedesigmar", "civilizationv": "civilisationv", "ittakestwo": "iltafaitdeux", "wingspan": "envergure", "terraformingmars": "terraformagemars", "heroesofmightandmagic": "herosdepouvoirsetmagie", "btd6": "btd6", "supremecommander": "commandants<PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "époquedemythologie", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "aventure2", "banished": "banni", "caesar3": "caesar3", "redalert": "alerterouge", "civilization6": "civilisation6", "warcraft2": "warcraft2", "commandandconquer": "commandetuconquiers", "warcraft3": "warcraft3", "eternalwar": "guerreeternelle", "strategygames": "jeuxdestratégie", "anno2070": "anno2070", "civilizationgame": "jeudecivilisation", "civilization4": "civilisation4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "guerretotale", "travian": "travian", "forts": "forts", "goodcompany": "bonnecompagnie", "civ": "civ", "homeworld": "mondedeboo", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "pour<PERSON><PERSON>", "realtimestrategy": "stratégieenrealtime", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "roisdeuxcouronnes", "eu4": "eu4", "vainglory": "vanité", "ww40k": "ww40k", "godhood": "divinité", "anno": "ann<PERSON>", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "coursdalgebredave", "plagueinc": "plagueinc", "theorycraft": "théorisation", "mesbg": "mesbg", "civilization3": "civilisation3", "4inarow": "4àlasuite", "crusaderkings3": "croisadesrois3", "heroes3": "héros3", "advancewars": "advancewars", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "jeuxdestratégie", "stratejioyunları": "strate<PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "âgedesmerveilles", "dinosaurking": "roidesdinosaures", "worldconquest": "conquêtemondiale", "heartsofiron4": "heartsofiron4", "companyofheroes": "compagni<PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "batail<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeempire", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "oieoiescanard", "phobies": "phobies", "phobiesgame": "jeuxph<PERSON><PERSON>", "gamingclashroyale": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "tourpartour", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilisation5", "victoria2": "victoria2", "crusaderkings": "roiscrusaders", "cultris2": "cultris2", "spellcraft": "artdelamagie", "starwarsempireatwar": "starwarsemepireenlutte", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stratégie", "popfulmail": "popfulmail", "shiningforce": "forceéclatante", "masterduel": "<PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "tycoontransport", "unrailed": "<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planedescapetorture", "uplandkingdoms": "royaumesupland", "galaxylife": "viegalaxie", "wolvesvilleonline": "wolvesvilleenligne", "slaythespire": "slaylascierge", "battlecats": "battleschats", "sims3": "sims3", "sims4": "sims4", "thesims4": "lesims4", "thesims": "lesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "besoindevitesse", "needforspeedcarbon": "besoindepourvites<PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsjeugratuit", "ts4": "ts4", "thesims2": "lesims2", "thesims3": "lesims3", "thesims1": "lesims1", "lossims4": "perdresims4", "fnaf": "fnaf", "outlast": "survivre", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicemadnessrevient", "darkhorseanthology": "anthologiedehorseobscur", "phasmophobia": "phasmo<PERSON><PERSON><PERSON>", "fivenightsatfreddys": "cinqnuitschezfreddy", "saiko": "saiko", "fatalframe": "cadrefatal", "littlenightmares": "petitscauchemars", "deadrising": "montéede<PERSON>rts", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "restéàlamaison", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "petitemissmalchance", "projectzero": "projetzero", "horory": "horory", "jogosterror": "jogosterror", "helloneighbor": "salutvoisin", "helloneighbor2": "helloneighbor2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON>", "horrorgaming": "horrogaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartespourlhumanité", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenames", "dixit": "dixit", "bicyclecards": "cartesàbicyclette", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "légendederunetera", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "forgeclé", "cardtricks": "tourdecartes", "playingcards": "cartesàjouer", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartesdecommercant", "pokemoncards": "cartespokemon", "fleshandbloodtcg": "chairete<PERSON><PERSON><PERSON>", "sportscards": "cartesdesport", "cardfightvanguard": "batailledecartesvanguard", "duellinks": "duellinks", "spades": "pelles", "warcry": "<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "tokendanslevent", "kingofhearts": "roidescœurs", "truco": "truc", "loteria": "loterie", "hanafuda": "hana<PERSON>da", "theresistance": "laresistance", "transformerstcg": "transformerscartes", "doppelkopf": "doppelkopf", "yugiohcards": "cartesyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "magiciennoir", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON>usdragonblanc", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "jugemtg", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "chassedeplansmtg", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "jeukartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteenligne", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "sagadelespritde<PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "jolies", "facecard": "cartevisage", "cardfight": "batailledecartes", "biriba": "biriba", "deckbuilders": "constructeursdedeck", "marvelchampions": "marvelchampions", "magiccartas": "cartesmagiques", "yugiohmasterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "licornesinstables", "cyberse": "cyberse", "classicarcadegames": "jeuxdarcadenostalgique", "osu": "osu", "gitadora": "gitadora", "dancegames": "jeuxdedanse", "fridaynightfunkin": "vend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projetmirai", "projectdiva": "pro<PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rocklesmorts", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dansecentrale", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "j<PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "cielrythmique", "hypmic": "hypmic", "adanceoffireandice": "unebalancedela<PERSON>etdelaglace", "auditiononline": "audition<PERSON>ligne", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptodanslegrave", "rhythmdoctor": "rheumedoc", "cubing": "cubing", "wordle": "motle", "teniz": "teniz", "puzzlegames": "jeuxdepuzzle", "spotit": "spotit", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "cassetêteslogiques", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "cuberubik", "crossword": "motscroisés", "motscroisés": "crossthesis", "krzyżówki": "motscroisés", "nonogram": "nonogramme", "bookworm": "ratdelivre", "jigsawpuzzles": "puzzlesémentales", "indovinello": "de<PERSON>ette", "riddle": "énigme", "riddles": "énigmes", "rompecabezas": "casset<PERSON>te", "tekateki": "tekateki", "inside": "dedans", "angrybirds": "oiseauxenragés", "escapesimulator": "simulateurdescapes", "minesweeper": "d<PERSON><PERSON>ur", "puzzleanddragons": "puzzleetdragons", "crosswordpuzzles": "motscroisés", "kurushi": "k<PERSON>hi", "gardenscapesgame": "jeuxdeparcs", "puzzlesport": "puzzlesport", "escaperoomgames": "jeuxdesallederéveil", "escapegame": "<PERSON><PERSON><PERSON>", "3dpuzzle": "puzzle3d", "homescapesgame": "jeuxhomescapes", "wordsearch": "cher<PERSON><PERSON><PERSON>", "enigmistica": "énigmistique", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "histoiresdecharades", "fishdom": "poissondom", "theimpossiblequiz": "lequizzesimpossible", "candycrush": "candycrush", "littlebigplanet": "petit<PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kwerky", "rubikcube": "cuberubik", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "lep<PERSON><PERSON><PERSON><PERSON>", "homescapes": "maisonspectaculaires", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "jeuxdentrepreneurs", "cubosderubik": "cubosderubik", "cruciverba": "crosstexte", "ciphers": "chiffres", "rätselwörter": "motségnimes", "buscaminas": "minesdebonbons", "puzzlesolving": "résolutiondepetitscassetêtes", "turnipboy": "turnipboy", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "personnesinvisibles", "guessing": "deviner", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "motscroiséscryptiques", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "chassesauxtreasure", "catcrime": "crimefeline", "quebracabeça": "casset<PERSON>te", "hlavolamy": "casset<PERSON>te", "poptropica": "poptropica", "thelastcampfire": "ledernierfeudecamp", "autodefinidos": "autodéfinis", "picopark": "picopark", "wandersong": "chansondanslaville", "carto": "carto", "untitledgoosegame": "jeudebougedanslecanard", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrinthe", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "morceaux", "portalgame": "jeuxportail", "bilmece": "bilmece", "puzzelen": "puzzeler", "picross": "picross", "rubixcube": "cuberubik", "indovinelli": "devinettes", "cubomagico": "cubomagique", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "wonderlandtordu", "monopoly": "monopole", "futurefight": "combatdelavenir", "mobilelegends": "legendesmobiles", "brawlstars": "brawlstars", "brawlstar": "brawlstarfr", "coc": "coc", "lonewolf": "loupis<PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "étoilesensemble", "asphalt9": "asphalte9", "mlb": "mlb", "cookierunkingdom": "royaumedescookies", "alchemystars": "alchemystars", "stateofsurvival": "étatdesurvie", "mycity": "mamville", "arknights": "arknights", "colorfulstage": "scènecolorée", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "coursechevaleresque", "fireemblemheroes": "feheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "batailledesoc<PERSON>", "a3": "a3", "phonegames": "jeuxsurtelephone", "kingschoice": "choi<PERSON><PERSON><PERSON>", "guardiantales": "histoiresdegardien", "petrolhead": "passionnédevoiture", "tacticool": "tacticool", "cookierun": "cookiecourse", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "pasdansleflux", "craftsman": "artisans", "supersus": "supersus", "slowdrive": "doucementenroute", "headsup": "attention", "wordfeud": "motfeud", "bedwars": "guerredelalits", "freefire": "freefire", "mobilegaming": "jeuxmobiles", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "messagermystique", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "urgencehq", "enstars": "enstars", "randonautica": "randonautique", "maplestory": "maplestory", "albion": "albion", "hayday": "fêtedesfoins", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "shakesetfidget", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashdesclans", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "lé<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "amourdepoches", "androidgames": "j<PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "guerresdesommateurs", "cookingmadness": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leaguedesanges", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "nuageneural", "mysingingmonsters": "mesmonstreschantants", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "robotsdejouissance", "mirrorverse": "miroi<PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futime", "antiyoy": "antiboo", "apexlegendmobile": "apexlegendmobile", "ingress": "entrée", "slugitout": "slugitedur", "mpl": "mpl", "coinmaster": "maî<PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "potesdecompagnie", "gameofsultans": "jeudessultans", "arenabreakout": "arenabreakout", "wolfy": "loupyp", "runcitygame": "jeuruncity", "juegodemovil": "jeuvidemobile", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandechasse", "bombmebrasil": "bombemebresil", "ldoe": "ldoe", "legendonline": "légendeenligne", "otomegame": "otomeg<PERSON>", "mindustry": "mentalité", "callofdragons": "appeldesdragons", "shiningnikki": "nikkidéglingue", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "cheminversnullepart", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "motsavecsociaux2", "soulknight": "cheval<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "histoiredépurrfection", "showbyrock": "showbyrock", "ladypopular": "ladypopulaire", "lolmobile": "lolmobile", "harvesttown": "villederecolte", "perfectworldmobile": "mondeparfaitmobile", "empiresandpuzzles": "empiresetpuzzles", "empirespuzzles": "empirespuzzles", "dragoncity": "villedesdragons", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "petitcauche<PERSON><PERSON>", "aethergazer": "visualisateurdaether", "mudrunner": "mudrunner", "tearsofthemis": "larmes<PERSON><PERSON><PERSON>", "eversoul": "éversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknights", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "échosdêve", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "fillestactiques", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "histoire<PERSON>lune", "carxdriftracingonline": "carxdriftracingenligne", "jogosmobile": "jeuxmobiles", "legendofneverland": "légendedeneverland", "pubglite": "pubglite", "gamemobilelegends": "jeuxmobilelegends", "timeraiders": "chasseursdet<PERSON>ps", "gamingmobile": "jeuxmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "leschatsdecombat", "dnd": "dnd", "quest": "qu<PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "mondedelobscur", "travellerttrpg": "voyageurttrpg", "2300ad": "2300ap", "larp": "larp", "romanceclub": "clubromance", "d20": "d20", "pokemongames": "j<PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokémonmysterydungeon", "pokemonlegendsarceus": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "animepokemon", "pokémongo": "pokémongo", "pokemonred": "pokemonrouge", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "papote", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "<PERSON><PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monstresdepoches", "nuzlocke": "nuzlocke", "pokemonplush": "peluchesp<PERSON><PERSON>", "teamystic": "teammystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "rom<PERSON><PERSON><PERSON><PERSON>", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pok<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "mainsferroces", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "ombre<PERSON>", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokémondormir", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "enfant<PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulb<PERSON><PERSON>", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON>ass<PERSON><PERSON><PERSON><PERSON>", "ajedrez": "échecs", "catur": "<PERSON>ur", "xadrez": "échecs", "scacchi": "échecs", "schaken": "schaken", "skak": "skak", "ajedres": "échecs", "chessgirls": "filleauxéchecs", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "mondeblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "échecsjaponais", "chinesechess": "échecschinois", "chesscanada": "echecscanada", "fide": "<PERSON><PERSON><PERSON><PERSON>", "xadrezverbal": "échiquierverbal", "openings": "ouvertures", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabozosetdragons", "dungeonsanddragon": "donjonsetdragons", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "soleilenoire", "thelegendofvoxmachina": "<PERSON>legendedevocma<PERSON>", "doungenoanddragons": "doungenoetdragons", "darkmoor": "darkmoor", "minecraftchampionship": "championnatminecraft", "minecrafthive": "hivedeminecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "rêved<PERSON>mp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsdeminecraft", "mcc": "mcc", "candleflame": "flamedecierge", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpoche", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmoddé", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "entreterres", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "villeminecraft", "pcgamer": "joueurpc", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "gamers", "levelup": "levelup", "gamermobile": "gamermobile", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "jeuxpc", "gamen": "gamen", "oyunoynamak": "jou<PERSON><PERSON><PERSON><PERSON>", "pcgames": "jeuxpc", "casualgaming": "jeuxdécontractés", "gamingsetup": "setupgaming", "pcmasterrace": "ma<PERSON><PERSON>delap<PERSON>", "pcgame": "jeupc", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerfr", "gameplays": "gameplays", "consoleplayer": "jou<PERSON><PERSON><PERSON>e", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "semigamer", "gamergirls": "gamerfille", "gamermoms": "mamansgamer", "gamerguy": "gamerducoin", "gamewatcher": "observateur<PERSON><PERSON><PERSON>", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerfées", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "équipesérieuse", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "qu<PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "vieuxgamer", "cozygaming": "gamingconfort", "gamelpay": "gamelpay", "juegosdepc": "jeuxdepê<PERSON>", "dsswitch": "dsswitch", "competitivegaming": "gamingcompétitif", "minecraftnewjersey": "minecraftnouveaujersey", "faker": "faux", "pc4gamers": "pc4gamers", "gamingff": "gamingpp", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "jeupc", "girlsgamer": "girlsgamer", "fnfmods": "fnfmods", "dailyquest": "quêtequotidienne", "gamegirl": "gamegirl", "chicasgamer": "chicasgamer", "gamesetup": "<PERSON><PERSON><PERSON><PERSON>", "overpowered": "tropfort", "socialgamer": "gamerocial", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "joueurdepersonnage", "myteam": "monéquipe", "republicofgamers": "republiquedesgamers", "aorus": "aorus", "cougargaming": "coucougaming", "triplelegend": "triplelégende", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "besoindefillesgamers", "christiangamer": "gamerechretien", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "absent", "andregamer": "andregamer", "casualgamer": "gamerdécontracté", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "geekos", "oyunizlemek": "j<PERSON><PERSON><PERSON>", "gamertag": "pseudo", "lanparty": "partydejeux", "videogamer": "jou<PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "jouonsensemble", "mortdog": "mortchien", "playstationgamer": "gamerplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamerensant", "gtracing": "gtracing", "notebookgamer": "carnet<PERSON><PERSON><PERSON>", "protogen": "protogène", "womangamer": "femmejoueuse", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "cueilleur", "humanfallflat": "hum<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musicdenintendo", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonique", "fallguys": "fallguys", "switch": "switcher", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splaatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "masquemajora", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "avocatace", "ssbm": "ssbm", "skychildrenofthelight": "enfant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simulateurdepromenade", "nintendogames": "j<PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "lunederécolte", "mariobros": "mario<PERSON>s", "runefactory": "fermemagique", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "céleste", "breathofthewild": "souffledelaventure", "myfriendpedro": "monpotepedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON><PERSON>", "earthbound": "terreattachée", "tales": "histoires", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "stratégietriangle", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "chataignesmauvaispoil", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerriersdhyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "yu<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "<PERSON><PERSON><PERSON><PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "canidrouge", "vanillalol": "vanillalol", "wildriftph": "wildriftfr", "lolph": "lolph", "leagueoflegend": "leagueoflegendes", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "pubportée", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespagne", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "liéesauxlégendes", "gaminglol": "gamingmdr", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexportes", "hextech": "hextech", "fortnitegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitefr", "retrovideogames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "jeu<PERSON><PERSON><PERSON>", "videosgame": "j<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "profe<PERSON><PERSON><PERSON><PERSON>", "overwatch": "surveillancetop", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "sorcier101", "battleblocktheater": "battleblocktheater", "arcades": "arcades", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulateurfarm", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxfrance", "robloxdeutsch": "robloxfr", "erlc": "erlc", "sanboxgames": "sanboxgames", "videogamelore": "loredujeuvideo", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "universderêve", "starcitizen": "starcitizen", "yanderesimulator": "simulate<PERSON><PERSON><PERSON>", "grandtheftauto": "gta", "deadspace": "espacemort", "amordoce": "amordoce", "videogiochi": "j<PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "lancienrépublique", "videospiele": "j<PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "rêvecast", "adventuregames": "jeuxdaventure", "wolfenstein": "wolfenstein", "actionadventure": "actionaventure", "storyofseasons": "histoiredessaisons", "retrogames": "retrogaming", "retroarcade": "arcaderetro", "vintagecomputing": "informatiquevintage", "retrogaming": "rétrogaming", "vintagegaming": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "rendezvous<PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "<PERSON><PERSON>", "injustice2": "injustice2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "jeuxciel", "zenlife": "viezen", "beatmaniaiidx": "beatmaniaiidx", "steep": "raide", "mystgames": "<PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "gamingblockchain", "medievil": "medieval", "consolegaming": "gamingconsole", "konsolen": "konsolen", "outrun": "dépassement", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horreurgaming", "monstergirlquest": "quê<PERSON>fillemon<PERSON><PERSON>", "supergiant": "supergéant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simulationsagricoles", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "fictioninteractive", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "ledernierdesnous2", "amantesamentes": "amantesamentes", "visualnovel": "novelvisuel", "visualnovels": "romansvisuels", "rgg": "rgg", "shadowolf": "ombre<PERSON>", "tcrghost": "tcrghost", "payday": "jourde<PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "princess<PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "bacàsable", "aestheticgames": "jeuxesthétiques", "novelavisual": "romanavisuel", "thecrew2": "léquipe2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogaming", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON>", "leafblowerrevolution": "révolutiondessouffleuses", "wiiu": "wiiu", "leveldesign": "designniveau", "starrail": "starrail", "keyblade": "clef<PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsouvent", "novelasvisuales": "novelasservisuales", "robloxbrasil": "robloxfrance", "pacman": "pacman", "gameretro": "gamerétro", "videojuejos": "vidéojouets", "videogamedates": "rendezvous<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "monamourdebonbon", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "retourdujugement", "gamstergaming": "gamstergaming", "dayofthetantacle": "jourdutentacule", "maniacmansion": "maniacmansion", "crashracing": "coursedecrazy", "3dplatformers": "plateformes3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "jeuxvidéosoldschool", "hellblade": "boohellblade", "storygames": "jeux<PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "sondodger", "beyondtwosouls": "audelàdesdeuxâmes", "gameuse": "gameuse", "offmortisghost": "mortdégoût", "tinybunny": "petitlapin", "retroarch": "retroarch", "powerup": "boostezvous", "katanazero": "katanazero", "famicom": "famillecom", "aventurasgraficas": "aventuresgraphiques", "quickflash": "flashrapide", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "arcadesrétro", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON>", "powerwashsim": "simulateurdenettoyage", "coralisland": "îlecoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "unautremonde", "metaquest": "metaquest", "animewarrios2": "animeguerriers2", "footballfusion": "<PERSON>foot", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalpervers", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "pileàshame", "simulator": "simulateur", "symulatory": "symulatory", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sametmax", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "paysdesmerveillesenligne", "skylander": "skylander", "boyfrienddungeon": "donjonducopain", "toontownrewritten": "toontownréécrit", "simracing": "simracing", "simrace": "simcourse", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "corpscélestes", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "simulateurdeflottespaciales", "legacyofkain": "lhéritagedekain", "hackandslash": "frappeettranche", "foodandvideogames": "nourriture<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "vid<PERSON>osdejeu", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulateurdetraction", "horizonworlds": "mondesdhorizon", "handygame": "jeuxpaschers", "leyendasyvideojuegos": "légendesetjeuxvidéo", "oldschoolvideogames": "jeuxvidéoàlancienne", "racingsimulator": "simulateurracing", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gatesolympe", "monsterhunternow": "chass<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "étoilerebelle", "indievideogaming": "indievideogaming", "indiegaming": "indiegaming", "indievideogames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "imbattable", "projectl": "projetl", "futureclubgames": "jeuxclubavenir", "mugman": "mugman", "insomniacgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "scienceaperture", "backlog": "retard", "gamebacklog": "backlog<PERSON><PERSON><PERSON>", "gamingbacklog": "carnetdegames", "personnagejeuxvidéos": "persojoueurs", "achievementhunter": "chasseurdeperformances", "cityskylines": "silhouette<PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "chien<PERSON><PERSON>", "beastlord": "seigneur<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "j<PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "orietchampforetaveugle", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoirededopamine", "staxel": "staxel", "videogameost": "ostjeuxvideo", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "jetaimekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "coursepcr", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesad", "darkerthanblack": "plusnoirquelecharbon", "animescaling": "animescaling", "animewithplot": "animeavectouteuneintrigue", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "seigneurdesténèbres", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "saisondrstone1", "rapanime": "rapanime", "chargemanken": "chargementmec", "animecover": "animecouverture", "thevisionofescaflowne": "lavisiondescaflowne", "slayers": "tueurs", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "poisson<PERSON>ne", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanité", "fireforce": "forceenfeu", "moriartythepatriot": "moriartylepatriote", "futurediary": "journaldufutur", "fairytail": "contesdefées", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "b<PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitassanscarte", "mermaidmelody": "melodiemérmaid", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON>", "romancemangas": "romanceman<PERSON>", "karneval": "carnaval", "dragonmaid": "dragondame", "blacklagoon": "lagoonnoire", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "chienstraybungo", "jujustukaisen": "juju<PERSON><PERSON><PERSON>n", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "uncertainmagicalindex", "sao": "sao", "blackclover": "trèflenoir", "tokyoghoul": "tokyoghoul", "onepunchman": "unemanchotue", "hetalia": "hetalia", "kagerouproject": "projetkagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8linfini", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prior<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsofdeath": "angesdelamort", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeetsport", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagadebootanyalevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "jolieg<PERSON><PERSON><PERSON>", "theboyandthebeast": "le<PERSON><PERSON><PERSON>bê<PERSON>", "fistofthenorthstar": "p<PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbutler", "towerofgod": "<PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "plein<PERSON><PERSON><PERSON>usagas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "mignoneteterrifiant", "martialpeak": "sommetmartial", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "superscorefille", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revenantfae", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "fillemon<PERSON>re", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amitié", "sailorsaturn": "marin<PERSON><PERSON><PERSON>e", "dio": "dio", "sailorpluto": "marinpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animesdantan", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animehorreur", "fruitsbasket": "panierdefruits", "devilmancrybaby": "devil<PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangad<PERSON><PERSON>re", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "vivelamour", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "votresouffranceenavril", "buggytheclown": "buggyleclown", "bokunohero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "se<PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magie", "deepseaprisoner": "prisonnierdesprofondeurs", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "coeursdepandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerresdelalimentation", "cardcaptorsakura": "sakurachasseusedecartes", "stolas": "stolas", "devilsline": "lign<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "toutevotreesternité", "infpanime": "infpanime", "eleceed": "élève", "akamegakill": "akamegakill", "blueperiod": "pério<PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "alliancecachée", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "<PERSON><PERSON><PERSON><PERSON>", "yuki": "yuki", "erased": "effac<PERSON>", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "chevalierdelavampire", "mugi": "mugi", "blueexorcist": "exorcistenoble", "slamdunk": "coupdepoing", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "famille<PERSON><PERSON>", "airgear": "airgear", "magicalgirl": "fillemagique", "thesevendeadlysins": "lesseptpéchéscapitaux", "prisonschool": "écoledeprison", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandbleu", "mydressupdarling": "machérieenmode", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universanime", "swordartonlineabridge": "swordart<PERSON>lineabrid<PERSON>", "saoabridged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "premierpas", "undeadunluck": "mortviventmalchance", "romancemanga": "mangadamour", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromance", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentine", "lolicon": "lo<PERSON>on", "demonslayertothesword": "démonslayeràlasword", "bloodlad": "bloodlad", "goodbyeeri": "auvo<PERSON><PERSON>", "firepunch": "coupdeflamme", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "lesétoilesseplacent", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cherrymagic": "magie<PERSON><PERSON>ise", "housekinokuni": "maison<PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "lycéalmort", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "<PERSON><PERSON><PERSON><PERSON>", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "classeassassin", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paradedelamort", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponais", "animespace": "animespace", "girlsundpanzer": "fillesundpanzer", "akb0048": "akb0048", "hopeanuoli": "espoironbosse", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "filledepeche", "cavalieridellozodiaco": "cavalierisdelozodiaque", "mechamusume": "mecahéroines", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "co<PERSON><PERSON>man<PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "registrederagnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutopo<PERSON><PERSON>", "overgeared": "overéquipé", "toriko": "<PERSON><PERSON>o", "ravemaster": "ma<PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sang<PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "lesmanguescestlavie", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "leschevaliersduzodia", "animeshojo": "animeshojo", "reverseharem": "reverseharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "grandsenseionizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldat", "mybossdaddy": "monpatronpapa", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON>", "bloodplus": "sanguinplus", "bloodplusanime": "<PERSON><PERSON><PERSON><PERSON>", "bloodcanime": "sanganime", "bloodc": "sanguin", "talesofdemonsandgods": "histoiresdedémonsetdieux", "goreanime": "animegore", "animegirls": "filleanime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxlespires", "splatteranime": "splattersanime", "splatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "animesomalie", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespagne", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "enfantsdesbaleines", "liarliar": "menteurmantenons", "supercampeones": "superchampions", "animeidols": "idolesanime", "isekaiwasmartphone": "isekaiavaitunsmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "fillesmagiques", "callofthenight": "appeldelanuit", "bakuganbrawler": "brawlerdebakugan", "bakuganbrawlers": "brawlersdebakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronique", "findermanga": "trouvetonmanga", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuedelastarlight", "animeverse": "animeunivers", "persocoms": "persocoms", "omniscientreadersview": "vuelectoratomniscient", "animecat": "chatanime", "animerecommendations": "recommandationsanimées", "openinganime": "ouvert<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "monromanticomédiedado", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "grandsrobots", "neongenesisevangelion": "neongenesisévangélion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mec", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "lebigoanime", "bleach": "décolorant", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "lesaventuresbizarresdejojo", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "glace", "jojobizarreadventures": "aventuresbizarresdejojo", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitaire", "greenranger": "rangervert", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "villeanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonaventure", "hxh": "hxh", "highschooldxd": "lycéedxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "monheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "démonslayers", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attaquedestitans", "erenyeager": "<PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "monheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corpsdesenquête", "onepieceanime": "animeonepiece", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "letone<PERSON><PERSON><PERSON><PERSON>", "revengers": "vengeurs", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoeexorciste", "joyboyeffect": "effet<PERSON><PERSON>", "digimonstory": "digimonhistoire", "digimontamers": "digimontamers", "superjail": "superprison", "metalocalypse": "métalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uram<PERSON>chioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "notreclubdeshôtes", "flawlesswebtoon": "webtoonparfait", "kemonofriends": "kemon<PERSON><PERSON>", "utanoprincesama": "pasdep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "campingminimaliste", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "sorcièrevolante", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "justeparceque", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "comptepetitsmoments"}