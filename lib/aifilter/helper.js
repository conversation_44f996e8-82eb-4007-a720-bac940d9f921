/* eslint-disable indent */
const replicateClient = require('../replicate-client');
const lambdaClient = require('../lambda');
const { getEmbedConfig } = require('../constants');
const { get_encoding } = require('tiktoken');

const client = replicateClient.getReplicateApiClient();
const config = getEmbedConfig();
const LAMBDA_FUNCTION_NAME = 'merge_embeddings';
const ENCODING = get_encoding('gpt2');

const generateEmbedding = async (url, text) => {
  if (!url && !text) {
    console.log('Url or text must be provided to generate embedding.');
    return null;
  }

  const timeout = 60000;

  const embeddingPromise = (async () => {
    try {
      const input = text ? { text } : { image: url };
      const data = config.version
        ? {
            path: 'predictions',
            payload: {
              version: config.version,
              input,
            },
          }
        : {
            path: config.deployment,
            payload: { input },
          };

      // For AIFilter text embeddings, use synchronous prediction
      if (text) {
        const response = await client.createSyncPrediction(data);
        if (!response?.output?.embedding) {
          console.log('Failed to create embedding. No output embedding returned.');
          return null;
        }
        return response.output.embedding;
      }

      const { id } = await client.createClipPrediction(data);
      if (!id) {
        console.log('Failed to create embedding. No prediction id returned.');
        return null;
      }

      const result = await client.getPrediction(id, 2000);
      if (!result?.output?.embedding) {
        return null;
      }

      return result.output.embedding;
    } catch (error) {
      console.log('Error generating embedding:', error.message);
      return null;
    }
  })();

  if (!text) {
    return embeddingPromise;
  }

  // If request is for AIFilter generation should be timed out after 60 seconds
  const timeoutPromise = new Promise((_, reject) =>
    setTimeout(() => reject(new Error('Failed to generate embedding within 60 seconds')), timeout)
  );

  return Promise.race([embeddingPromise, timeoutPromise])
    .catch(error => {
      console.log(error.message);
      return `ERROR_TIMEOUT`;
    });
};

const mergeEmbeddings = async (embeddings) => {
  if (!Array.isArray(embeddings) || embeddings.length === 0) {
    console.log('No valid embeddings provided for merging.');
    return null;
  }

  if (embeddings.length === 1) {
    return embeddings[0];
  }

  const input = {
    body: {
      vectors: embeddings,
    },
  };

  const params = {
    FunctionName: LAMBDA_FUNCTION_NAME,
    Payload: JSON.stringify(input),
  };

  try {
    const data = await lambdaClient.lambda.invoke(params).promise();

    if (!data.Payload) {
      console.log('No payload returned from Lambda function.');
      return null;
    }

    try {
      const { body } = JSON.parse(data.Payload);
      const parsedBody = JSON.parse(body);

      const mergedVector = parsedBody?.merged_vector?.[0];
      if (!mergedVector) {
        console.log('No merged vector found in Lambda response.');
        return null;
      }

      return mergedVector;
    } catch (parseError) {
      console.log('Error parsing Lambda payload:', parseError.message);
      return null;
    }
  } catch (error) {
    console.log('Error invoking Lambda function:', error.message);
    return null;
  }
};

const isTokenCountValidForClip = (text) => {
  if (!text || typeof text !== 'string' || text.trim() === '') return false;

  const tokens = ENCODING.encode(text);
  return tokens.length <= 60;
};

module.exports = { generateEmbedding, mergeEmbeddings, isTokenCountValidForClip };
