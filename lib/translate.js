const path = require('path');
const { I18n } = require('i18n');

const i18n = new I18n({
  directory: path.join(__dirname, 'locales'),
  updateFiles: !!process.env.UPDATE_TRANSLATION_FILES,
  indent: '  ',
});

const locales = [
  null,
  'af',
  'ar',
  'as',
  'az',
  'be',
  'bg',
  'bn',
  'bs',
  'ca',
  'ceb',
  'co',
  'cs',
  'cy',
  'da',
  'de',
  'el',
  'en',
  'es',
  'et',
  'eu',
  'fa',
  'fi',
  'fil',
  'fr',
  'fy',
  'ga',
  'gl',
  'gu',
  'ha',
  'he',
  'hi',
  'hmn',
  'hr',
  'ht',
  'hu',
  'hy',
  'id',
  'ig',
  'is',
  'it',
  'ja',
  'jv',
  'ka',
  'kk',
  'km',
  'kn',
  'ko',
  'ku',
  'ky',
  'lb',
  'lt',
  'lv',
  'mg',
  'mi',
  'mk',
  'ml',
  'mn',
  'mr',
  'ms',
  'mt',
  'my',
  'ne',
  'nl',
  'no',
  'ny',
  'or',
  'pa',
  'pl',
  'ps',
  'pt',
  'ro',
  'ru',
  'rw',
  'sd',
  'si',
  'sk',
  'sl',
  'sm',
  'sn',
  'so',
  'sq',
  'sr',
  'st',
  'su',
  'sv',
  'sw',
  'ta',
  'te',
  'tg',
  'th',
  'tk',
  'tr',
  'tt',
  'ug',
  'uk',
  'ur',
  'uz',
  'vi',
  'xh',
  'yi',
  'yo',
  'zh',
  'zh-Hans',
  'zh-Hant',
  'zu',
];

function translate(phrase, locale, ...subs) {
  if (!phrase) {
    return phrase;
  }
  if (!locale || !locales.includes(locale)) {
    locale = 'en';
  }
  return i18n.__({ phrase, locale }, ...subs);
}

const i18n_frontend = new I18n({
  directory: path.join(__dirname, 'locales-frontend'),
  updateFiles: false,
});
function translate_frontend(phrase, locale, ...subs) {
  if (!phrase) {
    return phrase;
  }
  if (!locale || !locales.includes(locale)) {
    locale = 'en';
  }
  return i18n_frontend.__({ phrase, locale }, ...subs);
}

const i18n_locations = new I18n({
  directory: path.join(__dirname, 'locales-locations'),
  updateFiles: false,
});
function translate_locations(phrase, locale, ...subs) {
  if (!phrase) {
    return phrase;
  }
  if (!locale || !locales.includes(locale)) {
    locale = 'en';
  }
  return i18n_locations.__({ phrase, locale }, ...subs);
}

module.exports = {
  translate,
  translate_frontend,
  translate_locations,
  locales,
  i18n,
};
