const getlanguageSpecificPrompt = (languageCode, languageName, locationName) => {
  const prompts = {
    af: `Vertaal die volgende pleknaam in ${languageName}/n, soos 'n moedertaalspreker van daardie taal na die plek sou verwys. Die reaksie moet slegs die vertaalde weergawe van die verskafte teks bevat, sonder wysigings, toevoegings, of geselsies.`,
    ar: `قم بتعريب اسم المكان التالي إلى ${languageName}/n، كما يشير إليه المتحدثون الأصليون بهذه اللغة. يجب أن تحتوي الإجابة فقط على النسخة المترجمة للنص المقدم، دون تعديلات أو إضافات أو دردشة.`,
    as: `তলতোৱা স্থানৰ নাম ${languageName}/n ভাষালৈ স্থানীয়কৰণ কৰক, যিদৰে সেই ভাষাৰ মাতৃভাষী লোকজনে সেই স্থানটোলৈ উল্লেখ কৰিব। উত্তৰত মাথো দিয়া পাঠ্যটোৰ অনুবাদিত সংস্কৰণ থাকিব লাগে, সংশোধন, সবলগ কৰা বা আনকি বাতৰি বিনিময় নকৰীব।`,
    az: `Aşağıdakı yer adını ${languageName}/n, o dili ana dili kimi danışan biri bu yeri necə adlandıracaqsa, həmin şəkildə yerliyə çevirin. Cavab yalnız təqdim edilmiş mətnin tərcüməsini ehtiva etməlidir, dəyişikliklər, əlavələr və ya söhbət olmadan.`,
    be: `Лакалізуйце наступную назву месца на ${languageName}/n, як назваў бы гэтае месца носьбіт мовы. Адказ павінен утрымліваць толькі перакладзены варыянт прадастаўленага тэксту, без змен, дадаткаў або гутаркі.`,
    bg: `Локализирайте следното име на място в ${languageName}/n, както носител на езика би се позовавал на мястото. Отговорът трябва да съдържа само преведената версия на предоставения текст, без промени, добавки или приказки.`,
    bn: `নিম্নলিখিত স্থানের নামটি ${languageName}/n এ স্থানীয়করণ করুন, যেমনটি ওই ভাষার একজন দেশীয় বক্তা স্থানটিকে উল্লেখ করবেন। উত্তরে কেবল প্রদত্ত পাঠ্যের অনূদিত সংস্করণটি থাকবে, কোনো পরিবর্তন, যোগফল, বা ছোটখাট আলাপ ছাড়া।`,
    bs: `Prevedite sljedeći naziv mjesta na ${languageName}/n, onako kako bi ga izvorni govornik tog jezika nazivao. Odgovor bi trebao sadržavati samo prevedenu verziju pruženog teksta, bez izmjena, dodataka ili razgovora.`,
    ca: `Localitza el següent nom de lloc a ${languageName}/n, tal com un parlant natiu d'aquesta llengua es referiria al lloc. La resposta només ha de contenir la versió traduïda del text proporcionat, sense modificacions, afegitons o xerrameca.`,
    ceb: `I-localize ang mosunod nga ngalan sa lugar ngadto sa ${languageName}/n, sama sa paglitok niini sa usa ka katutubong mamumulong sa kana nga sinultian. Ang tubag kinahanglan nga adunay sulod ra nga gihubad nga bersyon sa gihatag nga teksto, nga walay mga pagbag-o, dugang o mga chit chat.`,
    co: `Localizà u nome di stu locu in ${languageName}/n, cum'è un parlante nativu di quella lingua si rifereria à u locu. A risposta deve cuntene solu a versione tradutta di u testu furnitu, senza mudificazioni, aghjunte, o chiacchierate.`,
    cs: `Lokalizujte následující název místa do ${languageName}/n, jak by na toto místo odkazoval rodilý mluvčí daného jazyka. Odpověď by měla obsahovat pouze přeloženou verzi poskytnutého textu, bez úprav, doplnění nebo konverzace.`,
    cy: `Lleoleiddiwch yr enw lle canlynol i ${languageName}/n, fel y byddai siaradwr brodorol o'r iaith honno yn cyfeirio at y lle. Dylai'r ymateb gynnwys dim ond y fersiwn gyfieithiedig o'r testun a ddarperir, heb addasiadau, ychwanegiadau, neu sgwrs.`,
    da: `Oversæt følgende stednavn til ${languageName}/n, som en indfødt taler af det sprog ville referere til stedet. Svaret skal kun indeholde den oversatte version af den angivne tekst, uden ændringer, tilføjelser eller småsnak.`,
    de: `Lokalisieren Sie den folgenden Ortsnamen in ${languageName}/n, so wie ein Muttersprachler dieser Sprache den Ort nennen würde. Die Antwort sollte nur die übersetzte Version des bereitgestellten Textes enthalten, ohne Änderungen, Ergänzungen oder Plauderei.`,
    el: `Εντοπίστε το ακόλουθο όνομα τόπου στα ${languageName}/n, όπως θα αναφερόταν στον τόπο αυτόν ένας ομιλητής της γλώσσας. Η απάντηση πρέπει να περιέχει μόνο τη μεταφρασμένη έκδοση του παρεχόμενου κειμένου, χωρίς τροποποιήσεις, προσθήκες ή ανούσια σχόλια.`,
    es: `Localiza el siguiente nombre de lugar en ${languageName}/n, como lo referiría un hablante nativo de ese idioma. La respuesta debe contener únicamente la versión traducida del texto proporcionado, sin modificaciones, adiciones o charlas.`,
    et: `Kohandage järgmine kohanimi ${languageName}/n, nii nagu selle keele emakeelena kõneleja seda kohta nimetaks. Vastus peaks sisaldama ainult esitatud teksti tõlgitud versiooni, ilma muudatuste, lisanduste või jututa.`,
    eu: `Lokalizatu honako toki-izena ${languageName}-era/n, hizkuntza horretako hiztunek tokiari dei egingo zioketen moduan. Erantzunak testu emanaren itzulpena baino ez du izan behar, aldaketarik, gehigarririk edo hitz aldaketarik gabe.`,
    fa: `محلی سازی نام مکان زیر به ${languageName}/n، همانطور که یک فرد بومی آن زبان به مکان اشاره می‌کند. پاسخ باید فقط شامل نسخه‌ی ترجمه‌شده‌ی متن ارائه‌شده باشد، بدون تغییرات، افزودنی‌ها، یا گپ زدن.`,
    fi: `Käännä seuraava paikannimi muotoon ${languageName}, kuten kyseisen kielen äidinkielenään puhuja viittaisi paikkaan. Vastauksen tulee sisältää ainoastaan käännös annetusta tekstistä ilman muutoksia, lisäyksiä tai small talkia.`,
    fil: `Isalin ang sumusunod na pangalan ng lugar sa ${languageName}/n, tulad ng pagtukoy ng isang katutubong nagsasalita ng wikang iyon sa lugar. Ang tugon ay dapat naglalaman lamang ng isinaling bersyon ng ibinigay na teksto, nang walang mga pagbabago, karagdagan, o walang kahit anong paligoy-ligoy.`,
    fr: `Localisez le nom de ce lieu en ${languageName}/n, comme le ferait un locuteur natif de cette langue. La réponse ne doit contenir que la version traduite du texte fourni, sans modifications, ajouts ou bavardages.`,
    fy: `Lokalizearje de folgjende plaknamme yn ${languageName}/n, sa't in memmetaalsprekkende fan dy taal de plaknamme soe útsprekke. It antwurd moat allinnich de oersette ferzje fan de levere tekst befetsje, sûnder wizigingen, tafoegings, of praatjes.`,
    ga: `Logánú an t-ainm áite seo a leanas isteach i ${languageName}/n, mar a dhéanfadh cainteoir dúchais na teanga sin tagairt don áit. Níor cheart go mbeadh i do fhreagra ach an leagan aistrithe den téacs ar fáil, gan modhnuithe, breiseanna ná cúrsaí cainte.`,
    gl: `Localiza o seguinte nome de lugar en ${languageName}/n, como o referiría un falante nativo desa lingua. A resposta só debe conter a versión traducida do texto proporcionado, sen modificacións, adicións ou conversa fóra de tema.`,
    gu: `નીચેના સ્થાનના નામનું ${languageName}/n માં સ્થાનિકીકરણ કરો, જેમ આ ભાષાના મૂળ વક્તા સ્થાનને સંબોધિત કરશે. પ્રતિસાદમાં ફક્ત પ્રદાન કરેલ પાઠનો ભાષાંતરો જ હોવો જોઈએ, ફેરફાર, ઉમેરા અથવા વાતચીત વગર.`,
    ha: `Fassara sunan wuri na gaba zuwa ${languageName}/n, kamar yadda mai magana da harshen zai ambaci wurin. Amsar ya kamata ta ƙunshi fassarar rubutun da aka bayar kawai, ba tare da gyare-gyare, ƙari, ko magana fiye da kima ba.`,
    he: `הקל על שם המקום הבא ל-${languageName}/n, כפי שדובר ילידי של השפה העברית היה מתייחס למקום. התגובה צריכה להכיל רק את הגרסה המתורגמת של הטקסט שניתן בכתב אשורי, ללא שינויים, הוספות או דיבור מיותר.`,
    hi: `निम्नलिखित स्थान का नाम ${languageName}/n में स्थानीयकृत करें, जैसे कि उस भाषा का मूल वक्ता उस स्थान को संदर्भित करेगा। प्रतिक्रिया में केवल प्रदान किए गए पाठ का अनुवादित संस्करण होना चाहिए, बिना किसी संशोधन, अतिरिक्त या बातचीत के।`,
    hmn: `Hloov chaw npe li hauv qab no mus rau ${languageName}/n, raws li ib tug neeg hais lus hauv zos ntawm cov lus ntawd yuav hu rau qhov chaw ntawd. Qhov lus teb tsuas muaj cov lus txhais ntawm cov ntawv muab, yam tsis muaj kev hloov kho, ntxiv, lossis chit chat.`,
    hr: `Localizirajte sljedeće ime mjesta na ${languageName}/n, kao što bi ga izvorni govornik tog jezika nazvao. Odgovor bi trebao sadržavati samo prevedenu verziju pruženog teksta, bez izmjena, dodataka ili čavrljanja.`,
    ht: `Lokalize non kote sa a nan ${languageName}/n, jan yon moun k ap pale lang sa a natif natal ta refere a kote a. Repons lan ta dwe sèlman gen vèsyon tradwi nan tèks bay la, san modifikasyon, adisyon, oswa pale pale.`,
    hu: `Fordítsd le a következő helynevet ${languageName}/n nyelvre úgy, ahogy az adott nyelv anyanyelvi beszélői hivatkoznának a helyre. A válasz csak a megadott szöveg fordítását tartalmazhatja, módosítások, kiegészítések vagy csevegés nélkül.`,
    hy: `Տեղականացրեք հետևյալ տեղանունը ${languageName}/n, ինչպես այդ լեզվի նախագծային խոսողը կոչեր այդ տեղը։ Պատասխանը պետք է պարունակի միայն տրված տեքստի թարգմանած տարբերակը՝ առանց փոփոխությունների, հավելվածների կամ զրույցի։`,
    id: `Terjemahkan nama tempat berikut ke dalam ${languageName}/n, seperti yang akan disebut oleh penutur asli bahasa tersebut. Respons hanya harus berisi versi terjemahan dari teks yang diberikan, tanpa modifikasi, tambahan, atau obrolan.`,
    ig: `Hulata aha ebe a n'ime ${languageName}/n, ka onye na-asụ asụsụ a n'ezughi oke ga-esi kpọọ ebe ahụ. Azịza ahụ kwesịrị ịgụnye naanị nsụgharị nke ederede enyere, nweghị mgbanwe, mgbakwunye, ma ọ bụ mkparịta ụka.`,
    is: `Berskjáðu eftirfarandi staðarnafn á ${languageName}/n, eins og móðurmálsmaður þess tungumáls myndi vísa til staðarins. Svarið ætti aðeins að innihalda þýdda útgáfu af gefnum texta, án útúrdúra, viðbóta eða spjalls.`,
    it: `Localizza il seguente nome di luogo in ${languageName}/n, come lo farebbe un madrelingua di quella lingua. La risposta dovrebbe contenere solo la versione tradotta del testo fornito, senza modifiche, aggiunte o chiacchiere.`,
    ja: `次の地名を${languageName}/nに地域化してください。その言語のネイティブスピーカーがその場所を参照するようにします。回答には、提供されたテキストの翻訳版のみを含め、変更、追加、または雑談は行わないでください。`,
    jv: `Lokalake jeneng papan ing ngisor iki menyang ${languageName}/n, kaya penutur asli basa kasebut bakal ngrujuk papan kasebut. Tanggapan kasebut kudu mung ngemot versi terjemahan saka teks sing diwenehake, tanpa modifikasi, tambahan, utawa omongan.`,
    ka: `გადაათარგმნეთ შემდეგი ადგილის სახელი ${languageName}/n-ში, როგორც ამ ენის მეტარებელი აღნიშნავდა ადგილს. პასუხი უნდა შეიცავდეს მხოლოდ გადათარგმნილ ტექსტს, მოდიფიკაციების, დამატებების ან საუბარის გარეშე.`,
    kk: `Төмендегі орын атауын ${languageName}/n деп аударыңыз, сол тілдің тума сөйлеушісі сол жерге қалай ат қойған болар еді. Жауап тек ұсынылған мәтіннің аудармасын ғана қамтуы тиіс, өзгерістерсіз, қосымшаларсыз және әңгімесіз.`,
    km: `ធ្វើមូលដ្ឋាននិយមន័យឈ្មោះទីកន្លែងដូចតទៅនៅ ${languageName}/n ដូចម្ដេចដែលអ្នកនិយាយភាសាមួយនោះត្រឡប់មកវិញទៅទីកន្លែង។ ការឆ្លើយតបគួរតែនាំមកនូវកំណែបកប្រែរបស់អត្ថបទដែលផ្តល់ឱ្យដោយគ្មានការកែប្រែបន្ថែម ឬការសន្ទនាបន្ថែម។`,
    kn: `ನೇಮಕನಾಮವನ್ನ ${languageName}/n ನಲ್ಲಿ ಸ್ಥಳೀಯೀಕರಿಸಿ, ಆ ಭಾಷೆಯ ಸ್ಥಳೀಯವಾಗಿ ಮಾತನಾಡುವ ವ್ಯಕ್ತಿಯು ಆ ಸ್ಥಳಕ್ಕೆ ಹೆಸರಿಡುವ ರೀತಿಯಲ್ಲಿ. ಉತ್ತರದಲ್ಲಿ ಒದಗಿಸಿದ ಪಠ್ಯದ ಅನುವಾದಿತ ಆವೃತ್ತಿಯನ್ನು ಮಾತ್ರ ಹೊಂದಿರಬೇಕು, ವ್ಯತ್ಯಾಸಗಳು, ಹೆಚ್ಚುವರಿ, ಅಥವಾ ಚಿತಿಚಾಟ್ಜಿಲ್ಲದೆ.`,
    ko: `다음 장소 이름을 ${languageName}(으)로 현지화하세요./n, 해당 언어의 모굌어 사용자가 그 장소를 언급할 때 사용하는 방식대로. 응답은 제공된 텍스트의 번역된 버전만을 포함해야 하며, 수정, 추가 또는 잡담이 없어야 합니다.`,
    ku: `Navê cihê yên jêrîn bi ${languageName} biguherîne/n, wekî agahdarekî zimanê zindanna bihêze be ku bi cihê reference dikare. Bersîva tenê divê guhertiha daran yên peyda kirî bifirin, bê guherandin, lêzanîn, an healbest.`,
    ky: `Бул жерде көрсөтүлгөн жер аталышын ${languageName} тилине, ошол тилдин эне тилинде сүйлөгөн адамы айткандай кылып которуңуз/n. Жооп берилген тексттин котормосун гана камтуусу керек, өзгөртүүлөрсүз, кошумчаларсыз же коштоочу сөздөрсүз.`,
    lb: `Lokaliséiert de folgende Plazennumm an ${languageName}/n, sou wéi en Nativespriecher vun där Sprooch op d'Plaz géif referéieren. D'Äntwert soll nëmmen déi iwwersaten Versioun vum ugebuedene Text enthalen, ouni Modifikatiounen, Zousätz oder Geplänkel.`,
    lt: `Vietininkite šią vietos pavadinimą į ${languageName}/n, kaip tos kalbos gimtakalbis vadintų šią vietą. Atsakyme turi būti tik išverstas pateiktas tekstas, be jokių pakeitimų, papildymų ar pokalbių.`,
    lv: `Lokalizējiet šo vietas nosaukumu ${languageName}/n, tāpat kā šīs valodas dzimtā valoda lietotājs to dēvētu. Atbildei jābūt tikai norādītā teksta tulkotajai versijai, bez modifikācijām, pievienojumiem vai sarunām.`,
    mg: `Ampifanaraho amin'ny teny ${languageName} ireto anarana toerana ireto/n, araka ny fomba fiantsoan'ny mpiteny zanatany io toerana io. Ny valiny dia tokony hiasa ny dikan-teny fotsiny amin'ny lahatsoratra omena, tsy misy fanovana, fanampiny, na resaka hafa.`,
    mi: `Hāngaihia te ingoa wāhi e whai ake nei ki te ${languageName}/n, pēnei i te kī a tētahi kaikorero taketake o taua reo. Ko te whakahoki me kapi noa iho te putanga whakamaoritanga o te kōrero i tukuna mai, me kore e whakarerekētanga, tāpirihanga, tika te kōrero noa rānei.`,
    mk: `Локализирајте го следното име на место на ${languageName}/n, како што би се однесувал кон местото како мајчин говорник на тој јазик. Одговорот треба да содржи само преведената верзија на дадениот текст, без модификации, дополнувања или разговор.`,
    ml: `ഈ സ്ഥലനാമം ${languageName} എന്നാക്കുക, ആ ഭാഷയുടെ സ്വദേശി സംസാരക്കാരൻ ആ സ്ഥലത്തെ പരാമർശിക്കുന്നത് എങ്ങനെയെന്നു പറയുക. മറുപടിയിൽ നൽകുന്ന വാചകത്തിന്റെ പരിഭാഷ മാത്രമേ ഉണ്ടാകൂ, മാറ്റങ്ങൾ, കൂട്ടിച്ചേർക്കലുകൾ, അല്ലെങ്കിൽ സംഭാഷണം ഇല്ലാതെ.`,
    mn: `Дараах газрын нэрийг ${languageName}/n руу орчуулаарай, тухайн хэлээр ярьдаг уугуул хүн тухайн газрыг яаж нэрлэх вэ. Хариу нь зөвхөн өгөгдсөн текстийн орчуулгыг өгөх ёстой, засвар, нэмэлт, эсвэл хэрэггүй зүйлгүйгээр.`,
    mr: `खालील स्थानाचे नाव ${languageName}/n मध्ये स्थानिकीकरण करा, जसे त्या भाषेचा मूळ वक्ता त्या स्थानाला संबोधित करेल. प्रतिसादात फक्त दिलेल्या मजकुराचे अनुवादित आवृत्ती समाविष्ट करावी, त्यात कोणत्याही प्रकारचे बदल, भर अथवा गप्पा नसाव्यात.`,
    ms: `Terjemahkan nama tempat berikut ke dalam ${languageName}/n, seperti yang dirujuk oleh penutur asli bahasa tersebut. Respons hanya harus mengandungi versi terjemahan teks yang diberikan, tanpa modifikasi, tambahan, atau perbualan sampingan.`,
    mt: `Lokalizza l-isem ta’ din il-post f’${languageName}/n, kif jitkellmu persuni nattivi ta’ dik il-lingwa dwar il-post. Ir-risposta għandha tinkludi biss il-verżjoni tradotta tat-test ipprovdut, mingħajr modifiki, żidiet, jew diskors.`,
    my: `{name}များကို ${languageName} ၌ မြို့နာမည်ကို ဒေသခံတွေက ဘယ်လိုခေါ်ကြသလဲဆိုတာကို ပြောင်းလဲပါ။ အဲဒီဘာသာစကားလို ပြောကြားတဲ့အတိုင်းသာ ဖြေကြာရပါမည်။ တက်ကတက် နထေိုက်မျှ {text} ကိုသာ ဘာသာပြန်ထားပါ။`,
    ne: `यो स्थान नामलाई ${languageName}/n मा स्थानीय बनाउनुहोस्, जसरी त्यो भाषाका आम बोल्नेहरूले स्थानलाई उल्लेख गर्नेछन्। प्रतिक्रियामा प्रदान गरिएको पाठको केवल अनुवादित संस्करण हुनुपर्छ, कुनै परिमार्जन, थप, वा गफगाफ बिना।`,
    nl: `Localiseer de volgende plaatsnaam in ${languageName}/n, zoals een moedertaalspreker van die taal naar de plaats zou verwijzen. Het antwoord moet alleen de vertaalde versie van de verstrekte tekst bevatten, zonder wijzigingen, toevoegingen of smalltalk.`,
    no: `Oversett følgende stedsnavn til ${languageName}/n, slik en morsmålstaler av det språket ville referert til stedet. Svaret skal kun inneholde den oversatte versjonen av den oppgitte teksten, uten endringer, tillegg eller småprat.`,
    ny: `Lembani dzina la malo awa mu ${languageName}/n, monga wolankhula wamba wa chinenerocho angamatchulire malowo. Yankho liyenera kukhala lokha okha, litanthauzo la chinenerocho, popanda zosintha, zowonjezera, kapena nkhani.`,
    or: `ନିମ୍ନଲିଖିତ ସ୍ଥାନ ନାମକୁ ${languageName}/n ରେ ସ୍ଥାନୀୟକରଣ କରନ୍ତୁ, ଯେପରିକି ସେ ଭାଷାର ଏକ ମାତୃଭାଷୀର ଦ୍ୱାରା ସେହି ସ୍ଥାନକୁ ସମ୍ବୋଧନ କରାଯାଏ। ଉତ୍ତରରେ କେବଳ ପ୍ରଦାନ କରାଯାଇଥିବା ପାଠ୍ୟର ଅନୁବାଦ ରହିବ ଏବଂ କୌଣସି ପରିବର୍ତ୍ତନ, ଯୋଡ଼ାଣି, କିମ୍ବା ଚାଟ୍ ଚାଟ୍ ନଥିବ।`,
    pa: `ਨਿਮਨਲਿਖਿਤ ਸਥਾਨ ਦਾ ਨਾਮ ${languageName}/n ਵਿਗ਼ ਸਥਾਨਕ ਕਰੋ, ਜਿਵੇਂ ਉਸ ਭਾਸ਼ਾ ਦਾ ਦੇਸੀ ਵਕਤਾ ਉਸ ਜਗ੍ਹਾ ਨੂੰ ਸੰਦਰਭਿਤ ਕਰੇਗਾ। ਜਵਾਬ ਵਿੱਚ ਕੇਵਲ ਦਿੱਤੇ ਗਏ ਪਾਠ ਦਾ ਅਨੁਵਾਦ ਹੀ ਸ਼ਾਮਲ ਹੋਵੇ, ਬਿਨਾਂ ਕਿਸੇ ਤਬਦੀਲੀ, ਵਾਧੇ ਜਾਂ ਗੱਪਸ਼ੱਪ ਦੇ।`,
    pl: `Zlokalizuj następującą nazwę miejsca w ${languageName}/n, tak jak osoba mówiąca w tym języku ojczystym odnosiłaby się do tego miejsca. Odpowiedź powinna zawierać tylko przetłumaczoną wersję podanego tekstu, bez modyfikacji, dodatków czy pogawędek.`,
    ps: `لاندې د ځای نوم په ${languageName} کې ځايي کړئ/n، لکه څنګه چې د هغې ژبې یو اصلي ویونکی به هغې ځای ته مراجعه وکړي. ځواب باید یوازې د ورکړل شوي متن ژباړل شویه نسخه ولري، پرته له تعدیلاتو، اضافاتو، یا خبرې اترې.`,
    pt: `Localize o seguinte nome de lugar em ${languageName}/n, como um falante nativo daquela língua se referiria ao lugar. A resposta deve conter apenas a versão traduzida do texto fornecido, sem modificações, acréscimos ou conversa fiada.`,
    ro: `Localizează următorul nume de loc în ${languageName}/n, așa cum un vorbitor nativ al acelei limbi ar face referire la locul respectiv. Răspunsul trebuie să conțină doar versiunea tradusă a textului furnizat, fără modificări, adăugiri sau discuții suplimentare.`,
    ru: `Локализуйте следующее название места в ${languageName}/n, как это делал бы носитель этого языка. Ответ должен содержать только переведённый вариант предоставленного текста, без изменений, дополнений или разговоров.`,
    rw: `Simbuza izina ry'aha hantu mu rurimi rwa ${languageName}/n, nk'uko umuntu ukivuga yaba avuga aho hantu. Igisubizo kigomba kuba gifite gusa igisobanuro cyahinduwe cy'iyo nyandiko yahatanzwe, nta guhindura, kongera, cyangwa kuvuga imirimo yo ku ruhande.`,
    sd: `هن هنڌ جو نالو ${languageName}/n ۾ مقامي بڻايو، جيئن ان ٻولي جو مقامي ڳالهائيندڙ ان هنڌ ڏانهن اشارو ڪندو. جواب ۾ رڳو مهيا ڪيل متن جو ترجمو ٿيل نسخو شامل هجڻ گهرجي، ڪنهن به تبديلي، واڌاري، يا ڳالھ ٻولهه کان سواءِ.`,
    si: `මෙම ස්ථාන නාමය දේශීය කරන්න ${languageName}/n, එම භාෂාවේ මුල් කථිකයෙකු එම ස්ථානයට යොමු කරන අන්දමට. ප්‍රතිචාරය ලබා දෙන්නේ ප්‍රදාන පාඨයේ හැරවුම්, එකතු කිරීම් හෝ කතා බහ රහිතව පරිවර්තනය කළ පාඨය පමණි.`,
    sk: `Lokalizujte nasledujúce miesto do ${languageName}/n, tak ako by na to miesto odkazoval rodilý hovoriaci daného jazyka. Odpoveď by mala obsahovať len preloženú verziu poskytnutého textu, bez úprav, doplnkov alebo neformálnej konverzácie.`,
    sl: `Lokalizirajte naslednje ime kraja v ${languageName}/n, kot bi ga poimenoval domači govorec tega jezika. Odgovor naj vsebuje samo prevedeno različico podanega besedila, brez sprememb, dodatkov ali klepeta.`,
    sm: `Fa'aliliu le igoa o le nofoaga o lo'o mulimuli mai i le ${languageName}/n, e pei ona ta'ua e se tagata e tautatala masani i lenā gagana le nofoaga. O le tali e tatau ona iai le fa'aliliuga o le tusitusiga na tu'uina mai, e aunoa ma ni suiga, faʻaopoopoga, pe talatalanoaga.`,
    sn: `Shandurudza zita renzvimbo inotevera kuita ${languageName}/n, sezvazvinonzi nemutauri wemutauro iwoyo. Mhinduro inofanira kungotora shanduro yehurukuro yakapihwa, pasina kuchinja, kuwedzera, kana chit chat.`,
    so: `Ku samee deegaanka magacaan meeshiisa ${languageName}/n, sida qof luuqaddaas u dhashay uu meesha ugu yeeri lahaa. Jawaabtu waa inay ka koobnaato oo keliya nooca la tarjumey ee qoraalka la bixiyey, iyada oo aan wax ka beddelin, wax ku darin, ama la dhihin.`,
    sq: `Lokalizo emrin e vendit të mëposhtëm në ${languageName}/n, siç do ta referonte një folës nativ i atij gjuhe. Përgjigja duhet të përmbajë vetëm versionin e përkthyer të tekstit të dhënë, pa modifikime, shtesa, apo bisedë të tjera.`,
    sr: `Lokalizujte sledeće ime mesta u ${languageName}/n, kako bi to izrazio izvorni govornik tog jezika. Odgovor treba da sadrži samo prevedenu verziju datog teksta, bez izmena, dodataka ili ćaskanja. `,
    st: `Fetolela lebitso la sebaka se latelang ho ${languageName}/n, kamoo sebui sa letsoalloa la puo eo se ka bua ka sebaka seo. Karabo e lokela ho ba le phetolelo feela ea sengoloa se fanoeng, ntle le liphetoho, litlatsetso, kapa puisano e seng ya bohlokwa.`,
    su: `Lokalkeun ngaran tempat ieu kana ${languageName}/n, sakumaha panyatakeun penutur asli tina basa éta ngarujuk kana tempat éta. Résponna kedah ngan ukur ngandung vérsi tarjamahan tina téks anu disayogikeun, tanpa modifikasi, tambahan, atanapi obrolan.`,
    sv: `Översätt följande platsnamn till ${languageName}/n, som en infödd talare av det språket skulle referera till platsen. Svaret ska endast innehålla den översatta versionen av den angivna texten, utan ändringar, tillägg eller småprat. `,
    sw: `Tafsiri jina la mahali lifuatalo katika ${languageName}/n, kama vile mzungumzaji wa asili wa lugha hiyo angeyarejelea mahali hapo. Jibu linapaswa kuwa na toleo lililotafsiriwa tu la maandishi yaliyotolewa, bila mabadiliko, nyongeza, au mazungumzo ya kawaida.`,
    ta: `பின்வரும் இடத்தின் பெயரை ${languageName}-க்கு உள்ளூர் பெயராக மொழிபெயர்க்கவும், அப் மொழியின் ஒரு சொந்த மொழி பேருரையாளர் அந்த இடத்தை எப்படி குறிப்பிடுவார் என்பதையும் கவனத்தில் கொள்ளவும். பதில், வழங்கப்பட்ட உரையின் மொழிபெயர்க்கப்பட்ட பதிப்பை மட்டுமே கொண்டிருக்க வேண்டும், மாற்றங்கள், சேர்க்கைகள், அல்லது பேசுபோது இல்லாமல்.`,
    te: `ఈ ప్రదేశ పేరును ${languageName}/n లోకలైజ్ చేయండి, ఆ భాషా స్థానికుడు ఆ ప్రదేశాన్ని ఎలా పిలుస్తారో అలా. స్పందన అందించబడిన పాఠ్యాన్ని అనువదించబడిన రూపంలో మాత్రమే ఉండాలి, మార్పులు, అదనపులు లేదా చిట్ చాట్ లేకుండా.`,
    tg: `Ҷойгоҳи зеринро ба ${languageName}/n маҳаллӣ кунед, чунон ки як нафаре зодаи ин забон ба он макон ишора мекунад. Ҷавоб бояд танҳо тарҷумаи матни додашударо дар бар гирад, бе тағйирот, иловаҳо ё гуфтугӯи дигар.`,
    th: `แปลชื่อสถานที่ต่อไปนี้เป็น ${languageName}/n ตามที่ผู้พูดภาษานั้นๆ จะเรียกสถานที่นั้น คำตอบควรมีเพียงแค่เวอร์ชันที่แปลแล้วของข้อความที่ให้มา โดยไม่มีการเปลี่ยนแปลง การเพิ่มเติม หรือการพูดคุย (คำตอบต้องเขียนเป็นภาษาไทย):`,
    tk: `Şu ýer adyny ${languageName} diline terjime ediň/n, şol diliň ene dilde gepläp bilýän biri ýaly o ýere nähili at berilse. Jogap diňe berlen tekstiň terjime edilen görnüşini öz içine almalydyr, üýtgeşmeler, goşmalar ýa-da goşmaça gürrüňsiz.`,
    tr: `Aşağıdaki yer adını ${languageName}/n olarak yerelleştirin, o dilin yerli bir konuşucusunun o yeri nasıl adlandıracağı şeklinde. Yanıt, verilen metnin çevrilmiş halini içermelidir, değişiklikler, eklemeler veya sohbet içermemelidir.`,
    tt: `Җир исемен ${languageName}/n теленә тәрҗемә итегез, әлеге телнең туган тел буларак сөйләшүчесе ул урынны ничек атаячагы кебек. Җавап бары тик бирелгән текстның тәрҗемә ителгән версиясен генә үз эченә алырга тиеш, үзгәртүләр, өстәмәләр яки көндәлек сөйләмсез.`,
    ug: `تۆۋەندىكى يەر نامىنى ${languageName} تىكىدە لوكاللاشتۇرۇڭ، بۇ تىلنىڭ تۇغما سۆزلىگۈچىسى بۇ يەرنى قانداق ئاتاشى كېرەك بولسا شۇنداق بولسۇن. جاۋاب پەقەت تەرجىمە قىلىنغان نامدىنلا ئىبارەت بولۇشى كېرەك، ئۆزگەرتمە، قوشۇمچا، ياكى بوش سۆز قوشۇش كېرەك ئەمەس.`,
    uk: `Локалізуйте наступну назву місця у ${languageName}/n, як би на це місце посилалися носії цієї мови. Відповідь має містити лише перекладений варіант наданого тексту, без змін, доповнень чи балачок.`,
    ur: `مندرجہ ذیل مقامی نام کو ${languageName}/n میں ترجمہ کریں، جیسا کہ اس زبان کا مقامی بولنے والا اس مقام کا حوالہ دے گا۔ جواب میں صرف فراہم کردہ متن کا ترجمہ شامل ہونا چاہیے، بغیر کسی تبدیلی، اضافے یا گفتگو کے۔`,
    uz: `Quyidagi joy nomini ${languageName} tiliga mahalliylashtiring/n, o'sha tilning ona tilida so'zlashuvchi qanday atalgan bo'lsa. Javob faqat taqdim etilgan matnning tarjima qilingan versiyasini o'z ichiga olishi kerak, o'zgartirishlar, qo'shimchalar yoki ortiqcha so'zlarsiz.`,
    vi: `Dịch tên địa điểm sau sang ${languageName}/n, theo cách mà người bản xứ của ngôn ngữ đó sẽ gọi địa điểm đó. Câu trả lời chỉ nên chứa phiên bản đã dịch của văn bản được cung cấp, không thay đổi, bổ sung, hoặc nói chuyện phiếm.`,
    xh: `Guqula igama lendawo elilandelayo libe ${languageName}/n, njengokuba utshaba lwendawo kuloo lwimi belinokubhekisela kwindawo. Impendulo kufuneka iqulathe kuphela inguqulelo yombhalo onikiweyo, ngaphandle koqheliselo, wongezelelo, okanye incoko.`,
    yi: `לאָקאַליזירן די ווייַטערדיק אָרט נאָמען אין ${languageName}/n, ווי אַ געבוירענער רעדנער פון דעם שפּראַך וואָלט אָפּשיקן צו דעם אָרט. דער ענטפֿער זאָל בלויז אַנטהאַלטן די איבערגעזעצטע ווערסיע פון די פֿאַרזארגטע טעקסט, אָן מאַדיפיקאַציעס, צוגעבענישן, אָדער שמועס.`,
    yo: `Lokalise orukọ ibi ti a mẹnuba si ${languageName}/n, gẹgẹ bi ẹni ti o n sọ ede naa yoo ṣe tọkasi ibi naa. Idahun yẹ ki o kun fun ẹya ti a tumọ ti ọrọ ti a pese, lai si awọn iyipada, awọn afikun, tabi ọrọ imuduro.`,
    'zh-Hans': `将以下地名本地化为 ${languageName}/n，就像该语言的母语使用者会提到的那样。回应应仅包含所提供文本的翻译版本，不进行修改、添加或闲聊。`,
    'zh-Hant': `將以下地名本地化為${languageName}/n，如該語言的母語使用者所稱。回應應僅包含所提供文本的翻譯版本，不得修改、增加或閒聊。`,
    zu: `Lokalisa igama lendawo elilandelayo libe ku-${languageName}/n, njengoba umuntu okhuluma lolu limi engasho ngalo igama. Impendulo kufanele ibe nalesi sicwili esihunyushiwe kuphela, ngaphandle kokuguqulwa, ukwengeza, noma ukukhiqiza izingxoxo.`,
  };

  const defaultPrompt = `Localize the following place name into ${languageName}/n, as a native speaker of that language would refer to the place. The response should only contain the translated version of the provided text, without modifications, additions, or chit chat.`;

  return `${prompts[languageCode] || defaultPrompt}\n\n${locationName}`;
};

module.exports = getlanguageSpecificPrompt;
