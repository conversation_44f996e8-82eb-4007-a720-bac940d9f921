const axios = require('axios');

let replicate;

/* Sample output of createPrediction api response
{
  "id": "xag3atv30xrgm0cf7yca88gpaw",
  "model": "replicate-internal/llama3-70b-chat-vllm-unquantized",
  "version": "dp-cf04fe09351e25db628e8b6181276547",
  "input": {
    "frequency_penalty": 0.2,
    "max_tokens": 512,
    "min_tokens": 0,
    "presence_penalty": 1.15,
    "prompt": "Work through this problem step by step:\n\nQ: <PERSON> has 7 llamas. Her friend gives her 3 more trucks of llamas. Each truck has 5 llamas. How many llamas does <PERSON> have in total?",
    "prompt_template": "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a helpful assistant<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n",
    "temperature": 0.6,
    "top_p": 0.9
  },
  "logs": "",
  "error": null,
  "status": "starting",
  "created_at": "2024-05-03T17:55:02.535Z",
  "urls": {
    "cancel": "https://api.replicate.com/v1/predictions/xag3atv30xrgm0cf7yca88gpaw/cancel",
    "get": "https://api.replicate.com/v1/predictions/xag3atv30xrgm0cf7yca88gpaw"
  }
}
*/

/* Sample out put of getPrediction api response, once the prediction is completed
{
  "completed_at": "2024-05-03T13:32:23.806337Z",
  "created_at": "2024-05-03T13:32:20.119000Z",
  "error": null,
  "id": "a9np4byz2xrgj0cf7tktkgw1g4",
  "input": {
    "top_p": 0.9,
    "prompt": "Help me talk with my match on Boo, a personality-based social dating app, in language \"ja\". Provide 4 topical icebreakers for me to send to my match, formatted as a json array in the following format: { output: [\"example 1\", \"example 2\", \"example 3\", \"example 4\"] }. Don't assume anything not stated in the following sentences.\n\n        Me: Rahul, 29 M, INTJ, Taurus, Dating/Friends, My location Pune, My interests psychology gadgets philosophy memes dogs\n\n        Match: Kate, 39 F, INFJ, Virgo, Dating/Friends, Their location Indonesia, Their interests animals relationshipadvice psychology\n\n        Ensure the response is in language \"ja\"",
    "max_tokens": 1024,
    "min_tokens": 0,
    "temperature": 0.6,
    "prompt_template": "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are a helpful assistant<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>",
    "presence_penalty": 1.15,
    "frequency_penalty": 0.2
  },
  "logs": null,
  "metrics": {
    "total_time": 3.687337,
    "input_token_count": 128,
    "tokens_per_second": 25.958289612715852,
    "output_token_count": 94,
    "predict_time": 3.682803,
    "time_to_first_token": 0.075211082
  },
  "output": [
    "{\n",
    " ",
    " \"",
    "output",
    "\":",
    " [\n",
    "   ",
    " \"\",\n",
    "   ",
    " \"\",\n",
    "   ",
    " \"\",\n",
    "   ",
    " \"\"\n",
    " ",
    " ]\n",
    "}\n\n",
    "Here",
    " is",
    " the",
    " response",
    ":\n\n",
    "{\n",
    " ",
    " \"",
    "output",
    "\":",
    " [\n",
    "   ",
    " \"",
    "あ",
    "なた",
    "は",
    "イン",
    "ド",
    "ネ",
    "シア",
    "で",
    "何",
    "を",
    "している",
    "？",
    "\"\n",
    " ",
    " ]\n",
    "}",
    ""
  ],
  "started_at": "2024-05-03T13:32:20.123534Z",
  "status": "succeeded",
  "urls": {
    "stream": "https://streaming-api.svc.us.c.replicate.net/v1/streams/vg7ag7oon4ekuc5hs54arhae6ei6or24ecwqebnb2eawlt742bka",
    "get": "https://api.replicate.com/v1/predictions/a9np4byz2xrgj0cf7tktkgw1g4",
    "cancel": "https://api.replicate.com/v1/predictions/a9np4byz2xrgj0cf7tktkgw1g4/cancel"
  },
  "version": "9d8bfb582119f963f94e29ea485fa1b36aba45c4dbb45a4a9a04337f24605f87"
}
*/
class ReplicateApiClient {
  constructor(apiToken) {
    this.apiToken = apiToken;
    this.baseUrl = 'https://api.replicate.com/v1/';
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async createSyncPrediction(data) {
    try {
      const response = await this.axiosInstance.post(data.path, data.payload, {
        headers: {
          ...this.axiosInstance.defaults.headers,
          Prefer: 'wait',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Sync prediction POST request failed: ${error}`);
    }
  }

  async createClipPrediction(data) {
    try {
      const response = await this.axiosInstance.post(data.path, data.payload);
      return response.data;
    } catch (error) {
      throw new Error(`Prediction POST request failed: ${error}`);
    }
  }

  async getPoseVerificationPrediction(input) {
    try {
      const requestData = { input: { ...input } };
      const response = await this.axiosInstance.post('deployments/boo-world/deployment-llava-trained/predictions', requestData);
      return response.data;
    } catch (error) {
      throw new Error(`Prediction POST request failed: ${error.message}`);
    }
  }

  async createPrediction(model, input) {
    try {
      const requestData = { input: { ...input } };
      const response = await this.axiosInstance.post(`models/${model}/predictions`, requestData);
      return response.data;
    } catch (error) {
      throw new Error(`Prediction POST request failed: ${error.message}`);
    }
  }

  async getPrediction(predictionId, waitMs = 3000) {
    try {
      const response = await this.axiosInstance.get(`predictions/${predictionId}`);
      if (response.data.status === 'processing' || response.data.status === 'starting') {
        await new Promise((resolve) => setTimeout(resolve, waitMs));
        return this.getPrediction(predictionId);
      }
      if (response.data.status === 'succeeded') {
        return response.data;
      }
      console.log(`Got unexpected result from prediction: ${response.data.status}: ${response.data.error}`);
      return {};
    } catch (error) {
      throw new Error(`Prediction GET request failed: ${error.message}`);
    }
  }
}

if (process.env.REPLICATE_KEY) {
  replicate = new ReplicateApiClient(process.env.REPLICATE_KEY);
}

function getReplicateApiClient() {
  return replicate;
}

module.exports = {
  getReplicateApiClient,
};
