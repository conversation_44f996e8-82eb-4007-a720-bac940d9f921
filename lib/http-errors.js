class HttpError extends Error {
  constructor(status, message) {
    super(message);
    this.status = status || 500;
  }
}

const stockError = (statusCode, defaultMessage) => (message = defaultMessage) => new HttpError(statusCode, message);

const badRequestError = stockError(400, 'Malformed request');
const unauthenticatedError = stockError(401, 'It appears we are facing temporary issues. If this error continues, please try logging out and logging in again.');
const forbiddenError = stockError(403, 'Forbidden');
const notFoundError = stockError(404, 'Not found');
const conflictError = stockError(409, 'Conflict');
const invalidInputError = stockError(422, 'Invalid input');
const applicationError = stockError(500, 'Something went wrong. Please try again.');
const requestTimeoutError = stockError(408, 'Request Timeout.');

module.exports = {
  HttpError,
  badRequestError,
  unauthenticatedError,
  forbiddenError,
  notFoundError,
  applicationError,
  conflictError,
  invalidInputError,
  requestTimeoutError,
};
