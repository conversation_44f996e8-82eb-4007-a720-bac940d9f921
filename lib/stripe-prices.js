const fs = require('fs');
const { parse } = require('csv-parse/sync');
const countryToCurrency = require('country-to-currency');
const geoip = require('geoip-lite');
const moment = require('moment');
const locationLib = require('../lib/location');
const curExLib = require('../lib/currency-exchange');
const premiumLib = require('../lib/premium');

const zeroDecimalCurrencies = [
  'bif', 'clp', 'djf', 'gnf', 'jpy', 'kmf', 'krw', 'mga',
  'pyg', 'rwf', 'ugx', 'vnd', 'vuv', 'xaf', 'xof', 'xpf',
];
const threeDecimalCurrencies = [
  'bhd', 'jod', 'kwd', 'omr', 'tnd',
];

const lookupKeys = [];
const months = [1, 3, 6, 12];
const variants = [0, 1, 2, 3];
for (const month of months) {
  for (const variant of variants) {
    lookupKeys.push(`infinity_m${month}_x${variant}`);
  }
}
lookupKeys.push('infinity_lifetime');

const consumableProducts = [];
for (let n of [1000, 4000, 10000]) {
  consumableProducts.push(`${n}_coins`);
}
for (let n of [3, 12, 50]) {
  consumableProducts.push(`super_love_${n}_v1`);
}
for (let n of [40, 100, 300, 1000]) {
  consumableProducts.push(`${n}_neurons`);
}
for (let n of [1, 5, 10]) {
  consumableProducts.push(`boost_${n}_v1`);
}

const validCurrencies = new Set();;
const specialCountries = new Set();;
const currencyOptions = {};

function convertPriceToStripe(price, currency) {
  let unit_amount = parseFloat(price.replace(/,/g, ''));
  if (threeDecimalCurrencies.includes(currency)) {
    unit_amount = 10 * Math.round(unit_amount * 100);
  } else if (zeroDecimalCurrencies.includes(currency)) {
    // no transformation needed
  } else {
    // two-decimal currencies
    unit_amount = unit_amount * 100;
  }
  return Math.round(unit_amount);
}

function convertPriceFromStripe(price, currency) {
  if (threeDecimalCurrencies.includes(currency)) {
    price = price / 1000;
  } else if (zeroDecimalCurrencies.includes(currency)) {
    // no transformation needed
  } else {
    // two-decimal currencies
    price = price / 100;
  }
  return price;
}


{
  const pathToCsv = __dirname + '/stripe-prices-currencies.csv';
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  for (const record of records) {
    const currency = record.currency.toLowerCase();
    validCurrencies.add(currency);
    for (const lookupKey of lookupKeys) {
      if (!currencyOptions[lookupKey]) {
        currencyOptions[lookupKey] = {};
      }
      currencyOptions[lookupKey][currency] = {
        unit_amount: convertPriceToStripe(record[lookupKey], currency),
        tax_behavior: 'exclusive',
      }
    }
  }
}

{
  const pathToCsv = __dirname + '/stripe-prices-countries.csv';
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  for (const record of records) {
    for (const lookupKey of lookupKeys) {
      const countryCode = locationLib.getCountryCode(record.country);
      specialCountries.add(countryCode);
      const countryLookupKey = `${countryCode}_${lookupKey}`;
      if (!currencyOptions[countryLookupKey]) {
        currencyOptions[countryLookupKey] = {};
      }
      currencyOptions[countryLookupKey]['usd'] = {
        unit_amount: convertPriceToStripe(record[lookupKey], 'usd'),
        tax_behavior: 'exclusive',
      }
      currencyOptions[countryLookupKey]['eur'] = {
        unit_amount: convertPriceToStripe(record[lookupKey], 'eur'),
        tax_behavior: 'exclusive',
      }
    }
  }
}

{
  const pathToCsv = __dirname + '/stripe-prices-consumable-currencies.csv';
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  for (const record of records) {
    const currency = record.currency.toLowerCase();
    validCurrencies.add(currency);
    for (const lookupKey of consumableProducts) {
      if (!currencyOptions[lookupKey]) {
        currencyOptions[lookupKey] = {};
      }
      currencyOptions[lookupKey][currency] = {
        unit_amount: convertPriceToStripe(record[lookupKey], currency),
        tax_behavior: 'exclusive',
      }
    }
  }
}

{
  const pathToCsv = __dirname + '/stripe-prices-consumable-countries.csv';
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });
  for (const record of records) {
    for (const lookupKey of consumableProducts) {
      const countryCode = locationLib.getCountryCode(record.country);
      specialCountries.add(countryCode);
      const countryLookupKey = `${countryCode}_${lookupKey}`;
      if (!currencyOptions[countryLookupKey]) {
        currencyOptions[countryLookupKey] = {};
      }
      currencyOptions[countryLookupKey]['usd'] = {
        unit_amount: convertPriceToStripe(record[lookupKey], 'usd'),
        tax_behavior: 'exclusive',
      }
      currencyOptions[countryLookupKey]['eur'] = {
        unit_amount: convertPriceToStripe(record[lookupKey], 'eur'),
        tax_behavior: 'exclusive',
      }
    }
  }
}

function getCurrencyOptions(lookupKey) {
  return currencyOptions[lookupKey];
}

function getAllCurrencyOptions() {
  return currencyOptions;
}

function hasCountryPrice(countryCode) {
  return specialCountries.has(countryCode);
}

function getCurrency(countryCode) {
  let currency = (countryToCurrency[countryCode] || 'usd').toLowerCase();
  if (!validCurrencies.has(currency)) {
    currency = 'usd';
  }
  return currency;
}

function getPrice(lookup_key, currency) {
  const currency_options = getCurrencyOptions(lookup_key);
  let price = currency_options[currency].unit_amount;
  return convertPriceFromStripe(price, currency);
}

async function getPricingConfig(user, ip) {
  const countryCode = getCountryCode(ip, user);
  const variant = getVariant(user);
  const discount = premiumLib.getPremiumFlashSale(user)?.discount;
  const superLikeDiscount = (await premiumLib.getSuperLikeFlashSale(user))?.discount;
  const coinsDiscount = (await premiumLib.getCoinsFlashSale(user))?.discount;

  const currency = user.stripeCurrency || getCurrency(countryCode);

  let countryPrefix = '';
  if (hasCountryPrice(countryCode)) {
    countryPrefix = countryCode + '_';
  }

  let rv = {
    currency,
  };

  for (let product of ['m1', 'm3', 'm6', 'm12', 'lifetime']) {

    let lookup_key = `${countryPrefix}infinity_${product}`;
    if (product != 'lifetime') {
      lookup_key = lookup_key + variant;
    }

    let price = getPrice(lookup_key, currency);
    let productConfig = { price };

    let usdValue = price * curExLib.getExchangeData()[currency.toUpperCase()];
    if (usdValue < 2) {
      productConfig.unavailable = true;
    }

    if (discount && ['m3', 'm6', 'm12'].includes(product)) {
      if (usdValue * discount / 100 >= 2) {
        productConfig.discount = discount;
      }
    }

    rv[product] = productConfig;
  }

  for (let product of consumableProducts) {
    let lookup_key = `${countryPrefix}${product}`;

    let price = getPrice(lookup_key, currency);
    let productConfig = { price };

    let usdValue = price * curExLib.getExchangeData()[currency.toUpperCase()];
    if (usdValue < 2) {
      productConfig.unavailable = true;
    }

    if (superLikeDiscount && product.includes('super_love')) {
      if (usdValue * superLikeDiscount / 100 >= 2) {
        productConfig.discount = superLikeDiscount;
      }
    }

    if (coinsDiscount && product.includes('coins')) {
      if (usdValue * coinsDiscount / 100 >= 2) {
        productConfig.discount = coinsDiscount;
      }
    }

    rv[product] = productConfig;
  }

  return rv;
}

function getCountryCode(ip, user) {

  const geo = geoip.lookup(ip);
  if (geo && geo.country) {
    return geo.country;
  }

  if (user.timezone) {
    const country = locationLib.getCountryNameFromTimezone(user.timezone);
    const countryCode = locationLib.getCountryCode(country);
    return countryCode;
  }

  return 'US';
}

function getVariant(user) {
  let variant = '_x0';
  if (moment().diff(user.birthday, 'years') <= 23) {
    variant = '_x3';
  } else {
    if (user.preferences.dating.length > 0 && user.preferences.friends.length == 0) {
      variant = '_x1';
    }
    if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
      variant = '_x2';
    }
  }
  return variant;
}

module.exports = {
  getCurrencyOptions,
  getAllCurrencyOptions,
  hasCountryPrice,
  zeroDecimalCurrencies,
  getPricingConfig,
  convertPriceFromStripe,
  getCountryCode,
  getVariant,
}
