const fs = require('fs');
const { parse } = require('csv-parse/sync');
const mongoose = require('mongoose');
const Profile = require('../models/profile');
const { getHoroscope } = require('../lib/horoscope');
const { createSlug } = require('../lib/url');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const FILE = process.env.FILE || 'profiles.csv';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const input = fs.readFileSync(FILE);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });

  let i = 0;
  let bulk = Profile.collection.initializeUnorderedBulkOp();

  for (const record of records) {
    try {
      let query = { slug: createSlug(record.name), subcategories: JSON.parse(record.subcategories) };
      let update = {};
      if (record.mbti && record.mbti != 'x') {
        update.pdb_mbti = record.mbti;
      }
      if (record.enneagram && record.enneagram != 'x') {
        update.pdb_enneagram = record.enneagram;
      }
      if (record.birthday) {
        update.pdb_horoscope = getHoroscope(new Date(record.birthday));
      }
      if (record.confidence_score) {
        update.pdb_confidence = parseInt(record.confidence_score.replace('%',''));
      }

      console.log(query, update);
      bulk.find(query).update({ $set: update });
      i++;
      if (i % 100 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Profile.collection.initializeUnorderedBulkOp();
      }
    } catch (err) {
      console.log(`Error processing record: ${record}, err: ${err}`);
    }
  }

  const res = await bulk.execute();
  console.log(i, res);

  await mongoose.disconnect();
})();
