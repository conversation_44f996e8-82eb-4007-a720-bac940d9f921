const fs = require('fs');
const { parse } = require('csv-parse/sync');
const mongoose = require('mongoose');
const Profile = require('../models/profile');
const { getHoroscope } = require('../lib/horoscope');
const { createSlug } = require('../lib/url');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const FILE = process.env.FILE || 'profiles.csv';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const input = fs.readFileSync(FILE);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });

  const newProfiles = [];

  for (const record of records) {
    let profile;
    if (!record.edited) {
      continue;
    }
    const name = record.oldName || record.name;
    console.log('Edited profile', name);
    profile = await Profile.findOne({ slug: createSlug(name) }, '-intros');
    if (!profile) {
      console.log('Could not find profile', name);
      continue;
    }
    profile.name = record.name;
    profile.slug = createSlug(record.name);
    profile.subcategories = JSON.parse(record.subcategories);
    /*
    profile.description = record.description;
    profile.mbti = record.mbti;
    profile.enneagram = record.enneagram;
    if (record.birthday) {
      profile.birthday = new Date(record.birthday);
      profile.horoscope = getHoroscope(new Date(record.birthday));
    }
    */
    if (!process.env.DRY_RUN) {
      await profile.save();
    }
    console.log(profile);
  }

  await mongoose.disconnect();
})();
