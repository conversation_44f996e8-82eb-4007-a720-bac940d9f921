const fs = require('fs').promises;
const { parse } = require('csv-parse/sync');
const { stringify } = require('csv-stringify/sync');
const { locales } = require('../lib/translate');
const { mapLanguageCodeToName } = require('../lib/languages');
const openaiClient = require('../lib/openai-client');
const { OpenAI } = require('../lib/prompt');
const getlanguageSpecificPrompt = require('../lib/getLanguageSpecificPrompt');

const CITIES_FILE = `${__dirname}/../lib/cities_translated.csv`;
const REGIONS_FILE = `${__dirname}/../lib/regions_translated.csv`;
const langCodeAliases = { he: 'iw', jv: 'jw' };

const translate = async (prompt) => {
  try {
    const client = openaiClient.getOpenaiClient();
    const provider = new OpenAI(client, 'gpt-4-turbo');
    let params = { prompt };
    const response = await provider.executePrompt(params);
    return response.output.replace(/\n/g, '').replace(/"/g, '').trim();
  } catch (error) {
    console.log('Error occurred during translation: ', error.message, prompt);
    return '';
  }
};

const processLine = async (line) => {
  const promises = [];
  for (const languageCode of locales) {
    if (languageCode && !line[languageCode]) {
      const nameCode = langCodeAliases[languageCode] || languageCode;
      const language = mapLanguageCodeToName(nameCode);
      const prompt = getlanguageSpecificPrompt(languageCode, language, line.en);
      const translationPromise = (async () => {
        const translated = await translate(prompt);
        line[languageCode] = translated;
        console.log(`Translation of ${line.en} to ${language} complete.`);
      })();
      promises.push(translationPromise);
    }
  }
  await Promise.all(promises);
  return line;
};

const translateAndWriteToFile = async (inputFile, outputFile, limit) => {
  try {
    const content = await fs.readFile(inputFile, 'utf8');
    const lines = parse(content, { delimiter: ',', columns: true, skip_empty_lines: true });

    limit = limit && limit < lines.length ? limit : lines.length;
    for (let i = 0; i < limit; i++) {
      lines[i] = await processLine(lines[i]);
      const output = stringify(lines, { header: true });
      await fs.writeFile(outputFile, output);
    }
  } catch (error) {
    console.log('An error occurred: ', error.message);
  }
};

translateAndWriteToFile(CITIES_FILE, CITIES_FILE);
translateAndWriteToFile(REGIONS_FILE, REGIONS_FILE);
