require('dotenv').config();
const mongoose = require('mongoose');
const basicLib = require('../lib/basic');
const interestLib = require('../lib/interest');
const Interest = require('../models/interest');
const { locales } = require('../lib/translate');
const { sexualOrientationMap } = require('../lib/interest-dating-preferences');

// Database configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const langCodeAliases = { he: 'iw', jv: 'jw' };

function createValueArrayOfObject(map) {
  return [...new Set(Object.values(map).flat())];
}

async function insertInterests(interests, locale) {
  try {
    const existingNames = await Interest.distinct('name', { name: { $in: interests } });
    const newNames = basicLib.removeDuplicates(interests.filter((x) => !existingNames.includes(x)));
    console.log(`locale: ${locale}\ninterests: ${interests}\nexistingNames: ${existingNames}\nnewNames: ${newNames}\n\n`);
    let docs = [];
    if (locale) {
      docs = newNames.map((name) => ({
        name,
        interest: `#${name}`,
        language: langCodeAliases[locale] || locale,
      }));
    } else {
      docs = newNames.map((name) => ({
        name,
        interest: `#${name}`,
      }));
    }
    if (process.env.INSERT) {
      await Interest.insertMany(docs);
      await Interest.updateMany({ name: { $in: interests } }, { $unset: { status: 1 } });
    }
  } catch (error) {
    console.log('Error inserting interests', error);
  }
}

function collectInterestsForLocale(locale) {
  const interests = [];
  if (locale) {
    interests.push(...createValueArrayOfObject(sexualOrientationMap).map((x) => interestLib.i18n_interests.__({ phrase: x, locale })));
  }
  return interests.map(interestLib.cleanInterestName);
}

async function processInterests() {
  for (const locale of locales) {
    const translatedInterests = collectInterestsForLocale(locale);
    if (translatedInterests.length) {
      await insertInterests(translatedInterests, locale);
    }
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to database');
    await processInterests();
  } catch (error) {
    console.log('Error connecting to the database', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
})();
