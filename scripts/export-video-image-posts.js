const fs = require('fs');
const mongoose = require('mongoose');
const { stringify } = require('csv-stringify/sync');
const Question = require('../models/question');
const { generateQuestionUrl } = require('../lib/url');
const rekognitionLib = require('../lib/rekognition');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';


(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  {
    const data = await Question.find(
      {
        isVideo: true,
        banned: { $ne: true },
        interestName: { $ne: 'memes' },
        createdAt: {
          $gt: new Date('2023-08-01T00:00:00'),
          $lt: new Date('2023-08-31T00:00:00'),
        },
      },
      'interestName webId title text createdAt createdBy image language',
    ).limit(100).lean()
    for (let i = 0; i < data.length; i++) {
      const question = data[i];
      question.url = generateQuestionUrl(question);
    }
    let output = stringify(data, {header: true});
    let outputFile = 'videos.csv';
    fs.writeFileSync(outputFile, output);
  }

  {
    const hasText = [];
    const noText = [];
    const cursor = Question.find(
      {
        image: { $exists: true },
        isVideo: { $ne: true },
        banned: { $ne: true },
        interestName: { $ne: 'memes' },
        createdAt: {
          $gt: new Date('2023-08-01T00:00:00'),
          $lt: new Date('2023-08-31T00:00:00'),
        },
      },
      'interestName webId title text createdAt createdBy image language',
    )
    for await (const doc of cursor) {
      const imageHasText = await rekognitionLib.containsText(doc.image);
      doc.imageHasText = imageHasText;
      await doc.save();
      const question = doc.toObject();
      question.url = generateQuestionUrl(question);
      if (imageHasText) {
        hasText.push(question);
      } else {
        noText.push(question);
      }
      if (hasText.length >= 100 && noText.length >= 100) {
        break;
      }
    }
    {
      let output = stringify(hasText, {header: true});
      let outputFile = 'hasText.csv';
      fs.writeFileSync(outputFile, output);
    }
    {
      let output = stringify(noText, {header: true});
      let outputFile = 'noText.csv';
      fs.writeFileSync(outputFile, output);
    }
  }

  await mongoose.disconnect();
})();
