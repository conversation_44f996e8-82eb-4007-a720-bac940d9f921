const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const Question = require('../models/question');
const { generateQuestionUrl } = require('../lib/url');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

const ids = [
'64468bd7d02726c6430ce86d',
 '64469374874735e9ee0f4184',
 '64468c28184039d9585dd40f',
 '644690ae874735e9ee0e10a4',
 '64468a8e322aae864ece81b7',
 '64468a46486bff6a9e46a8ce',
 '64466349fd0b55b3a2fa127e',
 '64466b559057ee63f419a9b4',
 '6446894f486bff6a9e464478',
 '644689fd2d3677ec490ccc44',
 '64466edd285e989dc7b7a221',
 '64467224a7bc0ea4933da553',
 '644690e87b71dbc2b547877e',
 '64468575165ef8ef040e55ae',
 '64467f6247fae80d67faff9b',
 '644666f7199d7f3fc6e1d705',
 '6446811064a24c23d11fff21',
 '64467296735056643a9e5c6f',
 '64466b50eee6d5861cc40792',
 '644689ee35c0368277ddbc8b'
];

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const questions = await Question.find({_id: {$in: ids}});
  const urls = questions.map(x => generateQuestionUrl(x));
  console.log(urls);
})();
