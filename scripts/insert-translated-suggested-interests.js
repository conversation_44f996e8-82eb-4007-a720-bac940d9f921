require('dotenv').config();
const mongoose = require('mongoose');
const basicLib = require('../lib/basic');
const interestLib = require('../lib/interest');
const Interest = require('../models/interest');
const { languageCodes } = require('../lib/languages');
const { translate, locales, translate_frontend } = require('../lib/translate');
const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { moreAboutUserChoices, relationshipTypeChoices } = require('../lib/moreAboutUser');
const { datingPreferencesMap, relationshipStatusMap } = require('../lib/interest-dating-preferences');
const { ethnicities } = require('../lib/ethnicities');

// Database configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const langCodeAliases = { he: 'iw', jv: 'jw' };

function createValueArrayOfObject(map) {
  return [...new Set(Object.values(map).flat())];
}

async function insertInterests(interests, locale) {
  try {
    const existingNames = await Interest.distinct('name', { name: { $in: interests } });
    const newNames = basicLib.removeDuplicates(interests.filter((x) => !existingNames.includes(x)));
    console.log(`locale: ${locale}\ninterests: ${interests}\nexistingNames: ${existingNames}\nnewNames: ${newNames}\n\n`);
    let docs=[]
    if(locale){
       docs = newNames.map((name) => ({
        name,
        interest: `#${name}`,
        language: langCodeAliases[locale] || locale,
      }));
    }else{
       docs = newNames.map((name) => ({
        name,
        interest: `#${name}`,
      }));
    }
    if (process.env.INSERT) {
      await Interest.insertMany(docs);
      await Interest.updateMany({ name: { $in: interests } }, { $unset: { status: 1 } });
    }
  } catch (error) {
    console.error('Error inserting interests', error);
  }
}

function collectInterestsForLocale(locale) {
  const interests = [];
  if (locale) {
    interests.push(...horoscopes.map((x) => interestLib.i18n_interests.__({ phrase: interestLib.cleanInterestName(x), locale })));
    interests.push(...moreAboutUserChoices.religion.map((x) => translate_frontend(x, locale)));
    interests.push(...ethnicities.map((x) => interestLib.getEthnicityTranslation(x, locale)));
    interests.push(translate_frontend('Dating', locale));
    interests.push(translate_frontend('Friends', locale));
    interests.push(translate_frontend('Divorced', locale));
    interests.push(translate_frontend('Widowed', locale));
    interests.push(...createValueArrayOfObject(datingPreferencesMap).map((x) => interestLib.i18n_interests.__({ phrase: x, locale })));
    interests.push(...createValueArrayOfObject(relationshipStatusMap).map((x) => interestLib.i18n_interests.__({ phrase: x, locale })));
    interests.push(...relationshipTypeChoices.map((x) => translate_frontend(x, locale)));  
  } else {
    interests.push(...validMbti);
    interests.push(...enneagrams);
  }
  return interests.map(interestLib.cleanInterestName);
}

async function processInterests() {
  for (const locale of locales) {
    const translatedInterests = collectInterestsForLocale(locale);
    if (translatedInterests.length) {
      await insertInterests(translatedInterests, locale);
    }
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to database');
    await processInterests();
  } catch (error) {
    console.error('Error connecting to the database', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
})();
