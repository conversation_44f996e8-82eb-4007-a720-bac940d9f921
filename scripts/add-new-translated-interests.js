/* eslint-disable no-continue */
const fs = require('fs');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const mongoose = require('mongoose');
const Interest = require('../models/interest');
const { languageCodes } = require('../lib/languages');
const { locales } = require('../lib/translate');
const { i18n_interests } = require('../lib/interest');

const inputFile = `${__dirname}/../lib/onboarding_interests.csv`;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const BATCH_SIZE = 5000;
const insertNewLocalesOnly = false; // Set to true to create all onbaording interests for new locales only

const newLocales = [
  'as',
  'be',
  'bs',
  'ceb',
  'co',
  'cy',
  'eu',
  'fy',
  'ga',
  'gu',
  'ha',
  'hmn',
  'hr',
  'ht',
  'ig',
  'is',
  'jv',
  'kn',
  'km',
  'ku',
  'ky',
  'lb',
  'lv',
  'mg',
  'mi',
  'mk',
  'mn',
  'mt',
  'my',
  'ne',
  'ny',
  'ps',
  'rw',
  'sd',
  'sm',
  'sn',
  'so',
  'st',
  'su',
  'ta',
  'tg',
  'tk',
  'tt',
  'ug',
  'uz',
  'xh',
  'yi',
  'yo',
  'zu',
];

const batchCreateOrUpdateInterests = async (interests) => {
  try {
    const bulkOps = interests.map((item) => ({
      updateOne: {
        filter: { name: item.name, interest: item.interest },
        update: { $setOnInsert: item, $unset: { status: 1 } },
        upsert: true,
      },
    }));

    const result = await Interest.bulkWrite(bulkOps);
    const { upserted, ...rest } = result.result || {};
    console.log('Bulk operation result:', rest);
  } catch (error) {
    console.log('Error during batch operation:', error);
  }
};

const batchInsertNewInterests = async (interests) => {
  try {
    const result = await Interest.insertMany(interests, { ordered: false });
    console.log(`Inserted ${result.length} new interests. No duplicates.`);
  } catch (error) {
    const isDuplicateError = error.name === 'BulkWriteError' || error.name === 'MongoBulkWriteError' || error.code === 11000;

    if (isDuplicateError) {
      const insertedCount = error.result?.insertedCount ?? 0;
      const duplicateCount = error.writeErrors?.length ?? 0;

      console.log(`Inserted ${insertedCount} new interests. Skipped ${duplicateCount} duplicates.`);
    } else {
      console.log('Unexpected error during insertMany:', error);
    }
  }
};

(async () => {
  try {
    await mongoose.connect(MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    console.log('Connected to MongoDB');
    const fileContent = fs.readFileSync(inputFile);
    const lines = parse(fileContent, {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
      relax_quotes: true,
    });

    const records = [];
    const langCodeAliases = { he: 'iw', jv: 'jw' };

    for (const line of lines) {
      if (!line.sortIndex && !insertNewLocalesOnly) {
        records.push({
          interest: `#${line.name}`,
          name: line.name,
          category: line.category,
          libCategory: line.category === 'Games' ? 'gaming' : 'anime',
        });
        console.log(`Adding interest: ${line.name}`);
        for (const locale of locales) {
          if (locale && languageCodes.includes(langCodeAliases[locale] || locale) && locale !== 'en') {
            records.push({
              interest: `#${i18n_interests.__({ phrase: line.name, locale })}`,
              name: i18n_interests.__({ phrase: line.name, locale }),
              language: langCodeAliases[locale] || locale,
            });
          }
        }
      }

      if (insertNewLocalesOnly) {
        for (const locale of newLocales) {
          const name = i18n_interests.__({ phrase: line.name, locale });
          if (name === line.name) continue;

          records.push({
            interest: `#${name}`,
            name,
            language: langCodeAliases[locale] || locale,
          });
          console.log(`Adding new interest for locale: ${locale}, Main interest: ${line.name}, Translated Interest: ${name}`);
        }
      }
    }

    console.log(`Total records to insert: ${records.length}`);
    for (let i = 0; i < records.length; i += BATCH_SIZE) {
      const batch = records.slice(i, i + BATCH_SIZE);
      if (insertNewLocalesOnly) {
        await batchInsertNewInterests(batch);
      } else {
        await batchCreateOrUpdateInterests(batch);
      }
      console.log(`Inserted batch of ${batch.length} documents, progress: ${i + batch.length}/${records.length}`);
    }
    console.log('Data inserted successfully.');
  } catch (error) {
    console.log('Error occurred during batch insert:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
})();
