const fs = require('fs');
const { parse } = require('csv-parse/sync');
const mongoose = require('mongoose');
const Subcategory = require('../models/subcategory');
const { createSlug } = require('../lib/url');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const FILE = process.env.FILE || 'subcategories.csv';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const input = fs.readFileSync(FILE);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });

  const newDocs = [];

  for (const record of records) {
    let doc;
    if (record.edited) {
      console.log('Edited doc');
      doc = await Subcategory.findOne({ id: record.id }, '-intros');
    }
    if (record.new) {
      console.log('New doc');
      doc = new Subcategory();
    }
    if (doc) {
      doc.id = record.id;
      doc.name = record.name;
      doc.slug = createSlug(doc.name);
      doc.category = record.category;
      if (record.new) {
        newDocs.push(doc);
      } else {
        if (!process.env.DRY_RUN) {
          await doc.save();
        }
      }
      console.log(doc);
    }
  }

  console.log('New docs: ', newDocs.length);
  if (!process.env.DRY_RUN) {
    await Subcategory.insertMany(newDocs);
  }

  await mongoose.disconnect();
})();
