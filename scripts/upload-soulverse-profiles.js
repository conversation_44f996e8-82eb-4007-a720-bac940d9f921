const fs = require('fs');
const { parse } = require('csv-parse/sync');
const mongoose = require('mongoose');
const Profile = require('../models/profile');
const { getHoroscope } = require('../lib/horoscope');
const { createSlug } = require('../lib/url');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const FILE = process.env.FILE || 'profiles.csv';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const input = fs.readFileSync(FILE);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });

  const newProfiles = [];

  for (const record of records) {
    let profile;
    if (!record.new) {
      continue;
    }
    console.log('New profile');
    profile = new Profile();
    profile.id = record.id;
    profile.name = record.name;
    profile.slug = createSlug(record.name);
    profile.gender = record.gender;
    profile.description = record.description;
    profile.mbti = record.mbti;
    profile.enneagram = record.enneagram;
    profile.subcategories = JSON.parse(record.subcategories);
    if (record.birthday) {
      profile.birthday = new Date(record.birthday);
      profile.horoscope = getHoroscope(new Date(record.birthday));
    }
    newProfiles.push(profile);
    console.log(profile);
  }

  console.log('New profiles: ', newProfiles.length);
  if (!process.env.DRY_RUN) {
    await Profile.insertMany(newProfiles);
  }

  await mongoose.disconnect();
})();