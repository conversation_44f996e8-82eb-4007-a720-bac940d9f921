FORMAT: 1A
HOST: http://polls.apiblueprint.org/

# Boo

Boo API


## User [/user]

### Get user info [GET /v1/user/]

No longer used as of app version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "_id": "inClQpt6V3e5stEcbKNSEd06Bim2dk",
            "firstName": "baf1a184fe20c3",
            "anonymousProfileNickname": "Nickname", // if undefined, nickname not set
            "birthday": "1999-12-30T08:07:42.359Z",
            "age": 30,
            "horoscope": "Capricorn",
            "gender": "male",
            "description": "Sample description",
            "education": "college",
            "work": "company",
            "location": "Brooklyn, NY",
            "teleport": true,
            "actualLocation": "San Diego, CA",
            "prompts": [
                {
                    "id": "id",
                    "prompt": "prompt",
                    "answer": "answer"
                }
            ],
            "crown": true,
            "handle": "handle",
            "searchable": true,
            "hidden": false,
            "relationshipStatus":"Single",
            "datingSubPreferences":"Undecided",
            "relationshipType":"Monogamous",
            "preferences": {
                "personality": [ "ESFP" ],
                "gender": ["female"],
                "distance": 40,
                "minAge": 18,
                "maxAge": 30
                "relationshipStatus": ["Single"],
                "datingSubPreferences":["Short term fun", "Long term partner"],
                "relationshipType":["Monogamous", "Polyamorous"],
                "excludedInterestNames": [ "chess" ],
                "showUnspecified": {
                    "datingSubPreferences": Boolean,
                    "relationshipStatus": Boolean,
                    "relationshipType": Boolean,
                    "sexuality": Boolean,
                    "enneagrams": Boolean,
                    "ethnicities": Boolean,
                    "drinking": Boolean,
                    "educationLevel": Boolean,
                    "exercise": Boolean,
                    "kids": Boolean,
                    "religion": Boolean,
                    "smoking": Boolean,
                },
                "showUsersOutsideMyRange": Boolean
            },
            "pictures": [
                "samples/sample1.jpg"
            ],
            "personality": {
                "mbti": "ESFP",
                "avatar": "Performer"
            },
            "deletionRequestDate": "1999-12-30T08:07:42.359Z",
            "premium": true,
            "coins": 200,
            "interplanetaryMode": true,
            "showFreeTrialOption": false,
            "premiumDiscount": 50,  // if undefined, no discount
            "premiumFlashSale": {   // if undefined, no flash sale
                "discount": 60,
                "saleEndDate": "2020-12-30T12:00:00.000Z"
            },
            "numDbUploads": 1,
            "dbUploadCoinsReceived": 10,
            "dbUploadKarmaReceived": 10,
        }

### Get user personality [GET /v1/user/personality]

No longer used as of app version 1.5+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "mbti": "ENTJ",
            "avatar": "Achiever",
            "description": "You’re a master of efficiency; formidable, confident, and a force to be reckoned with. You’re at your best at the forefront, taking on life’s challenges head on and moving the world forward. But at your worst, you can be stubborn, intolerant, impatient, and out of touch with your feelings. You’ll want someone who sees the world as you do and loves you for your best, but also understands and forgives you at your worst, and would have it no other way."
        }

+ Response 404 (text/plain)

        You have not taken the personality test yet

### Get quiz questions [GET /v1/user/quizQuestions]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "questions": {
                "EbzFD": "Rather than doing a lot in your spare time, you enjoy hanging around doing nothing.",
                "orju0": "You tend to initiate conversations and keep them going rather than rely on others.",
                ...
            }
        }

### Get short quiz questions [GET /v1/user/shortQuizQuestions]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "questions": {
                "pnpBs": {
                    "You talk to a lot of different people at parties.": 1,
                    "You don't like to draw attention to yourself and keep to the background.": 0
                },
                "yXX0y": {
                    "If your friend is sad about something, your first instinct is to support them emotionally, not try to solve their problem.": 1,
                    "You dislike talking about your feelings and emotional expression.": 0
                }
            }
        }

### Submit quiz answers [PUT /v1/user/quizAnswers]

The "description" field is no longer used as of app version 1.5+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "answers": {
                    "EbzFD": 0.2,
                    "orju0": 0.5,
                    ...
                }
            }

+ Response 200 (application/json)

        {
            "mbti": "ENFP",
            "avatar": "Crusader",
            "EI": 0.6,
            "NS": 0.6,
            "FT": 0.6,
            "JP": 0.6,
            "description": "You're a true free spirit, energetic, enthusiastic, and full of wonder. But at your worst, you can neglect details, you get stressed easily, and you may get overemotional. You’ll want someone who sees the world as you do and loves you for your best, but also understands and forgives you at your worst, and would have it no other way."
        }

### Get recommended personalities [GET /v1/user/recommendedPersonalities]

The "description" field is no longer used as of app version 1.5+

No longer used as of version 1.10.13+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "introduction": "These are the personalities you're most likely to love, and most likely to love you for who you are. They're all people you can talk to for hours on end on anything and everything, and share what you see, a world full of limitless possibilities. Pick any or all to match with them.",
            "personalities": [
                {
                    "mbti": "INTJ",
                    "description": "<p>Strategic and intuitive problem solvers. They're confident, independent, decisive, capable, and thorough planners. But at their worst, they can be arrogant, judgmental, overly analytical, and clueless in romance.</p><p><b>What You'll Love:</b> You'll love that you can seemingly talk to them for hours on end about anything and be so alike in values. You'll do most of the talking of course and you'll love that they are happy to listen. You care about efficiency, you can count on them to ensure that shit gets handled, well. You'll be their social butterfly they'll be your rock. Because they tend to shy away from the spotlight, they're happy to be the sidekick and support you in your grand endeavors, if they think you're capable.</p><p><b>How They're Different:</b> They differ from INFPs in that they typically think of things from a more logical standpoint, choosing logic and reasoning over feelings, not that they don't have feelings, it's that sometimes, like yourself, they may neglect this part of the equation. They differ from INTPs in that they also like to be in control, from making decisions, planning their lives, being organized, etc.</p><p><b>Potential Problems:</b> Like you, they can sometimes be stubborn, and because they also believe in their opinions strongly, you may sometimes fight for control or authority, and will need to compromise and let the other person steer the wheel sometimes for the relationship to work.</p>",
                    "avatar": "Strategist"
                }
            ]
        }

### Get three-tier personality recommendations [GET /v1/user/threeTierPersonalityRecommendations]

No longer used as of version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "recommended": [
                "INTJ"
            ],
            "potential": [
                "ESTJ"
            ],
            "other": [
                "INFP"
            ],
            "recommendedHelpText": "text"
        }

### Get user preferences [GET /v1/user/preferences]

No longer used as of app version ???

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "personality": [
                "INTJ"
            ],
            "gender": "female",
            "distance": 40,      // 12,500 indicates Infinity for premium users
            "minAge": 18,
            "maxAge": 30,
            "relationshipStatus": ["Single"],
            "datingSubPreferences":["Short term fun", "Long term partner"],
            "relationshipType":["Monogamous", "Polyamorous"],
            "showUsersOutsideMyRange": Boolean,
            "excludedInterestNames": [ "chess" ],
            "showUnspecified": {
                "datingSubPreferences": Boolean,
                "relationshipStatus": Boolean,
                "relationshipType": Boolean,
                "sexuality": Boolean,
                "enneagrams": Boolean,
                "ethnicities": Boolean,
                "drinking": Boolean,
                "educationLevel": Boolean,
                "exercise": Boolean,
                "kids": Boolean,
                "religion": Boolean,
                "smoking": Boolean,
            },
            "purpose": [
                "dating",
                "friends"
            ]
        }

### Get profile prompts [GET /v1/user/profilePrompts]

No longer used as of app version 1.11.42

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "prompts": [
                {
                    "id": "id",
                    "prompt": "prompt",
                    "exampleAnswer": "exampleAnswer"
                }
            ]
        }

### Submit profile prompt answers [PUT /v1/user/profilePromptAnswers]

Use this route for adding, editing, or reording answers.
Submit all answers in order (up to a maximum of 3).

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt", # use prompt for AI generated
                        "answer": "answer"
                    }
                ]
            }

+ Response 200 (application/json)

        {
        }

### Set custom personality compatibility [PUT /v1/user/customPersonalityCompatibility]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "customPersonalityCompatibility": {
                    "compatible": [
                        'INTJ',
                    ],
                    "potential": [
                        'ENTP',
                    ],
                    "challenging": [
                        'ENTJ',
                    ],
                }
            }

+ Response 200 (application/json)

        {
        }

### Upload profile verification picture [POST /v1/user/profileVerificationPicture]

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
        }

### Upload profile verification payload captured via Yoti module [POST /v1/user/profileVerificationPicture/liveness]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

        {
            "img": "base-64-image-string",
            "secure": {
                "version": "String",
                "token": "String",
                "verification": "String",
                "signature": "String"
            },
        }

+ Response 200 (application/json)

        {
            "verificationStatus": "verified/rejected",
            "rejectionReason": "String", # Optional
        }

### Check Yoti verification eligibility [POST /v1/user/profileVerificationPicture/liveness/eligibility]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "eligible": "true/false"
        }


### Upload picture [POST /v1/user/picture/v2]

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            "pictures": [
                "sample/sample.jpg"
            ],
            "coinReward": [
                {
                    "caption": "Referral Reward",
                    "rewardAmount": 500,
                    "newTotal": 650
                },
                {
                    "caption": "Upload 6 Pictures Reward",
                    "rewardAmount": 50,
                    "newTotal": 700
                },
            ]
        }

+ Response 422 (text/plain)

        Your picture could not be saved due to inappropriate content: Explicit Nudity


### Upload picture [POST /v1/user/picture]

No longer used as of app version 1.7+

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        [
            "sample/sample.jpg"
        ]

+ Response 422 (text/plain)

        Your picture could not be saved due to inappropriate content: Explicit Nudity

### Edit picture [POST /v1/user/editPicture?id={id}&batchId={batchId}]

+ Parameters
    + id (string)
    + batchId (string) #optional

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        [
            "sample/sample.jpg"
        ]

+ Response 422 (text/plain)

        Your picture could not be saved due to inappropriate content: Explicit Nudity

+ Response 404 (text/plain)

        The picture you are trying to replace could not be found

### Upload profile video [POST /v1/user/profileVideo]

Video file should be less than 30 seconds long, less than 50 MB in size.

Accepted video formats:
mp4
webm
avi
flv
mkv
mpg
wmv
mov

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="video"

+ Response 200 (application/json)

        {
            "pictures": [
                "sample/sample.mov"
            ],
            "coinReward": [
                {
                    "caption": "Upload 6 Pictures Reward",
                    "rewardAmount": 50,
                    "newTotal": 700
                },
            ]
        }

+ Response 422 (text/plain)

        There's an issue with this video. Please select another one.


### Delete picture [DELETE /v1/user/picture?id={id}]

+ Parameters
    + id (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        [
            "sample/sample.jpg"
        ]

### Reorder pictures [PUT /v1/user/reorderPictures]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "ids": [
                    "inClQpt6V3e5stEcbKNSEd06Bim2/n7qlok2m6fqdoendbrch",
                    "sample"
                ]
            }

+ Response 200 (application/json)

        [
            "sample/sample.jpg"
        ]

### Update first name [PUT /v1/user/firstName]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "firstName": "Tom"
            }

+ Response 200 (application/json)

        {
            "firstName": "Tom"
        }

### Update personality [PUT /v1/user/personality]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "mbti": "ESTJ"
            }

+ Response 200 (application/json)

        {
        }

### Update education [PUT /v1/user/education]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "education": "college"
            }

+ Response 200 (application/json)

        {
        }

### Update work [PUT /v1/user/work]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "work": "company"
            }

+ Response 200 (application/json)

        {
        }

### Update enneagram [PUT /v1/user/enneagram]

Provide optional "answers" field if user took the quiz

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "enneagram": "1w9"
                "answers": {
                    "1": "1w2",
                    "2": "3w4",
                    ...
                },
            }

+ Response 200 (application/json)

        {
        }

### Update relationshipStatus [PUT /v1/user/relationshipStatus]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "relationshipStatus": "Single"
            }

+ Response 200 (application/json)

        {
        }

### Update relationshipType [PUT /v1/user/relationshipType]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "relationshipType": "Monogamous"
            }

+ Response 200 (application/json)

        {
        }

### Update datingSubPreferences [PUT /v1/user/datingSubPreferences]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "datingSubPreferences": "Undecided" //to close dating sub preferences popup put datingSubPreferences: null or sending actual values will also turn off the boolean value for the popup close
            }

+ Response 200 (application/json)

        {
        }

### Update height [PUT /v1/user/height]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "height": 60, # cm
            }

+ Response 200 (application/json)

        {
        }

### Update birthday [PUT /v1/user/birthday]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "year": 1990,
                "month": 1,     // 1 is January
                "day": 1
            }

+ Response 200 (application/json)

        {
            "birthday": "1990-01-01T05:00:00.000Z"
        }

### Update gender [PUT /v1/user/gender]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "gender": "male"   // "male", "female", or "non-binary"
            }

+ Response 200 (application/json)

        {
            "gender": "male"
        }

### Update location [PUT /v1/user/location]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "longitude": -122.5,
                "latitude": 37.7
            }

+ Response 200 (application/json)

        {
            "location": "Brooklyn, NY",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

### Hide city [PUT /v1/user/hideCity]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideCity": true
            }

+ Response 200 (application/json)

        {
            "location": "New York",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

### Hide read receipts [PUT /v1/user/hideReadReceipts]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideReadReceipts": true
            }

+ Response 200 (application/json)

        {
        }

### Hide location [PUT /v1/user/hideLocation]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideLocation": true
            }

+ Response 200 (application/json)

        {
        }

### Hide my profile views from others [PUT /v1/user/hideProfileViews]

User must be premium

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideLocation": true
            }

+ Response 200 (application/json)

        {
        }

### Opt out of ad targeting [PUT /v1/user/optOutOfAdTargeting]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "optOutOfAdTargeting": true
            }

+ Response 200 (application/json)

        {
        }

### Autoplay [PUT /v1/user/autoplay]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "autoplay": true
            }

+ Response 200 (application/json)

        {
        }

### Approve all followers [PUT /v1/user/approveAllFollowers]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "approveAllFollowers": true
            }

+ Response 200 (application/json)

        {
        }

### Auto-follow likes [PUT /v1/user/autoFollowLikes]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "autoFollowLikes": true
            }

+ Response 200 (application/json)

        {
        }

### Auto-follow matches [PUT /v1/user/autoFollowMatches]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "autoFollowMatches": true
            }

+ Response 200 (application/json)

        {
        }

### Hide questions [PUT /v1/user/hideQuestions]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideQuestions": true
            }

+ Response 200 (application/json)

        {
        }

### Hide comments [PUT /v1/user/hideComments]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideComments": true
            }

+ Response 200 (application/json)

        {
        }

### Hide horoscope [PUT /v1/user/hideHoroscope]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideHoroscope": true
            }

+ Response 200 (application/json)

        {
        }

### Dark mode [PUT /v1/user/darkMode]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "darkMode": true
            }

+ Response 200 (application/json)

        {
        }

### Use metric system [PUT /v1/user/useMetricSystem]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "useMetricSystem": true
            }

+ Response 200 (application/json)

        {
        }

### Disable vibrations [PUT /v1/user/vibrationsDisabled]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "vibrationsDisabled": true
            }

+ Response 200 (application/json)

        {
        }

### Set data saver mode [PUT /v1/user/dataSaver]

Deprecated, newer app versions use dataSaving

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "dataSaver": true
            }

+ Response 200 (application/json)

        {
        }

### Set data saving preferences [PUT /v1/user/dataSaving]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                dataSaving: {
                  profiles: {
                    videos: true,
                    pictures: true,
                    firstPhotoOnly: true,
                  },
                  messages: {
                    videos: true,
                    pictures: true,
                  },
                  universe: {
                    videos: true,
                    pictures: true,
                  },
                },
            }

+ Response 200 (application/json)

        {
        }

### Hide my follower count [PUT /v1/user/hideMyFollowerCount]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideMyFollowerCount": true
            }

+ Response 200 (application/json)

        {
        }

### Hide my awards [PUT /v1/user/hideMyAwards]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideMyAwards": true
            }

+ Response 200 (application/json)

        {
        }

### Hide my karma [PUT /v1/user/hideMyKarma]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideMyKarma": true
            }

+ Response 200 (application/json)

        {
        }

### Hide my age [PUT /v1/user/hideMyAge]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hideMyAge": true
            }

+ Response 200 (application/json)

        {
        }

### Change home screen to social [PUT /v1/user/changeHomeScreenToSocial]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "changeHomeScreenToSocial": true
            }

+ Response 200 (application/json)

        {
        }

### Messages theme [PUT /v1/user/messagesTheme]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "messagesTheme": 1
            }

+ Response 200 (application/json)

        {
        }

### Instant match [PUT /v1/user/instantMatch]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "instantMatch": false
            }

+ Response 200 (application/json)

        {
        }



### Update description [PUT /v1/user/description]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "description": "Sample description"
            }

+ Response 200 (application/json)

        {
            "description": "Sample description",
            "coinReward": [
                {
                    "caption": "Write a Bio Reward",
                    "rewardAmount": 10,
                    "newTotal": 60
                }
            ]
        }

### Add or edit audio description [POST /v1/user/audioDescription]

+ Fields
    + waveform (string)
    + duration (number)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="audio"

+ Response 200 (application/json)

        {
            "audioDescription": "path/to/sample.wav",
            "audioDescriptionWaveform": [1.2, 1.4],
            "audioDescriptionDuration": 30,
        }

### Delete audio description [DELETE /v1/user/audioDescription]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

### Update Firebase Cloud Messaging token [PUT /v1/user/fcmToken]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "fcmToken": "asdf"
            }

+ Response 200 (application/json)

        {
        }

### Update email [PUT /v1/user/email]

Call this route only after the user verifies their email.
The backend will retrieve the verified email from Firebase.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "coinReward": [
                {
                    "caption": "Email Verification Reward",
                    "rewardAmount": 200,
                    "newTotal": 400
                }
            ]
        }

### Update handle [PUT /v1/user/handle]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "handle": "handle"
            }

+ Response 200 (application/json)

        {
        }

### Update whether user is searchable by handle [PUT /v1/user/searchable]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "searchable": false
            }

+ Response 200 (application/json)

        {
        }

### Update whether user is hidden [PUT /v1/user/hidden]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "hidden": false
            }

+ Response 200 (application/json)

        {
        }


### Update preferences [PATCH /v1/user/preferences]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "dating": ["female"],
                "friends": ["female"],
                "personality": [
                    "ENTP",
                    "ISTJ"
                ],
                "minAge": 18,   // min 18
                "maxAge": 30,   // max 200
                "minHeight": 60,
                "maxHeight": 72,
                "local": true,
                "global": true,
                "showVerifiedOnly": true,
                "showToVerifiedOnly": true,
                "sameCountryOnly": true,
                "distance2": 10,  # max 100
                "bioLength": 100,

                # for the following, set to [] to select ALL
                "countries": [ "GB" ],    # ISO 3166 (2 digits)
                "enneagrams": [ "1w2" ],
                "languages": [ "en" ],
                "ethnicities": [ "Asian" ],
                "horoscopes": [ "Aries" ],
                "interestNames": [ "chess" ],
                "keywords": [ String ],
                "exercise": [ String ],
                "educationLevel": [ String ],
                "drinking": [ String ],
                "smoking": [ String ],
                "kids": [ String ],
                "religion": [ String ],
                "relationshipStatus": ["Single"],
                "datingSubPreferences":["Short term fun", "Long term partner"],
                "relationshipType":["Monogamous", "Polyamorous"],
                "showUsersOutsideMyRange": Boolean,

                # deprecated
                "gender": [
                    "female",
                    "male",
                    "non-binary"
                ],
                "purpose": [
                    "dating",
                    "friends"
                ],
                "distance": 4,   // miles, max 250 for non-premium
                                 // use 12,500 to indicate Infinity for premium users
                "excludedInterestNames": [ "chess" ],
                "showUnspecified": {
                    "datingSubPreferences": Boolean,
                    "relationshipStatus": Boolean,
                    "relationshipType": Boolean,
                    "sexuality": Boolean,
                    "enneagrams": Boolean,
                    "ethnicities": Boolean,
                    "drinking": Boolean,
                    "educationLevel": Boolean,
                    "exercise": Boolean,
                    "kids": Boolean,
                    "religion": Boolean,
                    "smoking": Boolean,
                },
            }

+ Response 200 (application/json)

        {
            "gender": "female",
            "personality": [
                "ENTP",
                "ISTJ"
            ],
            "relationshipStatus": ["Single"],
            "datingSubPreferences":["Short term fun", "Long term partner"],
            "relationshipType":["Monogamous", "Polyamorous"],
            "showUsersOutsideMyRange": Boolean,
            "distance": 4,
            "minAge": 18,
            "maxAge": 30
        }

### Update country preferences [PATCH /v1/user/preferences/countries]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "countries": [ "GB" ],    # ISO 3166 (2 digits)
                                          # set to null/undefined to select ALL
            }

+ Response 200 (application/json)

        {
        }

### Update datingSubPreferences preferences [PATCH /v1/user/preferences/datingSubPreferences]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "datingSubPreferences": [ "Undecided" ],
                                          # set to null/undefined to select ALL
            }

+ Response 200 (application/json)

        {
        }

### Update relationshipStatus preferences [PATCH /v1/user/preferences/relationshipStatus]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "relationshipStatus": [ "Single" ],
                                          # set to null/undefined to select ALL
            }

+ Response 200 (application/json)

        {
        }

### Update relationshipType preferences [PATCH /v1/user/preferences/relationshipType]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "relationshipType": [ "Polyamorous" ],
                                          # set to null/undefined to select ALL
            }

+ Response 200 (application/json)

        {
        }

### Update enneagram preferences [PATCH /v1/user/preferences/enneagrams]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "enneagrams": [ "1w2" ],
            }

+ Response 200 (application/json)

        {
        }
### Update language preferences [PATCH /v1/user/preferences/languages]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "languages": [ "en" ],
            }

+ Response 200 (application/json)

        {
        }

### Update horoscope preferences [PATCH /v1/user/preferences/horoscopes]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "horoscopes": [ "Aries" ],
            }

+ Response 200 (application/json)

        {
        }

### Update interest preferences [PATCH /v1/user/preferences/interests]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "interests": [ "id" ],    # deprecated
                "interestNames": [ "chess" ],    # set to null/undefined to select ALL
            }

+ Response 200 (application/json)

        {
        }

### Update keywords preferences [PATCH /v1/user/preferences/keywords]
keywords may be undefined else it should be array of words (containing no special characters or duplicates)
if undefined it is treated as []

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "keywords": [ String ],
            }

+ Response 200 (application/json)

        {
        }

#### Update exercise [PUT /v1/user/preferences/exercise]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["active"],
            }

+ Response 200 (application/json)

        {
        }

#### Update education [PUT /v1/user/preferences/education]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["sixth form"],
            }

+ Response 200 (application/json)

        {
        }

#### Update drinking [PUT /v1/user/preferences/drinking]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["socially"],
            }

+ Response 200 (application/json)

        {
        }

#### Update smoking [PUT /v1/user/preferences/smoking]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["socially"],
            }

+ Response 200 (application/json)

        {
        }

#### Update kids [PUT /v1/user/preferences/kids]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["want someday"],
            }

+ Response 200 (application/json)

        {
        }

#### Update religion [PUT /v1/user/preferences/religion]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                values:["agnostic"],
            }

+ Response 200 (application/json)

        {
        }

### Update incoming requests preferences [PUT /v1/user/incomingRequestsPreferences]

sameCountryOnly, matchSwipingPreferences, and customActivated are mutually exclusive (at most one should be true)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                incomingRequestsPreferences: {
                    sameCountryOnly: false,
                    matchSwipingPreferences: false,
                    customActivated: true,
                    custom: {
                        "dating": ["female"],
                        "friends": ["female"],
                        "minAge": 18,
                        "maxAge": 30,
                        "countries": ["US"],
                    },
                },
            }

+ Response 200 (application/json)

        {
        }

### Update social preferences [PUT /v1/user/socialPreferences]

Set a field to undefined to clear that filter.
Set socialPreferences to undefined to clear all filters.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                socialPreferences: {
                    "minAge": 18,
                    "maxAge": 30,
                    "showVerifiedOnly": true,
                    "personality": ["ENTP"],
                    "dating": ["female"],
                    "friends": ["female"],
                    "countries": ["US"],
                    "enneagrams": ["1w2"],
                    "horoscopes": ["Libra"],
                    "minKarma": 100,
                },
            }

+ Response 200 (application/json)

        {
        }

### Activate social preferences [PUT /v1/user/socialPreferencesActivated]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                socialPreferencesActivated: true
            }

+ Response 200 (application/json)

        {
        }

### Update custom feeds [PUT /v1/user/customFeeds]

Set a field to undefined to clear that filter.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                customFeeds: [
                    {
                        "feedName": "My Feed",
                        "dating": ["female"],
                        "friends": ["female"],
                        "minAge": 18,
                        "maxAge": 30,
                        "showVerifiedOnly": true,
                        "relationship": "everyone", # 'friends', 'following'
                        "interestNames": ["anime"],
                        "keywords": ["naruto"],
                        "excludeKeywords": ["itachi"],
                        "countries": ["US"],
                        "prioritizeNearby": true,
                        "prioritizeSameCountry": true,
                        "personality": ["ENTP"],
                        "enneagrams": ["1w2"],
                        "horoscopes": ["Libra"],
                    },
                ]
            }

+ Response 200 (application/json)

        {
        }

### Create or update custom feed [PUT /v1/user/customFeed]

Set a field to undefined to clear that filter.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                customFeed: {
                    "feedName": "My Feed",
                    "dating": ["female"],
                    "friends": ["female"],
                    "minAge": 18,
                    "maxAge": 30,
                    "showVerifiedOnly": true,
                    "relationship": "everyone", # 'friends', 'following'
                    "interestNames": ["anime"],
                    "keywords": ["naruto"],
                    "excludeKeywords": ["itachi"],
                    "countries": ["US"],
                    "prioritizeNearby": true,
                    "prioritizeSameCountry": true,
                    "personality": ["ENTP"],
                    "enneagrams": ["1w2"],
                    "horoscopes": ["Libra"],
                },
            }

+ Response 200 (application/json)

        {
        }

### Delete custom feed [DELETE /v1/user/customFeed]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "feedName": "My Feed",
            }

+ Response 200 (application/json)

        {
        }

### Send events [PATCH /v1/user/events]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "enterDeleteAccountFlow": true,
                "enterCoinsPage": true,
                "enterSuperLovePage": true,
                "enterNotificationsPage": true,
                "open_power_popup": true,
                "open_boost_popup": true,
                "open_superlove_popup": true,
                "open_dm_popup": true,
                "open_filter_popup" : true,
                "finished_signup": true,
            }

+ Response 200 (application/json)

        {
        }

### Update spotify [PUT /v1/user/spotify]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "artists": [  # max length 10
                    {
                        "name": "Justin Bieber",
                        "picture": "https://domain.com/jb.png",
                        "link": "https://spotify.com/justin-bieber",
                    }
                ]
            }

+ Response 200 (application/json)

        {
        }

### Update languages [PUT /v1/user/languages]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "languages": [
                    "en",
                    "de",
                    "zh",
                ]
            }

+ Response 200 (application/json)

        {
        }

### Update ethnicities [PUT /v1/user/ethnicities]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "ethnicities": [
                    "Asian",
                ]
            }

+ Response 200 (application/json)

        {
        }

### Update notification settings [PUT /v1/user/notificationSettings]

Provide all fields

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "pushNotificationSettings": {
                    "profileLikes": true,
                    "matches": true,
                    "messages": true,
                    "commentLikes": true,
                    "commentLikesMatches": true,
                    "commentLikesOtherSouls": true,
                    "commentReplies": true,
                    "commentRepliesMatches": true,
                    "commentRepliesOtherSouls": true,
                    "dailyPush": true,
                    "dailyFacts": true,
                    "questionOfTheDay": true,
                    "newSoulsNearby": true,
                    "followRequests": true,
                    "approvedFollowRequests": true,
                    "friendPosts": true,
                    "friendStories": true,
                    "email":true,
                    "sms":true,
                    "promotions": true,
                }
            }

+ Response 200 (application/json)

        {
        }

### Update wingman settings [PUT /v1/user/wingmanSettings]

Provide all fields

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "wingmanSettings": {
                    "warnings": true,
                }
            }

+ Response 200 (application/json)

        {
        }

### Update utm info [PATCH /v1/user/utm]

No longer used as of version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "utm_source": "source",
                "utm_medium": "medium",
                "utm_campaign": "campaign"
            }

+ Response 200 (application/json)

        {
        }

### Get daily boos [GET /v1/user/dailyBoos]

No longer used as of app version 1.5+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        [
            {
                "_id": "5dfee1eb5eeb4c58787a9394",
                "gender": "female",
                "handle": "jill",
                "personality": {
                    "mbti": "ESFP",
                    "avatar": "Performer"
                },
                "age": 35,
                "firstName": "Sarah",
                "pictures": [
                    "samples/sample1.jpg"
                ],
                "description": "Lorem ipsum",
                "education": "college",
                "work": "company",
                "crown": true,
            }
        ]

+ Response 422 (text/plain)

        To see others, make yourself visible on boo.


### Get daily profiles [GET /v1/user/dailyProfiles?excludeProfileIds=excludeProfileIds=1&excludeProfileIds=2]

Alternate API for web: [GET /web/dailyProfiles?excludeProfileIds=1&excludeProfileIds=2]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            profiles:
                [
                    {
                        "_id": "5dfee1eb5eeb4c58787a9394",
                        "gender": "female",
                        "handle": "jill",
                        "personality": {
                            "mbti": "ESFP",
                            "avatar": "Performer",
                            "EI": 0.6,
                            "NS": 0.6,
                            "FT": 0.6,
                            "JP": 0.6,
                        },
                        "age": 35,
                        "height": 60,
                        "firstName": "Sarah",
                        "pictures": [
                            "samples/sample1.jpg"
                        ],
                        "description": "Lorem ipsum",
                        "audioDescription": "path/to/sample.wav",
                        "audioDescriptionWaveform": [1.2, 1.4],
                        "audioDescriptionDuration": 30,
                        "education": "college",
                        "work": "company",
                        "enneagram": "1w9", (nullable)
                        "prompts": [
                            {
                                "id": "id",
                                "prompt": "prompt",  # deprecated in 1.11.42
                                "answer": "answer"
                            }
                        ],
                        "interests": [
                            {
                                "_id": "82938",
                                "interest": "#chess",
                            }
                        ],
                        "interestNames": ["chess"],
                        interestPoints: [{
                          interest: 'chess',
                          language: 'en',
                          points: 100,
                          rank: 10,
                        }],
                        "relationshipStatus": "Single",
                        "datingSubPreferences": "Short term fun",
                        "relationshipType": "Monogamous",
                        "crown": true,
                        "preferences": {
                            "purpose": ["dating", "friends"],
                            "showToVerifiedOnly": true,
                        },
                        "hideQuestions": true,
                        "hideComments": true,
                        "location": "Brooklyn, NY",
                        "teleport": true,
                        "horoscope": "Capricorn",
                        "karma": 0,
                        "numFollowers": 0,  # null if user set to hidden
                        "banned": true,
                        "verified": true, # deprecated
                        "verificationStatus": "unverified",
                            # Possible values: ['unverified', 'verified', 'reverifying']
                        "awards": {
                          "reward_id_1": 2,
                          "reward_id_2": 1,
                        },
                        "spotify": {
                            "artists": [
                                {
                                    "name": "Justin Bieber",
                                    "picture": "https://domain.com/jb.png",
                                    "link": "https://spotify.com/justin-bieber",
                                }
                            ],
                        },
                        "nearby": true, (nullable)
                        "languages": ['en', 'de'], (nullable)
                        "ethnicities": [ "Asian" ],
                        "timezone": 'Asia/Manila', (nullable)
                        "hidden": false,
                        stories: [
                            {
                                _id: '',
                                createdAt: '',
                                image: '',
                                hasUserViewed: true,
                                hasUserLiked: true,
                                backgroundColor: 1,
                            },
                        ],
                        tags: ['Active Now', 'Compatible Personality', 'Nearby', 'New Soul', 'Top Soul', 'Mutual Interests'],
                        premium: true, # nullable
                    }
                ],
            dailyLimitExceeded: true, (nullable)
            dailyLimitResetsAt: "2020-12-30T12:00:00.000Z", (nullable)
            noUsersNearby: true, (nullable)
            timer: "2020-12-30T12:00:00.000Z", (nullable)
            numSwipesRemaining: 30,
        }

+ Response 422 (text/plain)

        To see others, make yourself visible on boo.


### Get top picks [GET /v1/user/topPicks]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            profiles:
                [
                    { ... },
                ],
            alreadySuperLiked: [ 'id' ],
            noUsersNearby: true, (nullable)
        }

+ Response 422 (text/plain)

        To see others, make yourself visible on boo.


### Get rewind [GET /v1/user/rewind?price={price}&profileId={profileId} ]

+ Parameters
    + price (number)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "user": {
                "_id": "5dfee1eb5eeb4c58787a9394",
                "gender": "female",
                "handle": "jill",
                "personality": {
                    "mbti": "ESFP",
                    "avatar": "Performer"
                },
                "age": 35,
                "firstName": "Sarah",
                "pictures": [
                    "samples/sample1.jpg"
                ],
                "description": "Lorem ipsum",
                "education": "college",
                "work": "company",
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt",
                        "answer": "answer"
                    }
                ],
                "crown": true,
                "preferences": {
                    "purpose": ["dating", "friends"]
                },
                "location": "Brooklyn, NY",
                "teleport": true,
            },
            "coinsRemaining": 100
        }

+ Response 200 (application/json)

        {
            "message": "The Boo multiverse laws of space-time only allow time travel back to boos you passed on."
        }

+ Response 422 (text/plain)

        To see others, make yourself visible on boo.

+ Response 403 (text/plain)

        Rewind is only available to premium users.

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

+ Response 404 (text/plain)

        Rewind may only be used if your last action was a Nope.

### Like a user [PATCH /v1/user/like]

No longer used as of 1.8.0+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "message": "Hi"
            }

+ Response 200 (application/json)

        {
            "coinReward": [
                {
                    "caption": "1st Direct Message Reward",
                    "rewardAmount": 10,
                    "newTotal": 60
                }
            ]
        }

### Like a user [PATCH /v1/user/sendLike]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
            dailyLimitExceeded: true, # nullable
            dailyLimitResetsAt: "2020-12-30T12:00:00.000Z", #nullable
            approvedChat: { ... },    # nullable
            showSecretAdmirer: true,  # nullable
            numSwipesRemaining: 30,
        }

+ Response 404 (text/plain)

        User not found

### Send direct message to a user [PATCH /v1/user/sendDirectMessage]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "message": "Hi", # optional if gif exist
                "gif": "https://gif.com/gif" # optional if message exist
                "aspectRatio" : 0.75, # optional
                "quotedQuestion": "id", # optional
                "quotedComment": "id",  # optional
                "quotedStory": "id",  # optional
                "price": 200  # send the price that the app has stored
            }

+ Response 200 (application/json)

        {
            "coinsRemaining": 100,
            approvedChat: { ... },    # nullable
        }

+ Response 404 (text/plain)

        User not found

+ Response 422 (text/plain)

        Character limit exceeded

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Send super like [PATCH /v1/user/sendSuperLike]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "message": "Hi", # optional
                "gif": "https://gif.com/gif", # optional
                "aspectRatio" : 0.75, # optional
            }

+ Response 200 (application/json)

        {
            approvedChat: { ... },    # nullable
        }


### Send gift to a user [PATCH /v1/user/sendGift]

No longer used as of version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "gift": {
                    "id": "1",
                    "price": 400,  # send the price that the app has stored
                    "message": "my direct message", # optional
                    "caption": "my custom caption"  # for later, not implemented yet
                }
            }

+ Response 200 (application/json)

        {
            "coinsRemaining": 100
        }

+ Response 404 (text/plain)

        User not found

+ Response 422 (text/plain)

        Character limit exceeded

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed




### Pass on a user [PATCH /v1/user/pass]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
            dailyLimitExceeded: true,   # nullable
            dailyLimitResetsAt: "2020-12-30T12:00:00.000Z", #nullable
            missedPotentialMatch: true, # nullable
            showSecretAdmirer: true,    # nullable
            numSwipesRemaining: 30,
        }

### Approve a user's request to chat [PATCH /v1/user/approve?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca" # deprecated in 1.10.19
            }

+ Response 200 (application/json)

        {
        }

### Reject a user's request to chat [PATCH /v1/user/reject?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca" # deprecated in 1.10.19
            }

+ Response 200 (application/json)

        {
        }

### Unmatch with a user [PATCH /v1/user/unmatch?chatId={chatId}]

Deletes the chat if it exists.

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca" # deprecated in 1.10.19
            }

+ Response 200 (application/json)

        {
        }

### Block a user [PATCH /v1/user/block]

Blocking a user also deletes the corresponding chat if it exists.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Get blocked users list [GET /v1/user/blocked]

Use createdAt for pagination.

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            users: [
                {
                    createdAt: '2024-06-16T08:43:52.538Z',
                    user: {
                        _id: 1,
                        firstName: 1,
                        picture: 'url',
                        'personality.mbti': 1,
                        enneagram: 1,
                        karma: 1,
                        gender: 1,
                        age: 1,
                        handle: 1,
                    }
                }
            ]
        }

### Remove user from block list [DELETE /v1/user/block]

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
            user: 'userId'
        }

+ Response 200 (application/json)

        {
        }

### Get recently unblocked users list [GET /v1/user/unblocked]

Use createdAt for pagination.

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            users: [
                {
                    createdAt: '2024-06-16T08:43:52.538Z',
                    user: {
                        _id: 1,
                        firstName: 1,
                        picture: 'url',
                        'personality.mbti': 1,
                        enneagram: 1,
                        karma: 1,
                        gender: 1,
                        age: 1,
                        handle: 1,
                    }
                }
            ]
        }

### Hide User contacts [PATCH /v1/user/hideContacts]

Send reset true to unhide contacts

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                reset:true,
                emails:["<EMAIL>"],
                numbers:["+16505550001]
            }

+ Response 200 (application/json)

        {
        }

### Hide from keywords [PUT /v1/user/hideFromKeywords]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                hideFromKeywords: ['keyword'],
            }

+ Response 200 (application/json)

        {
        }

### Hide from nearby [PUT /v1/user/hideFromNearby]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                hideFromNearby: true,
            }

+ Response 200 (application/json)

        {
        }

### Hide my infinity status [PUT /v1/user/showMyInfinityStatus]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                showMyInfinityStatus: true,
            }

+ Response 200 (application/json)

        {
        }


### Get user's karma [GET /v1/user/karma]

+ Response 200 (application/json)

        {
            karma: 100,
        }

### Get user's verification status [GET /v1/user/verificationStatus]

+ Response 200 (application/json)

        {
            verificationStatus: 'reverifying',
            rejectionReason: 'string',
        }

### Get profile [GET /v1/user/profile?user={user}&from={from}]

Returns profile formatted in the same way as daily profiles

Provide either user _id or handle

Called whenever a user views a profile that hasn’t already been loaded.

+ Parameters
    + user (string)
    + handle (string)
    + fromUniverse (string)
    + from (string, possible values: universe, viewedYou, youViewed, search)

+ Response 200 (application/json)

        {
            "user": { ... },
        }

### Increase profile view [PATCH /v1/user/profileView?user={user}&source={source}]

Increments profile view count for user

Provide either user _id or handle

+ Parameters
    + user (string)
    + handle (string)
    + source (string) # possible values: for_you

+ Response 200 (application/json)

        {
        }

### Get profile details [GET /v1/user/profileDetails?user={user}&from={from}]

Provide either user _id or handle

Called whenever a user views a profile.

+ Parameters
    + user (string)
    + handle (string)
    + from (string, possible values: universe, viewedYou, youViewed, receivedRequests, sentRequests, search, matches)

+ Response 200 (application/json)

        {
            "user": {
                "allowIncomingRequests": true,
                "followRequestApproved": true, (null means no request sent yet)
                "isMatched": true,
                "hideFollowButton": true,
                "isChatExpired": true,
                "dndPost":false,
                "dndMessage":false,
                "stories": [
                    {
                        _id: '',
                        createdAt: '',
                        image: '',
                        hasUserViewed: true,
                    },
                ],
            },
        }

### Find a boo by handle [GET /v1/user/boo?handle={handle}]

+ Parameters
    + handle (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "user": {
                "personality": {
                    "mbti": "ESFJ",
                    "avatar": "Ambassador"
                },
                "_id": "0d13d4f3dd3e26d7a92df51dd22573feb866b7e8",
                "age": 35,
                "horoscope": "Capricorn",
                "gender": "male",
                "handle": "jake",
                "firstName": "a196d240725630",
                "pictures": [
                    "samples/sample1.jpg"
                ],
                "description": "Lorem ipsum",
                "education": "college",
                "work": "company",
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt",
                        "answer": "answer"
                    }
                ],
                "crown": true,
                "preferences": {
                    "purpose": ["dating", "friends"]
                },
                "location": "Brooklyn, NY",
                "teleport": true,
            },
            "recommended": true, // deprecated
            "compatibility": "recommended", // "recommended", "potential", "other"
            "compatibilityAnalysis": "<h5>Recommended</h5>"
        }

### boostPopup [GET /v1/user/boostPopup]

Call this to decide boost pop up should appear or not refer to numLikesReceivedDuringBoost value

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            numLikesReceivedDuringBoost: 3, # field may not return when conditions not fulfilled
        }

### Init app [PUT /v1/user/initApp]

Introduced in version 1.10

Call this when the app is initialized

This route combines the following routes:
- [GET /v1/user]
- [PUT /v1/user/deviceInfo]
- [PUT /v1/user/openApp]
- [PATCH /v1/user/utm]
- [GET /v1/coins/products]
- [GET /v1/personality/allTelepathyDescriptions]
- [GET /v1/user/threeTierPersonalityRecommendations]
- [GET /v1/personality/avatars]

The "recommendedHelpText" field is no longer used as of version 1.10.13

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "appVersion": "1.5.8",
                "os": "android",
                "osVersion": "10.1.4",
                "phoneModel": "Samsung Galaxy 10",
                "timezone": "Asia/Shanghai",
                "utm_source": "source",
                "utm_medium": "medium",
                "utm_campaign": "campaign",
                "utm_content": "content",
                "adset_name": "adset_name",
                "advertisingId": "",
                "locale": "fr",
                "countryLocale": "fr_FR",
                "isPhysicalDevice": true,
                "jailbroken": false,
                "deviceId": "deviceId",
                "deviceLanguage": "es",
                "webSignupPage": "/database/anime",
                "webFirstVisitPage": "/database",
                "webFirstVisitReferringURL": "https://google.com/search?q=boo",
                "webDeviceId": "id",
                "darkMode": true,
                "useMetricSystem": true,
                "deviceSize": 's',
                "signupMethod": 'email',  # possible methods: phone, email, google, apple
                "kochava": {},
                "appsflyer": {},
                "appsflyer_id": "id",
                "partnerCampaign": "gaming"
            }

+ Response 200 (application/json)

        {
            "user": {
                "_id": "inClQpt6V3e5stEcbKNSEd06Bim2dk",
                "email": "<EMAIL>",
                "createdAt": "2022-12-30T08:07:42.359Z",
                "firstName": "baf1a184fe20c3",
                "anonymousProfileNickname": "Nickname", // if undefined, nickname not set
                "birthday": "1999-12-30T08:07:42.359Z",
                "age": 30,
                "height": 60,
                "horoscope": "Capricorn",
                "gender": "male",
                "description": "Sample description",
                "audioDescription": "path/to/sample.wav",
                "audioDescriptionWaveform": [1.2, 1.4],
                "audioDescriptionDuration": 30,
                "education": "college",
                "work": "company",
                "enneagram": "1w9", (nullable)
                "relationshipStatus": "Single",
                "datingSubPreferences": "Short term fun",
                "relationshipType":"Monogamous",
                "location": "Brooklyn, NY",
                "teleport": true,
                "teleportCity": "Brooklyn",
                "teleportState": "New York",
                "actualLocation": "San Diego, CA",
                "locationComponents": {
                    "city": "Brooklyn",
                    "state": "New York",
                    "country": "United States",
                },
                "actualLocationComponents": {
                    "city": "Brooklyn",
                    "state": "New York",
                    "country": "United States",
                },
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt",
                        "answer": "answer"
                    }
                ],
                "interests": [
                    {
                        "_id": "82938",
                        "interest": "#chess",
                        "allowImages": true,
                    }
                ],
                "moreAboutUser":{
                    "exercise":"active" or undefined
                    "education":"sixth form" or undefined
                    "drinking":"socially" or undefined
                    "smoking":"socially" or undefined
                    "kids":"Not Sure Yet" or undefined
                    "religion":"agnostic" or undefined
                },
                "spotify": {
                    "artists": [
                        {
                            "name": "Justin Bieber",
                            "picture": "https://domain.com/jb.png",
                            "link": "https://spotify.com/justin-bieber",
                        }
                    ],
                },
                customPersonalityCompatibility: {
                    "compatible": [
                        'INTJ',
                    ],
                    "potential": [
                        'ENTP',
                    ],
                    "challenging": [
                        'ENTJ',
                    ],
                },
                "interestNames": ["chess"],
                interestPoints: [{
                  interest: 'chess',
                  language: 'en',
                  points: 100,
                  rank: 10,
                }],
                "hiddenInterests": ["chess"],
                "crown": true,
                "handle": "handle",
                "searchable": true,
                "hidden": false,
                "hideCity": true,
                "hideReadReceipts": true,
                "hideLocation": true,  # nullable
                "hideProfileViews": true,
                "optOutOfAdTargeting": false,
                "autoplay": false,
                "numSuperLikes": 10,
                "numBooAINeurons": 0,
                "hideQuestions": true,
                "hideComments": true,
                "hideHoroscope": true,
                "hideFromKeywords": ['keyword'],
                "hideFromNearby": false,
                "instantMatch": true,
                "preferences": {
                    "dating": ["female"],
                    "friends": ["female"],
                    "personality": [ "ESFP" ],
                    "minAge": 18,
                    "maxAge": 30,
                    "bioLength": 100,
                    "local": true,
                    "global": true,
                    "sameCountryOnly": true,
                    "countries": [ "GB" ],    # ISO 3166 (2 digits)
                                              # undefined/null means ALL countries
                    "enneagrams": ['1w2'],    # undefined/null means ALL
                    "horoscopes": ['Aries'],  # undefined/null means ALL
                    "interests": [ "id" ],    # deprecated
                    "interestNames": [ "chess" ],    # undefined/null means ALL interests
                    "languages": [ "en" ],
                    "ethnicities": [ "Asian" ],
                    "exercise": ['text'],
                    "relationshipStatus": ["Single"],
                    "datingSubPreferences":["Short term fun", "Long term partner"],
                    "relationshipType": ["Monogamous"],
                    "showUsersOutsideMyRange": Boolean,
                    "education": ['text'],
                    "drinking": ['text'],
                    "smoking": ['text'],
                    "kids": ['text'],
                    "religion": ['text'],
                    "showVerifiedOnly": true,
                    "showToVerifiedOnly": true,
                    # deprecated
                    "gender": ["female"],
                    "purpose": ["dating"],
                    "distance": 40,
                    "excludedInterestNames": [ "chess" ],
                    "showUnspecified": {
                        "datingSubPreferences": Boolean,
                        "relationshipStatus": Boolean,
                        "relationshipType": Boolean,
                        "sexuality": Boolean,
                        "enneagrams": Boolean,
                        "ethnicities": Boolean,
                        "drinking": Boolean,
                        "educationLevel": Boolean,
                        "exercise": Boolean,
                        "kids": Boolean,
                        "religion": Boolean,
                        "smoking": Boolean,
                    },
                },
                incomingRequestsPreferences: {
                    sameCountryOnly: false,
                    matchSwipingPreferences: false,
                    customActivated: true,
                    custom: {
                        "dating": ["female"],
                        "friends": ["female"],
                        "minAge": 18,
                        "maxAge": 30,
                        "countries": ["US"],
                    },
                },
                "socialPreferencesActivated": true,
                "socialPreferences": {      # undefined means no filters
                    "minAge": 18,
                    "maxAge": 30,
                    "showVerifiedOnly": true,
                    "personality": [ "ESFP" ],
                    "dating": ["female"],
                    "friends": ["female"],
                    "countries": [ "GB" ],
                    "enneagrams": ['1w2'],
                    "horoscopes": ['Aries'],
                },
                "customFeeds": [ { ... } ],
                "pushNotificationSettings": {
                    "profileLikes": true,
                    "matches": true,
                    "messages": true,
                    "commentLikes": true,
                    "commentLikesMatches": true,
                    "commentLikesOtherSouls": true,
                    "commentReplies": true,
                    "commentRepliesMatches": true,
                    "commentRepliesOtherSouls": true,
                    "dailyPush": true,
                    "dailyFacts": true,
                    "questionOfTheDay": true,
                    "newSoulsNearby": true,
                    "followRequests": true,
                    "approvedFollowRequests": true,
                    "friendPosts": true,
                    "friendStories": true,
                    "email":true,
                    "sms":true,
                    "promotions": true,
                },
                "wingmanSettings": {
                    "warnings": true,
                }
                aiSettings: {
                    outputLanguage: 'en',
                    tone: 'tone',
                },
                "pictures": [
                    "samples/sample1.jpg"
                ],
                "personality": {
                    "mbti": "ESFP",
                    "avatar": "Performer",
                    "EI": 0.6,
                    "NS": 0.6,
                    "FT": 0.6,
                    "JP": 0.6,
                },
                "deletionRequestDate": "1999-12-30T08:07:42.359Z",
                "deletionScheduledDate": "1999-12-30T08:07:42.359Z",
                "boostExpiration": "1999-12-30T08:07:42.359Z",  # nullable
                "premium": true,
                "premiumV2": true,
                "godMode": true,
                productIdPurchased: 'boo_infinity_1_month',
                unlimitedLikesExpiration: "1999-12-30T08:07:42.359Z",  # nullable
                unlimitedDmsExpiration: "1999-12-30T08:07:42.359Z",  # nullable
                "receivedFreeTrialFromBackend": true, # nullable
                hiddenContacts: true, # nullable
                "admin": true,
                "adminPermissions": {
                  all: {type: Boolean},
                  support: {type: Boolean},
                  approveQod: {type: Boolean},
                  approveInterest: {type: Boolean},
                  approveDatabase: {type: Boolean},
                  translator: {type: String, enum: languageCodes},
                  setConfig: {type: Boolean},
                },
                "coins": 100,
                "karma": 0,
                "approveAllFollowers": true,
                "autoFollowLikes": true,
                "autoFollowMatches": true,
                "numFollowers": 0,
                "numFollowing": 0,
                "numFollowRequests": 0,
                "numProfileViews": 0,
                "numPendingLikes": 0,
                "interplanetaryMode": true,
                "showFreeTrialOption": false,
                "premiumDiscount": 50,  // if undefined, no discount
                "premiumFlashSale": {   // if undefined, no flash sale
                    "discount": 60,
                    "saleEndDate": "2020-12-30T12:00:00.000Z",
                    "oneTime": true,  # set to true only for one-time sales
                },
                "webFlashSale": {   // if undefined, no flash sale
                    "discount": 60,
                    "saleEndDate": "2020-12-30T12:00:00.000Z"
                },
                "verificationStatus": "unverified",
                    # Possible values: ['unverified', 'pending', 'verified', 'rejected', 'reverifying']
                "rejectionReason":'Incorrect pose',
                "verified": true, # deprecated
                "languages": ['en', 'de'],
                "ethnicities": ['Asian'],
                "darkMode": true, # nullable
                "useMetricSystem": true, # nullable
                "vibrationsDisabled": true, # nullable
                "dataSaver": true, # nullable
                dataSaving: {      # nullable
                  profiles: {
                    videos: true,
                    pictures: true,
                  },
                  messages: {
                    videos: true,
                    pictures: true,
                  },
                  universe: {
                    videos: true,
                    pictures: true,
                  },
                },
                "hideMyFollowerCount": true, # nullable
                "hideMyAwards": true, # nullable
                "hideMyKarma": true, # nullable
                "hideMyAge": true, # nullable
                showMyInfinityStatus: true, # nullable
                "changeHomeScreenToSocial": true # nullable
                "messagesTheme": 1, # nullable
                "tempBanReason":"string",
                "tempBanEndAt":"2020-12-30T12:00:00.000Z"
                "profileTempBanReason":"string",
                "awards": {
                  "reward_id_1": 2,
                  "reward_id_2": 1,
                },
                "stickerPackPurchases":["pack1","pack2"],
                "numDbUploads": 1,
                "dbUploadCoinsReceived": 10,
                "dbUploadKarmaReceived": 10,
                "aiFilter": "string" # Optional,
                "changedBirthday": boolean (optional) — `true` if user can no longer change birthday
            },
            "showDatingSubPreferencesPopup": false, //when this value is true show popup to select datingSubPreferences
            "showRelationshipTypePopup": false, //when this value is true show popup to select relationshipType
            "showSexualityPopup": true/false, //available from 1.13.59
            "avatars": { # deprecated in 1.11.42
                "ENTJ": "Commander",
                "ENTP": "Challenger",
                "INTJ": "Mastermind",
            },
            "telepathyDescriptions": { # deprecated in 1.11.42
                "INTP": {
                    "title": "The Genius (INTP)", // deprecated in 1.10.13
                    "recommendation": "recommended",   // "potential", "other"
                    "introduction": "Geniuses are ...",
                    "categories": [
                        {
                            "title": "Strengths",
                            "emoji": "👍",
                            "list": [
                                "analytical",
                            ]
                        },
                    ]
                },
            },
            "threeTierPersonalityRecommendations": { # deprecated in 1.11.42
                "recommended": [
                    "INTJ"
                ],
                "potential": [
                    "ESTJ"
                ],
                "other": [
                    "INFP"
                ],
                "recommendedHelpText": "text"
            },
            "coinProducts": {
                "directMessage": {
                    "price": 200,
                },
                "revival": {
                    "price": 1000,
                },
                "rewind": {
                    "price": 50,
                },
                "boost": {
                    "price": 200,
                    "durationMinutes": 30,
                },
                "boostPost": {
                    "price": 500,
                },
                "levelUp": {
                    "price": 1000,
                    "durationMinutes": 1440,
                },
                "viewLastSeen": {
                    "price": 500,
                },
                "stickerPack": {
                    "price": 1000,
                },
                "reactivateChat": {
                    "price": 50,
                },
                "gifts": [
                    {
                        "id": 1,
                        "price": 400,
                        "emoji": 😃,
                        "caption": "Love your personality."
                    }
                ],
                "awards": [
                    {
                        "id": 'id',
                        "price": 400,
                        "reward": 150,
                        "type": 'regular', # or 'premium'
                        "name": 'name',
                        "description": 'description',
                        "color": 'ffffd914',
                    }
                ]
            },
            "referralRewardFreePremium": true,
            "interests": [
                {
                    "_id": "82938",       # deprecated
                    "interest": "#chess", # deprecated
                    "name": "chess",
                    "category": "Games",
                    "allowImages": true,  # deprecated
                }
            ],
            "numPendingChats": 10,
            "karmaTiers": [0, 50, 100],
            "karmaTierCoinRewards": [0, 50, 100],
            "karmaTierGhostMeter": [0, 50, 100],
            "nextLoginReward": "2020-02-14T05:28:35.993Z",
            "karmaChange": 10,
            "numLikesReceivedChange": 10,
            "newSignup": true,  (nullable)
            "newTermsAcceptanceRequired": true,
            freeSwipeFromQodReceived: true, # nullable
            freeSwipeReceivedForQods: [ 'id' ], # nullable
            promptAppRating: true, # nullable
            receivedFreeSuperLove: true, # nullable
            numLikesReceivedDuringBoost: 3, # field may not return when conditions not fulfilled
            purchaseInfo: {
                status: 'active',  # possible values are 'active', 'cancelled', or 'expired'
                productId: 'boo_infinity_1_month', # possibly null if added manually
                expirationDate: '1999-12-30T08:07:42.359Z',
            },
            recordingTrigger: {
                record: true, # true = start recording, false = do not record
                metadata: {
                    userPlatform: 'ios',
                    userDeviceSize: 's',
                    userGender: 'male', # field not return when onboarding
                    userAge: '18-23', # field not return when onboarding
                    userPreferences: 'both', # field not return when onboarding
                    userSubscription: 'premium', # field not return when onboarding
                    userID: '0',
                    userLanguage: 'en', # field not return when onboarding
                    userConfig: 'app_141=true',
                    trafficSource: 'organic' # possible value all traffic source e.g Facebook, Google Ads, Instagram, etc
                }
            },
            promptTranslationRating: 'ja', # nullable
            is_boo_support: true, # nullable,
            numYourTurnChats: 12,
            aiimages: null,
            aiGenPictures: null,
        }

### Send translation rating [PUT /v1/user/translationRating]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                language: 'ja',
                rating: 5,  # from 1 to 5, or null if the user declined
            }

+ Response 200 (application/json)

        {
        }

### Accept terms [PUT /v1/user/acceptTerms]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Accept verification terms [PUT /v1/user/acceptVerificationTerms]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Log out [PUT /v1/user/logout]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Update interests [PUT /v1/user/interests]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "interestIds": ["id"], # deprecated
                "interestNames": ["chess"],
            }

+ Response 200 (application/json)

        {
        }

### Update hidden interests [PUT /v1/user/hiddenInterests]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "interestNames": ["chess"],
            }

+ Response 200 (application/json)

        {
        }

### Locale [PUT /v1/user/locale]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "locale": "fr",
            }

+ Response 200 (application/json)

        {
        }


### Device Info [PUT /v1/user/deviceInfo]

No longer used as of version 1.10+

Call this when the app is started (i.e., on loading page)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "appVersion": "1.5.8",
                "os": "android",
                "osVersion": "10.1.4",
                "phoneModel": "Samsung Galaxy 10",
                "timezone": "Asia/Shanghai"
            }

+ Response 200 (application/json)

        {
        }


### Open App [PUT /v1/user/openApp]

No longer used as of version 1.10+

Call this when the app is brought to the foreground (including on initial start)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                // These fields are deprecated as of version 1.6.0+
                // Use [PUT /v1/user/deviceInfo] instead.
                "appVersion": "1.5.8",
                "os": "android",
                "osVersion": "10.1.4",
                "phoneModel": "Samsung Galaxy 10",
                "timezone": "Asia/Shanghai"
            }

+ Response 200 (application/json)

        {
            // if this field is present, prompt user for in-app rating
            "promptAppRating": true,
            "coins": 100,
            "coinReward": [
                {
                    "caption": "Daily Login Reward",
                    "rewardAmount": 10,
                    "newTotal": 60,
                    "firstWeekLoginRewards": [
                      {
                        reward: 10,
                        received: true,
                        currentDay: false
                      }
                    ]
                },
                {
                    "caption": "Email Verification Reward",
                    "rewardAmount": 200,
                    "newTotal": 400
                }
            ]
        }

### Claim login reward [PUT /v1/user/claimLoginReward]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Close App [PUT /v1/user/closeApp]

No longer used as of version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Use promo code [PUT /v1/user/usePromoCode]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "promoCode": "promoCode"
            }

+ Response 200 (application/json)

        {
            "valid": true,
            "discount": 30
        }

+ Response 200 (application/json)

        {
            "valid": false
        }

### Purchase premium [PUT /v1/user/purchasePremium]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string",
                purchasedFrom: 'string',
                price: 10,
                currency: 'USD',
                productIdPurchased: 'premium_m3_v5',
            }

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": {
                    "data": {google-play-receipt-json},
                    "signature": "Base 64 encoded signature string"
                },
                purchasedFrom: 'string',
                price: 10,
                currency: 'USD',
                productIdPurchased: 'premium_m3_v5',
            }

+ Response 200 (application/json)

        {
            # this means frontend should use these values to send purchase data to analytics platforms
            purchaseData: {
                productId: 'premium_m3_v5',
                price: 10,
                currency: 'USD',
            },
        }

        {
            # this means backend decided the purchase was not valid, so frontend should not send to analytics platforms
            purchaseData: null,
        }

        {
            # this means keep the previous frontend behavior for sending purchase data to analytics platforms
        }

### Submit a request to delete user account [DELETE /v1/user?reason={reason}]

No longer used as of version 1.9.5+

+ Parameters
    + reason (string) - comma separated numbers, e.g. reason=1,3,4

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200

### Submit a request to delete user account [POST /v1/user/accountDeletion]

Reasons:

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "feedback": "feedback"   // maxlength: 10000
                "reason": [1, 3, 4],
                                     // 0 I want to be single forever
                                     // 1 Don’t wanna meet person of my dreams
                                     // 2 Not fun
                                     // 3 No matches
                                     // 4 Don't feel safe on the app
                                     // 5 Was just curious
                                     // 6 Found someone through the app
                                     // 7 Found someone elsewhere
                                     // 8 Redo setup process
                                     // 9 Other
                                     // 10 Difficulty of use
                                     // 11 Missing functionality
                                     // 12 Language missing
                                     // 13 Want a break
            }

+ Response 200


### Cancel an account deletion [PATCH /v1/user/cancelAccountDeletion]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200

### Get user questions [GET /v1/user/questions&createdBy={createdBy}&sort={sort}&beforeId={beforeId}]

+ Parameters
    + createdBy (String)
    + sort (string) ... recent, popular
    + beforeId (string) ... questionId

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ]
        }

### Get user comments [GET /v1/user/comments&createdBy={createdBy}&sort={sort}&beforeId={beforeId}]

+ Parameters
    + createdBy (String)
    + sort (string) ... recent, popular
    + beforeId (string) ... questionId

+ Response 200 (application/json)

        {
            "comments": [
                {
                  ...,
                  questionUrl: "https://...",
                }
            ]
        }

### Get user anonymous questions [GET /v1/user/anonymousQuestions]

+ Parameters
    + anonymousProfileNickname (String)
    + sort (string) ... recent, popular
    + beforeId (string) ... questionId

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ]
        }

### Get user anonymous comments [GET /v1/user/anonymousComments]

+ Parameters
    + anonymousProfileNickname (String)
    + sort (string) ... recent, popular
    + beforeId (string) ... questionId

+ Response 200 (application/json)

        {
            "comments": [
                {
                  ...,
                  questionUrl: "https://...",
                }
            ]
        }

### Submit a request for copy of user data [POST /v1/user/requestData]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Update sexuality [PUT /v1/user/sexuality]

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
            "sexuality": "heterosexual",
            "sexualityVisibility": "all"
        }

    + Attributes
        + sexuality: `heterosexual` (string, required) - Describes the sexuality of the individual. Value must be one of "heterosexual," "homosexual," "bisexual," "pansexual," "asexual," or "other.". Value must be 'null' when user wants to delete sexuality.
        + sexualityVisibility: `all` (string, optional) - The default value is "all" for "heterosexual" and "sameSexuality" for all other values of "sexuality.". Value must be 'null' when user wants to delete sexuality.

+ Response 200 (application/json)

        {
        }

### Update sexuality popup [PUT /v1/user/closedSexualityPopup]

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
            sexualityPopup: true/false, // send true when user skips sexuality, otherwise no need to call this route
        }

+ Response 200 (application/json)

        {
        }


### moreAboutUser [/moreAboutUser]

#### Update exercise [PUT /v1/user/moreAboutUser/exercise]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                exercise:"active"
            }

+ Response 200 (application/json)

        {
        }
#### Update education [PUT /v1/user/moreAboutUser/education]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                education:"sixth form"
            }

+ Response 200 (application/json)

        {
        }

#### Update drinking [PUT /v1/user/moreAboutUser/drinking]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                drinking:"socially"
            }

+ Response 200 (application/json)

        {
        }

#### Update smoking [PUT /v1/user/moreAboutUser/smoking]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                smoking:"socially"
            }

+ Response 200 (application/json)

        {
        }

#### Update kids [PUT /v1/user/moreAboutUser/kids]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                kids:"want someday"
            }

+ Response 200 (application/json)

        {
        }

#### Update religion [PUT /v1/user/moreAboutUser/religion]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                religion:"agnostic"
            }

+ Response 200 (application/json)

        {
        }


### Submit  Translation[POST /v1/user/submitTranslation]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "currentTranslation": "String"   // maxlength: 500
                "correctTranslation": "String"   // maxlength: 500
                "language":"de",
                "details": "abcdef",(optional) // maxlength: 2000
            }

+ Response 200 (application/json)

    {
      _id:""
    }


### Post image for translation [POST /v1/user/translation/image?id={translationId}]

+ Parameters
    + id (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
        }

### Update notification settings [PATCH /v1/user/profile/notifications ]

Atleast Either of the dndPost|dndMessage is required
+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca"|'chatId':'5e095489c38eeb2df469a4ca',
                dndPost:true,
                dndMessage:false,
            }

+ Response 200 (application/json)

        {
        }

### Claim additional swipes [PUT /v1/user/additionalSwipes]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Send kochava data [PUT /v1/user/kochava]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                kochava: { ... }
            }

+ Response 200 (application/json)

        {
        }

### Send appsflyer data [PUT /v1/user/appsflyer]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                appsflyer: { ... }
            }

+ Response 200 (application/json)

        {
            partnerCampaign: 'anime',
        }

### Search for users by name [GET /v1/user/searchUsers]

Returns matched users only.

+ Parameters
    + search (String)

+ Response 200 (application/json)

        {
            "users": [
                {
                    _id: '',
                    firstName: '',
                    picture: '',
                    personality: {
                        mbti: '',
                    },
                    enneagram: '',
                    horoscope: '',
                    karma: 10,
                },
            ]
        }

### Get profile previews by id [GET /v1/user/profilePreviews]

Max length of array is 25

+ Parameters
    + userIds   # ?userIds[]=1&userIds[]=2

+ Response 200 (application/json)

        {
            "users": [
                {
                    _id: '',
                    firstName: '',
                    picture: '',
                    personality: {
                        mbti: '',
                    },
                    enneagram: '',
                    horoscope: '',
                    karma: 10,
                },
            ]
        }

### Hide a user's posts and comments on social [PUT /v1/user/hideOnSocial]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
            }

+ Response 200 (application/json)

        {
        }

### Un-hide a user's posts and comments on social [DELETE /v1/user/hideOnSocial]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
            }

+ Response 200 (application/json)

        {
        }

### Get users who I hid on social [GET /v1/user/hideOnSocial]

Use createdAt for pagination.

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            users: [
                {
                    createdAt: "2020-07-06T01:47:40.468Z",
                    user: {
                        _id: '',
                        firstName: '',
                        picture: '',
                        personality: {
                            mbti: '',
                        },
                        enneagram: '',
                        horoscope: '',
                        karma: 10,
                        gender: 'male',
                        age: 30,
                    }
                },
            ],
        }

### Get nearby cities [GET /v1/user/nearbyCities]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            nearbyCities: [
                {
                    city: 'New York City',
                    state: 'New York',
                    country: 'United States',
                    countryCode: 'US',
                },
            ],
        }

### Put location override [PUT /v1/user/locationOverride]

Must be from the list of nearbyCities

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                city: 'New York City',
                state: 'New York',
                countryCode: 'US',
            }

+ Response 200 (application/json)

        {
            location: "New York City, NY",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

### Delete location override [DELETE /v1/user/locationOverride]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            location: "New York City, NY",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

### Get post-purchase screen [GET /v1/user/post-purchase-screen]

Returns 404 error if user has never purchased infinity

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            baselineMetrics: {
                loveSent: 18.76,
                loveReceived: 49.2,
                DMSent: 0.48,
                DMReceived: 1.79,
                matches: 3.45,
                profileViews: 108.28,
            },
            userMetrics: {
                loveSent: 100,
                loveReceived: 100,
                DMSent: 100,
                DMReceived: 100,
                matches: 100,
                profileViews: 100,
            },
            purchaseInfo: {
                status: 'active',  # possible values are 'active', 'cancelled', or 'expired'
                productId: 'boo_infinity_1_month', # possibly null if added manually
                expirationDate: '1999-12-30T08:07:42.359Z',
            },
            matchPictures: [ 'imagekey' ],
            profileViewPictures: [ 'imagekey' ],
        }

### inform notification was open by user [PUT /v1/user/open-notification]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                analyticsLabel: 'analyticsLabel', # 'someone-viewed-your-profile', 'someone-left-you-on-read', 'question-of-the-day', etc
            }

+ Response 200 (application/json)

        {
        }

### Set anonymous profile nickname [PUT /v1/user/anonymousProfileNickname]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                anonymousProfileNickname: 'Nickname'
            }

+ Response 200 (application/json)

        {
            anonymousProfileNickname: 'Nickname'
        }

+ Response 422 (text/plain)

        Character limit exceeded

### create/update AIFilter [PUT /v1/user/aiFilter]

Applicable from version 1.13.78

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
            filter: 'muscular', # String, less than 300 characters
        }

+ Response 200 (application/json)

        {
        }

### delete AIFilter [DELETE /v1/user/aiFilter]

Applicable from version 1.13.78

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
        }

### get popular AIFilters [GET /v1/user/popularAiFilters]

Applicable from version 1.13.78

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            "popularAiFilters": [{
                "_id": "5e463003b9ffe52154a115e8"
                "filter": "muscular",
                "usageCount": 10,
            }]
        }

### set visibility in vision search [PATCH /v1/user/visionVisibility]

Applicable from version 1.13.85

+ Request (application/json)

    + Headers
        Authorization: token

    + Body
        {
            hideFromVisionSearch: true/false, # Must be boolean
        }

+ Response 200 (application/json)
        {
        }

### get Boo Infinity Copy [GET /v1/user/infinityCopy]

Applicable from version 1.13.86

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            "infinityCopy": "String"
        }

## Chat [/chat]

### Get approved chats [GET /v1/chat/approved?before={before}&pinned={pinned}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z
    + pinned (boolean) ... true
    + beforeId (string) ... chatId # deprecated (used only in 1.11.34)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "chats": [
                {
                    "_id": "5e463003b9ffe52154a115e8",
                    "lastMessageTime": "2020-07-06T01:47:40.468Z",
                    "pendingUser": null,
                    "createdAt": "2020-02-14T05:28:35.993Z",
                    "lastMessage": {   # null or undefined if no DM
                        "text": "Jake sent you a gif! [update to latest version to view]",
                        "_id": "5f0282bccd57a53eac14cbe8",
                        "gif": "https://gif.com/gif",
                        "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                        "createdAt": "2020-07-06T01:47:40.444Z",
                        "chat": "5e463003b9ffe52154a115e8",
                        "unsent": true # Optional. Exists only when true.
                    },
                    "lastMessageReaction": { # null or undefined if no reaction
                        "sender": '1',
                        "firstName": 'name1',
                        "reaction": '😘',
                        "createdAt": '2025-03-10T14:04:01.884Z'
                    },
                    "user": { ... },
                    "numUnreadMessages": 0,
                    partnerNumUnreadMessages: 0, # nullable
                    expirationDate: "2020-07-06T01:47:40.444Z", # nullable
                    "numMessages": 0,
                    "instantMatch": true,
                    "muted": true,
                    pinned: true, # nullable
                    # the fields below are present only on group chats
                    "groupChat": true,
                    "groupChatName": 'name', # undefined or '' means no name
                    "users": [ { ... } ], # does not include yourself
                    "dndPost":false,
                    "dndMessage":false,
                    "matchIndicator": "boost/superLike/infinity", #optional,
                    "noreply": true, # Optional, exists when true, Minimum APP Version 1.13.64
                    "automatedChat": true, # Optional, exists when true, Minimum APP Version 1.13.67
                    "supportAdded": true, # Optional, exists when true, Minimum APP Version 1.13.67
                    "yourTurn": true, # when true showing yourTurn
                }
            ]
        }

### Get pending chats [GET /v1/chat/pending?before={before}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z
    + beforeId (string) ... chatId # deprecated (used only in 1.11.34)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "chats": [
                {
                    "_id": "5e463003b9ffe52154a115e8",
                    "lastMessageTime": "2020-07-06T01:47:40.468Z",
                    "pendingUser": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776",
                    "createdAt": "2020-02-14T05:28:35.993Z",
                    "lastMessage": {   # null or undefined if no DM
                        "text": "Jake sent you a gif! [update to latest version to view]",
                        "_id": "5f0282bccd57a53eac14cbe8",
                        "gif": "https://gif.com/gif",
                        "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                        "createdAt": "2020-07-06T01:47:40.444Z",
                        "chat": "5e463003b9ffe52154a115e8",
                        "unsent": true # Optional. Exists only when true.
                    },
                    "lastMessageReaction": { # null or undefined if no reaction
                        "sender": '1',
                        "firstName": 'name1',
                        "reaction": '😘',
                        "createdAt": '2025-03-10T14:04:01.884Z'
                    },
                    "user": { ... },
                    "numUnreadMessages": 0,
                    "numMessages": 0,
                    "initiatedBySuperLike": true,
                    "matchIndicator": "boost/superLike/infinity", #optional
                }
            ]
        }
### Get messaged chats [GET /v1/chat/sortMessages?paginationToken={token}&sort={sort}]
#Returns chats in sorted, added new route not to affect the older versions

+ Parameters
    + paginationToken (string)
    + sort (string) ... # 'recent' or 'unread' or 'yourTurn'  or 'ghostMeter' only one value to pass, if sort query missing 'recent' is default

+ Response 200 (application/json)

        {
            "chats": [ ... ],
            "paginationToken":"eyJiZWZvcmVJZCI6IjY3ZTQyZjk1MzAwMDMzNWE2ODQyYmJhOCIsImJlZm9yZSI6IjIwMjUtMDMtMjZUMTY6NDc6MTcuNDg4WiIsIm" #paginationToken can be null
        }

### Get sent chats [GET /v1/chat/sent?before={before}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z
    + beforeId (string) ... chatId # deprecated (used only in 1.11.34)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "chats": [ ... ]
        }

### Get messaged chats [GET /v1/chat/messaged?before={before}&pinned={pinned}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z
    + pinned (boolean) ... true

+ Response 200 (application/json)

        {
            "chats": [ ... ]
        }

### Get not-messaged chats [GET /v1/chat/notMessaged?before={before}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z

+ Response 200 (application/json)

        {
            "chats": [ ... ]
        }

### Get contacts [GET /v1/chat/contacts]

Returns paginated contacts, including hidden contacts.

+ Parameters
    + sort (string) ... latest, earliest
    + beforeId (string) ... chatId

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "chats": [ ... ]
        }

### Get individual chat [GET /v1/chat/individualChat?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            chat: { ... },
        }

### Get all contacts [GET /v1/chat/allContacts]

No longer used as of 1.11.55. Behavior now matches [GET /v1/chat/contacts]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "chats": [ ... ]
        }

### Get chats [GET /v1/chat]

No longer used as of version 1.10.19+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        [
            {
                "_id": "5e463003b9ffe52154a115e8",
                "lastMessageTime": "2020-07-06T01:47:40.468Z",
                "pendingUser": null,
                "createdAt": "2020-02-14T05:28:35.993Z",
                "lastMessage": {   # null or undefined if no DM
                    "text": "Jake sent you a gif! [update to latest version to view]",
                    "_id": "5f0282bccd57a53eac14cbe8",
                    "gif": "https://gif.com/gif",
                    "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                    "createdAt": "2020-07-06T01:47:40.444Z",
                    "chat": "5e463003b9ffe52154a115e8",
                    "__v": 0
                },
                "lastMessageReaction": { # null or undefined if no reaction
                    "sender": '1',
                    "firstName": 'name1',
                    "reaction": '😘',
                    "createdAt": '2025-03-10T14:04:01.884Z'
                },
                "__v": 0,
                "user": {
                  ...,
                  "lastSeen": "2020-07-06T01:47:40.468Z", (nullable)
                },
                "numUnreadMessages": 0,
                "viewLastSeenExpiration": "2020-07-06T01:47:40.444Z", (nullable)
            }
        ]

### Get chat read receipt [GET /v1/chat/readReceipt?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            partnerNumUnreadMessages: 0, # nullable
        }

### Hide chat [PATCH /v1/chat/hide]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Mark unread [PATCH /v1/chat/markUnread]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Pin chat [PATCH /v1/chat/pin]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Unpin chat [PATCH /v1/chat/unpin]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Get user profile for a chat [GET /v1/chat/userProfile?user={user}]

No longer used as of version 1.8.3+

+ Parameters
    + user (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "_id": "002229ff953a459d7428afbe2833c954e61ad97c",
            "firstName": "422d3429ecc993",
            "gender": "female",
            "age": 35,
            "description": "Lorem ipsum",
            "pictures": [
                "samples/sample1.jpg",
                "samples/sample2.jpg"
            ],
            "personality": {
                "mbti": "INFJ",
                "avatar": "Idealist"
            },
            "education": "",
            "handle": "abc",
            "work": "company",
            "crown": false,
            "preferences": {
                "purpose": ["dating", "friends"]
            },
            "location": "Brooklyn, NY",
            "teleport": true,
        }

### Create group chat [PATCH /v1/chat/createGroupChat]

+ Request (application/json)

    + Body

            {
                "users": ["id"],
                "groupChatName": 'name', # optional
            }

+ Response 200 (application/json)

        {
            "chat": { ... },
        }

### Change group chat name [PATCH /v1/chat/changeGroupChatName]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
                "groupChatName": 'name', # empty string to clear the name
            }

### Leave group chat [PATCH /v1/chat/leaveGroupChat]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Invite to group chat [PATCH /v1/chat/inviteToGroupChat]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
                "users": ["id"],
            }

### Remove from group chat [PATCH /v1/chat/removeFromGroupChat]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
                "user": 'id',
            }

### Mute chat [PATCH /v1/chat/mute]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Unmute chat [PATCH /v1/chat/unmute]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

### Submit a request for copy of chat [POST /v1/chat/exportChat]

Both participants of the chat need to call this API to approve the export.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "chatId": 'id',
            }

+ Response 200 (application/json)

        {
        }

### Leave automated support chat [PATCH /v1/chat/leaveAutomatedChat]

Only Boo Support user can call this API

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "chatId": 'id',
            }

+ Response 200 (application/json)

        {
        }

### get recently unmatched [GET /v1/chat/unmatched]

Added from version 1.13.85

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            "chats": [
                {
                    "_id": "66250b8dce4b1b97a74fa3a5",
                    "lastMessageTime": "2024-05-20T10:15:00.456Z",
                    "user": {
                        "_id": "661ff248be4ab907a07a2d42",
                        "name": "Jane Smith"
                    }
                }
            ]
        }

### delete recently unmatched record [DELETE /v1/chat/unmatched]

Added from version 1.13.85

+ Parameters
    + id

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            success: true, # Boolean
        }

### get hidden chats [GET /v1/chat/hidden]

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            "chats": [
                {
                    "_id": "5e463003b9ffe52154a115e8",
                    "lastMessageTime": "2020-07-06T01:47:40.468Z",
                    "pendingUser": null,
                    "createdAt": "2020-02-14T05:28:35.993Z",
                    "lastMessage": {   # null or undefined if no DM
                        "text": "Jake sent you a gif! [update to latest version to view]",
                        "_id": "5f0282bccd57a53eac14cbe8",
                        "gif": "https://gif.com/gif",
                        "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                        "createdAt": "2020-07-06T01:47:40.444Z",
                        "chat": "5e463003b9ffe52154a115e8",
                        "unsent": true # Optional. Exists only when true.
                    },
                    "lastMessageReaction": { # null or undefined if no reaction
                        "sender": '1',
                        "firstName": 'name1',
                        "reaction": '😘',
                        "createdAt": '2025-03-10T14:04:01.884Z'
                    },
                    "user": { ... },
                    "numUnreadMessages": 0,
                    partnerNumUnreadMessages: 0, # nullable
                    expirationDate: "2020-07-06T01:47:40.444Z", # nullable
                    "numMessages": 0,
                    "instantMatch": true,
                    "muted": true,
                    pinned: true, # nullable
                    # the fields below are present only on group chats
                    "groupChat": true,
                    "groupChatName": 'name', # undefined or '' means no name
                    "users": [ { ... } ], # does not include yourself
                    "dndPost":false,
                    "dndMessage":false,
                    "matchIndicator": "boost/superLike/infinity", #optional,
                    "noreply": true, # Optional, exists when true, Minimum APP Version 1.13.64
                    "automatedChat": true, # Optional, exists when true, Minimum APP Version 1.13.67
                    "supportAdded": true, # Optional, exists when true, Minimum APP Version 1.13.67
                    "yourTurn": true, # when true showing yourTurn
                }
            ]
        }

### Unhide chat [PATCH /v1/chat/unhide]

+ Request (application/json)

    + Body

            {
                "chatId": 'id',
            }

## Message [/message]

### Create message [POST /v1/message?chatId={chatId}&quotedMessageId={quotedMessageId}]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca", # deprecated in 1.10.19
                "text": "Sample message",
                "createdAt": "2020-11-15T01:16:34.326085Z",
            }

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Message 1",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "quotedMessage": {
                "_id": "5e08828e9d29861d481c157b",
                "chat": "5e0877eaa1c13730283c6631",
                "text": "Message 1",
                "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                "createdAt": "2019-12-29T10:40:14.735Z",
                "unsent": true # Optional. Exists only when true.
            },
            "unsent": true # Optional. Exists only when true.
        }

### Unsent message [PATCH /v1/message/unsend?messageId={messageId}]

+ Parameters
    + messageId (string)

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
        }

### Edit message [PATCH /v1/message/edit]

+ Parameters

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
            messageId: "5e08828e9d29861d481c157b",
            text: "Sample message"
        }

+ Response 200 (application/json)

        {
        }

### Send image [POST /v1/message/image?chatId={chatId}&quotedMessageId={quotedMessageId}&recipient={recipient}&createdAt=2020-11-15T01:16:34.326085Z]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)
    + recipient (string) # deprecated in 1.10.19

+ Fields
    + aspectRatio

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob sent you a picture! [update to latest version to view]",
            "image": "https://images.dev.boo.dating/chats/5e463003b9ffe52154a115e8/15939816303509a1435b9de8f0d3069c98fce586a07c3.jpg?Expires=1594069185&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9pbWFnZXMuZGV2LmJvby5kYXRpbmcvY2hhdHMvNWU0NjMwMDNiOWZmZTUyMTU0YTExNWU4LzE1OTM5ODE2MzAzNTA5YTE0MzViOWRlOGYwZDMwNjljOThmY2U1ODZhMDdjMy5qcGciLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE1OTQwNjkxODV9fX1dfQ__&Signature=QxrR6seyU7k5pq79M1JtnwgEbYzR~OWt1~bA86jsqvjJ4vKNKznla~5KoZxPI9sxhXR6Wx9qVgwonctvjzr9omujGD40s3F4gxVQF1gjwSZkj3EiwMkPLN4ewNkMTKhkf96gXPAbvadlYt~SPDEa~818xoxxr9Y2-osM93QEm4ikkE6kJMj4d3sG7yoX~J8On6aQ1OWrdI9eQShkisB7MYsl~rLX-wgT23~MYDnfzMCqL49LgIeGRc-JSwYf8vi8CQZnYhbU~jghLcfZcVIe9aAa0tY5vzK4vLIMd3y8bNl6GZVX~FMUZcaAAdCa1bNd9s~3uV5BzCgfJFWjddqTJw__&Key-Pair-Id=APKAIMVBRO6FH22NGCYQ",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
            "quotedMessage": {
                "_id": "5e08828e9d29861d481c157b",
                "chat": "5e0877eaa1c13730283c6631",
                "text": "Bob sent you a picture! [update to latest version to view]",
                "image": "https://images.dev.boo.dating/chats/5e463003b9ffe52154a115e8/15939816303509a1435b9de8f0d3069c98fce586a07c3.jpg?Expires=1594069185&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9pbWFnZXMuZGV2LmJvby5kYXRpbmcvY2hhdHMvNWU0NjMwMDNiOWZmZTUyMTU0YTExNWU4LzE1OTM5ODE2MzAzNTA5YTE0MzViOWRlOGYwZDMwNjljOThmY2U1ODZhMDdjMy5qcGciLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE1OTQwNjkxODV9fX1dfQ__&Signature=QxrR6seyU7k5pq79M1JtnwgEbYzR~OWt1~bA86jsqvjJ4vKNKznla~5KoZxPI9sxhXR6Wx9qVgwonctvjzr9omujGD40s3F4gxVQF1gjwSZkj3EiwMkPLN4ewNkMTKhkf96gXPAbvadlYt~SPDEa~818xoxxr9Y2-osM93QEm4ikkE6kJMj4d3sG7yoX~J8On6aQ1OWrdI9eQShkisB7MYsl~rLX-wgT23~MYDnfzMCqL49LgIeGRc-JSwYf8vi8CQZnYhbU~jghLcfZcVIe9aAa0tY5vzK4vLIMd3y8bNl6GZVX~FMUZcaAAdCa1bNd9s~3uV5BzCgfJFWjddqTJw__&Key-Pair-Id=APKAIMVBRO6FH22NGCYQ",
                "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                "createdAt": "2019-12-29T10:40:14.735Z",
                "unsent": true # Optional. Exists only when true.
            },
        }

### Send audio [POST /v1/message/audio?chatId={chatId}&quotedMessageId={quotedMessageId}&recipient={recipient}&createdAt=2020-11-15T01:16:34.326085Z]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)
    + recipient (string) # deprecated in 1.10.19

+ Fields
    + waveform (string)
    + duration (number)

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="audio"

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob sent you a voice message! [update to latest version to view]",
            "audio": "https://images.dev.boo.dating/chats/5e463003b9ffe52154a115e8/15939816303509a1435b9de8f0d3069c98fce586a07c3.jpg?Expires=1594069185&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9pbWFnZXMuZGV2LmJvby5kYXRpbmcvY2hhdHMvNWU0NjMwMDNiOWZmZTUyMTU0YTExNWU4LzE1OTM5ODE2MzAzNTA5YTE0MzViOWRlOGYwZDMwNjljOThmY2U1ODZhMDdjMy5qcGciLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE1OTQwNjkxODV9fX1dfQ__&Signature=QxrR6seyU7k5pq79M1JtnwgEbYzR~OWt1~bA86jsqvjJ4vKNKznla~5KoZxPI9sxhXR6Wx9qVgwonctvjzr9omujGD40s3F4gxVQF1gjwSZkj3EiwMkPLN4ewNkMTKhkf96gXPAbvadlYt~SPDEa~818xoxxr9Y2-osM93QEm4ikkE6kJMj4d3sG7yoX~J8On6aQ1OWrdI9eQShkisB7MYsl~rLX-wgT23~MYDnfzMCqL49LgIeGRc-JSwYf8vi8CQZnYhbU~jghLcfZcVIe9aAa0tY5vzK4vLIMd3y8bNl6GZVX~FMUZcaAAdCa1bNd9s~3uV5BzCgfJFWjddqTJw__&Key-Pair-Id=APKAIMVBRO6FH22NGCYQ",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
        }

### Send video [POST /v1/message/video?chatId={chatId}&quotedMessageId={quotedMessageId}&recipient={recipient}&createdAt=2020-11-15T01:16:34.326085Z]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)
    + recipient (string) # deprecated in 1.10.19

+ Fields
    + aspectRatio

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="video"

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob sent you a video! [update to latest version to view]",
            "video": "https://images.dev.boo.dating/chats/5e463003b9ffe52154a115e8/15939816303509a1435b9de8f0d3069c98fce586a07c3.jpg?Expires=1594069185&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9pbWFnZXMuZGV2LmJvby5kYXRpbmcvY2hhdHMvNWU0NjMwMDNiOWZmZTUyMTU0YTExNWU4LzE1OTM5ODE2MzAzNTA5YTE0MzViOWRlOGYwZDMwNjljOThmY2U1ODZhMDdjMy5qcGciLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE1OTQwNjkxODV9fX1dfQ__&Signature=QxrR6seyU7k5pq79M1JtnwgEbYzR~OWt1~bA86jsqvjJ4vKNKznla~5KoZxPI9sxhXR6Wx9qVgwonctvjzr9omujGD40s3F4gxVQF1gjwSZkj3EiwMkPLN4ewNkMTKhkf96gXPAbvadlYt~SPDEa~818xoxxr9Y2-osM93QEm4ikkE6kJMj4d3sG7yoX~J8On6aQ1OWrdI9eQShkisB7MYsl~rLX-wgT23~MYDnfzMCqL49LgIeGRc-JSwYf8vi8CQZnYhbU~jghLcfZcVIe9aAa0tY5vzK4vLIMd3y8bNl6GZVX~FMUZcaAAdCa1bNd9s~3uV5BzCgfJFWjddqTJw__&Key-Pair-Id=APKAIMVBRO6FH22NGCYQ",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
        }

### Send gif [POST /v1/message/gif?chatId={chatId}&quotedMessageId={quotedMessageId}]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)

+ Fields
    + aspectRatio

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "recipient": "5e095489c38eeb2df469a", # deprecated in 1.10.19
                "gif": "https://gif.com/gif",
                "createdAt": "2020-11-15T01:16:34.326085Z"
            }

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob sent you a gif! [update to latest version to view]",
            "gif": "https://gif.com/gif",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
        }
### Send sticker(owned by user) [POST /v1/message/sticker?chatId={chatId}&quotedMessageId={quotedMessageId}]

+ Parameters
    + chatId (string)
    + quotedMessageId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "recipient": "5e095489c38eeb2df469a", # deprecated in 1.10.19
                "sticker": "stickerid",
            }

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob sent you a sticker! [update to latest version to view]",
            "sticker": "stickerid",
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
        }

### Initiate call [POST /v1/message/initiateCall]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'user_id',
                session_id: 'session_id',
            }

+ Response 200 (application/json)

### Post call log for completed call [POST /v1/message/call?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                type: 'voice',      # voice/video
                status: 'answered', # canceled/unanswered/answered
                duration: 1200,     # seconds
            }

+ Response 200 (application/json)

        {
            "_id": "5e08828e9d29861d481c157b",
            "chat": "5e0877eaa1c13730283c6631",
            "text": "Bob called you! [update to latest version to view]",
            "call": {
                type: 'voice',
                status: 'answered',
                duration: 1200,
            },
            "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "unsent": true # Optional. Exists only when true.
        }

### Send gift [POST /v1/message/gift]

No longer used as of version 1.10+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "gift": {
                    "id": "1",
                    "price": 400,  # send the price that the app has stored
                    "message": "my direct message", # optional
                    "caption": "my custom caption"  # for later, not implemented yet
                },
                "createdAt": "2020-11-15T01:16:34.326085Z"
            }

+ Response 200 (application/json)

        {
            "coinsRemaining": 100,
            "savedMessage": {
                "_id": "5e08828e9d29861d481c157b",
                "chat": "5e0877eaa1c13730283c6631",
                "text": "Bob sent you a gift! [update to latest version to view]",
                "gift": {
                    "id": 1,
                    "emoji": 😃,
                    "caption": "Love your personality.",
                    "message": "my direct message"
                },
                "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                "createdAt": "2019-12-29T10:40:14.735Z",
                "unsent": true # Optional. Exists only when true.
            }
        }

+ Response 404 (text/plain)

        User not found

+ Response 422 (text/plain)

        Character limit exceeded

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Mark message seen [PUT /v1/message/seen?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca", # deprecated in 1.10.19
                "messageId": "5e3e52c4fff7a333dcc0d9b1" # no longer used in 1.8.5+
            }

+ Response 200 (application/json)

        {
        }

### Get messages [GET /v1/message?chatId={chatId}&user={user}&before={before}]

+ Parameters
    + chatId (string)
    + user (string) # deprecated in 1.10.19
    + before (datetime) ... 2020-01-19T21:16:41.901Z

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        [
            {
                "_id": "5e252d909384b348b49fc9b0",
                "chat": "5e2527d371916710d476409b",
                "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                "createdAt": "2020-01-20T04:33:20.039Z",
                "quotedMessage": {
                    "_id": "5e08828e9d29861d481c157b",
                    "chat": "5e0877eaa1c13730283c6631",
                    "text": "message",
                    "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
                    "createdAt": "2019-12-29T10:40:14.735Z",
                },
                "quotedStory": {
                    _id: '',
                    createdAt: '',
                    createdBy: '',
                    image: '',
                    backgroundColor: 1,
                },
                "likedStory": {
                    _id: '',
                    createdAt: '',
                    createdBy: '',
                    image: '',
                    backgroundColor: 1,
                },
                "quotedQuestion": "id", # nullable
                "quotedComment": "id",  # nullable
                "text": "Message 1",
                "image": "https://images.dev.boo.dating/chats/5e463003b...",
                "aspectRatio": 0.75,
                "gif": "...",
                "audio": "...",
                "audioWaveform": [1.2, 1.4],
                "audioDuration": 30,
                "systemMessage": true,
                "unsent": true, # Optional. Exists only when true.
                "edited": true, # Optional. Exists only when true,
                "automatedChatOptions": String, #Optional. Can exists only for bot response in automated chat
            },
        ]

### Put reaction on a message [PUT /v1/message/reaction]

+ Parameters
    + messageId (string)
    + reaction (string valid emoji)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "messageId": "67b5b31fc59ee36a2a56baa2",
                "reaction":"😎"
            }

+ Response 200 (application/json)

        {}

### Remove reaction from a message [Delete /v1/message/reaction]

+ Parameters
    + messageId (string)
    + reaction (string valid emoji)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "messageId": "67b5b31fc59ee36a2a56baa2",
                "reaction":"😎"
            }

+ Response 200 (application/json)

        {}

## Question [/question]

### Get question [GET /v1/question?questionId={questionId}]

Get a single question by id

Alternate API for web: [GET /v1/question?interestName={interestName}&webId={webId}]

+ Parameters
    + questionId (String)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "question": {
                "_id": "239u0",
                "createdAt": "2019-12-29T10:40:14.735Z",
                "createdBy": { ... },
                "postedAnonymouslyBy": {
                    "anonymousProfileNickname": "Nickname 0",
                    "personality": {
                    "mbti": "ESTJ",
                    "avatar": "Executive"
                    },
                    "enneagram": "1w2",
                    "horoscope": "Capricorn",
                    "gender": "male",
                    "age", 31
                }, # replacing createdBy / profilePreview if question was Anonymous
                "allowIncomingRequests": true,
                "interest": {
                    "_id": "82938",
                    "interest": "#chess",
                },
                "interestName": "chess",
                "title": "title",
                "text": "Is this a question?",
                "gif": "https://gif.com/gif",
                "image": "path/to/sample.jpg", # old object field before allow multiple images
                "images": [
                    {
                        "image": "questions/66f36d56e934b08a8badd48b/1727229272116154efb23490d9fe5bd19c3fe97dc5782.jpg",
                        "altText": "Inserted new image1",
                    },
                    {
                        "image": "questions/66f36d56e934b08a8badd48b/17272292721172d4902dde422b9deba50666e7a13d7a8.jpg",
                        "altText": "Inserted new image2",
                    },
                    {
                        "image": "questions/66f36d56e934b08a8badd48b/17272292721184d5d91480ed499770f7c18e680a2aac5.jpg",
                        "altText": "Inserted new image3",
                    }
                ],
                "videoThumbnail": "path/to/sample.jpg",
                "aspectRatio": 0.75,
                "altText": '',
                "audio": "path/to/sample.wav",
                "audioWaveform": [1.2, 1.4],
                "audioDuration": 30,
                "numComments": 2,
                "numLikes": 1,
                "numViews": 1,
                "isDeleted": false,
                "isEdited": false,
                "hasUserLiked": false,
                "hasUserSaved": false,
                "language": "en",
                "createdByInterestRank": 10,
                "url": "https://...",
                "awards": {
                  "reward_id_1": 2,
                  "reward_id_2": 1,
                },
                "poll": {
                  options: [
                    {
                      text: 'option 0',
                      numVotes: 2,
                    },
                  ],
                  optionVotedByUser: 0,
                },
                friendsThatCommented: [   # added in 1.11.60
                  {
                    _id: '2',
                    firstName: 'name2',
                    picture: 'picture0',
                  }
                ],
                isBoosted:false,
                linkedKeywords: [ 'keyword' ],
                linkedExploreKeywords: [ 'black', 'cat', 'black-cat' ],
                linkedPillarKeywords: [ {keyword: 'keyword', url: '/keyword' ],
                linkedCategories: [ { slug: 'slug' } ],
                linkedSubcategories: [ { slug: 'slug', categoryId: 1 } ],
                linkedProfiles: [ { slug: 'slug', id: 1 } ],
                "mentionedUsersTitle": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "mentionedUsersText": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "hashtags": [
                    "pets",
                    "jogging",
                ],
            }
        }

### Get paginated questions for interest group [GET /v1/question/allQuestions&interestName={interestName}&language={language}&sort={sort}&beforeId={beforeId}&afterId={afterId}]

Return questions for an interest group sorted by most recent.

+ Parameters
    + interestId (String) # deprecated
    + interestName (String)
    + language (string) ... de
    + sort (string) ... recent, popular, rising, nearby, topAllTime, topYear, topMonth, topWeek
    + beforeId (string) ... questionId
    + afterId (string) ... questionId
    + before (datetime) ... 2020-01-19T21:16:41.901Z # deprecated
    + search (jsonarray string) ... (String) (optional) all the leading and trailing punctuations in each word are ommitted, and if only punctuations then its considered same as no filter
    + videosOnly (boolean)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ],
        }

### Get home page feed [GET /v1/question/feed?filter={filter}&language={language}&sort={sort}&beforeId={beforeId}&afterId={afterId}]

Return questions for home page feed.

+ Parameters
    + filter (string) ... following, explore, friends, for_you, [customFeedName]
    + language (string) ... de
    + sort (string) ... recent, popular, rising, nearby, topAllTime, topYear, topMonth, topWeek
    + beforeId (string) ... questionId
    + afterId (string) ... questionId
    + search (jsonarray string) ... (String) (optional) all the leading and trailing punctuations in each word are ommitted, and if only punctuations then its considered same as no filter
    + videosOnly (boolean)
    + before (datetime) ... 2020-01-19T21:16:41.901Z # deprecated

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ],
        }

### Post question [POST /v1/question]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "interestId": "4i2y34h2i5f338eeb2df469a4ca", # deprecated
                "interestName": "chess",
                "title": "Title",
                "text": "text",
                "gif": "https://gif.com/gif",
                "aspectRatio": 0.75,
                "altText": '',
                "language": "de",
                "poll": [       # max 10 options, poll cannot be edited later
                  'option 0',   # each option: max 100 chars
                ],
                "mentionedUsersTitle": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "mentionedUsersText": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "hashtags": [
                    "pets",
                    "jogging",
                ],
                "checkLanguage": true,
                "mediaUploadPending": true,
                "postedAnonymously": true
            }

+ Response 200 (application/json)

        { ... }

+ Response 409 (application/json)

If checkLanguage is true and a language mismatch is detected.

### Delete question [DELETE /v1/question?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

### Add or edit image for question [POST /v1/question/image?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Fields
    + aspectRatio

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            "image": "path/to/sample.jpg",
        }

### Add or edit images for question [POST /v1/question/images?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Fields
    + aspectRatio

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "imagesArray": [
                    { "type": "existing", "id": "image1.jpg", "altText": "First image" }, # type existing, for the existing image
                    { "type": "new", "fileIndex": 0, "altText": "Inserted new image" }, # type new, for new image
                    { "type": "existing", "id": "image2.jpg", "altText": "Second image" }
                ], # existing images that not include on the array will be deleted
                aspectRatio: 0.75,
                type="files" name="images" # optional only if upload a new images
            }


+ Response 200 (application/json)

        {
            images: [
                        {
                            image: 'questions/66f36c0382fa0459bb379159/1727228932377a7b0045dc0ce9bd1754d19d0ead62fed.jpg',
                            altText: 'Inserted new image1',
                        },
                        {
                            image: 'questions/66f36c0382fa0459bb379159/17272289323780e726c78fef428cfa56db380ceea664f.jpg',
                            altText: 'Inserted new image2',
                        },
                        {
                            image: 'questions/66f36c0382fa0459bb379159/1727228932379883d8dd908c31c4613e764888ed32fa5.jpg',
                            altText: 'Inserted new image3',
                        }
                    ]
        }

### Upload question video [POST /v1/question/video?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Fields
    + aspectRatio

Video file should be less than 30 seconds long, less than 50 MB in size.

Accepted video formats:
mp4
webm
avi
flv
mkv
mpg
wmv
mov

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="video"

+ Response 200 (application/json)

        {
            "image": "path/to/sample.jpg",
        }

+ Response 422 (text/plain)

        There's an issue with this video. Please select another one.


### Delete picture for question [DELETE /v1/question/image?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)


### Add or edit audio for question [POST /v1/question/audio?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Fields
    + waveform (string)
    + duration (number)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="audio"

+ Response 200 (application/json)

        {
            "audio": "path/to/sample.wav",
        }

### Delete audio for question [DELETE /v1/question/audio?questionId={questionId}]

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)


### Edit question [PATCH /v1/question/edit]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca",
                "title": "title",
                "text": "text",
                "gif": "https://gif.com/gif",
                "altText": "",
                "mentionedUsersTitle": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "mentionedUsersText": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "hashtags": ['chess','latin'],
            }

+ Response 200 (application/json)

        {
        }

### Boost self question [PATCH /v1/question/boost]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca",
                "price": 500,
            }

+ Response 200 (application/json)

        {
          coinsRemaining:10000
        }

### Like question [PATCH /v1/question/like]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Unlike question [PATCH /v1/question/unlike]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Award question [POST /v1/question/award]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "postId": "postId",
                "awardId": "awardId",
                "anonymous": true,
                "price": 200  # send the price that the app has stored
            }

+ Response 200 (application/json)

        {
        }

### Get award senders [GET /v1/question/awardSenders?postId={postId}&awardId={awardId}]

Return the profiles of the users who sent the award non-anonymously.

+ Parameters
    + postId (string)
    + awardId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "senders": [
                { ... }
            ],
        }

### Vote for poll [PATCH /v1/question/voteForPoll]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca"
                "option": 0,   # null to unvote
            }

+ Response 200 (application/json)

        {
        }

### View likes on own question [GET /v1/question/likes?questionId={questionId}&page={page}]

+ Parameters
    + questionId (string)
    + page (0-indexed integer)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "totalPages": 10,
            "usersThatLiked": [
                {
                    "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                    "firstName": "a987f563700c07",
                    "gender": "female",
                    "age": 35,
                    "description": "Lorem ipsum",
                    "pictures": [
                        "samples/sample1.jpg",
                        "samples/sample2.jpg"
                    ],
                    "personality": {
                        "mbti": "ENTJ",
                        "avatar": "Achiever"
                    },
                    "education": "",
                    "handle": "abc",
                    "work": "company",
                    "prompts": [
                        {
                            "id": "id",
                            "prompt": "prompt",
                            "answer": "answer"
                        }
                    ],
                    "crown": false,
                    "preferences": {
                        "purpose": ["dating", "friends"]
                    },
                    "location": "Brooklyn, NY",
                    "teleport": true,
                    "horoscope": "Capricorn",
                },
            ]
        }

### Report question [PATCH /v1/question/report]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca",
                "reason": ["string"],
                "explanation": "comment",
            }

+ Response 200 (application/json)

        {
        }

### Save question [PATCH /v1/question/save]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Unsave question [PATCH /v1/question/unsave]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Get saved questions [GET /v1/question/saved?&beforeId={beforeId}]

+ Parameters
    + beforeId (string) ... questionId

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ],
        }

### Submit a Question of Day candidate[POST /v1/question/submitQod]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "text": "String"   // maxlength: 500
                "isAnonymous": true,(optional)
                "language":"de"(optional)
            }

+ Response 200 (application/json)

    {}

## Question View Data [/question-view-data]

### Update question view data [PUT /v1/question-view-data]

${field} can be one of: numClicks, numSecondsReadingOnFeed, numSecondsOnPost, numSecondsReadingComments, numShares

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                questions: [
                    {
                        questionId: "",
                        fieldsToIncrement: {
                            `${field1}`: 1,
                            `${field2}`: 1,
                        },
                    },
                ],
            }

+ Response 200 (application/json)

        {
        }

## Event [/event]

### Register events [POST /event]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                events: [
                    {
                        date: "",
                        event: "",
                    },
                ],
            }

+ Response 200 (application/json)

        {
        }

## Comment [/comment]

### Get comments [GET /v1/comment?questionId={questionId}&sort={sort}&beforeId={beforeId}&afterId={afterId}]

Starting from 1.13.12: [GET /v1/comment/v2]

Returns comments and replies.
- Comments are paginated, while replies are not.
- Comments are sorted by the specified sort order. Replies are sorted by oldest.

Note that empty comments array in the response indicates no more pages left to load.

+ Parameters
    + questionId (string)
    + profileId (string) # use _id field
    + webPageUrl (string)
    + sort (string) ... recent, oldest, popular, nearby
    + beforeId (string) ... questionId
    + afterId (string) ... questionId
    + before (datetime) ... 2020-01-19T21:16:41.901Z # deprecated

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "hasMore": true,
            "comments": [
                {
                    "_id": "2958yc28nv282n50nv2",
                    "createdAt": "2019-12-29T10:40:14.735Z",
                    "createdBy": {
                        "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                        "firstName": "a987f563700c07",
                        "gender": "female",
                        "age": 35,
                        "description": "Lorem ipsum",
                        "pictures": [
                            "samples/sample1.jpg",
                            "samples/sample2.jpg"
                        ],
                        "personality": {
                            "mbti": "ENTJ",
                            "avatar": "Achiever"
                        },
                        "education": "",
                        "handle": "abc",
                        "work": "company",
                        "prompts": [
                            {
                                "id": "id",
                                "prompt": "prompt",
                                "answer": "answer"
                            }
                        ],
                        "crown": false,
                        "preferences": {
                            "purpose": ["dating", "friends"]
                        },
                        "location": "Brooklyn, NY",
                        "teleport": true,
                        "horoscope": "Capricorn",
                    },
                    "profilePreview": {
                        _id: '',
                        firstName: '',
                        picture: '',
                        personality: {
                            mbti: '',
                        },
                        enneagram: '',
                        horoscope: '',
                        karma: 0,
                        gender: '',
                        age: 20,
                        nearby: true,
                    },
                    "postedAnonymouslyBy": {
                            "anonymousProfileNickname": "Nickname 0",
                            "personality": {
                            "mbti": "ESTJ",
                            "avatar": "Executive"
                            },
                            "enneagram": "1w2",
                            "horoscope": "Capricorn",
                            "gender": "male",
                            "age", 31
                    }, # replacing createdBy / profilePreview if comment was Anonymous
                    "allowIncomingRequests": true,
                    "question": "4i2y34h2i5f338eeb2df469a4ca",
                    "profile": "4i2y34h2i5f338eeb2df469a4ca",
                    "text": "This is a comment",
                    "vote":{
                      "mbti":"INTJ",
                      "enneagram":"2w1",
                      horoscope:"Pisces",
                    },
                    "gif": "https://gif.com/gif",
                    "audio": "path/to/sample.wav",
                    "audioWaveform": [1.2, 1.4],
                    "audioDuration": 30,
                    "parent": "39nv0345y7y35n3vv30cx",
                    "repliedTo": {
                        "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                        "firstName": "a987f563700c07",
                        "gender": "female",
                        "age": 35,
                        "description": "Lorem ipsum",
                        "pictures": [
                            "samples/sample1.jpg",
                            "samples/sample2.jpg"
                        ],
                        "personality": {
                            "mbti": "ENTJ",
                            "avatar": "Achiever"
                        },
                        "education": "",
                        "handle": "abc",
                        "work": "company",
                        "prompts": [
                            {
                                "id": "id",
                                "prompt": "prompt",
                                "answer": "answer"
                            }
                        ],
                        "crown": false,
                        "preferences": {
                            "purpose": ["dating", "friends"]
                        },
                        "location": "Brooklyn, NY",
                        "teleport": true,
                        "horoscope": "Capricorn",
                    },
                    "depth": 1,
                    "numComments": 2,
                    "numLikes": 14,
                    "isDeleted": false,
                    "isEdited": false,
                    "hasUserLiked": true,
                    "awards": {
                      "reward_id_1": 2,
                      "reward_id_2": 1,
                    },
                    "comments": [
                        {
                            "_id": "2958yc28nv282n50nv2",
                        }
                    ],
                    "isFriendComment": false,
                    "createdByInterestRank": 10,
                    "language": "en",
                }
            ],
        }

### Get a single comment by id [GET /v1/comment/single?commentId={commentId}]

Alternate API for web: [GET /web/comment/single?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "comment": {
                ...,
                questionUrl: "https://...",
            }
        }

### Get comment context [GET /v1/comment/context?commentId={commentId}&parentId={parentId}&questionId={questionId}&postRepliedTo={postRepliedTo}&sort={sort}]

Starting from 1.13.12: [GET /v1/comment/context/v2] and [GET /web/comment/context/v2]

Get a specific comment and its context.

Use this for handling push notifications for comments.

+ Parameters
    + commentId (string)
    + parentId (string)
    + questionId (string)
    + postRepliedTo (string, nullable)
    + sort (string) ... recent, popular, nearby

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "comments": [
                {
                    "_id": "2958yc28nv282n50nv2",
                    "createdAt": "2019-12-29T10:40:14.735Z",
                    "createdBy": {
                        "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                        "firstName": "a987f563700c07",
                        "gender": "female",
                        "age": 35,
                        "description": "Lorem ipsum",
                        "pictures": [
                            "samples/sample1.jpg",
                            "samples/sample2.jpg"
                        ],
                        "personality": {
                            "mbti": "ENTJ",
                            "avatar": "Achiever"
                        },
                        "education": "",
                        "handle": "abc",
                        "work": "company",
                        "prompts": [
                            {
                                "id": "id",
                                "prompt": "prompt",
                                "answer": "answer"
                            }
                        ],
                        "crown": false,
                        "preferences": {
                            "purpose": ["dating", "friends"]
                        },
                        "location": "Brooklyn, NY",
                        "teleport": true,
                        "horoscope": "Capricorn",
                    },
                    "postedAnonymouslyBy": {
                        "anonymousProfileNickname": "Nickname 0",
                        "personality": {
                        "mbti": "ESTJ",
                        "avatar": "Executive"
                        },
                        "enneagram": "1w2",
                        "horoscope": "Capricorn",
                        "gender": "male",
                        "age", 31
                    }, # replacing createdBy / profilePreview if question was Anonymous
                    "question": "4i2y34h2i5f338eeb2df469a4ca",
                    "text": "This is a comment",
                    "parent": "39nv0345y7y35n3vv30cx",
                    "repliedTo": {
                        "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                        "firstName": "a987f563700c07",
                        "gender": "female",
                        "age": 35,
                        "description": "Lorem ipsum",
                        "pictures": [
                            "samples/sample1.jpg",
                            "samples/sample2.jpg"
                        ],
                        "personality": {
                            "mbti": "ENTJ",
                            "avatar": "Achiever"
                        },
                        "education": "",
                        "handle": "abc",
                        "work": "company",
                        "prompts": [
                            {
                                "id": "id",
                                "prompt": "prompt",
                                "answer": "answer"
                            }
                        ],
                        "crown": false,
                        "preferences": {
                            "purpose": ["dating", "friends"]
                        },
                        "location": "Brooklyn, NY",
                        "teleport": true,
                        "horoscope": "Capricorn",
                    },
                    "depth": 1,
                    "numComments": 2,
                    "numLikes": 14,
                    "isDeleted": false,
                    "isEdited": false,
                    "hasUserLiked": true,
                    "comments": [
                        {
                            "_id": "2958yc28nv282n50nv2",
                        }
                    ]
                }
            ]
        }

### Post comment [POST /v1/comment]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "createdAt": "2019-12-29T10:40:14.735Z",
                "questionId": "4i2y34h2i5f338eeb2df469a4ca",
                "text": "This is a comment",
                "gif": "https://gif.com/gif",
                "aspectRatio": 0.75,
                "parentId": "39nv0345y7y35n3vv30cx",
                "webPageUrl": "/infp-personality",
                "vote":{
                  "mbti":"INTJ",
                  "enneagram":"1w2",
                  "horoscope":"Leo",
                }
                "mentionedUsersText": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
                "checkLanguage": true,
                "postedAnonymously": true
            }

+ Response 200 (application/json)

        {
            "_id": "203520vcxdadwvb2305283v",
            "createdAt": "2019-12-29T10:40:14.735Z",
            "createdBy": {
                "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                "firstName": "a987f563700c07",
                "gender": "female",
                "age": 35,
                "description": "Lorem ipsum",
                "pictures": [
                    "samples/sample1.jpg",
                    "samples/sample2.jpg"
                ],
                "personality": {
                    "mbti": "ENTJ",
                    "avatar": "Achiever"
                },
                "education": "",
                "handle": "abc",
                "work": "company",
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt",
                        "answer": "answer"
                    }
                ],
                "crown": false,
                "preferences": {
                    "purpose": ["dating", "friends"]
                },
                "location": "Brooklyn, NY",
                "teleport": true,
                "horoscope": "Capricorn",
            },
            "question": "4i2y34h2i5f338eeb2df469a4ca",
            "interestName": "chess",
            "profile": "4i2y34h2i5f338eeb2df469a4ca",
            "text": "This is a comment",
            "vote":{
                  "mbti":"INTJ",
                  "enneagram":"1w2",
                  "horoscope":"Leo",
                },
            "parent": "39nv0345y7y35n3vv30cx",
            "repliedTo": {
                "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                "firstName": "a987f563700c07",
                "gender": "female",
                "age": 35,
                "description": "Lorem ipsum",
                "pictures": [
                    "samples/sample1.jpg",
                    "samples/sample2.jpg"
                ],
                "personality": {
                    "mbti": "ENTJ",
                    "avatar": "Achiever"
                },
                "education": "",
                "handle": "abc",
                "work": "company",
                "prompts": [
                    {
                        "id": "id",
                        "prompt": "prompt",
                        "answer": "answer"
                    }
                ],
                "crown": false,
                "preferences": {
                    "purpose": ["dating", "friends"]
                },
                "location": "Brooklyn, NY",
                "teleport": true,
                "horoscope": "Capricorn",
            },
            "depth": 1,
            "numComments": 2,
            "numLikes": 14,
            "isDeleted": false,
            "isEdited": false,
            "hasUserLiked": false,
            "postRepliedTo": "39nv0345y7y35n3vv30cx",
            "comments": [],
            "aspectRatio": 0.75,
            linkedKeywords: [ 'keyword' ],
            linkedPillarKeywords: [ {keyword: 'keyword', url: '/keyword' ],
            linkedCategories: [ { slug: 'slug' } ],
            linkedSubcategories: [ { slug: 'slug', categoryId: 1 } ],
            linkedProfiles: [ { slug: 'slug', id: 1 } ],
            "mentionedUsersText": [
                {
                    _id: '1',
                    firstName: 'name',
                }
            ],
        }

+ Response 409 (application/json)

If checkLanguage is true and a language mismatch is detected.

### Add or edit image for comment [POST /v1/comment/image?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Fields
    + aspectRatio

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            "image": "path/to/sample.jpg",
        }

### Upload comment video [POST /v1/comment/video?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Fields
    + aspectRatio

Video file should be less than 30 seconds long, less than 50 MB in size.

Accepted video formats:
mp4
webm
avi
flv
mkv
mpg
wmv
mov

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="video"

+ Response 200 (application/json)

        {
            "image": "path/to/sample.jpg",
        }

+ Response 422 (text/plain)

        There's an issue with this video. Please select another one.


### Delete picture for comment [DELETE /v1/comment/image?commentId={commentId}]

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

### Add or edit audio for comment [POST /v1/comment/audio?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Fields
    + waveform (string)
    + duration (number)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="audio"

+ Response 200 (application/json)

        {
            "audio": "path/to/sample.wav",
        }

### Delete audio for comment [DELETE /v1/comment/audio?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

### Delete comment [DELETE /v1/comment?commentId={commentId}]

+ Parameters
    + commentId (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

### Like comment [PATCH /v1/comment/like]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Unlike comment [PATCH /v1/comment/unlike]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Award comment [POST /v1/comment/award]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "postId": "postId",
                "awardId": "awardId",
                "anonymous": true,
                "price": 200  # send the price that the app has stored
            }

+ Response 200 (application/json)

        {
        }

### Get award senders [GET /v1/comment/awardSenders?postId={postId}]

Return the profiles of the users who sent awards non-anonymously.

+ Parameters
    + postId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "senders": [
                {
                    profile: { ... },
                    awardId: 'awardId',
                },
            ],
        }

### Edit comment [PATCH /v1/comment/edit]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "5e095489c38eeb2df469a4ca",
                "text": "This is a comment",
                "gif": "https://gif.com/gif",
                "mentionedUsersText": [
                    {
                        _id: '1',
                        firstName: 'name',
                    }
                ],
            }

+ Response 200 (application/json)

        {
        }

### Report comment [PATCH /v1/comment/report]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "5e095489c38eeb2df469a4ca",
                "reason": ["string"],
                "explanation": "comment",
            }

+ Response 200 (application/json)

        {
        }

### Ban a comment [PUT /v1/comment/ban]

The poster of a question can ban comments on their own question.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "id"
            }

+ Response 200 (application/json)

        {
        }

### View likes on own comment [GET /v1/comment/likes?commentId={commentId}&page={page}]

+ Parameters
    + commentId (string)
    + page (0-indexed integer)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "totalPages": 10,
            "usersThatLiked": [
                {
                    "_id": "fa2b1295c708f07dc7d7df3b58a14420c0c9e776"
                    "firstName": "a987f563700c07",
                    "gender": "female",
                    "age": 35,
                    "description": "Lorem ipsum",
                    "pictures": [
                        "samples/sample1.jpg",
                        "samples/sample2.jpg"
                    ],
                    "personality": {
                        "mbti": "ENTJ",
                        "avatar": "Achiever"
                    },
                    "education": "",
                    "handle": "abc",
                    "work": "company",
                    "prompts": [
                        {
                            "id": "id",
                            "prompt": "prompt",
                            "answer": "answer"
                        }
                    ],
                    "crown": false,
                    "preferences": {
                        "purpose": ["dating", "friends"]
                    },
                    "location": "Brooklyn, NY",
                    "teleport": true,
                    "horoscope": "Capricorn",
                },
            ]
        }

## Notification [/notification]

### Get notifications [GET /v1/notification?before={before}]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "notifications": [
                {
                    _id: 'id',
                    updatedAt: "2020-02-08T02:56:42.494Z",
                    postType: 'question',  # 'question' or 'comment'
                    notificationType: 'like',  # 'like' or 'reply' or 'mention'
                    url: 'https://...',
                    seen: false,
                    data: { ... },  # handle same way as push notifications
                    profile: { ... },
                    numProfiles: 23,
                }
            ]
        }

### Mark notification seen [PUT /v1/notification/seen]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                notificationId: 'id',
            }

+ Response 200 (application/json)

        {
        }

### Mark all unseen notification to seen [PUT /v1/notification/seenAll]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

## AI [/ai]

### Get prices [GET /v1/ai/prices]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            prices: {
                icebreakers: 1,
                continueConversation: 2,
                analysis: 2,
                suggest: 2,
                paraphrase: 2,
                proofread: 1,
                bioGenerate: 2,
                bioImprove: 2,
                bioTone: 2,
                bioProofread: 2,
            },
        }

### Get neuron product ids [GET /v1/ai/neuronProductIds]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            neuronProductIds: [
                '6_neurons',
                '40_neurons',
                '100_neurons',
                '300_neurons',
                '1000_neurons',
            ],
        }

### Update ai settings [PUT /v1/ai/settings]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                aiSettings: {
                    outputLanguage: 'en',
                    tone: 'tone',
                },
            }

+ Response 200 (application/json)

        {
        }

### Get icebreakers [PUT /v1/ai/icebreakers]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                outputType: 'type',  # 'topical icebreakers', 'pickup lines', 'jokes', or 'compliments'
                contextType: 'type', # 'interests', 'bio', or 'unrelated'
                selectedInterests: [ 'interest' ],
                previousResponses: [
                    'output',
                ],
                userInput: 'String', # Added from 1.13.82
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Continue conversation [PUT /v1/ai/continueConversation]

Error code 499 indicates frontend should hide Retry button.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                previousResponses: [
                    'output',
                ],
                userInput: 'String', # Added from 1.13.80
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Get chat analysis [PUT /v1/ai/chatAnalysis]

Error code 499 indicates frontend should hide Retry button.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                outputType: 'type',  # 'sentiment', 'performance', or 'intent'
                userInput: 'String', # Added from 1.13.82
            }

+ Response 200 (application/json)

        {
            output: 'output',
            numBooAINeurons: 10,
        }

### Get profile analysis [PUT /v1/ai/profileAnalysis]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                outputType: 'type',  # 'profile' or 'compatibility'
                userInput: 'String', # Added from 1.13.82
            }

+ Response 200 (application/json)

        {
            output: 'output',
            numBooAINeurons: 10,
        }

### get users profile Analysis [GET /v1/ai/profileAnalysis]

Applicable from version 1.13.87

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            analyzedResults: {
                verificationStatus: String, # 'verified', 'reverifying', or 'unverified'
                insights_overall: {
                    overall: {
                        feedback: String
                    },
                },
                insights_biography: {
                    overall: {
                        score: Number # 0-10
                        feedback: String,
                        insights: String
                    },
                },
                insights_photos: {
                    overall: {
                        feedback: String,
                        score: Number # 0-10
                    },
                    items: [
                        {
                            key: 'Photo 1',
                            photoUrl: String,
                            evaluation: String,
                            insights: String,
                        },
                        {
                            key: 'Photo 2',
                            photoUrl: String,
                            evaluation: String,
                            insights: String,
                        },
                    ]
                },
                insights_prompts: {
                    overall: {
                        feedback: String,
                        score: Number # 0-10
                    },
                    items: [
                        {
                            key: 'Prompt 1',
                            evaluation: String,
                            insights: String
                        },
                        {
                            key: 'Prompt 2',
                            evaluation: String,
                            insights: String
                        },
                        {
                            key: 'Prompt 3',
                            evaluation: String,
                            insights: String
                        }
                    ]
                }
            }
        }



### Social [PUT /v1/ai/social]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                questionId: 'id',
                commentId: 'id',     # optional if responding to a comment
                outputType: 'type',  # 'suggest' or 'paraphrase' or 'proofread'
                userInput: 'text',
                previousResponses: [ # optional for 'suggest'
                    'output',
                ],
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Bio generate [PUT /v1/ai/bio/generate]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                context: {
                    gender: true,
                    age: true,
                    mbti: true,
                    location: true,
                    languages: true,
                    education: true,
                    work: true,
                    interests: true,
                    lookingFor: true,
                },
                content: 'text',
                tone: 'text',
                previousResponses: [
                    'output',
                ],
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Bio improve [PUT /v1/ai/bio/improve]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                context: {
                    gender: true,
                    age: true,
                    mbti: true,
                    location: true,
                    languages: true,
                    education: true,
                    work: true,
                    interests: true,
                    lookingFor: true,
                },
                content: 'text',
                bio: 'text',
                previousResponses: [
                    'output',
                ],
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Bio change tone [PUT /v1/ai/bio/changeTone]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                bio: 'text',
                tone: 'text',
                previousResponses: [
                    'output',
                ],
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Bio proofread [PUT /v1/ai/bio/proofread]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                bio: 'text',
                previousResponses: [
                    'output',
                ],
            }

+ Response 200 (application/json)

        {
            output: [
                'output',
            ],
            numBooAINeurons: 10,
        }

### Purchase neurons [PUT /v1/ai/purchaseNeurons]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string",
                price: 10,
                currency: 'USD',
            }

+ Response 200 (application/json)

        {
        }

### get users AI tailored prompts [GET /v1/ai/aiTailoredPrompts]

Applicable from version 1.13.86

+ Parameters
    + retry (boolean) ... true # true to generate additional prompt if retryAvailable is true

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            retryAvailable: Boolean,
            prompts: [
                { prompt: "suggested prompt 1", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 2", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 3", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 4", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 5", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 6", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 7", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 8", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 9", exampleAnswer: "suggested answer" },
                { prompt: "suggested prompt 10", exampleAnswer: "suggestedanswer" },
            ]
        }

### AI photo suggestions [GET /v1/ai/image/suggestions]

+ Request (application/json)

  + Headers

          Authorization: token

  + Body

          {}

+ Response 200 (application/json)

        {
            "textSuggestions": [
                    {
                        id: '11',
                        prompt: "Show me striking a dramatic pose in the style of a Jojo's Bizarre Adventure character, with bold lines, vibrant colors, and expressive shading"
                    },
                    {
                        id: '12',
                        prompt: 'Show me in shades, walking like a boss, while a massive explosion erupts dramatically behind me'
                    },
                    {
                        id: '13',
                        prompt: 'Show me in a futuristic diver-suit exploring the Challenger Deep, surrounded by bioluminescent sea-creatures '
                    },
            ],
            visualSuggestions: [
                    {
                        id: '1',
                        stye: 'Professional',
                        thumbnail: 'https://boo-resources.b-cdn.net/ai_photo_styles/Professional%20male.png'
                    },
                    {
                        id: '2',
                        stye: '1960s Retro',
                        thumbnail: 'https://boo-resources.b-cdn.net/ai_photo_styles/1960s%20Retro%20male.png'
                    },
                    {
                        id: '3',
                        stye: 'Softbox Studio',
                        thumbnail: 'https://boo-resources.b-cdn.net/ai_photo_styles/Softbox%20Studio%20male.png'
                    },
                    {
                        id: '4',
                        stye: 'Golden Hour',
                        thumbnail: 'https://boo-resources.b-cdn.net/ai_photo_styles/Golden%20Hour%20male.png'
                    },
            ]
        }


### AI photo imagine [POST /v1/ai/image/imagine]

+ Request (application/json)

  + Headers

          Authorization: token

  + Body

          {
              suggestionId: 1,
              inputPrompt: 'Show me taking a selfie with a friendly alien on Mars', # required if user not select any suggestions
              imageKey: 'nG7o7jBxo7en41H4RDZfxBl7tSx1/171142483972860290ba8b31a988b66a961b13417ca4a.jpg',
          }

+ Response 200 (application/json)

        {
            batchId: '67e645db48af67e29d3a84ba',
        }

+ Response 422 (text/plain)

        Image failed to process

### AI photo regenerate [POST /v1/ai/image/regenerate]

+ Request (application/json)

  + Headers

          Authorization: token

  + Body

          {
              batchId: '67e645db48af67e29d3a84ba' # only works for batch id with batchStatus is DONE
          }

+ Response 200 (application/json)

        {
            batchId: '682ab1c4340c06c7a86ed07a',
        }

+ Response 422 (text/plain)

        unable to regenerate this batch as it has REGENERATED

### AI photo results [GET /v1/ai/image/results?batchId={batchId}]

+ Request (application/json)

  + Headers

          Authorization: token

  + Body

          {}

+ Response 200 (application/json)

        {
            suggestionId: '41' # will be undefined when user not select any suggestions
            inputPrompt: 'Show my face clearly' # will be undefined when user select visual suggestion
            batchStatus: 'QUEUE'
            results: [
                {
                    status: 'PENDING',
                },
                {
                    status: 'FAILURE',
                },
                {
                    status: 'SUCCESS',
                    imageUrl: 'https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385'
                },
                ...
            ]
        }

### AI photo remove batch [PUT /v1/ai/image/remove]

+ Request (application/json)

  + Headers

          Authorization: token

  + Body

        {
            batchId: '682ab1c4340c06c7a86ed07a',
        }

+ Response 200 (application/json)

        {
        }


## Coins [/coins]

### Get coins and reward info [GET /v1/coins]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "coins": 100,
            "rewards": {
                "dailyLogin": {
                    "timer": "2020-12-30T12:00:00.000Z",
                    "received": false,   // show checkmark if received
                    "firstWeekLoginRewards": [
                      {
                        reward: 10,
                        received: true,
                        currentDay: false
                      }
                    ],
                },
                "directMessage": {
                    "reward": 50,
                    "received": false
                },
                "biography": {
                    "reward": 50,
                    "received": true
                },
                "picture": {
                    "reward": 50,
                    "received": false
                },
                "email": {
                    "reward": 200,
                    "received": true
                },
                "verifyProfile": {
                    "reward": 200,
                    "received": true
                },
                "postInTwoDimensions": {
                    "reward": 100,
                    "received": true
                },
                "shareToSocialMedia": {
                    "reward": 100,
                    "received": true
                },
                "rateApp": {
                    "reward": 50,
                    "received": true
                },
                "detailedReview": {
                    "reward": 50,
                    "received": true
                },
                "enablePushNotifications": {
                    "reward": 50,
                    "received": true
                },
                "enableLocation": {
                    "reward": 50,
                    "received": true
                },
                "enableTracking": {
                    "reward": 50,
                    "received": true
                },
                "howDidYouHearAboutUs": {
                    "reward": 50,
                    "received": true
                },
                "cancelDeleteAccount": {
                    "reward": 500,
                    "received": true
                },
                "enneagram": {
                    "reward": 50,
                    "received": false
                },
                "quizAnswer": {
                    "reward": 50,
                    "received": false
                },
                "audioDescription": {
                    "reward": 50,
                    "received": false
                },
                "watchAds": {
                    "reward": 5,
                },
                "reportIncorrectTranslations": {
                    "reward": 25,
                },
                "profileCommentReward": {
                    "reward": 5,
                    "received":15,
                },
                picturesReward: {
                    tierRequirements:[0,3,6,9],
                    tierCoins:[0,25,25,25],
                    currentTier:0
                },
                interestsReward: {
                    tierRequirements:[0,10,20,30,40,50],
                    tierCoins:[0,25,25,25,25,25],
                    currentTier:0
                },
                promptsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0
                },
                postQuestionsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,10,10,10],
                    currentTier:0,
                    currentValue: 10,
                },
                sendDMReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,10,10,10],
                    currentTier:0,
                    currentValue: 10,
                },
                postStoriesReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,10,10,10],
                    currentTier:0,
                    currentValue: 10,
                },
                sharePostsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,10,10,10],
                    currentTier:0,
                    currentValue: 10,
                },
                postCommentsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0,
                    currentValue: 10,
                },
                giveLikesReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0,
                    currentValue: 10,
                },
                getLikesReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0,
                    currentValue: 10,
                },
                giveAwardsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0,
                    currentValue: 10,
                },
                getAwardsReward: {
                    tierRequirements:[0,1,2,3],
                    tierCoins:[0,25,25,25],
                    currentTier:0,
                    currentValue: 10,
                },
                postQuestionsRewardDaily: {
                    earnedToday: 0,
                    dailyLimit: 10,
                },
                postStoriesRewardDaily: {
                    earnedToday: 0,
                    dailyLimit: 10,
                },
                sharePostsRewardDaily: {
                    earnedToday: 0,
                    dailyLimit: 10,
                },
            },
            "coinReward": [
                {
                    "caption": "Daily Login Reward",
                    "rewardAmount": 10,
                    "newTotal": 60,
                    "firstWeekLoginRewards": [
                      {
                        reward: 10,
                        received: true,
                        currentDay: false
                      }
                    ]
                },
                {
                    "caption": "Email Verification Reward",
                    "rewardAmount": 200,
                    "newTotal": 400
                }
            ]
        }

### Get products [GET /v1/coins/products]

No longer used as of version 1.10+

The app should cache the results. Refresh the cache when the app restarts.
If the price of a product changed, or if a product was removed, the backend
will return 409 when a user tries to purchase, which indicates that the
purchase failed and the app should refresh the cache.

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "directMessage": {
                "price": 200,
            },
            "gifts": [
                {
                    "id": 1,
                    "price": 400,
                    "emoji": 😃,
                    "caption": "Love your personality."
                }
            ]
        }


### Get cost of telepathy [GET /v1/coins/telepathyCost]

No longer used as of version 1.8.0+

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "telepathyCost": 50
        }

### Purchase coins [PUT /v1/coins/purchaseCoins]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string",
                purchasedFrom: 'string',
                price: 10,
                currency: 'USD',
            }

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": {
                    "data": {google-play-receipt-json},
                    "signature": "Base 64 encoded signature string"
                },
                purchasedFrom: 'string',
            }

+ Response 200 (application/json)

        {
        }

### Purchase telepathy [PUT /v1/coins/purchaseTelepathy]

No longer used as of version 1.8.0+

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "mbti": "ENTJ"
            }

+ Response 200 (application/json)

        {
            "coinsRemaining": 10
        }

+ Response 422 (text/plain)

        Insufficient coins

### Revival [PUT /v1/coins/revival]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "price": 1000
            }

+ Response 200 (application/json)

        {
            "coinsRemaining": 100
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Boost [PUT /v1/coins/boost]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "price": 200
            }

+ Response 200 (application/json)

        {
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Level Up [PUT /v1/coins/levelUp]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "price": 1000
            }

+ Response 200 (application/json)

        {
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### View last seen [PUT /v1/coins/viewLastSeen]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                price: 500,
                user: 'id',
            }

+ Response 200 (application/json)

        {
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Sticker pack [PUT /v1/coins/stickerPack]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                price: 500,
                productId: 'id',
            }

+ Response 200 (application/json)

        {
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Reactivate chat [PUT /v1/coins/reactivateChat?chatId={chatId}]

+ Parameters
    + chatId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "price": 50
            }

+ Response 200 (application/json)

        {
            expirationDate: "2020-07-06T01:47:40.444Z", # nullable
        }

+ Response 403 (text/plain)

        Insufficient coins

+ Response 409 (text/plain)

        Price has changed

### Share to social media [PUT /v1/coins/shareToSocialMedia]

Claim the coin reward for sharing to social media.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "coinReward": [
                {
                    "caption": "Share to Social Media Reward",
                    "rewardAmount": 100,
                    "newTotal": 160
                }
            ]
        }

### Rate app [PUT /v1/coins/rateApp]

Claim the coin reward for rating the app.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "coinReward": [
                {
                    "caption": "Rate App Reward",
                    "rewardAmount": 50,
                    "newTotal": 160
                }
            ]
        }

### Detailed review [PUT /v1/coins/detailedReview]

Claim the coin reward for leaving a detailed review.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Enable tracking [PUT /v1/coins/enableTracking]

Claim the coin reward for enable tracking.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

#### How did you hear about us [PUT /v1/coins/howDidYouHearAboutUs]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                howDidYouHearAboutUs: "",
            }

+ Response 200 (application/json)

        {
        }

#### Cancel delete account [PUT /v1/coins/cancelDeleteAccount]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Get karma, crystal and coin reward config [GET /v1/coins/karmaCoinReward]

Get karma, crystal and cpin reward config.

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "config": [
                {
                    "level": 1,
                    "crystal": "turquoise",
                    "karmaTiers": 50,
                    "karmaTierSwipeLimits": 35,
                    "coinReward": 50
                }, ...
            ]
        }

## Super Like [/super-like]

### Purchase super likes [PUT /v1/super-like/purchase]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string",
                price: 10,
                currency: 'USD',
            }

+ Response 200 (application/json)

        {
        }

### Rate app [PUT /v1/super-like/rateApp]

Claim the super-like reward for rating the app.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

### Detailed review [PUT /v1/super-like/detailedReview]

Claim the super-like reward for leaving a detailed review.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

## Boosts [/boosts]

### Purchase boosts [PUT /v1/boosts/purchase]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string",
                purchasedFrom: 'string',
                price: 6.99,
                currency: 'USD',
            }

+ Response 200 (application/json)

        {
        }

### Use boosts [PUT /v1/boosts/use]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                boostedPopup: true,
                boostExpiration: '2024-08-15T12:56:44.261Z'
            }

+ Response 200 (application/json)

        {
        }

## Referrals [/referral]

### Get referrals info [GET /v1/referral]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "referralReward": 500,
            "freeTrialGoal": 5,
            "numActivatedReferrals": 1,
            "totalCoinsEarned": 1000
        }


### Put a referral [PUT /v1/referral]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "referredBy": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }


## Personality [/personality]

### Get list of all personalities with descriptions [GET /v1/personality]

The "description" field is no longer used as of app version 1.5+

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        [
            {
                "mbti": "ENTJ",
                "avatar": "Achiever",
                "description": "Masters of efficiency; formidable, confident, and a force to be reckoned with. They like being at the forefront, taking on life’s challenges head on and moving the world forward. But at their worst, they can be stubborn, intolerant, impatient, and out of touch with their feelings."
            },
            {
                "mbti": "ENTP",
                "avatar": "Challenger",
                "description": "Quick thinkers, witty, and never at a lack for ideas. They're at their best when they're free to brainstorm and let their ideas run. But at their worst, they're argumentative, insenstive, intolerant, impulsive, and detail avoidant."
            }
        ]

### Get details of one personality [GET /v1/personality?mbti={mbti}]

No longer used as of app version 1.5+

+ Parameters
    + mbti (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "mbti": "ENTJ",
            "avatar": "Achiever",
            "description": "Masters of efficiency; formidable, confident, and a force to be reckoned with. They like being at the forefront, taking on life’s challenges head on and moving the world forward. But at their worst, they can be stubborn, intolerant, impatient, and out of touch with their feelings."
        }

### Get avatar map [GET /v1/personality/avatars]

Not used in any version

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "avatars": {
                "ENTJ": "Commander",
                "ENTP": "Challenger",
                "INTJ": "Mastermind",
            }
        }

### Get telepathy descriptions for all personalities [GET /v1/personality/allTelepathyDescriptions]

No longer used as of version 1.10+

Remember to refresh the telepathy descriptions after:
* user changes personality
* user purchases premium

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "INTP": {
                "title": "The Genius (INTP)",
                "recommendation": "recommended",   // "potential", "other"
                "introduction": "Geniuses are ...",
                "categories": [
                    {
                        "title": "Strengths",
                        "emoji": "👍",
                        "list": [
                            "analytical",
                        ]
                    },
                ]
            },
        }

### Get telepathy description of one personality [GET /v1/personality/telepathy?mbti={mbti}]

No longer used as of version 1.10+

+ Parameters
    + mbti (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "title": "The Genius (INTP)",
            "recommendation": "recommended",   // "potential", "other"
            "introduction": "Geniuses are ...",
            "categories": [
                {
                    "title": "Strengths",
                    "emoji": "👍",
                    "list": [
                        "analytical",
                    ]
                },
                {
                    "title": "Weaknesses",
                    "emoji": "👎",
                    "list": [
                        "private and withdrawn",
                    ]
                },
                {
                    "title": "Attracted By",
                    "emoji": "😍",
                    "intro": "Qualities they love",
                    "list": [
                        "intelligent",
                    ]
                },
                {
                    "title": "Pet Peeves",
                    "emoji": "😡",
                    "intro": "Qualities they can't stand",
                    "locked": true
                },
                {
                    "title": "Likely Interests",
                    "emoji": "⚽",
                    "list": [
                        "internet surfing",
                    ]
                },
                {
                    "title": "Love Languages",
                    "emoji": "🎁",
                    "intro": "How they give and feel love most, ranked in order by most popular",
                    "list": [
                        "1. Quality Time",
                    ]
                },
                {
                    "title": "What You'll Love",
                    "emoji": "❤️",
                    "intro": "Commander (ENTJ) x Genius (INTP)",
                    "text": "..."
                },
                {
                    "title": "Potential Conflicts",
                    "emoji": "⚠️",
                    "intro": "Commander (ENTJ) x Genius (INTP)",
                    "text": "..."
                },
                {
                    "title": "Love Philosophy",
                    "emoji": "🧠",
                    "text": "..."
                },
                {
                    "title": "Mating Call",
                    "emoji": "🐥",
                    "intro": "How to know if they like you",
                    "text": "..."
                },
                {
                    "title": "Ideal Date",
                    "emoji": "🌹",
                    "text": "..."
                },
                {
                    "title": "Flirting Tips",
                    "emoji": "😘",
                    "text": "..."
                },
                {
                    "title": "Relationship Material",
                    "emoji": "💍",
                    "intro": "Values they'll cuff you for",
                    "text": "..."
                },
                {
                    "title": "Relationship Fear",
                    "emoji": "😱",
                    "text": "..."
                },
                {
                    "title": "Secret Desire",
                    "emoji": "🤞",
                    "locked": true
                },
            ]
        }


### Get telepathy description for a user [GET /v1/personality/telepathy?user={user}]

No longer used as of version 1.10+

+ Parameters
    + user (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "title": "The Genius (INTP)",
            "recommendation": "recommended",   // "potential", "other"
            "introduction": "Geniuses are ...",
            "categories": [
                {
                    "title": "Strengths",
                    "emoji": "👍",
                    "list": [
                        "analytical",
                    ]
                },
                {
                    "title": "Weaknesses",
                    "emoji": "👎",
                    "list": [
                        "private and withdrawn",
                    ]
                },
                {
                    "title": "Attracted By",
                    "emoji": "😍",
                    "intro": "Qualities they love",
                    "list": [
                        "intelligent",
                    ]
                },
                {
                    "title": "Pet Peeves",
                    "emoji": "😡",
                    "intro": "Qualities they can't stand",
                    "locked": true
                },
                {
                    "title": "Likely Interests",
                    "emoji": "⚽",
                    "list": [
                        "internet surfing",
                    ]
                },
                {
                    "title": "Love Languages",
                    "emoji": "🎁",
                    "intro": "How they give and feel love most, ranked in order by most popular",
                    "list": [
                        "1. Quality Time",
                    ]
                },
                {
                    "title": "What You'll Love",
                    "emoji": "❤️",
                    "intro": "Commander (ENTJ) x Genius (INTP)",
                    "text": "..."
                },
                {
                    "title": "Potential Conflicts",
                    "emoji": "⚠️",
                    "intro": "Commander (ENTJ) x Genius (INTP)",
                    "text": "..."
                },
                {
                    "title": "Love Philosophy",
                    "emoji": "🧠",
                    "text": "..."
                },
                {
                    "title": "Mating Call",
                    "emoji": "🐥",
                    "intro": "How to know if they like you",
                    "text": "..."
                },
                {
                    "title": "Ideal Date",
                    "emoji": "🌹",
                    "text": "..."
                },
                {
                    "title": "Flirting Tips",
                    "emoji": "😘",
                    "text": "..."
                },
                {
                    "title": "Relationship Material",
                    "emoji": "💍",
                    "intro": "Values they'll cuff you for",
                    "text": "..."
                },
                {
                    "title": "Relationship Fear",
                    "emoji": "😱",
                    "text": "..."
                },
                {
                    "title": "Secret Desire",
                    "emoji": "🤞",
                    "locked": true
                },
            ]
        }

## App Rating [/app-rating]

### Submit app rating [POST /v1/app-rating]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }


## Report [/report]

### Create report [POST /v1/report]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
                "reason": ["spam", "photos"],
                "comment": "comment"
            }

+ Response 200 (application/json)

        {
        }

## Feedback [/feedback]

### Submit feedback [POST /v1/feedback]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "message": "comment"
            }

+ Response 200 (application/json)

        {
        }

### Submit feedback from rate app [POST /v1/feedback/rateApp]

+ Parameters
    + authorization

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                feedback: 'string',
            }

+ Response 200 (application/json)

        {
        }

### Create, re-initiate or post user selected options for automated chat [POST /v1/feedback/chat]

This endpoint creates a new automated chat or continues an existing one. It uses the Boo Support bot to handle user inquiries. The chat continues based on the user's input and the current state of the conversation. Minimum app version 1.13.67


+ Parameters
    + authorization

+ Request (application/json)

    + Headers

        Authorization: token

    + Body
        {
            selectedItem: {
                title: "Account",
                en_title: "Account", #Added from version 1.13.74
                next_step: "account_issues",
            }, # (optional) - The selected item from the user's input, which determines the next steps in the conversation.
            message: 'some feedback text', # (optional) - The user's message. This must be less than 10,000 characters.
        }


+ Response 200 (application/json)

        {
            createdAt: 2024-10-12T13:45:58.735Z,
            chat: new ObjectId("670a7d9617a268218aa4211e"),
            sender: 'USER_ID',
            text: 'String',
            _id: new ObjectId("670a7d9617a268218aa42122"),
            __v: 0,
        }


### Submit feedback for Automated Chat [POST /v1/feedback/rateChat]

+ Parameters
    + authorization

+ Request (application/json)

    + Headers
        Authorization: token

    + Body
        {
            rating: 5, // Number between 1 and 5
            feedback: 'Excellent', // String
        }

+ Response 200 (application/json)

        {
        }

## Teleport [/teleport]

### Get cities [GET /v1/teleport/cities]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            cities: [
              {
                "city": "Mexico City",
                "state": "Mexico City",
                "country": "Mexico",
                "latitude": "19.42847",
                "longitude": "-99.12766",
                "timezone": "America/Mexico_City",
              },
            ],
        }

### Update teleport location [PUT /v1/teleport/location]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "longitude": -122.5,
                "latitude": 37.7
            }

+ Response 200 (application/json)

        {
            "location": "Brooklyn, NY",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

### Stop using teleport [DELETE /v1/teleport/location]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            "location": "Brooklyn, NY",
            "locationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
            "actualLocationComponents": {
                "city": "Brooklyn",
                "state": "New York",
                "country": "United States",
            },
        }

## Follow [/follow]

### Get follow requests [GET /v1/follow/followRequests?before={before}]

Use createdAt for pagination.

+ Response 200 (application/json)

        {
            "followRequests": [
                {
                    "createdAt": "2020-07-06T01:47:40.468Z",
                    "user": { ... },
                }
            ]
        }

### Get followers [GET /v1/follow/followers?before={before}]

Use createdAt for pagination.

+ Response 200 (application/json)

        {
            "followers": [
                {
                    "createdAt": "2020-07-06T01:47:40.468Z",
                    "user": { ... },
                }
            ]
        }

### Get following [GET /v1/follow/following?before={before}]

Use createdAt for pagination.

+ Response 200 (application/json)

        {
            "following": [
                {
                    "createdAt": "2020-07-06T01:47:40.468Z",
                    "user": { ... },
                }
            ]
        }

### Get follow metrics [GET /v1/follow/metrics]

+ Response 200 (application/json)

        {
            "numFollowers": 0,
            "numFollowing": 0,
            "numFollowRequests": 0,
        }

### Send follow request [PATCH /v1/follow/sendFollowRequest]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
            }

+ Response 200 (application/json)

        {
            "approved": true, (nullable)
        }

### Approve follow request [PATCH /v1/follow/approveFollowRequest]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
            }

+ Response 200 (application/json)

        {
        }

### Remove follower [PATCH /v1/follow/removeFollower]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
            }

+ Response 200 (application/json)

        {
        }

### Unfollow [PATCH /v1/follow/unfollow]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "5e095489c38eeb2df469a4ca",
            }

+ Response 200 (application/json)

        {
        }

## Config [/config]

### Get config variables [GET /v1/config]

+ Response 200 (application/json)

        {
            numUniqueUsersWhoPurchasedInfinity: 10,
            numUniqueUsersWhoPurchasedSuperLike: 10,
            numUniqueUsersWhoPurchasedCoins: 10,
            product_ids: [
                'boo_infinity_1_month',
                'boo_infinity_3_months',
                'boo_infinity_12_months',
            ],
            super_like_product_ids: [
                'super_love_3_v1',
                'super_love_12_v1',
                'super_love_50_v1',
            ],
            super_like_discounted_product_ids: [
                'super_love_3_discount_30_v1',
                'super_love_12_discount_30_v1',
                'super_love_50_discount_30_v1',
            ],
            super_like_discount: 30,
            super_like_sale_end_date: "2020-12-30T12:00:00.000Z",
            subscription_products: {
              boo_infinity: ["...", "..."],
              boo_infinity_v2: ["...", "..."],
              god_mode: ["...", "..."],
              unlimited_likes: ["..."],
              unlimited_dms: ["..."],
            },
            popular_languages: [ 'es' ],
            onboarding_video_locales: [ 'en' ],
            onboarding_video_v2_locales: [ 'en' ],
            onboarding_video_friends_locales: [ 'en' ],
            onboarding_video_url: 'https://images.prod.boo.dating/onboarding_video/en_US_black.mp4',
            removeCountryFilter: true,
            configA": true,
            configB": 3,
            show_message_sort_options: true, //when true show sort options in chats
            app_687: true, //when true use the yourTurn and numYourTurnChats in frontend
            app_765: 0.7,  # multiplier for postback revenue
            account_deletion_grace_period: 30,  # number of days
        }

### Get config for coins [GET /v1/config/coins]

+ Response 200 (application/json)

        {
            coins_product_ids: [
                '100_coins',
                '1000_coins',
                '4000_coins',
                '10000_coins',
            ],
            coins_discounted_product_ids: [
                '100_coins_discount_30',
                '1000_coins_discount_30',
                '4000_coins_discount_30',
                '10000_coins_discount_30',
            ],
            coins_discount: 30,
            coins_sale_end_date: "2020-12-30T12:00:00.000Z",
        }

### Get config for boosts [GET /v1/config/boosts]

+ Response 200 (application/json)

        {
            boosts_product_ids: [
                'boost_1_v1',
                'boost_5_v1',
                'boost_10_v1',
            ],
            boosts_discounted_product_ids: [
                'boost_5_v1_d50',
                'boost_10_v1_d50',
            ],
            boosts_discount: 50,
            boosts_sale_end_date: "2020-12-30T12:00:00.000Z",
        }

### Get config for super like [GET /v1/config/superLike]

+ Response 200 (application/json)

        {
            super_like_product_ids: [
                'super_love_3_v1',
                'super_love_12_v1',
                'super_love_50_v1',
            ],
            super_like_discounted_product_ids: [
                'super_love_3_discount_30_v1',
                'super_love_12_discount_30_v1',
                'super_love_50_discount_30_v1',
            ],
            super_like_discount: 30,
            super_like_sale_end_date: "2020-12-30T12:00:00.000Z",
        }

### Get config for partnerships [GET /v1/config/partnerships]

Alternate API for web: [GET /web/partnerships]

Returns empty array if user is not eligible for partnerships.

+ Response 200 (application/json)

        {
            partnerships: [
                {
                    brand: 'Mokobara',
                    geography: 'India',
                    category: 'Travel / Shopping',
                    perk: '12 % discount on Mokobara',
                    coupon_code: 'BOOWORLD',
                    instructions: '',
                    rules: 'Offer is only applicable on website',
                    url: 'https://mokobara.com/',
                    logo: 'https://images.dev.boo.dating/partner_logos/mokobara.svg'
                    dark_mode_logo: 'https://images.dev.boo.dating/partner_logos/mokobara_dark_mode.svg'  # nullable
                },
            ],
        }

### Put config variables [GET /v1/config]

+ Request (application/json)

    + Body

            {
                "configA": true,
            }

+ Response 200 (application/json)

        {
        }

## Social Proof [/social-proof]

### Get social proof [GET /v1/social-proof]

Alternate API for web: [GET /web/cached/social-proof]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get social proof verified users [GET /v1/social-proof/verification]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get social proof for super like [GET /v1/social-proof/super-like]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get social proof for coin purchase [GET /v1/social-proof/coin-purchase]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get social proof for neuron purchase [GET /v1/social-proof/neuron-purchase]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get social proof for boost purchase [GET /v1/social-proof/boost-purchase]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

### Get testimonials [GET /v1/social-proof/testimonials]

+ Response 200 (application/json)

        {
            "testimonials": [
                {}, # formatted as a question (post)
            ],
        }

### Get recentSignups [GET /web/recentSignups]

+ Response 200 (application/json)

        {
            "images": [
                "samples/sample1.jpg"
            ],
        }

## Interest [/interest]

### Get interest by name [GET /v1/interest?name={name}]

Alternate API for web: [GET /web/interest?name={name}]

+ Parameters
    + name (string)

+ Response 200 (application/json)

        {
            noIndex: Boolean,
            interest: {
                name: 'name',
                numFollowers: 10,
                numQuestions: 10,
                numQuestionsPerLanguage: {
                  en: 8,
                  es: 2,
                },
                similar: ['music'],
            }
        }

### Get similar interests [GET /v1/interest/similar]

Alternate API for web: [GET /web/cached/interest/similar?name={name}]

+ Parameters
    + name (string)

+ Response 200 (application/json)

        {
            interests: [
                {
                    name: 'name',
                    numFollowers: 10,
                    similar: ['music'],
                }
            ],
        }

### Get popular interests based on country and locale by following users[GET /v1/interest/popularInterestByCountry]

+ Query Parameters
    + selectedInterest (the name of the selected interest by user to fetch top 6 interest from users country and locale)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            interests: [
                {
                    interestName: 'name',
                    category: 'string' (nullable)
                },
                {
                    interestName: 'name',
                    category: 'string' (nullable)
                }
            ],
        }

### Get interests by popularity [GET /v1/interest/popular?before={before}]

Alternate API for web: [GET /web/popularInterests?before={before}]

+ Parameters
    + before (string)  # name of the last interest in the prior page

+ Response 200 (application/json)

        {
            "interests": [
                {
                    name: 'name',
                    numFollowers: 10,
                }
            ],
        }

### Get popular interests for onboarding [GET /v1/interest/popularInterestsForOnboarding]

+ Response 200 (application/json)

        {
            "interestNames": [
                "music",
            ],
        }

### Get popular interests for onboarding with num followers [GET /v1/interest/popularInterestsForOnboardingWithNumFollowers]

Note: not sorted

+ Response 200 (application/json)

        {
            "interests": [
                {
                    name: 'name',
                    numFollowers: 10,
                }
            ],
        }

### Get interests by autocomplete [GET /v1/interest/autocomplete?query={query}]

Alternate API for web: [GET /web/interest/autocomplete?query={query}]

+ Parameters
    + query (string)

+ Response 200 (application/json)

        {
            "interests": [
                {
                    name: 'name',
                }
            ],
        }

### Create interest [POST /v1/interest]

+ Request (application/json)

    + Body

            {
                name: 'name',
            }

+ Response 200 (application/json)

        {
            approved: true,
        }

### Get users in interest group [GET /v1/interest/users]

Paginated and sorted by most recently active.
Returns profile thumbnails.

+ Parameters
    + interestName (String)
    + beforeId (string) ... userId

+ Response 200 (application/json)

        {
            "users": [
                {
                    _id: '',
                    firstName: '',
                    picture: '',
                    personality: {
                        mbti: '',
                    },
                    enneagram: '',
                    horoscope: '',
                    gender: 'male',
                    age: 30,
                    karma: 10,
                },
            ]
        }

### Suggest interest for a user based on demographics [GET /v1/interest/suggested]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            suggestedInterests: [ String ]
        }

### Get interests for onbarding carousel  [GET /v1/interest/getOnboardingInterestsCarousel]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            "interestSlides": [
                {
                    name: "gaming",
                    translated: String,
                },
                {
                    name: "anime",
                    translated: String,
                }
            ],
        }

### Get top ranked users in interest group [GET /v1/interest/topRankedUsers]

Returns profile thumbnails.

+ Parameters
    + interestName (String)
    + language (String)

+ Response 200 (application/json)

        {
            "myStats": {
                rank: 1,
                points: 100,
            },
            "topRankedUsers": [
                {
                    rank: 1,
                    points: 100,
                    user: {
                        _id: '',
                        firstName: '',
                        picture: '',
                        personality: {
                            mbti: '',
                        },
                        enneagram: '',
                        horoscope: '',
                        gender: 'male',
                        age: 30,
                        karma: 10,
                        handle: '',
                    },
                },
            ]
        }

### Get interest follower pictures [GET /v1/interest/followerPictures?name={name}&name={name}]

+ Parameters
    + name (max 10 strings)

+ Response 200 (application/json)

        {
            interests: [
                {
                    name: 'name',
                    numFollowers: 10,
                    followerPictures: ['image1.jpg', 'image2.jpg'],
                }
            ]
        }


### Get onboarding interests [GET /v1/interest/onboardingInterests]

+ Request (application/json)

    + Headers

        Authorization: token

+ Response 200 (application/json)

    [
        {
            category: “Popular”,
            interestNames: [“music”, “movies"]
        },
        {
            category: “Topics”,
            interestNames: [“memes", “funny”]
        }
    ]


## Sticker Packs [/sticker-pack]

### Get sticker packs [GET /v1/sticker-pack/]

+ Response 200 (application/json)

        {
            "stickerPacks": [
                {
                  packName:"",
                  productId:"",
                  stickers:[{
                    id:""
                  }],
                  premium:true | undefined,
                }
            ],
        }


### Purchase sticker pack [PUT /v1/sticker-pack/purchase]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": "apple-receipt-base64-string"
            }

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "receipt": {
                    "data": {google-play-receipt-json},
                    "signature": "Base 64 encoded signature string"
                }
            }

+ Response 200 (application/json)

        {
        }

## Database [/database]

### Post profile candidate [POST /v1/database/profileCandidate]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                name: {type: String},
                mbti: {type: String},
                enneagram: {type: String},
                horoscope: {type: String},
                description: {type: String},
                subcategories: [{type: Number}],
                newSubcategory: {
                  name: 'New Subcategory',
                  categorySlug: 'anime',
                },
            }

+ Response 200 (application/json)

        {
            _id: '_id'
        }

### Post profile image candidate [POST /v1/database/profileImageCandidate?_id={}]

+ Parameters
    + _id (the _id of the profile, can be used for both pending and approved profiles)

+ Fields
    + imageSource (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
        }

## Profile View [/profile-view]

### Get users who viewed my profile [GET /v1/profile-view]

First page is available for all users, further pages require premium.
Use createdAt for pagination.

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            numProfileViews: 1,
            views: [
                {
                    createdAt: "2020-07-06T01:47:40.468Z",
                    user: {
                        _id: '',
                        firstName: '',
                        picture: '',
                        personality: {
                            mbti: '',
                        },
                        enneagram: '',
                        horoscope: '',
                        karma: 10,
                        age: 30,
                        gender: male,
                    }
                },
            ],
        }

### Get profiles I viewed [GET /v1/profile-view/profiles-i-viewed]

First page is available for all users, further pages require premium.
Use createdAt for pagination.

+ Parameters
    + beforeDate

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            views: [
                {
                    createdAt: "2020-07-06T01:47:40.468Z",
                    source: "for_you",
                    user: {
                        _id: '',
                        firstName: '',
                        picture: '',
                        personality: {
                            mbti: '',
                        },
                        enneagram: '',
                        horoscope: '',
                        karma: 10,
                        age: 30,
                        gender: male,
                    }
                },
            ],
        }


## Story [/story]

### Post story [POST /v1/story]

Provide either image or video file.

+ Fields
    + visibility (everyone/followersAndFriends/friends)
    + backgroundColor (int)
    + textData (string)

+ Request (multipart/form-data)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            story: {
                _id: '',
                createdAt: '',
                image: '',
                textData: '',
                visibility: '',
                numUsersWhoViewed: 0,
                numUsersWhoLiked: 0,
                backgroundColor: 1,
            }
        }

### Get story feed [GET /v1/story/feed]

No pagination.

+ Parameters
    + filter (string) ... following, explore, friends

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            profilesWithStories: [
                {
                    profile: {
                        _id: '',
                        firstName: '',
                        picture: '',
                    },
                    stories: [
                        {
                            _id: '',
                            createdAt: '',
                            image: '',
                            textData: '',
                            hasUserViewed: true,
                            hasUserLiked: true,
                        },
                    ],
                }
            ],
        }

### Get stories from user [GET /v1/story/user]

+ Parameters
    + user (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            stories: [
                {
                    _id: '',
                    createdAt: '',
                    image: '',
                    textData: '',
                    hasUserViewed: true,
                    hasUserLiked: true,
                },
            ],
        }

### Get users who viewed one of my stories [GET /v1/story/usersWhoViewed]

+ Parameters
    + storyId (string)
    + page (0-indexed integer)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            usersWhoViewed: [
                {
                    _id: '',
                    firstName: '',
                    picture: '',
                    personality: {
                        mbti: '',
                    },
                    enneagram: '',
                    horoscope: '',
                    karma: 10,
                    age: 30,
                    gender: 'male',
                    hasUserLiked: true,
                },
            ],
        }

### View story [PUT /v1/story/view]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "storyId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Like story [PUT /v1/story/like]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "storyId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Unlike story [PUT /v1/story/unlike]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "storyId": "5e095489c38eeb2df469a4ca"
            }

+ Response 200 (application/json)

        {
        }

### Get my active stories [GET /v1/story/myActiveStories]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            stories: [
                {
                    _id: '',
                    createdAt: '',
                    image: '',
                    textData: '',
                    visibility: '',
                    numUsersWhoViewed: 1,
                    numUsersWhoLiked: 1,
                    hasUserViewed: true,
                },
            ],
        }

### Get my archived stories [GET /v1/story/myArchivedStories]

+ Parameters
    + before (datetime) ... 2020-01-19T21:16:41.901Z

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            stories: [
                {
                    _id: '',
                    createdAt: '',
                    image: '',
                    textData: '',
                    visibility: '',
                    numUsersWhoViewed: 1,
                    numUsersWhoLiked: 1,
                },
            ],
        }

### Edit visibility [PUT /v1/story/visibility]

+ Parameters
    + storyId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                storyId: '',
                visibility: '',
            }

+ Response 200 (application/json)

        {
        }

### Delete story [DELETE /v1/story]

+ Parameters
    + storyId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
        }

## Azure Liveness [/liveness-new]

### Create session for liveness detection [POST /liveness-new/session]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            challengeId: '1110f6e4-a225-11ed-bf55-d33b0802ecdc',
            token: 'thisisasecrettokenveryverysecretsecret'
        }

### Get liveness result [POST /v1/liveness-new/result?challengeId={challengeId}]

+ Parameters
    + challengeId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
            "verificationStatus": "verified",
                # Possible values: ['unverified', 'pending', 'verified', 'rejected', 'reverifying']
            "rejectionReason": "Incorrect pose",
        }

## Liveness [/liveness]

### Start liveness challenge [POST /v1/liveness/start]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                imageWidth: 1000,
                imageHeight: 1000,
            }

+ Response 200 (application/json)

        {
            challenge: {
                id: '1110f6e4-a225-11ed-bf55-d33b0802ecdc',
                imageWidth: 1000,
                imageHeight: 1000,
                areaLeft: 218,
                areaTop: 125,
                areaWidth: 562,
                areaHeight: 750,
                minFaceAreaPercent: 50,
                noseLeft: 412,
                noseTop: 525,
                noseWidth: 20,
                noseHeight: 20,
            }
        }

### Post liveness frame [POST /v1/liveness/frame?challengeId={challengeId}]

+ Parameters
    + challengeId (string)

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            type="file" name="image"

+ Response 200 (application/json)

        {
        }

### Verify liveness challenge [POST /v1/liveness/verify]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                challengeId: '1110f6e4-a225-11ed-bf55-d33b0802ecdc',
            }

+ Response 200 (application/json)

        {
            "verificationStatus": "verified",
                # Possible values: ['unverified', 'pending', 'verified', 'rejected', 'reverifying']
            "rejectionReason": "Incorrect pose",
        }

### Request manual verification [POST /v1/liveness/requestManualVerification]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                challengeId: '1110f6e4-a225-11ed-bf55-d33b0802ecdc',
            }

+ Response 200 (application/json)

        {
        }

## Web [/web]

### Send login email from backend [PUT /web/emailLogin]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "deviceId": "id",
                "webDeviceId": "id",
                "email": "email",
                "timezone": "Europe/London",
                "locale": "en"
            }

+ Response 200 (application/json)

        {
            allowed: true,
            useLegacy: false,  # if true, then app should use the legacy method of requesting Firebase to send the email
        }

### Submit quiz answers [PUT /web/quizAnswers]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "timezone": "Asia/Shanghai",
                "answers": {
                    "EbzFD": 0.2,
                    "orju0": 0.5,
                    ...
                }
            }

+ Response 200 (application/json)

        {
            "mbti": "ENFP",
            "EI": 0.6,
            "NS": 0.6,
            "FT": 0.6,
            "JP": 0.6,
        }

### Submit enneagram quiz answers [PUT /web/enneagramQuizAnswers]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "deviceId": "",
                "timezone": "Asia/Shanghai",
                "enneagram": "9w1",
                "answers": {
                    "1": "1w2",
                    "2": "3w4",
                    ...
                },
            }

+ Response 200 (application/json)

        {
        }

### Get num quizzes taken [GET /web/numQuizzesTaken]

+ Response 200 (application/json)

        {
            numQuizzesTaken: 10,
        }

### Register web visitor and get config [PUT /web/visitor]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "webDeviceId": "id",
                "locale": "en",
                "platform": "mobile",  # "desktop" or "mobile"
            }

+ Response 200 (application/json)

        {
            "config": {
                "sign_up_button": true,
            }
        }

### Register app visitor [PUT /web/appVisitor]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "appVersion": "1.13.68",
                "deviceId": "id",
                "locale": "en",
                "kochava": {},
                "appsflyer": {},
                "appsflyer_id": "id"
            }

+ Response 200 (application/json)

        {
            "config": {
                "app_459": true/false, # Optional
                "app_483": true/false, # Optional
            },
            "countryCode": "US",
        }

### Get num users per language [GET /web/numUsersPerLanguage]

+ Response 200 (application/json)

        {
            numUsersPerLanguage: {
              en: 10,
              de: 3,
            },
        }

### Get num followers per interest [GET /web/numFollowersPerInterest]

+ Response 200 (application/json)

        {
            numFollowersPerInterest: {
              music: 10,
              movies: 3,
            },
        }

### Check if phone login is allowed [PUT /web/phoneLogin]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "phoneNumber": "+16505551111",
            }

+ Response 200 (application/json)

        {
            allowed: true
        }

### Check if auth is allowed [PUT /web/isAuthAllowed]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "deviceId": "id",
                "webDeviceId": "id",
                "email": "email",
            }

+ Response 200 (application/json)

        {
            allowed: true
        }

### Get database autocomplete [GET /web/database/autocomplete]

+ Parameters
    + query

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            categories: [],
            subcategories: [],
            profiles: [],
        }

### Get database profile [GET /web/database/profile]

+ Parameters
    + id (the id of the profile)
    + locale

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            profile: {
                _id: 'string',
                id: {type: Number, unique: true},
                createdAt: { type: Date, default: Date.now },
                createdBy: {
                  _id: 'string',
                  handle: 'handle',
                },
                name: {type: String},
                mbti: {type: String},
                enneagram: {type: String},
                horoscope: {type: String},
                description: {type: String},
                countries: ['string'],
                subcategories: [{type: Number}],
                image: {type: String},
                imageSource: {type: String},
                confidence_score: {
                  mbti: 0,
                  enneagram: 0,
                  horoscope: 0,
                  total: 0,
                  },
                vote:{
                  totalCount:1,
                  mbti:{
                    INTJ:1,
                  },
                  enneagram:{
                    1w1: 1,
                  },
                  horoscope:{
                    Gemini: 1,
                  },
                },
                intros: {
                  en: {

                  },
                },
                lastUpdated: Date,
                "linkedCategories": [ { slug: 'slug', , id: 1 } ],
                "linkedSubcategories": [ { slug: 'slug', categoryId: 1 } ],
                "linkedProfiles": [ { slug: 'slug', id: 1 } ],
                linkedPillarKeywords: {
                    {languageCode}: [
                        {
                        url: String,
                        keyword: String,
                        },
                        {
                        url: String,
                        keyword: String,
                        },
                    ],
                },
                translatedNames:{
                    {locale}: String,
                },
                imageAttribution: String,
            }
        }

### Get database subcategories with profiles [GET /web/database/subcategoriesWithProfiles]

+ Parameters
    + categoryId (number)
    + filter (string, examples: infp, introverts, 1w9, aries)
    + page (number)

+ Response 200 (application/json)

        {
            subcategoriesWithProfiles: [
                {
                    id: 123,
                    name: 'Naruto',
                    slug: 'naruto',
                    category: 2,
                    url: '/database/anime/naruto',
                    profiles: [
                        {
                            _id: 'string',
                            id: {type: Number, unique: true},
                            createdAt: { type: Date, default: Date.now },
                            createdBy: {
                              _id: 'string',
                              handle: 'handle',
                            },
                            name: {type: String},
                            mbti: {type: String},
                            enneagram: {type: String},
                            horoscope: {type: String},
                            description: {type: String},
                            categories: [
                                {
                                    id: 123,
                                    name: 'Anime',
                                    slug: 'anime',
                                    url: '/database/anime',
                                }
                            ],
                            subcategories: [
                                {
                                    id: 123,
                                    name: 'Naruto',
                                    slug: 'naruto',
                                    category: 2,
                                    url: '/database/anime/naruto',
                                }
                            ],
                            image: {type: String},
                            imageSource: {type: String},
                            url: '/database/profile/123/naruto',
                        },
                    ],
                },
            ],
        }

### Get database profiles [GET /web/database/profiles]

+ Parameters
    + subcategoryId (number)
    + filter (string, examples: infp, introverts, 1w9, aries)
    + page (number)

+ Response 200 (application/json)

        {
            profiles: [
                {
                    _id: 'string',
                    id: {type: Number, unique: true},
                    createdAt: { type: Date, default: Date.now },
                    createdBy: {
                      _id: 'string',
                      handle: 'handle',
                    },
                    name: {type: String},
                    mbti: {type: String},
                    enneagram: {type: String},
                    horoscope: {type: String},
                    description: {type: String},
                    categories: [
                        {
                            id: 123,
                            name: 'Anime',
                            slug: 'anime',
                            url: '/database/anime',
                        }
                    ],
                    subcategories: [
                        {
                            id: 123,
                            name: 'Naruto',
                            slug: 'naruto',
                            category: 2,
                            url: '/database/anime/naruto',
                        }
                    ],
                    image: {type: String},
                    imageSource: {type: String},
                    url: '/database/profile/123/naruto',
                    lastUpdated: Date
                },
            ],
        }



### Get leadership board[GET /web/database/leaders&page={page}]

+ Parameters
    + page (Number) //zero-based index

+ Response 200 (application/json)

        {
            "leaders": [{
              _id: '',
              firstName: '',
              picture: '',
              'personality.mbti': '',
              enneagram: '',
              horoscope: '',
              numDbUploads: 1,
              dbUploadCoinsReceived: 10,
              dbUploadKarmaReceived: 10,
            }]
        }

### Get all database profiles [GET /web/cached/allDatabaseProfiles]

+ Response 200 (application/json)

        {
            allDatabaseProfiles: [
                {
                    id: 10,
                    name: 'Naruto',
                    subcategories: [3, 5],
                    vote: {
                        totalCount: 6,
                    },
                },
            ]
        }

### Unsubscribe email [PUT /web/unsubscribe-email?email={$email}&hash={$hash}]

+ Response 200 (application/json)

        {
        }

## Admin [/admin]

### Ban a user [PUT /v1/admin/ban]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id",
                "bannedReason": "scammer",
                "bannedNotes": "reason",
            }

+ Response 200 (application/json)

        {
        }

### Update banned notes [PUT /v1/admin/bannedNotes]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "bannedNotes": "reason",
            }

+ Response 200 (application/json)

        {
        }

### Unban a user [PUT /v1/admin/unban]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id"
                "notes": "notes",
            }

+ Response 200 (application/json)

        {
        }

### Get reported comments [GET /v1/admin/reportedComments]

Returns one page. No specific sort order or pagination.
App should call this route again to get more after all processed.

+ Response 200 (application/json)

        {
            "comments": [
                {
                  ...,
                  questionUrl: "https://...",
                }
            ]
        }

### Ban a comment [PUT /v1/admin/banComment]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "id"
            }

+ Response 200 (application/json)

        {
        }

### Dismiss a comment [PUT /v1/admin/dismissComment]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "commentId": "id"
            }

+ Response 200 (application/json)

        {
        }

### Get reported questions [GET /v1/admin/reportedQuestions]

Returns one page. No specific sort order or pagination.
App should call this route again to get more after all processed.

+ Response 200 (application/json)

        {
            "questions": [
                { ... }
            ]
        }

### Ban a question [PUT /v1/admin/banQuestion]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "id"
            }

+ Response 200 (application/json)

        {
        }

### Dismiss a question [PUT /v1/admin/dismissQuestion]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "questionId": "id"
            }

+ Response 200 (application/json)

        {
        }

### Get reports [GET /v1/admin/reports]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            reports: [
                {
                    createdAt: "2020-02-08T02:56:42.494Z",
                    reportedUser: 'user1',
                    reportedBy: 'user2',
                    reason: ['spam'],
                    comment: 'comment',
                    status: 'needs review',
                    adminNotes: '',
                    handledBy: 'adminId',
                }
            ]
        }

### Get recently verified reports [GET /v1/admin/recentlyVerifiedReports]

Returns 100 most recent, no pagination.

+ Response 200 (application/json)

        {
            reports: [
                { ... }
            ]
        }

### Get recently dismissed reports [GET /v1/admin/recentlyDismissedReports]

Returns 100 most recent, no pagination.

+ Response 200 (application/json)

        {
            reports: [
                { ... }
            ]
        }

### Get reports for user [GET /v1/admin/reportsForUser?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            reports: [
                { ... }
            ]
        }

### Get processed coin purchases for user [GET /v1/admin/user/coinPurchases?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            purchases: [
                {
                    productId: '',
                    purchaseDate: '',
                }
            ]
        }

### Get processed super like purchases for user [GET /v1/admin/user/superLikePurchases?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            purchases: [
                {
                    productId: '',
                    purchaseDate: '',
                }
            ]
        }

### Get processed neuron purchases for user [GET /v1/admin/user/neuronPurchases?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            purchases: [
                {
                    productId: '',
                    purchaseDate: '',
                }
            ]
        }

### Get processed boost purchases for user [GET /v1/admin/user/boostPurchases?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            purchases: [
                {
                    productId: '',
                    purchaseDate: '',
                }
            ]
        }

### Get coin history for user [GET /v1/admin/user/coinTransactions?user={user}&before={before}]

+ Parameters
    + user (string)
    + before (string)

+ Response 200 (application/json)

        {
            transactions: [
                {
                    _id: '65c2d6670e1488d6e78d333f',
                    createdAt: '2024-02-11T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 4,
                    newBalance: 14,
                    description: 'Test balance 4'
                },
                {
                    _id: '65c2d6670e1488d6e78d333d',
                    createdAt: '2024-02-10T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 3,
                    newBalance: 9,
                    description: 'Test balance 3'
                },
                {
                    _id: '65c2d6670e1488d6e78d333b',
                    createdAt: '2024-02-09T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 2,
                    newBalance: 5,
                    description: 'Test balance 2'
                },
                {
                    _id: '65c2d6670e1488d6e78d3339',
                    createdAt: '2024-02-08T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 1,
                    newBalance: 2,
                    description: 'Test balance 1'
                },
                {
                    _id: '65c2d6670e1488d6e78d3336',
                    createdAt: '2024-02-07T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 0,
                    newBalance: 0,
                    description: 'Test balance 0'
                }
            ]
        }

### Get boost history for user [GET /v1/admin/user/boostTransactions]

+ Parameters
    + user (string)
    + before (string)

+ Response 200 (application/json)

        {
            transactions: [
                {
                    _id: '65c2d6670e1488d6e78d333f',
                    createdAt: '2024-02-11T01:01:27.000Z',
                    user: '1',
                    transactionAmount: 4,
                    newBalance: 14,
                    description: 'Test balance 4'
                },
            ]
        }

### Get super like history for user [GET /v1/admin/user/superLikeTransactions]

+ Parameters
    + user (string)
    + before (string)

+ Response 200 (application/json)

        {
            transactions: [
                {
                    _id: '65c2d6670e1488d6e78d333f',
                    createdAt: '2024-02-11T01:01:27.000Z',
                    user: '1',
                    freeSuperLoveTransactionAmount: 4,
                    freeSuperLoveNewBalance: 14,
                    paidSuperLoveTransactionAmount: 4,
                    paidSuperLoveNewBalance: 14,
                    description: 'Test balance 4'
                },
            ]
        }

### Get post reports for user [GET /v1/admin/user/postReports?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            reports: [
                {
                    createdAt: '',
                    reportedBy: '',
                    reason: [''],
                    explanation: '',
                    status: '',
                    reportedQuestion: '',
                    reportedComment: '',
                    prompt: '',
                    output: '',
                    openaiBan: '',
                    openaiExplanation: '',
                }
            ]
        }

### Get message reports for user [GET /v1/admin/user/messageReports?user={user}]

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            reports: [
                {
                    createdAt: '',
                    keyword: '',
                    decision: '',
                    messages: '',
                    openai: {
                        prompt: '',
                        output: '',
                        ban: '',
                        explanation: '',
                    },
                }
            ]
        }

### Get user [GET /v1/admin/user?id={id}]

The id parameter can be either id or handle.

+ Parameters
    + id (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            user: { ... },
            metadata: { ... },
            verification: {
                status: 'unverified',
                pictures: ['samples/sample1.jpg'],
                verifiedBy: 'uid',
            },
            reports: [
                { ... }
            ],
            accountsWithSameDeviceId: ['id'],
            bannedProfileData: [
                {
                    date: "2020-02-08T02:56:42.494Z",
                    profile: { ... },
                },
            ],
            deletedAccount: true,
        }

### Find user by profile details [GET /v1/admin/userByProfileDetails]

Requires "manager" permissions.
All parameters are optional, provide as many as possible to narrow the results.
Up to 10 users are returned, in the same format as GET /v1/admin/user.
All parameters are case-sensitive. Provide them exactly as written on the user's profile.

+ Parameters
    + firstName (string)
    + age (number)
    + gender (string)
    + mbti (string)
    + horoscope (string)
    + city (string)
    + work (string)
    + education (string)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                { ... },
            ],
        }

### Get messages sent by user [GET /v1/admin/messagesSentByUser?user={user}]

Returns 100 messages sent by the user, not sorted or paginated.

+ Parameters
    + user (string)

+ Response 200 (application/json)

        [
            { ... }
        ]

### Get messages [GET /v1/admin/messages?reportedBy={reportedBy}&reportedUser={reportedUser}&before={before}]

+ Parameters
    + reportedBy (string)
    + reportedUser (string)
    + before (datetime) ... 2020-01-19T21:16:41.901Z

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        [
            { ... }
        ]

### Get user's deleted chats [GET /v1/admin/deletedChats?user={user}]

Returns up to 100 deleted chats, sorted by date of last message. No pagination.
Chat objects in response formatted the same way as /v1/chat routes.

+ Response 200 (application/json)

        {
            deletedChats: [
                { ... },
            ]
        }

### Get messages for a deleted chat [GET /v1/admin/deletedChatMessages?chatId={chatId}]

Returns up to 100 messages for a deleted chat, sorted by date. No pagination.
Message objects in response formatted the same way as /v1/message routes.

+ Response 200 (application/json)

        {
            messages: [
                { ... },
            ]
        }

### Get users who blocked a user [GET /v1/admin/usersWhoBlocked?user={user}]

Returns the 10 most recent users who blocked this user, formatted the same way as daily profiles.

+ Parameters
    + user (string)

+ Response 200 (application/json)

        {
            users: [
                { ... }
            ]
        }

### Dismiss reports [PUT /v1/admin/dismissReports]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id"
            }

+ Response 200 (application/json)

        {
        }

### Get profiles to verify [GET /v1/admin/verifyProfile]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users:
              [
                  {
                        "_id": "5dfee1eb5eeb4c58787a9394",
                        "firstName": "Sarah",
                        "pictures": [
                            "samples/sample1.jpg"
                        ],
                        "personality": {
                            "mbti": "ESFP",
                        },
                        "gender": "female",
                        "age": 35,
                        "description": "Lorem ipsum",
                        "audioDescription": "path/to/sample.wav",
                        "audioDescriptionWaveform": [1.2, 1.4],
                        "audioDescriptionDuration": 30,
                        "education": "college",
                        "work": "company",
                        "enneagram": "1w9",
                        "moreAboutUser": {
                            "exercise": "active",
                            "education": "sixth form",
                            "drinking": "socially",
                            "smoking": "socially",
                            "kids": "Not Sure Yet",
                            "religion": "agnostic",
                        },
                        "prompts": [
                            {
                                "id": "id",
                                "answer": "answer"
                            }
                        ],
                        "city": 'Honolulu',
                        'state': 'Hawaii',
                        'country': 'United States',
                        "horoscope": "Capricorn",
                        "interestNames": ["chess"],
                        "karma": 0,
                        "metrics": {
                            "numFollowers": 0,
                        },
                        "awards": {
                          "reward_id_1": 2,
                          "reward_id_2": 1,
                        },
                        "languages": ['en', 'de'],
                        "handle": "jill",
                        'shadowBanned': true,
                        createdAt: "2020-02-08T02:56:42.494Z",
                        updatedAt: "2020-02-08T02:56:42.494Z",
                        deviceId: 'deviceid',
                        timezone: 'Asia/Manila',
                        telepathyPreviewMbti: 'INFP',
                        actualCity: 'Honolulu',
                        actualState: 'Hawaii',
                        actualCountry: 'United States',
                        verification: {
                            status: 'pending',
                            updatedAt: "2020-02-08T02:56:42.494Z",
                            pictures: [ 'picture' ],
                            verifiedBy: 'id',
                            verifiedDate: 'date',
                        },
                        livenessVerification: {
                            id: { type: String },
                            imageWidth: { type: Number },
                            imageHeight: { type: Number },
                            areaLeft: { type: Number },
                            areaTop: { type: Number },
                            areaWidth: { type: Number },
                            areaHeight: { type: Number },
                            minFaceAreaPercent: { type: Number },
                            noseLeft: { type: Number },
                            noseTop: { type: Number },
                            noseWidth: { type: Number },
                            noseHeight: { type: Number },
                            frames: [
                              {
                                timestamp: { type: Date },
                                key: { type: String },
                              }
                            ],
                            livenessSuccess: { type: Boolean },
                            livenessFailureReason: { type: String },
                            matchingPictures: [ { type: String } ],
                            notMatchingPictures: [ { type: String } ],
                            compareFacesSuccess: { type: Boolean },
                            rejectionReason: { type: String },
                            date: { type: Date },
                            manuallyCheckedBy: { type: String, ref: 'User' },
                            manuallyCheckedDate: { type: Date },
                            manuallyCheckedResult: { type: Boolean },
                            manuallyCheckedRejectionReason: { type: String },
                        },
                  }
            ]
        }

### Get profiles to verify (pending 1st time) [GET /v1/admin/verifyProfile/pending]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                {
                  ...,
                  reports: [ ],
                }
            ]
        }

### Get profiles to verify (reverifying) [GET /v1/admin/verifyProfile/reverifying]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                { ... }
            ]
        }

### Get profiles to verify (pending 1st time) [GET /v1/admin/verifyProfile/pendingQueued]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                {
                  ...,
                  reports: [ ],
                }
            ]
        }

### Get profiles to verify (reverifying) [GET /v1/admin/verifyProfile/reverifyingQueued]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                { ... }
            ]
        }

### Get recently verified profiles [GET /v1/admin/recentlyVerifiedProfiles]

Returns up to 100 of the most recently verified profiles.
Pagination not supported.

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            users: [
                { ... }
            ]
        }

### Verify profile [PATCH /v1/admin/verifyProfile]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id",
                "verified": false,
                "rejectionReason":'Incorrect pose'|'No facial photo'|'Not same person'|'Photo unclear'//if verified false only
            }

+ Response 200 (application/json)

        {
        }

### Update user birthday [PUT /v1/admin/userBirthday]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": 'id',
                "year": 1990,
                "month": 1,     // 1 is January
                "day": 1
            }

+ Response 200 (application/json)

        {
        }

### Set user location [PUT /v1/admin/userLocation]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                city: 'city',         # optional
                state: 'state',       # optional
                country: 'country',   # optional
            }

+ Response 200 (application/json)

        {
            "location": "Brooklyn, NY",
        }

### Delete admin-specified user location [DELETE /v1/admin/userLocation]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
            }

+ Response 200 (application/json)

        {
            "location": "Brooklyn, NY",
        }

### Update user premium expiration [PUT /v1/admin/userPremiumExpiration]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": 'id',
                "year": 1990,
                "month": 1,     // 1 is January
                "day": 1
            }

+ Response 200 (application/json)

        {
        }

### Give user coins [PATCH /v1/admin/incrementCoins]

+ Request (application/json)

    + Body

            {
                "user": 'id',
                "coins": 100,
            }

+ Response 200 (application/json)

        {
        }

### Give user super likes [PATCH /v1/admin/incrementSuperLikes]

+ Request (application/json)

    + Body

            {
                "user": 'id',
                "numSuperLikes": 3,
            }

+ Response 200 (application/json)

        {
        }

### Give user boosts [PATCH /v1/admin/incrementBoosts]

+ Request (application/json)

    + Body

            {
                "user": 'id',
                "numBoosts": 3,
            }

+ Response 200 (application/json)

        {
        }

### Give user neurons [PATCH /v1/admin/incrementNeurons]

+ Request (application/json)

    + Body

            {
                "user": 'id',
                "numBooAINeurons": 3,
            }

+ Response 200 (application/json)

        {
        }

### Update user config [PATCH /v1/admin/userConfig]

Requires adminPermission `setConfig`

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                configName: 'test_config',
                configValue: true,
            }

+ Response 200 (application/json)

        {
        }

### Update user gender [PATCH /v1/admin/userGender]

Requires adminPermission `support`

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                gender: 'male/female/non-binary'
            }

+ Response 200 (application/json)

        {
        }

### Get pending interests [GET /v1/pendingInterests]

+ Response 200 (application/json)

        {
            interests: [
                {
                    name: 'name',
                }
            ]
        }

### Approve interest [PUT /v1/approveInterest]

+ Request (application/json)

    + Body

            {
                name: 'name',
            }

+ Response 200 (application/json)

        {
        }

### Reject interest [PUT /v1/rejectInterest]

+ Request (application/json)

    + Body

            {
                name: 'name',
            }

+ Response 200 (application/json)

        {
        }

### Update User Phone Number [PUT /v1/admin/userNumber]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id",
                "phoneNumber":"+***********"  # null to remove phone number
            }

+ Response 200 (application/json)

        {
        }

### Update User Email [PUT /v1/admin/userEmail]

Note: email cannot be removed

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "user": "id",
                "email": "<EMAIL>",
            }

+ Response 200 (application/json)

        {
        }

### Delete User Account Immediately [DELETE /v1/admin/user]
Cannot delete admin acoounts of same or higher levels
+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "userId": "id"
            }

+ Response 200 (application/json)

        {
        }

### Temporarily Ban User [PUT /v1/admin/tempBan]

The user to temp ban must be on app version 1.11.45+. Reason must be non empty string.

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                reason:String,
                banUser:"id"
            }

+ Response 200 (application/json)

        {
        }

### Undo Temp Ban [PUT /v1/admin/undoTempBan]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                user: 'id',
                notes: 'notes',
            }

+ Response 200 (application/json)

        {
        }

### Get qod candidates [GET /v1/admin/questionCandidates]

The id parameter can be either id or handle.

+ Parameters
    + lastSent (id string)(optional)
    + status (optional [pending|approved|rejected]) default is pending
    + language (optional) default is all
    + order (optional [asc|desc]) default is desc
    + count (number) //indicates page max count (default set by server)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            candidates: [{
                id:"string",
                createdBy:"id"|null,
                isAnonymous: boolean,
                language:"en"|undefined,
                createdAt: "date",
                text: "string",
                status: "string",
                reviewedBy: "id|null",
                reviewedAt: "date|null",
            }]
        }

### Apprrove/Reject qod candidates [POST /v1/admin/questionCandidates/status]

The id parameter can be either id or handle.

+ Parameters
    + id (id string)
    + status ([approved|rejected])

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {        }

### Get profile candidates [GET /v1/admin/database/profileCandidate]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            candidates: [{
                _id: 'string',
                createdAt: { type: Date, default: Date.now },
                createdBy: { type: String, ref: 'User' },
                name: {type: String},
                mbti: {type: String},
                enneagram: {type: String},
                horoscope: {type: String},
                description: {type: String},
                subcategories: [
                  {
                    name: 'name',
                  },
                ],
                newSubcategory: {
                  name: 'New Subcategory',
                  categorySlug: 'anime',
                },
                image: {type: String},
                imageSource: {type: String},
            }]
        }

### Approve/Reject profile candidate [POST /v1/admin/database/profileCandidate/status]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                _id: 'candidate _id',
                status: 'approved|rejected',
            }

+ Response 200 (application/json)

        {        }

### Get profile image candidates [GET /v1/admin/database/profileImageCandidate]

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            candidates: [{
                _id: 'string',
                createdAt: { type: Date, default: Date.now },
                createdBy: { type: String, ref: 'User' },
                image: {type: String},
                imageSource: {type: String},
                profile: {
                    _id: 'string',
                    id: {type: Number, unique: true},
                    createdAt: { type: Date, default: Date.now },
                    createdBy: { type: String, ref: 'User' },
                    name: {type: String},
                    mbti: {type: String},
                    enneagram: {type: String},
                    horoscope: {type: String},
                    description: {type: String},
                    subcategories: [
                      {
                        name: 'name',
                      },
                    ],
                    image: {type: String},
                },
            }]
        }

### Approve/Reject profile image candidate [POST /v1/admin/database/profileImageCandidate/status]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                _id: 'candidate _id',
                status: 'approved|rejected',
            }

+ Response 200 (application/json)

        {        }

### Get user translations [GET /v1/admin/translations]

The id parameter can be either id or handle.

+ Parameters
    + lastSent (id string)(optional)
    + status (optional [pending|approved|rejected]) default is pending
    + language (optional) if not sent , shows all languages data
    + order (optional [asc|desc]) default is desc
    + count (number) //indicates page max count (default set by server)

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {
            translations: [{
                _id:"string",
                createdBy:"id"|null,
                refImage:"string"|undefined,
                createdAt: "date",
                currentTranslation: "string",
                correctTranslation: "string",
                language:"en",
                details: "string"|undefined,
                status: "string",
                reviewedBy: "id|null",
                reviewedAt: "date|null",
            }]
        }

### Apprrove/Reject user translations [POST /v1/admin/translations/status]

+ Parameters
    + id (id string)
    + status ([approved|rejected])

+ Request (application/json)

    + Headers

            Authorization: token

+ Response 200 (application/json)

        {        }

### Search receipts by transactionId [GET /v1/admin/receipts]

Requires adminPermission `support`

+ Parameters
    + transactionId (string)

+ Request (application/json)

    + Headers

        Authorization: token

    + Body

        {
        }

+ Response 200 (application/json)

        {
            user: { type: String },
            purchaseDate: { type: Date },
            productId: { type: String },
            currency: { type: String },
            price: { type: Number },
        }

### Search QOD for User [GET /v1/admin/qods/byUser]

Requires adminPermission `support`

+ Parameters
    + user (string)

+ Request (application/json)

    + Headers

        Authorization: token

+ Response 200 (application/json)

    {
        qods: [
            { _id: 'id', createdAt: 'date', text: 'string', language: 'string' },
        ],
        questionCandidates: [
            { _id: 'id', createdAt: 'date', text: 'string', language: 'string', isAnonymous: boolean}
        ]
    }

### Delete QOD [DELETE /v1/admin/qod]

Requires adminPermission `support`

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

        Authorization: token

+ Response 200 (application/json)

    {
    }

### Delete question candidate [DELETE /v1/admin/questionCandidate]

Requires adminPermission `support`

+ Parameters
    + questionId (string)

+ Request (application/json)

    + Headers

        Authorization: token

+ Response 200 (application/json)

    {
    }

### Get profiles for scammer cleanup review [GET /v1/admin/scammerCleanup/review?queue={queue}]

Requires adminPermission `support`

+ Parameters
    + queue (number between 1-7)

+ Response 200 (application/json)

    {
        profiles: [
            {
                user: '_id',
                verificationPhoto: 'https://images.prod.boo.dating/...',
            },
        ],
    }

### Get profiles for scammer cleanup final review [GET /v1/admin/scammerCleanup/reviewFinal]

Requires adminPermission `manager`

+ Response 200 (application/json)

    {
        profiles: [
            {
                user: '_id',
                verificationPhoto: 'https://images.prod.boo.dating/...',
                review1: {
                    queue: { type: Number },
                    reviewedBy: { type: String },
                    reviewedAt: { type: Date },
                    decision: { type: String },
                },
                review2: {
                    queue: { type: Number },
                    reviewedBy: { type: String },
                    reviewedAt: { type: Date },
                    decision: { type: String },
                },
            },
        ],
    }

### Submit decision for scammer cleanup review [PUT /v1/admin/scammerCleanup/review/decision]

Requires adminPermission `support`

+ Request (application/json)

    + Body

            {
                queue: 1,
                user: '_id',
                decision: 'ban|dismiss|verify|unverify',
            }

+ Response 200 (application/json)

    {
    }

### Submit decision for scammer cleanup final review [PUT /v1/admin/scammerCleanup/reviewFinal/decision]

Requires adminPermission `manager`

+ Request (application/json)

    + Body

            {
                user: '_id',
                decision: 'ban|dismiss|verify|unverify',
            }

+ Response 200 (application/json)

    {
    }

### Get profiles for verification review [GET /v1/admin/verifyProfileNew/review?queue={queue}]

Requires adminPermission `support`

+ Parameters
    + queue (number between 1-2)

+ Response 200 (application/json)

    {
        users: [
            { ... }, # same format as [GET /v1/admin/verifyProfile]
        ],
    }

### Get profiles for verification final review [GET /v1/admin/verifyProfileNew/reviewFinal]

Requires adminPermission `manager`

+ Response 200 (application/json)

    {
        users: [
            {
                ..., # same format as [GET /v1/admin/verifyProfile]
                verification: {
                    ...,
                    manualReview: {
                        review1: {
                            queue: { type: Number },
                            reviewedBy: {
                                _id: { type: String },
                                email: { type: String },
                            }
                            reviewedAt: { type: Date },
                            decision: { type: String },
                        },
                        review2: {
                            queue: { type: Number },
                            reviewedBy: {
                                _id: { type: String },
                                email: { type: String },
                            }
                            reviewedAt: { type: Date },
                            decision: { type: String },
                        },
                    }
                }
            },
        ],
    }

### Submit decision for verification review [PUT /v1/admin/verifyProfileNew/review/decision]

Requires adminPermission `support`

+ Request (application/json)

    + Body

            {
                queue: 1,
                user: '_id',
                decision: 'verify|reject|ban',
                rejectionReason: 'reason',
                bannedReason: 'scammer',
                bannedNotes: 'reason',
            }

+ Response 200 (application/json)

    {
    }

### Submit decision for verification final review [PUT /v1/admin/verifyProfileNew/reviewFinal/decision]

Requires adminPermission `manager`

+ Request (application/json)

    + Body

            {
                user: '_id',
                decision: 'verify|reject|ban',
                rejectionReason: 'reason',
                bannedReason: 'scammer',
                bannedNotes: 'reason',
            }

+ Response 200 (application/json)

    {
    }

## Pusher [/pusher]

### Auth [POST /pusher/user-auth]

+ Parameters
    + authorization

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                socket_id: 'id',
            }

+ Response 200 (application/json)

        {
            "auth": "$AUTHORIZATION_STRING",
            "user_data": "$USER_DATA",
        }

## Stripe [/stripe]

### Get products [GET /v1/stripe/products]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            currency: 'usd',
            m1: {
              price: 20,
              unavailable: true,
            },
            m3: {
              price: 40,
            },
            m6: {
              price: 70,
              discount: 50,
            },
            m12: {
              price: 130,
              discount: 50,
            },
            lifetime: {
              price: 200,
            },
            100_coins: {
              price: 200,
            },
            super_love_3_v1: {
              price: 200,
            },
            6_neurons: {
              price: 200,
            },
            boosts_5_v1: {
              price: 31.99
            }
        }

### Create checkout session [POST /v1/stripe/create-checkout-session]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                "product": "m1",
                "redirectUrl": "url",
            }

+ Response 200 (application/json)

        {
            redirect: 'url',
        }

### Create portal session [POST /v1/stripe/create-portal-session]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
            }

+ Response 200 (application/json)

        {
            redirect: 'url',
        }

## Socket events to listen for [/socket]

### numYourTurnChats update
{
  numYourTurnChats: 45,
}

### karma
{
  karmaGained: 1,
  totalKarma: 10,
  reason: 'First Comment',
}

### typing
{
  chatId: 'chatId',
  partnerId: 'uid',
}

### verification
{
  verificationStatus: 'verified',
  verified: true,
  rejectionReason: '...',
}

### pending chat

This event is only sent to the user who needs to approve it

{
  "_id": "5e3e236a01e6c94b28b1227e",
  "pendingUser": "7c10e12da6b1669531f2173c9a3252005b04bf31",
  "lastMessage": {
    "_id": "5e3e236a01e6c94b28b1227f",
    "chat": "5e3e236a01e6c94b28b1227e",
    "text": "Hi",
    "sender": "352761d3596d2dc0b0fde8ff7bf592f9c46f1626",
    "createdAt": "2020-02-08T02:56:42.494Z",
    "__v": 0
  },
  "createdAt": "2020-02-08T02:56:42.503Z",
  "lastMessageTime": "2020-02-08T06:07:55.275Z",
  "numUnreadMessages": 1,
  "__v": 0,
  "user": {
    "_id": "352761d3596d2dc0b0fde8ff7bf592f9c46f1626",
    "firstName": "f2f373da2b4d3e",
    "pictures": [
      "samples/sample1.jpg"
    ],
    "personality": {
        "mbti": "ESFP",
        "avatar": "Performer"
    }
  },
  "numUnreadMessages": 1
}

### approved chat

This event is only sent to the user who was approved

{
  "_id": "5e3e236a01e6c94b28b1227e",
  "pendingUser": null,
  "lastMessage": {
    "_id": "5e3e236a01e6c94b28b1227f",
    "text": "Hi",
    "sender": "352761d3596d2dc0b0fde8ff7bf592f9c46f1626",
    "createdAt": "2020-02-08T02:56:42.494Z"
  },
  "createdAt": "2020-02-08T02:56:42.503Z",
  "lastMessageTime": "2020-02-08T06:07:55.275Z",
  "numUnreadMessages": 1,
  "__v": 0,
  "user": {
    "_id": "7c10e12da6b1669531f2173c9a3252005b04bf31",
    "firstName": "518237f85964c3",
    "pictures": [
      "samples/sample1.jpg"
    ],
    "personality": {
        "mbti": "ESFP",
        "avatar": "Performer"
    }
  },
  "numUnreadMessages": 0,
  "noreply": true, # Optional, exists when true, Minimum APP Version 1.13.64
}

### deleted chat

{
  "_id": "5e3e236a01e6c94b28b1227e",
}

### chat read receipt

{
  "_id": "5e3e236a01e6c94b28b1227e",
  partnerNumUnreadMessages: 0,
}

### message

This event is only sent to the recipient of the message

{
  "_id": "5e3a4b790d33ea288ceec66e",
  "chat": "5e3a4b050d33ea288ceec66a",
  "text": "Message 1",
  "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
  "createdAt": "2020-02-05T04:58:33.041Z",
  "additionalInfo": { replaceText: 'Change Filter', openPage: 'filter' }, # Optional
  "__v": 0
}

### unsent message

This event is only sent to the recipient/recipients of the message

{
  "messageId": "5e3a4b790d33ea288ceec66e",
  "chatId": "5e3a4b050d33ea288ceec66a",
}

### message edited

This event is only sent to the recipient/recipients of the message

{
  "messageId": "5e3a4b790d33ea288ceec66e",
  "chatId": "5e3a4b050d33ea288ceec66a",
  "updatedMessage": "string",
}

### reaction

This event is only sent to the recipient of the reaction

{
    chatId: "67c4da73e834102217660aee",
    messageId: "67c4da73e834102217660b26",
    reaction: '❤️',
    sender: '1',
    firstName: 'name1',
    createdAt: 2025-03-02T22:23:47.781Z
}

### reaction removed

{
  messageId: "67c4da73e834102217660b26",
  chatId: "67c4da73e834102217660aee",
  removedReaction: { sender: '1', reaction: '😘', createdAt: 2025-03-02T22:23:47.887Z }
}

### coin reward

{
  "caption": "Referral Reward",
  "rewardAmount": 500,
  "newTotal": 650,
}

### super like reward

{
  "caption": "Rate App Reward",
  "rewardAmount": 1,
  "newTotal": 2,
}

### login reward

{
  "caption": "Daily Login Reward",
  "rewardAmount": 10,
  "newTotal": 210,
  "firstWeekLoginRewards": [
    {
      reward: 10,
      received: true,
      currentDay: true
    },
    {
      reward: 50,
      received: false,
      currentDay: false
    },
  ]
}

### prompt app rating

{
}

### initApp

{
}

### profileTempBan

{
  profileTempBanReason: 'reason',
}

### undoProfileTempBan

{
}

### ai image ready

{
  batchId: '682eabaaad3a64a3f15767ea',
  imageKey: '0/17478890665262210d60763fd9abdbc2f5cfefd94b991.jpg'
}


<!--## Socket events that frontend can send [/socket_send]-->

### typing
{
  chatId: 'chatId',
  partnerId: 'uid',
}

<!--### send message-->

<!--Argument 1: other user's id-->
<!--Argument 2: message text-->


<!--### message seen-->

<!--Argument 1: other user's id-->
<!--Argument 2: message's id-->

### automatedChatApproved

{
    _id: new ObjectId("670c9f80c5cda84279f33437"),
    createdAt: 2024-10-14T04:35:12.876Z,
    users: [
        {
        _id: 'MOCK_BOO_BOT_ID',
        firstName: '',
        pictures: [],
        description: '',
        education: '',
        work: undefined,
        crown: false,
        handle: 'graceful_apricot_pinniped_921',
        interests: [],
        verified: true,
        verificationStatus: 'verified'
        }
    ],
    user: {
        _id: 'MOCK_BOO_BOT_ID',
        firstName: '',
        pictures: [],
        description: '',
        education: '',
        work: undefined,
        crown: false,
        handle: 'graceful_apricot_pinniped_921',
        interests: [],
        verified: true,
        verificationStatus: 'verified'
    },
    lastMessage: {
        createdAt: 2024-10-14T04:35:12.882Z,
        chat: new ObjectId("670c9f80c5cda84279f33437"),
        sender: 'MOCK_BOO_BOT_ID',
        text: 'Welcome to Boo Support. What can we help you with?\n' +
        'Choose from one option below:',
        automatedChatOptions: [ {title: 'String', next_step: 'String' } ],
        _id: new ObjectId("670c9f80c5cda84279f3343c"),
        __v: 0
    },
    lastMessageTime: 2024-10-14T04:35:12.886Z,
    numMessages: 1,
    numUnreadMessages: 1,
    partnerNumUnreadMessages: undefined,
    pendingUser: null,
    instantMatch: undefined,
    expirationDate: undefined,
    groupChat: undefined,
    groupChatName: undefined,
    muted: false,
    pinned: undefined,
    viewLastSeenExpiration: undefined,
    dndMessage: false,
    dndPost: false,
    initiatedBySuperLike: undefined,
    matchIndicator: undefined,
    noreply: true,
    automatedChat: true,
    supportAdded: undefined
}

### automatedChatAutoResponse

{
    message: {
        createdAt: '2024-10-14T04:35:13.078Z',
        chat: '670c9f80c5cda84279f33437',
        sender: 'MOCK_BOO_BOT_ID',
        text: 'Is there anything else I can assist you with? If so, please select one of the options below:',
        automatedChatOptions: automatedChatOptions: [ {title: 'String', next_step: 'String' } ], #Optional
        _id: '670c9f81c5cda84279f33467',
        __v: 0
    },
    canWriteMessage: true/false
}

### supportAdded in automatedChat

{
    _id: new ObjectId("670ca1ef44ce41617262b0fe"),
    createdAt: 2024-10-14T04:45:35.342Z,
    users: [
        {
            _id: '0',
            firstName: 'name 0',
            pictures: [],
            profilePicture: undefined,
            personality: null,
            gender: null,
            age: null,
            height: undefined,
            ethnicities: [],
            description: '',
            audioDescription: undefined,
            audioDescriptionWaveform: undefined,
            audioDescriptionDuration: undefined,
            education: '',
            work: undefined,
            enneagram: undefined,
            moreAboutUser: {},
            prompts: [],
            crown: false,
            handle: 'linear_indigo_crab_103',
            location: null,
            teleport: false,
            preferences: [Object],
            hideQuestions: false,
            hideComments: false,
            horoscope: null,
            interests: [],
            interestNames: [],
            karma: null,
            numFollowers: null,
            verified: false,
            verificationStatus: 'unverified',
            awards: undefined,
            nearby: undefined,
            languages: undefined,
            timezone: undefined,
            spotify: undefined,
            hidden: false,
            stories: undefined,
            interestPoints: [],
            relationshipStatus: undefined,
            datingSubPreferences: undefined,
            relationshipType: undefined,
            sexuality: undefined
        },
        {
            _id: 'MOCK_BOO_BOT_ID',
            firstName: '',
            pictures: [],
            description: '',
            education: '',
            work: undefined,
            crown: false,
            handle: 'passing_pink_ermine_103',
            interests: [],
            verified: true,
            verificationStatus: 'verified'
        }
    ],
    user: {
        _id: '0',
        firstName: 'name 0',
        pictures: [],
        profilePicture: undefined,
        personality: null,
        gender: null,
        age: null,
        height: undefined,
        ethnicities: [],
        description: '',
        audioDescription: undefined,
        audioDescriptionWaveform: undefined,
        audioDescriptionDuration: undefined,
        education: '',
        work: undefined,
        enneagram: undefined,
        moreAboutUser: {},
        prompts: [],
        crown: false,
        handle: 'linear_indigo_crab_103',
        location: null,
        teleport: false,
        preferences: { showToVerifiedOnly: undefined, purpose: [] },
        hideQuestions: false,
        hideComments: false,
        horoscope: null,
        interests: [],
        interestNames: [],
        karma: null,
        numFollowers: null,
        verified: false,
        verificationStatus: 'unverified',
        awards: undefined,
        nearby: undefined,
        languages: undefined,
        timezone: undefined,
        spotify: undefined,
        hidden: false,
        stories: undefined,
        interestPoints: [],
        relationshipStatus: undefined,
        datingSubPreferences: undefined,
        relationshipType: undefined,
        sexuality: undefined
    },
    lastMessage: {
        _id: new ObjectId("670ca1ef44ce41617262b147"),
        createdAt: 2024-10-14T04:45:35.500Z,
        chat: new ObjectId("670ca1ef44ce41617262b0fe"),
        sender: '0',
        text: 'some random issues',
        notificationId: undefined,
        __v: 0
    },
    lastMessageTime: 2024-10-14T04:45:35.503Z,
    numMessages: 5,
    numUnreadMessages: 1,
    partnerNumUnreadMessages: undefined,
    pendingUser: null,
    instantMatch: undefined,
    expirationDate: undefined,
    groupChat: undefined,
    groupChatName: undefined,
    muted: false,
    pinned: undefined,
    viewLastSeenExpiration: undefined,
    dndMessage: false,
    dndPost: false,
    initiatedBySuperLike: undefined,
    matchIndicator: undefined,
    noreply: undefined,
    automatedChat: true,
    supportAdded: true
}

### support left automatedChat
{
    _id: new ObjectId("670ca2b7089d534d1d7428c7"),
    createdAt: 2024-10-14T04:48:55.902Z,
    users: [
        {
            _id: 'MOCK_BOO_BOT_ID',
            firstName: '',
            pictures: [],
            description: '',
            education: '',
            work: undefined,
            crown: false,
            handle: 'keen_olive_sheep_209',
            interests: [],
            verified: true,
            verificationStatus: 'verified'
        }
    ],
    user: {
        _id: 'MOCK_BOO_BOT_ID',
        firstName: '',
        pictures: [],
        description: '',
        education: '',
        work: undefined,
        crown: false,
        handle: 'keen_olive_sheep_209',
        interests: [],
        verified: true,
        verificationStatus: 'verified'
    },
    lastMessage: {
        _id: new ObjectId("670ca2b8089d534d1d742926"),
        chat: new ObjectId("670ca2b7089d534d1d7428c7"),
        sender: 'MOCK_BOO_SUPPORT_ID',
        text: 'test message from support user',
        createdAt: 2024-10-14T04:48:56.128Z,
        notificationId: undefined,
        __v: 0
    },
    lastMessageTime: 2024-10-14T04:48:56.131Z,
    numMessages: 6,
    numUnreadMessages: 4,
    partnerNumUnreadMessages: undefined,
    pendingUser: null,
    instantMatch: undefined,
    expirationDate: undefined,
    groupChat: undefined,
    groupChatName: undefined,
    muted: false,
    pinned: undefined,
    viewLastSeenExpiration: undefined,
    dndMessage: false,
    dndPost: false,
    initiatedBySuperLike: undefined,
    matchIndicator: undefined,
    noreply: true,
    automatedChat: true,
    supportAdded: undefined
}

## Push notifications [/notifications]

### pending chat

{
  "notification": {
    "title": "New chat request!",
    "body": "${name} has sent you a message!"
  },
  "data": {
    "pendingChat":

        /* this JSON object is encoded as a string
        {
          "_id": "5e3e236a01e6c94b28b1227e",
        }
        this JSON object is encoded as a string */

    }
}


### approved chat

{
  "notification": {
    "title": "New match!",
    "body": "${name} likes you too!"
  },
  "data": {
    "approvedChat":

        /* this JSON object is encoded as a string
        {
          "_id": "5e3e236a01e6c94b28b1227e",
        }
        this JSON object is encoded as a string */

    }
}


### message

{
  "notification": {
    "title": "${name}",
    "body": "${message}"
  },
  "data": {
    "message":

        /* this JSON object is encoded as a string
        {
          "_id": "5e3a4b790d33ea288ceec66e",
          "chat": "5e3a4b050d33ea288ceec66a",
          "text": "Message 1",
          "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
          "createdAt": "2020-02-05T04:58:33.041Z",
          "__v": 0
        }
        this JSON object is encoded as a string */

    }
}

### message reaction

{
    notification: { title: 'name1 reacted to your message', body: '😘' },
    apns: { payload: { aps: [Object] } },
    android: {
        priority: 'high',
        notification: { channelId: 'love', sound: 'love.wav' }
    },
    data: {
        reaction: '{"chatId":"67caae4a24e520d6c6fb6869","messageId":"67caae4b24e520d6c6fb68a1","reaction":"😘", "sender":"1","firstName":"name1","createdAt":"2025-03-07T08:28:59.276Z"}'
    },
    fcmOptions: { analyticsLabel: 'message-reaction' }
}

<!-- Diabled for now
Starting from 1.13.39:

 - A new notification structure for messages has been introduced

{
  "data": {
    "title": "${name}",
    "body": "${message.text}",
    notificationId: "${message.notificationId}",
    "message":

        /* this JSON object is encoded as a string
        {
          "_id": "5e3a4b790d33ea288ceec66e",
          "chat": "5e3a4b050d33ea288ceec66a",
          "text": "Message 1",
          "sender": "inClQpt6V3e5stEcbKNSEd06Bim2",
          "createdAt": "2020-02-05T04:58:33.041Z",
          "__v": 0
        }
        this JSON object is encoded as a string */

    }
}

### unsent message notification

Starting from 1.13.39:

 - When a message is unsent by the sender, this notification will be sent to the participant(s) of the chat

{
    data: {
      action: 'delete',
      notificationId: "${message.notificationId}",
    }
} -->




### comment

{
  "notification": {
    "title": "What do most people misunderstand about you?",
    "body": "Bobby: @Sarah I completely agree. When I was young..."
  },
  "data": {
    "comment":

        /* this JSON object is encoded as a string
        {
            "_id": "203520vcxdadwvb2305283v",
            "question": "4i2y34h2i5f338eeb2df469a4ca",
            "parent": "4i2y34h2i5f338eeb2df469a4ca",
            "postRepliedTo": "4i2y34h2i5f338eeb2df469a4ca", (nullable)
            "interest": {
                "_id": "82938",
                "interest": "#chess",
            },
            "interestName": "chess",
        }
        this JSON object is encoded as a string */

    }
}


### question

{
  "notification": {
    "title": "What do most people misunderstand about you?",
    "body": "Bobby: @Sarah I completely agree. When I was young..."
  },
  "data": {
    "question":

        /* this JSON object is encoded as a string
        {
            "_id": "203520vcxdadwvb2305283v",
            "interest": {
                "_id": "82938",
                "interest": "#chess",
            },
            "interestName": "chess",
        }
        this JSON object is encoded as a string */

    }
}

### award

{
  "data": {
    "award":

        /* this JSON object is encoded as a string
        {
            "id": "203520vcxdadwvb2305283v",
        }
        this JSON object is encoded as a string */

    },
    "senderId": "id",
}

### follow request

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "followRequest":

        /* this JSON object is encoded as a string
        {
        }
        this JSON object is encoded as a string */

    }
}

### follower

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "follower":

        /* this JSON object is encoded as a string
        {
        }
        this JSON object is encoded as a string */

    }
}

### following

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "following":

        /* this JSON object is encoded as a string
        {
        }
        this JSON object is encoded as a string */

    }
}

### karma

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "karma":

        /* this JSON object is encoded as a string
        {
            "karma": 100,
            "coins": 2100,
        }
        this JSON object is encoded as a string */

    }
}

### verification

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "verification":

        /* this JSON object is encoded as a string
        {
            "verificationStatus": "unverified",
            "verified": true,
            "rejectionReason":'Incorrect pose',
        }
        this JSON object is encoded as a string */

    }
}

### stories

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "stories":

        /* this JSON object is encoded as a string
        {
            "user": "_id",
            "story": "_id",
        }
        this JSON object is encoded as a string */

    }
}

### openPage

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "openPage": "universes"
  }
}

Options include:
  "universes"
  "match"
  "messages"
  "editProfile"

### premiumPopup

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "premiumPopup": "soulmate"
  }
}

Options include:
  "soulmate",
  "unlimitedLikes",
  "unlimitedDMs",
  "readReceipts",
  "countryFilter",
  "teleport",
  "spiritRealm",
  "timeTravel",
  "seeWhoViewed",


### message support

{
  "data": {
    "messageSupport":

        /* this JSON object is encoded as a string
        {
        }
        this JSON object is encoded as a string */

    }
}

### superLikeFlashSale

{
  "data": {
    "superLikeFlashSale":

        /* this JSON object is encoded as a string
        {
            super_like_discounted_product_ids: [
                'super_love_3_discount_30_v1',
                'super_love_12_discount_30_v1',
                'super_love_50_discount_30_v1',
            ],
            super_like_discount: 30,
        }
        this JSON object is encoded as a string */

    }
}

### coinsFlashSale

{
  "data": {
    "coinsFlashSale":

        /* this JSON object is encoded as a string
        {
        }
        this JSON object is encoded as a string */

    }
}

### AI Image Ready

{
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
     "openPage": "aiImagesResults"
    }
}

### Get question Internal Linking [GET /web/question/internalLinking]

+ Query Parameters
    + questionId (the id of the question)

+ Request (application/json)

+ Response 200 (application/json)

        {
            questions:  [ //max length of questions can be 3
                {
                    "_id": String,
                    "webId": String,
                    "createdAt": Date,
                    "createdBy": { ... },
                    "profilePreview": { ... },
                    "allowIncomingRequests": Boolean,
                    "title": String,
                    "text": String,
                    "image": "path/to/sample.jpg",
                    "videoThumbnail": "path/to/sample.jpg",
                    "aspectRatio": 0.75,
                    "altText": '',
                    "audio": "path/to/sample.wav",
                    "audioWaveform": [Number],
                    "audioDuration": Number,
                    "gif": "https://gif.com/gif",
                    "interest": { ... },
                    "interestName": String,
                    "numComments": Number,
                    "numLikes": Number,
                    "numViews": Number,
                    "isDeleted": Boolean,
                    "isEdited": Boolean,
                    "hasUserLiked": Boolean,
                    "hasUserSaved": Boolean,
                    "language": String,
                    "url": "https://...",
                    "awards": {
                    "reward_id_1": 2,
                    "reward_id_2": 1,
                    },
                    "poll": {
                    "options": [
                        {
                        "text": 'option 0',
                        "numVotes": 2,
                        },
                    ],
                    "optionVotedByUser": 0,
                    },
                    "friendsThatCommented": [
                    {
                        "_id": String,
                        "firstName": String,
                        "picture": 'picture0',
                    }
                    ],
                    "isBoosted": Boolean,
                    "createdByInterestRank": Number,
                    "linkedKeywords": [ 'keyword' ],
                    "linkedExploreKeywords": [ String ],
                    "linkedPillarKeywords": [ { keyword: 'keyword', url: '/keyword' } ],
                    "linkedCategories": [ { slug: 'slug' } ],
                    "linkedSubcategories": [ { slug: 'slug', categoryId: 1 } ],
                    "linkedProfiles": [ { slug: 'slug', id: 1 } ],
                    "mentionedUsersTitle": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "mentionedUsersText": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "hashtags": [String],
                }
            ]
        }

### Get questions based and related with profiles name [GET /web/question/profileNameRelated]

+ Query Parameters
    + profileId (the id of the profile, required)
    + language (language of the profile viewed)

+ Request (application/json)

+ Response 200 (application/json)

        {
            questions:  [ //max length of questions can be 15
                {
                    "_id": String,
                    "webId": String,
                    "createdAt": Date,
                    "createdBy": { ... },
                    "profilePreview": { ... },
                    "allowIncomingRequests": Boolean,
                    "title": String,
                    "text": String,
                    "image": "path/to/sample.jpg",
                    "videoThumbnail": "path/to/sample.jpg",
                    "aspectRatio": 0.75,
                    "altText": '',
                    "audio": "path/to/sample.wav",
                    "audioWaveform": [Number],
                    "audioDuration": Number,
                    "gif": "https://gif.com/gif",
                    "interest": { ... },
                    "interestName": String,
                    "numComments": Number,
                    "numLikes": Number,
                    "numViews": Number,
                    "isDeleted": Boolean,
                    "isEdited": Boolean,
                    "hasUserLiked": Boolean,
                    "hasUserSaved": Boolean,
                    "language": String,
                    "url": "https://...",
                    "awards": {
                    "reward_id_1": 2,
                    "reward_id_2": 1,
                    },
                    "poll": {
                    "options": [
                        {
                        "text": 'option 0',
                        "numVotes": 2,
                        },
                    ],
                    "optionVotedByUser": 0,
                    },
                    "friendsThatCommented": [
                    {
                        "_id": String,
                        "firstName": String,
                        "picture": 'picture0',
                    }
                    ],
                    "isBoosted": Boolean,
                    "createdByInterestRank": Number,
                    "linkedKeywords": [ 'keyword' ],
                    "linkedExploreKeywords": [ String ],
                    "linkedPillarKeywords": [ { keyword: 'keyword', url: '/keyword' } ],
                    "linkedCategories": [ { slug: 'slug' } ],
                    "linkedSubcategories": [ { slug: 'slug', categoryId: 1 } ],
                    "linkedProfiles": [ { slug: 'slug', id: 1 } ],
                    "mentionedUsersTitle": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "mentionedUsersText": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "hashtags": [String],
                }
            ]
        }

### Get questions based on profiles sub categories [GET /web/question/profileSubcategoryRelated]

+ Query Parameters
    + profileId (the id of the profile, required)
    + language (language of the profile viewed)

+ Request (application/json)

+ Response 200 (application/json)

        {
            questions:  [ //max length of questions can be 15
                {
                    "_id": String,
                    "webId": String,
                    "createdAt": Date,
                    "createdBy": { ... },
                    "profilePreview": { ... },
                    "allowIncomingRequests": Boolean,
                    "title": String,
                    "text": String,
                    "image": "path/to/sample.jpg",
                    "videoThumbnail": "path/to/sample.jpg",
                    "aspectRatio": 0.75,
                    "altText": '',
                    "audio": "path/to/sample.wav",
                    "audioWaveform": [Number],
                    "audioDuration": Number,
                    "gif": "https://gif.com/gif",
                    "interest": { ... },
                    "interestName": String,
                    "numComments": Number,
                    "numLikes": Number,
                    "numViews": Number,
                    "isDeleted": Boolean,
                    "isEdited": Boolean,
                    "hasUserLiked": Boolean,
                    "hasUserSaved": Boolean,
                    "language": String,
                    "url": "https://...",
                    "awards": {
                    "reward_id_1": 2,
                    "reward_id_2": 1,
                    },
                    "poll": {
                    "options": [
                        {
                        "text": 'option 0',
                        "numVotes": 2,
                        },
                    ],
                    "optionVotedByUser": 0,
                    },
                    "friendsThatCommented": [
                    {
                        "_id": String,
                        "firstName": String,
                        "picture": 'picture0',
                    }
                    ],
                    "isBoosted": Boolean,
                    "createdByInterestRank": Number,
                    "linkedKeywords": [ 'keyword' ],
                    "linkedExploreKeywords": [ String ],
                    "linkedPillarKeywords": [ { keyword: 'keyword', url: '/keyword' } ],
                    "linkedCategories": [ { slug: 'slug' } ],
                    "linkedSubcategories": [ { slug: 'slug', categoryId: 1 } ],
                    "linkedProfiles": [ { slug: 'slug', id: 1 } ],
                    "mentionedUsersTitle": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "mentionedUsersText": [
                        {
                            _id: '1',
                            firstName: 'name',
                        }
                    ],
                    "hashtags": [String],
                }
            ]
        }


### Record Poll for Blogs [PATCH /blogs/poll]

+ Request (application/json)

    + Headers

            Authorization: token

    + Body

            {
                blogUrl: String,
                option: 0, // Positive Integer or Zero
            }

+ Response 200 (application/json)

        {
        }

### Fetch Poll for a Blog of a User [GET /blogs/user/poll]

+ Request (application/json)

    + Headers

            Authorization: token

    + Query Parameters
        + blogUrl (selected url of the blog, required)

+ Response 200 (application/json)

        {
            poll: {
                blogUrl: String,
                option: Number,
                mbti: String,
            }
        }
