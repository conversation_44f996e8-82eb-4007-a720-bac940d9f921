const mongoose = require('mongoose');
const { locales } = require('../lib/translate');

const translationSchema = new mongoose.Schema({
  reviewedAt: { type: Date },
  reviewedBy: { type: String, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  createdBy: { type: String, ref: 'User' },
  status: { type: String, enum: ['approved', 'rejected', 'pending'], default: 'pending' },
  refImage: { type: String },
  currentTranslation: {
    type: String,
    trim: true,
    maxlength: 500,
    required: true,
  },
  correctTranslation: {
    type: String,
    trim: true,
    maxlength: 500,
    required: true,
  },
  details: {
    type: String,
    trim: true,
    maxlength: 2000,
  },
  language: {
    type: String,
    enum: locales,
    default: 'en',
  },
});

translationSchema.index({
  status: 1,
});

translationSchema.statics.removeAllForUser = async function (userId) {
  await this.updateMany({ createdBy: userId }, { $set: { createdBy: null } });
};
module.exports = mongoose.model('Translation', translationSchema);
