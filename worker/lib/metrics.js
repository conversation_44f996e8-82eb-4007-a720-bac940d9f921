const { DateTime } = require('luxon');
const moment = require('moment');
const User = require('../../models/user');
const Metric = require('../../models/metric');
const Question = require('../../models/question');
const PurchaseReceipt = require('../../models/purchase-receipt');
const SuperLikePurchaseReceipt = require('../../models/super-like-purchase-receipt');
const CoinPurchaseReceipt = require('../../models/coin-purchase-receipt');
const LanguageStat = require('../../models/language-stat');
const InterestStat = require('../../models/interest-stat');
const Interest = require('../../models/interest');
const PreemptiveModerationLog = require('../../models/preemptive-moderation-log');
const BannedInfringingText = require('../../models/banned-infringing-text');
const BannedEmailDomain = require('../../models/banned-email-domain');
const { updateCityStats } = require('../../lib/location-stat');
const databaseLib = require('../../lib/database');
const constants = require('../../lib/constants');
const attLib = require('../../lib/att');
const reportLib = require('../../lib/report');

async function recordMetrics(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const now = Date.now();
  const DAU = await User.countDocuments({
    'metrics.lastSeen': { $gt: DateTime.utc().minus({ days: 1 }).toJSDate() },
  });
  const WAU = await User.countDocuments({
    'metrics.lastSeen': { $gt: DateTime.utc().minus({ days: 7 }).toJSDate() },
  });
  const MAU = await User.countDocuments({
    'metrics.lastSeen': { $gt: DateTime.utc().minus({ days: 30 }).toJSDate() },
  });

  let numUniqueUsersWhoPurchasedInfinity = 0;
  {
    const result = await PurchaseReceipt.aggregate([
      {
        $match: {
          productId: {
            $ne: 'boo_infinity_3_months_1_week_free_trial',
          },
        },
      },
      {
        $group: {
          _id: '$user',
          numPurchases: {
            $sum: 1,
          },
        },
      },
      {
        $count: 'count',
      },
    ]);
    if (result.length > 0) {
      numUniqueUsersWhoPurchasedInfinity = result[0].count;
    }
  }

  let numUniqueUsersWhoPurchasedSuperLike = 0;
  {
    const result = await SuperLikePurchaseReceipt.aggregate([
      {
        $group: {
          _id: '$user',
          numPurchases: {
            $sum: 1,
          },
        },
      },
      {
        $count: 'count',
      },
    ]);
    if (result.length > 0) {
      numUniqueUsersWhoPurchasedSuperLike = result[0].count;
    }
  }

  let numUniqueUsersWhoPurchasedCoins = 0;
  {
    const result = await CoinPurchaseReceipt.aggregate([
      {
        $group: {
          _id: '$user',
          numPurchases: {
            $sum: 1,
          },
        },
      },
      {
        $count: 'count',
      },
    ]);
    if (result.length > 0) {
      numUniqueUsersWhoPurchasedCoins = result[0].count;
    }
  }

  /*
  const numActiveLastWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
      },
    },
  );

  const numActiveLastWeekAndThisWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
      },
      'metrics.lastSeen': {
        $gt: moment().subtract(7, 'days').toDate(),
      },
    },
  );

  const numReactivatedLastWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
        $not: {
          $gt: moment().subtract(21, 'days').toDate(),
          $lt: moment().subtract(14, 'days').toDate(),
        },
      },
      'createdAt': {
        $lt: moment().subtract(21, 'days').toDate(),
      },
    },
  );

  const numReactivatedLastWeekAndActiveThisWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
        $not: {
          $gt: moment().subtract(21, 'days').toDate(),
          $lt: moment().subtract(14, 'days').toDate(),
        },
      },
      'createdAt': {
        $lt: moment().subtract(21, 'days').toDate(),
      },
      'metrics.lastSeen': {
        $gt: moment().subtract(7, 'days').toDate(),
      },
    },
  );
  */

  // get popular languages (at least 50 posts per day)
  let docs = await Question.aggregate([
    {
      $match:
        {
          createdAt: {
            $gt: DateTime.utc().minus({ days: 1 }).toJSDate(),
            $lt: new Date(),
          },
        },
    },
    {
      $group:
        {
          _id: "$language",
          count: {
            $sum: 1,
          },
        },
    },
    {
      $match:
        {
          count: {
            $gte: constants.getPopularLanguageDailyPostsThreshold(),
          },
        },
    },
  ]);
  let popularLanguages = docs.map(x => x._id);

  const metric = new Metric({
    createdAt: now,
    DAU,
    WAU,
    MAU,
    numUniqueUsersWhoPurchasedInfinity,
    numUniqueUsersWhoPurchasedSuperLike,
    numUniqueUsersWhoPurchasedCoins,
    // CURR: numActiveLastWeekAndThisWeek / numActiveLastWeek || 0,
    // RURR: numReactivatedLastWeekAndActiveThisWeek / numReactivatedLastWeek || 0,
    popularLanguages,
  });
  await metric.save();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function recordCityMetrics(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await updateCityStats();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function recordLanguageMetrics(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const metrics = await User.aggregate(
    [
      {
        '$unwind': {
          'path': '$languages'
        }
      }, {
        '$group': {
          '_id': '$languages',
          'count': {
            '$sum': 1
          }
        }
      }
    ]
  );

  const numUsers = {};
  for (const metric of metrics) {
    numUsers[metric._id] = metric.count;
  }

  await LanguageStat.updateOne(
    {},
    {
      $set: {
        numUsers: numUsers,
      },
    },
    {
      upsert: true,
    },
  );

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function recordInterestMetrics(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const docs = await Interest.find({ status: null }, 'name numFollowers').lean();

  const data = {};
  for (const doc of docs) {
    data[doc.name] = doc.numFollowers;
  }

  await InterestStat.updateOne(
    {},
    {
      $set: {
        numFollowers: data,
      },
    },
    {
      upsert: true,
    },
  );

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function recordATT(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await attLib.recordATT();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function backFillPersonalityData(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await databaseLib.backFillPersonalityData();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function updateDatabaseProfileScores(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await databaseLib.updateDatabaseProfileScores();

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function mark30DayInactive(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const result = await User.updateMany(
    {
      'metrics.lastSeen': {
        $gte: moment().subtract(31, 'days').toDate(),
        $lte: moment().subtract(30, 'days').toDate(),
      }
    },
    {
      'metrics.had30DayInactivity': 1,
    },
  );
  console.log(result);

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

module.exports = {
  recordMetrics,
  recordCityMetrics,
  recordLanguageMetrics,
  recordInterestMetrics,
  backFillPersonalityData,
  updateDatabaseProfileScores,
  mark30DayInactive,
  recordATT,
};
